## githubConfigUrl is the GitHub url for where you want to configure runners
githubConfigUrl: 'https://github.com/aacom/roadops'

## githubConfigSecret is the k8s secrets to use when auth with GitHub API.
githubConfigSecret: 'gh-ci-admin-secret'

# Common scaleset name
runnerScaleSetName: 'ripper-light-nodejs'

### Scalability
maxRunners: 60
minRunners: 15

## template is the PodSpec for each runner Pod
template:
    spec:
        imagePullSecrets:
            - name: ghcr-worker-secret
        terminationGracePeriodSeconds: 6000
        containers:
            - name: runner
              image: ghcr.io/aacom/rip-action-runner:202506121049
              command: ['/home/<USER>/run.sh']
              resources:
                  requests:
                      memory: '1000Mi'
                      cpu: '1000m'
                  limits:
                      memory: '2000Mi'
                      cpu: '1200m'
              env:
                  - name: ACTIONS_RUNNER_CONTAINER_HOOKS
                    value: /home/<USER>/k8s/index.js
                  - name: ACTIONS_RUNNER_POD_NAME
                    valueFrom:
                        fieldRef:
                            fieldPath: metadata.name
                  - name: ACTIONS_RUNNER_REQUIRE_JOB_CONTAINER
                    value: 'false'
                  - name: RUNNER_GRACEFUL_STOP_TIMEOUT
                    value: '600'
              volumeMounts:
                  - name: work
                    mountPath: /home/<USER>/_work
                  - name: nfs-volume
                    mountPath: /home/<USER>/cache
        volumes:
            - name: work
              ephemeral:
                  volumeClaimTemplate:
                      spec:
                          accessModes: ['ReadWriteOnce']
                          storageClassName: 'local-path'
                          resources:
                              requests:
                                  storage: 1.5Gi
            - name: nfs-volume
              persistentVolumeClaim:
                  claimName: nfscache

## Add ttlSecondsAfterFinished to automatically delete finished jobs
ttlSecondsAfterFinished: 7200 # Automatically delete the job and its pods after 2 hours
