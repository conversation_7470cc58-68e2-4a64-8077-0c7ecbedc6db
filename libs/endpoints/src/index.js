'use strict';

var AAHelp = require('./lib/aahelp'),
    Restful = require('./lib/restful'),
    aaHelpProxy = require('./lib/aahelp-proxy'),
    bridgeDataStream = require('./lib/bridge-data-stream'),
    IMIClient = require('./lib/imi-mobile'),
    Factories = require('./lib/factories');

module.exports = {
    AAHelp: AAHelp,
    IMIClient: IMIClient,
    Restful: Restful,
    aaHelpProxy: aaHelpProxy,
    bridgeDataStream: bridgeDataStream,
    restfulRequest: Restful.restfulRequest,
    Aah2UidFactory: Factories.Aah2UidFactory
};
