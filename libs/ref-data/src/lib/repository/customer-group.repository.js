'use strict';

const { CustomerGroup } = require('@aa/data-models/common');
var _ = require('lodash'),
    AaHelpRefDataRepo = require('./aahelp-reference-data.repository');

function CustomerGroups(bridge) {
    var cacheRepo = this,
        query = {
            CUSTOMER_GROUP: {}
        };

    _.extend(cacheRepo, new AaHelpRefDataRepo(bridge), {
        clone: function clone(oldVal) {
            return oldVal !== null ? new CustomerGroup(oldVal.toJSON()) : new CustomerGroup();
        },
        queryBuilder: function queryBuilder() {
            return query;
        },
        processFindResponse: function processFindResponse(raw) {
            var models = {},
                reversedList = _(raw.CUSTOMER_GROUP).reverse().value();

            _.forEach(reversedList, function customerGroupParser(item) {
                models[item.custGroupCode] = new CustomerGroup({
                    id: item.Id,
                    code: item.custGroupCode,
                    name: item.custGroupName,
                    seSystemId: item.seSysId,
                    resSystemId: item.resSysId,
                    warrantyPrompt: item.warrantyPromptInd === 'Y',
                    allowForceRecy: item.allowForceRecyInd === 'Y',
                    captureMileage: item.captureMileageInd === 'Y',
                    displayInd: item.displayInd === 1,
                    hirerNetworkId: item.defHirerNetworkId,
                    supNetworkId: item.defSupNetworkId,
                    secondLevelSrchAllowed: item.slvSrchAllowedInd === 'Y',
                    firstLevelValidationLabel: item.flvLabel,
                    recoveryCheckRequired: item.recoveryChkRqd === 'Y',
                    msgHandling: item.msgHandlingInd === 'Y',
                    landmarkClassTypeId: item.landmarkClassTypeId,
                    driverDetsReqInd: item.driverDetsReqInd === 'Y'
                });
            });
            cacheRepo.map(models); // populate cache ..
            return models;
        }
    });
}

module.exports = CustomerGroups;
