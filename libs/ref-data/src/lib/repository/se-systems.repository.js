'use strict';

const { SeSystem } = require('@aa/data-models/common');
var _ = require('lodash'),
    AaHelpRefDataRepo = require('./aahelp-reference-data.repository');

function SeSystemRepository(bridge) {
    var cacheRepo = this,
        query = {
            SE_SYSTEM: {}
        };

    _.extend(
        cacheRepo,
        {
            queryBuilder: function queryBuilder() {
                return query;
            },
            processFindResponse: function processFindResponse(raw) {
                var models = {};
                _.forEach(raw.SE_SYSTEM, function packageParser(item) {
                    models[item.Id] = new SeSystem({
                        id: item.Id,
                        code: item.seSysCode,
                        name: item.seSysName
                    });
                });
                cacheRepo.map(models); // populate cache ..
                return models;
            }
        },
        new AaHelpRefDataRepo(bridge)
    );
}

module.exports = SeSystemRepository;
