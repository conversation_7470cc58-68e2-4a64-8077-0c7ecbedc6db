'use strict';

const { Benefit } = require('@aa/data-models/aux/entitlement');
describe('risk benefit repo', function () {
    var _ = require('lodash');
    var RiskBenefitsRepo = require('./risk-benefits.repository');
    var refRiskBenefitsMock = require('../mock-data/ref-data.mock').riskBenefitMock;
    var refBenefitMock = require('../mock-data/ref-data.mock').benefitsMock;
    var bridgeMockSuccess;
    var repo;

    beforeEach(function () {
        bridgeMockSuccess = {
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            get: function () {}
        };
        repo = new RiskBenefitsRepo(bridgeMockSuccess);
    });

    it('test query builder', function () {
        var query = repo.queryBuilder();
        expect(query).not.toBeNull();
        expect(query.RISK_BENEFIT).not.toBeNull();
    });

    it('test parser', function () {
        var data = repo.processFindResponse(refRiskBenefitsMock);
        var mockData = refRiskBenefitsMock.RISK_BENEFIT[0];

        expect(data).not.toBeNull();

        expect(data[mockData.riskCode].riskCode()).toBe(mockData.riskCode);
        expect(data[mockData.riskCode].payForUseIndicator()).toBe(mockData.payForUseIndicator);
        expect(data[mockData.riskCode].benefits()[0].id()).toBe(mockData.benefitId);

        _.extend(mockData, {
            findSync: jest.fn()
        });

        repo.postProcess(mockData);
        expect(mockData.findSync).toHaveBeenCalled();
    });

    it('test parser returns benefit', function () {
        var data = repo.processFindResponse(refRiskBenefitsMock);
        var mockData = refRiskBenefitsMock.RISK_BENEFIT[0];

        expect(data).not.toBeNull();

        expect(data[mockData.riskCode].riskCode()).toBe(mockData.riskCode);
        expect(data[mockData.riskCode].payForUseIndicator()).toBe(mockData.payForUseIndicator);
        expect(data[mockData.riskCode].benefits()[0].id()).toBe(mockData.benefitId);

        _.extend(mockData, {
            findSync: jest.fn().mockReturnValue([
                new Benefit({
                    id: 69
                })
            ])
        });

        repo.postProcess(mockData);
        expect(data[mockData.riskCode].benefits()[0].id()).toBe(69);
    });
});
