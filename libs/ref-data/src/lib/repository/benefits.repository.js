'use strict';

const { Benefit } = require('@aa/data-models/aux/entitlement');
var _ = require('lodash'),
    AaHelpRefDataRepo = require('./aahelp-reference-data.repository');

function Benefits(bridge) {
    var cacheRepo = this,
        _mapById = {},
        query = {
            BENEFITS: {}
        };

    _.extend(
        cacheRepo,
        {
            queryBuilder: function queryBuilder() {
                return query;
            },
            processFindResponse: function processFindResponse(raw) {
                var models = {};

                _.forEach(raw.BENEFITS, function benefitParser(item) {
                    models[item.benefitCode] = _mapById[item.Id] = new Benefit({
                        id: item.Id,
                        code: item.benefitCode,
                        name: item.benefitName,
                        promptTextId: item.entPromptTextId
                    });
                });

                cacheRepo.map(models); // populate cache ..

                return models;
            },
            findSyncById: function findSyncById(id) {
                return _mapById[id] || null;
            }
        },
        new AaHelpRefDataRepo(bridge)
    );
}

module.exports = Benefits;
