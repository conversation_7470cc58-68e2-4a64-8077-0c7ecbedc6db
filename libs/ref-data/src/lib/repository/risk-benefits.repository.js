'use strict';

const { Benefit } = require('@aa/data-models/aux/entitlement');
var _ = require('lodash'),
    RiskBenefits = require('@aa/malstrom-models/lib/risk-benefits.model'),
    AaHelpRefDataRepo = require('./aahelp-reference-data.repository');

function RiskBenefitsRepo(bridge) {
    var cacheRepo = this,
        query = {
            RISK_BENEFIT: {}
        };

    _.extend(
        cacheRepo,
        {
            queryBuilder: function queryBuilder() {
                return query;
            },
            processFindResponse: function processFindResponse(raw) {
                var models = {};

                _.forEach(raw.RISK_BENEFIT, function riskBenefitParser(item) {
                    if (!models[item.riskCode]) {
                        models[item.riskCode] = new RiskBenefits({
                            riskCode: item.riskCode,
                            payForUseIndicator: item.payForUseIndicator,
                            benefits: []
                        });
                    }

                    models[item.riskCode].benefits().push(
                        new Benefit({
                            id: item.benefitId
                        })
                    );
                });

                cacheRepo.map(models); // populate cache ..

                return models;
            },
            postProcess: function postPorcessing(benefitsRepo) {
                var t_benefit;

                //risk benefit has an array of benefits - this code replaces the array of benefits with complete benefits array

                _.forEach(cacheRepo.map(), function (riskBenefits) {
                    var t_benefitArray = [];

                    _.forEach(riskBenefits.benefits(), function forEachBenefit(riskBenefit) {
                        t_benefit = benefitsRepo.findSync(function findBenefitById(benefitItem) {
                            return benefitItem.id() === riskBenefit.id();
                        });

                        if (t_benefit) {
                            t_benefitArray.push(t_benefit[0]);
                        }
                    });

                    riskBenefits.benefits(t_benefitArray);
                });
            }
        },
        new AaHelpRefDataRepo(bridge)
    );
}

module.exports = RiskBenefitsRepo;
