import { WithId } from 'mongodb';
import { Store, StoreConfig } from './store';
import { Case } from '@aa/data-models/aux/case';

export class CaseStore extends Store {
    protected name = 'Case Store for CUV';
    protected databaseName: string;
    protected collectionName = 'case';

    constructor(config: CaseStoreConfig) {
        super(config);
        const { databaseName } = config;
        this.databaseName = databaseName;
    }

    async getCaseDetails(customerRequestId: number): Promise<WithId<Case> | null> {
        const collection = await this.mongo.collection<Case>(this.databaseName, this.collectionName);

        const result = await collection.findOne({ customerRequestId });

        return result ?? null;
    }
}

export interface CaseStoreConfig extends StoreConfig {
    databaseName: string;
}
