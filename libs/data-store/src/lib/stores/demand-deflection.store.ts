import { BankCustomerGroupCodes, CustomerGroupCode, CUVData, CUVStatus } from '@aa/data-models/common';
import { EventSource, EventSourceMeta, SanitizedEntity } from '@aa/event-source';
import { EventCode, Exception } from '@aa/exception';
import { Utils } from '@aa/utils';
import { Bindings } from '../providers/oracle-data-provider';
import { assertResult, Store, StoreConfig } from './store';

/**
 * DataStore for DD queries
 */
export class DemandDeflectionStore extends Store {
    protected name = 'Demand deflection store';
    protected databaseName: string;

    constructor(config: DemandDeflectionStoreConfig) {
        super(config);

        const { databaseName } = config;
        this.databaseName = databaseName;
    }

    /**
     * Upsert CUV entry
     * @param {CUVData} data
     * @param {EventSourceMeta} meta
     * @param  collectionName:string
     * @returns {Promise<{success: boolean}>}
     */
    public async upsertCUV(data: SanitizedEntity<CUVData, 'membership' | 'vrn'>, meta?: EventSourceMeta, collectionName?: string) {
        if (collectionName === undefined || collectionName === null || collectionName === '') {
            collectionName = 'cuv-entries';
        }
        const collection = await this.mongo.collection<CUVData>(this.databaseName, collectionName);
        const eventSource = new EventSource({
            collection,
            logger: this.logger,
            aggregationKeys: ['membership', 'vrn']
        });

        try {
            await eventSource.set(data, meta);

            return { success: true };
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to add cuv'
            });
        }
    }

    /**
     * Revoke CUV
     * @param {{membership: string, vrn: string, remarks: string}} query
     * @param {EventSourceMeta} meta
     * @returns {Promise<{success: boolean}>}
     */
    public async revokeCUV(
        query: {
            membership: string;
            vrn: string;
            remarks: string;
        },
        meta?: EventSourceMeta
    ): Promise<{
        success: boolean;
    }> {
        const { membership, vrn, remarks } = query;

        const collection = await this.mongo.collection<CUVData>(this.databaseName, 'cuv-entries');

        const eventSource = new EventSource({
            collection,
            logger: this.logger,
            aggregationKeys: ['membership', 'vrn']
        });

        try {
            await eventSource.set(
                {
                    membership: membership,
                    vrn: vrn,
                    remarks: remarks,
                    status: CUVStatus.REVOKED,
                    updated: new Date()
                },
                meta
            );

            return { success: true };
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to revoke cuv'
            });
        }
    }

    /**
     * Get CUV status
     * @param {{membership: string, vrn: string}} query
     * @returns {Promise<{passed: boolean} | undefined>}
     */
    public async getCUVStatus(query: { membership: string; vrn: string }): Promise<{ passed: boolean }> {
        try {
            const collection = await this.mongo.collection<CUVData>(this.databaseName, 'cuv-entries');

            // Search for newest with status, no need to mess with event source store here
            const result = await collection.findOne(
                {
                    ...query,
                    status: { $exists: true }
                },
                {
                    sort: {
                        _id: -1
                    }
                }
            );

            return {
                passed: !result || result.status === CUVStatus.REVOKED
            };
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to get status cuv'
            });
        }
    }

    /**
     * Get CUV for a query
     * @param {{membership: string, vrn: string}} query
     * @param  collectionName:string
     * @returns {Promise<CUVData | undefined>}
     */
    public async getCUV(
        query: {
            membership: string;
            vrn: string;
        },
        collectionName?: string
    ): Promise<CUVData | undefined> {
        try {
            if (collectionName === undefined || collectionName === null || collectionName === '') {
                collectionName = 'cuv-entries';
            }
            const { membership, vrn } = query;
            const collection = await this.mongo.collection<CUVData>(this.databaseName, collectionName);

            const eventSource = new EventSource({
                collection,
                logger: this.logger,
                aggregationKeys: ['membership', 'vrn']
            });

            return (await eventSource.get({ membership, vrn })) as CUVData;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to get status cuv'
            });
        }
    }

    /**
     * Get all CUVs for membership
     * TODO: this needs to be routed via MongodbUtils.paginate()
     *
     * @param {{membership: string}} query
     * @returns {Promise<CUVData[]>}
     */
    public async getCUVsByMembership(query: { membership: string }): Promise<CUVData[]> {
        const { membership } = query;
        const collection = await this.mongo.collection<CUVData>(this.databaseName, 'cuv-entries');

        const vrns = await collection
            .aggregate<{
                vrn: string;
            }>([{ $match: { membership } }, { $group: { _id: '$vrn' } }, { $project: { _id: 0, vrn: '$_id' } }])
            .toArray();

        const cuvs: CUVData[] = [];
        for (const { vrn } of vrns) {
            const cuv = await this.getCUV({ membership, vrn });

            if (cuv) {
                cuvs.push(cuv);
            }
        }

        return cuvs;
    }

    public async isRedFlagged(query: { membership: string; vrn: string }): Promise<boolean> {
        const { membership, vrn } = query;
        const collection = await this.mongo.collection<CUVData>(this.databaseName, 'cuv-entries');

        const redFlagDocument = await collection.findOne({
            membership,
            vrn,
            status: CUVStatus.RED
        });

        return redFlagDocument ? true : false;
    }

    /**
     * Get task id of prev recovery - improved version that works for banking custs
     * @param {{membership: string, custGroup: CustomerGroupCode, isBank: boolean, vrn: string, days: number, lat:
     *     number, long: number}} query
     * @returns {Promise<{passed: boolean, taskId?: number} | undefined>}
     */
    public async secondRecovery(query: { membership: string; custGroup: CustomerGroupCode; isBank: boolean; vrn: string; days: number; lat: number; long: number }): Promise<
        | {
              passed: boolean;
              taskId?: number;
          }
        | undefined
    > {
        const { membership, vrn, isBank, custGroup, days, long, lat } = query;
        const isBankYesNo: 'Y' | 'N' = isBank ? 'Y' : 'N';

        const { easting, northing } = Utils.toEastingNorthing(lat, long, true);

        const sql = `
            BEGIN
              MSDSDBA.AAH_CONTRACT_HISTORY_DS.RETURN_T_VRN_RECOVERY_FULL(
                :membership, :custGroup, :isBankYesNo, :vrn, :days,
                :schema, :easting, :northing, :separations, :separationDayLimit,
                :taskId
              );
            END;
        `;

        const bindings: Bindings = {
            membership,
            vrn,
            custGroup,
            isBankYesNo,
            days,
            schema: 'G',
            easting,
            northing,
            separations: 8,
            separationDayLimit: 3,
            taskId: { type: 'number', direction: 'out' }
        };

        try {
            const results = await this.oracle.execute<{
                taskId: number;
            }>(sql, bindings);
            assertResult(results, true);

            if (!results.length) {
                return { passed: true };
            }

            const { taskId } = results[0];
            // if no task pass
            if (!taskId) {
                return { passed: true };
            }

            return { passed: !taskId, taskId };
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to get sec recovery'
            });
        }
    }

    /**
     * Get task id of a task that had same fault in a time range
     * @param {{custGroup: string, vrn: string, faultCode: string}} query
     * @returns {Promise<{passed: boolean, taskId?: number, daysChecked: number} | undefined>}
     */
    public async repeatFault(query: { custGroup: CustomerGroupCode; vrn: string; faultCode: number }): Promise<
        | {
              passed: boolean;
              taskId?: number;
              daysChecked: number;
          }
        | undefined
    > {
        const daysChecked = 10;
        const { faultCode, vrn, custGroup } = query;

        // if vrn contains illegal characters (e.g. ? that is used by operators sometimes) bail out
        if (vrn.match(/\?/)) {
            return { passed: true, daysChecked };
        }

        // we restrict check to specific cust groups, for rest bailout
        if (!repeatFaultAllowedCustGroups.includes(custGroup)) {
            return { passed: true, daysChecked };
        }

        // we exclude check for specific fault codes
        if (repeatFaultExcludedFaultCodes.includes(faultCode)) {
            return { passed: true, daysChecked };
        }

        const dateLimit = Utils.generatePastDate(`${daysChecked}d`);

        const sql = `
            SELECT
                t.task_id "taskId",
                bt.completion_code "completionCode"
            FROM
                breakdown_task bt
            JOIN task t on bt.task_id = t.task_id
            JOIN customer_request cr on cr.cust_request_id = t.cust_request_id
            JOIN assistance_type a on a.assist_type_id = cr.assist_type_id
            JOIN cust_group cg on cg.cust_group_id = a.cust_group_id
            JOIN completion c on bt.completion_code = c.completion_code
            JOIN task_status ts on t.task_status_id = ts.task_status_id
            JOIN task_create_reason tcr on bt.task_create_reason = tcr.tc_id
            JOIN task_txn_hist tth on bt.task_id = tth.task_id
            JOIN vehicle_fault vf on bt.vehicle_fault_id = vf.vehicle_fault_id
            LEFT JOIN fin_txn_task ftt on ftt.assoc_task_id = bt.task_id
            LEFT JOIN payment_reason pr on pr.payment_reason_id = ftt.payment_reason_id
            WHERE
                t.task_id = bt.task_id
                AND tth.new_task_status_id IN (16,47)
                AND bt.vehicle_reg_no = :vrn
                AND bt.vehicle_fault_id = :faultCode
                AND cr.creation_time > :dateLimit
                AND (
                    pr.payment_reason_id NOT IN (134,135,137,136)
                    OR pr.payment_reason_id IS NULL
                )
            ORDER BY
                cr.creation_time DESC
        `;

        const bindings: Bindings = { faultCode, vrn, dateLimit };

        try {
            const results = await this.oracle.execute<{
                taskId: number;
                completionCode: string;
            }>(sql, bindings);
            assertResult(results, true);

            if (!results.length) {
                return { passed: true, daysChecked };
            }

            // filter result completion codes
            const result = results.find(({ completionCode }) => {
                return !repeatFaultExcludedCompletionCodes.includes(completionCode);
            });

            if (!result) {
                return { passed: true, daysChecked };
            }

            const { taskId } = result;
            // if no task pass
            if (!taskId) {
                return { passed: true, daysChecked };
            }

            return { passed: !taskId, taskId, daysChecked };
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to get repeat fault'
            });
        }
    }
}

// only banking, personal and aurora allowed for repeat fault checks
const repeatFaultAllowedCustGroups: CustomerGroupCode[] = [CustomerGroupCode.PERS, CustomerGroupCode.FSC, CustomerGroupCode.CDL, CustomerGroupCode.CDLV, ...BankCustomerGroupCodes];

// completion codes excluded from the check
const repeatFaultExcludedCompletionCodes: string[] = [
    '14', // LOST CONTACT WITH MEMBER / SILENT CALL
    '15', // BREAKDOWN RESOLVED - MEMBER FIX
    '21', // SERVICE DECLINED
    '51', // REPLACED COMPONENT (ON-BOARD SPARES KIT)
    '52', // REPLACED COMPONENT (COLLECTED FROM AA PARTS SUPPLIER)
    '53', // REPLACED COMPONENT (FRANCHISED DEALER)
    '54', // REPLACED COMPONENT (OTHER SOURCE)
    '56', // REPAIRED COMPONENT FAULT (PERMANENT)
    '61', // WHEEL CHANGE
    '61', // WHEEL CHANGE
    '64', // REPLACED COMPONENT UNDER AA PARTS WARRANTY
    '67', // ENHANCED REPAIR - SUCCESSFUL
    '69', // REPAIRED WITH TRIAL OBD EQUIPMENT
    '70', // MEMBER WISHES TO ARRANGE OWN REPAIR
    '85', // NO TRACE
    '86', // NOTE LEFT
    '89', // REFUSE SERVICE
    '90', // MEMBER CANCELLED
    '90', // MEMBER CANCELLED
    '93', // COMPASSIONATE SERVICE
    '94', // COURTESY SERVICE
    'RR', // RELAY PLUS SERVICE GIVEN
    'S1', // SVC ABUSE: NO MOT - PFU DECLINED (QUOTE PROVIDED)
    'S2', // SVC ABUSE: NO MOT - PFU DECLINED (QUOTE NOT PROVIDED)
    'S3', // SVC ABUSE: NO TAX - PFU DECLINED (QUOTE PROVIDED)
    'S4', // SVC ABUSE: NO TAX - PFU DECLINED (QUOTE NOT PROVIDED)
    'S5', // SVC ABUSE: NO TAX - MBR CALL WHEN PURCH (OUT GRACE PERIOD)
    'S6', // SVC ABUSE: NO TAX - MBR CALL WHEN PURCH (IN GRACE PERIOD)
    'S7', // SVC ABUSE: SORN - PFU DECLINED (QUOTE PROVIDED)
    'S8', // SVC ABUSE: SORN - PFU DECLINED (QUOTE NOT PROVIDED)
    'S9', // SVC ABUSE: SORN - MBR CALL WHEN PURCH (OUT GRACE PERIOD)
    'SA', // SVC ABUSE: SORN - MBR CALL WHEN PURCH (IN GRACE PERIOD)
    'SC', // SVC ABUSE: NO TAX/MOT - PFU DECLINED (QUOTE PROVIDED)
    'SD', // SVC ABUSE: NO TAX/MOT - PFU DECLINED (QUOTE NOT PROVIDED)
    'YV', // NO MOT
    'YW', // NO MOT AND SORN
    'YX', // NO MOT OR TAX
    'YY', // NO TAX
    'YZ', // DECLARED SORN
    'ZE', // SERVICE ABUSE
    'ZI', // EMERGENCY SERVICES
    'ZO', // UNSAFE / UNROADWORTHY / UNLAWFUL VEHICLE
    '1S', // NON-REP: COMPONENT NOT AVL - SUBSEQUENT RECY RES REQD
    '2S', // NON-REP: TOOL NOT AVL - SUBSEQUENT RECY RES REQD
    '3S', // NON-REP: REP INFO NOT AVL - SUBSEQUENT RECY RES REQD
    '4S', // NON-REP: ADDITIONAL TRNG REQD - SUBSEQUENT RECY RES REQD
    '5S', // NON-REP: VEH COND PREVENTS REP - SUBSEQUENT RECY RES REQD
    '6S', // NON-REP: R/S REP NOT FEASIBLE - SUBSEQUENT RECY RES REQD
    '7S', // NON-REP: UNABLE TO DIAGNOSE - SUBSEQUENT RECY RES REQD
    'WS', // MBR-DEC: DEALER WARRANTY REP - SUBSEQUENT RECY RES REQD
    'BS', // MBR-DEC: ARRANGE OWN REP - SUBSEQUENT RECY RES REQD
    'ES', // MBR-DEC: PARTS TOO EXPENSIVE - SUBSEQUENT RECY RES REQD
    'HS', // MBR-DEC: MBR UNABLE TO WAIT - SUBSEQUENT RECY RES REQD
    '12', // THD Remote Fix
    '30' // Successful Advice given
];

const repeatFaultExcludedFaultCodes: number[] = [
    2142, // PETROL IN DIESEL (NATWEST/RBS)
    2143, // DIESEL IN PETROL (NATWEST/RBS)
    2144, // WATER IN DIESEL (NATWEST/RBS)
    2145, // WATER IN PETROL (NATWEST/RBS)
    161, // KEYS LOST
    496, // KEYS LOST ( INSURANCE BASED )
    273, // DIESEL IN PETROL
    480, // KEYS LOST..
    330, // KEYS STOLEN
    318, // KEYS LOST (KROOK-LOCK, METRO-LOCK, NIGHT-STICK ETC)
    482, // KEYS LOST..(KROOKLOCK,METRO-LOCK,NIGHTSTICK ETC)
    547, // PETROL IN DIESEL..
    140, // KEYS BROKEN
    488, // K	KEYS.
    479, // KEYS BROKEN..
    1249, // FUEL ASSIST ONLY
    1412, // KEY REPAIR/REPLACEMENT
    1510, // KEY ASSIST REPAIR
    1563, // KEYS STOLEN    .
    1564, // KEYS BROKEN    .
    1565, // KEYS LOST    .
    1677, // KEYS LOST   '
    1678, // KEYS STOLEN   '
    1758, // KEYS LOCKED IN VEHICLE '
    1759, // KEYS LOCKED IN BOOT '
    2132, // KEYS LOST'
    2133, // KEYS STOLEN'
    2130, // KEYS LOCKED IN VEHICLE'
    2131, // KEYS LOCKED IN BOOT'
    274, // PETROL IN DIESEL
    501, // KEYS LOST ( KROOKLOCK, NIGHTSTICK ETC ) ( INSURANCE BASED )
    481, // KEYS STOLEN..
    546, // DIESEL IN PETROL..
    176, // KEYS LOCKED IN BOOT
    2151, // FLOOD WATER - DRIVEN THROUGH NATWEST/RBS (AAS)
    161, // KEYS LOST
    367, // OPERATOR FORCED RECOVERY
    211, // RTC (MINOR) NOW WON'T START
    496, // KEYS LOST ( INSURANCE BASED )
    455, // RELAY PLUS/STAY MOBILE
    480, // KEYS LOST..
    371, // VRS - OPERATOR FORCED
    471, // RTC..
    330, // KEYS STOLEN
    334, // RTC - OFF ROAD
    490, // RTC - OFF ROAD ( INSURANCE BASED )
    377, // MOTORCYCLE RECOVERY - OPERATOR FORCED
    128, // EFFECT ENTRY (SERVICE DELIVERY USE ONLY)
    177, // KEYS LOCKED IN VEHICLE
    318, // KEYS LOST (KROOK-LOCK, METRO-LOCK, NIGHT-STICK ETC)
    482, // KEYS LOST..(KROOKLOCK,METRO-LOCK,NIGHTSTICK ETC)
    910, // RTC (AAS)
    966, // EFFECT ENTRY          SIF FAULT
    987, // RTC - SIF FAULT
    1036, // EFFECT ENTRY        SIF FAULT
    1037, // KEYS LOCKED IN VEHICLE    SIF FAULT
    1038, // KEYS LOCKED IN BOOT    SIF FAULT
    1039, // KEYS LOST       SIF FAULT
    1040, // KEYS STOLEN         SIF FAULT
    1057, // KEYS LOST (KROOK-LOCK, METRO-LOCK, NIGHT-STICK ETC)  SIF FAULT
    1060, // RTC           LLOYDS TSB
    1079, // RTC ..
    1080, // RTC .
    1103, // RTC    ..
    1104, // RTC - OFF ROAD    ..
    1146, // RTC - SIF FAULT. OTHER MANUFACTURERS
    1171, // RTC           G4S
    1292, // RTC - RENAULT/DACIA SUPPORT
    1293, // RTC OFF ROAD IN DITCH - RENAULT/DACIA SUPPORT
    1294, // RTC OFF ROAD IN SNOW - RENAULT/DACIA SUPPORT
    1312, // RTC                       HONDA MOTORCYCLES
    1302, // RTC      HONDA
    1318, // RTC - PEUGEOT SUPPORT
    1321, // RTC OFF ROAD IN DITCH - PEUGEOT SUPPORT
    1322, // RTC OFF ROAD - PEUGEOT SUPPORT
    1334, // RTC          SUZUKI AM
    1335, // RTC - OFF ROAD            SUZUKI AM
    1345, // RTC - CITROEN SUPPORT
    1348, // RTC OFF ROAD  - CITROEN SUPPORT
    1349, // RTC OFF ROAD IN DITCH  -  CITROEN SUPPORT
    1451, // KEYS STUCK IN IGNITION (PRESTIGE)
    1459, // RTC        (PRESTIGE)
    1474, // RTC (PRESTIGE).
    1496, // RTC - IMMOBILE
    1563, // KEYS STOLEN    .
    1565, // KEYS LOST    .
    1590, // KEYS LOCKED IN VEHICLE (EHS)
    1591, // EFFECT ENTRY (EHS)
    1592, // KEYS LOCKED IN BOOT (EHS)
    1593, // KEYS STOLEN (EHS)
    1594, // KEYS LOST (EHS)
    1596, // RTC (EHS)
    1635, // FLOOD WATER - DRIVEN THROUGH (EHS)
    1643, // STUCK IN SNOW / ICE (EHS)
    1677, // KEYS LOST   '
    1678, // KEYS STOLEN   '
    1701, // STUCK IN MUD / ON GRASS (SIF FAULT)
    1702, // STUCK IN SNOW (SIF FAULT)
    1705, // STUCK IN MUD / ON GRASS
    1706, // STUCK IN SNOW
    1708, // FLOOD WATER - DRIVEN THROUGH
    1709, // FLOOD WATER - DRIVEN THROUGH (SIF FAULT)
    1710, // RTC - OFF ROAD IN DITCH    ..
    1711, // RTC - OFF ROAD IN DITCH..
    1712, // RTC - OFF ROAD IN DITCH
    1713, // RTC - OFF ROAD IN SNOW    ..
    1714, // RTC - OFF ROAD IN SNOW..
    1715, // RTC - OFF ROAD IN SNOW
    1716, // RTC - OFF ROAD IN SNOW   SUZUKI AM
    1717, // STUCK IN MUD / ON GRASS..
    1719, // STUCK IN SNOW..
    1721, // STUCK IN MUD / ON GRASS (INSURANCE BASED)
    1723, // STUCK IN SNOW (INSURANCE BASED)
    1730, // RTC (VMA)
    1731, // EFFECT ENTRY (VMA)
    1732, // KEYS LOCKED IN VEHICLE (VMA)
    1733, // KEYS LOCKED IN BOOT (VMA)
    1734, // FLOOD WATER - DRIVEN THROUGH (VMA)
    1742, // KEYS LOST / STOLEN (VMA)
    1751, // STUCK IN MUD, SAND, SNOW, WATER (VMA)
    1758, // KEYS LOCKED IN VEHICLE '
    1759, // KEYS LOCKED IN BOOT '
    1760, // EFFECT ENTRY ' (SERVICE DELIVERY USE ONLY)
    1258, // Z BATTERY ASSIST
    1775, // RTC - HYUNDAI SUPPORT
    1779, // RTC - OFF ROAD - HYUNDAI SUPPORT
    1784, // KEYS STOLEN (PORSCHE)
    1785, // KEYS LOST (PORSCHE)
    1790, // RTC (PORSCHE)
    1800, // FLOOD WATER - DRIVEN THROUGH (VWG)
    1803, // KEYS LOST (VWG)
    1804, // KEYS STOLEN (VWG)
    1806, // EFFECT ENTRY (VWG)
    1807, // KEYS LOCKED IN BOOT (VWG)
    1808, // KEYS LOCKED IN VEHICLE (VWG)
    1830, // RTC - LEGAL ADVICE ONLY (VWG)
    1834, // RTC (VWG)
    1840, // KEYS LOST (KROOKLOCK/ANTI THEFT DEVICE) (VWG)
    1843, // STUCK IN MUD / ON GRASS (VWG)
    1844, // STUCK IN SNOW (VWG)
    1849, // RTC - OFF ROAD (VWG)
    1854, // RTC - MOBILE
    1902, // RTC  (FLEET INSURED)
    2003, // SPECIALIST EQUIPMENT
    2012, // STUCK IN SNOW / ON ICE (SAFEGUARD)
    2014, // STUCK IN MUD / ON GRASS (SAFEGUARD)
    2023, // EFFECT ENTRY (JLR)
    2024, // KEYS LOCKED IN VEHICLE (JLR)
    2025, // KEYS LOCKED IN BOOT (JLR)
    2033, // STUCK IN MUD / GRASS '
    2035, // STUCK IN SNOW '
    2057, // RTC  (ADMIRAL)
    2058, // RTC      (TOYOTA)
    2059, // RTC       (LEXUS)
    2112, // RTC (AARA)
    2117, // IGNITION/STEERING LOCK BUILD
    2123, // KEYS LOST.
    2124, // KEYS STOLEN.
    2128, // EFFECT ENTRY.
    2136, // RTC (AARA) - WARM TRANSFER
    2149, // FLOOD WATER - DRIVEN THROUGH (AAS)
    2132, // KEYS LOST'
    2133, // KEYS STOLEN'
    2137, // RTC NATWEST/RBS (AAS)
    2130, // KEYS LOCKED IN VEHICLE'
    2131, // KEYS LOCKED IN BOOT'
    114, // RTC
    804, // KEYS LOCKED IN PANNIER BOX
    460, // RTC - OFF ROAD..
    802, // ASSIST RECOVERY TO LOAD (SERVICE DELIVERY USE ONLY)
    501, // KEYS LOST ( KROOKLOCK, NIGHTSTICK ETC ) ( INSURANCE BASED )
    497, // KEYS STOLEN ( INSURANCE BASED )
    481, // KEYS STOLEN..
    489, // RTC ( INSURANCE BASED )
    415, // RTC.
    2150, // STUCK IN WATER NATWEST/RBS (AAS)
    2151, // FLOOD WATER - DRIVEN THROUGH NATWEST/RBS (AAS)
    910, // RTC (AAS)
    1307, // CATALYTIC CONVERTER STOLEN (AAS)
    2032, // STUCK IN WATER (AAS)
    2113, // STUCK ON KERB/WALL/SIMILAR OBJECT (AAS)
    2114, // STUCK IN DITCH (AAS)
    2115, // VANDALISM (AAS)
    2149, // FLOOD WATER - DRIVEN THROUGH (AAS)
    2137, // RTC NATWEST/RBS (AAS)
    2138, // VANDALISM NATWEST/RBS (AAS)
    2139, // CATALYTIC CONVERTER STOLEN NATWEST/RBS (AAS)
    2140, // STUCK IN DITCH NATWEST/RBS (AAS)
    2141, // STUCK ON KERB/WALL/SIMILAR OBJECT NATWEST/RBS (AAS)
    2142, // PETROL IN DIESEL (NATWEST/RBS)
    2143, // DIESEL IN PETROL (NATWEST/RBS)
    2144, // WATER IN DIESEL (NATWEST/RBS)
    2145, // WATER IN PETROL (NATWEST/RBS)
    161, // KEYS LOST
    496, // KEYS LOST ( INSURANCE BASED )
    273, // DIESEL IN PETROL
    480, // KEYS LOST..
    330, // KEYS STOLEN
    318, // KEYS LOST (KROOK-LOCK, METRO-LOCK, NIGHT-STICK ETC)
    482, // KEYS LOST..(KROOKLOCK,METRO-LOCK,NIGHTSTICK ETC)
    547, // PETROL IN DIESEL..
    140, // KEYS BROKEN
    488, // K	KEYS.
    479, // KEYS BROKEN..
    1249, // FUEL ASSIST ONLY
    1412, // KEY REPAIR/REPLACEMENT
    1510, // KEY ASSIST REPAIR
    1563, // KEYS STOLEN    .
    1564, // KEYS BROKEN    .
    1565, // KEYS LOST    .
    1677, // KEYS LOST   '
    1678, // KEYS STOLEN   '
    1758, // KEYS LOCKED IN VEHICLE '
    1759, // KEYS LOCKED IN BOOT '
    2132, // KEYS LOST'
    2133, // KEYS STOLEN'
    2130, // KEYS LOCKED IN VEHICLE'
    2131, // KEYS LOCKED IN BOOT'
    274, // PETROL IN DIESEL
    501, // KEYS LOST ( KROOKLOCK, NIGHTSTICK ETC ) ( INSURANCE BASED )
    481, // KEYS STOLEN..
    546, // DIESEL IN PETROL..
    2150, // STUCK IN WATER NATWEST/RBS (AAS)
    2151, // FLOOD WATER - DRIVEN THROUGH NATWEST/RBS (AAS)
    910, // RTC (AAS)
    1307, // CATALYTIC CONVERTER STOLEN (AAS)
    2032, // STUCK IN WATER (AAS)
    2113, // STUCK ON KERB/WALL/SIMILAR OBJECT (AAS)
    2114, // STUCK IN DITCH (AAS)
    2115, // VANDALISM (AAS)
    2149, // FLOOD WATER - DRIVEN THROUGH (AAS)
    2137, // RTC NATWEST/RBS (AAS)
    2138, // VANDALISM NATWEST/RBS (AAS)
    2139, // CATALYTIC CONVERTER STOLEN NATWEST/RBS (AAS)
    2140, // STUCK IN DITCH NATWEST/RBS (AAS)
    2141, // STUCK ON KERB/WALL/SIMILAR OBJECT NATWEST/RBS (AAS)
    474, // '2' PUNCTURES AND NO SPARE..
    801, // '1' PUNCTURE - NO SPARE.....
    433, // 'PUNCTURE.
    451, // 'PUNCTURE - NO SPARE.
    473, // '2' PUNCTURES AND A SPARE..
    467, // '1 PUNCTURE NO SPARE..
    477, // '4' PUNCTURES AND A SPARE..
    310, // PNO	'1 PUNCTURE NO SPARE
    312, // PAS	'3 PUNCTURES AND A SPARE
    475, // '3' PUNCTURES AND A SPARE..
    314, // PAS	'4 PUNCTURES AND A SPARE
    308, // PAS	'2 PUNCTURES AND A SPARE
    322, // 'PUNCTURE ON CARAVAN / TRAILER AND SPARE
    814, // '1 PUNCTURE WITH A SPARE (MOTORCYCLE)
    815, // 'PUNCTURE/S  (MOTORCYCLE)
    816, // '2 PUNCTURES (MOTORCYCLE)
    817, // '3 PUNCTURES (MOTORCYCLE)
    818, // '4 PUNCTURES (MOTORCYCLE)
    821, // MULTIPLE PUNCTURES...
    820, // PUNCTURE NO SPARE...
    822, // PUNCTURE WITH SPARE...
    1119, // '1 PUNCTURE AND UNUSABLE SPARE    ..
    1120, // '1 PUNCTURE NO SPARE    ..
    1122, // '1 PUNCTURE NO SPARE - WITH MANUFACTURER REPAIR KIT    ..
    1123, // '2 PUNCTURES AND A SPARE    ..
    1124, // '2 PUNCTURES NO SPARE    ..
    1125, // '3 PUNCTURES AND A SPARE    ..
    1126, // '3 PUNCTURES NO SPARE    ..
    1127, // '4 PUNCTURES AND SPARE    ..
    1128, // '4 PUNCTURES NO SPARE    ..
    1129, // *'PUNCTURE ON CARAVAN / TRAILER NO SPARE    ..
    1130, // 'PUNCTURE ON CARAVAN / TRAILER AND SPARE    ..
    1413, // TYRE REPAIR/REPLACEMENT - 1 TYRE
    1414, // TYRE REPAIR/REPLACEMENT - 2 TYRES
    1415, // TYRE REPAIR/REPLACEMENT - 3 TYRES
    1416, // TYRE REPAIR/REPLACEMENT - 4 OR MORE TYRES
    1581, // 1 PUNCTURE
    1582, // 2 PUNCTURES
    1583, // 3 PUNCTURES
    1584, // 4 PUNCTURES
    1681, // ' ALL OTHER PUNCTURE(S)
    1682, // PAN	' 1 PUNCTURE - WITH A SPARE
    452, // 'MULTIPLE PUNCTURES.
    808, // '1 PUNCTURE NO SPARE - WITH MANUFACTURER REPAIR KIT
    476, // '3' PUNCTURES AND NO SPARE..
    313, // PNO	'4 PUNCTURES NO SPARE
    311, // PNO	'2 PUNCTURES NO SPARE
    307, // PAS	'1 PUNCTURE AND A SPARE
    472, // '1' PUNCTURE AND AN UNUSABLE SPARE..
    331, // PPS	'1 PUNCTURE AND AN UNUSABLE SPARE
    309, // PNO	'3 PUNCTURES NO SPARE
    323, // 'PUNCTURE ON CARAVAN / TRAILER NO SPARE
    478, // '4' PUNCTURES AND NO SPARE..
    176, // KEYS LOCKED IN BOOT
    2151, // FLOOD WATER - DRIVEN THROUGH NATWEST/RBS (AAS)
    161, // KEYS LOST
    211, // RTC (MINOR) NOW WON'T START
    496, // KEYS LOST ( INSURANCE BASED )
    455, // RELAY PLUS/STAY MOBILE
    480, // KEYS LOST..
    371, // VRS - OPERATOR FORCED
    471, // RTC..
    330, // KEYS STOLEN
    334, // RTC - OFF ROAD
    490, // RTC - OFF ROAD ( INSURANCE BASED )
    377, // MOTORCYCLE RECOVERY - OPERATOR FORCED
    128, // EFFECT ENTRY (SERVICE DELIVERY USE ONLY)
    177, // KEYS LOCKED IN VEHICLE
    318, // KEYS LOST (KROOK-LOCK, METRO-LOCK, NIGHT-STICK ETC)
    482, // KEYS LOST..(KROOKLOCK,METRO-LOCK,NIGHTSTICK ETC)
    910, // RTC (AAS)
    966, // EFFECT ENTRY          SIF FAULT
    987, // RTC - SIF FAULT
    1036, // EFFECT ENTRY        SIF FAULT
    1037, // KEYS LOCKED IN VEHICLE    SIF FAULT
    1038, // KEYS LOCKED IN BOOT    SIF FAULT
    1039, // KEYS LOST       SIF FAULT
    1040, // KEYS STOLEN         SIF FAULT
    1057, // KEYS LOST (KROOK-LOCK, METRO-LOCK, NIGHT-STICK ETC)  SIF FAULT
    1060, // RTC           LLOYDS TSB
    1079, // RTC ..
    1080, // RTC .
    1103, // RTC    ..
    1104, // RTC - OFF ROAD    ..
    1146, // RTC - SIF FAULT. OTHER MANUFACTURERS
    1171, // RTC           G4S
    1292, // RTC - RENAULT/DACIA SUPPORT
    1293, // RTC OFF ROAD IN DITCH - RENAULT/DACIA SUPPORT
    1294, // RTC OFF ROAD IN SNOW - RENAULT/DACIA SUPPORT
    1312, // RTC                       HONDA MOTORCYCLES
    1302, // RTC      HONDA
    1318, // RTC - PEUGEOT SUPPORT
    1321, // RTC OFF ROAD IN DITCH - PEUGEOT SUPPORT
    1322, // RTC OFF ROAD - PEUGEOT SUPPORT
    1334, // RTC          SUZUKI AM
    1335, // RTC - OFF ROAD            SUZUKI AM
    1345, // RTC - CITROEN SUPPORT
    1348, // RTC OFF ROAD  - CITROEN SUPPORT
    1349, // RTC OFF ROAD IN DITCH  -  CITROEN SUPPORT
    1451, // KEYS STUCK IN IGNITION (PRESTIGE)
    1459, // RTC        (PRESTIGE)
    1474, // RTC (PRESTIGE).
    1496, // RTC - IMMOBILE
    1563, // KEYS STOLEN    .
    1565, // KEYS LOST    .
    1590, // KEYS LOCKED IN VEHICLE (EHS)
    1591, // EFFECT ENTRY (EHS)
    1592, // KEYS LOCKED IN BOOT (EHS)
    1593, // KEYS STOLEN (EHS)
    1594, // KEYS LOST (EHS)
    1596, // RTC (EHS)
    1635, // FLOOD WATER - DRIVEN THROUGH (EHS)
    1643, // STUCK IN SNOW / ICE (EHS)
    1677, // KEYS LOST   '
    1678, // KEYS STOLEN   '
    1701, // STUCK IN MUD / ON GRASS (SIF FAULT)
    1702, // STUCK IN SNOW (SIF FAULT)
    1705, // STUCK IN MUD / ON GRASS
    1706, // STUCK IN SNOW
    1708, // FLOOD WATER - DRIVEN THROUGH
    1709, // FLOOD WATER - DRIVEN THROUGH (SIF FAULT)
    1710, // RTC - OFF ROAD IN DITCH    ..
    1711, // RTC - OFF ROAD IN DITCH..
    1712, // RTC - OFF ROAD IN DITCH
    1713, // RTC - OFF ROAD IN SNOW    ..
    1714, // RTC - OFF ROAD IN SNOW..
    1715, // RTC - OFF ROAD IN SNOW
    1716, // RTC - OFF ROAD IN SNOW   SUZUKI AM
    1717, // STUCK IN MUD / ON GRASS..
    1719, // STUCK IN SNOW..
    1721, // STUCK IN MUD / ON GRASS (INSURANCE BASED)
    1723, // STUCK IN SNOW (INSURANCE BASED)
    1730, // RTC (VMA)
    1731, // EFFECT ENTRY (VMA)
    1732, // KEYS LOCKED IN VEHICLE (VMA)
    1733, // KEYS LOCKED IN BOOT (VMA)
    1734, // FLOOD WATER - DRIVEN THROUGH (VMA)
    1742, // KEYS LOST / STOLEN (VMA)
    1751, // STUCK IN MUD, SAND, SNOW, WATER (VMA)
    1758, // KEYS LOCKED IN VEHICLE '
    1759, // KEYS LOCKED IN BOOT '
    1760, // EFFECT ENTRY ' (SERVICE DELIVERY USE ONLY)
    1258, // Z BATTERY ASSIST
    1775, // RTC - HYUNDAI SUPPORT
    1779, // RTC - OFF ROAD - HYUNDAI SUPPORT
    1784, // KEYS STOLEN (PORSCHE)
    1785, // KEYS LOST (PORSCHE)
    1790, // RTC (PORSCHE)
    1800, // FLOOD WATER - DRIVEN THROUGH (VWG)
    1803, // KEYS LOST (VWG)
    1804, // KEYS STOLEN (VWG)
    1806, // EFFECT ENTRY (VWG)
    1807, // KEYS LOCKED IN BOOT (VWG)
    1808, // KEYS LOCKED IN VEHICLE (VWG)
    1830, // RTC - LEGAL ADVICE ONLY (VWG)
    1834, // RTC (VWG)
    1840, // KEYS LOST (KROOKLOCK/ANTI THEFT DEVICE) (VWG)
    1843, // STUCK IN MUD / ON GRASS (VWG)
    1844, // STUCK IN SNOW (VWG)
    1849, // RTC - OFF ROAD (VWG)
    1854, // RTC - MOBILE
    2003, // SPECIALIST EQUIPMENT
    2012, // STUCK IN SNOW / ON ICE (SAFEGUARD)
    2014, // STUCK IN MUD / ON GRASS (SAFEGUARD)
    2023, // EFFECT ENTRY (JLR)
    2024, // KEYS LOCKED IN VEHICLE (JLR)
    2025, // KEYS LOCKED IN BOOT (JLR)
    2033, // STUCK IN MUD / GRASS '
    2035, // STUCK IN SNOW '
    2057, // RTC  (ADMIRAL)
    2058, // RTC      (TOYOTA)
    2059, // RTC       (LEXUS)
    2112, // RTC (AARA)
    2123, // KEYS LOST.
    2124, // KEYS STOLEN.
    2128, // EFFECT ENTRY.
    2136, // RTC (AARA) - WARM TRANSFER
    2149, // FLOOD WATER - DRIVEN THROUGH (AAS)
    2132, // KEYS LOST'
    2133, // KEYS STOLEN'
    2137, // RTC NATWEST/RBS (AAS)
    2130, // KEYS LOCKED IN VEHICLE'
    2131, // KEYS LOCKED IN BOOT'
    114, // RTC
    804, // KEYS LOCKED IN PANNIER BOX
    460, // RTC - OFF ROAD..
    802, // ASSIST RECOVERY TO LOAD (SERVICE DELIVERY USE ONLY)
    501, // KEYS LOST ( KROOKLOCK, NIGHTSTICK ETC ) ( INSURANCE BASED )
    497, // KEYS STOLEN ( INSURANCE BASED )
    481, // KEYS STOLEN..
    489, // RTC ( INSURANCE BASED )
    415, // RTC.
    474, // '2' PUNCTURES AND NO SPARE..
    801, // '1' PUNCTURE - NO SPARE.....
    433, // 'PUNCTURE.
    451, // 'PUNCTURE - NO SPARE.
    473, // '2' PUNCTURES AND A SPARE..
    467, // '1 PUNCTURE NO SPARE..
    477, // '4' PUNCTURES AND A SPARE..
    310, // PNO	'1 PUNCTURE NO SPARE
    312, // PAS	'3 PUNCTURES AND A SPARE
    475, // '3' PUNCTURES AND A SPARE..
    314, // PAS	'4 PUNCTURES AND A SPARE
    308, // PAS	'2 PUNCTURES AND A SPARE
    322, // 'PUNCTURE ON CARAVAN / TRAILER AND SPARE
    814, // '1 PUNCTURE WITH A SPARE (MOTORCYCLE)
    815, // 'PUNCTURE/S  (MOTORCYCLE)
    816, // '2 PUNCTURES (MOTORCYCLE)
    817, // '3 PUNCTURES (MOTORCYCLE)
    818, // '4 PUNCTURES (MOTORCYCLE)
    821, // MULTIPLE PUNCTURES...
    820, // PUNCTURE NO SPARE...
    822, // PUNCTURE WITH SPARE...
    1119, // '1 PUNCTURE AND UNUSABLE SPARE    ..
    1120, // '1 PUNCTURE NO SPARE    ..
    1122, // '1 PUNCTURE NO SPARE - WITH MANUFACTURER REPAIR KIT    ..
    1123, // '2 PUNCTURES AND A SPARE    ..
    1124, // '2 PUNCTURES NO SPARE    ..
    1125, // '3 PUNCTURES AND A SPARE    ..
    1126, // '3 PUNCTURES NO SPARE    ..
    1127, // '4 PUNCTURES AND SPARE    ..
    1128, // '4 PUNCTURES NO SPARE    ..
    1129, // *'PUNCTURE ON CARAVAN / TRAILER NO SPARE    ..
    1130, // 'PUNCTURE ON CARAVAN / TRAILER AND SPARE    ..
    1413, // TYRE REPAIR/REPLACEMENT - 1 TYRE
    1414, // TYRE REPAIR/REPLACEMENT - 2 TYRES
    1415, // TYRE REPAIR/REPLACEMENT - 3 TYRES
    1416, // TYRE REPAIR/REPLACEMENT - 4 OR MORE TYRES
    1581, // 1 PUNCTURE
    1582, // 2 PUNCTURES
    1583, // 3 PUNCTURES
    1584, // 4 PUNCTURES
    1681, // ' ALL OTHER PUNCTURE(S)
    1682, // PAN	' 1 PUNCTURE - WITH A SPARE
    452, // 'MULTIPLE PUNCTURES.
    808, // '1 PUNCTURE NO SPARE - WITH MANUFACTURER REPAIR KIT
    476, // '3' PUNCTURES AND NO SPARE..
    313, // PNO	'4 PUNCTURES NO SPARE
    311, // PNO	'2 PUNCTURES NO SPARE
    307, // PAS	'1 PUNCTURE AND A SPARE
    472, // '1' PUNCTURE AND AN UNUSABLE SPARE..
    331, // PPS	'1 PUNCTURE AND AN UNUSABLE SPARE
    309, // PNO	'3 PUNCTURES NO SPARE
    323, // 'PUNCTURE ON CARAVAN / TRAILER NO SPARE
    478 // '4' PUNCTURES AND NO SPARE..
];

export interface DemandDeflectionStoreConfig extends StoreConfig {
    databaseName: string;
}
