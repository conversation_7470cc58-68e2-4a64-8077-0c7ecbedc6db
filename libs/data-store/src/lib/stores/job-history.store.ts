import { EventCode, Exception } from '@aa/exception';
import { Store, StoreConfig } from './store';
import { WithId } from 'mongodb';
import { Case } from '@aa/data-models/aux/case';
/**
 * DataStore for job history queries
 */
export class JobHistoryStore extends Store {
    protected name = 'Job history store';
    protected databaseName: string;

    constructor(config: JobHistoryStoreConfig) {
        super(config);

        const { databaseName } = config;
        this.databaseName = databaseName;
    }

    public async insertCase(data: Case) {
        try {
            const collection = await this.mongo.collection<Case>(this.databaseName, 'case');

            data.entitlement.products = data.entitlement.products?.map((product) => {
                product.coveredVehicle = product.coveredVehicle?.map((vehicle) => vehicle.toUpperCase());
                return product;
            });

            const idx = { customerRequestId: data.customerRequestId };
            await collection.updateOne(idx, { $set: data }, { upsert: true });
            this.logger.log(`Case data inserted for ${data.customerRequestId}`);
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to set case data'
            });
        }
    }

    public async getCase(query: { customerRequestId: string }): Promise<WithId<Case> | undefined> {
        try {
            const { customerRequestId } = query;
            const collection = await this.mongo.collection<Case>(this.databaseName, 'case');
            const caseData = await collection.findOne({ customerRequestId: Number.parseInt(customerRequestId) });
            return caseData ? caseData : undefined;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to get case data'
            });
        }
    }
    public async isCuvCovered(query: { vrn: string; customerRequestId: string }): Promise<boolean> {
        try {
            const { customerRequestId } = query;
            const collection = await this.mongo.collection<Case>(this.databaseName, 'case');

            // this doesn't seems to work from nodejs even though it works on compass.
            // adding to uppercase both here and in the insertCase method above until we find a better solution
            // const filter = {
            //     customerRequestId,
            //     "entitlement.products.benefitCode": "CUV",
            //     "entitlement.products.coveredVehicle": {$regex: new RegExp(query.vrn, 'i')},
            // };
            const filter = {
                customerRequestId: Number.parseInt(customerRequestId),
                'entitlement.products.benefitCode': 'CUV',
                'entitlement.products.coveredVehicle': query.vrn.toUpperCase()
            };
            const caseData = await collection.findOne(filter);
            return caseData ? true : false;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to get case data'
            });
        }
    }
}

export interface JobHistoryStoreConfig extends StoreConfig {
    databaseName: string;
}
