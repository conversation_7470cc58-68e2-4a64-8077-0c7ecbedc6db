import { EventCode, Exception } from '@aa/exception';
import { GarageBookingStatus, GarageBookingStatusType } from '@aa/data-models/common';
import { WithId } from 'mongodb';
import { Store, StoreConfig } from './store';

export class GarageBookingStore extends Store {
    protected name = 'Garage Booking Store for SMR';
    protected databaseName: string;
    protected statusCollectionName = 'garage-booking-status';

    constructor(config: GarageBookingStoreConfig) {
        super(config);
        const { databaseName } = config;
        this.databaseName = databaseName;
    }

    async upsertBookingStatus(aah2BookingRequestId: string, taskId: number, status: GarageBookingStatusType, unityBookingRefNo?: number, error?: string): Promise<void> {
        try {
            if (!aah2BookingRequestId || !taskId || !status) {
                throw new Exception({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Invalid inputs for upsert garage booking status'
                });
            }

            const collection = await this.mongo.collection<GarageBookingStatus>(this.databaseName, this.statusCollectionName);
            await collection.updateOne(
                { aah2BookingRequestId },
                {
                    $set: {
                        taskId: taskId,
                        date: new Date(),
                        status: status,
                        unityBookingRefNo: unityBookingRefNo,
                        error: error
                    }
                },
                { upsert: true }
            );
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to upsert garage booking status'
            });
        }
    }

    async getStatusByAah2BookingRequestId(aah2BookingRequestId: string): Promise<WithId<GarageBookingStatus> | null> {
        try {
            const collection = await this.mongo.collection<GarageBookingStatus>(this.databaseName, this.statusCollectionName);

            const result = await collection.findOne({ aah2BookingRequestId });

            return result ?? null;
        } catch (error) {
            this.logger.warn({
                sourceName: this.name,
                message: `Failed to get garage booking status by Aah2 booking request id`,
                data: { aah2BookingRequestId }
            });
            return null;
        }
    }

    async getStatusByTaskId(taskId: number): Promise<WithId<GarageBookingStatus> | null> {
        try {
            const collection = await this.mongo.collection<GarageBookingStatus>(this.databaseName, this.statusCollectionName);

            const result = await collection.findOne({ taskId }, { sort: { date: 'desc' } });

            return result ?? null;
        } catch (error) {
            this.logger.warn({
                sourceName: this.name,
                message: `Failed to get garage booking status by task id`,
                data: { taskId }
            });
            return null;
        }
    }
}

export interface GarageBookingStoreConfig extends StoreConfig {
    databaseName: string;
}
