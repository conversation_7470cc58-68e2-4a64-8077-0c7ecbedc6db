import { Environment, Exception } from '@aa/exception';
import { Logger } from '@aa/logger';
import { System } from '@aa/system';
import { Collection, Document, MongoClient } from 'mongodb';
import { MongodbConfig, MongodbDataProvider } from './mongodb-data-provider';

jest.mock('mongodb');
jest.mock('@aa/logger');
jest.mock('@aa/system');

describe('MongodbDataProvider', () => {
    let provider: MongodbDataProvider;
    let mockConfig: MongodbConfig;
    let mockClient: jest.Mocked<MongoClient>;

    beforeEach(() => {
        mockClient = new MongoClient('') as jest.Mocked<MongoClient>;
        const logger = new Logger({ context: {} as unknown as any, appName: '' }),
            mockConfig = {
                connectionString: 'mongodb://localhost:27017',
                appName: 'TestApp',
                logger,
                system: new System({ logger, environment: Environment.INT, process: {} as unknown as any })
            };
        (MongoClient as unknown as jest.Mock).mockImplementation(() => mockClient);
        provider = new MongodbDataProvider(mockConfig);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('returns a collection when collection is called with valid db and collection names', async () => {
        const mockCollection = {} as Collection<Document>;
        mockClient.db.mockReturnValue({
            collection: jest.fn().mockReturnValue(mockCollection)
        } as any);

        const result = await provider.collection('testDb', 'testCollection');

        expect(mockClient.db).toHaveBeenCalledWith('testDb');
        expect(result).toBe(mockCollection);
    });

    describe('creates connection to the database only when queries executed', () => {
        it('for change stream', async () => {
            const mockCommand = jest.fn();
            mockClient.db.mockReturnValue({
                command: mockCommand
            } as any);

            await provider.setupChangeStream('testDb', 'testCollection');

            expect(mockClient.connect).toHaveBeenCalled();
        });

        it('for transaction', async () => {
            const mockSession = {
                withTransaction: jest.fn(async (callback) => await callback()),
                endSession: jest.fn()
            };
            mockClient.startSession.mockReturnValue(mockSession as any);

            const executor = jest.fn();
            await provider.transaction(executor);

            expect(mockClient.connect).toHaveBeenCalled();
        });

        it('for collection query', async () => {
            const mockCollection = {} as Collection<Document>;
            mockClient.db.mockReturnValue({
                collection: jest.fn().mockReturnValue(mockCollection)
            } as any);

            await provider.collection('testDb', 'testCollection');

            expect(mockClient.connect).toHaveBeenCalled();
        });
    });

    it('throws an exception when collection fails to retrieve a collection', async () => {
        mockClient.db.mockImplementation(() => {
            throw new Error('DB error');
        });

        await expect(provider.collection('testDb', 'testCollection')).rejects.toThrow(Exception);
    });

    it('enables change stream pre and post images when setupChangeStream is called', async () => {
        const mockCommand = jest.fn();
        mockClient.db.mockReturnValue({
            command: mockCommand
        } as any);

        await provider.setupChangeStream('testDb', 'testCollection');

        expect(mockCommand).toHaveBeenCalledWith({
            collMod: 'testCollection',
            changeStreamPreAndPostImages: { enabled: true }
        });
    });

    it('throws an exception when setupChangeStream fails', async () => {
        mockClient.db.mockImplementation(() => {
            throw new Error('DB error');
        });

        await expect(provider.setupChangeStream('testDb', 'testCollection')).rejects.toThrow(Exception);
    });

    it('executes a transaction and commits successfully', async () => {
        const mockSession = {
            withTransaction: jest.fn(async (callback) => await callback()),
            endSession: jest.fn()
        };
        mockClient.startSession.mockReturnValue(mockSession as any);

        const executor = jest.fn();
        await provider.transaction(executor);

        expect(mockSession.withTransaction).toHaveBeenCalledWith(executor);
        expect(mockSession.endSession).toHaveBeenCalled();
    });

    it('throws an exception when transaction fails', async () => {
        const mockSession = {
            withTransaction: jest.fn(async () => {
                throw new Error('Transaction error');
            }),
            endSession: jest.fn()
        };
        mockClient.startSession.mockReturnValue(mockSession as any);

        const executor = jest.fn();
        await expect(provider.transaction(executor)).rejects.toThrow(Exception);

        expect(mockSession.endSession).toHaveBeenCalled();
    });

    it('disconnects successfully when disconnect is called', async () => {
        mockClient.close.mockResolvedValue();

        await (provider as any).disconnect();

        expect(mockClient.removeAllListeners).toHaveBeenCalled();
        expect(mockClient.close).toHaveBeenCalledWith(true);
    });

    it('throws an exception when disconnect fails', async () => {
        mockClient.close.mockRejectedValue(new Error('Disconnect error'));

        await expect((provider as any).disconnect()).rejects.toThrow(Exception);
    });
});
