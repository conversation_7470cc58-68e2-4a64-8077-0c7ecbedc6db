import { EventCode, Exception } from '@aa/exception';
import { Logger } from '@aa/logger';
import { Provider } from '@aa/provider';
import { System, SystemEvent } from '@aa/system';
import { Utils } from '@aa/utils';
import { DBObject_IN } from 'oracledb';
import { OraclePool, OraclePoolConfig } from './oracle-pool-provider';

export class OracleDataProvider extends Provider<OracleConfig> {
    protected name = 'Oracle data provider';
    private logger!: Logger;
    private system!: System;
    private connectedPoolId?: string;
    private stats = false;
    private pools!: OraclePool[];
    private retryRoundMax!: number;
    private connectStrings!: string[];
    private poolConfig!: OraclePoolConfig;

    private get activePool(): OraclePool | null {
        return this.getPool(this.connectedPoolId) || null;
    }

    constructor(config: OracleConfig) {
        super();
        this.initialise(config).catch((error) => {
            this.system.onException(error, true);
        });
    }

    public async initialise(config: OracleConfig) {
        try {
            const { logger, system } = config;
            this.logger = logger;
            this.system = system;
            this.applyConfig(config);
            this.setupListeners();
        } catch (error) {
            throw new Exception({
                error,
                sourceName: this.name,
                code: EventCode.MOD_INIT_FAIL
            });
        }

        try {
            this.pools = [];
            this.createPools();
            await this.connect();
        } catch (error) {
            throw new Exception({
                error,
                sourceName: this.name,
                code: EventCode.MOD_INIT_FAIL
            });
        }

        this.ready();
        this.logger.log({
            sourceName: this.name,
            message: 'Oracle provider ready'
        });
    }

    /**
     * If stats enabled during pool creation (config) and any pool active outputs pool stats to the console
     */
    public async logStats(): Promise<void> {
        if (!this.stats) {
            this.logger.log({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                message: 'Oracle stats are disabled!'
            });
            return;
        }

        if (!this.connectedPoolId) {
            this.logger.warn({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                message: 'No active pools to produce stats!'
            });
            return;
        }

        try {
            const pool = this.getPool(this.connectedPoolId);
            if (!pool) {
                this.logger.warn({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_WARN,
                    message: 'Unable to retrieve pool to query stats'
                });
                return;
            }

            await pool.logStats();
        } catch (error) {
            this.logger.log({
                error,
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                message: 'Oracle stats call caused error!'
            });
        }
    }

    public async disconnect(): Promise<void> {
        await this.awaitInitialisation();
        try {
            for (const pool of this.pools) {
                await pool.disconnect();
            }
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failure closing pool connection'
            });
        }
    }

    /**
     * Execute query
     *
     * TODO: we need to enforce bindings here to prevent SQL injection
     *
     * @param {string} sql
     * @param {Bindings} bindings
     * @return {Promise<RESULT[]>}
     */
    public async execute<RESULT = unknown>(sql: string, bindings?: Bindings): Promise<RESULT[]> {
        await this.awaitInitialisation();
        const activePool = this.activePool;
        if (!activePool) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                message: 'Cant execute sql query due to lack of active pools'
            });
        }

        try {
            // Get a connection from the default pool
            return await activePool.execute<RESULT>(sql, bindings);
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed execution of sql req'
            });
        }
    }

    private createPools() {
        try {
            // create pool for each provided string
            for (const connectString of this.connectStrings) {
                const pool = new OraclePool({
                    ...this.poolConfig,
                    connectString
                });
                this.pools.push(pool);
                this.logger.info({
                    sourceName: this.name,
                    message: 'Added connection pool to Oracle DB',
                    data: { connectString }
                });
            }
        } catch (error) {
            throw new Exception({
                error,
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL
            });
        }
    }

    private async connect(): Promise<void> {
        if (this.connectedPoolId) {
            this.logger.log({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                message: 'Found active pool connection - attempting to disconnect',
                data: { connectedPoolId: this.connectedPoolId }
            });
            // if connected pool - disconnect
            const activePool = this.activePool;
            if (activePool) {
                await activePool.disconnect();
            }
        }

        // try to connect to each pool until we connect, after few rounds throw error
        for (let round = 0; round < this.retryRoundMax && !this.connectedPoolId; round++) {
            for (const pool of this.pools) {
                this.logger.log({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_WARN,
                    message: 'Attempting pool connection',
                    data: { poolId: pool.id }
                });
                try {
                    // if initial connection ok, this is our active pool
                    await pool.connect();
                    this.connectedPoolId = pool.id;
                    this.logger.log({
                        sourceName: this.name,
                        code: EventCode.MOD_EXEC_WARN,
                        message: 'Pool connected',
                        data: { poolId: this.connectedPoolId }
                    });
                    break;
                } catch (error) {
                    this.logger.log({
                        sourceName: this.name,
                        code: EventCode.MOD_EXEC_WARN,
                        error,
                        message: 'Skipping pool due to initial connection failure'
                    });
                }
            }
            // await
            await Utils.sleep(1000);
        }

        if (!this.connectedPoolId) {
            // if we reached limit of rounds and are not connected
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Limit of consecutive reconnections reached'
            });
        }
    }

    /**
     * Get pool by id
     * @param {string} id
     * @returns {OraclePool | void}
     * @private
     */
    private getPool(id?: string): OraclePool | void {
        return this.pools.find((pool) => {
            return pool.id === id;
        });
    }

    /**
     * Apply config to the default config
     * @param {OracleConfig} config
     * @private
     */
    private applyConfig(config: OracleConfig) {
        const { user, password, poolMax, poolMin, pingInterval, timeout, queueMax, queueTimeout, stmtCacheSize, stats, connectStrings, retryRoundMax, appName } = config;
        if (!connectStrings || (connectStrings && !connectStrings.length)) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_INVALID_CONFIG,
                message: 'No connection strings provided'
            });
        }
        this.connectStrings = connectStrings;

        // if custom max retry number provided
        this.retryRoundMax = typeof retryRoundMax === 'number' ? retryRoundMax : 10;

        this.poolConfig = {
            connectString: '',
            user,
            password,
            poolMax,
            poolMin,
            pingInterval,
            timeout,
            queueMax,
            queueTimeout,
            stmtCacheSize,
            stats,
            logger: this.logger,
            appName
        };

        // Update env variable according to oracledb docs, all priory to creation of thread pools
        process.env.UV_THREADPOOL_SIZE = config.poolMax?.toString();
    }

    /**
     * Setup callbacks for pool cleanup
     * @private
     */
    private setupListeners() {
        this.system.once(SystemEvent.EXIT, this.disconnect.bind(this));
        this.system.once(SystemEvent.ERROR, this.disconnect.bind(this));
    }
}

export interface OracleConfig extends OracleBaseConfig, OracleDeps {
    // connection strings to all possible instances of DB
    connectStrings: string[];
    // how many times we should loop via all pools to reconnect before we throw error
    retryRoundMax?: number;
}

export interface OracleBaseConfig {
    // DB credentials
    user: string;
    password: string;
    // maximum size of the pool
    poolMax?: number;
    // start with no connections
    poolMin?: number;
    // check aliveness of connection if idle in the pool for X seconds
    pingInterval?: number;
    // terminate connections that are idle in the pool for X seconds
    timeout?: number;
    // don't allow more than X unsatisfied getConnection() calls in the pool queue
    queueMax?: number;
    // terminate getConnection() calls queued for longer than X milliseconds
    queueTimeout?: number;
    // number of statements that are cached in the statement cache of each connection
    stmtCacheSize?: number;
    // record pool usage statistics that can be output with pool._logStats()
    stats?: boolean;
    // traking the application connecting to oracle
    appName?: string;
}

export interface OracleDeps {
    logger: Logger;
    system: System;
}

export type Bindings = Record<string, BindingParam | BindingLike>;
export type BindingParam = OutputBinding | InputNumericBinding | InputStringBinding;
export type BindingLike = string | number | Date | DBObject_IN<any> | null | undefined;

interface OutputBinding {
    type: 'string' | 'number' | 'cursor';
    direction: 'out';
}

interface InputNumericBinding {
    type: 'number';
    direction: 'in';
    value?: number;
}

interface InputStringBinding {
    type: 'string';
    direction: 'in';
    value?: string;
}

/**
 * Check if binding definition
 * @param {BindingParam | unknown} binding
 * @return {binding is BindingParam}
 */
export function isBindingParam(binding: BindingParam | BindingLike): binding is BindingParam {
    if (!binding) {
        return false;
    }

    return typeof binding === 'object' && 'type' in binding && 'direction' in binding;
}
