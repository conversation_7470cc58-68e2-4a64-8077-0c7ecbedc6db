import { EventCode, Exception } from '@aa/exception';
import { Logger } from '@aa/logger';
import { Provider } from '@aa/provider';
import { System, SystemEvent } from '@aa/system';
import { Utils } from '@aa/utils';
import { OraclePool, OraclePoolConfig } from './oracle-pool-provider';
import { OracleTransaction } from './oracle-transaction';

/**
 * OracleDataProvider is a class responsible for managing connections to Oracle databases.
 * It handles connection pooling, transaction management, and query execution.
 */
export class OracleDataProvider extends Provider<OracleConfig> {
    protected name = 'Oracle data provider';
    private logger!: Logger;
    private system!: System;
    private connectedPoolId?: string;
    private stats = false;
    private pools!: OraclePool[];
    private retryRoundMax!: number;
    private connectStrings!: string[];
    private poolConfig!: OraclePoolConfig;

    /**
     * Returns the currently active connection pool, or null if no pool is active.
     */
    private get activePool(): OraclePool | null {
        return this.getPool(this.connectedPoolId) || null;
    }

    /**
     * Constructor for OracleDataProvider.
     * @param {OracleConfig} config - Configuration object for the OracleDataProvider.
     */
    constructor(config: OracleConfig) {
        super();
        this.initialise(config).catch((error) => {
            this.system.onException(error, true);
        });
    }

    /**
     * Initializes the OracleDataProvider with the provided configuration.
     * Sets up logging, system listeners, and connection pools.
     * @param {OracleConfig} config - Configuration object for initialization.
     */
    public async initialise(config: OracleConfig) {
        try {
            const { logger, system } = config;
            this.logger = logger;
            this.system = system;
            this.applyConfig(config);
            this.setupListeners();
        } catch (error) {
            throw new Exception({
                error,
                sourceName: this.name,
                code: EventCode.MOD_INIT_FAIL
            });
        }

        try {
            this.pools = [];
            this.createPools();
            await this.connect();
        } catch (error) {
            throw new Exception({
                error,
                sourceName: this.name,
                code: EventCode.MOD_INIT_FAIL
            });
        }

        this.ready();
        this.logger.log({
            sourceName: this.name,
            message: 'Oracle provider ready'
        });
    }

    /**
     * Logs statistics for the active connection pool if stats are enabled.
     * Outputs warnings if stats are disabled or no active pool is found.
     */
    public async logStats(): Promise<void> {
        if (!this.stats) {
            this.logger.log({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                message: 'Oracle stats are disabled!'
            });
            return;
        }

        if (!this.connectedPoolId) {
            this.logger.warn({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                message: 'No active pools to produce stats!'
            });
            return;
        }

        try {
            const pool = this.getPool(this.connectedPoolId);
            if (!pool) {
                this.logger.warn({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_WARN,
                    message: 'Unable to retrieve pool to query stats'
                });
                return;
            }

            await pool.logStats();
        } catch (error) {
            this.logger.log({
                error,
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                message: 'Oracle stats call caused error!'
            });
        }
    }

    /**
     * Disconnects all connection pools managed by the provider.
     * Throws an exception if any pool fails to disconnect.
     */
    public async disconnect(): Promise<void> {
        await this.awaitInitialisation();
        try {
            for (const pool of this.pools) {
                await pool.disconnect();
            }
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failure closing pool connection'
            });
        }
    }

    /**
     * Retrieves a new transaction object from the active connection pool.
     * Throws an exception if no active pool is available.
     * @returns {Promise<OracleTransaction>} - A new OracleTransaction instance.
     */
    public async getTransaction(): Promise<OracleTransaction> {
        await this.awaitInitialisation();
        const activePool = this.activePool;
        if (!activePool) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                message: 'Cant obtain transaction due to lack of active pools'
            });
        }

        return new OracleTransaction({ logger: this.logger, poll: activePool });
    }

    /**
     * Executes a SQL query using the active connection pool.
     * TODO: Enforce bindings to prevent SQL injection.
     * @template RESULT
     * @param {string} sql - The SQL query to execute.
     * @param {Bindings} [bindings] - Optional bindings for the query.
     * @returns {Promise<RESULT[]>} - The result of the query execution.
     */
    public async execute<RESULT = unknown>(sql: string, bindings?: Bindings): Promise<RESULT[]> {
        await this.awaitInitialisation();
        const activePool = this.activePool;
        if (!activePool) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                message: 'Cant execute sql query due to lack of active pools'
            });
        }

        try {
            // Get a connection from the default pool
            return await activePool.execute<RESULT>(sql, bindings);
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed execution of sql req'
            });
        }
    }

    /**
     * Creates connection pools for each provided connection string.
     * Logs the creation of each pool.
     */
    private createPools() {
        try {
            // create pool for each provided string
            for (const connectString of this.connectStrings) {
                const pool = new OraclePool({
                    ...this.poolConfig,
                    connectString
                });
                this.pools.push(pool);
                this.logger.info({
                    sourceName: this.name,
                    message: 'Added connection pool to Oracle DB',
                    data: { connectString }
                });
            }
        } catch (error) {
            throw new Exception({
                error,
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL
            });
        }
    }

    /**
     * Attempts to connect to the Oracle database using the available pools.
     * Retries connections up to the configured maximum number of rounds.
     * Throws an exception if all connection attempts fail.
     */
    private async connect(): Promise<void> {
        if (this.connectedPoolId) {
            this.logger.log({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                message: 'Found active pool connection - attempting to disconnect',
                data: { connectedPoolId: this.connectedPoolId }
            });
            // if connected pool - disconnect
            const activePool = this.activePool;
            if (activePool) {
                await activePool.disconnect();
            }
        }

        // try to connect to each pool until we connect, after few rounds throw error
        for (let round = 0; round < this.retryRoundMax && !this.connectedPoolId; round++) {
            for (const pool of this.pools) {
                this.logger.log({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_WARN,
                    message: 'Attempting pool connection',
                    data: { poolId: pool.id }
                });
                try {
                    // if initial connection ok, this is our active pool
                    await pool.connect();
                    this.connectedPoolId = pool.id;
                    this.logger.log({
                        sourceName: this.name,
                        code: EventCode.MOD_EXEC_WARN,
                        message: 'Pool connected',
                        data: { poolId: this.connectedPoolId }
                    });
                    break;
                } catch (error) {
                    this.logger.log({
                        sourceName: this.name,
                        code: EventCode.MOD_EXEC_WARN,
                        error,
                        message: 'Skipping pool due to initial connection failure'
                    });
                }
            }
            // await
            await Utils.sleep(1000);
        }

        if (!this.connectedPoolId) {
            // if we reached limit of rounds and are not connected
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Limit of consecutive reconnections reached'
            });
        }
    }

    /**
     * Retrieves a connection pool by its ID.
     * @param {string} [id] - The ID of the pool to retrieve.
     * @returns {OraclePool | void} - The matching pool, or undefined if not found.
     */
    private getPool(id?: string): OraclePool | void {
        return this.pools.find((pool) => {
            return pool.id === id;
        });
    }

    /**
     * Applies the provided configuration to the OracleDataProvider.
     * Updates environment variables and prepares the pool configuration.
     * @param {OracleConfig} config - The configuration object.
     */
    private applyConfig(config: OracleConfig) {
        const { user, password, poolMax, poolMin, pingInterval, timeout, queueMax, queueTimeout, stmtCacheSize, stats, connectStrings, retryRoundMax, appName } = config;
        if (!connectStrings || (connectStrings && !connectStrings.length)) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_INVALID_CONFIG,
                message: 'No connection strings provided'
            });
        }
        this.connectStrings = connectStrings;

        // if custom max retry number provided
        this.retryRoundMax = typeof retryRoundMax === 'number' ? retryRoundMax : 10;

        this.poolConfig = {
            connectString: '',
            user,
            password,
            poolMax,
            poolMin,
            pingInterval,
            timeout,
            queueMax,
            queueTimeout,
            stmtCacheSize,
            stats,
            logger: this.logger,
            appName
        };

        // Update env variable according to oracledb docs, all priory to creation of thread pools
        process.env.UV_THREADPOOL_SIZE = config.poolMax?.toString();
    }

    /**
     * Sets up system event listeners for pool cleanup on exit or error.
     */
    private setupListeners() {
        this.system.once(SystemEvent.EXIT, this.disconnect.bind(this));
        this.system.once(SystemEvent.ERROR, this.disconnect.bind(this));
    }
}

/**
 * Configuration interface for OracleDataProvider.
 */
export interface OracleConfig extends OracleBaseConfig, OracleDeps {
    connectStrings: string[]; // Connection strings to all possible instances of DB
    retryRoundMax?: number; // Maximum retry rounds for reconnection
}

/**
 * Base configuration interface for OracleDataProvider.
 */
export interface OracleBaseConfig {
    user: string; // Database username
    password: string; // Database password
    poolMax?: number; // Maximum size of the connection pool
    poolMin?: number; // Minimum size of the connection pool
    pingInterval?: number; // Interval to check connection aliveness
    timeout?: number; // Timeout for idle connections
    queueMax?: number; // Maximum unsatisfied getConnection() calls
    queueTimeout?: number; // Timeout for queued getConnection() calls
    stmtCacheSize?: number; // Number of cached statements per connection
    stats?: boolean; // Enable or disable pool usage statistics
    appName?: string; // Application name for tracking
}

/**
 * Dependencies required for OracleDataProvider.
 */
export interface OracleDeps {
    logger: Logger; // Logger instance
    system: System; // System instance
}

/**
 * Type definitions for SQL bindings.
 */
export type Bindings = Record<string, BindingParam | BindingLike>;
export type BindingParam = InputBinding | OutputBinding;
export type BindingLike = string | number | Date | null | undefined;

export type SimplifiedInputBinding =
    | InputNumericBinding
    | InputStringBinding
    | {
          type: 'string' | 'number' | 'cursor';
          direction: 'in';
          value?: unknown;
      };

export interface OracleInputBinding {
    type: number;
    dir: number;
    val?: unknown;
}

type InputBinding = SimplifiedInputBinding | OracleInputBinding;

export interface OracleOutputBinding {
    type: number;
    dir: number;
}

export interface SimplifiedOutputBinding {
    type: 'string' | 'number' | 'cursor';
    direction: 'out';
}

/**
 * Output binding definition for SQL queries.
 */
type OutputBinding = SimplifiedOutputBinding | OracleOutputBinding;

/**
 * Input numeric binding definition for SQL queries.
 */
interface InputNumericBinding {
    type: 'number';
    direction: 'in';
    value?: number;
}

/**
 * Input string binding definition for SQL queries.
 */
interface InputStringBinding {
    type: 'string';
    direction: 'in';
    value?: string;
}

/**
 * Checks if the provided binding is a valid BindingParam.
 * @param {BindingParam | unknown} binding - The binding to check.
 * @returns {binding is BindingParam} - True if the binding is a valid BindingParam.
 */
export function isBindingParam(binding: BindingParam | BindingLike): binding is BindingParam {
    if (!binding) {
        return false;
    }

    return typeof binding === 'object' && 'type' in binding && ('direction' in binding || 'dir' in binding);
}

export function isSimplifiedBinding(binding: BindingParam | BindingLike): binding is SimplifiedInputBinding | SimplifiedOutputBinding {
    return !!(binding && typeof binding === 'object' && 'direction' in binding);
}
