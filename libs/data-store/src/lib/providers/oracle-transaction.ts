import { EventCode, Exception } from '@aa/exception';
import { Logger } from '@aa/logger';
import { Connection } from 'oracledb';
import { Bindings } from './oracle-data-provider';
import { OraclePool } from './oracle-pool-provider';

/**
 * Represents a transactional operation using an Oracle database.
 */
export class OracleTransaction {
    /**
     * Name of the transaction.
     */
    protected name = 'Oracle transaction';

    /**
     * Database connection used for the transaction.
     */
    protected connection: Connection | undefined;

    /**
     * Oracle connection pool.
     */
    private poll: OraclePool;

    /**
     * Logger instance for logging transaction events.
     */
    private logger: Logger;

    /**
     * Creates an instance of OracleTransaction.
     * @param {OracleTransactionConfig} config - Configuration for the transaction.
     * @param {Logger} config.logger - Logger instance.
     * @param {OraclePool} config.poll - Oracle connection pool.
     */
    constructor({ logger, poll }: OracleTransactionConfig) {
        this.poll = poll;
        this.logger = logger;
    }

    /**
     * Ensures the transaction is initialized by obtaining a database connection.
     * @throws {Exception} If obtaining the connection fails.
     */
    public async awaitInitialisation(): Promise<void> {
        if (this.connection) {
            return;
        }

        try {
            // Get a connection from the active pool
            this.connection = await this.poll.getConnection();
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed obtaining connection for transaction'
            });
        }
    }

    /**
     * Executes a SQL query within the transaction.
     * @template RESULT
     * @param {string} sql - The SQL query to execute.
     * @param {Bindings} [bindings] - Optional bindings for the query.
     * @returns {Promise<RESULT[]>} The result of the query execution.
     * @throws {Exception} If the query execution fails.
     */
    public async execute<RESULT>(sql: string, bindings?: Bindings): Promise<RESULT[]> {
        try {
            this.logger.warn({ sourceName: this.name, message: 'Executing transaction started' });
            await this.awaitInitialisation();
            const result = await this.poll.execute<RESULT>(sql, bindings, {
                connection: this.connection,
                closeConnection: false
            });
            this.logger.warn({ sourceName: this.name, message: 'Executing transaction finished' });
            return result;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                message: 'Failure while executing transaction',
                error
            });
        }
    }

    /**
     * Commits the transaction, persisting all changes.
     * @throws {Exception} If the commit operation fails.
     */
    public async commit(): Promise<void> {
        try {
            this.logger.warn({ sourceName: this.name, message: 'Commiting transaction started' });
            await this.awaitInitialisation();
            // Commit all execution commands up to this point
            await this.connection?.commit();
            this.logger.warn({ sourceName: this.name, message: 'Commiting transaction finished' });
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed commiting transaction'
            });
        }
    }

    /**
     * Rolls back the transaction, undoing all changes.
     * @throws {Exception} If the rollback operation fails.
     */
    public async rollback(): Promise<void> {
        try {
            this.logger.warn({ sourceName: this.name, message: 'Rolling back transaction started' });
            await this.awaitInitialisation();
            // Rollback execution commands up to this point
            await this.connection?.rollback();
            this.logger.warn({ sourceName: this.name, message: 'Rolling back transaction finished' });
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed rolling back transaction'
            });
        }
    }
}

/**
 * Configuration for creating an OracleTransaction instance.
 */
interface OracleTransactionConfig {
    /**
     * Logger instance for logging transaction events.
     */
    logger: Logger;

    /**
     * Oracle connection pool.
     */
    poll: OraclePool;
}
