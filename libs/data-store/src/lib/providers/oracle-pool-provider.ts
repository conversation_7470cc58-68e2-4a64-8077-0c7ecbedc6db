import { EventCode, Exception } from '@aa/exception';
import { Logger } from '@aa/logger';
import { Provider } from '@aa/provider';
import { Utils } from '@aa/utils';
import oraceldb, { BindParameters, Connection, OUT_FORMAT_ARRAY, OUT_FORMAT_OBJECT, Pool, POOL_STATUS_OPEN, Result } from 'oracledb';
import { Bindings, OracleBaseConfig } from './oracle-data-provider';
import { OracleUtils } from './oracle-utils';

export class OraclePool extends Provider<OraclePoolConfig> {
    public readonly id = Utils.uuid();
    // Default config
    protected name = 'Oracle pool provider';
    private logger!: Logger;
    private connectString!: string;
    private user!: string;
    private password!: string;
    private poolMax = 4;
    private poolMin = 0;
    private pingInterval = 60;
    private timeout = 60;
    private queueMax = 500;
    private queueTimeout = 60000;
    private stmtCacheSize = 30;
    private stats = false;
    private pool!: Pool;
    private appName?: string = 'unknown';

    public get active(): boolean {
        return this.pool.status === POOL_STATUS_OPEN;
    }

    public get status(): PoolStatus {
        return this.active ? PoolStatus.OPEN : PoolStatus.CLOSED;
    }

    constructor(config: OraclePoolConfig) {
        super();
        this.initialise(config).catch((error) => {
            this.logger.warn({
                error,
                message: 'Failure in initialising one of the Oracle pools'
            });
        });
    }

    public async initialise({ logger, ...config }: OraclePoolConfig) {
        try {
            this.logger = logger;
            this.applyConfig(config);
        } catch (error) {
            throw new Exception({
                error,
                sourceName: this.name,
                code: EventCode.MOD_CREATE_FAIL
            });
        }

        try {
            // Create a connection pool which will later be accessed via the pool cache via an alias.
            this.pool = await oraceldb.createPool({
                poolAlias: this.id,
                poolPingInterval: this.pingInterval,
                poolTimeout: this.timeout,
                enableStatistics: this.stats,
                poolMax: this.poolMax,
                poolMin: this.poolMin,
                user: this.user,
                password: this.password,
                connectString: this.connectString,
                queueMax: this.queueMax,
                queueTimeout: this.queueTimeout,
                stmtCacheSize: this.stmtCacheSize
            });
        } catch (error) {
            throw new Exception({
                code: EventCode.DB_CONNECTION_FAIL,
                error,
                sourceName: this.name,
                message: 'Failed pool creation'
            });
        }

        this.ready();
        this.logger.log({
            sourceName: this.name,
            message: `Oracle pool provider ready for ${this.connectString}`,
            data: { id: this.id }
        });
    }

    public async execute<RESULT>(sql: string, bindings?: Bindings): Promise<RESULT[]> {
        let poolConnection: Connection;
        try {
            poolConnection = await this.pool.getConnection();
            poolConnection.module = this.appName;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_CONNECTION_FAIL,
                message: 'Unable to obtain connection from the pool',
                error
            });
        }

        try {
            const bindParams: BindParameters = OracleUtils.mapBindings(bindings);
            const rawResults = await poolConnection.execute<RESULT>(sql, bindParams, {
                outFormat: OUT_FORMAT_ARRAY,
                autoCommit: true
            });

            // if data in rows
            if (rawResults.rows) {
                return OracleUtils.getResultsFromRows<RESULT>(rawResults);
            }

            // if update return count of affected
            if (typeof rawResults.rowsAffected !== 'undefined') {
                return [rawResults.rowsAffected as unknown as RESULT];
            }

            // if not in rows then probably in bindings
            if (bindings) {
                const results = await OracleUtils.getResultFromOutParams<RESULT>(bindings, rawResults as unknown as Result<RESULT>);
                return results ? [results] : [];
            }

            // if none above - no results
            return [];
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                message: 'Failed executing sql query',
                error
            });
        } finally {
            if (poolConnection) {
                // Put the connection back in the pool
                await poolConnection.close();
            }
        }
    }

    public async logStats() {
        await this.awaitInitialisation();
        this.pool.logStatistics();
    }

    public async connect(): Promise<void> {
        try {
            await this.awaitInitialisation();
            this.logger.log({
                sourceName: this.name,
                message: `Connecting to the pool ${this.id}`,
                data: { connectString: this.connectString }
            });
            // initial connection to establish active pool
            const connection = await this.pool.getConnection();
            await connection.close();
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_CONNECTION_FAIL,
                message: 'Failed connecting to the pool'
            });
        }
    }

    public async disconnect(): Promise<void> {
        await this.awaitInitialisation();
        if (this.active) {
            this.logger.log({
                sourceName: this.name,
                message: `Closing active connection for the pool ${this.id}`
            });
            // close pool when no connections are in use, or force it closed after 10 seconds.
            await this.pool.close(10);
            this.logger.log({
                sourceName: this.name,
                message: `Closed connection for the pool ${this.id}`
            });
        }
    }

    /**
     * Apply config to the default config
     * @param {OraclePoolConfig} config
     * @private
     */
    private applyConfig(config: Omit<OraclePoolConfig, 'logger'>) {
        const { connectString, password, pingInterval, poolMax, poolMin, queueMax, queueTimeout, stats, stmtCacheSize, timeout, user, appName } = config;

        if (!connectString) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_INVALID_CONFIG,
                message: 'No connection string provided'
            });
        }
        this.connectString = connectString;

        if (!user || !password) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_INVALID_CONFIG,
                message: 'Missing DB username or password'
            });
        }
        this.user = user;
        this.password = password;

        if (config.poolMax === 0) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_INVALID_CONFIG,
                message: 'Pool max size set to 0 - clearly not what you want, right?!'
            });
        }

        this.poolMax = poolMax && !isNaN(poolMax as number) ? poolMax : this.poolMax;
        this.poolMin = poolMin && !isNaN(poolMin as number) ? poolMin : this.poolMin;
        this.pingInterval = pingInterval && !isNaN(pingInterval as number) ? pingInterval : this.pingInterval;
        this.queueMax = queueMax && !isNaN(queueMax as number) ? queueMax : this.queueMax;
        this.queueTimeout = queueTimeout && !isNaN(queueTimeout as number) ? queueTimeout : this.queueTimeout;
        this.stmtCacheSize = stmtCacheSize && !isNaN(stmtCacheSize as number) ? stmtCacheSize : this.stmtCacheSize;
        this.timeout = timeout && !isNaN(timeout as number) ? timeout : this.timeout;
        this.stats = typeof stats === 'boolean' ? stats : this.stats;
        this.appName = appName ?? 'unknown';
    }
}

export interface OraclePoolConfig extends OracleBaseConfig, OraclePoolDeps {
    // connection string for specific pool
    connectString: string;
}

export interface OraclePoolDeps {
    logger: Logger;
}

enum PoolStatus {
    OPEN = 'OPEN',
    CLOSED = 'CLOSED'
}
