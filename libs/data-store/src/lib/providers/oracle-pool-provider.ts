import { EventCode, Exception } from '@aa/exception';
import { Logger } from '@aa/logger';
import { Provider } from '@aa/provider';
import { Utils } from '@aa/utils';
import oraceldb, { BindParameters, Connection, OUT_FORMAT_ARRAY, Pool, POOL_STATUS_OPEN, Result } from 'oracledb';
import { Bindings, OracleBaseConfig } from './oracle-data-provider';
import { OracleUtils } from './oracle-utils';

/**
 * Represents an Oracle connection pool provider.
 * Manages database connections and provides methods for executing SQL queries.
 */
export class OraclePool extends Provider<OraclePoolConfig> {
    /**
     * Unique identifier for the pool.
     */
    public readonly id = Utils.uuid();

    /**
     * Name of the provider.
     */
    protected name = 'Oracle pool provider';

    /**
     * Logger instance for logging events.
     */
    private logger!: Logger;

    /**
     * Database connection string.
     */
    private connectString!: string;

    /**
     * Database username.
     */
    private user!: string;

    /**
     * Database password.
     */
    private password!: string;

    /**
     * Maximum number of connections in the pool.
     */
    private poolMax = 4;

    /**
     * Minimum number of connections in the pool.
     */
    private poolMin = 0;

    /**
     * Interval for pinging the database to keep connections alive (in seconds).
     */
    private pingInterval = 60;

    /**
     * Timeout for idle connections (in seconds).
     */
    private timeout = 60;

    /**
     * Maximum number of requests in the queue.
     */
    private queueMax = 500;

    /**
     * Timeout for requests in the queue (in milliseconds).
     */
    private queueTimeout = 60000;

    /**
     * Size of the statement cache.
     */
    private stmtCacheSize = 30;

    /**
     * Flag indicating whether to enable statistics.
     */
    private stats = false;

    /**
     * Oracle connection pool instance.
     */
    private pool!: Pool;

    /**
     * Application name for the connection.
     */
    private appName?: string = 'unknown';

    /**
     * Indicates whether the pool is active.
     */
    public get active(): boolean {
        return this.pool.status === POOL_STATUS_OPEN;
    }

    /**
     * Returns the current status of the pool.
     */
    public get status(): PoolStatus {
        return this.active ? PoolStatus.OPEN : PoolStatus.CLOSED;
    }

    /**
     * Creates an instance of OraclePool.
     * @param {OraclePoolConfig} config - Configuration for the Oracle pool.
     */
    constructor(config: OraclePoolConfig) {
        super();
        this.initialise(config).catch((error) => {
            this.logger.warn({
                error,
                message: 'Failure in initialising one of the Oracle pools'
            });
        });
    }

    /**
     * Initializes the Oracle pool with the provided configuration.
     * @param {OraclePoolConfig} config - Configuration for the Oracle pool.
     * @throws {Exception} If initialization fails.
     */
    public async initialise({ logger, ...config }: OraclePoolConfig) {
        try {
            this.logger = logger;
            this.applyConfig(config);
        } catch (error) {
            throw new Exception({
                error,
                sourceName: this.name,
                code: EventCode.MOD_CREATE_FAIL
            });
        }

        try {
            // Create a connection pool with the specified configuration.
            this.pool = await oraceldb.createPool({
                poolAlias: this.id,
                poolPingInterval: this.pingInterval,
                poolTimeout: this.timeout,
                enableStatistics: this.stats,
                poolMax: this.poolMax,
                poolMin: this.poolMin,
                user: this.user,
                password: this.password,
                connectString: this.connectString,
                queueMax: this.queueMax,
                queueTimeout: this.queueTimeout,
                stmtCacheSize: this.stmtCacheSize
            });
        } catch (error) {
            throw new Exception({
                code: EventCode.DB_CONNECTION_FAIL,
                error,
                sourceName: this.name,
                message: 'Failed pool creation'
            });
        }

        this.ready();
        this.logger.log({
            sourceName: this.name,
            message: `Oracle pool provider ready for ${this.connectString}`,
            data: { id: this.id }
        });
    }

    /**
     * Executes a SQL query using the pool.
     * @template RESULT
     * @param {string} sql - The SQL query to execute.
     * @param {Bindings} [bindings] - Optional bindings for the query.
     * @param {Object} [options] - Additional options for execution.
     * @param {Connection} [options.connection] - Optional connection to use.
     * @param {boolean} [options.closeConnection] - Whether to close the connection after execution.
     * @returns {Promise<RESULT[]>} The result of the query execution.
     * @throws {Exception} If the query execution fails.
     */
    public async execute<RESULT>(
        sql: string,
        bindings?: Bindings,
        options?: {
            connection?: Connection;
            closeConnection?: boolean;
        }
    ): Promise<RESULT[]> {
        // by default we want to close connection
        const { connection, closeConnection = true } = options || {};
        let poolConnection: Connection;
        try {
            poolConnection = connection || (await this.pool.getConnection());
            poolConnection.module = this.appName;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_CONNECTION_FAIL,
                message: 'Unable to obtain connection from the pool',
                error
            });
        }

        try {
            const bindParams: BindParameters = OracleUtils.mapBindings(bindings);
            const rawResults = await poolConnection.execute<RESULT>(sql, bindParams, {
                outFormat: OUT_FORMAT_ARRAY,
                autoCommit: true
            });

            if (rawResults.rows) {
                return OracleUtils.getResultsFromRows<RESULT>(rawResults);
            }

            if (typeof rawResults.rowsAffected !== 'undefined') {
                return [rawResults.rowsAffected as unknown as RESULT];
            }

            if (bindings) {
                return await OracleUtils.getResultsFromOutParams<RESULT>(bindings, rawResults as unknown as Result<RESULT>);
            }

            return [];
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                message: 'Failed executing sql query',
                error
            });
        } finally {
            if (poolConnection && closeConnection) {
                await poolConnection.close();
            }
        }
    }

    /**
     * Logs statistics for the pool.
     */
    public async logStats() {
        await this.awaitInitialisation();
        this.pool.logStatistics();
    }

    /**
     * Obtains a connection from the pool.
     * @returns {Promise<Connection>} The obtained connection.
     * @throws {Exception} If obtaining the connection fails.
     */
    public async getConnection(): Promise<Connection> {
        await this.awaitInitialisation();
        try {
            return this.pool.getConnection();
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_CONNECTION_FAIL,
                message: 'Unable to obtain connection from the pool',
                error
            });
        }
    }

    /**
     * Establishes a connection to the pool.
     * @throws {Exception} If the connection fails.
     */
    public async connect(): Promise<void> {
        try {
            await this.awaitInitialisation();
            this.logger.log({
                sourceName: this.name,
                message: `Connecting to the pool ${this.id}`,
                data: { connectString: this.connectString }
            });
            const connection = await this.pool.getConnection();
            await connection.close();
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_CONNECTION_FAIL,
                message: 'Failed connecting to the pool'
            });
        }
    }

    /**
     * Closes the pool and releases all resources.
     * @throws {Exception} If the disconnection fails.
     */
    public async disconnect(): Promise<void> {
        await this.awaitInitialisation();
        if (this.active) {
            this.logger.log({
                sourceName: this.name,
                message: `Closing active connection for the pool ${this.id}`
            });
            await this.pool.close(10);
            this.logger.log({
                sourceName: this.name,
                message: `Closed connection for the pool ${this.id}`
            });
        }
    }

    /**
     * Applies the provided configuration to the pool.
     * @param {OraclePoolConfig} config - Configuration for the Oracle pool.
     * @private
     * @throws {Exception} If the configuration is invalid.
     */
    private applyConfig(config: Omit<OraclePoolConfig, 'logger'>) {
        const { connectString, password, pingInterval, poolMax, poolMin, queueMax, queueTimeout, stats, stmtCacheSize, timeout, user, appName } = config;

        if (!connectString) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_INVALID_CONFIG,
                message: 'No connection string provided'
            });
        }
        this.connectString = connectString;

        if (!user || !password) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_INVALID_CONFIG,
                message: 'Missing DB username or password'
            });
        }
        this.user = user;
        this.password = password;

        if (config.poolMax === 0) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_INVALID_CONFIG,
                message: 'Pool max size set to 0 - clearly not what you want, right?!'
            });
        }

        this.poolMax = poolMax && !isNaN(poolMax as number) ? poolMax : this.poolMax;
        this.poolMin = poolMin && !isNaN(poolMin as number) ? poolMin : this.poolMin;
        this.pingInterval = pingInterval && !isNaN(pingInterval as number) ? pingInterval : this.pingInterval;
        this.queueMax = queueMax && !isNaN(queueMax as number) ? queueMax : this.queueMax;
        this.queueTimeout = queueTimeout && !isNaN(queueTimeout as number) ? queueTimeout : this.queueTimeout;
        this.stmtCacheSize = stmtCacheSize && !isNaN(stmtCacheSize as number) ? stmtCacheSize : this.stmtCacheSize;
        this.timeout = timeout && !isNaN(timeout as number) ? timeout : this.timeout;
        this.stats = typeof stats === 'boolean' ? stats : this.stats;
        this.appName = appName ?? 'unknown';
    }
}

/**
 * Configuration for the Oracle pool provider.
 */
export interface OraclePoolConfig extends OracleBaseConfig, OraclePoolDeps {
    /**
     * Connection string for the Oracle database.
     */
    connectString: string;
}

/**
 * Dependencies required for the Oracle pool provider.
 */
export interface OraclePoolDeps {
    /**
     * Logger instance for logging events.
     */
    logger: Logger;
}

/**
 * Enum representing the status of the pool.
 */
enum PoolStatus {
    OPEN = 'OPEN',
    CLOSED = 'CLOSED'
}
