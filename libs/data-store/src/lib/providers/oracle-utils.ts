import { EventCode, Exception } from '@aa/exception';
import { BIND_IN, BIND_OUT, BindParameter, BindParameters, CURSOR, NUMBER, Result, ResultSet, STRING } from 'oracledb';
import { Bindings, isBindingParam, isSimplifiedBinding } from './oracle-data-provider';

const sourceName = 'Oracle utils';

export class OracleUtils {
    public static getResultsFromRows<TYPE = Record<string, unknown>>(rawResults: Pick<Result<TYPE>, 'rows' | 'metaData'>): TYPE[] {
        const { rows, metaData } = rawResults;
        if (!rows || !metaData) {
            return [];
        }

        return rows.map((row) => {
            const result: Record<string, unknown> = {};

            for (let i = 0; i < metaData.length; i++) {
                result[metaData[i].name] = (row as unknown as Array<unknown>)[i];
            }

            return result as TYPE;
        });
    }

    public static async getResultsFromOutParams<TYPE = Record<string, unknown>>(bindings: Bindings = {}, rawResults: Result<TYPE>): Promise<TYPE[]> {
        // if no output bindings expected
        if (!Object.keys(bindings).length) {
            return [];
        }

        const { outBinds } = rawResults;
        // if no output bindings
        if (!outBinds) {
            return [];
        }
        // search for first outbind config that is of cursor type
        // Note: we should search for all configs, but there is no case in our code that uses
        // anything else than "cursor" binding

        const cursorKey = Object.keys(bindings).find((key) => {
            const binding = bindings[key];
            return isBindingParam(binding) && (binding.type === 'cursor' || binding.type === CURSOR);
        });

        if (!cursorKey) {
            return [];
        }

        // if cursor found lets get data from it
        const cursor = (outBinds as Record<string, ResultSet<TYPE>>)[cursorKey];
        return await OracleUtils.getResultsFromResultSet(cursor);
    }

    public static async getResultsFromResultSet<TYPE = Record<string, unknown>>(resultSet: ResultSet<TYPE>): Promise<TYPE[]> {
        const resultStream = resultSet.toQueryStream();

        try {
            const rows: any[] = await new Promise((resolve, reject) => {
                const results: unknown[] = [];
                resultStream.on('error', function (error) {
                    reject(error);
                });
                resultStream.on('data', function (data) {
                    results.push(data);
                });
                resultStream.on('end', function () {
                    resultStream.destroy();
                });

                resultStream.on('close', function () {
                    resolve(results);
                });
            });

            resultStream.destroy();

            const { metaData } = resultSet;
            const rawResults: Pick<Result<TYPE>, 'rows' | 'metaData'> = {
                rows,
                metaData
            };

            return OracleUtils.getResultsFromRows<TYPE>(rawResults);
        } catch (error) {
            throw new Exception({
                sourceName,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed retrieving ResultSets'
            });
        }
    }

    public static mapBindings(bindings: Bindings = {}): BindParameters {
        const bindingParams: BindParameters = {};

        for (const [key, config] of Object.entries(bindings)) {
            if (isBindingParam(config)) {
                bindingParams[key] = config;
            } else if (isSimplifiedBinding(config)) {
                const finalBinding: BindParameter = {};
                finalBinding.dir = isSimplifiedBinding(config) && config.direction === 'out' ? BIND_OUT : BIND_IN;

                switch (config.type) {
                    case 'string':
                        finalBinding.type = STRING;
                        break;
                    case 'number':
                        finalBinding.type = NUMBER;
                        break;
                    case 'cursor':
                        finalBinding.type = CURSOR;
                        break;
                }

                // if value provided for input binding
                if (isSimplifiedBinding(config) && config.direction === 'in' && typeof config.value !== undefined) {
                    finalBinding.val = config.value;
                }

                bindingParams[key] = finalBinding;
            } else {
                const finalBinding: BindParameter = {
                    dir: BIND_IN,
                    val: config
                };

                // we treat it as 'in'
                switch (typeof config) {
                    case 'string':
                        finalBinding.type = STRING;
                        break;
                    case 'number':
                        finalBinding.type = NUMBER;
                        break;
                }

                bindingParams[key] = finalBinding;
            }
        }

        return bindingParams;
    }
}
