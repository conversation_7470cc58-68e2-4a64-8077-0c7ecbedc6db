const { SeSystem } = require('@aa/data-models/common');
var _ = require('lodash'),
    AssistanceType = require('@aa/malstrom-models/lib/assistance-type.model'),
    Task = require('@aa/malstrom-models/lib/task.model'),
    MobilityTask = require('./mobility-task.model'),
    FinancialTask = require('@aa/malstrom-models/lib/financial-task.model'),
    FlpTask = require('@aa/malstrom-models/lib/flp-task.model'),
    CommendationTask = require('@aa/malstrom-models/lib/commendation-task.model'),
    UpdateTask = require('@aa/malstrom-models/lib/update-task.model'),
    ArchivedTask = require('@aa/malstrom-models/lib/archived-task.model'),
    SeUpdateTask = require('@aa/malstrom-models/lib/se-update-task.model'),
    RefCode = require('@aa/malstrom-models/lib/ref-code.model'),
    //helpers
    DateUtil = require('@aa/utilities/date');

function CustomerRequest(raw) {
    var model = this,
        _assistType = new AssistanceType(),
        _contractKey,
        _creationTime,
        _customerKey,
        _memberReference,
        _id = -1,
        _custShortName,
        _callInfoPresent,
        _noOfLiveTasks,
        _seLocatorId,
        _seSystem = new SeSystem(),
        _status = new RefCode(),
        _statusUpdatedTime,
        _altJobLabel,
        _tasks = [];

    if (raw) {
        _assistType = raw.assistType ? new AssistanceType(raw.assistType) : _assistType;
        _contractKey = raw.contractKey;
        _creationTime = raw.creationTime ? new Date(raw.creationTime) : null;
        _customerKey = raw.customerKey;
        _memberReference = raw.memberReference;
        _id = raw.id;
        _custShortName = raw.custShortName;
        _callInfoPresent = raw.callInfoPresent;
        _noOfLiveTasks = raw.noOfLiveTasks;
        _seLocatorId = raw.seLocatorId;
        _seSystem = raw.seSystem ? new SeSystem(raw.seSystem) : _seSystem;
        _status = new RefCode(raw.status);
        _statusUpdatedTime = raw.statusUpdatedTime ? new Date(raw.statusUpdatedTime) : null;
        _altJobLabel = raw.altJobLabel;
        _.forEach(raw.tasks, function (task) {
            switch (task.taskType.code) {
                case 'FIN':
                    _tasks.push(new FinancialTask(task));
                    break;
                case 'FLP':
                    _tasks.push(new FlpTask(task));
                    break;
                case 'TUPD':
                    _tasks.push(new UpdateTask(task));
                    break;
                case 'CMD':
                    _tasks.push(new CommendationTask(task));
                    break;
                case 'BRK':
                    _tasks.push(new Task(task));
                    break;
                case 'ADM':
                    _tasks.push(new MobilityTask(task));
                    break;
                case 'ARCH':
                    _tasks.push(new ArchivedTask(task));
                    break;
                case 'SEU':
                    _tasks.push(new SeUpdateTask(task));
                    break;
                default:
                    //ignore the task not one we are interested in
                    break;
            }
        });
    }

    _.extend(model, {
        assistType: function assistTypeAccessor(val) {
            return arguments.length > 0 ? (_assistType = val) : _assistType;
        },
        contractKey: function contractKeyAccessor(val) {
            return arguments.length > 0 ? (_contractKey = val) : _contractKey;
        },
        creationTime: function creation_timeAccessor(val) {
            return arguments.length > 0 ? (_creationTime = val) : _creationTime;
        },
        customerKey: function custKeyAccessor(val) {
            return arguments.length > 0 ? (_customerKey = val) : _customerKey;
        },
        id: function idAccessor(val) {
            return arguments.length > 0 ? (_id = val) : _id;
        },
        custShortName: function custShortNameAccessor(val) {
            return arguments.length > 0 ? (_custShortName = val) : _custShortName;
        },
        callInfoPresent: function callInfoPresentAccessor(val) {
            return arguments.length > 0 ? (_callInfoPresent = val) : _callInfoPresent;
        },
        noOfLiveTasks: function noLiveTasksAccessor(val) {
            return arguments.length > 0 ? (_noOfLiveTasks = val) : _noOfLiveTasks;
        },
        seLocatorId: function seLocatorIdAccessor(val) {
            return arguments.length > 0 ? (_seLocatorId = val) : _seLocatorId;
        },
        seSystem: function seSysIdAccessor(val) {
            return arguments.length > 0 ? (_seSystem = val) : _seSystem;
        },
        status: function statusAccessor(val) {
            return arguments.length > 0 ? (_status = val) : _status;
        },
        statusUpdatedTime: function statusUpdateTimeAccessor(val) {
            return arguments.length > 0 ? (_statusUpdatedTime = val) : _statusUpdatedTime;
        },
        altJobLabel: function altJobLabelAccessor(val) {
            return arguments.length > 0 ? (_altJobLabel = val) : _altJobLabel;
        },
        memberReference: function memberReferenceAccessor(val) {
            return arguments.length > 0 ? (_memberReference = val) : _memberReference;
        },
        tasks: function tasksAccessor(val) {
            return arguments.length > 0 ? (_tasks = val) : _tasks;
        },
        addTasks: function addTaskMethod(tasks) {
            _.forEach(tasks, function (task) {
                model.addTask(task);
            });
        },
        /**
         * add - replace task in _tasks
         * @param {[type]} val [description]
         */
        addTask: function addTaskMethod(val) {
            var idx = _.findIndex(_tasks, function (item) {
                return item.id() === val.id();
            });
            if (idx > -1) {
                _tasks[idx] = val;
            } else {
                // insert ...
                _tasks.push(val);
            }
        },
        /**
         * find a task in _tasks by id if not found return null
         * @param  {number} id of the task to search
         * @return {Task}
         */
        findTask: function customerRequestSearch(task) {
            var retVal;

            if (task.hasOwnProperty('id')) {
                retVal = _.find(_tasks, function (item) {
                    return item.id() === task.id();
                });
            } else {
                retVal = _.find(_tasks, function (item) {
                    return item.id() === task;
                });
            }

            return retVal;
        },
        /**
         * remove task from _tasks
         * @param  {number | Task} val identifies task to remove
         */
        removeTask: function removeTask(val) {
            var id = _.isNumber(val) ? val : val.id(),
                idx = _.findIndex(_tasks, function (item) {
                    return item.id() === id;
                });

            if (idx > -1) {
                _tasks.splice(idx, 1);
            }
        },
        isActive: function completionTest() {
            return _status.code() !== 'COMP' && _status.code() !== 'CLSD';
        },
        isCompWithin7Days: function isCompWithin7Days() {
            return (_status.code() === 'COMP' || _status.code() === 'CLSD') && DateUtil.durationInDaysFromNow(new Date(model.statusUpdatedTime())) < 7;
        },
        isOKToAutoLoadTask: function isOKToAutoLoadTask() {
            //TODO: this code should be checking the createTime of the tasks in the CR, but because
            //we haven't got any tasks, will have to use createTime of CR for now...
            //only auto-load active tasks on a CR created within 2 days [NB: will reduce to 1 when we check task create time]
            if (DateUtil.durationInDaysFromNow(model.creationTime()) < 2) {
                return true;
            } else {
                return false;
            }

            //PLEASE LEAVE AS THIS CONTAINS LEGACY LOGIC AROUND RELAY PLUS TASKS WHICH WE WILL
            //NEED TO ADD WHEN WE GET A TASK SUMMARY BACK WITH CR
            //var returnTask = _.find(model.tasks(), function(task) {
            //	return DateUtil.durationInDaysFromNow(task.schedule().creationTime() < 1) &&
            //		!(task.createReason().isRelayPlus() && model.noOfLiveTasks() === 1);
            //});
            //return returnTask;
        },
        isCompleted: function () {
            var completedTask = true;
            _.forEach(_tasks, function (task) {
                completedTask = completedTask && task.isCompleted();
            });

            return completedTask;
        },
        // trying to stop from pulling all tasks back to server
        toJSONExcludeTasks: function toJSONExcludeTasks() {
            return {
                assistType: _assistType ? _assistType.toJSON() : null,
                contractKey: _contractKey,
                creationTime: _creationTime,
                customerKey: _customerKey,
                memberReference: _memberReference,
                id: _id,
                custShortName: _custShortName,
                callInfoPresent: _callInfoPresent,
                noOfLiveTasks: _noOfLiveTasks,
                seLocatorId: _seLocatorId,
                seSystem: _seSystem ? _seSystem.toJSON() : null,
                status: _status.toJSON(),
                statusUpdatedTime: _statusUpdatedTime,
                altJobLabel: _altJobLabel
            };
        },
        toJSON: function toJSON() {
            var jsTasks = [];

            _.forEach(_tasks, function (task) {
                jsTasks.push(task.toJSON());
            });

            return {
                assistType: _assistType ? _assistType.toJSON() : null,
                contractKey: _contractKey,
                creationTime: _creationTime,
                customerKey: _customerKey,
                memberReference: _memberReference,
                id: _id,
                custShortName: _custShortName,
                callInfoPresent: _callInfoPresent,
                noOfLiveTasks: _noOfLiveTasks,
                seLocatorId: _seLocatorId,
                seSystem: _seSystem ? _seSystem.toJSON() : null,
                status: _status.toJSON(),
                statusUpdatedTime: _statusUpdatedTime,
                altJobLabel: _altJobLabel,
                tasks: jsTasks // no toJSON here as we want a key value pair returned ...
            };
        }
    });
}

module.exports = CustomerRequest;
