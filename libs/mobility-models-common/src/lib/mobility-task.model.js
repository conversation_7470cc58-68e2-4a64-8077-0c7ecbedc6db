const _ = require('lodash');
const Task = require('@aa/malstrom-models/lib/task.model');
const createReasonFactory = require('@aa/malstrom-models/lib/factories/create-reason.factory');

const CarRental = require('@aa/mobility-models/lib/car-rental.model');
const MobilityInsurance = require('@aa/mobility-models/lib/mobility-insurance.model');
const HireSite = require('@aa/mobility-models/lib/hire-site.model');

/**
 * mobilty task inherits from AbstractAncillaryTask
 * @class  MobilityTask
 * @extends AbstractAncillaryTask
 * @param {Object=} raw jason represetntation of MobilityTasj
 */
function MobilityTask(raw) {
    var model = this,
        _abstractTask = new Task(raw),
        _rental = new CarRental(),
        _hireFleetSupplierNetwork = () => {
            // not a hire that is insured by coopers
            //  -- below might be needed before, now commenting to avoid conversion from HYUA to JLR
            // if (_rental.insurance().type() !== MobilityInsurance.TYPE) {
            //     return HireSite.NETWORK_CODES.JAGUAR;
            // }

            if (_abstractTask.entitlement().customerGroup().isJaguar()) {
                return HireSite.NETWORK_CODES.JAGUAR;
            }

            if (_abstractTask.entitlement().customerGroup().isPorsche()) {
                return HireSite.NETWORK_CODES.PORSCHE;
            }

            if (_abstractTask.entitlement().customerGroup().isLandRover()) {
                return HireSite.NETWORK_CODES.LAND_ROVER;
            }

            if (_abstractTask.entitlement().customerGroup().isHyundai()) {
                return HireSite.NETWORK_CODES.HYUNDAI;
            }
            return HireSite.NETWORK_CODES.JAGUAR; // default to JAGUAR ...
        };

    _hireFleetSupplierNetwork = _hireFleetSupplierNetwork.bind(this);
    // set the supplierNewtork code for the insurance ...
    // this is here because of the _extend ..

    if (raw) {
        //
        // enforce taskType to be ADM because not always comes back from
        //
        _abstractTask.taskType().code('ADM');
        _abstractTask.taskType().name('Administration');
        /**
         * rental
         */
        _rental = raw.rental ? new CarRental(raw.rental) : _rental;
        // we now evalute the insurance supNetworkCode ... based on customer group..
        _rental.insurance().supNetworkCode(_hireFleetSupplierNetwork());
    }

    if (_abstractTask.createReason().id() === -1) {
        _abstractTask.createReason(createReasonFactory.carHire());
        _abstractTask.taskType().code('ADM');
        _abstractTask.taskType().name('Administration');
    }

    _.extend(model, _abstractTask, {
        /**
         * setter/getter for rental
         */
        rental: (...args) => {
            return args.length ? (_rental = args[0]) : _rental;
        },

        /**
         * Util function for working out estimated hire completion
         */
        hireCompletion: () => {
            let date = new Date();

            date.setTime(model.schedule().create().getTime() + model.fault().repairMinutes() * 60 * 1000);

            return date;
        },

        extendHire: (minutes) => {
            model.fault().repairMinutes(model.fault().repairMinutes() + minutes);
        },

        checkoutReady: () => {
            return model.status() === 'GDET' || (model.status() === 'HEAD' && model.indicators().headLocked());
        },

        checkinReady: () => {
            return model.status() === 'ARVD' || model.status() === 'HIRE' || model.status() === 'GARR';
        },

        isEnterpriseHireEngaged: () => {
            return (model.status() === 'PLAN' || model.status() === 'GDET') && _rental.isThirdPartyHireSet() && _rental.thirdPartyHire().isEnterprise();
        },

        /**
         * hire fleet supplier network only returns HireSite.NETWORK_CODE if coopers insurance is involved .. for enterprise and all other hires types returns null
         * @returns HireSite.NETWORK_CODE supplier Network code
         */
        hireFleetSupplierNetwork: _hireFleetSupplierNetwork,

        /**
         * added this as a new property in task model which is same as srcRentalTaskId in Rental, required for swap car
         * @returns srcRentalTaskId
         */
        parentTaskId: (val) => {
            if (arguments.length > 0) {
                _abstractTask.parentTaskId(val);
                _rental.srcRentalTaskId(val);
            }
            return _rental.srcRentalTaskId();
        },

        /**
         * AbstractAncillaryTask serialiser
         * @return {Object} json representation of AbstractAncillaryTask
         */
        toJSON: function serialiseObject() {
            return _.extend({}, _abstractTask.toJSON(), {
                rental: _rental.toJSON()
            });
        }
    });
}

module.exports = MobilityTask;
