const { Vehicle } = require('@aa/data-models/common');
const Contact = require('@aa/malstrom-models/lib/contact.model'),
    MobilityTask = require('./mobility-task.model'),
    Recovery = require('@aa/malstrom-models/lib/recovery.model'),
    Schedule = require('@aa/malstrom-models/lib/task-schedule.model'),
    HireVehicle = require('@aa/mobility-models/lib/raf-vehicle.model'),
    Appointment = require('@aa/malstrom-models/lib/appointment.model');
/**
 * details of the recovered vehicle arriving at the retailer
 */

const _ = require('lodash');
/**
 * @class  constructor for class
 * @param  {Object} raw json representation
 * @param {string}  raw.id
 * @param {Contact} raw.contact  details of the person registered the breaddown
 * @param {string} raw.recoveryVehicleReg  vehicle recovered
 */
function IncommingRecoverySummary(raw) {
    var model = this,
        _id = null,
        _contact = null,
        _recovery = null,
        _vehicleSchedule = null,
        _hireAppointment = null,
        _passengerSchedule = null,
        _vehicle = new Vehicle(),
        _hireVehicle = null,
        _tasks = {
            breakdown: null,
            recovery: null,
            passenger: null,
            hire: null
        },
        _custReqId = -1,
        _isLocked = false,
        _hireTask = null,
        _status = '',
        _events = {}; // this is to hold multiple active hires they are keyed on `custId.taskId`

    if (raw) {
        /**
         * id
         */
        _id = raw.id ? raw.id : _id;

        /**
         * contact
         */
        _contact = raw.contact ? new Contact(raw.contact) : _contact;

        _recovery = raw.recovery ? new Recovery(raw.recovery) : _recovery;
        _vehicleSchedule = raw.vehicleSchedule ? new Schedule(raw.vehicleSchedule) : _vehicleSchedule;
        _passengerSchedule = raw.passengerSchedule ? new Schedule(raw.passengerSchedule) : _passengerSchedule;
        _vehicle = raw.vehicle ? new Vehicle(raw.vehicle) : _vehicle;
        _hireAppointment = raw.hireAppointment ? new Appointment(raw.hireAppointment) : _hireAppointment;
        _hireVehicle = raw.hireVehicle ? new HireVehicle(raw.hireVehicle) : _hireVehicle;
        _tasks = raw.tasks ? raw.tasks : _tasks;
        _isLocked = raw.isLocked ? raw.isLocked : _isLocked;
        _custReqId = raw.customerRequestId ? raw.customerRequestId : _custReqId;
        _status = raw.status ? raw.status : _status;
        _hireTask = raw.hireTask ? new MobilityTask(raw.hireTask) : null;

        if (raw.hasOwnProperty('events')) {
            Object.keys(raw.events).forEach((key) => {
                _events[key] = new IncommingRecoverySummary(raw.events[key]);
            });
        }
    }

    _.extend(model, {
        /**
         * setter/getter for id
         */
        id: (...args) => (args.length ? (_id = args[0]) : _id),

        /**
         * setter/getter for contact
         */
        contact: (...args) => (args.length ? (_contact = args[0]) : _contact),

        recovery: (...args) => (args.length ? (_recovery = args[0]) : _recovery),

        vehicleSchedule: (...args) => (args.length ? (_vehicleSchedule = args[0]) : _vehicleSchedule),
        passengerSchedule: (...args) => (args.length ? (_passengerSchedule = args[0]) : _passengerSchedule),
        isLocked: (...args) => (args.length ? (_isLocked = args[0]) : _isLocked),
        vehicle: (...args) => (args.length ? (_vehicle = args[0]) : _vehicle),
        hireVehicle: (...args) => (args.length ? (_hireVehicle = args[0]) : _hireVehicle),
        tasks: (...args) => (args.length ? (_tasks = args[0]) : _tasks),
        custReqId: (...args) => (args.length ? (_custReqId = args[0]) : _custReqId),
        status: (...args) => (args.length ? (_status = args[0]) : _status),
        hireTask: (...args) => (args.length ? (_hireTask = args[0]) : _hireTask),

        hireAppointment: (...args) => {
            return args.length ? (_hireAppointment = args[0]) : _hireAppointment;
        },

        /**
         * return set of events a backward implemnetation to support multiple events ..
         * @param  {...any} args
         * @returns
         */
        events: (...args) => {
            return args.length ? (_events = args[0]) : _events;
        },

        /**
         * IncommingRecoverySummary serialiser
         * @return {Object} json representation of IncommingRecoverySummary
         */
        toJSON: function serialiseObject() {
            const _nestedEvents = {};
            Object.keys(_events).forEach((key) => {
                _nestedEvents[key] = _events[key].toJSON();
            });

            return {
                id: _id,
                status: _status,
                customerRequestId: _custReqId,
                contact: _contact ? _contact.toJSON() : null,
                recovery: _recovery ? _recovery.toJSON() : null,
                vehicleSchedule: _vehicleSchedule ? _vehicleSchedule.toJSON() : null,
                passengerSchedule: _passengerSchedule ? _passengerSchedule.toJSON() : null,
                hireVehicle: _hireVehicle ? _hireVehicle.toJSON() : null,
                vehicle: _vehicle ? _vehicle.toJSON() : null,
                tasks: _tasks,
                hireTask: _hireTask ? _hireTask.toJSON() : null,
                hireAppointment: _hireAppointment ? _hireAppointment.toJSON() : null,
                isLocked: _isLocked,
                events: _nestedEvents
            };
        }
    });
}

module.exports = IncommingRecoverySummary;
