import { Customer<PERSON>roup<PERSON><PERSON>, PaginationQuery, PaginationQueryResult } from '@aa/data-models/common';
import { CustomerRequest } from '@aa/data-models/aux/customer-request';
import { EventCode, Exception } from '@aa/exception';
import { HttpMethod } from '@aa/http-client';
import { applicationBasePaths } from '../application-base-paths';
import { BackendApplication } from '@aa/identifiers';
import { EntityConnector } from '../entity-connector';
import { Entitlement, EntitlementData, LegacyEntitlementSearchQuery, LegacyEntitlementSearchResult } from '@aa/data-models/aux/entitlement';

export const entitlementUrlPaths = {
    query: `/search`,
    find: {
        byCr: '/search/cr'
    }
};

export const entitlementUrlMethods = {
    query: HttpMethod.POST,
    find: {
        byCr: HttpMethod.POST
    }
};

export const entitlementAPIUrlPaths = {
    query: `${applicationBasePaths[BackendApplication.ENTITLEMENT]}${entitlementUrlPaths.query}`,
    find: {
        byCr: `${applicationBasePaths[BackendApplication.ENTITLEMENT]}${entitlementUrlPaths.find.byCr}`
    }
};

export class EntitlementConnector extends EntityConnector {
    protected name = 'Entitlement Connector';
    public find = {
        byVRN: async (vrn: string): Promise<PaginationQueryResult<EntitlementData>> => {
            return this.find.byQuery({ vehicleRegistrationNumber: vrn });
        },
        byBCASP: async (customerGroupCode: CustomerGroupCode, bcaspCode: string): Promise<PaginationQueryResult<EntitlementData>> => {
            return this.find.byQuery({
                customerGroupCode,
                cardNumber: bcaspCode
            });
        },
        byCr: async (cr: CustomerRequest, includeExpired = false): Promise<EntitlementData[]> => {
            const response = await this.httpClient.fetch<{
                unListedSlvEntitlement?: EntitlementData;
                entitlements?: EntitlementData[];
            }>({
                url: `${this.onSiteDomain}${entitlementAPIUrlPaths.find.byCr}`,
                method: entitlementUrlMethods.find.byCr,
                body: { cr }
            });

            const result = response.body;
            if (!response.ok || !result) {
                throw new Exception({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_FAIL,
                    data: { body: { cr }, responseBody: result, response },
                    message: `Failed searching for entitlement by CR`
                });
            }

            let entitlements: EntitlementData[] = result.entitlements || [];
            // there is a chance that we can get null as entitlement
            entitlements = entitlements.filter((entry) => !!entry);

            if (!includeExpired) {
                entitlements = entitlements.filter((entitlement) => {
                    if (!entitlement.policy) {
                        return false;
                    }
                    return new Date(entitlement.policy.endDate as Date) > new Date();
                });
            }

            // aggregate all entitlements
            return [...entitlements];
        },
        byQuery: async (query: EntitlementSearchQuery, includeExpired = false): Promise<PaginationQueryResult<EntitlementData>> => {
            const { limit = 20, skip = 0, entitlementNumber, ...legacyCompatible } = query;
            const body: LegacyEntitlementSearchQuery = {
                membershipSearch: {
                    search: entitlementNumber,
                    param: {
                        ...legacyCompatible,
                        page: skip,
                        pageSize: limit
                    }
                }
            };
            const response = await this.httpClient.fetch<LegacyEntitlementSearchResult>({
                url: `${this.onSiteDomain}${entitlementAPIUrlPaths.query}`,
                method: entitlementUrlMethods.query,
                body
            });

            const result = response.body;
            if (!response.ok || !result) {
                throw new Exception({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_FAIL,
                    data: { body, responseBody: result, response },
                    message: `Failed searching for entitlement`
                });
            }

            let entitlements: EntitlementData[] = result.results.entitlements || [];
            let unlistedSlvEntitlements: EntitlementData[] = [];
            let companyEntitlements: EntitlementData[] = [];

            if (result.results.companies) {
                for (const company of result.results.companies) {
                    const { unlistedSlvEntitlement, entitlements } = company;

                    if (unlistedSlvEntitlement) {
                        unlistedSlvEntitlements.push(unlistedSlvEntitlement);
                    }

                    if (entitlements) {
                        companyEntitlements.push(...company.entitlements);
                    }
                }
            }

            if (!includeExpired) {
                entitlements = entitlements.filter((entitlement) => {
                    const policy = entitlement.policy;
                    if (!policy) {
                        return false;
                    }
                    return new Date(policy.endDate as Date) > new Date();
                });
                unlistedSlvEntitlements = unlistedSlvEntitlements.filter((entitlement) => {
                    const policy = entitlement.policy;
                    if (!policy) {
                        return false;
                    }
                    return new Date(policy.endDate as Date) > new Date();
                });
                companyEntitlements = companyEntitlements.filter((entitlement) => {
                    const policy = entitlement.policy;
                    if (!policy) {
                        return false;
                    }
                    return new Date(policy.endDate as Date) > new Date();
                });
            }

            // aggregate all entitlements
            return {
                results: [...entitlements, ...companyEntitlements, ...unlistedSlvEntitlements],
                more: !!result.results.nextPage
            };
        },
        byTaskId: async (taskId: number): Promise<EntitlementData | undefined> => {
            const task = await this.connector.task.find.byId(taskId);
            if (!task || (task && !task.customerRequestId)) {
                return;
            }

            const crs = await this.connector.customerRequest.find.byId(taskId);
            if (!crs.length) {
                return;
            }

            const taskCR = crs.find((cr) => {
                return cr.tasks.find((entry) => {
                    return entry.id === taskId;
                });
            });

            if (!taskCR) {
                return;
            }

            const entitlements = await this.connector.entitlement.find.byCr(taskCR, false);

            // return entitlements.find((entry) => {
            //     const policy = entry.policy();
            //     if(!policy) {
            //         return false;
            //     }
            //     return  policy.customerGroup().code() === task.entitlement?.customerGroup.code;
            // });
            return entitlements.find((entry) => {
                return entry.policy?.customerGroup.code === task.entitlement?.customerGroup.code;
            });
        }
    };
}

export interface EntitlementSearchQuery extends PaginationQuery {
    entitlementNumber?: string;
    inPostcode?: string;
    outPostcode?: string;
    membershipNumber?: string;
    vehicleRegistrationNumber?: string;
    vehicleIdentificationNumber?: string;
    policyId?: string;
    surname?: string;
    initials?: string;
    town?: string;
    companyName?: string;
    cardNumber?: string;
    customerGroupCode?: string;
}
