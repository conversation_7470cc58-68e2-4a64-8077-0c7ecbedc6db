import { BackendApplication } from '@aa/identifiers';

export const applicationBasePaths: { [key in BackendApplication]: string } = {
    OUTBOUND_EMAIL_STREAM: '/api/outbound-email-stream',
    EUROHELP_API: '/api/eurohelp-api-service',
    // On-site servers
    ENTITLEMENT: '/api/service-entitlement',
    TASK: '/api/task-service',
    VEHICLE_DETAILS: '/api/vehicle-details-service',
    AUTH: '/api/unified-auth-service',
    CONFIG: '/api/config-service',
    // Azure servers
    QUEUE_STATUS: '/api/queue-status-service',
    AUDIT: '/api/audit-service',
    AUDIT_WRITER: '/api/audit-writer-service',
    ECALL_TASK: '/api/ecall-task-service',
    ECALL_TASK_WRITER: '/api/ecall-task-writer-service',
    IVR: '/api/ivr-service',
    USER: '/api/user-service',
    UAC: '/api/uac-service',
    GATEKEEPER: '/api/gatekeeper-service',
    DEMAND_DEFLECT: '/api/demand-deflect-service',
    OUTDOOR_INGEST: '/api/outdoor-ingest-service',
    POI_FACADE: '/api/poi-facade-service',
    UNITY_PROCESSOR: '/api/unity-processor-service',
    SMR_PROCESSOR: '/api/smr-processor-service',
    POI: '/api/poi-service',
    BATTERY_TEST: '/api/battery-test-service',
    BATTERY_TEST_MONITOR: '/api/battery-test-monitor-service',
    CATHIE_PROCESSOR: '/api/cathie-processor-service',
    CUV_SERVICE_PROCESSOR: '/api/cuv-processor-service',
    CASE_SERVICE_PROCESSOR: '/api/case-processor-service',
    BCAS_PROCESSOR: '/api/bcas-processor-service',
    EUOPS_OUTBOUND_EMAIL_STREAM: '/api/euops-outbound-email-stream-service',
    EUOPS_INBOUND_EMAIL_STREAM: '/api/euops-inbound-email-stream-service',
    EDOCS: '/api/edocs-service',
    AUDIT_STREAM: '/api/audit-stream-service',
    ATTACHMENT_RENDERER_STREAM: '/api/attachement-renderer-stream-service',
    INBOUND_EMAIL_STREAM: '/api/inbound-email-stream-service',
    NOTE_API: '/api/note-api-service',
    TRIP_API: '/api/trip-api-service',
    BILLING_API: '/api/billing-api',
    ACTION_API: '/api/action-api-service',
    MOBILITY_TASK: '/api/mobility-task-service',
    ACTION_STREAM: '/api/action-stream-service',
    TEAM_API: '/api/team-api-service',
    EUOPS_SPARX_STREAM: 'api/euops-sparx-stream-service',
    EUOPS_EVENT_INGEST_API: '/api/euops-event-ingest-api-service',
    PRODUCT_BENEFIT_API: '/api/product-benefit-api-service',
    EUOPS_TASK_STREAM: '/api/euops-task-stream-service',
    TASK_STREAM: '/api/task-stream',
    WORKER_API: '/api/worker-api',
    CLOSED_HIRES_API: '/api/closed-hires',
    ADMIN_APP_API: 'api/admin-api',
    PLAYGROUND: '/api/playground-api',
    NOTIFICATION_API: '/api/notification-api',
    NOTIFICATION_STREAM: '/api/notification-stream',
    TRANSACTION_HISTORY: '/api/transaction-history-service',
    MESSAGE_API: '/api/message-api'
};
