import { AuthMiddleware } from '@aa/auth-middleware';
import { Context } from '@aa/context';
import { EventCode, Exception, SnapshotLike } from '@aa/exception';
import { HttpMethod, ServerResponseCode } from '@aa/http-client';
import { Instrumentation } from '@aa/instrumentation';
import { Logger } from '@aa/logger';
import { OpenApi } from '@aa/open-api';
import { Schema } from '@aa/schema';
import { SecurityUtils } from '@aa/security-utils';
import { getResponse, getValidationResponse, normalisePath, RouteDefinition, RouteOptions, ServerRouteHandler } from '@aa/server-utils';
import { System, SystemEvent } from '@aa/system';
import * as BodyParser from 'body-parser';
import cors, { CorsOptions } from 'cors';
import ExpressApp, { Application, NextFunction, Request, RequestHandler, Response } from 'express';
import helmet from 'helmet';
import { Server as HttpServer } from 'http';
import HttpsServer from 'spdy';
import multer from 'multer';
const upload = multer({ storage: multer.memoryStorage() });

/**
 * TODO:
 *  HTTP2 on express? or Fastify?
 *  https://www.fastify.io/docs/latest/HTTP2/
 *  https://medium.com/@azatmardan/http-2-with-node-js-c928b90423d0
 *  remove the basePath and trust whole url, urls will be provided by urlPathFactory
 */
export class Server {
    protected name = 'Server';
    private logger: Logger;
    private system: System;
    private context: Context;
    private instrumentation?: Instrumentation;
    private openApi?: OpenApi;
    private authMiddleware: AuthMiddleware;
    private host: string;
    private secure: boolean;
    private ssl?: { cert: string; key: string };
    private port: number;
    private isAuthServer: boolean;
    private basePath?: string;
    // by default no cors restrictions
    private cors: string[] = [];
    private express: Application;
    private server!: HttpServer;

    constructor(config: ServerConfig) {
        const { logger, system, authMiddleware, host, port, basePath, isAuthServer, cors = [], context, instrumentation, ssl, openApi } = config;
        this.logger = logger;
        this.system = system;
        this.context = context;
        this.authMiddleware = authMiddleware;
        this.instrumentation = instrumentation;
        this.system = system;
        this.port = port;
        this.host = host;
        this.basePath = (basePath && normalisePath(basePath)) || undefined;
        this.isAuthServer = !!isAuthServer;
        // if no cors provided we allow all
        this.cors = cors && cors.length ? cors : this.cors;
        if (ssl) {
            this.ssl = {
                key: SecurityUtils.formatRSAKey(ssl?.key || ''),
                cert: SecurityUtils.formatRSAKey(ssl?.cert || '')
            };
        }
        this.secure = !!ssl;

        if (openApi) {
            this.openApi = new OpenApi({});
        }
        this.express = ExpressApp();
        this.setupSecurity();
        this.setupAux();
        this.setupAuth();

        this.setupListeners();
    }

    private setupApiDocsRoutes() {
        if (!this.openApi) {
            return;
        }
        // if open api enabled lets use all collected definitions current route
        // let the openApi process api docs for Open Api
        this.express.use(this.getRoutePath('/api-docs'), ...this.openApi.docsHandlers());
    }

    private registerApiDocsRoute(definition: RouteDefinition) {
        if (!this.openApi) {
            return;
        }
        // if open api enabled lets use all collected definitions current route
        this.openApi.registerRoute({
            ...definition,
            path: this.getRoutePath(definition)
        });
    }

    private serverListener = () => {
        this.logger.log('################################################################################');
        this.logger.log(`Server started at http${this.secure ? 's' : ''}://${this.host}:${this.port}${this.getRoutePath('/')}`);
        this.logger.log(`SSL for server ${this.secure ? 'enabled' : 'disabled'}`);
        this.logger.log('################################################################################');
    };

    /**
     * Start server
     */
    public start() {
        if (this.server) {
            return;
        }

        // At this point we have all routes defined so we can
        // setup Api Docs routes
        this.setupApiDocsRoutes();

        // if we have ssl config we can use http2 spdy module
        if (this.secure) {
            this.server = HttpsServer.createServer(
                {
                    key: this.ssl?.key,
                    cert: this.ssl?.cert
                },
                this.express
            );
            this.server.listen(this.port, this.host, this.serverListener);
        } else {
            this.server = this.express.listen(this.port, this.host, this.serverListener);
        }
    }

    public get(path: string, handler: RequestHandler<any, any, any, any, any>, options?: RouteOptions) {
        this.route({ path, handler, method: HttpMethod.GET, ...options });
    }

    public post(path: string, handler: RequestHandler<any, any, any, any, any>, options?: RouteOptions) {
        this.route({ path, handler, method: HttpMethod.POST, ...options });
    }

    public put(path: string, handler: RequestHandler<any, any, any, any, any>, options?: RouteOptions) {
        this.route({ path, handler, method: HttpMethod.PUT, ...options });
    }

    public delete(path: string, handler: RequestHandler<any, any, any, any, any>, options?: RouteOptions) {
        this.route({ path, handler, method: HttpMethod.DELETE, ...options });
    }

    /**
     * Stop server
     */
    public stop() {
        if (this.server) {
            this.server.close();
        }
    }

    /**
     * Setup supporting modules
     * @private
     */
    private setupAux() {
        // enable context tracking
        this.express.use(this.context.provide);

        // parse body
        this.express.use(BodyParser.json());

        // notify about health of the system
        this.get('/health', this.healthCheckHandler, { protect: false });

        // if instrumentation enabled
        if (this.instrumentation) {
            // let the instrumentation process each req
            this.express.use(this.reqTraceHandler);
        }
    }

    private reqTraceHandler: ServerRouteHandler = (req, res, next) => {
        this.instrumentation && this.instrumentation.traceRequest(req);
        next();
    };

    private healthCheckHandler: ServerRouteHandler = (req, res) => {
        if (this.system.healthy) {
            return getResponse(res, ServerResponseCode.OK);
        } else {
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    private getSchemaCheckHandler(schema: Schema): ServerRouteHandler {
        return async (req: Request, res, next) => {
            const { body, method, path } = req;
            try {
                const result = Schema.validate(schema, body);

                if (!result.success) {
                    const snapshot: SnapshotLike = {
                        sourceName: this.name,
                        code: EventCode.MOD_EXEC_WARN,
                        message: 'Inbound payload validation failed',
                        data: { body, method, path, result }
                    };
                    this.logger.warn(snapshot);
                    getValidationResponse(res, result);
                    return;
                }

                req.body = result.data;

                // if valid let it through
                next();
            } catch (error) {
                const message = 'Failure in validating payload schema';
                const snapshot: SnapshotLike = {
                    sourceName: this.name,
                    error: new Exception({ error, message }),
                    code: EventCode.MOD_EXEC_FAIL,
                    message,
                    data: { body, method, path }
                };
                this.logger.warn(snapshot);
                return getResponse(res, ServerResponseCode.SERVER_ERROR);
            }
        };
    }

    private setupSecurity() {
        // set helmet to protect against various vulnerabilities
        this.express.use(helmet());

        // TODO: security
        //  - add rate limiting
        //    https://github.com/animir/node-rate-limiter-flexible

        // set cors for allowed origins
        const corsOptions: CorsOptions = {
            origin: this.cors.length ? this.cors : '*',
            methods: Object.values(HttpMethod)
        };
        this.express.use(cors(corsOptions));
    }

    private setupAuth() {
        // setup paths for auth server
        if (this.isAuthServer) {
            this.get('/authenticate', this.authMiddleware.authenticate, {
                protect: false
            });
            this.get('/persona', this.authMiddleware.getPersona);
        }
    }

    /**
     * Handle errors from req handlers
     * @param {ServerRouteHandler} handler
     * @returns {ServerRouteHandler}
     * @private
     */
    private withErrorHandling(handler: ServerRouteHandler): ServerRouteHandler {
        return (req: Request, res: Response, next: NextFunction) => {
            try {
                handler(req, res, next);
            } catch (error) {
                const exception = new Exception({
                    error,
                    message: 'Req failure'
                });
                this.logger.error(exception);
                return getResponse(res, ServerResponseCode.SERVER_ERROR);
            }
        };
    }

    private route(definition: RouteDefinition) {
        // all routes as protected by default
        const { schema, protect = true, allowedRoles, useMulter = false } = definition;
        const handlers: RequestHandler[] = [];

        if (protect) {
            handlers.push(this.authMiddleware.validate);
            // if no custom rules
            if (!allowedRoles || (allowedRoles && !allowedRoles.length)) {
                handlers.push(this.authMiddleware.authorise);
            } else {
                handlers.push(this.authMiddleware.authoriseFor(allowedRoles));
            }
        }

        // if schema for route provided
        if (schema) {
            handlers.push(this.getSchemaCheckHandler(schema));
        }

        // if useMulter for file upload
        if (useMulter) {
            handlers.push(upload.single('file'));
        }

        // at the end add route handler
        handlers.push(this.withErrorHandling(definition.handler));

        switch (definition.method) {
            case HttpMethod.POST:
                this.express.post(this.getRoutePath(definition), ...handlers);
                break;
            case HttpMethod.PUT:
                this.express.put(this.getRoutePath(definition), ...handlers);
                break;
            case HttpMethod.DELETE:
                this.express.delete(this.getRoutePath(definition), ...handlers);
                break;
            case HttpMethod.GET:
            default:
                // by default get
                this.express.get(this.getRoutePath(definition), ...handlers);
        }

        // Register api docs route
        this.registerApiDocsRoute(definition);
    }

    /**
     * Get final route path from definition
     * @param definitionOrPath
     * @protected
     */
    private getRoutePath(definitionOrPath: RouteDefinition | string): string {
        const path = typeof definitionOrPath === 'string' ? definitionOrPath : definitionOrPath.path;
        if (this.basePath && path) {
            return `/${this.basePath}/${normalisePath(path)}`;
        }

        return `/${normalisePath(path)}`;
    }

    /**
     * Setup listeners for system events
     * @private
     */
    private setupListeners() {
        // close server on system events
        this.system.once(SystemEvent.ERROR, this.stop.bind(this));
        this.system.once(SystemEvent.EXIT, this.stop.bind(this));
    }
}

export interface ServerConfig extends ServerDeps {
    host: string;
    port: number;
    basePath?: string;
    // which origin we allow request from
    cors?: string[];
    // is open api enabled - defaults to false
    openApi?: boolean;
    // is this server an auth server?
    // If so server will expose 2 special endpoints:
    //  - /authenticate
    //  - /persona
    isAuthServer?: boolean;
    // should we serve via https
    ssl?: {
        key: string;
        cert: string;
    };
}

export interface ServerDeps {
    logger: Logger;
    system: System;
    context: Context;
    instrumentation?: Instrumentation;
    authMiddleware: AuthMiddleware;
}
