{"name": "entitlement-benefit-client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/entitlement-benefit-client/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/entitlement-benefit-client/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/entitlement-benefit-client/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "tags": []}