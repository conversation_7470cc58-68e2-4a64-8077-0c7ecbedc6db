import { HttpClient, HttpMethod } from '@aa/http-client';
import { applicationBasePaths, Connector } from '@aa/connector';
import { BenefitLimit, EntitlementBenefits } from '@aa/data-models/common';
import { EventCode, Exception } from '@aa/exception';
import { BackendApplication } from '@aa/identifiers';

export class EntitlementBenefitClient {
    protected readonly name = 'Entitlement Benefit Client Library';
    protected httpClient: HttpClient;
    protected connector: Connector;
    protected onSiteDomain: string;

    constructor({ httpClient, connector }: { httpClient: HttpClient; connector: Connector }) {
        this.httpClient = httpClient;
        this.connector = connector;
        this.onSiteDomain = connector.config.onSiteDomain;
    }

    public queryEntitlementsBenefits = async (customerGroup?: string, code?: string, benefits?: Array<string>) => {
        try {
            let url = `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.ENTITLEMENTS}?limit=100`;
            if (customerGroup) {
                url += `&customerGroup=${customerGroup}`;
            }
            if (code) {
                url += `&code=${code}`;
            }
            if (benefits) {
                url += `&benefits=${benefits}`;
            }
            const result = await this.httpClient.fetch({
                url: url,
                method: HttpMethod.GET
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Entitlement Benefit Client Library: Failed getting all product benefits'
                });
            }

            return result.body as EntitlementBenefits[];
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Entitlement Benefit Client Library: Failed getting all product benefits'
            });
        }
    };

    public getAllEntitlementsBenefits = async (): Promise<EntitlementBenefits[] | void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.ENTITLEMENTS}`,
                method: HttpMethod.GET
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Entitlement Benefit Client Library: Failed getting all product benefits'
                });
            }

            return result.body as EntitlementBenefits[];
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Entitlement Benefit Client Library: Failed getting all product benefits'
            });
        }
    };

    public getEntitlementBenefitByCode = async (code: string): Promise<EntitlementBenefits | void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.ENTITLEMENT}/${code}`,
                method: HttpMethod.GET
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: `Entitlement Benefit Client Library: Failed getting product benefit with id: ${code}`
                });
            }

            return result.body as EntitlementBenefits;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: `Entitlement Benefit Client Library: Failed getting product benefit with id: ${code}`
            });
        }
    };
    public getEntitlementBenefitByContract = async (contractKey: string, customerGroup: string): Promise<EntitlementBenefits | void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.ENTITLEMENT}/${contractKey}/${customerGroup}`,
                method: HttpMethod.GET
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: `Entitlement Benefit Client Library: !result.ok Failed getting product benefit with contract Key: ${contractKey}`
                });
            }

            return result.body as EntitlementBenefits;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: `Entitlement Benefit Client Library: Failed getting product benefit with contract key: ${contractKey}`
            });
        }
    };

    public insertEntitlementBenefit = async (entitlementBenefit: Omit<EntitlementBenefits, 'created' | 'updated'>): Promise<void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.ENTITLEMENT}`,
                method: HttpMethod.POST,
                body: entitlementBenefit
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Entitlement Benefit Client Library: Failed inserting product benefit'
                });
            }
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Entitlement Benefit Client Library: Failed inserting product benefit'
            });
        }
    };

    public updateEntitlementBenefit = async (entitlementBenefit: EntitlementBenefits): Promise<void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.ENTITLEMENT}/${entitlementBenefit.code}`,
                method: HttpMethod.PUT,
                body: entitlementBenefit
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Entitlement Benefit Client Library: Failed updating product benefit'
                });
            }
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Entitlement Benefit Client Library: Failed updating product benefit'
            });
        }
    };

    public deleteEntitlementBenefit = async (code: string): Promise<void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.ENTITLEMENT}/${code}`,
                method: HttpMethod.DELETE
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: `Entitlement Benefit Client Library: Failed deleting product benefit with id: ${code}`
                });
            }
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: `Entitlement Benefit Client Library: Failed deleting product benefit with id: ${code}`
            });
        }
    };

    public associateBenefitsWithEntitlement = async (code: string, codes: Array<string>): Promise<void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.ENTITLEMENT}/${code}/associate`,
                method: HttpMethod.POST,
                body: codes
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: `Entitlement Benefit Client Library: Failed associating benefits with code: ${code}`
                });
            }
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: `Entitlement Benefit Client Library: Failed associating benefits with code: ${code}`
            });
        }
    };

    public queryBenefitLimits = async (type?: string, name?: string, amount?: number, currency?: string) => {
        try {
            let url = `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.BENEFITS}?limit=100`;
            if (type) {
                url += `&type=${type}`;
            }
            if (name) {
                url += `&name=${name}`;
            }
            if (amount) {
                url += `&amount=${amount}`;
            }
            if (currency) {
                url += `&currency=${currency}`;
            }

            const result = await this.httpClient.fetch({
                url: url,
                method: HttpMethod.GET
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Entitlement Benefit Client Library: Failed getting all benefits limits'
                });
            }

            return result.body as BenefitLimit[];
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Entitlement Benefit Client Library: Failed getting all benefits limits'
            });
        }
    };

    public getAllBenefitsLimits = async (): Promise<BenefitLimit[] | void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.BENEFITS}`,
                method: HttpMethod.GET
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Entitlement Benefit Client Library: Failed getting all benefits limits'
                });
            }

            return result.body as BenefitLimit[];
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Entitlement Benefit Client Library: Failed getting all benefits limits'
            });
        }
    };

    public getBenefitLimitByCode = async (code: string): Promise<BenefitLimit | void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.BENEFIT}/${code}`,
                method: HttpMethod.GET
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: `Entitlement Benefit Client Library: Failed getting benefit limit with id: ${code}`
                });
            }

            return result.body as BenefitLimit;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: `Entitlement Benefit Client Library: Failed getting benefit limit with id: ${code}`
            });
        }
    };

    public insertBenefitLimit = async (benefitLimit: Omit<BenefitLimit, 'created' | 'updated'>): Promise<void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.BENEFIT}`,
                method: HttpMethod.POST,
                body: benefitLimit
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Entitlement Benefit Client Library: Failed inserting benefit limit'
                });
            }
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Entitlement Benefit Client Library: Failed inserting benefit limit'
            });
        }
    };

    public updateBenefitLimit = async (benefitLimit: Omit<BenefitLimit, 'created' | 'updated'>): Promise<void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.BENEFIT}/${benefitLimit.code}`,
                method: HttpMethod.PUT,
                body: benefitLimit
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Entitlement Benefit Client Library: Failed updating benefit limit'
                });
            }
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Entitlement Benefit Client Library: Failed updating benefit limit'
            });
        }
    };

    public deleteBenefitLimit = async (code: string): Promise<void> => {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT_BENEFIT_API]}${EntitlementBenefitsEndpoints.BENEFIT}/${code}`,
                method: HttpMethod.DELETE
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    error: result.error,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: `Entitlement Benefit Client Library: Failed deleting benefit limit with id: ${code}`
                });
            }
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: `Entitlement Benefit Client Library: Failed deleting benefit limit with id: ${code}`
            });
        }
    };
}

enum EntitlementBenefitsEndpoints {
    ENTITLEMENTS = '/entitlement-benefits',
    ENTITLEMENT = '/entitlement-benefit',
    BENEFITS = '/benefits',
    BENEFIT = '/benefit-limits'
}
