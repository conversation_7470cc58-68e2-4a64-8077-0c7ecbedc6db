// let httpClient: HttpClient;
// let connector: Connector;
//
// describe('note client', () => {
//
//         beforeEach(() => {
//             httpClient = new HttpClient();
//             // httpClient.fetch = jest.fn().mockResolvedValue({ status: "foo" });
//             connector = new Connector({
//                 httpClient,
//                 onSiteDomain: "http://0.0.0.0:7831",
//             });
//         });
//
//         it('Should be created without errors', async () => {
//             const client = new EntitlementBenefitClient({ httpClient, connector });
//             let dataFromClient;
//             await client.getAllEntitlementsBenefits().then((data) => {
//                 dataFromClient = data;
//             });
//             console.log('data: ', dataFromClient);
//         });
// });

describe('entitlement-benefit-client', () => {
    it('should just work', () => {
        expect(true).toBeTruthy();
    });
});
