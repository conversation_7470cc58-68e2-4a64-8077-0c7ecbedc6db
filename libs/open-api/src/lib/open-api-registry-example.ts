import { Schema } from '@aa/schema';
import { OpenApiGeneratorV3, OpenAPIRegistry } from '@asteasolutions/zod-to-openapi';
import { RouteParameter } from '@asteasolutions/zod-to-openapi/dist/openapi-registry';

import { z } from 'zod';

// TODO: implement registry inside OpenApi class
//  https://github.com/asteasolutions/zod-to-openapi?tab=readme-ov-file#defining-custom-components

const registry = new OpenAPIRegistry();

const UserIdSchema = registry.registerParameter(
    'UserId',
    Schema.string().openapi({
        param: {
            name: 'id',
            in: 'path'
        },
        example: '1212121'
    })
);
const UserSchema = z
    .object({
        id: Schema.string().openapi({
            example: '1212121'
        }),
        name: Schema.string().openapi({
            example: '<PERSON>'
        }),
        age: Schema.number()
            .openapi({
                example: 42
            })
            .openapi({ description: 'Donkey lover' })
    })
    .openapi('User');

const bearerAuth = registry.registerComponent('securitySchemes', 'bearerAuth', {
    type: 'http',
    scheme: 'bearer',
    bearerFormat: 'JWT'
});

registry.registerPath({
    method: 'get',
    path: '/users/{id}',
    description: 'Get user data by its id',
    summary: 'Get a single user',
    security: [{ [bearerAuth.name]: [] }],
    request: {
        params: Schema.object({
            id: UserIdSchema
        }) as unknown as RouteParameter
    },
    responses: {
        200: {
            description: 'Object with user data.',
            content: {
                'application/json': {
                    schema: UserSchema
                }
            }
        },
        204: {
            description: 'No content - successful operation'
        }
    }
});

function getOpenApiDocumentation() {
    const generator = new OpenApiGeneratorV3(registry.definitions);

    return generator.generateDocument({
        openapi: '3.0.0',
        info: {
            version: '1.0.0',
            title: 'My API',
            description: 'This is the API'
        },
        servers: [{ url: 'https://0.0.0.0:9999/api/playground-api/' }]
    });
}

export const documentation = getOpenApiDocumentation();
