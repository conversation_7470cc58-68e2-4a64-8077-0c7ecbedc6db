const logger = require('winston');
const oracleDb = require('oracledb');

const SCHEMA_NAME_REGEX = /\/(.+)/;

class ConnectionPool {
    constructor(client) {
        this._pool = null; // we probably don't need this ... as we can the the pool
        this._user = client.user;
        this._password = client.password;
        this._connectString = client.connectString;
        this._enableStats = client.stats || false;
        this._poolMax = client.poolMax || 20;
        this._active = false; // we probably don't need this either ..
        this._poolAlias = client.poolAlias || SCHEMA_NAME_REGEX.exec(client.connectString)[1];
        this._appName = client.appName ?? 'unknown';
        logger.info(`connection-pool-service :: creating new instance for ${this._connectString} alias ${this._poolAlias}`);
    }

    isActive() {
        return this._active;
    }

    async createPool() {
        if (this._active) {
            return Promise.resolve();
        }

        logger.info(`connection-pool-service._createPool :: instanciating pool instance for ${this._poolAlias}`);
        try {
            this._pool = await oracleDb.createPool({
                user: this._user,
                password: this._password,
                connectString: this._connectString,
                _enableStats: true,
                poolMax: this._poolMax,
                poolAlias: this._poolAlias,
                appName: this._appName
            });
            this._active = true;
            return true;
        } catch (err) {
            logger.error(`connection-pool-service :: failed to create pool instance for ${this._poolAlias} ${err}`);
            this._active = false;
            return Promise.reject(err);
        }
    }

    async connect() {
        let conn = null;
        const pool = oracleDb.getPool(this._poolAlias);

        if (pool === null || pool.status !== oracleDb.POOL_STATUS_OPEN) {
            logger.error(`connection-pool-service.connect :: pool ${this._poolAlias} is closed`);
            return Promise.reject();
        }

        try {
            conn = await pool.getConnection();
            logger.info(`connection-pool-serivce : connected ${this.poolStats()}`);
            return Promise.resolve(conn);
        } catch (err) {
            logger.error(`connection-pool-serivce : failed to connection to ${this._poolAlias} ${err.message}`);
            await this._pool.close(2);
            this._pool = null;
            this._active = false;
            return Promise.reject(err);
        }
    }

    poolStats(client) {
        return this._pool
            ? `poolAlias : ${this._poolAlias}, open : ${this._pool.connectionsOpen}, inUse : ${this._pool.connectionsInUse}, poolMax : ${this._pool.poolMax}`
            : ` not connected - connectionString : ${this._poolAlias}`;
    }

    logStats() {
        let averageTimeInQueue;

        averageTimeInQueue = 0;

        if (this._pool._totalRequestsEnqueued !== 0) {
            averageTimeInQueue = Math.round(this._pool._totalTimeInQueue / this._pool._totalRequestsEnqueued);
        }
        return {
            totalUpTime: Date.now() - this._pool._createdDate,
            totalConnectionRequests: this._pool._totalConnectionRequests,
            totalRequestsEnqueued: this._pool._totalRequestsEnqueued,
            totalRequestsDequeued: this._pool._totalRequestsDequeued,
            totalFailedRequests: this._pool._totalFailedRequests,
            totalRequestsRejected: this._pool._totalRequestsRejected,
            totalRequestTimeouts: this._pool._totalRequestTimeouts,
            maxQueueLength: this._pool._maxQueueLength,
            totalTimeInQueue: this._pool._totalTimeInQueue,
            minTimeInQueue: this._pool._minTimeInQueue,
            maxTimeInQueue: this._pool._maxTimeInQueue,
            averageTimeInQueue: averageTimeInQueue,
            connectionsInUse: this._pool.connectionsInUse,
            connectionsOpen: this._pool.connectionsOpen,
            poolAlias: this._pool.poolAlias,
            queueMax: this._pool.queueMax,
            queueTimeout: this._pool.queueTimeout,
            poolMin: this._pool.poolMin,
            poolMax: this._pool.poolMax,
            poolIncrement: this._pool.poolIncrement,
            poolTimeout: this._pool.poolTimeout,
            poolPingInterval: this._pool.poolPingInterval,
            sessionCallback:
                typeof this._pool.sessionCallback === 'function'
                    ? this._pool.sessionCallback.name
                    : typeof this._pool.sessionCallback === 'string'
                    ? '"' + this._pool.sessionCallback + '"'
                    : this._pool.sessionCallback,
            stmtCacheSize: this._pool.stmtCacheSize,
            status: this._pool.status,
            UV_THREADPOOL_SIZE: process.env.UV_THREADPOOL_SIZE
        };
    }

    async release(conn) {
        return new Promise((resolve, reject) => {
            if (conn) {
                conn.close(() => {
                    logger.info(`connection-pool-service.release : connection ${this.poolStats()}`);
                    resolve();
                });
            } else {
                logger.info(`connection-pool-service.release : db con is null ${this.poolStats()}`);
                resolve();
            }
        });
    }

    close() {
        this._active = false;
        const pool = oracleDb.getPool(this._poolAlias);
        logger.info(`connection-pool-service.close : closing pool for ${this._poolAlias}`);
        if (pool !== null) {
            return pool.close(2);
        }

        return Promise.resolve();
    }

    static async getInstance(params) {
        const pool = new ConnectionPool(params);
        await pool.createPool();
        logger.info(`connection-pool-service.getInstance : pool created ${pool.poolStats()}`);
        return pool;
    }
}
module.exports = ConnectionPool;
