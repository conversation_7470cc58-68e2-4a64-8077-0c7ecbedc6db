'use strict';

const { defer } = require('q');

var _ = require('lodash'),
    async = require('async'),
    logger = require('winston'),
    oracleDb = require('oracledb'),
    Q = require('q'),
    hostName = require('os').hostname(),
    _clients = [];

// configure oracle connection pool
oracleDb.poolPingInterval = 5; // check if connection is active after been 5 seconds
oracleDb.fetchAsString = [oracleDb.CLOB];

function poolStats(client) {
    return client.pool
        ? `connectionString : ${client.connectString}, open : ${client.pool.connectionsOpen}, inUse : ${client.pool.connectionsInUse}, poolMax : ${client.pool.poolMax}`
        : ` not connected - connectionString : ${client.connectString}`;
}

function _poolStats(pool) {
    let averageTimeInQueue;

    averageTimeInQueue = 0;

    if (pool._totalRequestsEnqueued !== 0) {
        averageTimeInQueue = Math.round(pool._totalTimeInQueue / pool._totalRequestsEnqueued);
    }
    return {
        totalUpTime: Date.now() - pool._createdDate,
        totalConnectionRequests: pool._totalConnectionRequests,
        totalRequestsEnqueued: pool._totalRequestsEnqueued,
        totalRequestsDequeued: pool._totalRequestsDequeued,
        totalFailedRequests: pool._totalFailedRequests,
        totalRequestsRejected: pool._totalRequestsRejected,
        totalRequestTimeouts: pool._totalRequestTimeouts,
        maxQueueLength: pool._maxQueueLength,
        totalTimeInQueue: pool._totalTimeInQueue,
        minTimeInQueue: pool._minTimeInQueue,
        maxTimeInQueue: pool._maxTimeInQueue,
        averageTimeInQueue: averageTimeInQueue,
        connectionsInUse: pool.connectionsInUse,
        connectionsOpen: pool.connectionsOpen,
        poolAlias: pool.poolAlias,
        queueMax: pool.queueMax,
        queueTimeout: pool.queueTimeout,
        poolMin: pool.poolMin,
        poolMax: pool.poolMax,
        poolIncrement: pool.poolIncrement,
        poolTimeout: pool.poolTimeout,
        poolPingInterval: pool.poolPingInterval,
        sessionCallback: typeof pool.sessionCallback === 'function' ? pool.sessionCallback.name : typeof pool.sessionCallback === 'string' ? '"' + pool.sessionCallback + '"' : pool.sessionCallback,
        stmtCacheSize: pool.stmtCacheSize,
        status: pool.status,
        UV_THREADPOOL_SIZE: process.env.UV_THREADPOOL_SIZE
    };
}

function connectToStore() {
    var deferred = Q.defer(),
        _error = null,
        _connection = null;

    // try each available instances in series
    async.detectSeries(
        _clients,
        function tryClient(client, callback) {
            if (client.active) {
                logger.info(`Application : ${client.appName}` + ' OracleConnectionManager.connectToStore:: trying client :: ', client.connectString);
                if (client.pool === null) {
                    oracleDb.createPool(
                        {
                            user: client.user,
                            password: client.password,
                            connectString: client.connectString,
                            _enableStats: client.stats || false,
                            poolMax: client.poolMax || 20,
                            appName: client.appName
                        },
                        function (err, pool) {
                            if (err) {
                                logger.error('OracleConnectionManager.connectToStore :: failed to connect ::', err);
                                callback(null, false);
                            } else {
                                // success connecting ...
                                logger.info('OracleConnectionManager.connectToStore :: created new pool to ::', client.connectString);
                                // remember connection
                                client.pool = pool;
                                client.pool.getConnection(function (err, conn) {
                                    if (err) {
                                        logger.error(`OracleConnectionManager.connectToStore :: failed to get connection from newly created pool :: ${client.connectionString}`, err);
                                        client.active = false;
                                        client.pool.close();
                                        callback(null, false);
                                    } else {
                                        logger.info(`OracleConnectionManager.connectToStore :: connected :: ${poolStats(client)}`);
                                        _connection = conn;
                                        _connection.module = client.appName;
                                        callback(null, true);
                                    }
                                });
                            }
                        }
                    );
                } else {
                    client.pool.getConnection(function (err, conn) {
                        if (err) {
                            client.active = false; // mark client as dead
                            logger.error(`OracleConnectionManager.connectToStore :: failed to get connection ${poolStats(client)}`, err);
                            if (client.pool) {
                                client.pool.close(function () {
                                    client.pool = null;
                                });
                            }
                            callback(null, false);
                        } else {
                            logger.info(`OracleConnectionManager.connectToStore :: connected :: ${poolStats(client)}`);
                            _connection = conn;
                            _connection.module = client.appName;
                            callback(null, true);
                        }
                    });
                }
            } else {
                logger.info(`OracleConnectionManger.connectToStore :: client is not active ${client.connectString}`);
                callback(null, false);
            }
        },
        // called as soon as
        function detectSeriesDone(err, activeConnection) {
            if (err || activeConnection === undefined) {
                // all are innactive ..
                logger.error('EpyxConnectionManager.detectSeriesDone :: failed to connect to any of the available oracle end poitns :: ', _error);
                // reset all clients so next request has a chance
                _.forEach(_clients, function (client) {
                    if (!client.active) {
                        client.active = true;
                    }
                });
                // fail request because no gatwways are active
                deferred.reject(
                    _error || {
                        id: 'ora-con-manager' + hostName,
                        msg: 'failed to parse response'
                    }
                );
            } else {
                deferred.resolve(_connection);
            }
        }
    );

    return deferred.promise;
}

function _stats() {
    return _clients.map((client) => {
        return client.active
            ? {
                  connectString: client.connectString,
                  active: client.active,
                  connectionsOpen: client.pool ? client.pool.connectionOption : -1,
                  connectionsInUse: client.pool ? client.pool.connectionsInUse : -1,
                  poolMax: client.pool ? client.pool.poolMax : -1
              }
            : null;
    });
}

function _statsToString() {
    const details = _stats();
    return _clients
        .map((client) => {
            return poolStats(client);
        })
        .join(',');
}

module.exports = {
    init: function init(config) {
        var connectStrings = _.isArray(config.connectStrings) ? config.connectStrings : [config.connectStrings];
        _.forEach(connectStrings, function (connectString) {
            _clients.push({
                connectString: connectString,
                user: config.user,
                password: config.password,
                pool: null,
                count: 0,
                active: true, // assumar all are active,
                stats: true,
                appName: config.appName ?? 'unknown'
            });
        });

        // now make sure that we can connect with one istance before we start offering services
        return connectToStore().then(function connectSucess(connection) {
            // found a conncetion so now drop it ..
            connection.close(
                {
                    drop: false
                },
                () => {
                    logger.info(`OracleConnectionManager.release :: pool stats ${_statsToString()}`);
                }
            );
            return true;
        });
    },
    connect: function () {
        logger.info('OracleConnectionManager.connect :: requesting connection ::');
        return connectToStore().catch((err) => {
            logger.error('OracleConnectionManager.connect : 1st attempt to connect failed to identify active connection about to try again');
            return connectToStore() // second attempt hopfully we will not get here ...
                .catch((err) => {
                    logger.eror('OracleConnectionManager.connet :: failed to find any active db connection ... terminanting ...');
                    process.exit(1);
                });
        });
    },
    release: function release(dbCon) {
        const deferred = Q.defer();
        logger.info('OracleConnectionManager.release :: releasing connection ::');

        if (!dbCon) {
            deferred.resolve();
        } else {
            // this is OTT as I wrapping native promices with
            dbCon.close(
                {
                    drop: false
                },
                () => {
                    logger.info(`OracleConnectionManager.release :: pool stats ${_statsToString()}`);
                    deferred.resolve();
                }
            );
        }
        return deferred.promise;
    },
    closeAllConnections: function () {
        _.forEach(_clients, function (client) {
            if (client.active) {
                client.pool.close();
                client.pool = null;
                client.active = true;
            }
        });
        _clients = [];
    },
    stats: _stats,
    ConnectionPoolService: require('./connection-pool.service')
};
