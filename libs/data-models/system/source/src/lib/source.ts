interface SourceConfig {
    database: string;
    collection: string;
}

enum SourceType {
    ENTITY = 'ENTITY',
    AUDIT = 'AUDIT',
    CONFIG = 'CONFIG',
    QUEUE = 'QUEUE'
}

const Databases: { [key in SourceType]: string } = {
    [SourceType.ENTITY]: 'entities',
    [SourceType.AUDIT]: 'audit',
    [SourceType.CONFIG]: 'system-config',
    [SourceType.QUEUE]: 'queue'
};

export enum Source {
    // Queues
    ACTION_QUEUE = 'ACTION_QUEUE',
    // Configs
    TEAMS_CONFIG = 'TEAMS_CONFIG',
    USERS_CONFIG = 'USERS_CONFIG',
    // Audits
    EUOPS_AUDIT = 'EUOPS_AUDIT',
    // Entities
    BENEFIT_LIMIT_ENTITY = 'BENEFIT_LIMIT_ENTITY',
    CONTACT_ENTITY = 'CONTACT_ENTITY',
    CREDIT_NOTE_ENTITY = 'CREDIT_NOTE_ENTITY',
    EUOPS_MESSAGE_ENTITY = 'EUOPS_MESSAGE_ENTITY',
    EXCHANGE_RATE_ENTITY = 'EXCHANGE_RATE_ENTITY',
    INVOICE_LINE_ITEM_ENTITY = 'INVOICE_LINE_ITEM_ENTITY',
    INVOICE_ENTITY = 'INVOICE_ENTITY',
    NOTE_ENTITY = 'NOTE_ENTITY',
    ENTITLEMENT_BENEFIT_ENTITY = 'ENTITLEMENT_BENEFIT_ENTITY',
    REMITTANCE_BATCH_ENTITY = 'REMITTANCE_BATCH_ENTITY',
    SECURE_LOCATION_ENTITY = 'SECURE_LOCATION_ENTITY',
    TASK_PREVIEW_ENTITY = 'TASK_PREVIEW_ENTITY',
    TRIP_COST_ENTITY = 'TRIP_COST_ENTITY',
    TRIP_ENTITY = 'TRIP_ENTITY'
}

export const SourceConfigs: { [key in Source]: SourceConfig } = {
    // Queues
    [Source.ACTION_QUEUE]: { database: Databases.QUEUE, collection: 'actions' },
    // Configs
    [Source.TEAMS_CONFIG]: { database: Databases.CONFIG, collection: 'teams' },
    [Source.USERS_CONFIG]: { database: Databases.CONFIG, collection: 'users' },
    // Audits
    [Source.EUOPS_AUDIT]: { database: Databases.AUDIT, collection: 'euops' },
    // Entities
    [Source.BENEFIT_LIMIT_ENTITY]: { database: Databases.ENTITY, collection: 'benefitLimits' },
    [Source.CONTACT_ENTITY]: { database: Databases.ENTITY, collection: 'contacts' },
    [Source.CREDIT_NOTE_ENTITY]: { database: Databases.ENTITY, collection: 'creditNotes' },
    [Source.EUOPS_MESSAGE_ENTITY]: { database: Databases.ENTITY, collection: 'euopsMessages' },
    [Source.EXCHANGE_RATE_ENTITY]: { database: Databases.ENTITY, collection: 'exchangeRates' },
    [Source.INVOICE_LINE_ITEM_ENTITY]: { database: Databases.ENTITY, collection: 'invoiceLineItems' },
    [Source.INVOICE_ENTITY]: { database: Databases.ENTITY, collection: 'invoices' },
    [Source.NOTE_ENTITY]: { database: Databases.ENTITY, collection: 'notes' },
    [Source.ENTITLEMENT_BENEFIT_ENTITY]: { database: Databases.ENTITY, collection: 'entitlementBenefit' },
    [Source.REMITTANCE_BATCH_ENTITY]: { database: Databases.ENTITY, collection: 'remittanceBatches' },
    [Source.SECURE_LOCATION_ENTITY]: { database: Databases.ENTITY, collection: 'secureLocations' },
    [Source.TASK_PREVIEW_ENTITY]: { database: Databases.ENTITY, collection: 'taskPreview' },
    [Source.TRIP_COST_ENTITY]: { database: Databases.ENTITY, collection: 'tripCosts' },
    [Source.TRIP_ENTITY]: { database: Databases.ENTITY, collection: 'trips' }
};
