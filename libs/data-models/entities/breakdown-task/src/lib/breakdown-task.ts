import {
    AdditionalExperianDetails,
    Appointment,
    BreakdownContact,
    CreateReason,
    CreateReasonCode,
    FaultData,
    VehicleData,
    LocationData,
    PaginationQuerySchema,
    Recovery,
    RefCode,
    TaskIndicatorsData,
    TaskSchedule,
    TaskStatus,
    Vehicle
} from '@aa/data-models/common';
import { Eurohelp } from '@aa/data-models/entities/ancillary-task';
import { Schema, SchemaType } from '@aa/schema';
import { EntitlementSummary } from '@aa/data-models/aux/entitlement';

export interface BreakdownTask {
    id: number;
    customerRequestId?: number;
    taskType?: RefCode;
    operatorId: number;
    status: TaskStatus;
    createReason: CreateReason;
    schedule: TaskSchedule;
    changeOfNote?: boolean;
    customerGroupName?: string;
    glassModelId?: number;
    jobNoToday?: number;
    sequence?: number;
    canReattend?: boolean;
    canReopen?: boolean;
    deferQualification?: boolean;
    isDirty?: boolean;
    membImpressed?: boolean;
    split?: boolean;
    jobsheet?: string;
    contact: BreakdownContact;
    altContact?: unknown;
    entitlement?: EntitlementSummary;
    location?: LocationData;
    partsStatus?: string;
    fault?: FaultData;
    _telematicsDiagnostics?: unknown;
    eligibilityQAList?: unknown[];
    appointment?: Appointment;
    vehicle?: VehicleData;
    recovery?: Recovery;
    epyxGarageReference?: string;
    supJobTypeCode?: string;
    uiStatus?: unknown;
    parentTask?: BreakdownTask;
    indicators?: TaskIndicatorsData;
    miscFields?: unknown;
    priorities?: unknown[];
    systemStatus?: string;
    supplierJobType?: RefCode;
    coveringSiteId?: string;
    contractValidation?: unknown;
    eventHooks?: unknown;
    buzbyTelfix?: unknown;
    additionalExperianDetails?: AdditionalExperianDetails;
    eurohelp?: Eurohelp;
}

/**
 * Check if task entitlement has breakdown cover
 * @param {BreakdownTask} task
 * @return {boolean}
 */
export function isBreakdownCover(task: BreakdownTask): boolean {
    const benefit = task.entitlement?.benefits.find((benefit) => {
        return benefit.code === 'BRC';
    });

    return !!benefit;
}

// TODO: to be updated
export const BREAKDOWN_TASK_CREATE_REASONS = [
    CreateReasonCode.RSS_INVESTIGATION,
    CreateReasonCode.INITIAL_TASK,
    CreateReasonCode.SUPPORT_TASK,
    CreateReasonCode.RELAY_LEG,
    CreateReasonCode.FINAL_LEG,
    CreateReasonCode.ADDITIONAL_FAULT,
    CreateReasonCode.FUEL_ASSIST,
    CreateReasonCode.BATTERY_ASSIST,
    CreateReasonCode.TYRE_ASSIST,
    CreateReasonCode.TYRE_ASSIST_AT_DESTINATION,
    CreateReasonCode.KEY_ASSIST,
    CreateReasonCode.AA_LOGISTICS,
    CreateReasonCode.VEHICLE_RE_DELIVERY,
    CreateReasonCode.SERVICE_PROTECTION,
    CreateReasonCode.PASSENGER_RUN,
    CreateReasonCode.REPAIR_MANAGMENT,
    CreateReasonCode.SUPPORT_TASK_AT_DESTINATION,
    CreateReasonCode.PASSENGER_LEG,
    CreateReasonCode.WARRANTY_WORK,
    CreateReasonCode.ACCIDENT_MANAGMENT,
    CreateReasonCode.SELF_SERVICE_CAR,
    CreateReasonCode.SELF_SERVICE_APP,
    CreateReasonCode.ECALL,
    CreateReasonCode.REATTEND
];

export function isBreakdownTask(val: unknown): val is BreakdownTask {
    return !!val && typeof val === 'object' && BREAKDOWN_TASK_CREATE_REASONS.includes((val as unknown as BreakdownTask).createReason?.id);
}

export const QueryTaskSchema = PaginationQuerySchema.and(
    Schema.object({
        taskId: Schema.number().optional(),
        tripCode: Schema.string(),
        status: Schema.array(Schema.nativeEnum(TaskStatus)).optional(),
        associatedStatus: Schema.array(Schema.nativeEnum(TaskStatus)).optional(),
        showOnlyPrimary: Schema.boolean().optional()
    })
);
export type QueryBreakdownTask = SchemaType<typeof QueryTaskSchema>;
