import { BreakdownContact, RefCode, TaskSchedule, TaskStatus } from '@aa/data-models/common';

export interface FLPTask {
    taskType: RefCode;
    schedule: TaskSchedule;
    customerRequestId: number;
    remark: string | null;
    taskStatusTime: Date;
    taskStartTime: Date;
    furtherActionText: string;
    reasonText: string;
    actionTakenText: string;
    payment: number;
    partValue: number;
    hireCarCost: number;
    otherCosts: number;
    originatorForename: string;
    originatorSurname: string;
    patrolCallSign: string;
    originatorId: number;
    //address: Address; //don't know what to do with this yet.
    contact: BreakdownContact;
    cardName: string;
    callerName: string;
    operatorId: number;
    busLocnId: number;
    id: number;
    taskTypeId1: number;
    taskId: number;
    flpTypeText: string;
    reasonId: number;
    actionTakenId: number;
    uacRequestId: string;
    uacRejected: boolean;
    status: TaskStatus; //malstrom-models doesn't have it but we need it here.
    sequence: number; //malstrom-models doesn't have it but we need it here.
}

//todo: there should be a list of task types somewhere.
export const FLP_TASK_TYPE: RefCode = {
    code: 'FLP',
    name: 'FLP Task'
};

export function isFLPTask(val: unknown): val is FLPTask {
    return !!val && typeof val === 'object' && (val as unknown as FLPTask).taskType.code === FLP_TASK_TYPE.code;
}
