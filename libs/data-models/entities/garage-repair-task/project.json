{"name": "garage-repair-task-entity", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/data-models/entities/garage-repair-task/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/data-models/entities/garage-repair-task/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/data-models/entities/garage-repair-task/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "tags": []}