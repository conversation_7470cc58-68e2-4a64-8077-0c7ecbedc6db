import { CreateReasonCode } from '@aa/data-models/common';
import { AncillaryTask, Eurohelp } from '@aa/data-models/entities/ancillary-task';
import * as CreateReason from '@aa/malstrom-models/lib/create-reason.model';

export interface GarageRepairTask extends AncillaryTask {
    eurohelp: Eurohelp;
}

export const GARAGE_REPAIR_TASK_CREATE_REASONS = [CreateReasonCode.REPAIR, CreateReasonCode.GARAGE_REPAIR];

export function isGarageRepairTask(val: unknown): val is GarageRepairTask {
    return !!val && typeof val === 'object' && 'eurohelp' in val && GARAGE_REPAIR_TASK_CREATE_REASONS.includes((val as unknown as AncillaryTask).createReason.id);
}
