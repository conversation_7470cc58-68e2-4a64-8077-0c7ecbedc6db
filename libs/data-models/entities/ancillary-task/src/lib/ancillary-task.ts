import { Appointment, BreakdownContact, CreateReason, CreateReasonCode, VehicleData, LatLong, LocationData, RefCode, TaskSchedule, TaskStatus, UiStatus, Vehicle } from '@aa/data-models/common';
import { EntitlementSummary } from '@aa/data-models/aux/entitlement';

export interface AncillaryTask {
    id: number;
    customerRequestId: number;
    appointment: Appointment;
    bookingRefNo: number;
    contact: BreakdownContact;
    createReason: CreateReason;
    entitlement: EntitlementSummary;
    location: LocationData;
    memberLocation: LatLong;
    parentTaskId?: number;
    operatorId: number;
    schedule: TaskSchedule;
    sequence: number;
    taskType: RefCode;
    split: boolean;
    status: TaskStatus;
    uiStatus: UiStatus;
    vehicle: VehicleData;
}

export const ANCILLARY_TASK_CREATE_REASONS = [
    CreateReasonCode.PSO,
    CreateReasonCode.DIRECT_JOB,
    CreateReasonCode.SUPPLIER_ADD_TASK,
    CreateReasonCode.VEHICLE_INSPECTION,
    CreateReasonCode.MOBILE_SVC,
    CreateReasonCode.SAP_GARAGE_RECOVERY,
    CreateReasonCode.SAP_GARAGE_ROADSIDE,
    CreateReasonCode.TYREFIT_NON_BREAKDOWN,
    CreateReasonCode.AAONWARD_TASK,
    CreateReasonCode.QUOTATION,
    CreateReasonCode.DIAGNOSIS,
    CreateReasonCode.REPAIR_ASSIST,
    CreateReasonCode.SERVICE_ASSIST,
    CreateReasonCode.GLASS_ASSIST,
    CreateReasonCode.HER_CAT1,
    CreateReasonCode.HER_CAT2,
    CreateReasonCode.HER_CAT3,
    CreateReasonCode.HER_CAT4,
    CreateReasonCode.HER_CAT_SCHED,
    CreateReasonCode.GLASS_2ND_RESOURCE,
    CreateReasonCode.GLASS_REBOOK_PART,
    CreateReasonCode.GLASS_REBOOK_SAMERES,
    CreateReasonCode.GLASS_REWORK,
    CreateReasonCode.GLASS_PARTS_DELIVERY,
    CreateReasonCode.HER_2ND_RESOURCE,
    CreateReasonCode.HER_REBOOK_PART,
    CreateReasonCode.HER_REBOOK_SAMERES,
    CreateReasonCode.HER_REWORK,
    CreateReasonCode.HER_PARTS_DELIVERY,
    CreateReasonCode.HER_REIMBURSE,
    CreateReasonCode.HER_INSPECTION,
    CreateReasonCode.KEY_2ND_RESOURCE,
    CreateReasonCode.FUEL_2ND_RESOURCE,
    CreateReasonCode.HER_HEATER,
    CreateReasonCode.GLASS_REBOOK,
    CreateReasonCode.HER_REBOOK,
    CreateReasonCode.GLASS_WRAP,
    CreateReasonCode.HER_SERVICE,
    CreateReasonCode.RSS_2ND_OPINION,
    CreateReasonCode.ADHOC_TASK,
    CreateReasonCode.ADHOC_RECOVERY,
    CreateReasonCode.FUEL_ASSIST_AT_DEST,
    CreateReasonCode.KEY_ASSIST_AT_DEST,
    CreateReasonCode.BATTERY_ASSIST_AT_DEST,
    CreateReasonCode.REPAIR_ASSIST_AT_DEST,
    CreateReasonCode.FUEL_REWORK,
    CreateReasonCode.KEY_REWORK,
    CreateReasonCode.SMR_REWORK,
    CreateReasonCode.MAX_CREATE_REASONS
];

export function isAncillaryTask(val: unknown): val is AncillaryTask {
    return !!val && typeof val === 'object' && ANCILLARY_TASK_CREATE_REASONS.includes((val as unknown as AncillaryTask).createReason?.id);
}
