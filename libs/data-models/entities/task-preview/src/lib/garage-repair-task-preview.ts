import { BreakdownContact, LocationData, TaskStatus } from '@aa/data-models/common';
import { isGarageRepairTask } from '@aa/data-models/entities/garage-repair-task';
import { CreateReasonPreview, EurohelpPreview, SchedulePreview, VehiclePreview } from './aux-preview';
import { TaskPreviewBase } from './task-preview';

/**
 * Task snapshot stored in MongoDB
 */
export interface GarageRepairTaskPreview extends TaskPreviewBase {
    contact: BreakdownContact;
    status: TaskStatus;
    location: LocationData;
    schedule: SchedulePreview;
    vehicle: VehiclePreview;
    createReason: CreateReasonPreview;
    eurohelp: EurohelpPreview;
}

export function isGarageRepairTaskPreview(val: unknown): val is GarageRepairTaskPreview {
    return isGarageRepairTask(val);
}
