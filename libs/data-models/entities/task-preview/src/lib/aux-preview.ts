import { CreateReason, FaultData, Recovery, TaskSchedule, VehicleData } from '@aa/data-models/common';
import { Eurohelp } from '@aa/data-models/entities/ancillary-task';
import { EntitlementSummary } from '@aa/data-models/aux/entitlement';

export type CreateReasonPreview = Pick<CreateReason, 'id' | 'name' | 'serviceType'>;
export type SchedulePreview = Pick<TaskSchedule, 'create' | 'arrive' | 'dispatch' | 'complete'>;
export type FaultPreview = Pick<FaultData, 'id' | 'name' | 'outcome' | 'code' | 'capabilities' | 'categoryCode'>;
export type VehiclePreview = Pick<VehicleData, 'registration' | 'vehicleOwnerDetails' | 'trailerDetails' | 'experianDetails' | 'makeId' | 'modelId' | 'colour' | 'typeId' | 'twinnedWheels'>;
export type RecoveryPreview = Pick<Recovery, 'destination' | 'distance' | 'motorway'>;
export type EntitlementSummaryPreview = Pick<EntitlementSummary, 'variableData'>;
export type EurohelpPreview = Eurohelp;
