import { PeriodOfTimeData, PeriodOfTime } from '@aa/data-models/common';

export interface RentalExtensionData {
    period: PeriodOfTime;
    allowed: boolean | null;
    arrangedBy: string | null;
    limit: number | null;
}

export class RentalExtension {
    private _period: PeriodOfTime = new PeriodOfTime();
    private _allowed: boolean | null = null;
    private _arrangedBy: string | null = null;
    private _limit: number | null = null;

    constructor(raw?: Partial<RentalExtensionData>) {
        if (raw) {
            this._period = raw.period ? new PeriodOfTime(raw.period as unknown as PeriodOfTimeData) : this._period;
            this._allowed = raw.allowed ?? this._allowed;
            this._arrangedBy = raw.arrangedBy ?? this._arrangedBy;
            this._limit = raw.limit ?? this._limit;
        }
    }

    period(value?: PeriodOfTime): PeriodOfTime | void {
        if (arguments.length === 0) {
            return this._period;
        } else {
            this._period = value ?? new PeriodOfTime();
        }
    }

    allowed(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._allowed;
        } else {
            this._allowed = value ?? null;
        }
    }

    arrangedBy(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._arrangedBy;
        } else {
            this._arrangedBy = value ?? null;
        }
    }

    limit(value?: number | null): number | null | void {
        if (arguments.length === 0) {
            return this._limit;
        } else {
            this._limit = value ?? null;
        }
    }

    toJSON() {
        return {
            period: this._period.toJSON(),
            allowed: this._allowed,
            arrangedBy: this._arrangedBy,
            limit: this._limit
        };
    }
}
