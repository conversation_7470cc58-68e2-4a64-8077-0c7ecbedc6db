import { RentalApprovalData, RentalApproval } from './rental-approval';
import { InitialRentalData, InitialRental } from './initial-rental';
import { RentalExtensionData, RentalExtension } from './rental-extension';
import { ConditionalHire, ConditionalHireData } from './conditional-hire';
import { CarHireFaultRestrictions, CarHireFaultRestrictionsData } from './car-hire-fault-restrictions';

export interface CarRentalEntitlementData {
    rentalCategory?: string;
    idRequired?: boolean;
    creditCardRequired?: boolean;
    licenseRequired?: boolean;
    fuelDepositRequired?: boolean;
    towbarAllowed?: boolean;
    officeHoursApproval?: RentalApproval;
    outOfHoursApproval?: RentalApproval;
    initialRental?: InitialRental;
    extension?: RentalExtension;
    vehicleOffRoadLimit?: number;
    coolOffPeriod?: ConditionalHire;
    faultRestrictions?: CarHireFaultRestrictions;
    followOrRecoveryJob?: boolean;
    patrolCanBook?: boolean;
    agileEnabled?: boolean;
    isUKCustomerGrp?: boolean;
    isExtProcessEnabled?: boolean;
    isMaladminExtEnabled?: boolean;
    isPreAuthEnabled?: boolean;
    isSAPNotifyEnabled?: boolean;
    hireFleetTypes?: string[];
}

export interface CarRentalEntitlementData {
    rentalCategory?: string;
    idRequired?: boolean;
    creditCardRequired?: boolean;
    licenseRequired?: boolean;
    fuelDepositRequired?: boolean;
    towbarAllowed?: boolean;
    officeHoursApproval?: RentalApproval;
    outOfHoursApproval?: RentalApproval;
    initialRental?: InitialRental;
    extension?: RentalExtension;
    vehicleOffRoadLimit?: number;
    coolOffPeriod?: ConditionalHire;
    faultRestrictions?: CarHireFaultRestrictions;
    followOrRecoveryJob?: boolean;
    patrolCanBook?: boolean;
    agileEnabled?: boolean;
    defInitialHireDurationPayer?: string;
    isUKCustomerGrp?: boolean;
    isExtProcessEnabled?: boolean;
    isMaladminExtEnabled?: boolean;
    isPreAuthEnabled?: boolean;
    isSAPNotifyEnabled?: boolean;
    hireFleetTypes?: string[];
}

export class CarRentalEntitlement {
    private _rentalCategory: string | null = null;
    private _idRequired: boolean | null = null;
    private _creditCardRequired: boolean | null = null;
    private _licenseRequired: boolean | null = null;
    private _fuelDepositRequired: boolean | null = null;
    private _towbarAllowed: boolean | null = null;
    private _officeHoursApproval: RentalApproval = new RentalApproval();
    private _outOfHoursApproval: RentalApproval = new RentalApproval();
    private _initialRental: InitialRental = new InitialRental();
    private _extension: RentalExtension = new RentalExtension();
    private _vehicleOffRoadLimit: number | null = null;
    private _coolOffPeriod: ConditionalHire = new ConditionalHire();
    private _faultRestrictions: CarHireFaultRestrictions = new CarHireFaultRestrictions();
    private _followOrRecoveryJob: boolean | null = null;
    private _patrolCanBook = true;
    private _agileEnabled = true;
    private _defInitialHireDurationPayer = 'AA';
    private _isUKCustomerGrp = false;
    private _isExtProcessEnabled = true;
    private _isMaladminExtEnabled = true;
    private _isPreAuthEnabled = true;
    private _isSAPNotifyEnabled = true;
    private _hireFleetTypes: string[] = [];

    static LIKE_FOR_LIKE = 'LIKE-FOR-LIKE';
    static L4L = 'L4L';

    constructor(raw?: Partial<CarRentalEntitlementData>) {
        if (raw) {
            this._rentalCategory = raw.rentalCategory ? raw.rentalCategory.replace(/_/g, '-').replace('4-X-4', '4X4') : this._rentalCategory;
            this._idRequired = raw.idRequired ?? this._idRequired;
            this._creditCardRequired = raw.creditCardRequired ?? this._creditCardRequired;
            this._licenseRequired = raw.licenseRequired ?? this._licenseRequired;
            this._fuelDepositRequired = raw.fuelDepositRequired ?? this._fuelDepositRequired;
            this._towbarAllowed = raw.towbarAllowed ?? this._towbarAllowed;
            this._officeHoursApproval = raw.officeHoursApproval ? new RentalApproval(raw.officeHoursApproval as unknown as RentalApprovalData) : this._officeHoursApproval;
            this._outOfHoursApproval = raw.outOfHoursApproval ? new RentalApproval(raw.outOfHoursApproval as unknown as RentalApprovalData) : this._outOfHoursApproval;
            this._initialRental = raw.initialRental ? new InitialRental(raw.initialRental as unknown as InitialRentalData) : this._initialRental;
            this._extension = raw.extension ? new RentalExtension(raw.extension as unknown as RentalExtensionData) : this._extension;
            this._vehicleOffRoadLimit = raw.vehicleOffRoadLimit ?? this._vehicleOffRoadLimit;
            this._coolOffPeriod = raw.coolOffPeriod ? new ConditionalHire(raw.coolOffPeriod as unknown as ConditionalHireData) : this._coolOffPeriod;
            this._faultRestrictions = raw.faultRestrictions ? new CarHireFaultRestrictions(raw.faultRestrictions as unknown as CarHireFaultRestrictionsData) : this._faultRestrictions;
            this._followOrRecoveryJob = raw.followOrRecoveryJob ?? this._followOrRecoveryJob;
            this._patrolCanBook = raw.patrolCanBook ?? this._patrolCanBook;
            this._agileEnabled = raw.agileEnabled ?? this._agileEnabled;
            this._defInitialHireDurationPayer = raw.defInitialHireDurationPayer ?? this._defInitialHireDurationPayer;
            this._isUKCustomerGrp = raw.isUKCustomerGrp ?? this._isUKCustomerGrp;
            this._isExtProcessEnabled = raw.isExtProcessEnabled ?? this._isExtProcessEnabled;
            this._isMaladminExtEnabled = raw.isMaladminExtEnabled ?? this._isMaladminExtEnabled;
            this._isPreAuthEnabled = raw.isPreAuthEnabled ?? this._isPreAuthEnabled;
            this._isSAPNotifyEnabled = raw.isSAPNotifyEnabled ?? this._isSAPNotifyEnabled;
            this._hireFleetTypes = raw.hireFleetTypes ?? this._hireFleetTypes;
        }
    }

    rentalCategory(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._rentalCategory === CarRentalEntitlement.LIKE_FOR_LIKE ? CarRentalEntitlement.L4L : this._rentalCategory;
        } else {
            this._rentalCategory = value || null;
        }
    }

    idRequired(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._idRequired;
        } else {
            this._idRequired = value || null;
        }
    }

    creditCardRequired(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._creditCardRequired;
        } else {
            this._creditCardRequired = value || null;
        }
    }

    licenseRequired(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._licenseRequired;
        } else {
            this._licenseRequired = value || null;
        }
    }

    fuelDepositRequired(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._fuelDepositRequired;
        } else {
            this._fuelDepositRequired = value || null;
        }
    }

    towbarAllowed(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._towbarAllowed;
        } else {
            this._towbarAllowed = value || null;
        }
    }

    officeHoursApproval(value?: RentalApproval): RentalApproval | void {
        if (arguments.length === 0) {
            return this._officeHoursApproval;
        } else {
            this._officeHoursApproval = value || new RentalApproval();
        }
    }

    outOfHoursApproval(value?: RentalApproval): RentalApproval | void {
        if (arguments.length === 0) {
            return this._outOfHoursApproval;
        } else {
            this._outOfHoursApproval = value || new RentalApproval();
        }
    }

    initialRental(value?: InitialRental): InitialRental | void {
        if (arguments.length === 0) {
            return this._initialRental;
        } else {
            this._initialRental = value || new InitialRental();
        }
    }

    extension(value?: RentalExtension): RentalExtension | void {
        if (arguments.length === 0) {
            return this._extension;
        } else {
            this._extension = value || new RentalExtension();
        }
    }

    vehicleOffRoadLimit(value?: number | null): number | null | void {
        if (arguments.length === 0) {
            return this._vehicleOffRoadLimit;
        } else {
            this._vehicleOffRoadLimit = value || null;
        }
    }

    coolOffPeriod(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._coolOffPeriod;
        } else {
            this._coolOffPeriod = value || new ConditionalHire();
        }
    }

    faultRestrictions(value?: CarHireFaultRestrictions): CarHireFaultRestrictions | void {
        if (arguments.length === 0) {
            return this._faultRestrictions;
        } else {
            this._faultRestrictions = value || new CarHireFaultRestrictions();
        }
    }

    followOrRecoveryJob(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._followOrRecoveryJob;
        } else {
            this._followOrRecoveryJob = value || null;
        }
    }

    patrolCanBook(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._patrolCanBook;
        } else {
            this._patrolCanBook = value || true;
        }
    }

    agileEnabled(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._agileEnabled;
        } else {
            this._agileEnabled = value || true;
        }
    }

    defInitialHireDurationPayer(value?: string): string | void {
        if (arguments.length === 0) {
            return this._defInitialHireDurationPayer;
        } else {
            this._defInitialHireDurationPayer = value || 'AA';
        }
    }

    isUKCustomerGrp(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._isUKCustomerGrp;
        } else {
            this._isUKCustomerGrp = value || false;
        }
    }

    isExtProcessEnabled(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._isExtProcessEnabled;
        } else {
            this._isExtProcessEnabled = value || true;
        }
    }

    isMaladminExtEnabled(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._isMaladminExtEnabled;
        } else {
            this._isMaladminExtEnabled = value || true;
        }
    }

    isPreAuthEnabled(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._isPreAuthEnabled;
        } else {
            this._isPreAuthEnabled = value || true;
        }
    }

    isSAPNotifyEnabled(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._isSAPNotifyEnabled;
        } else {
            this._isSAPNotifyEnabled = value || true;
        }
    }

    hireFleetTypes(value?: string[]): string[] | void {
        if (arguments.length === 0) {
            return this._hireFleetTypes;
        } else {
            this._hireFleetTypes = value || [];
        }
    }

    toJSON() {
        return {
            rentalCategory: this._rentalCategory,
            idRequired: this._idRequired,
            creditCardRequired: this._creditCardRequired,
            licenseRequired: this._licenseRequired,
            fuelDepositRequired: this._fuelDepositRequired,
            towbarAllowed: this._towbarAllowed,
            officeHoursApproval: this._officeHoursApproval.toJSON(),
            outOfHoursApproval: this._outOfHoursApproval.toJSON(),
            initialRental: this._initialRental.toJSON(),
            extension: this._extension.toJSON(),
            vehicleOffRoadLimit: this._vehicleOffRoadLimit,
            coolOffPeriod: this._coolOffPeriod.toJSON(),
            faultRestrictions: this._faultRestrictions.toJSON(),
            followOrRecoveryJob: this._followOrRecoveryJob,
            patrolCanBook: this._patrolCanBook,
            agileEnabled: this._agileEnabled,
            defInitialHireDurationPayer: this._defInitialHireDurationPayer,
            isUKCustomerGrp: this._isUKCustomerGrp,
            isExtProcessEnabled: this._isExtProcessEnabled,
            isMaladminExtEnabled: this._isMaladminExtEnabled,
            isPreAuthEnabled: this._isPreAuthEnabled,
            isSAPNotifyEnabled: this._isSAPNotifyEnabled,
            hireFleetTypes: this._hireFleetTypes
        };
    }
}
