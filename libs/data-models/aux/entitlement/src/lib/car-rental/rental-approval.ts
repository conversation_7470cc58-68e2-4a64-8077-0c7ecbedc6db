export interface RentalApprovalData {
    instructions?: string | null;
    required?: boolean | null;
    needsAuthorisation?: boolean | null;
    phoneNumber?: string | null;
}

export class RentalApproval {
    private _instructions: string | null = null;
    private _required: boolean | null = null;
    private _needsAuthorisation: boolean | null = null;
    private _phoneNumber: string | null = null;

    constructor(raw?: Partial<RentalApprovalData>) {
        if (raw) {
            this._instructions = raw.instructions ?? this._instructions;
            this._required = raw.required ?? this._required;
            this._needsAuthorisation = raw.needsAuthorisation ?? this._needsAuthorisation;
            this._phoneNumber = raw.phoneNumber ?? this._phoneNumber;
        }
    }

    instructions(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._instructions;
        } else {
            this._instructions = value ?? null;
        }
    }

    required(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._required;
        } else {
            this._required = value ?? null;
        }
    }

    needsAuthorisation(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._needsAuthorisation;
        } else {
            this._needsAuthorisation = value ?? null;
        }
    }

    phoneNumber(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._phoneNumber;
        } else {
            this._phoneNumber = value ?? null;
        }
    }

    toJSON() {
        return {
            instructions: this._instructions,
            required: this._required,
            needsAuthorisation: this._needsAuthorisation,
            phoneNumber: this._phoneNumber
        };
    }
}
