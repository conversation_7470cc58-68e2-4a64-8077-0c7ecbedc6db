import { PeriodOfTimeData, PeriodOfTime } from '@aa/data-models/common';

export interface InitialRentalData {
    period?: PeriodOfTime;
    customerInitiated?: boolean | null;
    passwordRequired?: boolean | null;
    requestOnly?: boolean | null;
}

export class InitialRental {
    private _period: PeriodOfTime = new PeriodOfTime();
    private _customerInitiated: boolean | null = null;
    private _passwordRequired: boolean | null = null;
    private _requestOnly: boolean | null = null;

    constructor(raw?: Partial<InitialRentalData>) {
        if (raw) {
            this._period = raw.period ? new PeriodOfTime(raw.period as unknown as PeriodOfTimeData) : this._period;
            this._customerInitiated = raw.customerInitiated ?? this._customerInitiated;
            this._passwordRequired = raw.passwordRequired ?? this._passwordRequired;
            this._requestOnly = raw.requestOnly ?? this._requestOnly;
        }
    }

    period(value?: PeriodOfTime): PeriodOfTime | void {
        if (arguments.length === 0) {
            return this._period;
        } else {
            this._period = value ?? new PeriodOfTime();
        }
    }

    customerInitiated(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._customerInitiated;
        } else {
            this._customerInitiated = value ?? null;
        }
    }

    passwordRequired(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._passwordRequired;
        } else {
            this._passwordRequired = value ?? null;
        }
    }

    requestOnly(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._requestOnly;
        } else {
            this._requestOnly = value ?? null;
        }
    }

    toJSON() {
        return {
            period: this._period.toJSON(),
            customerInitiated: this._customerInitiated,
            passwordRequired: this._passwordRequired,
            requestOnly: this._requestOnly
        };
    }
}
