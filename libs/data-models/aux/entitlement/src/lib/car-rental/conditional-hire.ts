import { PeriodOfTimeData, PeriodOfTime } from '@aa/data-models/common';

export interface ConditionalHireData {
    period: PeriodOfTime;
    allowed: boolean;
}

export class ConditionalHire {
    private _period: PeriodOfTime = new PeriodOfTime();
    private _allowed = true;

    constructor(raw?: Partial<ConditionalHireData>) {
        if (raw) {
            this._period = raw.period ? new PeriodOfTime(raw.period as unknown as PeriodOfTimeData) : this._period;
            this._allowed = raw.allowed ?? this._allowed;
        }
    }

    period(value?: PeriodOfTime): PeriodOfTime | void {
        if (arguments.length === 0) {
            return this._period;
        } else {
            this._period = value ?? new PeriodOfTime();
        }
    }

    allowed(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._allowed;
        } else {
            this._allowed = value ?? true;
        }
    }

    toJSON() {
        return {
            period: this._period.toJSON(),
            allowed: this._allowed
        };
    }
}
