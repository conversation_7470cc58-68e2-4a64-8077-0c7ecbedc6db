import { ConditionalHire } from './conditional-hire';

export interface CarHireFaultRestrictionsData {
    rtc?: ConditionalHire;
    misfuel?: ConditionalHire;
    frozenPartFault?: ConditionalHire;
    punctureFault?: ConditionalHire;
    heatingSystemFault?: ConditionalHire;
    vehicleKeysFault?: ConditionalHire;
    adBlueInFuelFault?: ConditionalHire;
    stuckInFault?: ConditionalHire;
    towingFault?: ConditionalHire;
    miscFault?: ConditionalHire;
    waterInFuelFault?: ConditionalHire;
    insuranceCoveredFaults?: ConditionalHire;
    outOfFuelFault?: ConditionalHire;
}

export class CarHireFaultRestrictions {
    private _rtc: ConditionalHire = new ConditionalHire();
    private _misfuel: ConditionalHire = new ConditionalHire();
    private _frozenPartFault: ConditionalHire = new ConditionalHire();
    private _punctureFault: ConditionalHire = new ConditionalHire();
    private _heatingSystemFault: ConditionalHire = new ConditionalHire();
    private _vehicleKeysFault: ConditionalHire = new ConditionalHire();
    private _adBlueInFuelFault: ConditionalHire = new ConditionalHire();
    private _stuckInFault: ConditionalHire = new ConditionalHire();
    private _towingFault: ConditionalHire = new ConditionalHire();
    private _miscFault: ConditionalHire = new ConditionalHire();
    private _waterInFuelFault: ConditionalHire = new ConditionalHire();
    private _insuranceCoveredFaults: ConditionalHire = new ConditionalHire();
    private _outOfFuelFault: ConditionalHire = new ConditionalHire();

    constructor(raw?: Partial<CarHireFaultRestrictionsData>) {
        if (raw) {
            this._rtc = raw.rtc ?? this._rtc;
            this._misfuel = raw.misfuel ?? this._misfuel;
            this._frozenPartFault = raw.frozenPartFault ?? this._frozenPartFault;
            this._punctureFault = raw.punctureFault ?? this._punctureFault;
            this._heatingSystemFault = raw.heatingSystemFault ?? this._heatingSystemFault;
            this._vehicleKeysFault = raw.vehicleKeysFault ?? this._vehicleKeysFault;
            this._adBlueInFuelFault = raw.adBlueInFuelFault ?? this._adBlueInFuelFault;
            this._stuckInFault = raw.stuckInFault ?? this._stuckInFault;
            this._towingFault = raw.towingFault ?? this._towingFault;
            this._miscFault = raw.miscFault ?? this._miscFault;
            this._waterInFuelFault = raw.waterInFuelFault ?? this._waterInFuelFault;
            this._insuranceCoveredFaults = raw.insuranceCoveredFaults ?? this._insuranceCoveredFaults;
            this._outOfFuelFault = raw.outOfFuelFault ?? this._outOfFuelFault;
        }
    }

    rtc(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._rtc;
        } else {
            this._rtc = value || new ConditionalHire();
        }
    }

    misfuel(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._misfuel;
        } else {
            this._misfuel = value || new ConditionalHire();
        }
    }

    frozenPartFault(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._frozenPartFault;
        } else {
            this._frozenPartFault = value || new ConditionalHire();
        }
    }

    punctureFault(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._punctureFault;
        } else {
            this._punctureFault = value || new ConditionalHire();
        }
    }

    heatingSystemFault(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._heatingSystemFault;
        } else {
            this._heatingSystemFault = value || new ConditionalHire();
        }
    }

    vehicleKeysFault(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._vehicleKeysFault;
        } else {
            this._vehicleKeysFault = value || new ConditionalHire();
        }
    }

    adBlueInFuelFault(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._adBlueInFuelFault;
        } else {
            this._adBlueInFuelFault = value || new ConditionalHire();
        }
    }

    stuckInFault(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._stuckInFault;
        } else {
            this._stuckInFault = value || new ConditionalHire();
        }
    }

    towingFault(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._towingFault;
        } else {
            this._towingFault = value || new ConditionalHire();
        }
    }

    miscFault(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._miscFault;
        } else {
            this._miscFault = value || new ConditionalHire();
        }
    }

    waterInFuelFault(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._waterInFuelFault;
        } else {
            this._waterInFuelFault = value || new ConditionalHire();
        }
    }

    insuranceCoveredFaults(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._insuranceCoveredFaults;
        } else {
            this._insuranceCoveredFaults = value || new ConditionalHire();
        }
    }

    outOfFuelFault(value?: ConditionalHire): ConditionalHire | void {
        if (arguments.length === 0) {
            return this._outOfFuelFault;
        } else {
            this._outOfFuelFault = value || new ConditionalHire();
        }
    }

    toJSON() {
        return {
            rtc: this._rtc.toJSON(),
            misfuel: this._misfuel.toJSON(),
            frozenPartFault: this._frozenPartFault.toJSON(),
            punctureFault: this._punctureFault.toJSON(),
            heatingSystemFault: this._heatingSystemFault.toJSON(),
            vehicleKeysFault: this._vehicleKeysFault.toJSON(),
            adBlueInFuelFault: this._adBlueInFuelFault.toJSON(),
            stuckInFault: this._stuckInFault.toJSON(),
            towingFault: this._towingFault.toJSON(),
            miscFault: this._miscFault.toJSON(),
            waterInFuelFault: this._waterInFuelFault.toJSON(),
            insuranceCoveredFaults: this._insuranceCoveredFaults.toJSON(),
            outOfFuelFault: this._outOfFuelFault.toJSON()
        };
    }
}
