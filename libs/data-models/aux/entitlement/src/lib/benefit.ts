export interface BenefitData {
    id: number | null;
    name: string | null;
    code: string | null;
    promptTextId: number | null;
}
export class Benefit {
    private _id: number | null = null;
    private _name: string | null = null;
    private _code: string | null = null;
    private _promptTextId: number | null = null;

    constructor(rawData?: Partial<BenefitData>) {
        if (rawData) {
            this._id = rawData.id ?? null;
            this._name = rawData.name ?? null;
            this._code = rawData.code ?? null;
            this._promptTextId = rawData.promptTextId ?? null;
        }
    }

    id(value?: number | null): number | null | void {
        if (arguments.length === 0) {
            return this._id;
        } else {
            this._id = value ?? null;
        }
    }

    name(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._name;
        } else {
            this._name = value ?? null;
        }
    }

    code(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._code;
        } else {
            this._code = value ?? null;
        }
    }

    promptTextId(value?: number | null): number | null | void {
        if (arguments.length === 0) {
            return this._promptTextId;
        } else {
            this._promptTextId = value ?? null;
        }
    }

    toJSON(): BenefitData {
        return {
            id: this._id,
            name: this._name,
            code: this._code,
            promptTextId: this._promptTextId
        };
    }
}
