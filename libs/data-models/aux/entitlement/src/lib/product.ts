import { UsageData, Usage } from '@aa/data-models/common';

export interface ProductData {
    name?: string | null;
    code?: string | null;
    claimFromDateTime?: Date | string | null;
    startDate?: Date | string | null;
    endDate?: Date | string | null;
    benefitCode?: string | null;
    expired?: boolean;
    coveredVehicle?: string[];
    coolOffPeriod?: boolean;
    additionalProperties?: unknown[];
    usage?: Usage[];
}

export class Product {
    private _name: string | null = null;
    private _code: string | null = null;
    private _claimFromDateTime: Date | null = null;
    private _startDate: Date | null = null;
    private _endDate: Date | null = null;
    private _benefitCode: string | null = null;
    private _expired = false;
    private _coveredVehicle: string[] = [];
    private _coolOffPeriod = false;
    private _additionalProperties: any[] = [];
    private _usage: Usage[] = [];

    // Product codes
    static RELAY = 'Relay';
    static HOMESTART = 'HomeStart';
    static ROADSIDE = 'RoadSide';
    static CONNECTED_CAR = 'CONC';
    static CAR_GENIE = 'CONN';
    static COMMERCIAL_USE = 'Commercial Use';

    constructor(raw?: Partial<ProductData>) {
        if (raw) {
            this._name = raw.name ?? this._name;
            this._code = raw.code ?? this._code;
            this._claimFromDateTime = raw.claimFromDateTime ? this.dateWithTimeZoneOffset(raw.claimFromDateTime) : this._claimFromDateTime;
            this._startDate = raw.startDate ? new Date(raw.startDate) : this._startDate;
            this._endDate = raw.endDate ? new Date(raw.endDate) : this._endDate;
            this._benefitCode = raw.benefitCode ?? this._benefitCode;
            this._expired = raw.expired ?? this._expired;
            this._coveredVehicle = raw.coveredVehicle ?? this._coveredVehicle;
            this._coolOffPeriod = raw.coolOffPeriod ?? this._coolOffPeriod;
            this._additionalProperties = raw.additionalProperties ?? this._additionalProperties;

            // TODO: Test if it works correctly // Matt
            if (raw.usage) {
                raw.usage.forEach((usage) => {
                    this._usage.push(new Usage(usage as unknown as UsageData));
                });
            }
        }
    }

    private dateWithTimeZoneOffset(dateString: Date | string): Date {
        if (dateString instanceof Date) {
            return dateString;
        }
        return new Date(new Date(dateString).valueOf() + new Date(dateString).getTimezoneOffset() * 60000);
    }

    name(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            // Getter behavior
            return this._name;
        } else {
            // Setter behavior
            this._name = value ?? null;
        }
    }

    code(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._code;
        } else {
            this._code = value ?? null;
        }
    }

    claimFromDateTime(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._claimFromDateTime;
        } else {
            this._claimFromDateTime = value ? this.dateWithTimeZoneOffset(value) : null;
        }
    }

    startDate(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._startDate;
        } else {
            this._startDate = value ?? null;
        }
    }

    endDate(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._endDate;
        } else {
            this._endDate = value ?? null;
        }
    }

    benefitCode(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._benefitCode;
        } else {
            this._benefitCode = value ?? null;
        }
    }

    expired(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._expired;
        } else {
            this._expired = value ?? false;
        }
    }

    coveredVehicle(value?: string[]): string[] | void {
        if (arguments.length === 0) {
            return this._coveredVehicle;
        } else {
            this._coveredVehicle = value ?? [];
        }
    }

    coolOffPeriod(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._coolOffPeriod;
        } else {
            this._coolOffPeriod = value ?? false;
        }
    }

    additionalProperties(value?: unknown[]): unknown[] | void {
        if (arguments.length === 0) {
            return this._additionalProperties;
        } else {
            this._additionalProperties = value ?? [];
        }
    }

    usage(value?: Usage[]): Usage[] | void {
        if (arguments.length === 0) {
            return this._usage;
        } else {
            this._usage = value ?? [];
        }
    }

    getAdditionalProperty(key: string): unknown | null {
        const result = this._additionalProperties.find((property) => property[key] !== undefined);
        return result ? result : null;
    }

    isVehicleCovered(vrn: string): boolean {
        const coveredVehicleLowerCase = this._coveredVehicle.map((vehicle) => vehicle.toLowerCase());
        const vrnLowerCase = vrn.toLowerCase();
        return coveredVehicleLowerCase.includes(vrnLowerCase) && !this._expired;
    }

    isCUV(): boolean {
        return this._benefitCode === 'CUV';
    }

    isBRC(): boolean {
        return this._benefitCode === 'BRC';
    }

    toJSON() {
        return {
            name: this._name,
            code: this._code,
            claimFromDateTime: this._claimFromDateTime,
            startDate: this._startDate,
            endDate: this._endDate,
            benefitCode: this._benefitCode,
            expired: this._expired,
            coveredVehicle: this._coveredVehicle,
            coolOffPeriod: this._coolOffPeriod,
            additionalProperties: this._additionalProperties,
            usage: this._usage.map((usage) => usage.toJSON())
        };
    }
}
