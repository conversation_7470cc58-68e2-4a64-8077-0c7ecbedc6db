import { VulnerabilityData, Vulnerability } from '@aa/data-models/common';

export interface EntitlementContactOptionData {
    deceased: boolean;
    goneAway: boolean;
    specialNeeds: boolean;
    vulnerability: Vulnerability;
    additionalRoadsideSupport: boolean;
    additionalRoadsideSupportInformation: string | null;
}

export class EntitlementContactOption {
    private _deceased = false;
    private _goneAway = false;
    private _specialNeeds = false;
    private _vulnerability: Vulnerability = new Vulnerability();
    private _additionalRoadsideSupport = false;
    private _additionalRoadsideSupportInformation: string | null = null;

    constructor(raw?: Partial<EntitlementContactOptionData>) {
        if (raw) {
            this._deceased = raw.deceased ?? this._deceased;
            this._goneAway = raw.goneAway ?? this._goneAway;
            this._specialNeeds = raw.specialNeeds ?? this._specialNeeds;
            this._vulnerability = raw.vulnerability ? new Vulnerability(raw.vulnerability as unknown as VulnerabilityData) : this._vulnerability;
            this._additionalRoadsideSupport = raw.additionalRoadsideSupport ?? this._additionalRoadsideSupport;
            this._additionalRoadsideSupportInformation = raw.additionalRoadsideSupportInformation ?? this._additionalRoadsideSupportInformation;
        }
    }

    deceased(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._deceased;
        } else {
            this._deceased = value ?? false;
        }
    }

    goneAway(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._goneAway;
        } else {
            this._goneAway = value ?? false;
        }
    }

    specialNeeds(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._specialNeeds;
        } else {
            this._specialNeeds = value ?? false;
        }
    }

    vulnerability(value?: Vulnerability): Vulnerability | void {
        if (arguments.length === 0) {
            return this._vulnerability;
        } else {
            this._vulnerability = value ?? new Vulnerability();
        }
    }

    additionalRoadsideSupport(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._additionalRoadsideSupport;
        } else {
            this._additionalRoadsideSupport = value ?? false;
        }
    }

    additionalRoadsideSupportInformation(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._additionalRoadsideSupportInformation;
        } else {
            this._additionalRoadsideSupportInformation = value ?? null;
        }
    }

    toJSON() {
        return {
            deceased: this._deceased,
            goneAway: this._goneAway,
            specialNeeds: this._specialNeeds,
            vulnerability: this._vulnerability.toJSON(),
            additionalRoadsideSupport: this._additionalRoadsideSupport,
            additionalRoadsideSupportInformation: this._additionalRoadsideSupportInformation
        };
    }
}
