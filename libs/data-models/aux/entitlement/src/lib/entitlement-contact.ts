import { Address, AddressData, PhoneData, Phone } from '@aa/data-models/common';
import { EntitlementContactOption, EntitlementContactOptionData } from './entitlement-contact-option';

export interface EntitlementContactData {
    id: number | null;
    title: string | null;
    firstName: string | null;
    initials: string | null;
    surname: string | null;
    phones: PhoneData[];
    address: AddressData | null;
    dateOfBirth: Date | null;
    role: string | null;
    options: EntitlementContactOptionData;
}

export class EntitlementContact {
    private _id: number | null = null;
    private _title: string | null = null;
    private _firstName: string | null = null;
    private _initials: string | null = null;
    private _surname: string | null = null;
    private _phones: Phone[] = [];
    private _address: Address | null = new Address();
    private _dateOfBirth: Date | null = null;
    private _role: string | null = null;
    private _options: EntitlementContactOption = new EntitlementContactOption();

    static AUTHORISED_DRIVER_ROLE = 'Authorised Driver';
    static FAMILY_COVER_ROLE = 'Family Cover';
    static IS_ANY_DRIVER_ALLOWED = 99999;

    constructor(raw?: Partial<EntitlementContactData>) {
        if (raw) {
            this._id = raw.id ?? this._id;
            this._title = raw.title ?? this._title;
            this._firstName = raw.firstName ?? this._firstName;
            this._initials = raw.initials ?? this._initials;
            this._surname = raw.surname ?? this._surname;
            this._address = raw.address ? new Address(raw.address as unknown as AddressData) : this._address;
            this._dateOfBirth = raw.dateOfBirth ? new Date(raw.dateOfBirth) : this._dateOfBirth;
            this._role = raw.role ?? this._role;
            this._options = raw.options ? new EntitlementContactOption(raw.options as unknown as EntitlementContactOptionData) : this._options;

            if (raw.phones) {
                this._phones = raw.phones.map((phone) => new Phone(phone as unknown as PhoneData));
            }
        }
    }

    id(value?: number | null): number | null | void {
        if (arguments.length === 0) {
            return this._id;
        } else {
            this._id = value ?? null;
        }
    }

    title(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._title;
        } else {
            this._title = value ?? null;
        }
    }

    firstName(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._firstName;
        } else {
            this._firstName = value ?? null;
        }
    }

    initials(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._initials;
        } else {
            this._initials = value ?? null;
        }
    }

    surname(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._surname;
        } else {
            this._surname = value ?? null;
        }
    }

    phones(value?: Phone[]): Phone[] | void {
        if (arguments.length === 0) {
            return this._phones;
        } else {
            this._phones = value ?? [];
        }
    }

    address(value?: Address | null): Address | null | void {
        if (arguments.length === 0) {
            return this._address;
        } else {
            this._address = value ?? null;
        }
    }

    dateOfBirth(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._dateOfBirth;
        } else {
            this._dateOfBirth = value ?? null;
        }
    }

    role(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._role;
        } else {
            this._role = value ?? null;
        }
    }

    options(value?: EntitlementContactOption): EntitlementContactOption | void {
        if (arguments.length === 0) {
            return this._options;
        } else {
            this._options = value ?? new EntitlementContactOption();
        }
    }

    isAuthorisedDriver(): boolean {
        return this._role === EntitlementContact.AUTHORISED_DRIVER_ROLE;
    }

    isFamilyCover(): boolean {
        return this._role === EntitlementContact.FAMILY_COVER_ROLE;
    }

    isAnyDriverAllowed(): boolean {
        return this._id === EntitlementContact.IS_ANY_DRIVER_ALLOWED;
    }

    toJSON() {
        return {
            id: this._id,
            title: this._title,
            firstName: this._firstName,
            initials: this._initials,
            surname: this._surname,
            phones: this._phones.map((phone) => phone.toJSON()),
            address: this._address ? this._address.toJSON() : null,
            dateOfBirth: this._dateOfBirth,
            role: this._role,
            options: this._options.toJSON()
        };
    }
}
