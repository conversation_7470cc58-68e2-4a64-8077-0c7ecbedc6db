import { EntitlementContact, EntitlementContactData } from './entitlement-contact';
import { Cover, CoverData } from './cover';
import { ProductData, Product } from './product';
import { Benefit, BenefitData } from './benefit';
import {
    CorrespondenceAddress,
    CustomerGroupCode,
    CorrespondenceAddressData,
    NarrativeData,
    PolicyData,
    VehicleData,
    Narrative,
    Policy,
    Usage,
    Vehicle,
    Country,
    validDeploymentLocales
} from '@aa/data-models/common';
import { CarRentalEntitlement, CarRentalEntitlementData } from './car-rental/car-rental-entitlement';

export interface EntitlementData {
    systemId?: string | null;
    contact?: EntitlementContactData;
    policy?: PolicyData;
    vehicle?: VehicleData;
    cover?: CoverData;
    correspondenceAddress?: CorrespondenceAddressData | null;
    packages?: ProductData[];
    products?: ProductData[];
    narratives?: NarrativeData[];
    fairPlay?: boolean | null;
    vipServiceCode?: string | null;
    benefits?: BenefitData[];
    entitlementVariableData?: unknown[];
    memberDetailsForPrime?: string[];
    riskCode?: string | null;
    extendedProductCategory?: boolean;
    additionalBrcVRNs?: unknown[];
    payForUse?: boolean;
    carRental?: CarRentalEntitlementData | null;
    isNoServiceAccount?: boolean | null;
    isSplit?: boolean | null;
    daysCovered?: number | null;
    tripCountLimit?: number | null;
}

export interface LegacyEntitlementSearchQuery {
    membershipSearch: {
        search?: string;
        param: {
            inPostcode?: string;
            outPostcode?: string;
            membershipNumber?: string;
            vehicleRegistrationNumber?: string;
            vehicleIdentificationNumber?: string;
            policyId?: string;
            surname?: string;
            initials?: string;
            town?: string;
            companyName?: string;
            cardNumber?: string;
            page?: number;
            pageSize?: number;
            customerGroupCode?: string;
        };
    };
}

export interface LegacyEntitlementSearchResult {
    results: {
        entitlements?: EntitlementData[];
        companies?: CompanyEntitlement[];
        nextPage: number;
    };
    query: Record<string, string>;
}

export interface CompanyEntitlement {
    active: true;
    unlistedSlvEntitlement: EntitlementData;
    town: string;
    id: number;
    customerGroup: CustomerGroupCode;
    memberReference: string;
    cardReference: string;
    firstLevelValidation: string;
    systemId: string;
    companyName: string;
    package: string;
    roadEntitlementSystemUrl: unknown;
    secondLevelValidationTypes: unknown[];
    entitlements: EntitlementData[];
}

export class Entitlement {
    private _systemId: string | null = null;
    private _contact: EntitlementContact = new EntitlementContact();
    private _policy: Policy = new Policy();
    private _vehicle: Vehicle = new Vehicle();
    private _cover: Cover = new Cover();
    private _correspondenceAddress: CorrespondenceAddress | null = null;
    private _packages: Product[] = [];
    private _products: Product[] = [];
    private _narratives: Narrative[] = [];
    private _fairPlay: boolean | null = null;
    private _vipServiceCode: string | null = null;
    private _benefits: Benefit[] = [];
    private _entitlementVariableData: unknown[] = [];
    private _memberDetailsForPrime: string[] = [];
    private _riskCode: string | null = null;
    private _extendedProductCategory = false;
    private _additionalBrcVRNs: unknown[] = [];
    private _payForUse = false;
    private _carRental: CarRentalEntitlement | null = null;
    private _isNoServiceAccount: boolean | null = null;
    private _isSplit: boolean | null = null;
    private _daysCovered: number | null = null;
    private _tripCountLimit: number | null = null;

    static TIA_HOME_INS = 'TIA_HOME_INS';
    static DEFAULT_MAX_CALLOUTS = 1000;
    static DEFAULT_MAX_DISTANCE = 20;
    static DEMAND_DEFLECTION = {
        MAX_CALLOUTS_BY_MEMBERSHIP_TYPE: {
            Single: 7,
            Vehicle: 7,
            Joint: 8,
            Family: 9
        }
    };

    constructor(raw?: Partial<EntitlementData>) {
        if (raw) {
            this._systemId = raw.systemId ?? null;
            this._fairPlay = raw.fairPlay ?? null;
            this._vipServiceCode = raw.vipServiceCode ?? null;
            this._memberDetailsForPrime = raw.memberDetailsForPrime ?? [];
            this._contact = raw.contact ? new EntitlementContact(raw.contact as unknown as EntitlementContactData) : this._contact;
            this._correspondenceAddress = raw.correspondenceAddress ? new CorrespondenceAddress(raw.correspondenceAddress as unknown as CorrespondenceAddressData) : null;
            this._policy = raw.policy ? new Policy(raw.policy as unknown as PolicyData) : this._policy;
            this._vehicle = raw.vehicle ? new Vehicle(raw.vehicle as unknown as VehicleData) : this._vehicle;
            this._entitlementVariableData = raw.entitlementVariableData ?? [];
            this._riskCode = raw.riskCode ?? null;
            this._cover = raw.cover ? new Cover(raw.cover) : this._cover;
            this._extendedProductCategory = raw.extendedProductCategory ?? false;
            this._payForUse = raw.payForUse ?? false;
            this._additionalBrcVRNs = raw.additionalBrcVRNs ?? [];
            this._isNoServiceAccount = raw.isNoServiceAccount ?? null;
            this._isSplit = raw.isSplit ?? null;
            this._daysCovered = raw.daysCovered ?? null;
            this._tripCountLimit = raw.tripCountLimit ?? null;

            if (raw.benefits) {
                this._benefits = raw.benefits.map((benefit) => new Benefit(benefit as unknown as BenefitData));
            }

            if (raw.packages) {
                this._packages = raw.packages.map((pkg) => new Product(pkg as unknown as ProductData));
            }

            if (raw.products) {
                this._products = raw.products.map((product) => new Product(product as unknown as ProductData));
            }

            if (raw.narratives) {
                this._narratives = raw.narratives.map((narrative) => new Narrative(narrative as unknown as NarrativeData));
            }

            this._carRental = raw.carRental ? new CarRentalEntitlement(raw.carRental as unknown as CarRentalEntitlementData) : null;
        }
    }

    systemId(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._systemId;
        } else {
            this._systemId = value ?? null;
        }
    }

    contact(value?: EntitlementContact): EntitlementContact | void {
        if (arguments.length === 0) {
            return this._contact;
        } else {
            this._contact = value ?? new EntitlementContact();
        }
    }

    policy(value?: Policy): Policy | any {
        if (arguments.length === 0) {
            return this._policy;
        } else {
            this._policy = value ?? new Policy();
        }
    }

    vehicle(value?: Vehicle): Vehicle | void {
        if (arguments.length === 0) {
            return this._vehicle;
        } else {
            this._vehicle = value ?? new Vehicle();
        }
    }

    packages(value?: Product[]): Product[] | void {
        if (arguments.length === 0) {
            return this._packages;
        } else {
            this._packages = value ?? [];
        }
    }

    products(value?: Product[]): Product[] | void {
        if (arguments.length === 0) {
            return this._products;
        } else {
            this._products = value ?? [];
        }
    }

    narratives(value?: Narrative[]): Narrative[] | void {
        if (arguments.length === 0) {
            return this._narratives;
        } else {
            this._narratives = value ?? [];
        }
    }

    fairPlay(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._fairPlay;
        } else {
            this._fairPlay = value ?? null;
        }
    }

    vipServiceCode(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._vipServiceCode;
        } else {
            this._vipServiceCode = value ?? null;
        }
    }

    benefits(value?: Benefit[]): Benefit[] | void {
        if (arguments.length === 0) {
            return this._benefits;
        } else {
            this._benefits = value ?? [];
        }
    }

    memberDetailsForPrime(value?: string[]): string[] | void {
        if (arguments.length === 0) {
            return this._memberDetailsForPrime;
        } else {
            this._memberDetailsForPrime = value ?? [];
        }
    }

    entitlementVariableData(value?: unknown[]): unknown[] | void {
        if (arguments.length === 0) {
            return this._entitlementVariableData;
        } else {
            this._entitlementVariableData = value ?? [];
        }
    }

    riskCode(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._riskCode;
        } else {
            this._riskCode = value ?? null;
        }
    }

    cover(value?: Cover): Cover | void {
        if (arguments.length === 0) {
            return this._cover;
        } else {
            this._cover = value ?? new Cover();
        }
    }

    extendedProductCategory(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._extendedProductCategory;
        } else {
            this._extendedProductCategory = value ?? false;
        }
    }

    additionalBrcVRNs(value?: unknown[]): unknown[] | void {
        if (arguments.length === 0) {
            return this._additionalBrcVRNs;
        } else {
            this._additionalBrcVRNs = value ?? [];
        }
    }

    payForUse(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._payForUse;
        } else {
            this._payForUse = value ?? false;
        }
    }

    carRental(value?: CarRentalEntitlement | null): CarRentalEntitlement | null | void {
        if (arguments.length === 0) {
            return this._carRental;
        } else {
            this._carRental = value ?? null;
        }
    }

    isNoServiceAccount(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._isNoServiceAccount;
        } else {
            this._isNoServiceAccount = value ?? null;
        }
    }

    correspondenceAddress(value?: CorrespondenceAddress | null): CorrespondenceAddress | null | void {
        if (arguments.length === 0) {
            return this._correspondenceAddress;
        } else {
            this._correspondenceAddress = value ?? null;
        }
    }

    isCorrespondenceAddressInvalid(): boolean | void {
        return this._correspondenceAddress == null ? true : this._correspondenceAddress.valid();
    }

    hasExpiredProductByName(productName: string): boolean {
        if (!productName) {
            return false;
        }
        return !!this._products.find((product) => product.name()?.toString().toLowerCase() === productName.toLowerCase() && product.expired());
    }

    hasBenefit(benefitCode: string): boolean {
        if (!benefitCode) {
            return false;
        }
        return !!this._benefits.find((benefit) => benefit.code() === benefitCode);
    }

    isVIP(): boolean {
        return this._vipServiceCode !== null || this._hasVIPPackage();
    }

    private _hasVIPPackage(): boolean {
        const VIPProducts = ['VIP', 'VIP - Royal Household', 'RDA'];
        return !!this._products.find((product) => product.name() && VIPProducts.includes(product.name() as string));
    }

    isStandby(): boolean {
        return !!this._products.find((product) => product.name()?.toString().includes('Standby'));
    }

    hasBrc(): boolean {
        return !!this._products.find((product) => product.benefitCode()?.toString().toUpperCase() === 'BRC');
    }

    isLocalDriver(): boolean {
        return !!this._products.find((product) => product.name()?.toString().includes('Local Driver'));
    }

    hasProduct(productCode: string): boolean {
        if (!productCode) {
            return false;
        }
        return !!this._products.find((product) => product.code() === productCode);
    }

    hasValidProductByName(productName: string): boolean {
        if (!productName) {
            return false;
        }
        return !!this._products.find((product) => product.name()?.toString().toLowerCase() === productName.toLowerCase() && !product.expired());
    }

    getProduct(productName: string): Product | null {
        if (this.hasValidProductByName(productName)) {
            return this._products.find((product) => product.name()?.toString().toLowerCase() === productName.toLowerCase() && !!product.benefitCode()) || null;
        }
        return null;
    }

    isHomeInsurance(): boolean {
        return this._systemId?.trim() === 'TIA_HOME_INS';
    }

    isEuropeanBreakdownCover(): boolean {
        return this._products.length ? this._products[0].name()?.toString().toUpperCase().includes('EBC') || false : false;
    }

    isActive(): boolean {
        const now = new Date();
        const product = this._products.find((entry) => entry.benefitCode() === 'B');

        if (!product) {
            return true;
        }

        if (product.expired()) {
            return false;
        }

        const productStartDate = product.claimFromDateTime() || product.startDate();
        const productEndDate = product.endDate();

        if (productStartDate && productEndDate) {
            return productStartDate < now && now < productEndDate;
        }

        const policyStartDate = this._policy.startDate();
        const policyEndDate = this._policy.endDate();
        if (policyStartDate && policyEndDate) {
            return policyStartDate < now && now < policyEndDate;
        }

        return false;
    }

    isDisabled(): boolean {
        const policy = this._policy;
        const endDate = policy.endDate();
        return policy && policy.memberStatus()?.toString().toLowerCase() === 'stopped' && endDate instanceof Date && endDate < new Date();
    }

    isCuvCovered(vrn: string): boolean {
        return this._products.some((product) => product.isCUV() && product.isVehicleCovered(vrn));
    }

    isBrcCovered(vrn: string): boolean {
        return this._products.some((product) => product.isBRC() && product.isVehicleCovered(vrn));
    }

    maxCallouts(): number {
        let maxCallouts: number | undefined;

        // Check if the customer group is Aurora
        if (this._policy.customerGroup().isAurora()) {
            return 1;
        }

        // Iterate through products and check usage
        this._products.forEach((product) => {
            const usages = product.usage() as Usage[];
            usages.forEach((usage) => {
                if (usage.unit() === Usage.UNIT_CLAIM && usage.type() === Usage.TYPE_USAGE) {
                    maxCallouts = Number(usage.use());
                }
            });
        });

        // Fallback to membership type if no usage data is found
        if (!maxCallouts) {
            const membershipType = this._policy.membershipType();
            maxCallouts =
                Entitlement.DEMAND_DEFLECTION.MAX_CALLOUTS_BY_MEMBERSHIP_TYPE[membershipType as keyof typeof Entitlement.DEMAND_DEFLECTION.MAX_CALLOUTS_BY_MEMBERSHIP_TYPE] ||
                Entitlement.DEFAULT_MAX_CALLOUTS;
        }

        // Return the calculated or default max callouts
        return maxCallouts || Entitlement.DEFAULT_MAX_CALLOUTS;
    }

    maxDistance(): number {
        let maxDistance: number | undefined;

        this._products.forEach((product) => {
            const usages = product.usage() as Usage[];
            usages.forEach((usage) => {
                if (usage.unit() === Usage.UNIT_CLAIM && usage.type() === Usage.TYPE_DISTANCE_MILES) {
                    maxDistance = Number(usage.use());
                }
            });
        });

        return maxDistance || Entitlement.DEFAULT_MAX_DISTANCE;
    }

    isLocalDriverTooFar(distanceAsCrowFlies: number): boolean {
        if (!this.isLocalDriver()) {
            return false;
        }
        const maxDistance = this.maxDistance();
        return distanceAsCrowFlies > maxDistance;
    }

    hasEuropeanCover(): boolean {
        return hasEuropeanCover(this);
    }

    isValidLocale(value: Country): boolean {
        return isValidLocale(value);
    }
    isSplit(value?: boolean | null): boolean | null | void {
        if (arguments.length === 0) {
            return this._isSplit;
        } else {
            this._isSplit = value ?? null;
        }
    }
    daysCovered(value?: number | null): number | null | void {
        if (arguments.length === 0) {
            return this._daysCovered;
        } else {
            this._daysCovered = value ?? null;
        }
    }
    // setting default value to -1, this indicated no limit
    tripCountLimit(value?: number | null): number | null | void {
        if (arguments.length === 0) {
            return this._tripCountLimit;
        } else {
            this._tripCountLimit = value ?? null;
        }
    }
    toJSON() {
        return {
            systemId: this._systemId,
            contact: this._contact.toJSON(),
            policy: this._policy.toJSON(),
            vehicle: this._vehicle.toJSON(),
            cover: this._cover.toJSON(),
            correspondenceAddress: this._correspondenceAddress?.toJSON() || null,
            packages: this._packages.map((pkg) => pkg.toJSON()),
            products: this._products.map((product) => product.toJSON()),
            narratives: this._narratives.map((narrative) => narrative.toJSON()),
            fairPlay: this._fairPlay,
            vipServiceCode: this._vipServiceCode,
            benefits: this._benefits.map((benefit) => benefit.toJSON()),
            entitlementVariableData: this._entitlementVariableData,
            memberDetailsForPrime: this._memberDetailsForPrime,
            riskCode: this._riskCode,
            extendedProductCategory: this._extendedProductCategory,
            additionalBrcVRNs: this._additionalBrcVRNs,
            payForUse: this._payForUse,
            carRental: this._carRental,
            isNoServiceAccount: this._isNoServiceAccount,
            isSplit: this._isSplit,
            daysCovered: this._daysCovered,
            tripCountLimit: this._tripCountLimit
        };
    }
}

export function hasEuropeanCover(entitlement: Entitlement): boolean {
    if (!entitlement) {
        return false;
    }

    const benefits = entitlement.benefits();
    if (!benefits) {
        return false;
    }

    return benefits.some((benefit) => ['ET', 'FE'].includes(benefit.code() as string));
}

export function isValidLocale(locale: Country): boolean {
    return Object.values(validDeploymentLocales).includes(locale);
}
