export interface CoverData {
    type: string | null;
    startDate: string | Date | null;
    endDate: string | Date | null;
    package: string | null;
    noService: boolean;
}

export class Cover {
    private _type: string | null = null;
    private _startDate: Date | null = null;
    private _endDate: Date | null = null;
    private _package: string | null = null;
    private _noService = false;

    constructor(raw?: Partial<CoverData>) {
        if (raw) {
            this._type = raw.type ?? this._type;
            this._startDate = raw.startDate ? new Date(raw.startDate) : this._startDate;
            this._endDate = raw.endDate ? new Date(raw.endDate) : this._endDate;
            this._package = raw.package ?? this._package;
            this._noService = raw.noService ?? this._noService;
        }
    }

    type(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._type;
        } else {
            this._type = value ?? null;
        }
    }

    startDate(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._startDate;
        } else {
            this._startDate = value ?? null;
        }
    }

    endDate(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._endDate;
        } else {
            this._endDate = value ?? null;
        }
    }

    package(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._package;
        } else {
            this._package = value ?? null;
        }
    }

    noService(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._noService;
        } else {
            this._noService = value ?? false;
        }
    }

    isExpired(): boolean {
        return this._endDate !== null && this._endDate < new Date();
    }

    toJSON() {
        return {
            type: this._type,
            startDate: this._startDate,
            endDate: this._endDate,
            package: this._package,
            noService: this._noService
        };
    }
}
