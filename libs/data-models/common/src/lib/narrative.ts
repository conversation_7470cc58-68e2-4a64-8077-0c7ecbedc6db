export interface NarrativeData {
    text?: string | null;
    date?: string | Date | null;
    type?: string | null;
}

export class Narrative {
    private _text: string | null = null;
    private _date: Date | null = null;
    private _type: string | null = null;

    constructor(raw?: Partial<NarrativeData>) {
        if (raw) {
            this._text = raw.text ?? this._text;
            this._date = raw.date ? new Date(raw.date) : this._date;
            this._type = raw.type ?? this._type;
        }
    }

    text(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._text;
        } else {
            this._text = value ?? null;
        }
    }

    date(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._date;
        } else {
            this._date = value ?? null;
        }
    }

    type(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._type;
        } else {
            this._type = value ?? null;
        }
    }

    toJSON() {
        return {
            text: this._text,
            date: this._date,
            type: this._type
        };
    }
}
