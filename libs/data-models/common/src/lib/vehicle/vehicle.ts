import { ExperianDetails, ExperianDetailsData } from './experian-details';
import { RoadworthyData, Roadworthy } from './roadworthy';
import { TrailerDetailsData, TrailerDetails } from './trailer-details';
import { VehicleModelData, VehicleModel } from './vehicle-model';
import { VehicleOwnerDetailsData, VehicleOwnerDetails } from './vehicle-owner-details';

export interface VehicleData {
    registration?: string | null;
    modelId?: number;
    makeId?: number;
    typeId?: number;
    colour?: string | null;
    model?: VehicleModelData;
    experianDetails?: ExperianDetailsData;
    hasTrailer?: boolean;
    hasOwner?: boolean;
    roadworthy?: RoadworthyData;
    vehicleOwnerDetails?: VehicleOwnerDetailsData;
    trailerDetails?: TrailerDetailsData;
    twinnedWheels?: string | null;
}

export class Vehicle {
    private _registration: string | null = null;
    private _modelId = -1;
    private _makeId = -1;
    private _typeId = -1;
    private _colour: string | null = null;
    private _model: VehicleModel = new VehicleModel();
    private _experianDetails: ExperianDetails = new ExperianDetails();
    private _hasTrailer = false;
    private _hasOwner = false;
    private _trailerDetails: TrailerDetails = new TrailerDetails();
    private _vehicleOwnerDetails: VehicleOwnerDetails = new VehicleOwnerDetails();
    private _roadworthy: Roadworthy = new Roadworthy();
    private _twinnedWheels: string | null = null;

    constructor(rawData?: Partial<VehicleData>) {
        if (rawData) {
            this._registration = rawData.registration ?? null;
            this._modelId = rawData.modelId ?? -1;
            this._makeId = rawData.makeId ?? -1;
            this._typeId = rawData.typeId ?? -1;
            this._colour = rawData.colour ?? null;
            this._hasTrailer = rawData.hasTrailer ?? false;
            this._hasOwner = rawData.hasOwner ?? false;
            this._model = rawData.model ? new VehicleModel(rawData.model as unknown as VehicleModelData) : this._model;
            this._experianDetails = rawData.experianDetails ? new ExperianDetails(rawData.experianDetails as unknown as ExperianDetailsData) : this._experianDetails;
            this._trailerDetails = rawData.trailerDetails ? new TrailerDetails(rawData.trailerDetails as unknown as TrailerDetailsData) : this._trailerDetails;
            this._vehicleOwnerDetails = rawData.vehicleOwnerDetails ? new VehicleOwnerDetails(rawData.vehicleOwnerDetails as unknown as VehicleOwnerDetailsData) : this._vehicleOwnerDetails;
            this._roadworthy = rawData.roadworthy ? new Roadworthy(rawData.roadworthy as unknown as RoadworthyData) : this._roadworthy;
            this._twinnedWheels = rawData.twinnedWheels ?? null;
        }
    }

    registration(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._registration;
        } else {
            this._registration = value ?? null;
        }
    }

    modelId(value?: number): number | void {
        if (arguments.length === 0) {
            return this._modelId;
        } else {
            this._modelId = value ?? -1;
        }
    }

    makeId(value?: number): number | void {
        if (arguments.length === 0) {
            return this._makeId;
        } else {
            this._makeId = value ?? -1;
        }
    }

    typeId(value?: number): number | void {
        if (arguments.length === 0) {
            return this._typeId;
        } else {
            this._typeId = value ?? -1;
        }
    }

    colour(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._colour;
        } else {
            this._colour = value ?? null;
        }
    }

    model(value?: VehicleModel): VehicleModel | void {
        if (arguments.length === 0) {
            return this._model;
        } else {
            this._model = value ?? new VehicleModel();
        }
    }

    experianDetails(value?: ExperianDetails): ExperianDetails | void {
        if (arguments.length === 0) {
            return this._experianDetails;
        } else {
            this._experianDetails = value ?? new ExperianDetails();
        }
    }

    hasTrailer(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._hasTrailer;
        } else {
            this._hasTrailer = value ?? false;
        }
    }

    hasOwner(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._hasOwner;
        } else {
            this._hasOwner = value ?? false;
        }
    }

    roadworthy(value?: Roadworthy): Roadworthy | void {
        if (arguments.length === 0) {
            return this._roadworthy;
        } else {
            this._roadworthy = value ?? new Roadworthy();
        }
    }

    vehicleOwnerDetails(value?: VehicleOwnerDetails): VehicleOwnerDetails | void {
        if (arguments.length === 0) {
            return this._vehicleOwnerDetails;
        } else {
            this._vehicleOwnerDetails = value ?? new VehicleOwnerDetails();
        }
    }

    trailerDetails(value?: TrailerDetails): TrailerDetails | void {
        if (arguments.length === 0) {
            return this._trailerDetails;
        } else {
            this._trailerDetails = value ?? new TrailerDetails();
        }
    }

    twinnedWheels(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._twinnedWheels;
        } else {
            this._twinnedWheels = value ?? null;
        }
    }

    isValidVehicle(): boolean {
        return !!this._registration && this._typeId > -1 && this._makeId > -1 && this._modelId > -1;
    }

    validVehicle(): boolean {
        return this._typeId > -1 && this._makeId > -1 && this._modelId > -1;
    }

    reset(): void {
        this._registration = null;
        this._modelId = -1;
        this._makeId = -1;
        this._typeId = -1;
        this._colour = null;
        this._model = new VehicleModel();
        this._experianDetails = new ExperianDetails();
        this._hasTrailer = false;
        this._hasOwner = false;
        this._trailerDetails = new TrailerDetails();
        this._vehicleOwnerDetails = new VehicleOwnerDetails();
        this._roadworthy = new Roadworthy();
        this._twinnedWheels = null;
    }

    toJSON() {
        return {
            registration: this._registration,
            modelId: this._modelId,
            makeId: this._makeId,
            typeId: this._typeId,
            colour: this._colour,
            model: this._model,
            experianDetails: this._experianDetails,
            hasTrailer: this._hasTrailer,
            hasOwner: this._hasOwner,
            roadworthy: this._roadworthy,
            vehicleOwnerDetails: this._vehicleOwnerDetails,
            trailerDetails: this._trailerDetails,
            twinnedWheels: this._twinnedWheels
        };
    }
}
