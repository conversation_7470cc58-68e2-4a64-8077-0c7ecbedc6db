export interface VehicleOwnerDetailsData {
    title?: string | null;
    firstName?: string | null;
    surName?: string | null;
    contactNumber?: string | null;
    email?: string | null;
    address?: string | null;
    postCode?: string | null;
}

export class VehicleOwnerDetails {
    private _title: string | null = null;
    private _firstName: string | null = null;
    private _surName: string | null = null;
    private _contactNumber: string | null = null;
    private _email: string | null = null;
    private _address: string | null = null;
    private _postCode: string | null = null;

    constructor(raw?: Partial<VehicleOwnerDetailsData>) {
        if (raw) {
            this._title = raw.title ?? null;
            this._firstName = raw.firstName ?? null;
            this._surName = raw.surName ?? null;
            this._contactNumber = raw.contactNumber ?? null;
            this._email = raw.email ?? null;
            this._address = raw.address ?? null;
            this._postCode = raw.postCode ?? null;
        }
    }

    title(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._title;
        }
        this._title = value ?? null;
    }

    firstName(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._firstName;
        }
        this._firstName = value ?? null;
    }

    surName(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._surName;
        }
        this._surName = value ?? null;
    }

    contactNumber(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._contactNumber;
        }
        this._contactNumber = value ?? null;
    }

    email(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._email;
        }
        this._email = value ?? null;
    }

    address(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._address;
        }
        this._address = value ?? null;
    }

    postCode(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._postCode;
        }
        this._postCode = value ?? null;
    }

    toJSON() {
        return {
            title: this._title,
            firstName: this._firstName,
            surName: this._surName,
            contactNumber: this._contactNumber,
            email: this._email,
            address: this._address,
            postCode: this._postCode
        };
    }
}
