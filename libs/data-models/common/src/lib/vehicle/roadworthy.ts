export interface RoadworthyData {
    registrationNumber?: string | null;
    yearOfManufacture?: null | number;
    monthOfFirstRegistration?: Date | string | null;
    motExpiryDate?: Date | string | null;
    motStatus?: string | null;
    taxDueDate?: Date | null;
    taxStatus?: string | null;
}

export class Roadworthy {
    private _registrationNumber: string | null = null;
    private _yearOfManufacture: number | null = null;
    private _monthOfFirstRegistration: Date | null = null;
    private _motExpiryDate: Date | null = null;
    private _motStatus: string | null = null;
    private _taxDueDate: Date | null = null;
    private _taxStatus: string | null = null;

    static GRACELENGTH = 0;

    constructor(raw?: Partial<RoadworthyData>) {
        if (raw) {
            this._registrationNumber = raw.registrationNumber ?? null;
            this._yearOfManufacture = raw.yearOfManufacture ?? null;
            this._monthOfFirstRegistration = raw.monthOfFirstRegistration ? new Date(raw.monthOfFirstRegistration) : null;
            this._motExpiryDate = raw.motExpiryDate ? new Date(raw.motExpiryDate) : null;
            this._motStatus = raw.motStatus ?? null;
            this._taxDueDate = raw.taxDueDate ? new Date(raw.taxDueDate) : null;
            this._taxStatus = raw.taxStatus ?? null;
        }
    }

    registrationNumber(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._registrationNumber;
        } else {
            this._registrationNumber = value ?? null;
        }
    }

    yearOfManufacture(value?: number | null): number | null | void {
        if (arguments.length === 0) {
            return this._yearOfManufacture;
        } else {
            this._yearOfManufacture = value ?? null;
        }
    }

    monthOfFirstRegistration(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._monthOfFirstRegistration;
        } else {
            this._monthOfFirstRegistration = value ?? null;
        }
    }

    motExpiryDate(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._motExpiryDate;
        } else {
            this._motExpiryDate = value ?? null;
        }
    }

    motStatus(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._motStatus;
        } else {
            this._motStatus = value ?? null;
        }
    }

    taxDueDate(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._taxDueDate;
        } else {
            this._taxDueDate = value ?? null;
        }
    }

    taxStatus(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._taxStatus;
        } else {
            this._taxStatus = value ?? null;
        }
    }

    isClassicVehicle(): boolean {
        if (!this._yearOfManufacture) {
            if (!this._monthOfFirstRegistration) {
                return false;
            } else {
                return this._monthOfFirstRegistration.getFullYear() <= new Date().getFullYear() - 40;
            }
        } else {
            return this._yearOfManufacture <= new Date().getFullYear() - 40;
        }
    }

    isNewVehicle(): boolean {
        if (!this._monthOfFirstRegistration) {
            return false;
        } else {
            return Math.ceil(this._calculateDays(this._monthOfFirstRegistration) / 365) <= 3;
        }
    }

    isMotValid(): boolean {
        if (['Valid', 'Exempt', 'No details held by DVLA'].includes(this._motStatus || '') || this.isClassicVehicle() || this.isNewVehicle()) {
            return true;
        } else {
            return false;
        }
    }

    isTaxValid(): boolean {
        if (this._taxStatus === 'Taxed') {
            return true;
        } else {
            return false;
        }
    }

    isSorn(): boolean {
        if (this._taxStatus === 'SORN') {
            return true;
        } else {
            return false;
        }
    }

    isMOTWithinGrace(): boolean {
        if (this._motExpiryDate) {
            return this._calculateDays(this._motExpiryDate) <= Roadworthy.GRACELENGTH;
        } else {
            return false;
        }
    }

    isTaxWithinGrace(): boolean {
        if (this._taxDueDate) {
            return this._calculateExactDays(this._taxDueDate) <= Roadworthy.GRACELENGTH;
        } else {
            return false;
        }
    }

    reset(): void {
        this._registrationNumber = null;
        this._yearOfManufacture = null;
        this._monthOfFirstRegistration = null;
        this._motExpiryDate = null;
        this._motStatus = null;
        this._taxDueDate = null;
        this._taxStatus = null;
    }

    toJSON() {
        return {
            registrationNumber: this._registrationNumber,
            yearOfManufacture: this._yearOfManufacture,
            monthOfFirstRegistration: this._monthOfFirstRegistration,
            motExpiryDate: this._motExpiryDate,
            motStatus: this._motStatus,
            taxDueDate: this._taxDueDate,
            taxStatus: this._taxStatus
        };
    }

    private _calculateDays(date: Date): number {
        date.setHours(0, 0, 0, 0);
        const present = new Date();
        present.setHours(0, 0, 0, 0);
        return Math.floor((present.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    }

    private _calculateExactDays(date: Date): number {
        const present = new Date();
        return (present.getTime() - date.getTime()) / (1000 * 60 * 60 * 24);
    }
}
