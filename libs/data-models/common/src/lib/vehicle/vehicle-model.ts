export interface VehicleModelData {
    id?: number;
    name?: string | null;
    weight?: number;
    typeId?: number;
    makeId?: number;
    frontLiftWeight?: number;
    rearLiftWeight?: number;
    overPrsnWeight?: number;
    fuel?: string | null;
    transmission?: string | null;
}

export class VehicleModel {
    private _id = -1;
    private _name: string | null = null;
    private _weight = -1;
    private _typeId = -1;
    private _makeId = -1;
    private _frontLiftWeight = -1;
    private _rearLiftWeight = -1;
    private _overPrsnWeight = -1;
    private _fuel: string | null = null;
    private _transmission: string | null = null;

    constructor(raw?: Partial<VehicleModelData>) {
        if (raw) {
            this._id = raw.id ?? this._id;
            this._name = raw.name ?? this._name;
            this._weight = raw.weight ?? this._weight;
            this._typeId = raw.typeId ?? this._typeId;
            this._makeId = raw.makeId ?? this._makeId;
            this._frontLiftWeight = raw.frontLiftWeight ?? this._frontLiftWeight;
            this._rearLiftWeight = raw.rearLiftWeight ?? this._rearLiftWeight;
            this._overPrsnWeight = raw.overPrsnWeight ?? this._overPrsnWeight;
            this._fuel = raw.fuel ?? this._fuel;
            this._transmission = raw.transmission ?? this._transmission;
        }
    }

    id(value?: number): number | void {
        if (arguments.length === 0) {
            return this._id;
        } else {
            this._id = value ?? -1;
        }
    }

    name(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._name;
        } else {
            this._name = value ?? null;
        }
    }

    weight(value?: number): number | void {
        if (arguments.length === 0) {
            return this._weight;
        } else {
            this._weight = value ?? -1;
        }
    }

    typeId(value?: number): number | void {
        if (arguments.length === 0) {
            return this._typeId;
        } else {
            this._typeId = value ?? -1;
        }
    }

    makeId(value?: number): number | void {
        if (arguments.length === 0) {
            return this._makeId;
        } else {
            this._makeId = value ?? -1;
        }
    }

    frontLiftWeight(value?: number): number | void {
        if (arguments.length === 0) {
            return this._frontLiftWeight;
        } else {
            this._frontLiftWeight = value ?? -1;
        }
    }

    rearLiftWeight(value?: number): number | void {
        if (arguments.length === 0) {
            return this._rearLiftWeight;
        } else {
            this._rearLiftWeight = value ?? -1;
        }
    }

    overPrsnWeight(value?: number): number | void {
        if (arguments.length === 0) {
            return this._overPrsnWeight;
        } else {
            this._overPrsnWeight = value ?? -1;
        }
    }

    fuel(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._fuel;
        } else {
            this._fuel = value ?? null;
        }
    }

    transmission(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._transmission;
        } else {
            this._transmission = value ?? null;
        }
    }

    toJSON() {
        return {
            id: this._id,
            name: this._name,
            weight: this._weight,
            typeId: this._typeId,
            makeId: this._makeId,
            frontLiftWeight: this._frontLiftWeight,
            rearLiftWeight: this._rearLiftWeight,
            overPrsnWeight: this._overPrsnWeight,
            fuel: this._fuel,
            transmission: this._transmission
        };
    }
}
