export interface ExperianDetailsData {
    make?: string | null;
    model?: string | null;
    colour?: string | null;
    transmission?: string | null;
    grossWeight?: string | null;
    ukDateFirstRegistered?: string | null;
    dateFirstRegistered?: string | null;
    bodyStyle?: string | null;
    modelVariant?: string | null;
    smmtRange?: string | null;
    engineNo?: string | null;
    co2Emissions?: string | null;
    rbCodes?: string | null;
    mvrisCode?: string | null;
    vin?: string | null;
    vinConfirmFlag?: string | null;
    engineSize?: string | null;
    yrOfManufacture?: string | null;
    fuel?: string | null;
    wheelPlan?: string | null;
    imported?: string | null;
    series?: string | null;
    abiBrokernetCode?: string | null;
    glassModelId?: string | null;
    gears?: string | null;
    importNonEu?: string | null;
    kerbWeight?: string | null;
    length?: string | null;
    seatNumber?: string | null;
    wrongDetailsFlag?: boolean;
    flag?: boolean;
    makeModel?: string | null;
    doorPlan?: string | null;
    doorPlanCode?: string | null;
    width?: string | null;
    height?: string | null;
    driveType?: string | null;
}

export class ExperianDetails {
    private _make: string | null = null;
    private _model: string | null = null;
    private _colour: string | null = null;
    private _transmission: string | null = null;
    private _grossWeight: string | null = null;
    private _ukDateFirstRegistered: string | null = null;
    private _dateFirstRegistered: string | null = null;
    private _bodyStyle: string | null = null;
    private _modelVariant: string | null = null;
    private _smmtRange: string | null = null;
    private _engineNo: string | null = null;
    private _co2Emissions: string | null = null;
    private _rbCodes: string | null = null;
    private _mvrisCode: string | null = null;
    private _vin: string | null = null;
    private _vinConfirmFlag: string | null = null;
    private _engineSize: string | null = null;
    private _yrOfManufacture: string | null = null;
    private _fuel: string | null = null;
    private _wheelPlan: string | null = null;
    private _imported: string | null = null;
    private _series: string | null = null;
    private _abiBrokernetCode: string | null = null;
    private _glassModelId: string | null = null;
    private _gears: string | null = null;
    private _importNonEu: string | null = null;
    private _kerbWeight: string | null = null;
    private _length: string | null = null;
    private _seatNumber: string | null = null;
    private _wrongDetailsFlag = false;
    private _flag = true;
    private _makeModel: string | null = null;
    private _doorPlan: string | null = null;
    private _doorPlanCode: string | null = null;
    private _width: string | null = null;
    private _height: string | null = null;
    private _driveType: string | null = null;

    constructor(raw?: Partial<ExperianDetailsData>) {
        if (raw) {
            this._make = raw.make ?? this._make;
            this._model = raw.model ?? this._model;
            this._colour = raw.colour ?? this._colour;
            this._transmission = raw.transmission ?? this._transmission;
            this._grossWeight = raw.grossWeight ?? this._grossWeight;
            this._ukDateFirstRegistered = raw.ukDateFirstRegistered ?? this._ukDateFirstRegistered;
            this._dateFirstRegistered = raw.dateFirstRegistered ?? this._dateFirstRegistered;
            this._bodyStyle = raw.bodyStyle ?? this._bodyStyle;
            this._modelVariant = raw.modelVariant ?? this._modelVariant;
            this._smmtRange = raw.smmtRange ?? this._smmtRange;
            this._engineNo = raw.engineNo ?? this._engineNo;
            this._co2Emissions = raw.co2Emissions ?? this._co2Emissions;
            this._rbCodes = raw.rbCodes ?? this._rbCodes;
            this._mvrisCode = raw.mvrisCode ?? this._mvrisCode;
            this._vin = raw.vin ?? this._vin;
            this._vinConfirmFlag = raw.vinConfirmFlag ?? this._vinConfirmFlag;
            this._engineSize = raw.engineSize ?? this._engineSize;
            this._yrOfManufacture = raw.yrOfManufacture ?? this._yrOfManufacture;
            this._fuel = raw.fuel ?? this._fuel;
            this._wheelPlan = raw.wheelPlan ?? this._wheelPlan;
            this._imported = raw.imported ?? this._imported;
            this._series = raw.series ?? this._series;
            this._abiBrokernetCode = raw.abiBrokernetCode ?? this._abiBrokernetCode;
            this._glassModelId = raw.glassModelId ?? this._glassModelId;
            this._gears = raw.gears ?? this._gears;
            this._importNonEu = raw.importNonEu ?? this._importNonEu;
            this._kerbWeight = raw.kerbWeight ?? this._kerbWeight;
            this._length = raw.length ?? this._length;
            this._seatNumber = raw.seatNumber ?? this._seatNumber;
            this._wrongDetailsFlag = raw.wrongDetailsFlag ?? this._wrongDetailsFlag;
            this._flag = raw.flag ?? this._flag;
            this._makeModel = raw.makeModel ?? this._makeModel;
            this._doorPlan = raw.doorPlan ?? this._doorPlan;
            this._doorPlanCode = raw.doorPlanCode ?? this._doorPlanCode;
            this._width = raw?.width ?? this._width;
            this._height = raw?.height ?? this._height;
            this._driveType = raw?.driveType ?? this._driveType;
        }
    }

    make(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._make;
        } else {
            this._make = value ?? null;
        }
    }

    model(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._model;
        } else {
            this._model = value ?? null;
        }
    }

    colour(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._colour;
        } else {
            this._colour = value ?? null;
        }
    }

    transmission(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._transmission;
        } else {
            this._transmission = value ?? null;
        }
    }

    grossWeight(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._grossWeight;
        } else {
            this._grossWeight = value ?? null;
        }
    }

    ukDateFirstRegistered(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._ukDateFirstRegistered;
        } else {
            this._ukDateFirstRegistered = value ?? null;
        }
    }

    dateFirstRegistered(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._dateFirstRegistered;
        } else {
            this._dateFirstRegistered = value ?? null;
        }
    }

    bodyStyle(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._bodyStyle;
        } else {
            this._bodyStyle = value ?? null;
        }
    }

    modelVariant(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._modelVariant;
        } else {
            this._modelVariant = value ?? null;
        }
    }

    smmtRange(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._smmtRange;
        } else {
            this._smmtRange = value ?? null;
        }
    }

    engineNo(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._engineNo;
        } else {
            this._engineNo = value ?? null;
        }
    }

    co2Emissions(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._co2Emissions;
        } else {
            this._co2Emissions = value ?? null;
        }
    }

    rbCodes(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._rbCodes;
        } else {
            this._rbCodes = value ?? null;
        }
    }

    mvrisCode(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._mvrisCode;
        } else {
            this._mvrisCode = value ?? null;
        }
    }

    vin(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._vin;
        } else {
            this._vin = value ?? null;
        }
    }

    vinConfirmFlag(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._vinConfirmFlag;
        } else {
            this._vinConfirmFlag = value ?? null;
        }
    }

    engineSize(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._engineSize;
        } else {
            this._engineSize = value ?? null;
        }
    }

    yrOfManufacture(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._yrOfManufacture;
        } else {
            this._yrOfManufacture = value ?? null;
        }
    }

    fuel(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._fuel;
        } else {
            this._fuel = value ?? null;
        }
    }

    wheelPlan(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._wheelPlan;
        } else {
            this._wheelPlan = value ?? null;
        }
    }

    imported(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._imported;
        } else {
            this._imported = value ?? null;
        }
    }

    series(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._series;
        } else {
            this._series = value ?? null;
        }
    }

    abiBrokernetCode(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._abiBrokernetCode;
        } else {
            this._abiBrokernetCode = value ?? null;
        }
    }

    glassModelId(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._glassModelId;
        } else {
            this._glassModelId = value ?? null;
        }
    }

    gears(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._gears;
        } else {
            this._gears = value ?? null;
        }
    }

    importNonEu(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._importNonEu;
        } else {
            this._importNonEu = value ?? null;
        }
    }

    kerbWeight(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._kerbWeight;
        } else {
            this._kerbWeight = value ?? null;
        }
    }

    length(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._length;
        } else {
            this._length = value ?? null;
        }
    }

    seatNumber(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._seatNumber;
        } else {
            this._seatNumber = value ?? null;
        }
    }

    wrongDetailsFlag(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._wrongDetailsFlag;
        } else {
            this._wrongDetailsFlag = value ?? false;
        }
    }

    flag(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._flag;
        } else {
            this._flag = value ?? true;
        }
    }

    makeModel(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._makeModel;
        } else {
            this._makeModel = value ?? null;
        }
    }

    doorPlan(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._doorPlan;
        } else {
            this._doorPlan = value ?? null;
        }
    }

    doorPlanCode(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._doorPlanCode;
        } else {
            this._doorPlanCode = value ?? null;
        }
    }

    width(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._width;
        } else {
            this._width = value ?? null;
        }
    }

    height(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._height;
        } else {
            this._height = value ?? null;
        }
    }

    driveType(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._driveType;
        } else {
            this._driveType = value ?? null;
        }
    }

    isValidDetails(): boolean {
        return !!this._make && !!this._model && !!this._vin;
    }

    reset(): void {
        this._make = null;
        this._model = null;
        this._colour = null;
        this._transmission = null;
        this._grossWeight = null;
        this._ukDateFirstRegistered = null;
        this._dateFirstRegistered = null;
        this._bodyStyle = null;
        this._modelVariant = null;
        this._smmtRange = null;
        this._engineNo = null;
        this._co2Emissions = null;
        this._rbCodes = null;
        this._mvrisCode = null;
        this._vin = null;
        this._vinConfirmFlag = null;
        this._engineSize = null;
        this._yrOfManufacture = null;
        this._fuel = null;
        this._wheelPlan = null;
        this._imported = null;
        this._series = null;
        this._abiBrokernetCode = null;
        this._glassModelId = null;
        this._gears = null;
        this._importNonEu = null;
        this._kerbWeight = null;
        this._length = null;
        this._seatNumber = null;
        this._wrongDetailsFlag = false;
        this._flag = true;
        this._makeModel = null;
        this._doorPlan = null;
        this._doorPlanCode = null;
        this._width = null;
        this._height = null;
        this._driveType = null;
    }

    toJSON() {
        return {
            make: this._make,
            model: this._model,
            colour: this._colour,
            transmission: this._transmission,
            grossWeight: this._grossWeight,
            ukDateFirstRegistered: this._ukDateFirstRegistered,
            dateFirstRegistered: this._dateFirstRegistered,
            bodyStyle: this._bodyStyle,
            modelVariant: this._modelVariant,
            smmtRange: this._smmtRange,
            engineNo: this._engineNo,
            co2Emissions: this._co2Emissions,
            rbCodes: this._rbCodes,
            mvrisCode: this._mvrisCode,
            vin: this._vin,
            vinConfirmFlag: this._vinConfirmFlag,
            engineSize: this._engineSize,
            yrOfManufacture: this._yrOfManufacture,
            fuel: this._fuel,
            wheelPlan: this._wheelPlan,
            imported: this._imported,
            series: this._series,
            abiBrokernetCode: this._abiBrokernetCode,
            glassModelId: this._glassModelId,
            gears: this._gears,
            importNonEu: this._importNonEu,
            kerbWeight: this._kerbWeight,
            length: this._length,
            seatNumber: this._seatNumber,
            wrongDetailsFlag: this._wrongDetailsFlag,
            flag: this._flag,
            makeModel: this._makeModel,
            doorPlan: this._doorPlan,
            doorPlanCode: this._doorPlanCode,
            width: this._width,
            height: this._height,
            driveType: this._driveType
        };
    }
}
