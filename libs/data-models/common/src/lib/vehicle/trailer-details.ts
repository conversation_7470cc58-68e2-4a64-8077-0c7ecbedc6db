export interface TrailerDetailsData {
    make?: string | null;
    model?: string | null;
    colour?: string | null;
    type?: string | null;
    axles?: string | null;
    twinned?: string | null;
    hookUp?: string | null;
    curSymbol?: string | null;
    value?: string | null;
    abandonedFlag?: string | null;
    notes?: string | null;
    diagnosis?: string | null;
    fault?: string | null;
    yearConstructed?: string | null;
    weight?: string | null;
    length?: string | null;
    height?: string | null;
    width?: string | null;
}

export class TrailerDetails {
    private _make: string | null = null;
    private _model: string | null = null;
    private _colour: string | null = null;
    private _type: string | null = null;
    private _axles: string | null = null;
    private _twinned: string | null = null;
    private _hookUp: string | null = null;
    private _curSymbol: string | null = null;
    private _value: string | null = null;
    private _abandonedFlag: string | null = null;
    private _notes: string | null = null;
    private _diagnosis: string | null = null;
    private _fault: string | null = null;
    private _yearConstructed: string | null = null;
    private _weight: string | null = null;
    private _length: string | null = null;
    private _height: string | null = null;
    private _width: string | null = null;

    constructor(raw?: Partial<TrailerDetailsData>) {
        if (raw) {
            this._make = raw.make ?? null;
            this._model = raw.model ?? null;
            this._colour = raw.colour ?? null;
            this._type = raw.type ?? null;
            this._axles = raw.axles ?? null;
            this._twinned = raw.twinned ?? null;
            this._hookUp = raw.hookUp ?? null;
            this._curSymbol = raw.curSymbol ?? null;
            this._value = raw.value ?? null;
            this._abandonedFlag = raw.abandonedFlag ?? null;
            this._notes = raw.notes ?? null;
            this._diagnosis = raw.diagnosis ?? null;
            this._fault = raw.fault ?? null;
            this._yearConstructed = raw.yearConstructed ?? null;
            this._weight = raw.weight ?? null;
            this._length = raw.length ?? null;
            this._height = raw.height ?? null;
            this._width = raw.width ?? null;
        }
    }

    make(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._make;
        }
        this._make = value ?? null;
    }

    model(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._model;
        }
        this._model = value ?? null;
    }

    colour(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._colour;
        }
        this._colour = value ?? null;
    }

    type(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._type;
        }
        this._type = value ?? null;
    }

    axles(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._axles;
        }
        this._axles = value ?? null;
    }

    twinned(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._twinned;
        }
        this._twinned = value ?? null;
    }

    hookUp(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._hookUp;
        }
        this._hookUp = value ?? null;
    }

    curSymbol(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._curSymbol;
        }
        this._curSymbol = value ?? null;
    }

    value(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._value;
        }
        this._value = value ?? null;
    }

    abandonedFlag(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._abandonedFlag;
        }
        this._abandonedFlag = value ?? null;
    }

    notes(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._notes;
        }
        this._notes = value ?? null;
    }

    diagnosis(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._diagnosis;
        }
        this._diagnosis = value ?? null;
    }

    fault(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._fault;
        }
        this._fault = value ?? null;
    }

    yearConstructed(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._yearConstructed;
        }
        this._yearConstructed = value ?? null;
    }

    weight(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._weight;
        }
        this._weight = value ?? null;
    }

    length(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._length;
        }
        this._length = value ?? null;
    }

    height(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._height;
        }
        this._height = value ?? null;
    }

    width(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._width;
        }
        this._width = value ?? null;
    }

    toJSON() {
        return {
            make: this._make,
            model: this._model,
            colour: this._colour,
            type: this._type,
            axles: this._axles,
            twinned: this._twinned,
            hookUp: this._hookUp,
            curSymbol: this._curSymbol,
            value: this._value,
            abandonedFlag: this._abandonedFlag,
            notes: this._notes,
            diagnosis: this._diagnosis,
            fault: this._fault,
            yearConstructed: this._yearConstructed,
            weight: this._weight,
            length: this._length,
            height: this._height,
            width: this._width
        };
    }
}
