import { Address } from '../aux';

export interface BatteryTest {
    patrolId: number;
    taskId: number;
    vrn: string;
    status: BatteryTestStatus;
    results?: RawBatteryResults;
    imageUrl?: string;
    created?: Date | string;
    completed?: Date | string;
}

export enum OutdoorEvents {
    CREATE_GARAGE_BOOKING = 'create/booking/garage',
    COMPLETE_BREAKDOWN_TASK = 'complete/task/breakdown',
    COMPLETE_SMR = 'complete/smr',
    COMPLETE_B2Q = 'b2q/complete',
    START_B2Q = 'b2q/start',
    MI_B2Q = 'MI_B2Q',
    MI_SMR = 'MI_SMR'
}

export interface OutdoorTaskPayload {
    taskId: number;
    patrolId: number;
    vrn: string;
}

export enum BatteryTestStatus {
    BT_START = 'BT-START',
    BT_COMPLETE_EVA = 'BT-COMPLETE-EVA',
    BT_TESTS_NA = 'BT-TESTS-NA',
    BT_TESTS_RECVD = 'BT-TESTS-RECVD',
    BT_TESTS_IMAGE_GEN = 'BT-TESTS-IMAGE-GEN',
    BT_TESTS_COMP_MI = 'BT-TESTS-COMP-MI'
}

export interface RawBatteryResultSummary {
    // Date and time of test
    dateTime: Date | string;
    // State of charge in per cent as number e.x 94
    stateOfCharge: number;
    // State of health in per cent as number e.x 94
    stateOfHealth: number;
    // Charging system pass e.x Unknown, Pass or Fail
    chargingSystemPassOutcome: string;
    // Cranking health in per cent as number e.x 55
    crankingHealth: number;
    batterySpecification: string;
    batteryModel: string;
}

export interface BatteryHistory {
    history: RawBatteryResultSummary[];
}

export interface RawBatteryResults {
    r_id: number | undefined;
    r_serial: number | undefined;
    r_service_number: number | undefined;
    r_Vfraw: number | undefined;
    r_Traw: number | undefined;
    r_ohmraw: number | undefined;
    r_Vchraw: number | undefined;
    r_Fraw: number | undefined;
    r_Vcraw: number | undefined;
    r_recommendation: number | undefined;
    r_SoCStatus: number | undefined;
    r_SoCPerc: number | undefined;
    r_SBL: number | undefined;
    r_BLPerc: number | undefined;
    r_CST: number | undefined;
    r_CHPerc: number | undefined;
    r_CSRec: number | undefined;
    r_MV_user_ts: number | undefined;
    r_rdatetime: string | undefined;
    r_version: number | undefined;
    r_testerModel: string | undefined;
    r_appversion: string | undefined;
    r_startusing: number | undefined;
    r_startinst: number | undefined;
    r_apptestid: number | undefined;
    r_avalues: string | undefined;
    r_vvalues: string | undefined;
    r_catresult: number | undefined;
    r_engruntensec: number | undefined;
    b_id: number | undefined;
    b_test_standard: number | undefined;
    b_reference_value: number | undefined;
    b_upc: number | undefined;
    b_battery_type: number | undefined;
    b_brand: string | undefined;
    b_model: string | undefined;
    b_install_date: string | undefined;
    b_groupsize_id: number | undefined;
    b_next_battery_id: number | undefined;
    b_prev_battery_id: number | undefined;
    b_battery_status: number | undefined;
    b_replacement_type: string | undefined;
    v_VIN: string | undefined;
    v_year: string | undefined;
    v_maker: string | undefined;
    v_model: string | undefined;
    v_license: string | undefined;
    v_fuelType: string | undefined;
    v_odometer: number | undefined;
    c_id: number | undefined;
    c_name: string | undefined;
    c_isChain: string | undefined;
    c_parentChainId: string | undefined;
    u_trk_drv_id: number | undefined;
    u_email: string | undefined;
    u_fname: string | undefined;
    u_lname: string | undefined;
}

export interface OutdoorTaskComplete {
    taskId: number;
    staffNumber: number;
}

export type OutdoorSMRComplete = {
    taskId: number;
    staffNumber: number;
} & ({ accepted: true } | { accepted: false; declinedReason: 'Other'; comments: string } | { accepted: false; declinedReason: string });

export interface OutdoorGarageBooking {
    taskId: number;
    staffNumber: number;
    customer: {
        email: string;
        mobileNumber?: string;
        postCode?: string;
        address?: Address;
    };
    job: {
        notes?: string;
        garageId: number;
    };
    aah2BookingRequestId?: string;
}

export interface OutdoorGarageBookingStatusRequest {
    taskId: number;
    operatorId: number;
    reqId: string;
}

export enum GarageBookingStatusType {
    SUCCESS = 'success',
    PENDING = 'pending',
    FAILED = 'failed'
}

export interface GarageBookingStatus {
    aah2BookingRequestId: string;
    taskId: number;
    date: Date;
    status: GarageBookingStatusType;
    unityBookingRefNo?: number;
    error?: string;
}

export function isValidAddress(val?: Partial<Address> | undefined): val is Address {
    return typeof val?.houseNoName === 'string' && typeof val?.postcode === 'string' && Array.isArray(val?.addressLines);
}

export function isValidOutdoorGarageBooking(val?: Partial<OutdoorGarageBooking> | undefined): val is OutdoorGarageBooking {
    if (!val) {
        return false;
    }

    const { taskId, staffNumber, job, customer } = val;
    if (typeof taskId !== 'number' || typeof staffNumber !== 'number') {
        return false;
    }

    // if notes provided
    if (job?.notes && typeof job?.notes !== 'string') {
        return false;
    }

    if (typeof job?.garageId !== 'number') {
        return false;
    }

    if (typeof customer?.email !== 'string') {
        return false;
    }

    // ToDo: commenting validations due to Outdoor request, maybe in future need to put validations back
    // if (typeof customer?.mobileNumber !== 'string') {
    //     return false;
    // }

    // if (typeof customer?.postCode !== 'string') {
    //     return false;
    // }

    return !customer?.address || (customer?.address && isValidAddress(customer?.address));
}

export function isValidOutdoorGarageBookingStatusRequest(val?: Partial<OutdoorGarageBookingStatusRequest> | undefined): val is OutdoorGarageBookingStatusRequest {
    if (!val) {
        return false;
    }

    const { taskId, operatorId, reqId } = val;

    if (!taskId || !operatorId || !reqId) {
        return false;
    }

    if (typeof taskId !== 'number' || typeof operatorId !== 'number' || typeof reqId !== 'string') {
        return false;
    }

    return true;
}

export function isValidOutdoorTaskComplete(val?: Partial<OutdoorTaskComplete> | undefined): val is OutdoorTaskComplete {
    if (!val) {
        return false;
    }

    const { taskId, staffNumber } = val;
    return typeof taskId === 'number' && typeof staffNumber === 'number';
}

export function isValidOutdoorSMRComplete(val?: Partial<OutdoorSMRComplete> | undefined): val is OutdoorSMRComplete {
    if (!val) {
        return false;
    }

    const { taskId, staffNumber } = val;
    return typeof taskId === 'number' && typeof staffNumber === 'number';
}

export function isValidOutdoorB2Q(val?: Partial<OutdoorTaskPayload> | undefined): val is OutdoorTaskPayload {
    if (!val) {
        return false;
    }

    const { taskId, vrn, patrolId } = val;
    return typeof taskId === 'number' && typeof vrn === 'string' && typeof patrolId === 'number';
}
