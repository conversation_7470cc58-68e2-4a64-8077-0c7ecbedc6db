export interface SeSystemData {
    id: number;
    name: string | null;
    code: string | null;
}

export class SeSystem {
    private _id = 0;
    private _name: string | null = null;
    private _code: string | null = null;

    constructor(raw?: Partial<SeSystemData>) {
        if (raw) {
            this._id = raw.id ?? this._id;
            this._name = raw.name ?? this._name;
            this._code = raw.code ?? this._code;
        }
    }

    static SYSTEM_ID_TIA = 'TIA';

    id(value?: number): number | void {
        if (arguments.length === 0) {
            return this._id;
        } else {
            this._id = value ?? 0;
        }
    }

    name(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._name;
        } else {
            this._name = value ?? null;
        }
    }

    code(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._code;
        } else {
            this._code = value ?? null;
        }
    }

    toJSON() {
        return {
            id: this._id,
            name: this._name,
            code: this._code
        };
    }
}
