import { BankCustomerGroupCodes, CustomerGroupCode, EurohelpCustomerGroupsForCarHire } from '../../../common/src/lib/customer-group';
export class CustomerGroupHelper {
    static isBank = (customerGroupCode: CustomerGroupCode): boolean => {
        return BankCustomerGroupCodes.includes(customerGroupCode);
    };

    static isEuroHelp = (customerGroupCode: CustomerGroupCode): boolean => {
        return EurohelpCustomerGroupsForCarHire.includes(customerGroupCode);
    };

    static isFleetReferral = (entitlement: any) => {
        if (!entitlement) {
            return false;
        }
        return true;
        // const customerGroupCode = entitlement.
    };
}
