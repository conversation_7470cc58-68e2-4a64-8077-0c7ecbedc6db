import { Address, AddressData } from './address';

export interface CorrespondenceAddressData {
    abstractAddress: Address;
    valid: boolean;
}

export class CorrespondenceAddress {
    private _abstractAddress: Address;
    private _valid: boolean;

    constructor(raw?: Partial<CorrespondenceAddressData>) {
        this._abstractAddress = new Address(raw?.abstractAddress as unknown as AddressData);
        this._valid = raw?.valid ?? true;
    }

    abstractAddress(value?: Address): Address | void {
        if (arguments.length === 0) {
            return this._abstractAddress;
        } else {
            this._abstractAddress = value ?? new Address();
        }
    }

    valid(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._valid;
        } else {
            this._valid = value ?? true;
        }
    }

    toJSON() {
        return {
            abstractAddress: this._abstractAddress.toJSON(),
            valid: this._valid
        };
    }
}
