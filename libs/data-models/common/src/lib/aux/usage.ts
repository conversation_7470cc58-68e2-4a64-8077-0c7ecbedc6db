export interface UsageData {
    unit?: string | null;
    type?: string | null;
    use?: string | null;
}

export class Usage {
    private _unit: string | null = null;
    private _type: string | null = null;
    private _use: string | null = null;

    static UNIT_CLAIM = 'CLAIM';
    static TYPE_DISTANCE_MILES = 'DISTANCE_MILES';
    static TYPE_USAGE = 'USAGE';

    constructor(raw?: Partial<UsageData>) {
        if (raw) {
            this._unit = raw.unit ?? this._unit;
            this._type = raw.type ?? this._type;
            this._use = raw.use ?? this._use;
        }
    }

    unit(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._unit;
        } else {
            this._unit = value ?? null;
        }
    }

    type(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._type;
        } else {
            this._type = value ?? null;
        }
    }

    use(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._use;
        } else {
            this._use = value ?? null;
        }
    }

    toJSON() {
        return {
            unit: this._unit,
            type: this._type,
            use: this._use
        };
    }
}
