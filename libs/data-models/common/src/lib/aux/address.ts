export interface AddressData {
    addressLines: string[];
    postcode: string | null;
    houseNoName: string | null;
    valid?: boolean;
}

export interface DetailedAddress {
    postcode: string;
    city: string;
    county?: string;
    country: string;
    street: string;
    // Either house name or number
    houseNumber: number | string;
}

export class Address {
    private _addressLines: string[] = [];
    private _postcode: string | null = null;
    private _houseNoName: string | null = null;

    constructor(raw?: Partial<AddressData>) {
        this._addressLines = raw?.addressLines ?? this._addressLines;
        this._postcode = raw?.postcode ?? this._postcode;
        this._houseNoName = raw?.houseNoName ?? this._houseNoName;
    }

    addressLines(value?: string[]): string[] | void {
        if (arguments.length === 0) {
            return this._addressLines;
        } else {
            this._addressLines = value ?? [];
        }
    }

    postcode(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._postcode;
        } else {
            this._postcode = value ?? null;
        }
    }

    houseNoName(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._houseNoName;
        } else {
            this._houseNoName = value ?? null;
        }
    }

    addressAsString(): string {
        const lines = [...this._addressLines];

        if (this._postcode) {
            lines.push(this._postcode);
        }
        if (this._houseNoName && !lines[0].includes(this._houseNoName)) {
            lines[0] = `${this._houseNoName} ${lines[0]}`;
        }

        return lines.join(', ');
    }

    town(): string {
        return this._addressLines.length > 1 ? this._addressLines[this._addressLines.length - 1] : '';
    }

    county(): string {
        return this._addressLines.length > 2 ? this._addressLines[this._addressLines.length - 2] : '';
    }

    toJSON() {
        return {
            addressLines: this._addressLines,
            postcode: this._postcode,
            houseNoName: this._houseNoName
        };
    }
}
