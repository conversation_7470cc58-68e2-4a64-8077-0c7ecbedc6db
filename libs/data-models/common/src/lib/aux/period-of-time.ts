export interface PeriodOfTimeData {
    value: number | null;
    // Default d=DAYS
    unit: string | null;
}

export class PeriodOfTime {
    _value: number | null = null;
    _unit: string | null = null;
    constructor(raw?: PeriodOfTimeData) {
        if (raw) {
            this._value = raw.value ? raw.value : this._value;
            this._unit = raw.unit ? raw.unit : this._unit;
        }
    }

    value(value?: number | null): number | null | void {
        if (value) {
            this._value = value;
        } else {
            return this._value;
        }
    }

    unit(unit?: string | null): string | null | void {
        if (unit) {
            this._unit = unit;
        } else {
            return this._unit;
        }
    }

    toJSON() {
        return {
            value: this._value,
            unit: this._unit
        };
    }
}
