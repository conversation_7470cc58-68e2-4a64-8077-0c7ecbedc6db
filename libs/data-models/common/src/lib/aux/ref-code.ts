export interface RefCode {
    code: string;
    name: string;
}

// export interface IRefCode {
//     code: string | null;
//     name: string | null;
// }
// This has to be ported later as I don't have that much time right now // Matt
// export class RefCode {
//     private _code: string | null = null;
//     private _name: string | null = null;
//
//     constructor(raw?: Partial<IRefCode>) {
//         if (raw) {
//             this._code = raw.code ?? this._code;
//             this._name = raw.name ?? this._name;
//         }
//     }
//
//     code(value?: string | null): string | null | void {
//         if (arguments.length === 0) {
//             return this._code;
//         } else {
//             this._code = value ?? null;
//         }
//     }
//
//     name(value?: string | null): string | null | void {
//         if (arguments.length === 0) {
//             return this._name;
//         } else {
//             this._name = value ?? null;
//         }
//     }
//
//     toJSON() {
//         return {
//             code: this._code,
//             name: this._name
//         };
//     }
// }
