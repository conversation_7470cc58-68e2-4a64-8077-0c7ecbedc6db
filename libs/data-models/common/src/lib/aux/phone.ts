export interface PhoneData {
    extension: string | null;
    phoneNumber: string | null;
    type: string | null;
}

export class Phone {
    private _extension: string | null = null;
    private _phoneNumber: string | null = null;
    private _type: string | null = null;

    constructor(phone?: Partial<PhoneData>) {
        if (phone) {
            this._extension = phone.extension ?? this._extension;
            this._phoneNumber = phone.phoneNumber ?? this._phoneNumber;
            this._type = phone.type ?? this._type;
        }
    }

    extension(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._extension;
        } else {
            this._extension = value ?? null;
        }
    }

    phoneNumber(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._phoneNumber;
        } else {
            this._phoneNumber = value ?? null;
        }
    }

    type(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._type;
        } else {
            this._type = value ?? null;
        }
    }

    toJSON() {
        return {
            extension: this._extension,
            phoneNumber: this._phoneNumber,
            type: this._type
        };
    }
}
