import { CustomerGroup, CustomerGroupData } from './customer-group';
import { SeSystemData, SeSystem } from './se-system';

export interface PolicyData {
    policyNumber: string | null;
    startDate: Date | null;
    endDate: Date | null;
    membershipNumber: string | null;
    cardType: string | null;
    membershipType: string | null;
    customerGroup: CustomerGroupData;
    systemId: string | null;
    seSystem: SeSystem;
    supplierOwnReference: string | null;
    inceptionDate: Date | null;
    inceptionDateAlert: boolean;
    customerKey: string | null;
    contractKey: string | null;
    status: string | null;
    supplierCustomerReference: string | null;
    memberStatus: string | null;
}

export class Policy {
    private _policyNumber: string | null = null;
    private _startDate: Date | null = null;
    private _endDate: Date | null | string = null;
    private _membershipNumber: string | null = null;
    private _cardType: string | null = null;
    private _membershipType: string | null = null;
    private _customerGroup: CustomerGroup = new CustomerGroup();
    private _systemId: string | null = null;
    private _seSystem: SeSystem = new SeSystem();
    private _supplierOwnReference: string | null = null;
    private _inceptionDate: Date | null = null;
    private _inceptionDateAlert = false;
    private _customerKey: string | null = null;
    private _contractKey: string | null = null;
    private _status: string | null = null;
    private _supplierCustomerReference: string | null = null;
    private _memberStatus: string | null = null;

    constructor(raw?: Partial<PolicyData>) {
        if (raw) {
            this._policyNumber = raw.policyNumber ?? this._policyNumber;
            this._startDate = raw.startDate ? new Date(raw.startDate) : this._startDate;
            this._endDate = raw.endDate ? new Date(raw.endDate) : this._endDate;
            this._membershipNumber = raw.membershipNumber ?? this._membershipNumber;
            this._cardType = raw.cardType ?? this._cardType;
            this._membershipType = raw.membershipType ?? this._membershipType;
            this._customerGroup = raw.customerGroup ? new CustomerGroup(raw.customerGroup as unknown as CustomerGroupData) : new CustomerGroup();
            this._systemId = raw.systemId ?? this._systemId;
            this._seSystem = raw.seSystem ? new SeSystem(raw.seSystem as unknown as SeSystemData) : this._seSystem;
            this._supplierOwnReference = raw.supplierOwnReference ?? this._supplierOwnReference;
            this._inceptionDate = raw.inceptionDate ? new Date(raw.inceptionDate) : this._inceptionDate;
            this._inceptionDateAlert = raw.inceptionDateAlert ?? this._inceptionDateAlert;
            this._customerKey = raw.customerKey ?? this._customerKey;
            this._contractKey = raw.contractKey ?? this._contractKey;
            this._status = raw.status ?? this._status;
            this._supplierCustomerReference = raw.supplierCustomerReference ?? this._supplierCustomerReference;
            this._memberStatus = raw.memberStatus ?? this._memberStatus;
        }
    }

    policyNumber(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._policyNumber;
        } else {
            this._policyNumber = value ?? null;
        }
    }

    startDate(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._startDate;
        } else {
            this._startDate = value ?? null;
        }
    }

    endDate(value?: Date | null | string): Date | null | void | string {
        if (arguments.length === 0) {
            return this._endDate;
        } else {
            this._endDate = value ?? null;
        }
    }

    membershipNumber(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._membershipNumber;
        } else {
            this._membershipNumber = value ?? null;
        }
    }

    cardType(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._cardType;
        } else {
            this._cardType = value ?? null;
        }
    }

    membershipType(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            if (this._customerGroup.code() === 'SAGA') {
                if (this._membershipType === 'P') {
                    return 'PERSONAL';
                } else {
                    return 'VEHICLE';
                }
            }
            return this._membershipType;
        } else {
            this._membershipType = value ?? null;
        }
    }

    customerGroup(value?: CustomerGroup): any {
        if (arguments.length === 0) {
            return this._customerGroup;
        } else {
            this._customerGroup = value ?? new CustomerGroup();
        }
    }

    systemId(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._systemId;
        } else {
            this._systemId = value ?? null;
        }
    }

    seSystem(value?: SeSystem): SeSystem | void {
        if (arguments.length === 0) {
            return this._seSystem;
        } else {
            this._seSystem = value ?? new SeSystem();
        }
    }

    supplierOwnReference(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._supplierOwnReference;
        } else {
            this._supplierOwnReference = value ?? null;
        }
    }

    inceptionDate(value?: Date | null): Date | null | void {
        if (arguments.length === 0) {
            return this._inceptionDate;
        } else {
            this._inceptionDate = value ?? null;
        }
    }

    inceptionDateAlert(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._inceptionDateAlert;
        } else {
            this._inceptionDateAlert = value ?? false;
        }
    }

    customerKey(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._customerKey;
        } else {
            this._customerKey = value ?? null;
        }
    }

    contractKey(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._contractKey;
        } else {
            this._contractKey = value ?? null;
        }
    }

    status(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._status;
        } else {
            this._status = value ?? null;
        }
    }

    supplierCustomerReference(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._supplierCustomerReference;
        } else {
            this._supplierCustomerReference = value ?? null;
        }
    }

    memberStatus(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._memberStatus;
        } else {
            this._memberStatus = value ?? null;
        }
    }

    isGoldMember(): boolean {
        if (this._cardType) {
            return this._cardType.trim().toUpperCase() === 'GOLD';
        } else {
            return false;
        }
    }

    toJSON() {
        return {
            policyNumber: this._policyNumber,
            startDate: this._startDate,
            endDate: this._endDate,
            membershipNumber: this._membershipNumber,
            cardType: this._cardType,
            membershipType: this._membershipType,
            customerGroup: this._customerGroup.toJSON(),
            systemId: this._systemId,
            seSystem: this._seSystem.toJSON(),
            supplierOwnReference: this._supplierOwnReference,
            inceptionDate: this._inceptionDate,
            inceptionDateAlert: this._inceptionDateAlert,
            customerKey: this._customerKey,
            contractKey: this._contractKey,
            status: this._status,
            supplierCustomerReference: this._supplierCustomerReference,
            memberStatus: this._memberStatus
        };
    }
}
