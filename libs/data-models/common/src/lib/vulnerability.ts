export interface VulnerabilityData {
    vulnerabilities: string[];
    outcomeType: string | null;
    outcome: string | null;
}

export class Vulnerability {
    private _vulnerabilities: string[] = [];
    private _outcomeType: string | null = null;
    private _outcome: string | null = null;

    constructor(raw?: Partial<VulnerabilityData>) {
        if (raw) {
            this._vulnerabilities = raw.vulnerabilities ?? this._vulnerabilities;
            this._outcomeType = raw.outcomeType ?? this._outcomeType;
            this._outcome = raw.outcome ?? this._outcome;
        }
    }

    vulnerabilities(value?: string[]): string[] | void {
        if (arguments.length === 0) {
            return this._vulnerabilities;
        } else {
            this._vulnerabilities = value ?? [];
        }
    }

    outcomeType(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._outcomeType;
        } else {
            this._outcomeType = value ?? null;
        }
    }

    outcome(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._outcome;
        } else {
            this._outcome = value ?? null;
        }
    }

    isVulnerable(): boolean {
        return this._vulnerabilities.length > 0;
    }

    toJSON() {
        return {
            vulnerabilities: this._vulnerabilities,
            outcomeType: this._outcomeType,
            outcome: this._outcome
        };
    }
}
