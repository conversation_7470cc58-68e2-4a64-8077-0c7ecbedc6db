// TODO: migrate, and get a clue what it does, is it needed
export interface CustomerGroupDataOld {
    code: CustomerGroupCode;
    B2BGroupId?: B2BGroupType;
    // value from the refData that determines the type of B2B customer group
    landmarkClassTypeId?: B2BGroupType;
}

export interface CustomerGroupData {
    id: number | undefined;
    code: string;
    name: string;
    seSystemId: number;
    resSystemId: number;
    warrantyPrompt: boolean;
    allowForceRecy: boolean;
    captureMileage: boolean;
    displayInd: boolean;
    hirerNetworkId: number;
    supNetworkId: number;
    secondLevelSrchAllowed: boolean;
    firstLevelValidationLabel: string | null;
    recoveryCheckRequired: boolean;
    msgHandling: boolean;
    B2BGroupId: number;
    driverDetsReqInd: boolean;
    landmarkClassTypeId?: number;
}

export enum CustomerGroupCode {
    HYU = 'HYU',
    BEA = 'BEA',
    CUPR = 'CUPR',
    POR = 'POR',
    LAND = 'LAND',
    LANE = 'LANE',
    JAG = 'JAG',
    JAGT = 'JAGT',
    JAGA = 'JAGA',
    VW = 'VW',
    VWA = 'VWA',
    VWC = 'VWC',
    VWFS = 'VWFS',
    VWI = 'VWI',
    VWL = 'VWL',
    VWO = 'VWO',
    VWV = 'VWV',
    VWW = 'VWW',
    AUC = 'AUC',
    AUL = 'AUL',
    SEAT = 'SEAT',
    SKA = 'SKA',
    SMA = 'SMA',
    LTSB = 'LTSB',
    SILV = 'SILV',
    PLAT = 'PLAT',
    PRE = 'PRE',
    GOLD = 'GOLD',
    HALI = 'HALI',
    BOS = 'BOS',
    BOSS = 'BOSS',
    BOSR = 'BOSR',
    BOSP = 'BOSP',
    BOSG = 'BOSG',
    VSE = 'VSE',
    VSI = 'VSI',
    VPL = 'VPL',
    VGO = 'VGO',
    VPR = 'VPR',
    VERDESEL = 'VERDESEL',
    VERDESILV = 'VERDESILV',
    FTE = 'FTE',
    FMN = 'FMN',
    SAGA = 'SAGA',
    SAGB = 'SAGB',
    PERS = 'PERS',
    UBE = 'UBE', // This is for UBER. Not a mistake. it is UBE.
    FORD = 'FORD',
    OVERSEAS = 'OSC',
    HOND = 'HOND',
    BOSCH = 'BOSC',
    ADM = 'ADM',
    NBS = 'NBS',
    // START Ecall groups
    LREC = 'LREC', // Land Rover ecall
    JGEC = 'JGEC', // Jaguar & Land Rover joint ecall
    SMEC = 'SMEC', // Smart ecall
    LTEC = 'LTEC', // Lotus ecall
    XBEC = 'XBEC', // cross-border ecall
    // END Ecall groups
    NWGB = 'NWGB',
    NWGP = 'NWGP',
    RBSB = 'RBSB',
    RBSP = 'RBSP',
    //Roadside Insurance AddOn
    CDL = 'CDL',
    CDLV = 'CDLV',
    // END Roadside Insurance AddOn
    CHAS = 'CHAS',
    ACA = 'ACA',
    ASST = 'ASST',
    AAP = 'AAP',
    TCB = 'TCB',
    BIHA = 'BIHA',
    UAB = 'UAB',
    HAK = 'HAK',
    ODYK = 'ODYK',
    UAMK = 'UAMK',
    FDMV = 'FDMV',
    RGEO = 'RGEO',
    ALFI = 'ALFI',
    AEF = 'AEF',
    ADAC = 'ADAC',
    AEH = 'AEH',
    MAK = 'MAK',
    KROK = 'KROK',
    AAIR = 'AAIR',
    AGS = 'AGS',
    KTA = 'KTA',
    SIAA = 'SIAA',
    EAS = 'EAS',
    ACLX = 'ACLX',
    RMF = 'RMF',
    AMSC = 'AMSC',
    ANWB = 'ANWB',
    AMSM = 'AMSM',
    SOSV = 'SOSV',
    AEP = 'AEP',
    ACP = 'ACP',
    ATFO = 'ATFO',
    RAMC = 'RAMC',
    AMSS = 'AMSS',
    ASA = 'ASA',
    AMZS = 'AMZS',
    RACE = 'RACE',
    SOSI = 'SOSI',
    TCS = 'TCS',
    MARM = 'MARM',
    GARA = 'GARA',
    ASSQ = 'ASSQ',
    TCC = 'TCC',
    AEG = 'AEG',
    ADAD = 'ADAD',
    AGT = 'AGT',
    EAT = 'EAT',
    AMZT = 'AMZT',
    TCT = 'TCT',
    SAST = 'SAST',
    PEN = 'PEN',
    SSF = 'SSF',
    AEPP = 'AEPP',
    ADAE = 'ADAE',
    RACC = 'RACC',
    EBS = 'EBS',
    ACI = 'ACI',
    RGO = 'RGO',
    FOF = 'FOF',
    GAEN = 'GAEN',
    ASFP = 'ASFP',
    GSB = 'GSB',
    CASS = 'CASS',
    ZRKV = 'ZRKV',
    EUNE = 'EUNE',
    FIAR = 'FIAR',
    DUC = 'DUC',
    TES = 'TES',
    CAT = 'CAT',
    MAC = 'MAC',
    MERCEDES_AMG = 'MCR',
    SMART_EV = 'SMA',
    GREAT_WALL = 'GWA',
    BYD = 'BYD',
    KTM = 'KTM',
    LOTUS = 'LOTE',
    PVAR = 'PVAR',
    GKL = 'GKL LEASING',
    HCAR = 'HIYACAR',
    GENESIS = 'GEN',
    FSC = 'FSC'
}

export const BankCustomerGroupCodes: CustomerGroupCode[] = [
    CustomerGroupCode.LTSB,
    CustomerGroupCode.SILV,
    CustomerGroupCode.PLAT,
    CustomerGroupCode.PRE,
    CustomerGroupCode.GOLD,
    CustomerGroupCode.HALI,
    CustomerGroupCode.BOS,
    CustomerGroupCode.BOSS,
    CustomerGroupCode.BOSR,
    CustomerGroupCode.BOSP,
    CustomerGroupCode.BOSG,
    CustomerGroupCode.VSE,
    CustomerGroupCode.VSI,
    CustomerGroupCode.VPL,
    CustomerGroupCode.VGO,
    CustomerGroupCode.VPR,
    CustomerGroupCode.NBS,
    CustomerGroupCode.NWGB,
    CustomerGroupCode.NWGP,
    CustomerGroupCode.RBSB,
    CustomerGroupCode.RBSP,
    CustomerGroupCode.LTSB,
    CustomerGroupCode.CHAS
];

export enum B2BGroupType {
    NOT_B2B = 0,
    COMPANY_PAYS = 1,
    CUSTOMER_PAYS = 2,
    CUSTOMER_PAYS_FOR_SERVICE_ONLY = 3,
    COMPANY_MANUFACTURER_PAYS = 4
}

export const ROAM_ENABLED_CUST_GROUPS: CustomerGroupCode[] = [
    CustomerGroupCode.FORD,
    CustomerGroupCode.OVERSEAS,
    CustomerGroupCode.BOSCH,
    CustomerGroupCode.VWL,
    CustomerGroupCode.GENESIS,
    CustomerGroupCode.LOTUS,
    CustomerGroupCode.KTM,
    CustomerGroupCode.BYD,
    CustomerGroupCode.GREAT_WALL,
    CustomerGroupCode.SMART_EV,
    CustomerGroupCode.MERCEDES_AMG
];

export const SUPPRESS_SMS_CUST_GROUPS: CustomerGroupCode[] = [CustomerGroupCode.VWL, CustomerGroupCode.BOSCH, CustomerGroupCode.GENESIS];

export const SUPPRESS_SMS_INBOUND_ONLY: CustomerGroupCode[] = [
    CustomerGroupCode.FORD,
    CustomerGroupCode.OVERSEAS,
    CustomerGroupCode.LOTUS,
    CustomerGroupCode.KTM,
    CustomerGroupCode.BYD,
    CustomerGroupCode.GREAT_WALL,
    CustomerGroupCode.SMART_EV,
    CustomerGroupCode.MERCEDES_AMG
];

export const ENTITLEMENT_ACTIVE_CHECK_ENABLED: CustomerGroupCode[] = [CustomerGroupCode.NWGB, CustomerGroupCode.NWGP, CustomerGroupCode.RBSB, CustomerGroupCode.RBSP];

export const EurohelpCustomerGroupsForCarHire: CustomerGroupCode[] = [
    CustomerGroupCode.ACA,
    CustomerGroupCode.ASST,
    CustomerGroupCode.AAP,
    CustomerGroupCode.TCB,
    CustomerGroupCode.BIHA,
    CustomerGroupCode.UAB,
    CustomerGroupCode.HAK,
    CustomerGroupCode.ODYK,
    CustomerGroupCode.UAMK,
    CustomerGroupCode.FDMV,
    CustomerGroupCode.RGEO,
    CustomerGroupCode.ALFI,
    CustomerGroupCode.AEF,
    CustomerGroupCode.ADAC,
    CustomerGroupCode.AEH,
    CustomerGroupCode.MAK,
    CustomerGroupCode.KROK,
    CustomerGroupCode.AAIR,
    CustomerGroupCode.AGS,
    CustomerGroupCode.KTA,
    CustomerGroupCode.SIAA,
    CustomerGroupCode.EAS,
    CustomerGroupCode.ACLX,
    CustomerGroupCode.RMF,
    CustomerGroupCode.AMSC,
    CustomerGroupCode.ANWB,
    CustomerGroupCode.AMSM,
    CustomerGroupCode.SOSV,
    CustomerGroupCode.AEP,
    CustomerGroupCode.ACP,
    CustomerGroupCode.ATFO,
    CustomerGroupCode.RAMC,
    CustomerGroupCode.AMSS,
    CustomerGroupCode.ASA,
    CustomerGroupCode.AMZS,
    CustomerGroupCode.RACE,
    CustomerGroupCode.SOSI,
    CustomerGroupCode.TCS,
    CustomerGroupCode.MARM,
    CustomerGroupCode.GARA,
    CustomerGroupCode.ASSQ,
    CustomerGroupCode.TCC,
    CustomerGroupCode.AEG,
    CustomerGroupCode.ADAD,
    CustomerGroupCode.AGT,
    CustomerGroupCode.EAT,
    CustomerGroupCode.AMZT,
    CustomerGroupCode.TCT,
    CustomerGroupCode.SAST,
    CustomerGroupCode.PEN,
    CustomerGroupCode.SSF,
    CustomerGroupCode.AEPP,
    CustomerGroupCode.ADAE,
    CustomerGroupCode.RACC,
    CustomerGroupCode.EBS,
    CustomerGroupCode.ACI,
    CustomerGroupCode.RGO,
    CustomerGroupCode.FOF,
    CustomerGroupCode.GAEN,
    CustomerGroupCode.ASFP,
    CustomerGroupCode.GSB,
    CustomerGroupCode.CASS,
    CustomerGroupCode.ZRKV,
    CustomerGroupCode.EUNE,
    CustomerGroupCode.FIAR,
    CustomerGroupCode.DUC
];

export class CustomerGroup {
    private _id: number | undefined;
    private _code = '';
    private _name = '';
    private _seSystemId = 0;
    private _resSystemId = 0;
    private _warrantyPrompt = false;
    private _allowForceRecy = false;
    private _captureMileage = false;
    private _displayInd = false;
    private _hirerNetworkId = -1;
    private _supNetworkId = -1;
    private _secondLevelSrchAllowed = false;
    private _firstLevelValidationLabel: string | null = null;
    private _recoveryCheckRequired = false;
    private _msgHandling = false;
    private _B2BGroupId = 0;
    private _driverDetsReqInd = false;

    constructor(raw?: Partial<CustomerGroupData>) {
        if (raw) {
            this._id = raw.id;
            this._code = raw.code ?? '';
            this._name = raw.name ?? '';
            this._seSystemId = raw.seSystemId ?? 0;
            this._resSystemId = raw.resSystemId ?? 0;
            this._warrantyPrompt = raw.warrantyPrompt ?? this._warrantyPrompt;
            this._allowForceRecy = raw.allowForceRecy ?? this._allowForceRecy;
            this._captureMileage = raw.captureMileage ?? this._captureMileage;
            this._displayInd = raw.displayInd ?? this._displayInd;
            this._hirerNetworkId = raw.hirerNetworkId ?? this._hirerNetworkId;
            this._supNetworkId = raw.supNetworkId ?? this._supNetworkId;
            this._secondLevelSrchAllowed = raw.secondLevelSrchAllowed ?? this._secondLevelSrchAllowed;
            this._firstLevelValidationLabel = raw.firstLevelValidationLabel ?? this._firstLevelValidationLabel;
            this._recoveryCheckRequired = raw.recoveryCheckRequired ?? this._recoveryCheckRequired;
            this._msgHandling = raw.msgHandling ?? this._msgHandling;
            this._B2BGroupId = raw.B2BGroupId || raw.landmarkClassTypeId || 0;
            this._driverDetsReqInd = raw.driverDetsReqInd ?? this._driverDetsReqInd;
        }
    }

    static LAND = 'LAND';
    static LANE = 'LANE';
    static JAG = 'JAG';
    static JAGT = 'JAGT';
    static JAGA = 'JAGA';
    static VW = 'VW';
    static VWA = 'VWA';
    static VWC = 'VWC';
    static VWFS = 'VWFS';
    static VWI = 'VWI';
    static VWL = 'VWL';
    static VWO = 'VWO';
    static VWV = 'VWV';
    static VWW = 'VWW';
    static AUC = 'AUC';
    static AUL = 'AUL';
    static SEAT = 'SEAT';
    static SKA = 'SKA';
    static SMA = 'SMA';
    static LTSB = 'LTSB';
    static SILV = 'SILV';
    static PLAT = 'PLAT';
    static PRE = 'PRE';
    static GOLD = 'GOLD';
    static HALI = 'HALI';
    static BOS = 'BOS';
    static BOSS = 'BOSS';
    static BOSR = 'BOSR';
    static BOSP = 'BOSP';
    static BOSG = 'BOSG';
    static VSE = 'VSE';
    static VSI = 'VSI';
    static VPL = 'VPL';
    static VGO = 'VGO';
    static VPR = 'VPR';
    static VERDESEL = 'VERDESEL';
    static VERDESILV = 'VERDESILV';
    static FTE = 'FTE';
    static FMN = 'FMN';
    static SAGA = 'SAGA';
    static SAGB = 'SAGB';
    static PERS = 'PERS';
    static UBE = 'UBE'; // This is for UBER. Not a mistake. it is UBE.
    static FORD = 'FORD';
    static OVERSEAS = 'OSC';
    static HOND = 'HOND';
    static BOSCH = 'BOSC';
    static ADM = 'ADM';
    static GENESIS = 'GEN';
    static NBS = 'NBS';
    static HCAR = 'HIYACAR';
    static GKL = 'GKL LEASING';
    static PVAR = 'PETER VARDY';
    static POR = 'POR';
    static HYU = 'HYU';
    static BEA = 'BEA';
    static LOTUS = 'LOTE';
    static KTM = 'KTM';
    static BYD = 'BYD';
    static GREAT_WALL = 'GWA';
    static SMART_EV = 'SMA';
    static MERCEDES_AMG = 'MCR';
    static MAC = 'MAC';
    static CAT = 'CAT';
    static TES = 'TES';
    static CUPR = 'CUPR';

    // Eurohelp
    static ACA = 'ACA';
    static ASST = 'ASST';
    static AAP = 'AAP';
    static TCB = 'TCB';
    static BIHA = 'BIHA';
    static UAB = 'UAB';
    static HAK = 'HAK';
    static ODYK = 'ODYK';
    static UAMK = 'UAMK';
    static FDMV = 'FDMV';
    static RGEO = 'RGEO';
    static ALFI = 'ALFI';
    static AEF = 'AEF';
    static ADAC = 'ADAC';
    static AEH = 'AEH';
    static MAK = 'MAK';
    static KROK = 'KROK';
    static AAIR = 'AAIR';
    static AGS = 'AGS';
    static KTA = 'KTA';
    static SIAA = 'SIAA';
    static EAS = 'EAS';
    static ACLX = 'ACLX';
    static RMF = 'RMF';
    static AMSC = 'AMSC';
    static ANWB = 'ANWB';
    static AMSM = 'AMSM';
    static SOSV = 'SOSV';
    static AEP = 'AEP';
    static ACP = 'ACP';
    static ATFO = 'ATFO';
    static RAMC = 'RAMC';
    static AMSS = 'AMSS';
    static ASA = 'ASA';
    static AMZS = 'AMZS';
    static RACE = 'RACE';
    static SOSI = 'SOSI';
    static TCS = 'TCS';
    static MARM = 'MARM';
    static GARA = 'GARA';
    static ASSQ = 'ASSQ';
    static TCC = 'TCC';
    static AEG = 'AEG';
    static ADAD = 'ADAD';
    static AGT = 'AGT';
    static EAT = 'EAT';
    static AMZT = 'AMZT';
    static TCT = 'TCT';
    static SAST = 'SAST';
    static PEN = 'PEN';
    static SSF = 'SSF';
    static AEPP = 'AEPP';
    static ADAE = 'ADAE';
    static RACC = 'RACC';
    static EBS = 'EBS';
    static ACI = 'ACI';
    static RGO = 'RGO';
    static FOF = 'FOF';
    static GAEN = 'GAEN';
    static ASFP = 'ASFP';
    static GSB = 'GSB';
    static CASS = 'CASS';
    static ZRKV = 'ZRKV';
    static EUNE = 'EUNE';
    static FIAR = 'FIAR';
    static DUC = 'DUC';

    // Roadside Insurance AddOn
    static CDL = 'CDL';
    static CDLV = 'CDLV';

    static COMPANY_PAYS = 1;
    static CUSTOMER_PAYS = 2;
    static CUSTOMER_PAYS_FOR_SERVICE_ONLY = 3;
    static COMPANY_MANUFACTURER_PAYS = 4;

    static ROAM_ENABLED_CUST_GROUPS = [
        CustomerGroup.FORD,
        CustomerGroup.OVERSEAS,
        CustomerGroup.BOSCH,
        CustomerGroup.VWL,
        CustomerGroup.GENESIS,
        CustomerGroup.LOTUS,
        CustomerGroup.KTM,
        CustomerGroup.BYD,
        CustomerGroup.GREAT_WALL,
        CustomerGroup.SMART_EV,
        CustomerGroup.MERCEDES_AMG
    ];

    static SUPPRESS_SMS_CUST_GROUPS = [CustomerGroup.VWL, CustomerGroup.BOSCH, CustomerGroup.GENESIS];

    static SUPPRESS_SMS_INBOUND_ONLY = [
        CustomerGroup.FORD,
        CustomerGroup.OVERSEAS,
        CustomerGroup.LOTUS,
        CustomerGroup.KTM,
        CustomerGroup.BYD,
        CustomerGroup.GREAT_WALL,
        CustomerGroup.SMART_EV,
        CustomerGroup.MERCEDES_AMG
    ];

    static NWGB = 'NWGB';
    static NWGP = 'NWGP';
    static RBSB = 'RBSB';
    static RBSP = 'RBSP';

    static ENTITLEMENT_ACTIVE_CHECK_ENABLED = [CustomerGroup.NWGB, CustomerGroup.NWGP, CustomerGroup.RBSB, CustomerGroup.RBSP];

    static CHAS = 'CHAS'; // Chase

    // Ecall groups
    static JGEC = 'JGEC'; // Jaguar & Land Rover joint ecall
    static LREC = 'LREC'; // Land Rover ecall
    static SMEC = 'SMEC'; // Smart ecall
    static LTEC = 'LTEC'; // Lotus ecall
    static XBEC = 'XBEC'; // cross-border ecall

    id(value?: number): number | void {
        if (arguments.length === 0) {
            return this._id;
        } else {
            this._id = value;
        }
    }

    code(value?: string): string | void {
        if (arguments.length === 0) {
            return this._code;
        } else {
            this._code = value ?? '';
        }
    }

    name(value?: string): string | void {
        if (arguments.length === 0) {
            return this._name;
        } else {
            this._name = value ?? '';
        }
    }

    resSystemId(value?: number): number | void {
        if (arguments.length === 0) {
            return this._resSystemId;
        } else {
            this._resSystemId = value ?? 0;
        }
    }

    seSystemId(value?: number): number | void {
        if (arguments.length === 0) {
            return this._seSystemId;
        } else {
            this._seSystemId = value ?? 0;
        }
    }

    systemId(): number | undefined {
        return this._resSystemId && this._resSystemId > 0 ? this._resSystemId : this._seSystemId;
    }

    driverDetsReqInd(): boolean {
        return this._driverDetsReqInd;
    }

    warrantyPrompt(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._warrantyPrompt;
        } else {
            this._warrantyPrompt = value ?? false;
        }
    }

    allowForceRecy(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._allowForceRecy;
        } else {
            this._allowForceRecy = value ?? false;
        }
    }

    captureMileage(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._captureMileage;
        } else {
            this._captureMileage = value ?? false;
        }
    }

    displayInd(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._displayInd;
        } else {
            this._displayInd = value ?? false;
        }
    }

    hirerNetworkId(value?: number): number | void {
        if (arguments.length === 0) {
            return this._hirerNetworkId;
        } else {
            this._hirerNetworkId = value ?? -1;
        }
    }

    supNetworkId(value?: number): number | void {
        if (arguments.length === 0) {
            return this._supNetworkId;
        } else {
            this._supNetworkId = value ?? -1;
        }
    }

    secondLevelSrchAllowed(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._secondLevelSrchAllowed;
        } else {
            this._secondLevelSrchAllowed = value ?? false;
        }
    }

    recoveryCheckRequired(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._recoveryCheckRequired;
        } else {
            this._recoveryCheckRequired = value ?? false;
        }
    }

    msgHandling(value?: boolean): boolean | void {
        if (arguments.length === 0) {
            return this._msgHandling;
        } else {
            this._msgHandling = value ?? false;
        }
    }

    B2BGroupId(value?: number): number | void {
        if (arguments.length === 0) {
            return this._B2BGroupId;
        } else {
            this._B2BGroupId = value ?? 0;
        }
    }

    isPersonal(): boolean {
        return this.systemId() === 13;
    }

    isRoadsideAddOn(): boolean {
        return [CustomerGroup.CDL, CustomerGroup.CDLV].includes(this._code);
    }

    isCDLVRoadsideAddOn(): boolean {
        return this._code === CustomerGroup.CDLV;
    }

    isAdmiral(): boolean {
        return this._code === CustomerGroup.ADM;
    }

    isFleet(): boolean {
        return this.systemId() === 16;
    }

    isManufacturer(): boolean {
        return this.systemId() === 20 || this.systemId() === 21;
    }

    isPFU(): boolean {
        return this.systemId() === 17;
    }

    isSAGA(): boolean {
        return this.systemId() === 15 || [CustomerGroup.SAGA, CustomerGroup.SAGB].includes(this._code);
    }

    isTIAMotorInsurance(): boolean {
        return this.systemId() === 24;
    }

    isSAGAHER(): boolean {
        return this.systemId() === 26;
    }

    isAllJLR(): boolean {
        return [CustomerGroup.JAG, CustomerGroup.JAGA, CustomerGroup.LAND, CustomerGroup.LANE].includes(this._code);
    }

    isEcall(): boolean {
        return [CustomerGroup.JGEC, CustomerGroup.LREC].includes(this._code);
    }

    isJLR(): boolean {
        return [CustomerGroup.JAG, CustomerGroup.LAND].includes(this._code);
    }

    isCCP(): boolean {
        return [CustomerGroup.JAGA, CustomerGroup.LANE].includes(this._code);
    }

    isCheckoutExtensionGroup(): boolean {
        return [CustomerGroup.JAG, CustomerGroup.JAGA, CustomerGroup.LAND, CustomerGroup.LANE, CustomerGroup.POR].includes(this._code);
    }

    isSMR(): boolean {
        return this.isBank() || this._code === CustomerGroup.PERS;
    }

    isJaguar(): boolean {
        return [CustomerGroup.JAG, CustomerGroup.JAGA].includes(this._code);
    }

    isLandRover(): boolean {
        return [CustomerGroup.LAND, CustomerGroup.LANE].includes(this._code);
    }

    isHondaAssistance(): boolean {
        return this._code === CustomerGroup.HOND;
    }

    isVolkswagen(): boolean {
        return [
            CustomerGroup.VW,
            CustomerGroup.VWA,
            CustomerGroup.VWC,
            CustomerGroup.VWFS,
            CustomerGroup.VWI,
            CustomerGroup.VWL,
            CustomerGroup.VWO,
            CustomerGroup.VWV,
            CustomerGroup.VWW,
            CustomerGroup.AUC,
            CustomerGroup.AUL,
            CustomerGroup.SEAT,
            CustomerGroup.SKA,
            CustomerGroup.POR,
            CustomerGroup.BEA,
            CustomerGroup.CUPR
        ].includes(this._code);
    }

    isDealer(): boolean {
        return this.isVolkswagen() || this.isAllJLR();
    }

    isPrestige(): boolean {
        return true;
    }

    isTIAHomeInsurance(): boolean {
        return this.systemId() === 27;
    }

    isBank(): boolean {
        return [
            CustomerGroup.LTSB,
            CustomerGroup.SILV,
            CustomerGroup.PLAT,
            CustomerGroup.PRE,
            CustomerGroup.GOLD,
            CustomerGroup.HALI,
            CustomerGroup.BOS,
            CustomerGroup.BOSS,
            CustomerGroup.BOSR,
            CustomerGroup.BOSP,
            CustomerGroup.BOSG,
            CustomerGroup.VSE,
            CustomerGroup.VSI,
            CustomerGroup.VPL,
            CustomerGroup.VGO,
            CustomerGroup.VPR,
            CustomerGroup.NBS,
            CustomerGroup.NWGB,
            CustomerGroup.NWGP,
            CustomerGroup.RBSB,
            CustomerGroup.RBSP,
            CustomerGroup.CHAS
        ].includes(this._code);
    }

    isEuroHelp(): boolean {
        return [
            CustomerGroup.ACA,
            CustomerGroup.ASST,
            CustomerGroup.AAP,
            CustomerGroup.TCB,
            CustomerGroup.BIHA,
            CustomerGroup.UAB,
            CustomerGroup.HAK,
            CustomerGroup.ODYK,
            CustomerGroup.UAMK,
            CustomerGroup.FDMV,
            CustomerGroup.RGEO,
            CustomerGroup.ALFI,
            CustomerGroup.AEF,
            CustomerGroup.ADAC,
            CustomerGroup.AEH,
            CustomerGroup.MAK,
            CustomerGroup.KROK,
            CustomerGroup.AAIR,
            CustomerGroup.AGS,
            CustomerGroup.KTA,
            CustomerGroup.SIAA,
            CustomerGroup.EAS,
            CustomerGroup.ACLX,
            CustomerGroup.RMF,
            CustomerGroup.AMSC,
            CustomerGroup.ANWB,
            CustomerGroup.AMSM,
            CustomerGroup.SOSV,
            CustomerGroup.AEP,
            CustomerGroup.ACP,
            CustomerGroup.ATFO,
            CustomerGroup.RAMC,
            CustomerGroup.AMSS,
            CustomerGroup.ASA,
            CustomerGroup.AMZS,
            CustomerGroup.RACE,
            CustomerGroup.SOSI,
            CustomerGroup.TCS,
            CustomerGroup.MARM,
            CustomerGroup.GARA,
            CustomerGroup.ASSQ,
            CustomerGroup.TCC,
            CustomerGroup.AEG,
            CustomerGroup.ADAD,
            CustomerGroup.AGT,
            CustomerGroup.EAT,
            CustomerGroup.AMZT,
            CustomerGroup.TCT,
            CustomerGroup.SAST,
            CustomerGroup.PEN,
            CustomerGroup.SSF,
            CustomerGroup.AEPP,
            CustomerGroup.ADAE,
            CustomerGroup.RACC,
            CustomerGroup.EBS,
            CustomerGroup.ACI,
            CustomerGroup.RGO,
            CustomerGroup.FOF,
            CustomerGroup.GAEN,
            CustomerGroup.ASFP,
            CustomerGroup.GSB,
            CustomerGroup.CASS,
            CustomerGroup.ZRKV,
            CustomerGroup.EUNE,
            CustomerGroup.FIAR,
            CustomerGroup.DUC
        ].includes(this._code);
    }

    isAddedValueAccount(): boolean {
        return [CustomerGroup.LTSB, CustomerGroup.PLAT, CustomerGroup.PRE, CustomerGroup.SILV, CustomerGroup.HALI, CustomerGroup.BOS].includes(this._code);
    }

    isLloydsUpgrade(): boolean {
        return [
            CustomerGroup.LTSB,
            CustomerGroup.SILV,
            CustomerGroup.GOLD,
            CustomerGroup.BOSS,
            CustomerGroup.BOSG,
            CustomerGroup.PLAT,
            CustomerGroup.HALI,
            CustomerGroup.PRE,
            CustomerGroup.BOS,
            CustomerGroup.BOSR
        ].includes(this._code);
    }

    isLLOYDSGroup(): boolean {
        return [
            CustomerGroup.LTSB,
            CustomerGroup.SILV,
            CustomerGroup.GOLD,
            CustomerGroup.BOSS,
            CustomerGroup.BOSG,
            CustomerGroup.PLAT,
            CustomerGroup.HALI,
            CustomerGroup.PRE,
            CustomerGroup.BOS,
            CustomerGroup.BOSR
        ].includes(this._code);
    }

    isUBER(): boolean {
        return this._code === CustomerGroup.UBE;
    }

    isAurora(): boolean {
        return this._code === CustomerGroup.CDL;
    }

    isPorsche(): boolean {
        return this._code === CustomerGroup.POR;
    }

    isHyundai(): boolean {
        return this._code === CustomerGroup.HYU;
    }

    firstLevelValidationLabel(value?: string | null): string | null | void {
        if (arguments.length === 0) {
            return this._firstLevelValidationLabel;
        } else {
            this._firstLevelValidationLabel = value ?? null;
        }
    }

    showCustomMileage(): boolean {
        return [CustomerGroup.FTE, CustomerGroup.FMN].includes(this._code);
    }

    showCACRef(): boolean {
        //RBAUAA-2817 - disable CAC flexfield
        return false;
    }

    doesCompanyPay(): boolean {
        return this._B2BGroupId === CustomerGroup.COMPANY_PAYS;
    }

    doesCustomerPay(): boolean {
        return this._B2BGroupId === CustomerGroup.CUSTOMER_PAYS;
    }

    doesCustomerPayForServiceOnly(): boolean {
        return this._B2BGroupId === CustomerGroup.CUSTOMER_PAYS_FOR_SERVICE_ONLY;
    }

    doesCompanyManufacturerPay(): boolean {
        return this._B2BGroupId === CustomerGroup.COMPANY_MANUFACTURER_PAYS;
    }

    isB2BCustomer(): boolean {
        return this.doesCompanyPay() || this.doesCustomerPay() || this.doesCompanyManufacturerPay();
    }

    isNBS(): boolean {
        return this._code === CustomerGroup.NBS;
    }

    isNatwest(): boolean {
        return [CustomerGroup.NWGB, CustomerGroup.NWGP, CustomerGroup.RBSB, CustomerGroup.RBSP].includes(this._code);
    }

    isChas(): boolean {
        return [CustomerGroup.CHAS].includes(this._code);
    }

    isCDL(): boolean {
        return [CustomerGroup.CDL].includes(this._code);
    }

    isCDLV(): boolean {
        return [CustomerGroup.CDLV].includes(this._code);
    }

    isAuthorisedDriverAllowed(): boolean {
        return this.isNBS() || this.isLLOYDSGroup() || this.isNatwest();
    }

    isSelfServiceAuthorisedDriverAllowed(): boolean {
        //20230426 Decio Quintas
        //RBAUAA-1904 says that we should do for NBS and Natwest "the same we did to LLoyds".
        //This means, in fact, that no authorized drivers are now allowed to create tasks. period!
        //TODO: let's see how this goes. If business doesn't change their mind, we can remove this method (and the isAuthorisedDriverAllowed) and the code from the service-entitlement
        //return model.isNBS() || model.isNatwest();
        return false;
    }

    isCUVEnabled(): boolean {
        return this.isNatwest() || this.isPersonal();
    }

    toJSON() {
        return {
            id: this._id,
            code: this._code,
            name: this._name,
            seSystemId: this._seSystemId,
            resSystemId: this._resSystemId,
            warrantyPrompt: this._warrantyPrompt,
            allowForceRecy: this._allowForceRecy,
            captureMileage: this._captureMileage,
            displayInd: this._displayInd,
            hirerNetworkId: this._hirerNetworkId,
            supNetworkId: this._supNetworkId,
            secondLevelSrchAllowed: this._secondLevelSrchAllowed,
            firstLevelValidationLabel: this._firstLevelValidationLabel,
            recoveryCheckRequired: this._recoveryCheckRequired,
            msgHandling: this._msgHandling,
            B2BGroupId: this._B2BGroupId
        };
    }
}
