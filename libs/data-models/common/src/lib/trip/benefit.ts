import { Schema } from '@aa/schema';
import { Currency } from '../aux';

export enum BenefitType {
    // Example
    RSS = 'RSS',
    RECOVERY = 'RECOVERY',
    HOTEL = 'HOTEL',
    STORAGE = 'STORAGE',
    GARAGE_REPAIR = 'GARAGE_REPAIR',
    CAR_HIRE = 'CAR_HIRE',
    // TODO: all below are a part of PUBLIC_TRANSPORT atm
    // TAXI = 'TAXI',
    // FLIGHT = 'FLIGHT',
    // BUS = 'BUS',
    // FERRY = 'FERRY',
    PUBLIC_TRANSPORT = 'PUBLIC_TRANSPORT'
}

export interface EntitlementBenefits {
    code: string;
    created: string | Date;
    updated: string | Date;
    customerGroup: string;
    contractKey: string;
    limits: string[];
}

// standalone entity | collection entity.entitlementBenefit
export const EntitlementBenefitsSchema: Schema<EntitlementBenefits> = Schema.object({
    code: Schema.string(),
    created: Schema.union([Schema.date(), Schema.coerce.date()]),
    updated: Schema.union([Schema.date(), Schema.coerce.date()]),
    customerGroup: Schema.string(),
    contractKey: Schema.string(),
    limits: Schema.array(Schema.string())
});

export interface BenefitLimit {
    code: string;
    created: string | Date;
    updated: string | Date;
    type: BenefitType;
    name: string;
    amount: number;
    currency: Currency;
}

// standalone entity | collection entity.benefitLimit
export const BenefitLimitSchema: Schema<BenefitLimit> = Schema.object({
    code: Schema.string(),
    created: Schema.union([Schema.date(), Schema.coerce.date()]),
    updated: Schema.union([Schema.date(), Schema.coerce.date()]),
    type: Schema.nativeEnum(BenefitType),
    name: Schema.string(),
    amount: Schema.number(),
    currency: Schema.nativeEnum(Currency)
});

export interface BenefitSummary extends BenefitLimit {
    taskIds: number[];
    // total across all tasks
    forecast: {
        amount: number;
        currency: Currency;
    };
    forecastNative: {
        amount: number;
        currency: Currency;
    };
    actual: {
        amount: number;
        currency: Currency;
    };
    actualNative: {
        amount: number;
        currency: Currency;
    };
    // total across all tasks
    overspend?: {
        amount: number;
        currency: Currency;
    };
    tripCode: string;
}

export interface BenefitSummaryResponse {
    benefits: BenefitSummary[];
}

export const primaryBenefits = [BenefitType.RSS];
export const secondaryBenefits = [BenefitType.RECOVERY, BenefitType.PUBLIC_TRANSPORT, BenefitType.HOTEL, BenefitType.CAR_HIRE, BenefitType.STORAGE, BenefitType.GARAGE_REPAIR];
