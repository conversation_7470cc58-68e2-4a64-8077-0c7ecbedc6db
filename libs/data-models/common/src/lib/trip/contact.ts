import { AddressData, LatLong } from '../aux';

export enum ContactType {
    PERSON = 'PERSON',
    BUSINESS = 'BUSINESS',
    // Contact related to the specific customer group
    BUSINESS_PARTNER = 'BUSINESS_PARTNER'
}

export const ContactTypeLabels: { [key in ContactType]: string } = {
    [ContactType.PERSON]: 'Person',
    [ContactType.BUSINESS]: 'Business',
    [ContactType.BUSINESS_PARTNER]: 'Business partner'
};

export interface Contact {
    type: ContactType;
    code: string;
    created: string | Date;
    updated: string | Date;
    name: string;
    email?: string;
    telephone: string;
    note?: string;
    address?: AddressData;
    location?: LatLong;
}
