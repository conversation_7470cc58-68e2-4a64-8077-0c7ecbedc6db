import { Address, AddressData } from '../aux';
import { CommsRoute } from '../comms-route';
import { PaginationQuerySchema } from '../pagination-query';
import { Schema, SchemaType } from '@aa/schema';

/**
 * Represents a trip entity.
 *
 * @interface Trip
 * @property {Date | string} created - The creation date of the trip.
 * @property {Date | string} updated - The last updated date of the trip.
 * @property {string} code - Immutable trip identification code based on MongoDB.
 * @property {Date | string} start - The start date of the trip.
 * @property {Date | string} end - The end date of the trip.
 * @property {TripJourney} [outwardJourney] - The outward journey details of the trip.
 * @property {TripJourney} [returnJourney] - The return journey details of the trip.
 * @property {number} seLocatorId - The SE locator ID for entitlement details.
 * @property {string[]} benefitLimits - The IDs of benefit limits associated with the trip.
 * @property {string} [externalRef] - An optional external reference, e.g., Invoice Number.
 * @property {number[]} customerRequestIds - The list of all customer request IDs linked to this trip.
 * @property {PartyComposition} partyComposition - The party composition for the trip.
 * @property {string} [partyContactCode] - An optional reference to the trip contact describing the main point of
 *     contact for the trip party.
 * @property {string} vrn - The VRN (Vehicle Registration Number) for the trip.
 * @property {string} customerGroup - The customer group for the trip.
 * @property {string} productCode - The product code, also known as policy type.
 * @property {string} [paymentGuarantee] - An optional payment guarantee for the trip.
 * @property {TripStatus} status - The status of the trip.
 */
export interface Trip {
    created: Date | string;
    updated: Date | string;
    code: string;
    start: Date | string;
    end?: Date | string;
    outwardJourney?: TripJourney;
    returnJourney?: TripJourney;
    // Entitlement details
    customerKey: string;
    contractKey: string;
    // Ids of BenefitLimit
    benefitLimits: string[];
    externalRef?: string;
    customerRequestIds: number[];
    partyComposition?: PartyComposition;
    partyContactCode?: string;
    vrn: string;
    customerGroup: string;
    productCode: string;
    paymentGuarantee?: string;
    status: TripStatus;
    // Are trip notifications forced via specific comms channel
    commsRouteOverride?: CommsRoute;
}

/**
 * Enum for trip status.
 *
 * @enum {string}
 */
export enum TripStatus {
    OPEN = 'OPEN',
    CLOSED = 'CLOSED'
}

/**
 * Represents a journey within a trip.
 *
 * @interface TripJourney
 * @property {string} note - A note about the journey.
 * @property {Address} location - The location details of the journey.
 */
export interface TripJourney {
    note: string;
    location: AddressData;
}

/**
 * Represents the composition of a party for a trip.
 *
 * @interface PartyComposition
 * @property {number} adultCount - The number of adults in the party.
 * @property {number} childrenCount - The number of children in the party.
 * @property {boolean} pets - Indicates if there are pets in the party.
 * @property {number} petCount - The number of pets in the party.
 * @property {string} petNote - A note about the pets in the party.
 */
export interface PartyComposition {
    adultCount: number;
    childrenCount: number;
    pets: boolean;
    petCount: number;
    petNote: string;
}

export const QueryTripSchema = PaginationQuerySchema.and(
    Schema.object({
        code: Schema.string().optional(),
        vrn: Schema.string().optional(),
        customerGroup: Schema.string().optional(),
        productCode: Schema.string().optional(),
        created: Schema.date().optional(),
        externalRef: Schema.string().optional(),
        status: Schema.string().optional()
    })
);

export const LongQueryTripSchema = QueryTripSchema.and(
    Schema.object({
        query: Schema.string().optional(),
        status: Schema.string().optional()
    })
);

export type QueryTrip = SchemaType<typeof QueryTripSchema>;
export type LongQueryTrip = SchemaType<typeof LongQueryTripSchema>;
