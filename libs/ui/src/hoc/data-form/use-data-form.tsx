'use client';

import { Schema } from '@aa/schema';
import { DeepPartial, FieldPath, FieldVal<PERSON>, getNested, PathValue, setNested, unsetNested, Utils } from '@aa/utils';
import { useState } from 'react';
import { ZodString } from 'zod';
import { DataFormState } from './data-form-state';
import {
    Clear<PERSON>rrors<PERSON><PERSON>ler,
    FormValidators,
    GetFieldHandler,
    HTMLChangeH<PERSON>ler,
    HTMLSubmit<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>orm<PERSON><PERSON><PERSON>,
    RegisterInputH<PERSON><PERSON>,
    RegisterS<PERSON>us<PERSON><PERSON><PERSON>,
    <PERSON>set<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>mit<PERSON><PERSON><PERSON>,
    ValidateFieldHandler,
    ValidateHandler
} from './data-form-types';

enum ValidationTrigger {
    ON_BLUR = 'ON_BLUR',
    ON_CHANGE = 'ON_CHANGE',
    ON_SUBMIT = 'ON_SUBMIT',
    ALL = 'ALL'
}

interface UseDataFormProps<T extends FieldValues, U extends Schema<T>> {
    schema: U;
    // custom async generators
    validators?: FormValidators<T>;
    // Defaults to ALL
    validationTrigger?: ValidationTrigger;
    disabled?: boolean;
    onSubmit: (data: T) => void | Promise<void>;
    onBlur?: (data: DeepPartial<T>) => void | Promise<void>;
    onChange?: (data: DeepPartial<T>) => void | Promise<void>;
    defaultValues?: DeepPartial<T>;
    showAllErrors?: boolean;
}

/**
 * TODO:
 *  - IMPLEMENT ALL TODOS!
 *  - finish implementation of DataFormStatus
 *  - finish implementation of DataFormField
 *  - finish implementation of DataForm
 */

export const useDataForm = <T extends FieldValues, U extends Schema<T>>(config: UseDataFormProps<T, U>): UseDataFormReturnProps<T> => {
    const { schema, onChange, onBlur, onSubmit, defaultValues = {}, showAllErrors = false, validationTrigger = ValidationTrigger.ALL, validators = [], disabled = false } = config;

    const [formState, setFormState] = useState<DataFormState<T>>({
        values: { ...defaultValues },
        defaultValues: { ...defaultValues },
        disabled,
        isDirty: false,
        isTouched: false,
        isValid: false,
        isSubmitted: false,
        isValidated: false,
        isSubmitSuccessful: false,
        isSubmitting: false,
        isValidating: false,
        dirtyFields: {},
        touchedFields: {},
        invalidFields: {},
        validFields: {},
        validatingFields: {},
        errors: {}
    });

    const clearErrors: ClearErrorsHandler = () => {
        setFormState((prev) => ({
            ...prev,
            isValid: false,
            isValidated: false,
            isValidating: false,
            invalidFields: {},
            validFields: {},
            errors: {},
            validatingFields: {}
        }));
    };

    const cleanState = () => {
        setFormState((prev) => ({
            ...prev,
            isDirty: false,
            isTouched: false,
            isSubmitted: false,
            isSubmitting: false,
            isSubmitSuccessful: false,
            dirtyFields: {},
            touchedFields: {}
        }));
        clearErrors();
    };

    const cleanFieldState = (field: FieldPath<T>) => {
        // TODO:
        //  - remove field errors, dirty etc
        //  - if field is the only one that is dirty, it should reset isDirty & isTouched
        // setFormState((prev) => ({
        //     ...prev,
        //     isDirty: false,
        //     isTouched: false,
        //     isValid: false,
        //     isSubmitted: false,
        //     isValidated: false,
        //     dirtyFields: {},
        //     touchedFields: {},
        //     invalidFields: {},
        //     validFields: {},
        //     validatingFields: {},
        //     errors: {}
        // }));
    };

    const validate: ValidateHandler = () => {
        const errors = Schema.errors(schema, formState.values);
        console.log(errors);
        // TODO: what about errors for nested values? We need to parse to field paths?
        // TODO: implement schemaToFieldErrors function?
        // if errors found
        // if (errors && Object.keys(errors).length) {
        //     setFormState((prev)=>({...prev, errors}))
        // }
        /**
         * TODO:
         *  - validate schema
         *  - validate using validators
         *  - respect showAllErrors
         *  - set isValidating (for validators only as they can be async), isValidated etc
         */
    };
    const validateField: ValidateFieldHandler<T> = (field) => {
        /**
         * TODO:
         *  - validate schema
         *  - validate using validators
         *  - respect showAllErrors
         *  - set isValidating (for validators only as they can be async), isValidated etc
         */
    };

    const submit: SubmitHandler = () => {
        /**
         * TODO:
         *  - process if enabled
         *  - validate if trigger ALL or onSubmit
         *  - submit statuses
         *  - set isSubmitting, isSubmitted etc
         */
    };

    const nativeSubmit: HTMLSubmitHandler = (event) => {
        event.preventDefault();
        event.stopPropagation();

        Utils.toPromise(submit()).catch((error) => {
            // This should never happen as we catch in submit but just in case
            return new Error(`Error while executing form submit: ${(error as Error).message}`);
        });
    };

    const resolveFieldChange = (field: FieldPath<T>, value: PathValue<T, FieldPath<T>>) => {
        const prevValue = getNested(formState.values, field);
        const valueChanged = value !== prevValue;

        setFormState((prev) => {
            setNested(prev.values, field, value);
            let newState = { ...prev };

            if (!prev.isDirty && valueChanged) {
                newState = { ...newState, isDirty: true };
            }
            if (!prev.isTouched) {
                newState = { ...newState, isTouched: true };
            }

            return newState;
        });
    };

    const setField: SetFieldHandler<T> = (field, val) => {
        /**
         * TODO:
         *  - implement, use setNested
         *  - mark as touched
         *  - mark as dirty
         *  - validate if trigger ALL or onChange
         *  - trigger onChange
         */
    };

    const registerInput: RegisterInputHandler<T> = (field) => {
        const isNumber = Schema.getNested(schema, field) instanceof ZodString;

        const changeHandler: HTMLChangeHandler = async (event) => {
            const rawValue = event.target.value;
            const value = isNumber ? Number.parseInt(rawValue as string) : rawValue;

            resolveFieldChange(field, value);

            if ([ValidationTrigger.ALL, ValidationTrigger.ON_CHANGE].includes(validationTrigger)) {
                validate();
            }

            if (onChange) {
                await onChange(formState.values);
            }
        };
        const blurHandler: HTMLChangeHandler = async (event) => {
            const rawValue = event.target.value;
            const value = isNumber ? Number.parseInt(rawValue as string) : rawValue;

            resolveFieldChange(field, value);

            if ([ValidationTrigger.ALL, ValidationTrigger.ON_BLUR].includes(validationTrigger)) {
                validate();
            }

            if (onBlur) {
                await onBlur(formState.values);
            }
        };

        return {
            field,
            onChange: changeHandler,
            onBlur: blurHandler
        };
    };

    const registerField: RegisterFieldHandler<T> = (field) => {
        return { field, formState };
    };

    const registerForm: RegisterFormHandler<T> = () => {
        return { formState, onSubmit: nativeSubmit };
    };

    const registerState: RegisterStatusHandler<T> = () => {
        return { formState };
    };

    const setErrors: SetErrorsHandler<T> = (errors) => {
        // TODO: re-implement
        // if (Array.isArray(errors)) {
        //     for (const error of errors) {
        //         if (error instanceof Error) {
        //             setError('root', { message: error.message });
        //         } else if (typeof error === 'string') {
        //             setError('root', { message: error });
        //         } else {
        //             setError(error.name, { message: error.error instanceof Error ? error.error.message : error.error
        // }); } } } else { for (const [field, error] of Object.entries(errors)) { setError(field as FieldPath<T>, {
        // message: error as string }); } }
    };

    const reset: ResetHandler = () => {
        // if default exists reset do default, else set to undefined
        setFormState((prev) => {
            return { ...prev, values: { ...prev.defaultValues } };
        });
        cleanState();
    };

    const resetField: ResetFieldHandler<T> = (field) => {
        // if default exists for the field, reset do default, else set to undefined
        const defaultValue = getNested(formState.defaultValues, field);
        setFormState((prev) => {
            if (typeof defaultValue === 'undefined') {
                unsetNested(prev.values, field);
            } else {
                setNested(prev.values, field, defaultValue);
            }

            return { ...prev };
        });

        cleanFieldState(field);
    };

    const getField: GetFieldHandler<T> = (field) => {
        return getNested(formState.values, field);
    };

    const disable = () => {
        setFormState((prev) => ({ ...prev, disabled: false }));
    };

    const enable = () => {
        setFormState((prev) => ({ ...prev, disabled: true }));
        // revalidate if dirty
        if (formState.isDirty || formState.isTouched) {
            validate();
        }
    };

    // TODO: implement setFocus(fieldName)
    return {
        register: {
            input: registerInput,
            form: registerForm,
            status: registerState,
            field: registerField
        },
        values: formState.values,
        reset,
        resetField,
        getField,
        formState,
        submit,
        setField,
        setErrors: setErrors,
        clearErrors: clearErrors,
        disable,
        enable,
        validate,
        validateField
    };
};

interface UseDataFormReturnProps<T extends FieldValues> {
    submit: SubmitHandler;
    register: {
        form: RegisterFormHandler<T>;
        input: RegisterInputHandler<T>;
        field: RegisterFieldHandler<T>;
        status: RegisterStatusHandler<T>;
    };
    setErrors: SetErrorsHandler<T>;
    clearErrors: ClearErrorsHandler;
    reset: ResetHandler;
    resetField: ResetFieldHandler<T>;
    setField: SetFieldHandler<T>;
    getField: GetFieldHandler<T>;
    formState: DataFormState<T>;
    values: DeepPartial<T>;
    disable: () => void;
    enable: () => void;
    validate: ValidateHandler;
    validateField: ValidateFieldHandler<T>;
}
