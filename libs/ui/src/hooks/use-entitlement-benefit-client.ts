import { useHttpClient } from './use-http-client';
import { useConnector } from './use-connector';
import { EntitlementBenefitClient } from '@aa/entitlement-benefit-client';

let entitlementBenefitClient: EntitlementBenefitClient;

export const useEntitlementBenefitClient = () => {
    const [httpClient] = useHttpClient();
    const [connector] = useConnector();

    if (!entitlementBenefitClient && httpClient && connector) {
        entitlementBenefitClient = new EntitlementBenefitClient({ httpClient, connector });
    }

    return [entitlementBenefitClient];
};
