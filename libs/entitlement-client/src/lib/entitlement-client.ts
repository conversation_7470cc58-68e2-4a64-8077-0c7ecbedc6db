import { applicationBasePaths, Connector } from '@aa/connector';
import { BackendApplication } from '@aa/identifiers';
import { WithDataId, ProductPolicy } from '@aa/data-models/common';
import { EventCode, Exception } from '@aa/exception';
import { HttpClient, HttpMethod } from '@aa/http-client';
import { Entitlement } from '@aa/data-models/aux/entitlement';

export class EntitlementClient {
    protected readonly name = 'Entitlement Client Library';
    protected httpClient: HttpClient;
    protected connector: Connector;
    protected onSiteDomain: string;

    constructor({ httpClient, connector }: { httpClient: HttpClient; connector: Connector }) {
        this.httpClient = httpClient;
        this.connector = connector;
        this.onSiteDomain = connector.config.onSiteDomain;
    }

    public getPoliciesByCustomerGroupCode = async (customerGroupCode: string): Promise<ProductPolicy[]> => {
        try {
            const body = {
                customerGroupCode
            };
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT]}/getPoliciesByCustomerGroupCode`,
                method: HttpMethod.POST,
                authenticate: true,
                body
            });
            if (result.status === 404) {
                return [];
            }

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: `Entitlement Client Library: Failed getting products policies for customer group: ${customerGroupCode}`
                });
            }

            return result.body as ProductPolicy[];
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: `Entitlement Client Library: Failed getting products policies for customer group: ${customerGroupCode}`
            });
        }
    };

    public async getSELocator(query: { vrn: string; customerGroupCode: string }): Promise<number> {
        try {
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT]}/getSELocatorByCustomerGroupCode`,
                method: HttpMethod.POST,
                authenticate: true,
                body: query
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Entitlement Client Library: Failed getting SE Locator'
                });
            }

            return result.body as number;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Entitlement Client Library: Failed getting SE Locator'
            });
        }
    }

    public getProductsByCustomerGroupCode = async (customerGroupCode: string): Promise<WithDataId<Entitlement>> => {
        try {
            const body = {
                customerGroupCode
            };
            const result = await this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT]}/getProductsByCustomerGroupCode`,
                method: HttpMethod.POST,
                authenticate: true,
                body
            });

            if (!result.ok) {
                throw new Exception({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: `Entitlement Client Library: Failed getting products for customer group: ${customerGroupCode}`
                });
            }

            return result.body as WithDataId<Entitlement>;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: `Entitlement Client Library: Failed getting products for customer group: ${customerGroupCode}`
            });
        }
    };
}
