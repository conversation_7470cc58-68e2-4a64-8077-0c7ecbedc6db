import { Create<PERSON>eason, CreateReasonCode, FaultData, Recovery, RecoveryDist, TaskSchedule, TaskStatus, VehicleData } from '@aa/data-models/common';
import { Eurohelp, isAncillaryTask } from '@aa/data-models/entities/ancillary-task';
import { isBreakdownTask } from '@aa/data-models/entities/breakdown-task';
import { isCarHireTask } from '@aa/data-models/entities/car-hire-task';
import { isFinancialTask } from '@aa/data-models/entities/financial-task';
import { isFLPTask } from '@aa/data-models/entities/flp-task';
import { isGarageRepairTask } from '@aa/data-models/entities/garage-repair-task';
import { isTransportTask } from '@aa/data-models/entities/hotel-task';
import { isRecoveryTask } from '@aa/data-models/entities/recovery-task';
import { isStorageTask } from '@aa/data-models/entities/storage-task';
import { Task } from '@aa/data-models/entities/task';
import { CreateReasonPreview, EntitlementSummaryPreview, EurohelpPreview, FaultPreview, RecoveryPreview, SchedulePreview, TaskPreview, VehiclePreview } from '@aa/data-models/entities/task-preview';
import { isHotelTask } from '@aa/data-models/entities/transport-task';
import { EntitlementSummary } from '@aa/data-models/aux/entitlement';

/**
 * Converts a `BreakdownTask` object to a `TaskPreview` object.
 *
 * @param {BreakdownTask} task - The task data to convert.
 * @returns {TaskPreview} The converted task preview object.
 * @throws {Exception} If the task data is missing required fields.
 */
export function toTaskPreview(task: Task): TaskPreview {
    //TODO: reimplement operatorId check as part of https://theaacom.atlassian.net/browse/RBAUAA-9455
    task.status = task.status || TaskStatus.COMP;

    if (task.operatorId && typeof task.operatorId !== 'number') {
        task.operatorId = parseInt(task.operatorId);
    }

    if (!task.operatorId) {
        task.operatorId = -1;
    }

    if (isBreakdownTask(task)) {
        return {
            id: task.id || -1,
            customerRequestId: task.customerRequestId || -1,
            sequence: task.sequence || 0,
            operatorId: task.operatorId,
            status: task.status,
            location: task.location || {},
            isCompleted: [TaskStatus.COMP, TaskStatus.CLSD].includes(task.status),
            fault: toFaultPreview(task.fault),
            taskType: task.taskType || { code: '', name: '' },
            createReason: toCreateReasonPreview(task.createReason),
            schedule: toSchedulePreview(task.schedule),
            vehicle: toVehiclePreview(task.vehicle),
            recovery: toRecoveryPreview(task.recovery),
            customerGroupName: task.customerGroupName,
            // vrn: task.vehicle?.registration as string || '',
            vrn: (task.vehicle?.registration ?? '') as unknown as string,
            additionalExperianDetails: task.additionalExperianDetails,
            contact: task.contact,
            eurohelp: task.eurohelp && toEurohelpPreview(task.eurohelp),
            entitlement: toEntitlementSummaryPreview(task.entitlement)
        };
    }

    if (isRecoveryTask(task)) {
        return {
            id: task.id || -1,
            customerRequestId: task.customerRequestId || -1,
            sequence: task.sequence || 0,
            operatorId: task.operatorId,
            status: task.status,
            location: task.location || {},
            isCompleted: [TaskStatus.COMP, TaskStatus.CLSD].includes(task.status),
            fault: toFaultPreview(task.fault),
            taskType: task.taskType || { code: '', name: '' },
            createReason: toCreateReasonPreview(task.createReason),
            schedule: toSchedulePreview(task.schedule),
            vehicle: toVehiclePreview(task.vehicle),
            recovery: toRecoveryPreview(task.recovery),
            // vrn: task.vehicle?.registration as string || '',
            vrn: (task.vehicle?.registration ?? '') as unknown as string,
            additionalExperianDetails: task.additionalExperianDetails,
            contact: task.contact,
            eurohelp: task.eurohelp && toEurohelpPreview(task.eurohelp),
            entitlement: toEntitlementSummaryPreview(task.entitlement)
        };
    }

    if (isHotelTask(task)) {
        return {
            id: task.id || -1,
            customerRequestId: task.customerRequestId || -1,
            sequence: task.sequence || 0,
            operatorId: task.operatorId,
            status: task.status,
            taskType: task.taskType || { code: '', name: '' },
            createReason: toCreateReasonPreview(task.createReason),
            hotel: task.hotel,
            transport: task.transport,
            contact: task.contact,
            location: task.location,
            schedule: task.schedule,
            vehicle: task.vehicle
        };
    }

    if (isTransportTask(task)) {
        return {
            id: task.id || -1,
            customerRequestId: task.customerRequestId || -1,
            sequence: task.sequence || 0,
            operatorId: task.operatorId,
            status: task.status,
            createReason: toCreateReasonPreview(task.createReason),
            taskType: task.taskType || { code: '', name: '' },
            transport: task.transport,
            contact: task.contact,
            location: task.location,
            schedule: task.schedule,
            vehicle: task.vehicle
        };
    }

    if (isCarHireTask(task)) {
        return {
            id: task.id || -1,
            customerRequestId: task.customerRequestId || -1,
            sequence: task.sequence || 0,
            operatorId: task.operatorId,
            status: task.status,
            createReason: toCreateReasonPreview(task.createReason),
            taskType: task.taskType || { code: '', name: '' },
            contact: task.contact,
            location: task.location,
            schedule: task.schedule,
            vehicle: task.vehicle
        };
    }

    if (isFLPTask(task)) {
        return {
            id: task.id || -1,
            customerRequestId: task.customerRequestId || -1,
            operatorId: task.operatorId,
            schedule: task.schedule,
            taskType: task.taskType,
            status: task.status,
            sequence: task.sequence || 0
        };
    }

    if (isFinancialTask(task)) {
        return {
            id: task.id || -1,
            customerRequestId: task.customerRequestId || -1,
            sequence: task.sequence || 0,
            operatorId: task.operatorId,
            status: task.status,
            taskType: task.taskType || { code: '', name: '' },
            schedule: task.schedule
        };
    }

    if (isStorageTask(task)) {
        return {
            id: task.id || -1,
            customerRequestId: task.customerRequestId || -1,
            sequence: task.sequence || 0,
            operatorId: task.operatorId,
            status: task.status,
            taskType: task.taskType || { code: '', name: '' },
            schedule: task.schedule,
            contact: task.contact,
            createReason: toCreateReasonPreview(task.createReason),
            eurohelp: task.eurohelp && toEurohelpPreview(task.eurohelp),
            location: task.location || {},
            vehicle: task.vehicle
        };
    }

    if (isGarageRepairTask(task)) {
        return {
            id: task.id || -1,
            customerRequestId: task.customerRequestId || -1,
            sequence: task.sequence || 0,
            operatorId: task.operatorId,
            status: task.status,
            taskType: task.taskType || { code: '', name: '' },
            schedule: task.schedule,
            contact: task.contact,
            createReason: toCreateReasonPreview(task.createReason),
            eurohelp: task.eurohelp && toEurohelpPreview(task.eurohelp),
            location: task.location || {},
            vehicle: task.vehicle
        };
    }

    if (isAncillaryTask(task)) {
        return {
            id: task.id || -1,
            customerRequestId: task.customerRequestId || -1,
            sequence: task.sequence || 0,
            operatorId: task.operatorId,
            status: task.status,
            location: task.location || {},
            isCompleted: [TaskStatus.COMP, TaskStatus.CLSD].includes(task.status),
            taskType: task.taskType || { code: '', name: '' },
            createReason: toCreateReasonPreview(task.createReason),
            schedule: toSchedulePreview(task.schedule),
            vehicle: toVehiclePreview(task.vehicle),
            vrn: (task.vehicle?.registration ?? '') as unknown as string,
            // vrn: task.vehicle?.registration as string || '',
            contact: task.contact,
            entitlement: toEntitlementSummaryPreview(task.entitlement)
        };
    }

    throw new Error('Unable to convert task to preview: unknown task type');
}

/**
 * Converts a `Recovery` object to a `RecoveryPreview` object.
 *
 * @param {Recovery} [recovery] - The recovery data to convert.
 * @returns {RecoveryPreview} The converted recovery preview object, or `undefined` if the input is
 *     `undefined`.
 */
function toRecoveryPreview(recovery?: Recovery): RecoveryPreview {
    if (!recovery) {
        return { destination: {}, distance: 0, motorway: false };
    }

    return {
        destination: recovery.destination,
        distance: recovery.distance,
        motorway: recovery.motorway
    };
}

/**
 * Converts a `Vehicle` object to a `VehiclePreview` object.
 *
 * @param {Vehicle} [vehicle] - The vehicle data to convert.
 * @returns {VehiclePreview | undefined} The converted vehicle preview object, or `undefined` if the input is
 *     `undefined`.
 */
function toVehiclePreview(vehicle?: VehicleData): VehiclePreview {
    if (!vehicle) {
        return {} as VehiclePreview;
    }

    return {
        registration: vehicle.registration,
        vehicleOwnerDetails: vehicle.vehicleOwnerDetails,
        trailerDetails: vehicle.trailerDetails,
        experianDetails: vehicle.experianDetails,
        makeId: vehicle.makeId,
        modelId: vehicle.modelId,
        colour: vehicle.colour,
        typeId: vehicle.typeId,
        twinnedWheels: vehicle.twinnedWheels
    };
}

/**
 * Converts a `CreateReason` object to a `CreateReasonPreview` object.
 *
 * @param {CreateReason} [createReason] - The create reason data to convert.
 * @returns {CreateReasonPreview | undefined} The converted create reason preview object, or `undefined` if the input
 *     is `undefined`.
 */
function toCreateReasonPreview(createReason?: CreateReason): CreateReasonPreview {
    if (!createReason) {
        return {
            id: CreateReasonCode.INITIAL_TASK,
            name: '',
            serviceType: ''
        };
    }

    return {
        id: createReason.id,
        name: createReason.name,
        serviceType: createReason.serviceType
    };
}

/**
 * Converts a `TaskSchedule` object to a `SchedulePreview` object.
 *
 * @param {TaskSchedule} [schedule] - The schedule data to convert.
 * @returns {SchedulePreview | undefined} The converted schedule preview object, or `undefined` if the input is
 *     `undefined`.
 */
function toSchedulePreview(schedule?: TaskSchedule): SchedulePreview {
    if (!schedule) {
        const date = new Date();
        return { arrive: date, complete: date, create: date, dispatch: date };
    }

    return {
        create: schedule.create,
        arrive: schedule.arrive,
        dispatch: schedule.dispatch,
        complete: schedule.complete
    };
}

/**
 * Converts a `FaultData` object to a `FaultPreview` object.
 *
 * @param {FaultData} [fault] - The fault data to convert.
 * @returns {FaultPreview | undefined} The converted fault preview object, or `undefined` if the input is `undefined`.
 */
function toFaultPreview(fault?: FaultData): FaultPreview {
    if (!fault) {
        return {
            id: -1,
            outcome: {},
            code: { code: '', name: '' },
            capabilities: [],
            categoryCode: '',
            name: ''
        };
    }

    return {
        id: fault.id,
        outcome: fault.outcome,
        code: fault.code,
        capabilities: fault.capabilities,
        categoryCode: fault.categoryCode,
        name: fault.name
    };
}

/**
 * Converts a `FaultData` object to a `FaultPreview` object.
 *
 * @param {EntitlementSummary} [EntitlementSummary] - The fault data to convert.
 * @returns {EntitlementSummary | undefined} The converted fault preview object, or `undefined` if the input is
 *     `undefined`.
 */
function toEntitlementSummaryPreview(EntitlementSummary?: EntitlementSummary): EntitlementSummaryPreview {
    if (!EntitlementSummary) {
        return {
            variableData: []
        };
    }

    return {
        variableData: EntitlementSummary.variableData
    };
}

export function toEurohelpPreview(data: Eurohelp): EurohelpPreview {
    return data;
}
