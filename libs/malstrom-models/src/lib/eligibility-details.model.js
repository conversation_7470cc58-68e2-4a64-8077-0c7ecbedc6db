'use strict';

const { Address, Vehicle, CustomerGroup } = require('@aa/data-models/common');
var _ = require('lodash');

function EligibilityDetails(raw) {
    var model = this,
        _id = null,
        _title = '',
        _initials = '',
        _forename = '',
        _surname = '',
        _membershipNumber = '',
        _memberReference = '',
        _cardNumber = null,
        _role = null,
        _registrationNumber = '',
        _daytimeTelephoneNumber = '',
        _eveningTelephoneNumber = '',
        _daytimeTelephoneNumberExtension = '',
        _eveningTelephoneNumberExtension = '',
        _intendedSystemId,
        _riskCode = '',
        _address = new Address(),
        _remark = '',
        _remarkId = null,
        _company = null,
        _siteId = null,
        _customerGroup = new CustomerGroup(),
        _vehicle = new Vehicle();

    if (raw) {
        _id = raw.id;
        _title = raw.title;
        _initials = raw.initials;
        _forename = raw.forename;
        _surname = raw.surname;
        _membershipNumber = raw.membershipNumber;
        _memberReference = raw.memberReference;
        _cardNumber = raw.cardNumber;
        _role = raw.role;
        _registrationNumber = raw.registrationNumber || '';
        _daytimeTelephoneNumber = raw.daytimeTelephoneNumber;
        _eveningTelephoneNumber = raw.eveningTelephoneNumber;
        _daytimeTelephoneNumberExtension = raw.daytimeTelephoneNumberExtension;
        _eveningTelephoneNumberExtension = raw.eveningTelephoneNumberExtension;
        _riskCode = raw.riskCode;
        _address = raw.address ? new Address(raw.address) : _address;
        _remark = raw.remark;
        _remarkId = raw.remarkId;
        _company = raw.company;
        _siteId = raw.siteId;
        _customerGroup = raw.customerGroup ? new CustomerGroup(raw.customerGroup) : _customerGroup;
        _vehicle = raw.vehicle ? new Vehicle(raw.vehicle) : _vehicle;
    }

    _.extend(model, {
        id: function idAccessor(val) {
            return arguments.length ? (_id = val) : _id;
        },
        title: function titleAccessor(val) {
            return arguments.length ? (_title = val) : _title;
        },
        initials: function initialsAccessor(val) {
            return arguments.length ? (_initials = val) : _initials;
        },
        forename: function forenameAccessor(val) {
            return arguments.length ? (_forename = val) : _forename;
        },
        surname: function surnameAccessor(val) {
            return arguments.length ? (_surname = val) : _surname;
        },
        membershipNumber: function membershipNumberAccessor(val) {
            return arguments.length ? (_membershipNumber = val) : _membershipNumber;
        },
        cardNumber: function cardNumberAccessor(val) {
            return arguments.length ? (_cardNumber = val) : _cardNumber;
        },
        intendedSystemId: function intendedSystemIdAcessor(val) {
            return arguments.length ? (_intendedSystemId = val) : _intendedSystemId;
        },
        role: function roleAccessor(val) {
            return arguments.length ? (_role = val) : _role;
        },
        registrationNumber: function registrationNumberAccessor(val) {
            return arguments.length ? (_registrationNumber = val) : _registrationNumber;
        },
        daytimeTelephoneNumber: function daytimeTelephoneNumberAccessor(val) {
            return arguments.length ? (_daytimeTelephoneNumber = val) : _daytimeTelephoneNumber;
        },
        eveningTelephoneNumber: function eveningTelephoneNumberAccessor(val) {
            return arguments.length ? (_eveningTelephoneNumber = val) : _eveningTelephoneNumber;
        },
        daytimeTelephoneNumberExtension: function daytimeTelephoneNumberExtensionAccessor(val) {
            return arguments.length ? (_daytimeTelephoneNumberExtension = val) : _daytimeTelephoneNumberExtension;
        },
        eveningTelephoneNumberExtension: function eveningTelephoneNumberAccessor(val) {
            return arguments.length ? (_eveningTelephoneNumberExtension = val) : _eveningTelephoneNumberExtension;
        },
        riskCode: function riskCodeAccessor(val) {
            return arguments.length ? (_riskCode = val) : _riskCode;
        },
        address: function addressAccessor(val) {
            return arguments.length ? (_address = val) : _address;
        },
        remark: function remarkAccessor(val) {
            return arguments.length ? (_remark = val) : _remark;
        },
        remarkId: function remarkIdAccessor(val) {
            return arguments.length ? (_remarkId = val) : _remarkId;
        },
        company: function companyAccessor(val) {
            return arguments.length ? (_company = val) : _company;
        },
        siteId: function siteIdAccessor(val) {
            return arguments.length ? (_siteId = val) : _siteId;
        },
        customerGroup: function customerGroupCodedAccessor(val) {
            return arguments.length ? (_customerGroup = val) : _customerGroup;
        },
        vehicle: function vehicleAccessor(val) {
            return arguments.length ? (_vehicle = val) : _vehicle;
        },
        isWillJoin: function isWillJoin() {
            return _riskCode === 'WJ';
        },
        memberReference: function memberReference(val) {
            return arguments.length ? (_memberReference = val) : _memberReference;
        },
        toJSON: function serialise() {
            var obj = {
                id: _id,
                title: _title,
                initials: _initials,
                forename: _forename,
                surname: _surname,
                membershipNumber: _membershipNumber,
                memberReference: _memberReference,
                cardNumber: _cardNumber,
                role: _role,
                registrationNumber: _registrationNumber,
                daytimeTelephoneNumber: _daytimeTelephoneNumber,
                eveningTelephoneNumber: _eveningTelephoneNumber,
                daytimeTelephoneNumberExtension: _daytimeTelephoneNumberExtension,
                eveningTelephoneNumberExtension: _eveningTelephoneNumberExtension,
                riskCode: _riskCode,
                address: _address.toJSON(),
                remark: _remark,
                remarkId: _remarkId,
                company: _company,
                siteId: _siteId,
                customerGroup: _customerGroup.toJSON(),
                vehicle: _vehicle.toJSON()
            };

            if (_intendedSystemId) {
                obj.intendedSystemId = _intendedSystemId;
            }

            return obj;
        }
    });
}

EligibilityDetails.WILL_JOIN = 'WJ';

module.exports = EligibilityDetails;
