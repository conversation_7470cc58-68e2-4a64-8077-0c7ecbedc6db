'use strict';

const { Phone, Address } = require('@aa/data-models/common');
const { EntitlementContactOption } = require('@aa/data-models/aux/entitlement');
var _ = require('lodash'),
    _arrayObjectToJSON = require('./factories/array-object-to-json.factory');
/**
 * @typedef EntitlementContact
 * @constructor
 */
function EntitlementContact(raw) {
    var model = this,
        _id = null,
        _title = null,
        _firstName = null,
        _initials = null,
        _surname = null,
        _phones = [],
        _address = new Address(),
        _dateOfBirth = null,
        _role = null,
        _options = new EntitlementContactOption();

    if (raw) {
        _id = raw.id;
        _title = raw.title;
        _firstName = raw.firstName;
        _initials = raw.initials;
        _surname = raw.surname;
        _address = raw.address ? new Address(raw.address) : _address;
        _dateOfBirth = raw.dateOfBirth ? new Date(raw.dateOfBirth) : _dateOfBirth;
        _role = raw.role;
        _options = raw.options ? new EntitlementContactOption(raw.options) : _options;

        _.forEach(raw.phones, function _forEachPhone(rawPhone) {
            _phones.push(new Phone(rawPhone));
        });
    }

    _.extend(model, {
        id: function idAccessor(val) {
            return arguments.length ? (_id = val) : _id;
        },
        title: function titleAccessor(val) {
            return arguments.length ? (_title = val) : _title;
        },
        firstName: function firstNameAccessor(val) {
            return arguments.length ? (_firstName = val) : _firstName;
        },
        initials: function initialsAccessor(val) {
            return arguments.length ? (_initials = val) : _initials;
        },
        surname: function surnameAccessor(val) {
            return arguments.length ? (_surname = val) : _surname;
        },
        phones: function phonesAccessor(val) {
            return arguments.length ? (_phones = val) : _phones;
        },
        address: function addressAccessor(val) {
            return arguments.length ? (_address = val) : _address;
        },
        dateOfBirth: function dateOfBirthAccessor(val) {
            return arguments.length ? (_dateOfBirth = val) : _dateOfBirth;
        },
        role: function roleAccessor(val) {
            return arguments.length ? (_role = val) : _role;
        },
        options: function optionsAccessor(val) {
            return arguments.length ? (_options = val) : _options;
        },
        isAuthorisedDriver: () => {
            return _role === EntitlementContact.AUTHORISED_DRIVER_ROLE;
        },
        isFamilyCover: () => {
            return _role === EntitlementContact.FAMILY_COVER_ROLE;
        },
        isAnyDriverAllowed: () => {
            return _id === EntitlementContact.ISANYDRIVERALLOWED;
        },
        toJSON: function toJSON() {
            return {
                id: _id,
                title: _title,
                firstName: _firstName,
                initials: _initials,
                surname: _surname,
                phones: _arrayObjectToJSON(_phones),
                address: _address ? _address.toJSON() : null,
                dateOfBirth: _dateOfBirth,
                role: _role,
                options: _options.toJSON()
            };
        }
    });
}

EntitlementContact.AUTHORISED_DRIVER_ROLE = 'Authorised Driver';
EntitlementContact.FAMILY_COVER_ROLE = 'Family Cover';
EntitlementContact.ISANYDRIVERALLOWED = 99999;

module.exports = EntitlementContact;
