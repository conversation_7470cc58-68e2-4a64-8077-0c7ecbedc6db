'use strict';

var _ = require('lodash');

const CustomerGroupHelper = require('./helpers/customer-group.helper');

function CustomerGroup(raw) {
    var model = this,
        _id,
        _code,
        _name,
        _seSystemId,
        _resSystemId,
        _warrantyPrompt = false,
        _allowForceRecy = false,
        _captureMileage = false,
        _displayInd = false,
        _hirerNetworkId = -1,
        _supNetworkId = -1,
        _secondLevelSrchAllowed = false,
        _firstLevelValidationLabel = null,
        _recoveryCheckRequired = false,
        _msgHandling = false,
        _B2BGroupId = 0,
        _driverDetsReqInd = false;

    if (raw) {
        _id = raw.id;
        _code = raw.code;
        _name = raw.name;
        _seSystemId = raw.seSystemId;
        _resSystemId = raw.resSystemId;
        _warrantyPrompt = raw.warrantyPrompt === true;
        _allowForceRecy = raw.allowForceRecy === true;
        _captureMileage = raw.captureMileage === true;
        _displayInd = raw.displayInd === true;
        _driverDetsReqInd = raw.driverDetsReqInd === true;
        _hirerNetworkId = raw.hirerNetworkId ? raw.hirerNetworkId : -1;
        _supNetworkId = raw.supNetworkId ? raw.supNetworkId : -1;
        _secondLevelSrchAllowed = raw.secondLevelSrchAllowed ? raw.secondLevelSrchAllowed : false;
        _firstLevelValidationLabel = raw.firstLevelValidationLabel ? raw.firstLevelValidationLabel : null;
        _recoveryCheckRequired = raw.recoveryCheckRequired ? raw.recoveryCheckRequired : false;
        _msgHandling = raw.msgHandling ? raw.msgHandling : false;
        // landmarkClassTypeId is the value from the refData that determines the type of B2B customer group
        _B2BGroupId = raw.B2BGroupId || raw.landmarkClassTypeId ? raw.B2BGroupId || raw.landmarkClassTypeId : 0;
    }

    _.extend(model, {
        id: function idAccessor(val) {
            return arguments.length > 0 ? (_id = val) : _id;
        },
        code: function codeAccessor(val) {
            return arguments.length > 0 ? (_code = val) : _code;
        },
        name: function nameAccessor(val) {
            return arguments.length > 0 ? (_name = val) : _name;
        },
        resSystemId: function resSystemId(val) {
            return arguments.length > 0 ? (_resSystemId = val) : _resSystemId;
        },
        seSystemId: function seSystemId(val) {
            return arguments.length ? (_seSystemId = val) : _seSystemId;
        },
        systemId: function systemIdAccessor() {
            return _resSystemId > 0 ? _resSystemId : _seSystemId;
        },
        driverDetsReqInd: function driverDetsReqIndAccessor() {
            return _driverDetsReqInd;
        },
        warrantyPrompt: function warrantyPromptAccessor(val) {
            return arguments.length > 0 ? (_warrantyPrompt = val) : _warrantyPrompt;
        },
        allowForceRecy: function allowForceRecyAccessor(val) {
            return arguments.length > 0 ? (_allowForceRecy = val) : _allowForceRecy;
        },
        captureMileage: function captureMileageAccessor(val) {
            return arguments.length > 0 ? (_captureMileage = val) : _captureMileage;
        },
        displayInd: function displayIndAccessor(val) {
            return arguments.length > 0 ? (_displayInd = val) : _displayInd;
        },
        hirerNetworkId: function hirerNetworkIdAccessor(val) {
            return arguments.length > 0 ? (_hirerNetworkId = val) : _hirerNetworkId;
        },
        supNetworkId: function supNetworkIdAccessor(val) {
            return arguments.length > 0 ? (_supNetworkId = val) : _supNetworkId;
        },
        secondLevelSrchAllowed: function secondLevelSrchAllowedAccessor(val) {
            return arguments.length > 0 ? (_secondLevelSrchAllowed = val) : _secondLevelSrchAllowed;
        },
        recoveryCheckRequired: function recoveryCheckRequiredAccessor(val) {
            return arguments.length > 0 ? (_recoveryCheckRequired = val) : _recoveryCheckRequired;
        },
        msgHandling: function msgHandlingAccessor(val) {
            return arguments.length > 0 ? (_msgHandling = val) : _msgHandling;
        },
        B2BGroupId: function B2BGroupIdAccessor(val) {
            return arguments.length > 0 ? (_B2BGroupId = val) : _B2BGroupId;
        },
        isPersonal: function isPersonalEvaluator() {
            return model.systemId() === 13; // looking specifically for TIA ...
        },
        isRoadsideAddOn: function isRoadsideAddOnEvaluator() {
            return [CustomerGroup.CDL, CustomerGroup.CDLV].indexOf(_code) > -1;
        },
        isCDLVRoadsideAddOn: function isCDLVRoadsideAddOnEvaluator() {
            return _code === CustomerGroup.CDLV;
        },
        isChase: function isChase() {
            return _code === CustomerGroup.CHAS;
        },
        isAdmiral: function isAdmiral() {
            return [CustomerGroup.ADM].indexOf(_code) > -1;
        },
        isFleet: function isFleetEvaluator() {
            return model.systemId() === 16;
        },
        isManufacturer: function isManufacturerEvaluator() {
            return model.systemId() === 20 || model.systemId() === 21;
        },
        isPFU: function isPFU() {
            return model.systemId() === 17;
        },
        isSAGA: function isSAGA() {
            return model.systemId() === 15 || [CustomerGroup.SAGA, CustomerGroup.SAGB].indexOf(_code) > -1;
        },
        isTIAMotorInsurance: function () {
            return model.systemId() === 24;
        },
        isSAGAHER: function isSAGAHER() {
            return model.systemId() === 26;
        },
        isAllJLR: function isAllJLR() {
            return [CustomerGroup.JAG, CustomerGroup.JAGA, CustomerGroup.LAND, CustomerGroup.LANE].indexOf(_code) > -1;
        },
        isEcall: function isEcall() {
            return [CustomerGroup.JGEC, CustomerGroup.LREC].indexOf(_code) > -1;
        },
        isJLR: function isJLR() {
            return [CustomerGroup.JAG, CustomerGroup.LAND].indexOf(_code) > -1;
        },
        isCCP: function isCCP() {
            return [CustomerGroup.JAGA, CustomerGroup.LANE].indexOf(_code) > -1;
        },
        isCheckoutExtensionGroup: function isCheckoutExtensionGroup() {
            return [CustomerGroup.JAG, CustomerGroup.JAGA, CustomerGroup.LAND, CustomerGroup.LANE, CustomerGroup.POR].indexOf(_code) > -1;
        },
        isSMR: function isSMR() {
            // banks enabled for SMR
            if (model.isBank()) {
                return true;
            }
            // b2c enabled for smr
            return _code === CustomerGroup.PERS;
        },
        isJaguar: function isJaguar() {
            return [CustomerGroup.JAG, CustomerGroup.JAGA].indexOf(_code) > -1;
        },
        isLandRover: function isLandRover() {
            return [CustomerGroup.LAND, CustomerGroup.LANE].indexOf(_code) > -1;
        },
        isHondaAssistance: function isHondaAssistance() {
            return [CustomerGroup.HOND].indexOf(_code) > -1;
        },
        isVolkswagen: function isVolkswagen() {
            return (
                [
                    CustomerGroup.VW,
                    CustomerGroup.VWA,
                    CustomerGroup.VWC,
                    CustomerGroup.VWFS,
                    CustomerGroup.VWI,
                    CustomerGroup.VWL,
                    CustomerGroup.VWO,
                    CustomerGroup.VWV,
                    CustomerGroup.VWW,
                    CustomerGroup.AUC,
                    CustomerGroup.AUL,
                    CustomerGroup.SEAT,
                    CustomerGroup.SKA,
                    CustomerGroup.POR,
                    CustomerGroup.BEA,
                    CustomerGroup.CUPR
                ].indexOf(_code) > -1
            );
        },
        isDealer: function isDealerAccessor() {
            return model.isVolkswagen() || model.isAllJLR();
        },
        isPrestige: function isPrestige() {
            return true;
        },
        isTIAHomeInsurance: function isTIAHomeInsurance() {
            return model.systemId() === 27;
        },
        isBank: function isBank() {
            return CustomerGroupHelper.isBank(_code);
        },
        isEuroHelp: function isEuroHelp() {
            return CustomerGroupHelper.isEuroHelp(_code);
        },
        isAddedValueAccount: function () {
            return (
                [CustomerGroup.LTSB, CustomerGroup.PLAT, CustomerGroup.PRE, CustomerGroup.SILV, CustomerGroup.HALI, CustomerGroup.BOS, CustomerGroup.VERDESEL, CustomerGroup.VERDESILV].indexOf(_code) >
                -1
            );
        },
        isLloydsUpgrade: function () {
            return [CustomerGroup.LTSB, CustomerGroup.SILV, CustomerGroup.GOLD, CustomerGroup.HALI, CustomerGroup.BOSS, CustomerGroup.BOSG].indexOf(_code) > -1;
        },
        isLLOYDSGroup: function isLLOYDSGroup() {
            return [
                CustomerGroup.LTSB,
                CustomerGroup.SILV,
                CustomerGroup.GOLD,
                CustomerGroup.BOSS,
                CustomerGroup.BOSG,
                CustomerGroup.PLAT,
                CustomerGroup.HALI,
                CustomerGroup.PRE,
                CustomerGroup.BOS,
                CustomerGroup.BOSR,
                CustomerGroup.BOSP
            ].includes(_code);
        },
        isUBER: function isUBER() {
            return CustomerGroup.UBE === _code;
        },
        isAurora: function isAurora() {
            return CustomerGroup.CDL === _code;
        },
        isPorsche: function isPorsche() {
            return [CustomerGroup.POR].indexOf(_code) > -1;
        },
        isHyundai: function isHyundai() {
            return [CustomerGroup.HYU].indexOf(_code) > -1;
        },
        firstLevelValidationLabel: function firstLevelValidationLabelAccessor(val) {
            return arguments.length ? (_firstLevelValidationLabel = val) : _firstLevelValidationLabel;
        },
        showCustomMileage: function () {
            return [CustomerGroup.FTE, CustomerGroup.FMN].indexOf(_code) > -1;
        },
        showCACRef: function showCACRef() {
            //RBAUAA-2817 - disable CAC flexfield
            return false;
        },
        doesCompanyPay: function doesCompanyPay() {
            return _B2BGroupId === CustomerGroup.COMPANY_PAYS;
        },
        doesCustomerPay: function doesCustomerPay() {
            return _B2BGroupId === CustomerGroup.CUSTOMER_PAYS;
        },
        doesCustomerPayForServiceOnly: function doesCustomerPayForServiceOnly() {
            return _B2BGroupId === CustomerGroup.CUSTOMER_PAYS_FOR_SERVICE_ONLY;
        },
        doesCompanyManufacturerPay: function doesCompanyManufacturerPay() {
            return _B2BGroupId === CustomerGroup.COMPANY_MANUFACTURER_PAYS;
        },
        isB2BCustomer: () => {
            return model.doesCompanyPay() || model.doesCustomerPay() || model.doesCompanyManufacturerPay();
        },
        isNBS: () => {
            return [CustomerGroup.NBS].includes(_code);
        },
        isNatwest: () => {
            return [CustomerGroup.NWGB, CustomerGroup.NWGP, CustomerGroup.RBSB, CustomerGroup.RBSP].includes(_code);
        },
        isChas: () => {
            return [CustomerGroup.CHAS].includes(_code);
        },
        isCDL: () => {
            return [CustomerGroup.CDL].includes(_code);
        },
        isCDLV: () => {
            return [CustomerGroup.CDLV].includes(_code);
        },
        isAuthorisedDriverAllowed: () => {
            return model.isNBS() || model.isLLOYDSGroup() || model.isNatwest();
        },
        isSelfServiceAuthorisedDriverAllowed: () => {
            //20230426 Decio Quintas
            //RBAUAA-1904 says that we should do for NBS and Natwest "the same we did to LLoyds".
            //This means, in fact, that no authorized drivers are now allowed to create tasks. period!
            //TODO: let's see how this goes. If business doesn't change their mind, we can remove this method (and the isAuthorisedDriverAllowed) and the code from the service-entitlement
            //return model.isNBS() || model.isNatwest();
            return false;
        },
        isCUVEnabled: () => {
            return model.isNatwest() || model.isPersonal() || model.isLLOYDSGroup();
        },
        toJSON: function toJSONFormatter() {
            return {
                id: _id,
                code: _code,
                name: _name,
                seSystemId: _seSystemId,
                resSystemId: _resSystemId,
                warrantyPrompt: _warrantyPrompt,
                allowForceRecy: _allowForceRecy,
                captureMileage: _captureMileage,
                displayInd: _displayInd,
                driverDetsReqInd: _driverDetsReqInd,
                hirerNetworkId: _hirerNetworkId,
                supNetworkId: _supNetworkId,
                secondLevelSrchAllowed: _secondLevelSrchAllowed,
                firstLevelValidationLabel: _firstLevelValidationLabel,
                recoveryCheckRequired: _recoveryCheckRequired,
                msgHandling: _msgHandling,
                B2BGroupId: _B2BGroupId
            };
        }
    });
}

//customer group codes
CustomerGroup.LAND = 'LAND';
CustomerGroup.LANE = 'LANE';
CustomerGroup.JAG = 'JAG';
CustomerGroup.JAGT = 'JAGT';
CustomerGroup.JAGA = 'JAGA';
CustomerGroup.VW = 'VW';
CustomerGroup.VWA = 'VWA';
CustomerGroup.VWC = 'VWC';
CustomerGroup.VWFS = 'VWFS';
CustomerGroup.VWI = 'VWI';
CustomerGroup.VWL = 'VWL';
CustomerGroup.VWO = 'VWO';
CustomerGroup.VWV = 'VWV';
CustomerGroup.VWW = 'VWW';
CustomerGroup.AUC = 'AUC';
CustomerGroup.AUL = 'AUL';
CustomerGroup.SEAT = 'SEAT';
CustomerGroup.SKA = 'SKA';
CustomerGroup.SMA = 'SMA';
CustomerGroup.LTSB = 'LTSB';
CustomerGroup.SILV = 'SILV';
CustomerGroup.PLAT = 'PLAT';
CustomerGroup.PRE = 'PRE';
CustomerGroup.GOLD = 'GOLD';
CustomerGroup.HALI = 'HALI';
CustomerGroup.BOS = 'BOS';
CustomerGroup.BOSS = 'BOSS';
CustomerGroup.BOSR = 'BOSR';
CustomerGroup.BOSP = 'BOSP';
CustomerGroup.BOSG = 'BOSG';
CustomerGroup.VSE = 'VSE';
CustomerGroup.VSI = 'VSI';
CustomerGroup.VPL = 'VPL';
CustomerGroup.VGO = 'VGO';
CustomerGroup.VPR = 'VPR';
CustomerGroup.VERDESEL = 'VERDESEL';
CustomerGroup.VERDESILV = 'VERDESILV';
CustomerGroup.FTE = 'FTE';
CustomerGroup.FMN = 'FMN';
CustomerGroup.SAGA = 'SAGA';
CustomerGroup.SAGB = 'SAGB';
CustomerGroup.PERS = 'PERS';
CustomerGroup.UBE = 'UBE'; //This is for UBER. Not a mistake. it is UBE.
CustomerGroup.FORD = 'FORD';
CustomerGroup.OVERSEAS = 'OSC';
CustomerGroup.HOND = 'HOND';
CustomerGroup.BOSCH = 'BOSC';
CustomerGroup.ADM = 'ADM';
CustomerGroup.GENESIS = 'GEN';
CustomerGroup.NBS = 'NBS';
CustomerGroup.HCAR = 'HIYACAR';
CustomerGroup.GKL = 'GKL LEASING';
CustomerGroup.PVAR = 'PETER VARDY';
CustomerGroup.POR = 'POR';
CustomerGroup.HYU = 'HYU';
CustomerGroup.BEA = 'BEA';
CustomerGroup.LOTUS = 'LOTE';
CustomerGroup.KTM = 'KTM';
CustomerGroup.BYD = 'BYD';
CustomerGroup.GREAT_WALL = 'GWA';
CustomerGroup.SMART_EV = 'SMA';
CustomerGroup.MERCEDES_AMG = 'MCR';
CustomerGroup.MAC = 'MAC';
CustomerGroup.CAT = 'CAT';
CustomerGroup.TES = 'TES';
CustomerGroup.CUPR = 'CUPR';

// Roadside Insurance AddOn
CustomerGroup.CDL = 'CDL';
CustomerGroup.CDLV = 'CDLV';

CustomerGroup.COMPANY_PAYS = 1;
CustomerGroup.CUSTOMER_PAYS = 2;
CustomerGroup.CUSTOMER_PAYS_FOR_SERVICE_ONLY = 3;
CustomerGroup.COMPANY_MANUFACTURER_PAYS = 4;
CustomerGroup.ROAM_ENABLED_CUST_GROUPS = [
    CustomerGroup.FORD,
    CustomerGroup.OVERSEAS,
    CustomerGroup.BOSCH,
    CustomerGroup.VWL,
    CustomerGroup.GENESIS,
    CustomerGroup.LOTUS,
    CustomerGroup.KTM,
    CustomerGroup.BYD,
    CustomerGroup.GREAT_WALL,
    CustomerGroup.SMART_EV,
    CustomerGroup.MERCEDES_AMG
];
CustomerGroup.SUPPRESS_SMS_CUST_GROUPS = [CustomerGroup.VWL, CustomerGroup.BOSCH, CustomerGroup.GENESIS];
CustomerGroup.SUPPRESS_SMS_INBOUND_ONLY = [
    CustomerGroup.FORD,
    CustomerGroup.OVERSEAS,
    CustomerGroup.LOTUS,
    CustomerGroup.KTM,
    CustomerGroup.BYD,
    CustomerGroup.GREAT_WALL,
    CustomerGroup.SMART_EV,
    CustomerGroup.MERCEDES_AMG
];
CustomerGroup.NWGB = 'NWGB';
CustomerGroup.NWGP = 'NWGP';
CustomerGroup.RBSB = 'RBSB';
CustomerGroup.RBSP = 'RBSP';
CustomerGroup.ENTITLEMENT_ACTIVE_CHECK_ENABLED = [CustomerGroup.NWGB, CustomerGroup.NWGP, CustomerGroup.RBSB, CustomerGroup.RBSP];
CustomerGroup.CHAS = 'CHAS'; // Chase

// Ecall groups
CustomerGroup.JGEC = 'JGEC'; // Jaguar & Land Rover joint ecall
CustomerGroup.LREC = 'LREC'; // Land Rover ecall
CustomerGroup.SMEC = 'SMEC'; // Smart ecall
CustomerGroup.LTEC = 'LTEC'; // Lotus ecall
CustomerGroup.XBEC = 'XBEC'; // cross-border ecall

module.exports = CustomerGroup;
