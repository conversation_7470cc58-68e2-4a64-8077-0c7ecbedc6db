'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    Location = require('./location.model'),
    PaymentValue = require('./payment-value.model');

function TransportReservation(raw) {
    var model = this,
        _dropOffLocation = new Location(),
        _dropOffAddress = new Address(),
        _pickUpLocation = new Location(),
        _pickUpAddress = new Address(),
        _type = null,
        _price = new PaymentValue(),
        _noOfAdults = 0,
        _noOfChildren = 0,
        _operatorName = null,
        _reason = null,
        _startDate = null,
        _phoneNumber = null,
        _message = null,
        _arrivalTime = null,
        _class = null,
        _reservationId = null,
        _payAndClaim = false;

    if (raw) {
        _dropOffLocation = raw.dropOffLocation ? new Location(raw.dropOffLocation) : _dropOffLocation;
        _dropOffAddress = raw.dropOffAddress ? new Address(raw.dropOffAddress) : _dropOffAddress;
        _pickUpLocation = raw.pickUpLocation ? new Location(raw.pickUpLocation) : _pickUpLocation;
        _pickUpAddress = raw.pickUpAddress ? new Address(raw.pickUpAddress) : _pickUpAddress;
        _price = raw.price ? new PaymentValue(raw.price) : _price;
        _noOfAdults = raw.noOfAdults || _noOfAdults;
        _noOfChildren = raw.noOfChildren || _noOfChildren;
        _type = raw.type || _type;
        _operatorName = raw.operatorName || _operatorName;
        _reason = raw.reason || _reason;
        _phoneNumber = raw.phoneNumber || _phoneNumber;
        _message = raw.message || _message;
        _arrivalTime = raw.arrivalTime || _arrivalTime;
        _startDate = raw.startDate ? new Date(raw.startDate) : _startDate;
        _class = raw.class || _class;
        _reservationId = raw.reservationId || _reservationId;
        _payAndClaim = raw.payAndClaim || _payAndClaim;
    }

    _.extend(model, {
        /**
         * @param {Location} val
         */
        pickUpLocation: function pickUpLocationAccessor(val) {
            return arguments.length ? (_pickUpLocation = new Location(val)) : _pickUpLocation;
        },
        /**
         * @param {Location} val
         */
        dropOffLocation: function dropOffLocationAccessor(val) {
            return arguments.length ? (_dropOffLocation = new Location(val)) : _dropOffLocation;
        },
        /**
         * @param {Address} val
         */
        dropOffAddress: function dropOffAddressAccessor(val) {
            return arguments.length ? (_dropOffAddress = new Address(val)) : _dropOffAddress;
        },
        /**
         * @param {Address} val
         */
        pickUpAddress: function pickUpAddressAccessor(val) {
            return arguments.length ? (_pickUpAddress = new Address(val)) : _pickUpAddress;
        },
        /**
         * @param {TransportTask.TYPES | string} val
         */
        type: function typeAccessor(val) {
            return arguments.length ? (_type = val) : _type;
        },
        /**
         * @param {PaymentValue} val
         */
        price: function priceAccessor(val) {
            return arguments.length ? (_price = new PaymentValue(val)) : _price;
        },
        /**
         * @param {number} val
         */
        noOfAdults: function noOfAdultsAccessor(val) {
            return arguments.length ? (_noOfAdults = val) : _noOfAdults;
        },
        /**
         * @param {number} val
         */
        noOfChildren: function noOfChildrenAccessor(val) {
            return arguments.length ? (_noOfChildren = val) : _noOfChildren;
        },
        /**
         * @param {string} val
         */
        operatorName: function operatorNameAccessor(val) {
            return arguments.length ? (_operatorName = val) : _operatorName;
        },
        /**
         * @param {TransportTask.TRAVEL_REASONS} val
         */
        reason: function reasonAccessor(val) {
            return arguments.length ? (_reason = val) : _reason;
        },
        /**
         * @param {string|Date} val
         */
        startDate: function startDateAccessor(val) {
            return arguments.length ? (_startDate = val) : _startDate;
        },
        /**
         * @param {string} val
         */
        phoneNumber: function phoneNumberAccessor(val) {
            return arguments.length ? (_phoneNumber = val) : _phoneNumber;
        },
        /**
         * @param {string} val
         */
        message: function messageAccessor(val) {
            return arguments.length ? (_message = val) : _message;
        },
        /**
         * @param {string} val
         */
        arrivalTime: function arrivalTimeAccessor(val) {
            return arguments.length ? (_arrivalTime = val) : _arrivalTime;
        },
        /**
         * @param {string} val
         */
        class: function classAccessor(val) {
            return arguments.length ? (_class = val) : _class;
        },
        /**
         * @param {string} val
         */
        reservationId: function reservationIdAccessor(val) {
            return arguments.length ? (_reservationId = val) : _reservationId;
        },
        /**
         * @param {boolean} val
         */
        payAndClaim: function payAndClaimAccessor(val) {
            return arguments.length ? (_payAndClaim = val) : _payAndClaim;
        },
        /**
         * @deprecated preserved property for legacy purpose
         * @return {number}
         */
        numOfPassengers: function numOfPassengersAccessor() {
            // read only
            return _noOfAdults + _noOfChildren;
        },

        isTransportSet: function () {
            return !!(_type && _reason);
        },

        toJSON: function toJSON() {
            return {
                dropOffLocation: _dropOffLocation.toJSON(),
                pickUpLocation: _pickUpLocation.toJSON(),
                dropOffAddress: _dropOffAddress.toJSON(),
                pickUpAddress: _pickUpAddress.toJSON(),
                type: _type,
                price: _price.toJSON(),
                noOfAdults: _noOfAdults,
                noOfChildren: _noOfChildren,
                operatorName: _operatorName,
                reason: _reason,
                phoneNumber: _phoneNumber,
                message: _message,
                arrivalTime: _arrivalTime,
                startDate: _startDate,
                class: _class,
                reservationId: _reservationId,
                payAndClaim: _payAndClaim,
                // preserved for legacy purposes
                numOfPassengers: _noOfAdults + _noOfChildren
            };
        }
    });
}

module.exports = TransportReservation;
