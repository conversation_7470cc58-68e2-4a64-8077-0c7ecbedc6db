'user strict';

var _ = require('lodash'),
    moment = require('moment');

function ExperianDetails(rawData) {
    var model = this,
        _make = null,
        _model = null,
        _colour = null,
        _transmission = null,
        _grossWeight = null,
        _ukDateFirstRegistered = null,
        _dateFirstRegistered = null,
        _bodyStyle = null,
        _modelVariant = null,
        _smmtRange = null,
        _engineNo = null,
        _co2Emissions = null,
        _rbCodes = null,
        _mvrisCode = null,
        _vin = null,
        _vinConfirmFlag = null,
        _engineSize = null,
        _yrOfManufacture = null,
        _fuel = null,
        _wheelPlan = null,
        _imported = null,
        _series = null,
        _abiBrokernetCode = null,
        _glassModelId = null,
        _gears = null,
        _importNonEu = null,
        _kerbWeight = null,
        _length = null,
        _seatNumber = null,
        _wrongDetailsFlag = false,
        _flag = true,
        _makeModel = null,
        _doorPlan = null,
        _doorPlanCode = null,
        _firstRegistered, // need this to be undefined
        _ukFirstRegistered,
        _driveType = null,
        _width = null,
        _height = null;

    if (rawData) {
        _make = rawData.make;
        _model = rawData.model;
        _colour = rawData.colour;
        _driveType = rawData.driveType;
        _transmission = rawData.transmission;
        _grossWeight = rawData.grossWeight;
        _ukDateFirstRegistered = rawData.ukDateFirstRegistered ? rawData.ukDateFirstRegistered : null;
        _dateFirstRegistered = rawData.dateFirstRegistered ? rawData.dateFirstRegistered : null;
        _bodyStyle = rawData.bodyStyle;
        _modelVariant = rawData.modelVariant;
        _smmtRange = rawData.smmtRange;
        _engineNo = rawData.engineNo;
        _co2Emissions = rawData.co2Emissions;
        _rbCodes = rawData.rbCodes;
        _doorPlan = rawData.doorPlan;
        _doorPlanCode = rawData.doorPlanCode;
        _mvrisCode = rawData.mvrisCode;
        _vin = rawData.vin;
        _vinConfirmFlag = rawData.vinConfirmFlag;
        _engineSize = rawData.engineSize;
        _yrOfManufacture = rawData.yrOfManufacture;
        _fuel = rawData.fuel;
        _wheelPlan = rawData.wheelPlan;
        _imported = rawData.imported;
        _series = rawData.series;
        _abiBrokernetCode = rawData.abiBrokernetCode;
        _glassModelId = rawData.glassModelId;
        _gears = rawData.gears;
        _importNonEu = rawData.importNonEu;
        _kerbWeight = rawData.kerbWeight ? rawData.kerbWeight : null;
        _length = rawData.length ? rawData.length : _length;
        _seatNumber = rawData.seatNumber ? rawData._seatNumber : _seatNumber;
        _height = rawData.height ? rawData.height : _height;
        _width = rawData.width ? rawData.width : _width;
        _wrongDetailsFlag = rawData.wrongDetailsFlag;
        _flag = rawData.flag;
        _makeModel = rawData.makeModel;
    }

    function experianDateFormat(val) {
        var date = null;
        if (val) {
            date = new Date(val.substring(0, 4) + '-' + val.substring(4, 6) + '-' + val.substring(6, 8));
        }
        return date;
    }

    _.extend(model, {
        make: function makeAccessor(val) {
            return arguments.length > 0 ? (_make = val) : _make;
        },
        model: function modelAccessor(val) {
            return arguments.length > 0 ? (_model = val) : _model;
        },
        colour: function colourAccessor(val) {
            return arguments.length > 0 ? (_colour = val) : _colour;
        },
        driveType: function driveTypeAccessor(val) {
            return arguments.length > 0 ? (_driveType = val) : _driveType;
        },
        height: function heightAccessor(val) {
            return arguments.length > 0 ? (_height = val) : _height;
        },
        width: function widthAccessor(val) {
            return arguments.length > 0 ? (_width = val) : _width;
        },
        transmission: function transmissionAccessor(val) {
            return arguments.length > 0 ? (_transmission = val) : _transmission;
        },
        doorPlan: function doorPlanAccessor(val) {
            return arguments.length > 0 ? (_doorPlan = val) : _doorPlan;
        },
        doorPlanCode: function doorPlanCodeAccessor(val) {
            return arguments.length > 0 ? (_doorPlanCode = val) : _doorPlanCode;
        },
        grossWeight: function grossWeightAccessor(val) {
            return arguments.length > 0 ? (_grossWeight = val) : _grossWeight;
        },
        ukDateFirstRegistered: function ukDateFirstRegisteredAccessor(val) {
            return arguments.length > 0 ? (_ukDateFirstRegistered = val) : _ukDateFirstRegistered;
        },
        dateFirstRegistered: function dateFirstRegisteredAccessor(val) {
            return arguments.length > 0 ? (_dateFirstRegistered = val) : _dateFirstRegistered;
        },
        firstRegistered: function firstRegisteredGetter() {
            // to be used for UI to render the date ...
            if (_.isUndefined(_firstRegistered)) {
                _firstRegistered = experianDateFormat(_dateFirstRegistered);
            }
            return _firstRegistered;
        },
        vehicleAgeInYears: function vehicleAgeInYears() {
            let _date = experianDateFormat(_dateFirstRegistered);
            return moment().diff(moment(_date), 'years');
        },
        ukFirstRegistered: function ukFirstRegisteredGetter() {
            // to be used for UI to render the date ...
            if (_.isUndefined(_ukFirstRegistered)) {
                _ukFirstRegistered = experianDateFormat(_ukDateFirstRegistered);
            }
            return _ukFirstRegistered;
        },
        bodyStyle: function bodyStyleAccessor(val) {
            return arguments.length > 0 ? (_bodyStyle = val) : _bodyStyle;
        },
        modelVariant: function modelVariantAccessor(val) {
            return arguments.length > 0 ? (_modelVariant = val) : _modelVariant;
        },
        smmtRange: function smmtRangeAccessor(val) {
            return arguments.length > 0 ? (_smmtRange = val) : _smmtRange;
        },
        engineNo: function engineNoAccessor(val) {
            return arguments.length > 0 ? (_engineNo = val) : _engineNo;
        },
        co2Emissions: function co2EmissionsAccessor(val) {
            return arguments.length > 0 ? (_co2Emissions = val) : _co2Emissions;
        },
        rbCodes: function rbCodesAccessor(val) {
            return arguments.length > 0 ? (_rbCodes = val) : _rbCodes;
        },
        mvrisCode: function mvrisCodeAccessor(val) {
            return arguments.length > 0 ? (_mvrisCode = val) : _mvrisCode;
        },
        vin: function vinAccessor(val) {
            return arguments.length > 0 ? (_vin = val) : _vin;
        },
        vinConfirmFlag: function vinConfirmFlagAccessor(val) {
            return arguments.length > 0 ? (_vinConfirmFlag = val) : _vinConfirmFlag;
        },
        engineSize: function engineSizeAccessor(val) {
            return arguments.length > 0 ? (_engineSize = val) : _engineSize;
        },
        yrOfManufacture: function yrOfManufactureAccessor(val) {
            return arguments.length > 0 ? (_yrOfManufacture = val) : _yrOfManufacture;
        },
        fuel: function fuelAccessor(val) {
            return arguments.length > 0 ? (_fuel = val) : _fuel;
        },
        wheelPlan: function wheelPlanAccessor(val) {
            return arguments.length > 0 ? (_wheelPlan = val) : _wheelPlan;
        },
        imported: function importedAccessor(val) {
            return arguments.length > 0 ? (_imported = val) : _imported;
        },
        series: function seriesAccessor(val) {
            return arguments.length > 0 ? (_series = val) : _series;
        },
        abiBrokernetCode: function abiBrokernetCodeAccessor(val) {
            return arguments.length > 0 ? (_abiBrokernetCode = val) : _abiBrokernetCode;
        },
        glassModelId: function glassModelIdAccessor(val) {
            return arguments.length > 0 ? (_glassModelId = val) : _glassModelId;
        },
        gears: function gearsAccessor(val) {
            return arguments.length > 0 ? (_gears = val) : _gears;
        },
        importNonEu: function importNonEuAccessor(val) {
            return arguments.length > 0 ? (_importNonEu = val) : _importNonEu;
        },
        kerbWeight: function kerbWeightAccessor(val) {
            return arguments.length > 0 ? (_kerbWeight = val) : _kerbWeight;
        },
        seatNumber: function seatNumberAccessor(val) {
            return arguments.length > 0 ? (_seatNumber = val) : _seatNumber;
        },
        length: function lengthAccessor(val) {
            return arguments.length > 0 ? (_length = val) : _length;
        },
        wrongDetailsFlag: function wrongDetailsFlagAccessor(val) {
            return arguments.length > 0 ? (_wrongDetailsFlag = val) : _wrongDetailsFlag;
        },
        flag: function flagAccessor(val) {
            return arguments.length > 0 ? (_flag = val) : _flag;
        },
        makeModel: function makeModelAccessor(val) {
            return arguments.length > 0 ? (_makeModel = val) : _makeModel;
        },

        toJSON: function serialiseObject() {
            return {
                make: _make,
                model: _model,
                colour: _colour,
                driveType: _driveType,
                height: _height,
                width: _width,
                seatNumber: _seatNumber,
                transmission: _transmission,
                grossWeight: _grossWeight,
                ukDateFirstRegistered: _ukDateFirstRegistered,
                dateFirstRegistered: _dateFirstRegistered,
                bodyStyle: _bodyStyle,
                modelVariant: _modelVariant,
                smmtRange: _smmtRange,
                engineNo: _engineNo,
                co2Emissions: _co2Emissions,
                rbCodes: _rbCodes,
                mvrisCode: _mvrisCode,
                vin: _vin,
                vinConfirmFlag: _vinConfirmFlag,
                engineSize: _engineSize,
                yrOfManufacture: _yrOfManufacture,
                fuel: _fuel,
                wheelPlan: _wheelPlan,
                imported: _imported,
                series: _series,
                abiBrokernetCode: _abiBrokernetCode,
                glassModelId: _glassModelId,
                gears: _gears,
                importNonEu: _importNonEu,
                kerbWeight: _kerbWeight,
                length: _length,
                wrongDetailsFlag: _wrongDetailsFlag,
                flag: _flag,
                makeModel: _makeModel,
                doorPlan: _doorPlan,
                doorPlanCode: _doorPlanCode,
            };
        },
    });
}

module.exports = ExperianDetails;
