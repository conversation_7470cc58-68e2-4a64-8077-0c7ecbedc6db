'use strict';

const { Entitlement } = require('@aa/data-models/aux/entitlement');
var _ = require('lodash');

module.exports = function EntitlementSearchResponse(raw) {
    var model = this,
        _entitlements = [],
        _unlistedSlvEntitlement = null,
        _nextPage = 0;

    if (raw) {
        if (raw.entitlements) {
            _.forEach(raw.entitlements, function (eItem) {
                _entitlements.push(new Entitlement(eItem));
            });
        }
        _unlistedSlvEntitlement = raw.unlistedSlvEntitlement ? new Entitlement(raw.unlistedSlvEntitlement) : null;
        _nextPage = raw.nextPage ? raw.nextPage : 0;
    }

    _.extend(model, {
        entitlements: function entitlementsAccessor(val) {
            return arguments.length > 0 ? (_entitlements = val) : _entitlements;
        },
        unlistedSlvEntitlement: function unListedSlvEntitlementAccessor(val) {
            return arguments.length > 0 ? (_unlistedSlvEntitlement = val) : _unlistedSlvEntitlement;
        },
        nextPage: function nextPageAccessor(val) {
            return arguments.length > 0 ? (_nextPage = val) : _nextPage;
        },
        toJSON: function toJSON() {
            var arrayOject = [];
            _.forEach(_entitlements, function (item) {
                arrayOject.push(item.toJSON());
            });

            return {
                entitlements: arrayOject,
                unlistedSlvEntitlement: _unlistedSlvEntitlement,
                nextPage: _nextPage
            };
        }
    });
};
