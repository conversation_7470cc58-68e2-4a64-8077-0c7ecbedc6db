'use strict';
var _ = require('lodash');

function RentalApproval(raw) {
    var _instructions = null,
        _required = null,
        _needsAuthorisation = null,
        _phoneNumber = null;

    if (raw) {
        _instructions = raw.instructions;
        _required = raw.required;
        _needsAuthorisation = raw.needsAuthorisation;
        _phoneNumber = raw.phoneNumber;
    }

    _.extend(this, {
        instructions: function instructionsAccessor(val) {
            return arguments.length ? (_instructions = val) : _instructions;
        },
        required: function requiredAccessor(val) {
            return arguments.length ? (_required = val) : _required;
        },
        needsAuthorisation: function needsAuthorisationAccessor(val) {
            return arguments.length ? (_needsAuthorisation = val) : _needsAuthorisation;
        },
        phoneNumber: function phoneNumberAccessor(val) {
            return arguments.length ? (_phoneNumber = val) : _phoneNumber;
        },
        toJSON: function serialise() {
            return {
                instructions: _instructions,
                required: _required,
                needsAuthorisation: _needsAuthorisation,
                phoneNumber: _phoneNumber,
            };
        },
    });
}

module.exports = RentalApproval;
