'use strict';

const { CustomerGroup } = require('@aa/data-models/common');
var _ = require('lodash'),
    ServiceType = require('./service-type.model');

/**
 * service type
 * @param {object} rawData        [description]
 */
function AssistanceType(rawData) {
    var model = this,
        _id = 0,
        _customerGroup = new CustomerGroup(),
        _serviceType = new ServiceType();

    if (rawData) {
        _id = rawData.id;
        _customerGroup = rawData.customerGroup ? new CustomerGroup(rawData.customerGroup) : _customerGroup;
        _serviceType = rawData.serviceType ? new ServiceType(rawData.serviceType) : _serviceType;
    }

    _.extend(model, {
        id: function idAccessor(val) {
            return arguments.length > 0 ? (_id = val) : _id;
        },
        customerGroup: function customerGroupAccessor(val) {
            return arguments.length > 0 ? (_customerGroup = val) : _customerGroup;
        },
        serviceType: function serviceTypeAccessor(val) {
            return arguments.length > 0 ? (_serviceType = val) : _serviceType;
        },
        toJSON: function serialiseObject() {
            return {
                id: _id,
                customerGroup: _customerGroup.toJSON(),
                serviceType: _serviceType.toJSON()
            };
        }
    });
}

module.exports = AssistanceType;
