'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    AbstractTask = require('./abstract-task.model'),
    Schedule = require('./task-schedule.model'),
    RefCode = require('./ref-code.model'),
    Contact = require('./contact.model');

function CommendationTask(raw) {
    var model = this,
        _id = null,
        _taskType = new RefCode({
            code: 'CMD',
            name: 'Commendation task'
        }),
        _schedule = new Schedule(),
        _busLocnId = null,
        _staffNo = null,
        _callerName = null,
        _cardName = null,
        _address = new Address(),
        _contact = new Contact(),
        _commendComment = null,
        _customerRequestId = null,
        _remark = null,
        _operatorId = null;

    if (raw) {
        _id = raw.id ? raw.id : _id;
        _busLocnId = raw.busLocnId ? raw.busLocnId : _busLocnId;
        _staffNo = raw.staffNo ? raw.staffNo : _staffNo;
        _callerName = raw.callerName ? raw.callerName : _callerName;
        _cardName = raw.cardName ? raw.cardName : _cardName;
        _address = raw.address ? new Address(raw.address) : _address;
        _contact = raw.contact ? new Contact(raw.contact) : _contact;
        _commendComment = raw.commendComment ? raw.commendComment : _commendComment;
        _customerRequestId = raw.customerRequestId ? raw.customerRequestId : _customerRequestId;
        _schedule = raw.schedule ? new Schedule(raw.schedule) : _schedule;
        _remark = raw.remark ? raw.remark : _remark;
        _operatorId = raw.operatorId ? raw.operatorId : _operatorId;
    }

    _.extend(model, new AbstractTask(), {
        id: function taskIdAccessor(val) {
            return arguments.length ? (_id = val) : _id;
        },
        taskType: function taskTypeAccessor() {
            return _taskType;
        },
        busLocnId: function busLocnIdAccessor(val) {
            return arguments.length ? (_busLocnId = val) : _busLocnId;
        },
        staffNo: function staffNoAccessor(val) {
            return arguments.length ? (_staffNo = val) : _staffNo;
        },
        callerName: function callerNameAccessor(val) {
            return arguments.length ? (_callerName = val) : _callerName;
        },
        cardName: function cardNameAccessor(val) {
            return arguments.length ? (_cardName = val) : _cardName;
        },
        commendComment: function commendCommentAccessor(val) {
            return arguments.length ? (_commendComment = val) : _commendComment;
        },
        address: function addressAccessor(val) {
            return arguments.length ? (_address = val) : _address;
        },
        customerRequestId: function customerRequestIdAccessor(val) {
            return arguments.length ? (_customerRequestId = val) : _customerRequestId;
        },
        contact: function contactAccessor(val) {
            return arguments.length ? (_contact = val) : _contact;
        },
        schedule: function scheduleAccessor(val) {
            return arguments.length ? (_schedule = val) : _schedule;
        },
        remark: function remarkAccessor(val) {
            return arguments.length ? (_remark = val) : _remark;
        },
        operatorId: function operatorIdAccessor(val) {
            return arguments.length ? (_operatorId = val) : _operatorId;
        },
        toJSON: function toJSON() {
            return {
                id: _id,
                busLocnId: _busLocnId,
                staffNo: _staffNo,
                callerName: _callerName,
                cardName: _cardName,
                commendComment: _commendComment,
                address: _address ? _address.toJSON() : null,
                customerRequestId: _customerRequestId,
                contact: _contact ? _contact.toJSON() : null,
                schedule: _schedule ? _schedule.toJSON() : null,
                taskType: _taskType.toJSON(),
                remark: _remark,
                operatorId: _operatorId
            };
        }
    });
}

module.exports = CommendationTask;
