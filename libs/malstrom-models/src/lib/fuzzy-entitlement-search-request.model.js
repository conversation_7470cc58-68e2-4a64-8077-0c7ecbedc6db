'use strict';

var _ = require('lodash'),
    CustomerGroup = require('./customer-group.model');

module.exports = function FuzzyEntitlementSearchRequest(raw) {
    var model = this,
        _contractKeyLast4Digits = null,
        _customerGroup = null,
        _customerGroupCode = null,
        _firstName = null,
        _membershipNo = null,
        _postcode = null,
        _surname = null,
        _title = null,
        _dob = null,
        _vehicleReg = null,
        _vin = null,
        _policyId = null,
        _supplierOwnDriverId = null,
        _bcasp = null,
        _country = null,
        _contractType = null;

    if (raw) {
        _postcode = raw.postcode || null;
        _title = raw.title || ''; // fix for RBAUAA-1194
        _dob = raw.dob || null;
        _firstName = raw.firstName || '';
        _surname = raw.surname || '';

        _membershipNo = raw.membershipNo || null;
        _contractKeyLast4Digits = raw.contractKeyLast4Digits || null;

        _vehicleReg = raw.vehicleReg || null;
        _vin = raw.vin || null;

        _customerGroupCode = raw.customerGroupCode;
        _customerGroup = raw.customerGroup ? new CustomerGroup(raw.customerGroup) : null;

        _policyId = raw.policyId || null;
        _supplierOwnDriverId = raw.supplierOwnDriverId || null;

        _bcasp = raw.bcasp || null;
        _country = raw.country || null;
        _contractType = raw.contractType || null;
    }

    _.extend(model, {
        postcode: function postcodeAccessor(val) {
            return arguments.length > 0 ? (_postcode = val) : _postcode;
        },
        surname: function surnameAccessor(val) {
            return arguments.length > 0 ? (_surname = val) : _surname;
        },
        contractKeyLast4Digits: function contractKeyLast4DigitsAccessor(val) {
            return arguments.length > 0 ? (_contractKeyLast4Digits = val) : _contractKeyLast4Digits;
        },
        vehicleReg: function vehicleRegAccessor(val) {
            return arguments.length > 0 ? (_vehicleReg = val) : _vehicleReg;
        },
        vin: function vinAccessor(val) {
            return arguments.length > 0 ? (_vin = val) : _vin;
        },
        customerGroupCode: function customerGroupCodeAccessor(val) {
            return arguments.length > 0 ? (_customerGroupCode = val) : _customerGroupCode;
        },
        customerGroup: function customerGroupAccessor(val) {
            return arguments.length > 0 ? (_customerGroup = val) : _customerGroup;
        },
        title: function titleAccessor(val) {
            return arguments.length > 0 ? (_title = val) : _title;
        },
        dob: function dobAccessor(val) {
            return arguments.length > 0 ? (_dob = val) : _dob;
        },
        firstName: function firstNameAccessor(val) {
            return arguments.length > 0 ? (_firstName = val) : _firstName;
        },
        initials: function initials() {
            return _firstName ? _firstName.charAt(0) : null;
        },
        membershipNo: function membershipNoAccessor(val) {
            return arguments.length > 0 ? (_membershipNo = val) : _membershipNo;
        },
        policyId: function policyIdAccessor(val) {
            return arguments.length > 0 ? (_policyId = val) : _policyId;
        },
        supplierOwnDriverId: function supplierOwnDriverIdAccessor(val) {
            return arguments.length > 0 ? (_supplierOwnDriverId = val) : _supplierOwnDriverId;
        },
        bcasp: function bcaspAccessor(val) {
            return arguments.length > 0 ? (_bcasp = val) : _bcasp;
        },
        country: function countryAccessor(val) {
            return arguments.length > 0 ? (_country = val) : _country;
        },
        contractType: function contractTypeAccessor(val) {
            return arguments.length > 0 ? (_contractType = val) : _contractType;
        },
        toJSON: function toJSON() {
            return {
                postcode: _postcode,
                title: _title,
                dob: _dob,
                firstName: _firstName,
                surname: _surname,
                contractKeyLast4Digits: _contractKeyLast4Digits,
                membershipNo: _membershipNo,
                vehicleReg: _vehicleReg,
                vin: _vin,
                customerGroupCode: _customerGroupCode,
                customerGroup: _customerGroup !== null ? _customerGroup.toJSON() : null,
                policyId: _policyId,
                supplierOwnDriverId: _supplierOwnDriverId,
                bcasp: _bcasp,
                country: _country,
                contractType: _contractType
            };
        }
    });
};
