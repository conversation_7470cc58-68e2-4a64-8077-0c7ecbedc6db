'use strict';
var _ = require('lodash');

/**
 * Constructor for raw phone object
 *
 * @typedef {Object} Phone
 * @constructor
 */
function Phone(raw) {
    var model = this,
        _extension = null,
        _phoneNumber = null,
        _type = null;

    if (raw) {
        _extension = raw.extension;
        _phoneNumber = raw.phoneNumber;
        _type = raw.type;
    }

    _.extend(model, {
        extension: function extensionAccessor(val) {
            return arguments.length ? (_extension = val) : _extension;
        },
        phoneNumber: function phoneNumberAccessor(val) {
            return arguments.length ? (_phoneNumber = val) : _phoneNumber;
        },
        type: function typeAccessor(val) {
            return arguments.length ? (_type = val) : _type;
        },
        toJSON: function toJSON() {
            return {
                extension: _extension,
                phoneNumber: _phoneNumber,
                type: _type,
            };
        },
    });
}

module.exports = Phone;
