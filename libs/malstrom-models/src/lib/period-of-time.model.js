'use strict';
var _ = require('lodash');

function PeriodOfTime(raw) {
    var _value = null,
        _unit = 'd'; // default d=DAYS

    if (raw) {
        _value = raw.value; //Integer
        _unit = raw.unit?.toLowerCase() || _unit;
    }

    _.extend(this, {
        value: function valueAccessor(val) {
            return arguments.length ? (_value = val) : _value;
        },
        unit: function unitAccessor(val) {
            return arguments.length ? (_unit = val) : _unit;
        },

        toJSON: function serialise() {
            return {
                value: _value,
                unit: _unit,
            };
        },
    });
}

module.exports = PeriodOfTime;
