'use strict';
var _ = require('lodash'),
    RentalApproval = require('./rental-approval.model'),
    InitialRental = require('./initial-rental.model'),
    RentalExtension = require('./rental-extension.model'),
    ConditionalHire = require('./conditional-hire.model'),
    CarHireFaultRestrictions = require('./car-hire-fault-restrictions.model');

function CarRentalEntitlement(raw) {
    var _rentalCategory = null,
        _idRequired = null,
        _creditCardRequired = null,
        _licenseRequired = null,
        _fuelDepositRequired = null,
        _towbarAllowed = null,
        _officeHoursApproval = new RentalApproval(),
        _outOfHoursApproval = new RentalApproval(),
        _initialRental = new InitialRental(),
        _extension = new RentalExtension(),
        _vehicleOffRoadLimit = null,
        _coolOffPeriod = new ConditionalHire(),
        _faultRestrictions = new CarHireFaultRestrictions(),
        _followOrRecoveryJob = null,
        _patrolCanBook = true,
        _agileEnabled = true,
        _defInitialHireDurationPayer = 'AA',
        _isUKCustomerGrp = false,
        _isExtProcessEnabled = true,
        _isMaladminExtEnabled = true,
        _isPreAuthEnabled = true,
        _isSAPNotifyEnabled = true,
        _hireFleetTypes = [];

    if (raw) {
        _rentalCategory = raw.rentalCategory ? raw.rentalCategory.replace(/_/g, '-').replace('4-X-4', '4X4') : _rentalCategory; //TEMP fix unless get proper result from RES
        _idRequired = raw.idRequired; //boolean
        _creditCardRequired = raw.creditCardRequired; //boolean
        _licenseRequired = raw.licenseRequired; //boolean
        _fuelDepositRequired = raw.fuelDepositRequired; //boolean
        _towbarAllowed = raw.towbarAllowed; //boolean
        _officeHoursApproval = raw.officeHoursApproval ? new RentalApproval(raw.officeHoursApproval) : _officeHoursApproval;
        _outOfHoursApproval = raw.outOfHoursApproval ? new RentalApproval(raw.outOfHoursApproval) : _outOfHoursApproval;
        _initialRental = raw.initialRental ? new InitialRental(raw.initialRental) : _initialRental;
        _extension = raw.extension ? new RentalExtension(raw.extension) : _extension;
        _vehicleOffRoadLimit = raw.vehicleOffRoadLimit; //time
        _coolOffPeriod = raw.coolOffPeriod ? new ConditionalHire(raw.coolOffPeriod) : _coolOffPeriod;
        _faultRestrictions = raw.faultRestrictions ? new CarHireFaultRestrictions(raw.faultRestrictions) : _faultRestrictions;
        _followOrRecoveryJob = raw.followOrRecoveryJob; //boolean
        _patrolCanBook = raw.patrolCanBook === false ? raw.patrolCanBook : _patrolCanBook;
        _defInitialHireDurationPayer = 'AA';
        _agileEnabled = raw.agileEnabled === false ? raw.agileEnabled : _agileEnabled;
        _isUKCustomerGrp = raw.isUKCustomerGrp === false ? raw.isUKCustomerGrp : _isUKCustomerGrp;
        _isExtProcessEnabled = raw.isExtProcessEnabled === false ? raw.isExtProcessEnabled : _isExtProcessEnabled;
        _isMaladminExtEnabled = raw.isMaladminExtEnabled === false ? raw.isMaladminExtEnabled : _isMaladminExtEnabled;
        _isPreAuthEnabled = raw.isPreAuthEnabled === false ? raw.isPreAuthEnabled : _isPreAuthEnabled;
        _isSAPNotifyEnabled = raw.isSAPNotifyEnabled === false ? raw.isSAPNotifyEnabled : _isSAPNotifyEnabled;
        _hireFleetTypes = raw.hireFleetTypes ? raw.hireFleetTypes : _hireFleetTypes;
    }

    _.extend(this, {
        rentalCategory: function rentalCategoryAccessor(val) {
            if (arguments.length) {
                _rentalCategory = val;
            } else {
                return _rentalCategory === CarRentalEntitlement.LIKE_FOR_LIKE ? CarRentalEntitlement.L4L : _rentalCategory;
            }
        },
        idRequired: function idRequiredAccessor(val) {
            return arguments.length ? (_idRequired = val) : _idRequired;
        },
        creditCardRequired: function creditCardRequiredAccessor(val) {
            return arguments.length ? (_creditCardRequired = val) : _creditCardRequired;
        },
        licenseRequired: function licenseRequiredAccessor(val) {
            return arguments.length ? (_licenseRequired = val) : _licenseRequired;
        },
        fuelDepositRequired: function fuelDepositRequiredAccessor(val) {
            return arguments.length ? (_fuelDepositRequired = val) : _fuelDepositRequired;
        },
        towbarAllowed: function towbarAllowedAccessor(val) {
            return arguments.length ? (_towbarAllowed = val) : _towbarAllowed;
        },
        officeHoursApproval: function officeHoursApprovalAccessor(val) {
            return arguments.length ? (_officeHoursApproval = val) : _officeHoursApproval;
        },
        outOfHoursApproval: function outOfHoursApprovalAccessor(val) {
            return arguments.length ? (_outOfHoursApproval = val) : _outOfHoursApproval;
        },
        initialRental: function initialRentalAccessor(val) {
            return arguments.length ? (_initialRental = val) : _initialRental;
        },
        extension: function extensionAccessor(val) {
            return arguments.length ? (_extension = val) : _extension;
        },
        vehicleOffRoadLimit: function vehicleOffRoadLimitAccessor(val) {
            return arguments.length ? (_vehicleOffRoadLimit = val) : _vehicleOffRoadLimit;
        },
        coolOffPeriod: function coolOffPeriodAccessor(val) {
            return arguments.length ? (_coolOffPeriod = val) : _coolOffPeriod;
        },
        faultRestrictions: function faultRestrictionsAccessor(val) {
            return arguments.length ? (_faultRestrictions = val) : _faultRestrictions;
        },
        followRecoveryRequired: function followRecoveryRequiredAccessor(val) {
            return arguments.length ? (_followOrRecoveryJob = val) : _followOrRecoveryJob;
        },
        canPatrolBook: function canPatrolBookAccessor(val) {
            return arguments.length ? (_patrolCanBook = val) : _patrolCanBook;
        },
        agileEnabled: function agileEnabledAccessor(val) {
            return arguments.length ? (_agileEnabled = val) : _agileEnabled;
        },
        defInitialHireDurationPayer: function defInitialHireDurationPayerAccessor(val) {
            return arguments.length ? (_defInitialHireDurationPayer = val) : _defInitialHireDurationPayer;
        },
        isUKCustomerGrp: function isUKCustomerGrpAccessor(val) {
            return arguments.length ? (_isUKCustomerGrp = val) : _isUKCustomerGrp;
        },

        isExtProcessEnabled: function isExtProcessEnabledAccessor(val) {
            return arguments.length ? (_isExtProcessEnabled = val) : _isExtProcessEnabled;
        },
        isMaladminExtEnabled: function isMaladminExtEnabledAccessor(val) {
            return arguments.length ? (_isMaladminExtEnabled = val) : _isMaladminExtEnabled;
        },
        isPreAuthEnabled: function isPreAuthEnabledAccessor(val) {
            return arguments.length ? (_isPreAuthEnabled = val) : _isPreAuthEnabled;
        },
        isSAPNotifyEnabled: function isSAPNotifyEnabledAccessor(val) {
            return arguments.length ? (_isSAPNotifyEnabled = val) : _isSAPNotifyEnabled;
        },
        hireFleetTypes: function hireFleetTypesAccessor(val) {
            return arguments.length ? (_hireFleetTypes = val) : _hireFleetTypes;
        },

        toJSON: function serialise() {
            return {
                rentalCategory: _rentalCategory,
                idRequired: _idRequired,
                creditCardRequired: _creditCardRequired,
                licenseRequired: _licenseRequired,
                fuelDepositRequired: _fuelDepositRequired,
                towbarAllowed: _towbarAllowed,
                officeHoursApproval: _officeHoursApproval.toJSON(),
                outOfHoursApproval: _outOfHoursApproval.toJSON(),
                initialRental: _initialRental.toJSON(),
                extension: _extension.toJSON(),
                vehicleOffRoadLimit: _vehicleOffRoadLimit,
                coolOffPeriod: _coolOffPeriod.toJSON(),
                faultRestrictions: _faultRestrictions.toJSON(),
                followOrRecoveryJob: _followOrRecoveryJob,
                patrolCanBook: _patrolCanBook,
                agileEnabled: _agileEnabled,
                defInitialHireDurationPayer: _defInitialHireDurationPayer,
                isUKCustomerGrp: _isUKCustomerGrp,
                isExtProcessEnabled: _isExtProcessEnabled,
                isMaladminExtEnabled: _isMaladminExtEnabled,
                isPreAuthEnabled: _isPreAuthEnabled,
                isSAPNotifyEnabled: _isSAPNotifyEnabled,
                hireFleetTypes: _hireFleetTypes,
            };
        },
    });
}

module.exports = CarRentalEntitlement;

CarRentalEntitlement.LIKE_FOR_LIKE = 'LIKE-FOR-LIKE';
CarRentalEntitlement.L4L = 'L4L';
