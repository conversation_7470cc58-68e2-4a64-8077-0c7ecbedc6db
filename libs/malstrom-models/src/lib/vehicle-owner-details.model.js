'user strict';

var _ = require('lodash');

function OwnerDetails(rawData) {
    var _title = null,
        _firstName = null,
        _surName = null,
        _contactNumber = null,
        _email = null,
        _address = null,
        _postCode = null;

    if (rawData) {
        _title = rawData.title;
        _firstName = rawData.firstName;
        _surName = rawData.surName;
        _contactNumber = rawData.contactNumber;
        _email = rawData.email;
        _address = rawData.address;
        _postCode = rawData.postCode;
    }

    _.extend(this, {
        title: function titleAccessor(val) {
            return arguments.length > 0 ? (_title = val) : _title;
        },
        firstName: function firstNameAccessor(val) {
            return arguments.length > 0 ? (_firstName = val) : _firstName;
        },
        surName: function surNameAccessor(val) {
            return arguments.length > 0 ? (_surName = val) : _surName;
        },
        contactNumber: function contactNumberAccessor(val) {
            return arguments.length > 0 ? (_contactNumber = val) : _contactNumber;
        },
        email: function emailAccessor(val) {
            return arguments.length > 0 ? (_email = val) : _email;
        },
        address: function addressAccessor(val) {
            return arguments.length > 0 ? (_address = val) : _address;
        },
        postCode: function postCodeAccessor(val) {
            return arguments.length > 0 ? (_postCode = val) : _postCode;
        },

        toJSON: function serialiseObject() {
            return {
                title: _title,
                firstName: _firstName,
                surName: _surName,
                contactNumber: _contactNumber,
                email: _email,
                address: _address,
                postCode: _postCode,
            };
        },
    });
}

module.exports = OwnerDetails;
