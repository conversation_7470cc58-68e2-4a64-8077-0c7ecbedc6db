'use strict';
var _ = require('lodash');

/**
 * @typedef Entitlement
 * @constructor
 */
function Cover(raw) {
    var model = this,
        _type = null,
        _startDate = null,
        _endDate = null,
        _package = null,
        _noService = false;

    if (raw) {
        _type = raw.type || _type;
        _startDate = raw.startDate ? new Date(raw.startDate) : _startDate;
        _endDate = raw.endDate ? new Date(raw.endDate) : _endDate;
        _package = raw.package || _package;
        _noService = raw.noService || _noService;
    }

    _.extend(model, {
        type: function typeAccessor(val) {
            return arguments.length ? (_type = val) : _type;
        },
        startDate: function startDateAccessor(val) {
            return arguments.length ? (_startDate = val) : _startDate;
        },
        endDate: function endDateAccessor(val) {
            return arguments.length ? (_endDate = val) : _endDate;
        },
        package: function packageAccessor(val) {
            return arguments.length ? (_package = val) : _package;
        },
        noService: function noServiceAccessor(val) {
            return arguments.length ? (_noService = val) : _noService;
        },
        isExpired: function isExpired() {
            return _endDate < new Date();
        },

        toJSON: function toJSON() {
            return {
                type: _type,
                startDate: _startDate,
                endDate: _endDate,
                package: _package,
                noService: _noService,
            };
        },
    });
}

module.exports = Cover;
