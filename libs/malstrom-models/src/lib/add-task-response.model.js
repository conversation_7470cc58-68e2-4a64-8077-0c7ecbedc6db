'use strict';

const { Entitlement } = require('@aa/data-models/aux/entitlement');
var _ = require('lodash'),
    Task = require('./task.model');

function AddTaskResponse(raw) {
    var _model = this,
        _entitlement = null,
        _task = null;

    if (raw) {
        _entitlement = raw.entitlement ? new Entitlement(raw.entitlement) : _entitlement;
        _task = raw.task ? new Task(raw.task) : _task;
    }

    _.extend(_model, {
        entitlement: function entitlementAccessor(val) {
            return arguments.length > 0 ? (_entitlement = val) : _entitlement;
        },
        task: function taskAccessor(val) {
            return arguments.length > 0 ? (_task = val) : _task;
        },
        toJSON: function toJSON() {
            return {
                entitlement: _entitlement ? _entitlement.toJSON() : null,
                task: _task ? _task.toJSON() : null
            };
        }
    });
}

module.exports = AddTaskResponse;
