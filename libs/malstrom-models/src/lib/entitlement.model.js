'use strict';

import { hasEuropeanCover } from '@aa/data-models/aux/entitlement';
import { CarRentalEntitlement as CarHireEntitlement } from '@aa/data-models/aux/entitlement';
const { Product, Benefit, Cover, EntitlementContact } = require('@aa/data-models/aux/entitlement');
const { Usage, Policy, Vehicle, Narrative, CorrespondenceAddress, CustomerGroup, Country } = require('@aa/data-models/common');
var _ = require('lodash'),
    _arrayObjectToJSON = require('./factories/array-object-to-json.factory');

/**
 * @typedef Entitlement
 * @constructor
 */
function Entitlement(raw) {
    var model = this,
        _systemId = null,
        _contact = new EntitlementContact(),
        _policy = new Policy(),
        _vehicle = new Vehicle(),
        _cover = new Cover(),
        _correspondenceAddress = null,
        _packages = [],
        _products = [],
        _narratives = [],
        _fairPlay = null,
        _vipServiceCode = null,
        _benefits = [],
        _entitlementVariableData = [], // returning array of vanilla javascript object i.e. no accessor functions
        _memberDetailsForPrime = [],
        _riskCode = null,
        _extendedProductCategory = false,
        _additionalBrcVRNs = [],
        _payForUse = false,
        _carRental = null,
        _isNoServiceAccount = null;

    if (raw) {
        _systemId = raw.systemId;
        _fairPlay = raw.fairPlay;
        _vipServiceCode = raw.vipServiceCode ? raw.vipServiceCode : _vipServiceCode;

        _memberDetailsForPrime = raw.memberDetailsForPrime || [];
        _contact = raw.contact ? new EntitlementContact(raw.contact) : _contact;
        _correspondenceAddress = raw.correspondenceAddress ? new CorrespondenceAddress(raw.correspondenceAddress) : _correspondenceAddress;
        _policy = raw.policy ? new Policy(raw.policy) : _policy;
        _vehicle = raw.vehicle ? new Vehicle(raw.vehicle) : _vehicle;
        _entitlementVariableData = raw.entitlementVariableData || [];
        _riskCode = raw.riskCode;
        _cover = raw.cover ? new Cover(raw.cover) : _cover;
        _extendedProductCategory = raw.extendedProductCategory === true;
        _payForUse = raw.payForUse === true;
        _additionalBrcVRNs = raw._additionalBrcVRNs || [];
        _isNoServiceAccount = raw.hasOwnProperty('isNoServiceAccount') ? raw.isNoServiceAccount : null;

        _.forEach(raw.benefits, function _forEachBenefit(rawBenefit) {
            _benefits.push(new Benefit(rawBenefit));
        });

        _.forEach(raw.packages, function _forEachProduct(rawPackages) {
            _packages.push(new Product(rawPackages));
        });

        _.forEach(raw.products, function _forEachProduct(rawProduct) {
            _products.push(new Product(rawProduct));
        });

        _.forEach(raw.narratives, function _forEachNarratives(rawNarrative) {
            _narratives.push(new Narrative(rawNarrative));
        });
        _carRental = raw.carRental ? new CarHireEntitlement(raw.carRental) : _carRental;
    }

    function _hasVIPPackage() {
        let VIPProducts = ['VIP', 'VIP - Royal Household', 'RDA'];
        return !_.isUndefined(
            _.find(_products, function _forEachProduct(product) {
                return product.name() && VIPProducts.indexOf(product.name()) > -1;
            })
        );
    }

    function _hasStandby() {
        return !_.isUndefined(
            _.find(_products, function _forEachProduct(product) {
                return product.name() && product.name().indexOf('Standby') > -1;
            })
        );
    }

    function _hasBrc() {
        return !_.isUndefined(
            _.find(_products, function _forEachProduct(product) {
                let benefitCode = product.benefitCode() ? product.benefitCode().toUpperCase() : product.benefitCode();
                return benefitCode === 'BRC';
            })
        );
    }

    function _hasLocalDriver() {
        const result = !_.isUndefined(
            _.find(_products, function _forEachProduct(product) {
                return product.name() && product.name().indexOf('Local Driver') > -1;
            })
        );
        return result;
    }

    _.extend(model, {
        systemId: function systemIdAccessor(val) {
            return arguments.length ? (_systemId = val) : _systemId;
        },
        contact: function contactAccessor(val) {
            return arguments.length ? (_contact = val) : _contact;
        },
        policy: function policyAccessor(val) {
            return arguments.length ? (_policy = val) : _policy;
        },
        vehicle: function vehicleAccessor(val) {
            return arguments.length ? (_vehicle = val) : _vehicle;
        },
        packages: function packagesAccessor(val) {
            return arguments.length ? (_packages = val) : _packages;
        },
        products: function productsAccessor(val) {
            return arguments.length ? (_products = val) : _products;
        },
        narratives: function narrativesAccessor(val) {
            return arguments.length ? (_narratives = val) : _narratives;
        },
        fairPlay: function fairPlayAccessor(val) {
            return arguments.length ? (_fairPlay = val) : _fairPlay;
        },
        vipServiceCode: function vipServiceCodeAccessor(val) {
            return arguments.length ? (_vipServiceCode = val) : _vipServiceCode;
        },
        benefits: function benefitsAccessor(val) {
            return arguments.length ? (_benefits = val) : _benefits;
        },
        memberDetailsForPrime: function memberDetailsForPrimeAccessor(val) {
            return arguments.length ? (_memberDetailsForPrime = val) : _memberDetailsForPrime;
        },
        entitlementVariableData: function entitlementVariableDataAccessor(val) {
            return arguments.length ? (_entitlementVariableData = val) : _entitlementVariableData;
        },
        riskCode: function riskCodeAccessor(val) {
            return arguments.length ? (_riskCode = val) : _riskCode;
        },
        cover: function coverAccessor(val) {
            return arguments.length ? (_cover = val) : _cover;
        },
        extendedProductCategory: function extendedProductCategoryAccessor(val) {
            return arguments.length ? (_extendedProductCategory = val) : _extendedProductCategory;
        },
        payForUse: function payForUseAccessor(val) {
            return arguments.length ? (_payForUse = val) : _payForUse;
        },
        additionalBrcVRNs: function additionalBrcVRNsAccessor(val) {
            return arguments.length ? (_additionalBrcVRNs = val) : _additionalBrcVRNs;
        },
        hasProduct: function hasProduct(productCode) {
            if (!productCode) {
                return false;
            }

            return !!_.find(_products, function _findProduct(product) {
                return product.code() === productCode;
            });
        },
        hasValidProductByName: function (productName) {
            if (!productName) {
                return false;
            }

            return !!_.find(_products, function _findProduct(p) {
                return p.name().toLowerCase() === productName.toLowerCase() && !p.expired();
            });
        },
        hasExpiredProductByName: function (productName) {
            if (!productName) {
                return false;
            }

            return !!_.find(_products, function _findProduct(p) {
                return p.name().toLowerCase() === productName.toLowerCase() && p.expired();
            });
        },
        hasBenefit: function hasBenefit(benefitCode) {
            if (!benefitCode) {
                return false;
            }

            return !!_.find(_benefits, function _findBenefit(benefit) {
                return benefit.code() === benefitCode;
            });
        },
        /*
         *   Returns the product object with name 'productName'
         *   Checking for benefitCode because Package is also saved with entitlement.products()
         *   Roadside is a package and a product and may return the wrong result
         */
        getProduct: function getProduct(productName) {
            if (model.hasValidProductByName(productName)) {
                return _.find(_products, function _findProduct(p) {
                    return p.name().toLowerCase() === productName.toLowerCase() && !!p.benefitCode() && p;
                });
            }
            return null;
        },
        isHomeInsurance: function isHomeInsurance() {
            return _systemId && _systemId.trim() === Entitlement.TIA_HOME_INS;
        },
        isEuropeanBreakdownCover: function isEuropeanBreakdownCover() {
            return _products.length ? _products[0].name().toUpperCase().indexOf('EBC') > -1 : false;
        },
        isVIP: function isVIP() {
            return _vipServiceCode !== null || _hasVIPPackage();
        },
        correspondenceAddress: function correspondenceAddressAccessor(val) {
            return arguments.length ? (_correspondenceAddress = val) : _correspondenceAddress;
        },
        isCorrespondenceAddressInvalid: function isCorrespondenceAddressInvalidAccessor() {
            return _correspondenceAddress == null ? true : _correspondenceAddress && _correspondenceAddress.valid();
        },
        getOverseasArcB2BCountry: function getOverseasArcB2BCountry() {
            if (_policy.customerGroup().code() === CustomerGroup.OVERSEAS) {
                const overseasProduct = _products.find((product) => {
                    const productName = product.name() ? product.name().toLowerCase() : '';
                    return productName && productName.includes('overseas') && productName.includes('arc b2b') && !productName.includes('vw only');
                });

                if (overseasProduct && overseasProduct.name()) {
                    const overseasProductName = overseasProduct.name().toLowerCase();
                    if (overseasProductName.includes('other')) {
                        return 'other';
                    } else {
                        return overseasProductName.replace(' overseas arc b2b', '');
                    }
                }
            }
            return null;
        },
        carRental: function carRentalAccessor(val) {
            return arguments.length ? (_carRental = val) : _carRental;
        },
        isStandby: _hasStandby,
        hasBrc: _hasBrc,
        isLocalDriver: _hasLocalDriver,
        maxCallouts: () => {
            let _maxCallouts;

            //RBAUAA-2019 - Aurora is only 1. Wonder why we don't use the product's "usage" feature.
            if (_policy.customerGroup().isAurora()) {
                return 1;
            }
            if (_policy.customerGroup().isCDLVRoadsideAddOn()) {
                return 3;
            }
            if (_policy.customerGroup().isChase()) {
                return 4;
            }

            const maxCalloutsByMembershipType = (membershipType) => {
                const key = Object.keys(Entitlement.DEMAND_DEFLECTION.MAX_CALLOUTS_BY_MEMBERSHIP_TYPE).find((key) => {
                    return membershipType && membershipType.match(key);
                });
                return key && Entitlement.DEMAND_DEFLECTION.MAX_CALLOUTS_BY_MEMBERSHIP_TYPE[key];
            };

            //if use have data in the usage section, we use it
            _products.forEach((product) => {
                product.usage().forEach((usage) => {
                    if (usage.unit() === Usage.UNIT_CLAIM && usage.type() === Usage.TYPE_USAGE) {
                        _maxCallouts = usage.use();
                    }
                });
            });

            //if we don't have usage data we might be able to decide via the membershipType
            //Yeah, I know. I don't like this either.
            if (!_maxCallouts) {
                _maxCallouts = maxCalloutsByMembershipType(_policy.membershipType());
            }

            //still nothing? let's use the defaiult
            if (!_maxCallouts) {
                _maxCallouts = Entitlement.DEFAULT_MAX_CALLOUTS;
            }

            return _maxCallouts;
        },
        maxDistance: () => {
            let _maxDistance;
            _products.forEach((product) => {
                product.usage().forEach((usage) => {
                    if (usage.unit() === Usage.UNIT_CLAIM && usage.type() === Usage.TYPE_DISTANCE_MILES) {
                        _maxDistance = usage.use();
                    }
                });
            });
            return _maxDistance || Entitlement.DEFAULT_MAX_DISTANCE;
        },
        isLocalDriverTooFar: (distanceAsCrowFlies) => {
            if (!model.isLocalDriver()) {
                return false;
            }
            const maxDistance = model.maxDistance();

            return distanceAsCrowFlies > maxDistance;
        },
        isNoServiceAccount: function isNoServiceAccountAccessor() {
            return ['BCASP203978', 'BCASP423570', 'BCASP423575', 'BCASP503980', 'BCASP529612', 'BCASP640299', '*********', '*********', '*********', '*********'].indexOf(_policy.policyNumber()) > -1;
            // return arguments.length ? _isNoServiceAccount = val : _isNoServiceAccount;
        },
        isActive: function isActive() {
            const policy = this.policy();
            // if not enabled for "active check" always active
            if (!CustomerGroup.ENTITLEMENT_ACTIVE_CHECK_ENABLED.includes(policy.customerGroup().code())) {
                return true;
            }

            const product = this.products().find((entry) => entry.benefitCode() === 'B');

            // if no breakdown product - active
            if (!product) {
                return true;
            }

            // if breakdown product expired - not active
            if (product.expired()) {
                return false;
            }

            const now = new Date();

            // assert product dates
            const productStartDate = product.claimFromDateTime() || product.startDate();
            const productEndDate = product.endDate();

            // if breakdown product has dates
            if (productStartDate && productEndDate) {
                return productStartDate < now && now < productEndDate;
            }

            // if incomplete or missing product dates, fallback to the policy dates
            const policyStartDate = policy.startDate();
            const policyEndDate = policy.endDate();
            if (policyStartDate && policyEndDate) {
                return policyStartDate < now && now < policyEndDate;
            }

            // if none of above checks then not active
            return false;
            //     // https://rh0113p.theaa.local:8144/res/read/rest/schedule?membershipNo=15148394179896
            //     // https://rh0113p.theaa.local:8144/res/read/rest/vehicle?vrn=MO68XJF
            //     // https://rh0113p.theaa.local:8144/res/read/rest/schedule?membershipNo=6356013346130288
        },
        isDisabled: function isDisabled() {
            const policy = this.policy();
            if (policy.memberStatus() && policy.memberStatus().toLowerCase() === 'stopped' && policy.endDate() < new Date()) {
                return true;
            }
            return false;
        },
        isCuvCovered: function isCuvCovered(vrn) {
            return _products.some((product) => product.isCUV() && product.isVehicleCovered(vrn));
        },
        isBrcCovered: function isBrcCovered(vrn) {
            return _products.some((product) => product.isBRC() && product.isVehicleCovered(vrn));
        },
        hasEuropeanCover: function hasEuropeanCoverFunction() {
            return hasEuropeanCover(this);
        },
        isValidLocale: function isValidLocaleFunction(locale) {
            return isValidLocale(locale);
        },
        toJSON: function toJSON() {
            return {
                systemId: _systemId,
                fairPlay: _fairPlay,
                vipServiceCode: _vipServiceCode,
                contact: _contact ? _contact.toJSON() : null,
                policy: _policy ? _policy.toJSON() : null,
                vehicle: _vehicle ? _vehicle.toJSON() : null,
                packages: _arrayObjectToJSON(_packages),
                products: _arrayObjectToJSON(_products),
                narratives: _arrayObjectToJSON(_narratives),
                memberDetailsForPrime: _memberDetailsForPrime,
                benefits: _arrayObjectToJSON(_benefits),
                entitlementVariableData: _entitlementVariableData,
                riskCode: _riskCode,
                cover: _cover ? _cover.toJSON() : null,
                extendedProductCategory: _extendedProductCategory,
                payForUse: _payForUse,
                additionalBrcVRNs: _arrayObjectToJSON(_additionalBrcVRNs),
                correspondenceAddress: _correspondenceAddress ? _correspondenceAddress.toJSON() : _correspondenceAddress,
                carRental: _carRental ? _carRental.toJSON() : _carRental
            };
        }
    });
}

//system ids
Entitlement.TIA_HOME_INS = 'TIA_HOME_INS';

//this is for usage
Entitlement.DEFAULT_MAX_CALLOUTS = 1000;
Entitlement.DEFAULT_MAX_DISTANCE = 20;

//hmm... wonder why isn't everything driven by usage... oh well....
Entitlement.DEMAND_DEFLECTION = {
    MAX_CALLOUTS_BY_MEMBERSHIP_TYPE: {
        Single: 7,
        Vehicle: 7,
        Joint: 8,
        Family: 9
    }
};

module.exports = Entitlement;
