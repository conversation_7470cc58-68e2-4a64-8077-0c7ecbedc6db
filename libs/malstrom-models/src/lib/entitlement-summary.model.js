'use strict';

var _ = require('lodash'),
    CustomerGroup = require('./customer-group.model'),
    RefCode = require('./ref-code.model'),
    Benefit = require('./benefit.model'),
    FlexFieldsHelper = require('./factories/flexible-fields-helper.factory');

function EntitlementSummary(raw) {
    var _model = this,
        _customerGroup = new CustomerGroup(),
        _memberDetails = [],
        _packageName = null,
        _productPackage = new RefCode(),
        _benefits = [],
        _riskCode = null,
        _textTopics = [],
        _variableData = [],
        _fairPlay = null;

    if (raw) {
        // _customerGroup = raw.customerGroup ? new CustomerGroup(raw.customerGroup) : _customerGroup;
        if (raw.customerGroup) {
            _customerGroup = new CustomerGroup(raw.customerGroup);
        } else if (raw.custGroup) {
            _customerGroup = new CustomerGroup(raw.custGroup);
        }
        _memberDetails = raw.memberDetails ? raw.memberDetails : _memberDetails;
        _productPackage = raw.productPackage ? new RefCode(raw.productPackage) : _productPackage;

        _.forEach(raw.benefits, function (benefit) {
            _benefits.push(new Benefit(benefit));
        });
        _riskCode = raw.riskCode ? raw.riskCode : _riskCode;

        // res flexfields are stored in textTopics & variableData respectively
        // those are store are plain objects there are not accessor for these ..
        _textTopics = raw.textTopics ? raw.textTopics : _textTopics;
        _variableData = raw.variableData ? raw.variableData : _variableData;
        _fairPlay = raw.fairPlay ? raw.fairPlay : _fairPlay;
    }

    _.extend(_model, {
        customerGroup: function custGroupAccessor(val) {
            return arguments.length ? (_customerGroup = val) : _customerGroup;
        },
        memberDetails: function memberDetailsAccessor(val) {
            let memberDetails = arguments.length ? (_memberDetails = val) : _memberDetails;

            //NOTE: this is a temporary fix to debug prime for missing writes and will be removed soon
            //Check with Rahul and Sandhya for more details
            if (memberDetails.length) {
                _.each(memberDetails, (memberDetail, index) => {
                    if (memberDetail && memberDetail.trim().substring(0, 6) === 'Expiry') {
                        memberDetails[index] = memberDetail.trim().substring(0, 19);
                    }
                });
            }

            return memberDetails;
        },
        productPackage: function productPackageAccessor(val) {
            return arguments.length ? (_productPackage = val) : _productPackage;
        },
        benefits: function benefitsAccessor(val) {
            return arguments.length ? (_benefits = val) : _benefits;
        },
        packageName: function packageNameAccessor(val) {
            return arguments.length ? (_packageName = val) : _packageName;
        },
        riskCode: function riskCodeAccessor(val) {
            return arguments.length ? (_riskCode = val) : _riskCode;
        },
        memberName: function memberNameAccessor() {
            //first entry is always the member name
            return _memberDetails[0];
        },
        hasPFU: function hasPFU() {
            return !_.isEmpty(
                _.find(_benefits, function (bItem) {
                    return ['PFU', 'BPU', 'PPU', 'RPU', 'LPU', 'HPU', 'APU'].indexOf(bItem.code()) > -1;
                })
            );
        },
        hasRelay: function hasRelay() {
            return !_.isEmpty(
                _.find(_benefits, function (bItem) {
                    return ['R', 'PFU', 'BPU', 'PPU', 'RPU', 'LPU', 'HPU', 'APU'].indexOf(bItem.code()) > -1;
                })
            );
        },
        hasRelayPlus: function hasRelay() {
            return !_.isEmpty(
                _.find(_benefits, function _forEachBenefit(benefit) {
                    return ['P'].indexOf(benefit.code()) > -1;
                })
            );
        },
        hasHomestart: function hasHomestart() {
            return !_.isEmpty(
                _.find(_benefits, function (bItem) {
                    return ['H', 'HPU', 'VH'].indexOf(bItem.code()) > -1;
                })
            );
        },
        hasBRC: function hasBRC() {
            return !_.isEmpty(
                _.find(_benefits, function (bItem) {
                    return ['BRC'].indexOf(bItem.code()) > -1;
                })
            );
        },
        taskCreationSource: function taskCreationSource(details) {
            const { fieldNumber, value } = details;
            // need to search for fieldNumber 847
            const idx = _.findIndex(_variableData, (item) => {
                return item.flexFieldFormat.fieldNumber === fieldNumber;
            });

            if (idx >= 0) {
                _variableData[idx].flexFieldData.value = value;
                _variableData[idx].flexFieldFormat.dataSize = value.length;
            }
        },
        callType: function callTypeAccessor(details) {
            const { fieldNumber, value } = details;
            // need to search for fieldNumber 901
            const idx = _.findIndex(_variableData, (item) => {
                return item.flexFieldFormat.fieldNumber === fieldNumber;
            });

            if (idx >= 0) {
                _variableData[idx].flexFieldData.value = value;
                _variableData[idx].flexFieldFormat.dataSize = value.length;
            }
        },
        /***
         * member address accessor that returns an array rather than an address object -we won't be able
         * to split the address into its constituent parts. This is used for display purpose only
         * @returns {Array}
         */
        memberAddress: function memberAddresAccessor() {
            var address = [];

            /* 	first entry of member details is member name,
				so start at the second and loop through until a blank line is reached. */

            _.forEach(_memberDetails, function (item, i) {
                if (i !== 0) {
                    if (item === '') {
                        return false;
                    }

                    address.push(item);
                }
            });

            return address;
        },
        textTopics: function textTopicsAccessor(val) {
            return arguments.length ? (_textTopics = val) : _textTopics;
        },
        variableData: function variableDataAccessor(val) {
            return arguments.length ? (_variableData = val) : _variableData;
        },
        fairPlay: function fairPlayAccessor(val) {
            return arguments.length ? (_fairPlay = val) : _fairPlay;
        },
        isEntitlementExhausted: function isEntitlementExhausted() {
            return _fairPlay === EntitlementSummary.ENTITLEMENT_EXHAUSTED;
        },
        isValidMandatoryFlexFields: () => {
            return FlexFieldsHelper.areMandatoryFlexFieldsComplete(_variableData);
        },
        setFleetPfuManufacturerFields: function setFleetPfuManufacturerFields(entitlement) {
            return entitlement.customerGroup().isFleet() || entitlement.customerGroup().isPFU() || entitlement.customerGroup().isManufacturer() || entitlement.customerGroup().code() === 'MCR';
        },
        toJSON: function toJSON() {
            var benefitsJSON = [];
            _.forEach(_benefits, function (benefit) {
                benefitsJSON.push(benefit.toJSON());
            });
            return {
                customerGroup: _customerGroup.toJSON(),
                memberDetails: _memberDetails,
                productPackage: _productPackage.toJSON(),
                packageName: _packageName,
                benefits: benefitsJSON,
                riskCode: _riskCode,
                textTopics: _textTopics,
                variableData: _variableData,
                fairPlay: _fairPlay
            };
        }
    });
}

EntitlementSummary.ENTITLEMENT_EXHAUSTED = 26;
module.exports = EntitlementSummary;
