var _ = require('lodash');

function AddContactDetails(raw) {
    var model = this,
        _email = null,
        _dialCode = 'United Kingdom (+44)',
        _driverDialCode = 'United Kingdom (+44)';

    if (raw) {
        _email = raw.email;
        _dialCode = raw.dialCode ? raw.dialCode : _dialCode;
        _driverDialCode = raw.driverDialCode ? raw.driverDialCode : _driverDialCode;
    }
    _.extend(model, {
        email: function emailAccessor(val) {
            return arguments.length ? (_email = val) : _email;
        },
        dialCode: function dialCodeAccessor(val) {
            return arguments.length ? (_dialCode = val) : _dialCode;
        },
        driverDialCode: function driverDialCodeAccessor(val) {
            return arguments.length ? (_driverDialCode = val) : _driverDialCode;
        },

        toJSON: function toJSON() {
            return {
                email: _email,
                dialCode: _dialCode,
                driverDialCode: _driverDialCode
            };
        }
    });
}

module.exports = AddContactDetails;
