'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    AbstractTask = require('./abstract-task.model'),
    Contact = require('./contact.model'),
    RefCode = require('./ref-code.model'),
    Schedule = require('./task-schedule.model');

function SEUpdateTask(raw) {
    var model = this,
        _id = -1,
        _customerRequestId = -1,
        _taskType = new RefCode({
            code: 'SEU',
            name: 'SERVICE ENTITLEMENT UPDATE'
        }),
        _schedule = new Schedule(),
        _packageId = -1,
        _amount = null,
        _memberName = null,
        _address = null,
        _contact = null,
        _operatorId = -1,
        _username = null;

    if (raw) {
        _id = raw.id ? raw.id : _id;
        _customerRequestId = raw.customerRequestId ? raw.customerRequestId : _customerRequestId;
        _packageId = raw.packageId ? raw.packageId : _packageId;
        _amount = raw.amount ? raw.amount : _amount;
        _memberName = raw.memberName ? raw.memberName : _memberName;
        _address = raw.address ? new Address(raw.address) : _address;
        _contact = raw.contact ? new Contact(raw.contact) : _contact;
        _operatorId = raw.operatorId ? raw.operatorId : _operatorId;
        _username = raw.username ? raw.username : _username;
        _schedule = raw.schedule ? new Schedule(raw.schedule) : _schedule;
    }

    _.extend(model, new AbstractTask(), {
        id: function idAccessor(val) {
            return arguments.length ? (_id = val) : _id;
        },
        customerRequestId: function customerRequestIdAccessor(val) {
            return arguments.length ? (_customerRequestId = val) : _customerRequestId;
        },
        packageId: function packageIdAccessor(val) {
            return arguments.length ? (_packageId = val) : _packageId;
        },
        amount: function amountAccessor(val) {
            return arguments.length ? (_amount = val) : _amount;
        },
        memberName: function memberNameAccessor(val) {
            return arguments.length ? (_memberName = val) : _memberName;
        },
        address: function addressAccessor(val) {
            return arguments.length ? (_address = val) : _address;
        },
        contact: function contactAccessor(val) {
            return arguments.length ? (_contact = val) : _contact;
        },
        operatorId: function operatorIdAccessor(val) {
            return arguments.length ? (_operatorId = val) : _operatorId;
        },
        username: function usernameAccessor(val) {
            return arguments.length ? (_username = val) : _username;
        },
        taskType: function taskTypeAccessor() {
            return _taskType;
        },
        schedule: function scheduleAccessor(val) {
            return arguments.length ? (_schedule = val) : _schedule;
        },
        toJSON: function toJSON() {
            return {
                id: _id,
                customerRequestId: _customerRequestId,
                packageId: _packageId,
                amount: _amount,
                memberName: _memberName,
                address: _address ? _address.toJSON() : null,
                contact: _contact ? _contact.toJSON() : null,
                operatorId: _operatorId,
                username: _username,
                taskType: _taskType.toJSON(),
                schedule: _schedule ? _schedule.toJSON() : null
            };
        }
    });
}

module.exports = SEUpdateTask;
