var _ = require('lodash');

/**
 * @typedef Vulnerability
 * @constructor
 */
function Vulnerability(raw) {
    var model = this,
        _vulnerabilities = [],
        _outcomeType = null,
        _outcome = null;

    if (raw) {
        _vulnerabilities = raw.vulnerabilities;
        _outcomeType = raw.outcomeType;
        _outcome = raw.outcome;
    }

    _.extend(model, {
        vulnerabilities: function vulnerabilitiesAccessor(val) {
            return arguments.length ? (_vulnerabilities = val) : _vulnerabilities;
        },
        outcomeType: function outcomeTypeAccessor(val) {
            return arguments.length ? (_outcomeType = val) : _outcomeType;
        },
        outcome: function outcomeAccessor(val) {
            return arguments.length ? (_outcome = val) : _outcome;
        },
        isVulnerable: function vulnerableAccessor() {
            return !!(_vulnerabilities && _vulnerabilities.length);
        },
        toJSON: function toJSON() {
            return {
                vulnerabilities: _vulnerabilities,
                outcomeType: _outcomeType,
                outcome: _outcome,
            };
        },
    });
}

module.exports = Vulnerability;
