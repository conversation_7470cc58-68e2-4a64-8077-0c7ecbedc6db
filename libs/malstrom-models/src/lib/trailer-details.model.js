'user strict';

var _ = require('lodash');

function TrailerDetails(rawData) {
    var model = this,
        _make = null,
        _model = null,
        _colour = null,
        _type = null,
        _axles = null,
        _twinned = null,
        _hookUp = null,
        _curSymbol = null,
        _value = null,
        _abandonedFlag = null,
        _diagnosis = null,
        _fault = null,
        _notes = null,
        _yearConstructed = null,
        _weight = null,
        _length = null,
        _width = null,
        _height = null;

    if (rawData) {
        _make = rawData.make ? rawData.make : null;
        _model = rawData.model ? rawData.model : null;
        _colour = rawData.colour ? rawData.colour : null;
        _type = rawData.type ? rawData.type : null;
        _axles = rawData.axles ? rawData.axles : null;
        _twinned = rawData.twinned ? rawData.twinned : null;
        _hookUp = rawData.hookUp ? rawData.hookUp : null;
        _curSymbol = rawData.curSymbol ? rawData.curSymbol : null;
        _value = rawData.value ? rawData.value : null;
        _abandonedFlag = rawData.abandonedFlag ? rawData.abandonedFlag : null;
        _yearConstructed = rawData.yearConstructed ? rawData.yearConstructed : null;
        _diagnosis = rawData.diagnosis ? rawData.diagnosis : null;
        _fault = rawData.fault ? rawData.fault : null;
        _notes = rawData.notes ? rawData.notes : null;
        _weight = rawData.weight ? rawData.weight : null;
        _length = rawData.length ? rawData.length : null;
        _height = rawData.height ? rawData.height : null;
        _width = rawData.width ? rawData.width : null;
    }

    _.extend(model, {
        make: function makeAccessor(val) {
            return arguments.length > 0 ? (_make = val) : _make;
        },
        model: function modelAccessor(val) {
            return arguments.length > 0 ? (_model = val) : _model;
        },
        colour: function colourAccessor(val) {
            return arguments.length > 0 ? (_colour = val) : _colour;
        },
        type: function typeAccessor(val) {
            return arguments.length > 0 ? (_type = val) : _type;
        },
        axles: function axlesAccessor(val) {
            return arguments.length > 0 ? (_axles = val) : _axles;
        },
        twinned: function twinnedAccessor(val) {
            return arguments.length > 0 ? (_twinned = val) : _twinned;
        },

        hookUp: function hookUpAccessor(val) {
            return arguments.length > 0 ? (_hookUp = val) : _hookUp;
        },
        curSymbol: function curSymbolAccessor(val) {
            return arguments.length > 0 ? (_curSymbol = val) : _curSymbol;
        },
        value: function valueAccessor(val) {
            return arguments.length > 0 ? (_value = val) : _value;
        },
        abandonedFlag: function abandonedFlagAccessor(val) {
            return arguments.length > 0 ? (_abandonedFlag = val) : _abandonedFlag;
        },
        diagnosis: function diagnosisAccessor(val) {
            return arguments.length > 0 ? (_diagnosis = val) : _diagnosis;
        },
        fault: function faultAccessor(val) {
            return arguments.length > 0 ? (_fault = val) : _fault;
        },

        notes: function notesAccessor(val) {
            return arguments.length > 0 ? (_notes = val) : _notes;
        },
        yearConstructed: function yearConstructedAccessor(val) {
            return arguments.length > 0 ? (_yearConstructed = val) : _yearConstructed;
        },
        weight: function weightAccessor(val) {
            return arguments.length > 0 ? (_weight = val) : _weight;
        },
        length: function lengthAccessor(val) {
            return arguments.length > 0 ? (_length = val) : _length;
        },
        height: function heightAccessor(val) {
            return arguments.length > 0 ? (_height = val) : _height;
        },
        width: function widthAccessor(val) {
            return arguments.length > 0 ? (_width = val) : _width;
        },

        toJSON: function serialiseObject() {
            return {
                make: _make,
                model: _model,
                colour: _colour,
                type: _type,
                axles: _axles,
                twinned: _twinned,
                hookUp: _hookUp,
                curSymbol: _curSymbol,
                value: _value,
                abandonedFlag: _abandonedFlag,
                notes: _notes,
                diagnosis: _diagnosis,
                fault: _fault,
                yearConstructed: _yearConstructed,
                weight: _weight,
                length: _length,
                height: _height,
                width: _width,
            };
        },
    });
}

module.exports = TrailerDetails;
