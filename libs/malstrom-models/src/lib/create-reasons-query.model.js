'use strict';

const { CustomerGroup } = require('@aa/data-models/common');
var _ = require('lodash'),
    CreateReason = require('./create-reason.model'),
    Outcome = require('./outcome.model');
function CreateReasonsQuery(raw) {
    var _model = this,
        _taskId = null,
        _createReason = new CreateReason(),
        _outcome = new Outcome(),
        _customerGroup = new CustomerGroup();

    if (raw) {
        _taskId = raw.taskId || null;
        _createReason = raw.createReason ? new CreateReason(raw.createReason) : _createReason;
        _outcome = raw.outcome ? new Outcome(raw.outcome) : _outcome;
        _customerGroup = raw.customerGroup ? new CustomerGroup(raw.customerGroup) : _customerGroup;
    }

    _.extend(_model, {
        createReason: function createReasonAccessor(val) {
            return arguments.length ? (_createReason = val) : _createReason;
        },
        outcome: function outcomeAccessor(val) {
            return arguments.length ? (_outcome = val) : _outcome;
        },
        customerGroup: function customerGroupAccessor(val) {
            return arguments.length ? (_customerGroup = val) : _customerGroup;
        },
        taskId: function taskIdAccessor(val) {
            return arguments.length ? (_taskId = val) : _taskId;
        },
        toJSON: function toJSON() {
            return {
                taskId: _taskId,
                createReason: _createReason.toJSON(),
                outcome: _outcome.toJSON(),
                customerGroup: _customerGroup.toJSON()
            };
        }
    });
}

module.exports = CreateReasonsQuery;
