'use strict';

const { Phone, Address } = require('@aa/data-models/common');
const _ = require('lodash'),
    Location = require('./location.model'),
    PaymentValue = require('./payment-value.model');

function HotelReservation(raw) {
    var model = this,
        _hotelName = null,
        _hotelLocation = new Location(),
        _hotelAddress = new Address(),
        _phone = new Phone(),
        _reservationId = null,
        _noOfRooms = 0,
        _price = new PaymentValue(),
        _totalnights = null,
        _message = null,
        _completeAddress = null,
        _noOfAdults = 0,
        _noOfChildren = 0,
        _checkInDate = null,
        _checkOutDate = null,
        _payAndClaim = false;

    if (raw) {
        _hotelName = raw.hotelName || null;
        _reservationId = raw.reservationId || null;
        _hotelLocation = raw.hotelLocation ? new Location(raw.hotelLocation) : _hotelLocation;
        _hotelAddress = raw.hotelAddress ? new Address(raw.hotelAddress) : _hotelAddress;
        _noOfRooms = raw.noOfRooms || _noOfRooms;
        _totalnights = raw.totalnights || null;
        _message = raw.message || null;
        _completeAddress = raw.completeAddress || null;
        _price = raw.price ? new PaymentValue(raw.price) : _price;
        _noOfAdults = raw.noOfAdults || _noOfAdults;
        _noOfChildren = raw.noOfChildren || _noOfChildren;
        _phone = raw.phone ? new Phone(raw.phone) : _phone;
        _checkInDate = raw.checkInDate ? new Date(raw.checkInDate) : _checkInDate;
        _checkOutDate = raw.checkOutDate ? new Date(raw.checkOutDate) : _checkOutDate;
        _payAndClaim = raw.payAndClaim || _payAndClaim;
    }

    _.extend(model, {
        /**
         * @param {string} val
         */
        hotelName: function hotelNameAccessor(val) {
            return arguments.length ? (_hotelName = val) : _hotelName;
        },
        /**
         * @param {string} val
         */
        reservationId: function reservationIdAccessor(val) {
            return arguments.length ? (_reservationId = val) : _reservationId;
        },
        /**
         * @param {Location} val
         */
        hotelLocation: function hotelLocationAccessor(val) {
            return arguments.length ? (_hotelLocation = new Location(val)) : _hotelLocation;
        },
        /**
         * @param {Location} val
         */
        hotelAddress: function hotelAddressAccessor(val) {
            return arguments.length ? (_hotelAddress = new Address(val)) : _hotelAddress;
        },
        /**
         * @param {number} val
         */
        noOfRooms: function noOfRoomsAccessor(val) {
            return arguments.length ? (_noOfRooms = val) : _noOfRooms;
        },
        /**
         * @param {string} val
         */
        completeAddress: function completeAddressAccessor(val) {
            return arguments.length ? (_completeAddress = val) : _completeAddress;
        },
        /**
         * @param {Phone} val
         */
        phone: function phoneAccessor(val) {
            return arguments.length ? (_phone = new Phone(val)) : _phone;
        },
        /**
         * @param {PaymentValue} val
         */
        price: function priceAccessor(val) {
            return arguments.length ? (_price = new PaymentValue(val)) : _price;
        },
        /**
         * @param {string} val
         */
        totalnights: function totalnightsAccessor(val) {
            return arguments.length ? (_totalnights = val) : _totalnights;
        },
        /**
         * @param {string} val
         */
        message: function messageAccessor(val) {
            return arguments.length ? (_message = val) : _message;
        },
        /**
         * @param {number} val
         */
        noOfAdults: function noOfAdultsAccessor(val) {
            return arguments.length ? (_noOfAdults = val) : _noOfAdults;
        },
        /**
         * @param {number} val
         */
        noOfChildren: function noOfChildrenAccessor(val) {
            return arguments.length ? (_noOfChildren = val) : _noOfChildren;
        },
        /**
         * @deprecated preserved property for legacy purpose
         * @return {number}
         */
        noOfOccupants: function noOfOccupantsAccessor() {
            // read only
            return _noOfAdults + _noOfChildren;
        },
        /**
         * @param {Date} val
         */
        checkInDate: function checkInDateAccessor(val) {
            return arguments.length ? (_checkInDate = val) : _checkInDate;
        },
        /**
         * @param {Date} val
         */
        checkOutDate: function checkOutDateAccessor(val) {
            return arguments.length ? (_checkOutDate = val) : _checkOutDate;
        },
        /**
         * @param {boolean} val
         */
        payAndClaim: function payAndClaimAccessor(val) {
            return arguments.length ? (_payAndClaim = val) : _payAndClaim;
        },
        toJSON: function () {
            return {
                hotelName: _hotelName,
                reservationId: _reservationId,
                checkInDate: _checkInDate,
                checkOutDate: _checkOutDate,
                hotelLocation: _hotelLocation.toJSON(),
                hotelAddress: _hotelAddress.toJSON(),
                noOfRooms: _noOfRooms,
                phone: _phone.toJSON(),
                price: _price.toJSON(),
                noOfAdults: _noOfAdults,
                noOfChildren: _noOfChildren,
                payAndClaim: _payAndClaim,
                message: _message,
                totalnights: _totalnights,
                completeAddress: _completeAddress,
                // preserved for legacy purposes
                noOfOccupants: _noOfAdults + _noOfChildren
            };
        }
    });
}

module.exports = HotelReservation;
