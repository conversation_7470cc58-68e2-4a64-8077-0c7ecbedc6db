'use strict';

var _ = require('lodash');

function AdvancedMembershipQuery(raw) {
    var model = this,
        _inPostcode = null,
        _outPostcode = null,
        _membershipNumber = null,
        _vehicleRegistrationNumber = null,
        _recheckVehicleRegistrationNumber = null,
        _vehicleIdentificationNumber = null,
        _policyId = null,
        _surname = null,
        _initials = null,
        _town = null,
        _companyName = null,
        _cardNumber = null,
        _page = 1,
        _pageSize = 20,
        _customerGroupCode = null,
        _original = '';

    if (raw) {
        _inPostcode = raw.inPostcode || _inPostcode;
        _outPostcode = raw.outPostcode || _outPostcode;
        _membershipNumber = raw.membershipNumber || _membershipNumber;
        _vehicleRegistrationNumber = raw.vehicleRegistrationNumber || _vehicleRegistrationNumber;
        _recheckVehicleRegistrationNumber = raw.recheckVehicleRegistrationNumber || _recheckVehicleRegistrationNumber;
        _vehicleIdentificationNumber = raw.vehicleIdentificationNumber || _vehicleIdentificationNumber;
        _policyId = raw.policyId || _policyId;
        _surname = raw.surname || _surname;
        _initials = raw.initials || _initials;
        _town = raw.town || _town;
        _companyName = raw.companyName || _companyName;
        _cardNumber = raw.cardNumber || _cardNumber;
        _page = _.isNumber(raw.page) ? raw.page : _page;
        _pageSize = _.isNumber(raw.pageSize) ? raw.pageSize : _pageSize;
        _customerGroupCode = raw.customerGroupCode ? raw.customerGroupCode : _customerGroupCode;
        _original = raw.original ? raw.original : _original;
    }

    _.extend(model, {
        inPostcode: function inPostcodeAccessor(val) {
            return arguments.length ? (_inPostcode = val) : _inPostcode;
        },
        outPostcode: function outPostcodeAccessor(val) {
            return arguments.length ? (_outPostcode = val) : _outPostcode;
        },
        membershipNumber: function membershipNumberAccessor(val) {
            return arguments.length ? (_membershipNumber = val) : _membershipNumber;
        },
        vehicleRegistrationNumber: function vehicleRegistrationNumberAccessor(val) {
            return arguments.length ? (_vehicleRegistrationNumber = val) : _vehicleRegistrationNumber;
        },
        recheckVehicleRegistrationNumber: function checkVehicleRegistrationNumberAccessor(val) {
            return arguments.length ? (_recheckVehicleRegistrationNumber = val) : _recheckVehicleRegistrationNumber;
        },
        policyId: function policyIdAccessor(val) {
            return arguments.length ? (_policyId = val) : _policyId;
        },
        vehicleIdentificationNumber: function vehicleIdentificationNumberAccessor(val) {
            return arguments.length ? (_vehicleIdentificationNumber = val) : _vehicleIdentificationNumber;
        },
        surname: function surnameAccessor(val) {
            return arguments.length ? (_surname = val) : _surname;
        },
        initials: function initialsAccessor(val) {
            return arguments.length ? (_initials = val) : _initials;
        },
        town: function townAccessor(val) {
            return arguments.length ? (_town = val) : _town;
        },
        companyName: function companyNameAccessor(val) {
            return arguments.length ? (_companyName = val) : _companyName;
        },
        cardNumber: function cardNumberAccessor(val) {
            return arguments.length ? (_cardNumber = val) : _cardNumber;
        },
        page: function pageAccessor(val) {
            return arguments.length ? (_page = val) : _page;
        },
        pageSize: function pageSizeAccessor(val) {
            return arguments.length ? (_pageSize = val) : _pageSize;
        },
        customerGroupCode: function customerGroupCodeAccessor(val) {
            return arguments.length ? (_customerGroupCode = val) : _customerGroupCode;
        },
        original: function original(val) {
            return arguments.length ? (_original = val) : _original;
        },
        toJSON: function toJSON() {
            return {
                inPostcode: _inPostcode,
                outPostcode: _outPostcode,
                membershipNumber: _membershipNumber,
                vehicleRegistrationNumber: _vehicleRegistrationNumber,
                recheckVehicleRegistrationNumber: _recheckVehicleRegistrationNumber,
                vehicleIdentificationNumber: _vehicleIdentificationNumber,
                policyId: _policyId,
                surname: _surname,
                initials: _initials,
                town: _town,
                companyName: _companyName,
                cardNumber: _cardNumber,
                page: _page,
                pageSize: _pageSize,
                customerGroupCode: _customerGroupCode
            };
        }
    });
}

module.exports = AdvancedMembershipQuery;
