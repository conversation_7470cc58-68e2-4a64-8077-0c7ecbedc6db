'use strict';

var _ = require('lodash'),
    AbstractTask = require('./abstract-task.model'),
    Appointment = require('./appointment.model'),
    arrayObjectToJSON = require('./factories/array-object-to-json.factory'),
    Contact = require('./contact.model'),
    CreateReason = require('./create-reason.model'),
    EligibilityQuestion = require('./eligibility-question.model'),
    EntitlementSummary = require('./entitlement-summary.model'),
    Fault = require('./fault.model'),
    Location = require('./location.model'),
    MiscFields = require('./misc-fields.model'),
    PriorityReason = require('./priority-reason.model'),
    Recovery = require('./recovery.model'),
    RefCode = require('./ref-code.model'),
    Schedule = require('./task-schedule.model'),
    TaskIndicators = require('./task-indicators.model'),
    TelematicsDiagnostics = require('./telematics/telematics-diagnostics.model'),
    TypeErrorHelper = require('./exceptions/type-error-helper'),
    UiStatus = require('./ui-status.model'),
    ContractValidation = require('./contract-validation.model'),
    Roadworthiness = require('./contract-validation-roadworthiness.model'),
    DemandDeflection = require('./demand-deflection.model'),
    AdditionalExperianDetails = require('./additional-experian-details.model.js'),
    EurohelpData = require('./eurohelp-task-data.model'),
    EventHooks = require('./event-hooks.model'),
    BuzbyTelfix = require('./buzby-telfix.model'),
    customerGroupHelper = require('./helpers/customer-group.helper');
const { CustomerGroup, Vehicle } = require('@aa/data-models/common');

function Task(raw) {
    var model = this,
        _altContact = new Contact(),
        _appointment = new Appointment(),
        _changeOfNote = false,
        _contact = new Contact(),
        _createReason = new CreateReason(),
        _customerRequestId = -1,
        _eligibilityQAList = [],
        _entitlement = new EntitlementSummary(),
        _fault = new Fault(),
        _glassModelId = -1,
        _id = -1,
        _fleetReferral = false,
        _jobNoToday = -1,
        _jobsheet = null,
        _location = new Location(),
        _operatorId = -1,
        _parentTaskId = -1,
        _parentTask = null,
        _partsStatus = null,
        _priorities = [],
        _schedule = new Schedule(),
        _sequence = -1,
        _status = null,
        _taskType = new RefCode({
            code: 'BRK',
            name: 'Breakdown task'
        }),
        _telematicsDiagnostics = null,
        // rss
        _canReattend = false,
        _canReopen = false,
        _deferQualification = false,
        _epyxGarageReference = null,
        _indicators = new TaskIndicators(),
        _isDirty = false,
        _membImpressed = false,
        _miscFields = new MiscFields(),
        _recovery = new Recovery(),
        _supplierJobType = new RefCode(),
        _supJobTypeCode = null,
        _coveringSiteId = null,
        _split = false,
        _systemStatus = null,
        _uiStatus = new UiStatus(),
        _contractValidation = new ContractValidation(),
        _vehicle = new Vehicle(),
        _eventHooks = new EventHooks(),
        _buzbyTelfix = new BuzbyTelfix(),
        _demandDeflection = new DemandDeflection(),
        _eurohelp = new EurohelpData(),
        _additionalExperianDetails = new AdditionalExperianDetails();
    //end - rss

    if (raw) {
        _altContact = raw.altContact ? new Contact(raw.altContact) : _altContact;
        _appointment = raw.appointment ? new Appointment(raw.appointment) : _appointment;
        _changeOfNote = raw.changeOfNote === true;
        _contact = raw.contact ? new Contact(raw.contact) : _contact;
        _customerRequestId = raw.customerRequestId;
        _createReason = raw.createReason ? new CreateReason(raw.createReason) : _createReason;
        _.forEach(raw.eligibilityQAList, function _forEachEligibilityQuestion(rawEligibilityQuestion) {
            _eligibilityQAList.push(new EligibilityQuestion(rawEligibilityQuestion));
        });
        _entitlement = raw.entitlement ? new EntitlementSummary(raw.entitlement) : _entitlement;
        _fleetReferral = raw.fleetReferral ? raw.fleetReferral : customerGroupHelper.isFleetReferral(_entitlement);
        _fault = raw.fault ? new Fault(raw.fault) : _fault;
        _glassModelId = raw.glassModelId ? raw.glassModelId : _glassModelId;
        _id = raw.id;
        _isDirty = raw.isDirty ? raw.isDirty : _isDirty;
        _jobNoToday = raw.jobNoToday ? raw.jobNoToday : _jobNoToday;
        _jobsheet = raw.jobsheet ? raw.jobsheet : _jobsheet;
        _location = raw.location ? new Location(raw.location) : _location;
        _membImpressed = raw.membImpressed ? raw.membImpressed : false;
        _operatorId = raw.operatorId ? raw.operatorId : _operatorId;
        _parentTaskId = raw.parentTaskId ? raw.parentTaskId : _parentTaskId;
        _parentTask = raw.parentTask ? new Task(raw.parentTask) : null;
        _partsStatus = raw.partsStatus ? raw.partsStatus : _partsStatus;
        _.forEach(raw.priorities, function _forEachPriorities(priority) {
            _priorities.push(new PriorityReason(priority));
        });
        _telematicsDiagnostics = raw.telematicsDiagnostics ? new TelematicsDiagnostics(raw.telematicsDiagnostics) : _telematicsDiagnostics;
        _schedule = raw.schedule ? new Schedule(raw.schedule) : _schedule;
        _sequence = _.isNumber(raw.sequence) ? raw.sequence : _sequence;
        _status = raw.status ? raw.status : _status;
        _supJobTypeCode = raw.supJobTypeCode ? raw.supJobTypeCode : _supJobTypeCode;
        _coveringSiteId = raw.coveringSiteId ? raw.coveringSiteId : _coveringSiteId;
        _systemStatus = raw.systemStatus ? raw.systemStatus : _systemStatus;
        //rss
        _canReattend = raw.canReattend ? raw.canReattend : false;
        _canReopen = raw.canReopen ? raw.canReopen : false;
        _deferQualification = raw.deferQualification || _deferQualification;
        _epyxGarageReference = raw.epyxGarageReference ? raw.epyxGarageReference : _epyxGarageReference;
        _indicators = raw.indicators ? new TaskIndicators(raw.indicators) : _indicators;
        _miscFields = raw.miscFields ? new MiscFields(raw.miscFields) : _miscFields;
        _recovery = raw.recovery ? new Recovery(raw.recovery) : _recovery;
        _split = raw.split ? raw.split : false;
        _supplierJobType = raw.supplierJobType ? new RefCode(raw.supplierJobType) : _supplierJobType;
        if (raw.uiStatus) {
            _uiStatus = new UiStatus(raw.uiStatus);
        } else {
            _uiStatus.disable(_status === 'COMP' || _status === 'CLSD');
        }
        _vehicle = raw.vehicle ? new Vehicle(raw.vehicle) : _vehicle;
        //end - rss
        _contractValidation = raw.contractValidation ? new ContractValidation(raw.contractValidation) : _contractValidation;
        _eventHooks = raw.eventHooks ? new EventHooks(raw.eventHooks) : null;
        _buzbyTelfix = raw.buzbyTelfix ? new BuzbyTelfix(raw.buzbyTelfix) : null;
        _demandDeflection = raw.demandDeflection ? new DemandDeflection(raw.demandDeflection) : _demandDeflection;
        _eurohelp = raw.eurohelp ? new EurohelpData(raw.eurohelp) : _eurohelp;
        _additionalExperianDetails = raw.additionalExperianDetails ? new AdditionalExperianDetails(raw.additionalExperianDetails) : _additionalExperianDetails;
    }

    const _checkInvalidMotWithinGrace = (rwDetails) => {
        return rwDetails.isMOTWithinGrace() ? Roadworthiness.STATUS.WITHIN_GRACE_PERIOD : Roadworthiness.STATUS.OUTSIDE_GRACE_PERIOD;
    };
    const _checkInvalidTaxWithinGraceOrSorn = (rwDetails) => {
        if (rwDetails.isSorn()) {
            return Roadworthiness.STATUS.TAX_STATUS_SORN;
        }
        return rwDetails.isTaxWithinGrace() ? Roadworthiness.STATUS.WITHIN_GRACE_PERIOD : Roadworthiness.STATUS.OUTSIDE_GRACE_PERIOD;
    };

    const validateContract = () => {
        // building ContractValidationRoadworthiness object from the roadworthy model
        let rwDetails = _vehicle.roadworthy();
        let roadworthiness = _contractValidation.roadworthiness().toJSON();
        if (!rwDetails.registrationNumber()) {
            roadworthiness.passed = false;
            roadworthiness.noData = true;
        } else {
            // set motStatus and taxStatus --- Valid:1 , withingrace : 2 , outsidegrace : 3, sorn :4
            roadworthiness.motStatus = rwDetails.isMotValid() ? Roadworthiness.STATUS.VALID : _checkInvalidMotWithinGrace(rwDetails);
            roadworthiness.taxStatus = rwDetails.isTaxValid() ? Roadworthiness.STATUS.VALID : _checkInvalidTaxWithinGraceOrSorn(rwDetails);
            roadworthiness.passed = rwDetails.isMotValid() && rwDetails.isTaxValid();
            roadworthiness.noData = false;
        }

        // if no data received and we still have override indicating that no data received
        if (roadworthiness.noData) {
            // only set override to "no data" if we are not removing diff override
            if (!roadworthiness.override) {
                roadworthiness.override = ContractValidation.OVERRIDE_REASONS.UNABLE_TO_RETRIEVE_DETAILS.value;
            }
        } else {
            // if data received and override set to "no data", reset it
            if (roadworthiness.override === ContractValidation.OVERRIDE_REASONS.UNABLE_TO_RETRIEVE_DETAILS.value) {
                roadworthiness.override = null;
            }
        }

        _contractValidation.roadworthiness(new Roadworthiness(roadworthiness));

        // build the demand deflection checks
        if (_demandDeflection) {
            if ([CustomerGroup.CDL, CustomerGroup.CDLV, CustomerGroup.UBE, CustomerGroup.CHAS].includes(_entitlement.customerGroup().code())) {
                _contractValidation.excessiveUse().passed(_demandDeflection.calls() ? _demandDeflection.calls() < _demandDeflection.maxCallouts() : true);
            } else {
                _contractValidation.excessiveUse().passed(true);
            }
            _contractValidation.excessiveUse().callouts(_demandDeflection.calls());
        } else {
            _contractValidation.excessiveUse().passed(true);
        }
        return _contractValidation;
    };

    validateContract();

    _.extend(model, new AbstractTask(), {
        id: function idAccessor(val) {
            return arguments.length ? (_id = val) : _id;
        },
        customerRequestId: function customerRequestIdAccessor(val) {
            return arguments.length ? (_customerRequestId = val) : _customerRequestId;
        },
        taskType: function taskTypeAccessor() {
            return _taskType;
        },
        createReason: function createReasonAccessor(val) {
            return arguments.length ? (_createReason = val) : _createReason;
        },
        status: function statusAccessor(val) {
            return arguments.length ? (_status = val) : _status;
        },
        coveringSiteId: function coveringSiteIdAccessor(val) {
            return arguments.length ? (_coveringSiteId = val) : _coveringSiteId;
        },
        jobsheet: function jobsheetAccessor(val) {
            return arguments.length ? (_jobsheet = val) : _jobsheet;
        },
        contact: function contactAccessor(val) {
            return arguments.length ? (_contact = val) : _contact;
        },
        altContact: function altContactAccessor(val) {
            return arguments.length ? (_altContact = val) : _altContact;
        },
        entitlement: function entitlementAccessor(val) {
            _entitlement = arguments.length ? val : _entitlement;
            if (val) {
                // set fleet referral based on provided entitlement
                _fleetReferral = customerGroupHelper.isFleetReferral(_entitlement);
            }

            return _entitlement;
        },
        fleetReferral: function fleetReferralAccessor(val) {
            return arguments.length ? (_fleetReferral = val) : _fleetReferral;
        },
        location: function locationAccessor(val) {
            return arguments.length ? (_location = val) : _location;
        },
        telematicsDiagnostics: function telematicsDiagnosticsAccessor(val) {
            return !arguments.length
                ? _telematicsDiagnostics
                : !(val && !(val instanceof TelematicsDiagnostics))
                ? (_telematicsDiagnostics = val)
                : TypeErrorHelper.errorAndReturnNull(TypeErrorHelper.buildErrorMessage.classValue('Task.TelematicsDiagnostics'), val);
        },
        sequence: function sequenceAccessor(val) {
            return arguments.length ? (_sequence = val) : _sequence;
        },
        jobNoToday: function jobNoTodayAccessor(val) {
            return arguments.length ? (_jobNoToday = val) : _jobNoToday;
        },
        operatorId: function operatorIdAccessor(val) {
            return arguments.length ? (_operatorId = val) : _operatorId;
        },
        parentTaskId: function parentTaskIdAccessor(val) {
            return arguments.length ? (_parentTaskId = val) : _parentTaskId;
        },
        changeOfNote: function changeOfNoteAccessor(val) {
            return arguments.length ? (_changeOfNote = val) : _changeOfNote;
        },
        partsStatus: function partsStatusAccessor(val) {
            return arguments.length ? (_partsStatus = val) : _partsStatus;
        },
        fault: function faultAccessor(val) {
            return arguments.length ? (_fault = val) : _fault;
        },
        eligibilityQAList: function eligibilityQAListAccessor(val) {
            return arguments.length ? (_eligibilityQAList = val) : _eligibilityQAList;
        },
        appointment: function appointmentAccessor(val) {
            return arguments.length ? (_appointment = val) : _appointment;
        },
        schedule: function scheduleAccessor(val) {
            return arguments.length ? (_schedule = val) : _schedule;
        },
        priorities: function prioritiesAccessor(val) {
            return arguments.length ? (_priorities = val) : _priorities;
        },
        isNonPriority: function isNonPriority() {
            var retVal = false;
            if (_priorities.length) {
                retVal = _.find(model.priorities(), function (priority) {
                    return [PriorityReason.NON_URGENT].indexOf(priority.id()) > -1;
                });

                retVal =
                    retVal ||
                    !_.find(model.priorities(), function (priority) {
                        return priority.text() !== 'NONE';
                    });
            }
            return retVal;
        },
        isPriority: function isPriority() {
            return model.priorities().length && !model.isNonPriority();
        },
        glassModelId: function glassModelIdAccessor(val) {
            return arguments.length ? (_glassModelId = val) : _glassModelId;
        },
        //rss
        vehicle: function vehicleAccessor(val) {
            if (arguments.length) {
                _vehicle = val;
                this.validateContract();
            }
            return _vehicle;
        },
        recovery: function destinationAccessor(val) {
            return arguments.length ? (_recovery = val) : _recovery;
        },
        supplierJobType: function supplierJobTypeAccessor(val) {
            return arguments.length ? (_supplierJobType = val) : _supplierJobType;
        },
        epyxGarageReference: function epyxGarageReferenceAccessor(val) {
            return arguments.length ? (_epyxGarageReference = val) : _epyxGarageReference;
        },
        isCompleted: function completionTest() {
            return _status === 'COMP' || _status === 'CLSD';
        },
        isReattend: function isReattendTester() {
            return _createReason.id() === CreateReason.REOPEN_TASK || _miscFields.reattend();
        },
        isRelay: function isRelayTester() {
            return _recovery.isSet();
        },
        membImpressed: function membImpressedAccessor(val) {
            return arguments.length ? (_membImpressed = val) : _membImpressed;
        },
        supJobTypeCode: function supJobTypeCodeAccessor(val) {
            return arguments.length ? (_supJobTypeCode = val) : _supJobTypeCode;
        },
        canReattend: function canReattendAccessor(val) {
            return arguments.length ? (_canReattend = val) : _canReattend;
        },
        canReopen: function canReopenAccessor(val) {
            return arguments.length ? (_canReopen = val) : _canReopen;
        },
        split: function split(val) {
            return arguments.length ? (_split = val) : _split;
        },
        uiStatus: function uiAccessor(val) {
            return arguments.length ? (_uiStatus = val) : _uiStatus;
        },
        isNew: function isNew() {
            return _sequence === -1 && !model.isCompleted();
        },
        isActive: function isActive() {
            return _sequence > -1 && !model.isCompleted();
        },
        parentTask: function parentTaskAccessor(val) {
            return arguments.length ? (_parentTask = val) : _parentTask;
        },
        canReattendWithin: function (days) {
            var timeNow = Date.now(),
                periodLimit = _schedule.complete() ? _schedule.complete().getTime() + days * 24 * 3600 * 1000 : timeNow;
            return _status === 'COMP' && timeNow < periodLimit; //&& _createReason.serviceType() === 'RSS';
        },
        isCompletedWithin: function (days) {
            var timeNow = Date.now(),
                periodLimit = _schedule.complete() ? _schedule.complete().getTime() + days * 24 * 3600 * 1000 : timeNow;
            return model.isCompleted() && timeNow < periodLimit;
        },
        indicators: function indicatorsAccessor(val) {
            return arguments.length ? (_indicators = val) : _indicators;
        },
        miscFields: function miscFieldsAccessor(val) {
            return arguments.length ? (_miscFields = val) : _miscFields;
        },
        isRecovery: function recoveryTester() {
            return _recovery.isSet() || _createReason.isRecovery();
        },
        canCancelTask: function canCancelTask() {
            if (_systemStatus === 'WORK' || _systemStatus === 'COMP') {
                return false;
            }
            return true;
        },
        isDirty: function isDirtyAccessor(val) {
            return arguments.length ? (_isDirty = val) : _isDirty;
        },
        isMoveable: function isMoveable() {
            return !_.includes(['ARVD', 'FIX+', 'COMP', 'CLSD'], _status);
        },
        systemStatus: function systemStatusAccessor(val) {
            return arguments.length ? (_systemStatus = val) : _systemStatus;
        },
        isDiagnosedRecoveryJob: function isDiagnosedRecoveryJob() {
            return _fault.isDiagnosedRecoveryJob();
        },
        deferQualification: function deferQualification(val) {
            return arguments.length ? (_deferQualification = val) : _deferQualification;
        },
        contractValidation: function contractValidation(val) {
            if (arguments.length) {
                _contractValidation = val;
                this.validateContract();
            }
            return _contractValidation;
        },
        validateContract,
        isValidCapabilities: function isValidCapabilities() {
            return _fault.isCapabilitiesSet() || _recovery.fault().isCapabilitiesSet();
        },
        isValidLocation: function isValidLocation() {
            let motorwayInfoAvailable = _indicators && _indicators.motorway() && _sequence === -1 ? _location.isMotorwayInfoValid() && _location.isValidJunction() : true;
            return motorwayInfoAvailable && _location.coordinates().latitude() !== null && _location.coordinates().longitude() !== null && !!_location.text() && !!_location.area();
        },
        isValidRelayPlus: function isValidRelayPlus() {
            return !_createReason.isRelayPlus() || _miscFields.isValidRelayPlus();
        },
        /**
         * Check task is valid to schedule in PRIME
         * Does not include location is in UK boundry
         * **Don't use this for HIRE_CAR task**
         */
        isValidRSSTask: () => {
            return (
                this.isValidCapabilities() &&
                _contact.isValidCustomer() &&
                _entitlement.isValidMandatoryFlexFields() &&
                _vehicle.isValidVehicle() &&
                _fault.isSet() &&
                this.isValidLocation() &&
                (_createReason.isPassengerRun() ? _recovery.isValidPassengerRun() : _recovery.isValidRecovery()) &&
                this.isValidRelayPlus()
            );
        },
        //end - rss
        eventHooks: function eventHooks(val) {
            return arguments.length ? (_eventHooks = val) : _eventHooks;
        },
        isHeading: function isHeading() {
            return Task.HEADING.includes(_status);
        },
        isArrived: function isArrived() {
            return Task.ARRIVED.includes(_status);
        },
        isWaitingOnResource: function isWaitingOnResource() {
            return Task.WAITING_ON_RESOURCE.includes(_status);
        },
        isDeployed: function isDeployed() {
            return model.isHeading() || model.isWaitingOnResource() || model.isArrived();
        },
        isCarHireExists: function isCarHireExists() {
            return Task.HAS_HIRE_CAR.includes(_status);
        },
        tag: function tag() {
            return `[task ${_id}@${_sequence} st ${_status} res ${_schedule.resource().id()} (${_indicators.tag()}) (${_fault.tag()})]`;
        },
        buzbyTelfix: function buzbyTelfix(val) {
            return arguments.length ? (_buzbyTelfix = val) : _buzbyTelfix;
        },
        demandDeflection: function demandDeflection(val) {
            if (arguments.length) {
                _demandDeflection = val;
                this.validateContract();
            }
            return _demandDeflection;
        },
        eurohelp: function eurohelpAccessor(val) {
            return arguments.length ? (_eurohelp = val) : _eurohelp;
        },
        additionalExperianDetails: function additionalExperianDetailsAccessor(val) {
            return arguments.length ? (_additionalExperianDetails = val) : _additionalExperianDetails;
        },
        toJSON: function toJSON() {
            return {
                id: _id,
                fleetReferral: _fleetReferral,
                customerRequestId: _customerRequestId,
                createReason: _createReason ? _createReason.toJSON() : null,
                status: _status,
                jobsheet: _jobsheet,
                contact: _contact ? _contact.toJSON() : null,
                altContact: _altContact ? _altContact.toJSON() : null,
                entitlement: _entitlement ? _entitlement.toJSON() : null,
                location: _location ? _location.toJSON() : null,
                sequence: _sequence,
                jobNoToday: _jobNoToday,
                operatorId: _operatorId,
                parentTaskId: _parentTaskId,
                changeOfNote: _changeOfNote === true,
                partsStatus: _partsStatus,
                fault: _fault ? _fault.toJSON() : null,
                _telematicsDiagnostics: _telematicsDiagnostics ? _telematicsDiagnostics.toJSON() : null,
                eligibilityQAList: arrayObjectToJSON(_eligibilityQAList),
                appointment: _appointment ? _appointment.toJSON() : null,
                schedule: _schedule ? _schedule.toJSON() : null,
                glassModelId: _glassModelId,
                //rss
                vehicle: _vehicle ? _vehicle.toJSON() : null,
                recovery: _recovery ? _recovery.toJSON() : null,
                epyxGarageReference: _epyxGarageReference,
                supJobTypeCode: _supJobTypeCode,
                membImpressed: _membImpressed,
                canReattend: _canReattend,
                canReopen: _canReopen,
                split: _split,
                uiStatus: _uiStatus.toJSON(),
                parentTask: _parentTask ? _parentTask.toJSON() : null,
                indicators: _indicators.toJSON(),
                miscFields: _miscFields.toJSON(),
                priorities: arrayObjectToJSON(_priorities),
                taskType: _taskType.toJSON(),
                isDirty: _isDirty,
                systemStatus: _systemStatus,
                supplierJobType: _supplierJobType ? _supplierJobType.toJSON() : null,
                deferQualification: _deferQualification,
                coveringSiteId: _coveringSiteId,
                contractValidation: _contractValidation ? _contractValidation.toJSON() : null,
                eventHooks: _eventHooks ? _eventHooks.toJSON() : null,
                buzbyTelfix: _buzbyTelfix ? _buzbyTelfix.toJSON() : null,
                demandDeflection: _demandDeflection ? _demandDeflection.toJSON() : null,
                eurohelp: _eurohelp ? _eurohelp.toJSON() : null,
                additionalExperianDetails: _additionalExperianDetails ? _additionalExperianDetails.toJSON() : null
            };
        }
    });
}

//constants
Task.UNAC_STATUS = 'UNAC';
Task.CHCK_STATUS = 'CHCK';
Task.PLAN_STATUS = 'PLAN';
Task.HEAD_STATUS = 'HEAD';
Task.HIRE_STATUS = 'HIRE';
Task.PACK_STATUS = 'PACK';
Task.ARVD_STATUS = 'ARVD';
Task.COMP_STATUS = 'COMP';
Task.CLSD_STATUS = 'CLSD';
Task.GDET_STATUS = 'GDET';
Task.GHED_STATUS = 'GHED';
Task.GARR_STATUS = 'GARR';
Task.RECY_STATUS = 'RECY';
Task.TIDY_STATUS = 'TIDY';
Task.INIT_STATUS = 'INIT';
Task.WRAP_STATUS = 'WRAP';
Task.LOAD_STATUS = 'LOAD';
Task.UNLD_STATUS = 'UNLD';
Task.FLY_STATUS = 'FLY';
Task.HOLD_STATUS = 'HOLD';
Task.SIF_IND = 720;
Task.MILEAGE = 730;
Task.HOLDING_RESOURCE = 857;
Task.CAC_REF = 880;
Task.INIT_IDLE_TIME = 45 * 60 * 1000; // 45 mins

Task.HEADING = [Task.HEAD_STATUS, Task.GHED_STATUS]; // various status that are indicate task in HEAD status
Task.ARRIVED = [Task.ARVD_STATUS, Task.GARR_STATUS, Task.HIRE_STATUS]; // aka resource arrived for rss or vehicle checkouted out for hire tasks
Task.WAITING_ON_RESOURCE = [Task.PACK_STATUS, Task.GDET_STATUS];
Task.HAS_HIRE_CAR = [Task.PLAN_STATUS, Task.GDET_STATUS, Task.HEAD_STATUS, Task.HIRE_STATUS, Task.GARR_STATUS]; // used for evalite car hire - not allowing to create hire if task has already car hire exists.

module.exports = Task;
