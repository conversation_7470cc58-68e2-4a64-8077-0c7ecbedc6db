'use strict';
var _ = require('lodash'),
    PeriodOfTime = require('./period-of-time.model');

function RentalExtension(raw) {
    var _period = new PeriodOfTime(),
        _allowed = null,
        _arrangedBy = null,
        _limit = null;

    if (raw) {
        _period = raw.period ? new PeriodOfTime(raw.period) : _period;
        _allowed = raw.allowed; // boolean
        _arrangedBy = raw.arrangedBy; //string
        _limit = raw.limit; //int
    }

    _.extend(this, {
        period: function periodAccessor(val) {
            return arguments.length ? (_period = val) : _period;
        },
        allowed: function allowedAccessor(val) {
            return arguments.length ? (_allowed = val) : _allowed;
        },
        arrangedBy: function arrangedByAccessor(val) {
            return arguments.length ? (_arrangedBy = val) : _arrangedBy;
        },
        limit: function limitAccessor(val) {
            return arguments.length ? (_limit = val) : _limit;
        },
        toJSON: function serialise() {
            return {
                period: _period.toJSON(),
                allowed: _allowed,
                arrangedBy: _arrangedBy,
                limit: _limit,
            };
        },
    });
}

module.exports = RentalExtension;
