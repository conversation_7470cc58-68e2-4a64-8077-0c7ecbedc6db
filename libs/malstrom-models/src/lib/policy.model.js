'use strict';

const { SeSystem, CustomerGroup } = require('@aa/data-models/common');
var _ = require('lodash');
/**
 * @typedef Policy
 * @constructor
 */
function Policy(raw) {
    var model = this,
        _policyNumber = null,
        _startDate = null,
        _endDate = null,
        _membershipNumber = null,
        _cardType = null,
        _membershipType = null,
        _customerGroup = new CustomerGroup(),
        _systemId = null,
        _seSystem = new SeSystem(),
        _supplierOwnReference = null,
        _inceptionDate = null,
        _inceptionDateAlert = false,
        _customerKey = null,
        _contractKey = null,
        _status = null,
        _supplierCustomerReference = null,
        _memberStatus = null;

    if (raw) {
        _policyNumber = raw.policyNumber;
        _startDate = raw.startDate ? new Date(raw.startDate) : null;
        _endDate = raw.endDate ? new Date(raw.endDate) : null;
        _membershipNumber = raw.membershipNumber;
        _cardType = raw.cardType;
        _membershipType = raw.membershipType;

        _systemId = raw.systemId;

        _contractKey = raw.contractKey;

        _seSystem = raw.seSystem ? new SeSystem(raw.seSystem) : _seSystem;

        _customerGroup = raw.customerGroup ? new CustomerGroup(raw.customerGroup) : _customerGroup;

        _supplierOwnReference = raw.supplierOwnReference;

        _inceptionDate = raw.inceptionDate ? new Date(raw.inceptionDate) : null;

        _inceptionDateAlert = raw.inceptionDateAlert ? raw.inceptionDateAlert : false;

        _customerKey = raw.customerKey || null;
        _status = _.isString(raw.status) ? raw.status : null;

        _supplierCustomerReference = raw.supplierCustomerReference || null;

        _memberStatus = raw.memberStatus || null;
    }

    _.extend(model, {
        /** @param {String} [val] */
        policyNumber: function policyNumberAccessor(val) {
            return arguments.length ? (_policyNumber = val) : _policyNumber;
        },

        /** @param {Date} [val] */
        startDate: function startDateAccessor(val) {
            return arguments.length ? (_startDate = val) : _startDate;
        },

        /** @param {Date} [val] */
        endDate: function endDateAccessor(val) {
            return arguments.length ? (_endDate = val) : _endDate;
        },

        /** @param {string} [val] */
        membershipNumber: function membershipNumberAccessor(val) {
            return arguments.length ? (_membershipNumber = val) : _membershipNumber;
        },

        /**
         * Gold, Silver etc
         * @param {string} [val]
         */
        cardType: function cardTypeAccessor(val) {
            return arguments.length ? (_cardType = val) : _cardType;
        },

        /**
         * Joint, Family etc
         * @param {string} [val]
         */
        membershipType: function membershipTypeAccessor(val) {
            if (arguments.length && _customerGroup.code() === 'SAGA') {
                switch (val) {
                    case 'P':
                        return (_membershipType = 'PERSONAL');
                    default:
                        return (_membershipType = 'VEHICLE');
                }
            }
            return arguments.length ? (_membershipType = val) : _membershipType;
        },

        /**
         * Customer Group e.g. PERS
         * @param {RefCode} [val]
         */
        customerGroup: function customerGroupAccessor(val) {
            return arguments.length ? (_customerGroup = val) : _customerGroup;
        },

        /** @param {string} [val] */
        systemId: function systemIdAccessor(val) {
            return arguments.length ? (_systemId = val) : _systemId;
        },

        /** @param {string} [val] */
        supplierOwnReference: function supplierOwnReferenceAccessor(val) {
            return arguments.length ? (_supplierOwnReference = val) : _supplierOwnReference;
        },

        /** @param {Date} [val] - when cover was first taken out */
        inceptionDate: function inceptionDateAccessor(val) {
            return arguments.length ? (_inceptionDate = val) : _inceptionDate;
        },

        /** @param {boolean} [val] - true if need to show an alert to the user*/
        inceptionDateAlert: function inceptionDateAlertAccessor(val) {
            return arguments.length ? (_inceptionDateAlert = val) : _inceptionDateAlert;
        },

        /**
         * @param  {SeSystem} [val]
         * @return {SeSystem}     value of _seSystem
         */
        seSystem: function seSystemAccessor(val) {
            return arguments.length ? (_seSystem = val) : _seSystem;
        },

        /**
         * @param  {string} [val] val - the contract key aka policyId to set
         * @return {string} value of _contractKey
         */
        contractKey: function contractKeyAccessor(val) {
            return arguments.length ? (_contractKey = val) : _contractKey;
        },

        /**
         * @param  {null|string} [val] - customerKey aka cardNoFlv to set
         * @return {string} value of _customerKey:163
         */
        customerKey: function customerKeyAccesor(val) {
            return arguments.length ? (_customerKey = val) : _customerKey;
        },

        /**
         * @param  {string} [val] - Entitlement/policy status aka lapse status to set
         * @return {string} the status
         */
        status: function statusAccesor(val) {
            return arguments.length ? (_status = val) : _status;
        },

        /**
         * Checks if the customer is a gold member. Determines this from cardType and returns a boolean value.
         * @return {boolean}
         */
        isGoldMember: function isGoldMember() {
            return _cardType ? _cardType.trim().toUpperCase() === 'GOLD' : false;
        },

        /**
         * @param  {string} [val] - supplierCustomerReference for UBER
         * @return {string} - value of _supplierCustomerReference
         */
        supplierCustomerReference: function (val) {
            return arguments.length ? (_supplierCustomerReference = val) : _supplierCustomerReference;
        },

        /**
         * memberStatus determines if entiltment based on each driver is Live/Stopped
         */

        memberStatus: function (val) {
            return arguments.length ? (_memberStatus = val) : _memberStatus;
        },

        /**
         * serialized version of Policy model
         * @return {Object}
         */
        toJSON: function toJSON() {
            return {
                policyNumber: _policyNumber,
                startDate: _startDate,
                endDate: _endDate,
                membershipNumber: _membershipNumber,
                cardType: _cardType,
                membershipType: _membershipType,
                customerGroup: _customerGroup.toJSON(),
                systemId: _systemId,
                seSystem: _seSystem.toJSON(),
                supplierOwnReference: _supplierOwnReference,
                inceptionDateAlert: _inceptionDateAlert,
                inceptionDate: _inceptionDate,
                customerKey: _customerKey,
                contractKey: _contractKey,
                status: _status,
                supplierCustomerReference: _supplierCustomerReference,
                memberStatus: _memberStatus
            };
        }
    });
}

module.exports = Policy;
