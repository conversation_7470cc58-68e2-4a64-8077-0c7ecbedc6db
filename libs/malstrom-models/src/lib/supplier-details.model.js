'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    _arrayObjectToJSON = require('./factories/array-object-to-json.factory'),
    TelephoneNumberAndType = require('./telephone-number-and-type.model');

function SupplierDetails(raw) {
    var model = this,
        _chequesAcceptedIndicator = false,
        _creditCardsAcceptedIndicator = false,
        _extraCapabilities = null,
        _incidentManagedIndicator = false,
        _issA120FormsIndicator = false,
        _primeAgentIndicator = false,
        _relayPlusVoucherIndicator = false,
        _remarks = null,
        _resourceTelephoneNumbersAndType = [], //array of TelephoneNumberAndType
        _contactName = null,
        _address = new Address();

    if (raw) {
        _chequesAcceptedIndicator = raw.chequesAcceptedIndicator;
        _creditCardsAcceptedIndicator = raw.creditCardsAcceptedIndicator;
        _extraCapabilities = raw.extraCapabilities;
        _incidentManagedIndicator = raw.incidentManagedIndicator;
        _issA120FormsIndicator = raw.issA120FormsIndicator;
        _primeAgentIndicator = raw.primeAgentIndicator;
        _relayPlusVoucherIndicator = raw.relayPlusVoucherIndicator;
        _remarks = raw.remarks;
        _contactName = raw.contactName;
        _address = raw.address ? new Address(raw.address) : _address;
        _.forEach(raw.resourceTelephoneNumbersAndType, function forEachTelNoType(rawTelephoneNumberType) {
            _resourceTelephoneNumbersAndType.push(new TelephoneNumberAndType(rawTelephoneNumberType));
        });
    }

    _.extend(model, {
        chequesAcceptedIndicator: function chequesAcceptedIndicatorAccessor(val) {
            return arguments.length ? (_chequesAcceptedIndicator = val) : _chequesAcceptedIndicator;
        },
        creditCardsAcceptedIndicator: function creditCardsAcceptedIndicatorAccessor(val) {
            return arguments.length ? (_creditCardsAcceptedIndicator = val) : _creditCardsAcceptedIndicator;
        },
        extraCapabilities: function extraCapabilitiesAccessor(val) {
            return arguments.length ? (_extraCapabilities = val) : _extraCapabilities;
        },
        incidentManagedIndicator: function incidentManagedIndicatorrAccessor(val) {
            return arguments.length ? (_incidentManagedIndicator = val) : _incidentManagedIndicator;
        },
        issA120FormsIndicator: function issA120FormsIndicatorAccessor(val) {
            return arguments.length ? (_issA120FormsIndicator = val) : _issA120FormsIndicator;
        },
        primeAgentIndicator: function primeAgentIndicatorAccessor(val) {
            return arguments.length ? (_primeAgentIndicator = val) : _primeAgentIndicator;
        },
        relayPlusVoucherIndicator: function relayPlusVoucherIndicatorAccessor(val) {
            return arguments.length ? (_relayPlusVoucherIndicator = val) : _relayPlusVoucherIndicator;
        },
        remarks: function remarksAccessor(val) {
            return arguments.length ? (_remarks = val) : _remarks;
        },
        contactName: function contactNameAccessor(val) {
            return arguments.length ? (_contactName = val) : _contactName;
        },
        resourceTelephoneNumbersAndType: function resourceTelephoneNumbersAndTypeAccessor(val) {
            return arguments.length ? (_resourceTelephoneNumbersAndType = val) : _resourceTelephoneNumbersAndType;
        },
        address: function addressAccesor(val) {
            return arguments.length ? (_address = val) : _address;
        },
        toJSON: function toJSON() {
            return {
                chequesAcceptedIndicator: _chequesAcceptedIndicator,
                creditCardsAcceptedIndicator: _creditCardsAcceptedIndicator,
                extraCapabilities: _extraCapabilities,
                incidentManagedIndicator: _incidentManagedIndicator,
                issA120FormsIndicator: _issA120FormsIndicator,
                primeAgentIndicator: _primeAgentIndicator,
                relayPlusVoucherIndicator: _relayPlusVoucherIndicator,
                remarks: _remarks,
                contactName: _contactName,
                resourceTelephoneNumbersAndType: _arrayObjectToJSON(_resourceTelephoneNumbersAndType),
                address: _address.toJSON()
            };
        }
    });
}

module.exports = SupplierDetails;
