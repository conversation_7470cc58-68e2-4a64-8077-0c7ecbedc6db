'use strict';

var _ = require('lodash'),
    Location = require('./location.model'),
    Fault = require('./fault.model');

function Recovery(raw) {
    var model = this,
        _destination = new Location(),
        _follow = false,
        _motorway = false,
        _passengerRun = false,
        _relay = false,
        _unaccompaniedAfterLoading = false,
        _keysLocation = null,
        _fault = new Fault(),
        _distance = -1,
        _adults = null,
        _isTowing = false,
        _children = 0,
        _dogs = 0,
        _forceRecyFault = true,
        _reasonForce = true,
        _reasonExpert = false,
        _reasonRoadOps = false,
        _email = null,
        _franchiseEmail = null,
        _garage = null, // this is used by the UI to hold the recovery garage selected by the user, it is not saved in prime
        _destResourceId = null; //field to hold destination - Repairer: -1, Home = -2 and Other = 0

    if (raw) {
        _destination = raw.destination ? new Location(raw.destination) : _destination;
        _follow = raw.follow === true;
        _motorway = raw.motorway === true;
        _passengerRun = raw.passengerRun === true;
        _relay = raw.relay === true;
        _distance = _.isNumber(raw.distance) > -1 ? raw.distance : _distance;
        _adults = !(raw.adults === null || raw.adults === undefined) ? raw.adults : _adults;
        _children = raw.children ? raw.children : _children;
        _dogs = raw.dogs ? raw.dogs : _dogs;
        _unaccompaniedAfterLoading = raw.unaccompaniedAfterLoading === true;
        _keysLocation = raw.keysLocation ? raw.keysLocation : _keysLocation;
        _fault = raw.fault ? new Fault(raw.fault) : _fault;
        _forceRecyFault = raw.forceRecyFault === true;
        _reasonForce = raw.reasonForce === true;
        _reasonExpert = raw.reasonExpert === true;
        _reasonRoadOps = raw.reasonRoadOps === true;
        _isTowing = raw.isTowing ? raw.isTowing : _isTowing;
        _email = !(raw.email === null || raw.email === undefined) ? raw.email : _email;
        _franchiseEmail = !(raw.franchiseEmail === null || raw.franchiseEmail === undefined) ? raw.franchiseEmail : _franchiseEmail;
        _destResourceId = !(raw.destResourceId === null || raw.destResourceId === undefined) ? raw.destResourceId : _destResourceId;
    }

    _.extend(model, {
        isTowing: function isTowingAccessor(val) {
            return arguments.length > 0 ? (_isTowing = val) : _isTowing;
        },
        isSet: function recoveryTester() {
            // if one of those set then recovery object is active
            return _follow || _motorway || _passengerRun || _relay;
        },
        isDestResourceIdSet: function isDestResIdSet() {
            var retVal = false;
            if (_destResourceId !== null) {
                retVal = _destResourceId >= 0 || [-9, -8, -7, -6, -5, -4, -3, -2, -1].indexOf(_destResourceId) > -1;
            }
            return retVal;
        },
        unaccompanied: function unaccompanied() {
            return _.isNumber(_adults) && _adults + _dogs + _children === 0;
        },
        destination: function destinationAccessor(val) {
            return arguments.length ? (_destination = val) : _destination;
        },
        follow: function followAccessor(val) {
            return arguments.length ? (_follow = val) : _follow;
        },
        motorway: function motorwayAccessor(val) {
            return arguments.length ? (_motorway = val) : _motorway;
        },
        passengerRun: function passengerRunAccessor(val) {
            return arguments.length ? (_passengerRun = val) : _passengerRun;
        },
        relay: function relayAccessor(val) {
            return arguments.length ? (_relay = val) : _relay;
        },
        distance: function distanceAccessor(val) {
            return arguments.length ? (_distance = val) : _distance;
        },
        adults: function adultsAccessor(val) {
            return arguments.length ? (_adults = val) : _adults;
        },
        children: function childrenAccessor(val) {
            return arguments.length ? (_children = val) : _children;
        },
        dogs: function dogsAccessor(val) {
            return arguments.length ? (_dogs = val) : _dogs;
        },
        unaccompaniedAfterLoading: function unaccompaniedAfterLoadingAccessor(val) {
            return arguments.length ? (_unaccompaniedAfterLoading = val) : _unaccompaniedAfterLoading;
        },
        keysLocation: function keysLocationAccessor(val) {
            return arguments.length ? (_keysLocation = val) : _keysLocation;
        },
        fault: function faultAccessor(val) {
            return arguments.length ? (_fault = val) : _fault;
        },
        forceRecyFault: function forceRecyFaultAccessor(val) {
            return arguments.length ? (_forceRecyFault = val) : _forceRecyFault;
        },
        reasonForce: function reasonForceAccessor(val) {
            return arguments.length ? (_reasonForce = val) : _reasonForce;
        },
        reasonExpert: function reasonExpertAccessor(val) {
            return arguments.length ? (_reasonExpert = val) : _reasonExpert;
        },
        reasonRoadOps: function reasonRoadOpsAccessor(val) {
            return arguments.length ? (_reasonRoadOps = val) : _reasonRoadOps;
        },
        showMarker: function showMarker() {
            return model.isSet() && _destination.isSet();
        },
        garage: function garageAccessor(val) {
            return arguments.length ? (_garage = val) : _garage;
        },
        email: function email(val) {
            return arguments.length > 0 ? (_email = val) : _email;
        },
        franchiseEmail: function email(val) {
            return arguments.length > 0 ? (_franchiseEmail = val) : _franchiseEmail;
        },
        destResourceId: function destResourceIdAccessor(val) {
            return arguments.length ? (_destResourceId = val) : _destResourceId;
        },
        reset: function reset() {
            _destination = new Location();
            _follow = false;
            _motorway = false;
            _passengerRun = false;
            _relay = false;
            _unaccompaniedAfterLoading = false;
            _keysLocation = null;
            _fault = new Fault();
            _distance = -1;
            _adults = null;
            _children = 0;
            _dogs = 0;
            _forceRecyFault = true;
            _reasonForce = true;
            _reasonExpert = false;
            _reasonRoadOps = false;
            _garage = null;
            _email = null;
            _franchiseEmail = null;
            _destResourceId = null;
            _isTowing = false;
        },
        isValidRecovery: function isValidRecovery() {
            return !_relay || (_destination.isSet() && _destination.text() && _destination.area() && !_.isNaN(parseInt(_adults)) && this.isDestResourceIdSet());
        },
        isValidPassengerRun: function isValidPassengerRun() {
            return !_relay || (_destination.isSet() && !_.isNaN(parseInt(_adults)));
        },
        toJSON: function toJSON() {
            return {
                destination: _destination.toJSON(),
                fault: _fault.toJSON(),
                follow: _follow,
                motorway: _motorway,
                passengerRun: _passengerRun,
                relay: _relay,
                distance: _distance,
                adults: _adults,
                children: _children,
                dogs: _dogs,
                unaccompaniedAfterLoading: _unaccompaniedAfterLoading,
                keysLocation: _keysLocation,
                forceRecyFault: _forceRecyFault,
                reasonForce: _reasonForce,
                reasonExpert: _reasonExpert,
                reasonRoadOps: _reasonRoadOps,
                email: _email,
                franchiseEmail: _franchiseEmail,
                destResourceId: _destResourceId,
                isTowing: _isTowing
            };
        }
    });
}

Recovery.FUEL_ASSIST_RECOVERY_REMARK = 'LOCAL: FUEL ASSIST TASK';
Recovery.NEAREST_SAFE_PLACE_WITH_FACILITIES = 'LOCAL: NEAREST SAFE PLACE WITH FACILITIES';

module.exports = Recovery;
