'use strict';
var _ = require('lodash');
const Vulnerability = require('./vulnerability.model');

/**
 * @typedef EntitlementContactOption
 * @constructor
 */
function EntitlementContactOption(raw) {
    var model = this,
        _deceased = null,
        _goneAway = null,
        _specialNeeds = null,
        _vulnerability = new Vulnerability(),
        _additionalRoadsideSupport = false,
        _additionalRoadsideSupportInformation = null;

    if (raw) {
        _deceased = raw.deceased;
        _goneAway = raw.goneAway;
        _specialNeeds = raw.specialNeeds;
        _vulnerability = raw.vulnerability ? new Vulnerability(raw.vulnerability) : _vulnerability;
        _additionalRoadsideSupport = raw.hasOwnProperty('additionalRoadsideSupport') ? raw.additionalRoadsideSupport : false;
        _additionalRoadsideSupportInformation = raw.hasOwnProperty('additionalRoadsideSupportInformation') ? raw.additionalRoadsideSupportInformation : '';
    }

    _.extend(model, {
        deceased: function deceasedAccessor(val) {
            return arguments.length ? (_deceased = val) : _deceased;
        },
        goneAway: function goneAwayAccessor(val) {
            return arguments.length ? (_goneAway = val) : _goneAway;
        },
        specialNeeds: function specialNeedsAccessor(val) {
            return arguments.length ? (_specialNeeds = val) : _specialNeeds;
        },
        vulnerability: function vulnerabilityAccessor(val) {
            return arguments.length ? (_vulnerability = val) : _vulnerability;
        },
        additionalRoadsideSupport: function (val) {
            return arguments.length ? (_additionalRoadsideSupport = val) : _additionalRoadsideSupport;
        },
        additionalRoadsideSupportInformation: function (val) {
            return arguments.length ? (_additionalRoadsideSupportInformation = val) : _additionalRoadsideSupportInformation;
        },
        toJSON: function toJSON() {
            return {
                deceased: _deceased,
                goneAway: _goneAway,
                specialNeeds: _specialNeeds,
                vulnerability: _vulnerability ? _vulnerability.toJSON() : null,
                additionalRoadsideSupport: _additionalRoadsideSupport,
                additionalRoadsideSupportInformation: _additionalRoadsideSupportInformation,
            };
        },
    });
}

module.exports = EntitlementContactOption;
