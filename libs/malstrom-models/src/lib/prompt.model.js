'use strict';
var _ = require('lodash'),
    PromptOption = require('./prompt-option.model');

function Prompt(raw) {
    var model = this,
        _id = null,
        _name = 'Prompt',
        _text = null,
        _html = null,
        _htmlFile = null,
        _ignoreOnce = false,
        _options = [],
        _selectedOption = null,
        _data = {},
        _handlers = [];

    if (raw) {
        _id = !_.isNaN(parseInt(raw.id, 10)) ? parseInt(raw.id, 10) : _id;
        _name = _.isString(raw.name) ? raw.name : _name;
        _text = _.isString(raw.text) ? raw.text : _text;
        _html = _.isString(raw.html) ? raw.html : _html;
        _htmlFile = _.isString(raw.htmlFile) ? raw.htmlFile : _htmlFile;
        _ignoreOnce = raw.ignoreOnce === true;
        _data = raw.data || _data;
        _selectedOption = raw.selectedOption ? new PromptOption(raw.selectedOption) : _selectedOption;
        _.forEach(raw.options, function _forEachOption(rawOption) {
            _options.push(new PromptOption(rawOption));
        });
    }

    function _arrayToJSON(list) {
        var result = [];
        _.forEach(list, function _forEachItem(item) {
            result.push(item.toJSON());
        });
        return result;
    }

    function _notifyAll(promptOption) {
        _.forEach(_handlers, function _forEachOptionHandler(optionHandler) {
            optionHandler(promptOption);
        });
    }

    _.extend(model, {
        id: function idAccessor(val) {
            return arguments.length ? (_id = val) : _id;
        },
        name: function nameAccessor(val) {
            return arguments.length ? (_name = val) : _name;
        },
        text: function textAccessor(val) {
            return arguments.length ? (_text = val) : _text;
        },
        html: function htmlAccessor(val) {
            return arguments.length ? (_html = val) : _html;
        },
        htmlFile: function htmlFileAccessor(val) {
            return arguments.length ? (_htmlFile = val) : _htmlFile;
        },
        ignoreOnce: function ignoreOnceAccessor(val) {
            return arguments.length ? (_ignoreOnce = val) : _ignoreOnce;
        },
        options: function optionsAccessor(val) {
            return arguments.length ? (_options = val) : _options;
        },
        data: function dataAccessor(val) {
            return arguments.length ? (_data = val) : _data;
        },
        selectedOption: function selectedOption(val) {
            if (arguments.length) {
                _selectedOption = val;
                _notifyAll(model);
            }
            return _selectedOption;
        },
        addOptionHandler: function addOptionHandler(handler) {
            if (handler && _handlers.indexOf(handler) === -1) {
                _handlers.push(handler);
            }
        },
        removeAllOptionHandlers: function removeAllOptionHandlers() {
            _handlers.length = 0;
        },
        getPromptType: function getPromptType() {
            if (_htmlFile && !_html && !_text) {
                return Prompt.HTML_FILE_PROMPT_TYPE;
            } else if (!_htmlFile && _html) {
                return Prompt.RAW_HTML_PROMPT_TYPE;
            } else {
                return Prompt.PLAIN_TEXT_PROMPT_TYPE;
            }
        },
        toJSON: function toJSON() {
            return {
                id: _id,
                name: _name,
                text: _text,
                html: _html,
                htmlFile: _htmlFile,
                ignoreOnce: _ignoreOnce,
                selectedOption: _selectedOption ? _selectedOption.toJSON() : null,
                options: _arrayToJSON(_options)
            };
        }
    });
}

/**
 * WHEN ADDING A NEW PROMPT YOU ALSO NEED TO ADD A DEFITION IN
 * aa-reference-data-service-express/lib/repository/static-prompts.repository.js
 */

Prompt.RAW_HTML_PROMPT_TYPE = 1;
Prompt.HTML_FILE_PROMPT_TYPE = 0;
Prompt.PLAIN_TEXT_PROMPT_TYPE = 2;

// static/client side prompts
Prompt.PERSONAL_NOT_ENTITLED_FOR_RELAY = 1000;
Prompt.FLP_PROMPT = 1001;
Prompt.ENTITLEMENTS_EXPIRED = 1002;
Prompt.LOCAL_RECOVERY_DISTANCE_EXCEEDED = 1004;
Prompt.LOCAL_RECOVERY_WARN = 1005;
Prompt.NOT_ENTITLED_TO_HOMESTART = 1007;
Prompt.SECOND_RECOVERY = 1008;
Prompt.VEHICLE_HAS_ADDITIONAL_POLICIES = 1010;
Prompt.NO_ENTITLEMENTS_FOUND = 1011;
Prompt.VEHICLE_CHANGED_PROMPT = 1012;
Prompt.NEW_MEMBERSHIP = 1014;
Prompt.DATA_CHANGED = 1015;
Prompt.INCIDENT_INDICATOR = 1016;
Prompt.UPGRADE_SUCCESSFUL = 1017;
Prompt.REFUSE_RELAY_SERVICE = 1019;
Prompt.CASE_HAS_NEW_TASK = 1020;
Prompt.SERVICE_ADJUSTMENT_RELAY = 1021;
Prompt.HOMESTART_UPGRADE_GOLD_MEMBER_OVER_10_YEARS = 1022;
Prompt.HOMESTART_UPGRADE_GOLD_MEMBER_5_TO_10_YEARS = 1023;
Prompt.TASK_OUT_OF_SEQUENCE = 1024;
Prompt.MISSING_MEMBER_DETAILS = 1025;
Prompt.PFU_SCRIPT = 1026;
Prompt.NO_RESULTS_FOUND = 1027;
Prompt.BATTERY_ASSIST_WARRANTY = 1028;
Prompt.INVALID_LOCATION = 1029;
Prompt.INACCURATE_LOCATION = 1030;
Prompt.SERVICE_ADJUSTMENT_RELAY_PLUS = 1031;
Prompt.SAGA_NOT_ENTITLED_TO_HOMESTART = 1032;
Prompt.CLI_ENTITLEMENT = 1034;
Prompt.TASK_COMPLETED_WITHIN_SEVEN_DAYS = 1035;
Prompt.AGENT_LOGGED_OFF = 1036;
Prompt.NO_REFUSE_LBG_CUSTOMERS = 1037;
Prompt.SCHEDULE_WINDOW_OPEN = 1038;
Prompt.CHANGE_INSURANCE_OPTION = 1039;
Prompt.ECALL = 1040;
Prompt.SAVE_TASK_WITHOUT_CONTACT_NUMBER = 1041;
Prompt.ADD_CASE_ON_A_LIVE_BREAKDOWN = 1042;
Prompt.CLEAR_INCIDENT_INDICATOR = 1043;
Prompt.CHECK_BANK_CUSTOMER_HAS_UPGRADE = 1044;
Prompt.CONTRACT_VALIDATION = 1045;
Prompt.TASK_OVERRIDE = 1046;
Prompt.VIP_INDICATOR = 1047;
Prompt.VEHICLE_GROSS_WEIGHT_OVER_LIMIT = 1048;
Prompt.ROI = 1049;
Prompt.CORRESPONDENCE_ADDRESS = 1050;
Prompt.UBER_RECOVERY = 1051;
Prompt.VEHICLE_CHANGED_CREATE_NEW_TASK = 1052;
Prompt.WILL_JOIN_TASK = 1053;
Prompt.CORRESPONDENCE_ADDRESS_JOINT_MEMBER = 1054;
Prompt.HONDA_ASSISTANCE_PROMPT = 1055;
Prompt.CORRESPONDENCE_ADDRESS_VEHICLE_BASED_MEMBER = 1056;
Prompt.TASK_MERGE = 1057;
Prompt.DANGEROUS_LOCATION_ENTRY_JUNCTION = 1058;
Prompt.DUAL_COVER_CHECK = 1059;
Prompt.ADMIRAL_PREVENT_HOMESTART_PROMPT = 1060;
Prompt.ADMIRAL_PREVENT_RECOVERY_PROMPT = 1061;
Prompt.CHECK_MILEAGE_EXISTS = 1062;
Prompt.LOCAL_DRIVER_PROMPT = 1063;
Prompt.STANDBY_EARLY_PROMPT = 1064;
Prompt.BUZBY_CALL_DETAILS_PROMPT = 1065;
Prompt.CALLOUT_HISTORY_PROMPT = 1066;
Prompt.CALLOUT_HISTORY_LATTER_PROMPT = 1067;
Prompt.NO_SLOT_ACCEPTABLE = 1068;
Prompt.NO_REFUSE_NBS_CUSTOMERS = 1069;
Prompt.NBS_GROUP_VEHICLE_GROSS_WEIGHT_OVER_LIMIT = 1070;
Prompt.ELECTRIC_VEHICLE_DIAGNOSTIC = 1071;
Prompt.ADMIRAL_LOCAL_RECOVERY_DISTANCE_EXCEEDED = 1072;
Prompt.ADMIRAL_SERVICE_ADJUSTMENT_RELAY = 1073;
Prompt.VULNERABLE_CUSTOMER = 1074;
Prompt.NO_REFUSE_NWG_CUSTOMERS = 1075;
Prompt.NO_REFUSE_RBS_CUSTOMERS = 1076;
Prompt.NO_SERVICE_ACCOUNT = 1077;
Prompt.COMPASS_NO_AUTHORIZATION = 1078;
Prompt.ROADSIDE_ADDON_NOT_ENTITLED_TO_HOMESTART = 1079;
Prompt.ROADSIDE_ADDON_LOCAL_RECOVERY_DISTANCE_EXCEEDED = 1080;
Prompt.ROADSIDE_ADDON_EXCESSIVE_USE_NOTICE = 1081;
Prompt.ROADSIDE_ADDON_EXCESSIVE_USE_SAFE_LOCATION = 1082;
Prompt.ROADSIDE_ADDON_EXCESSIVE_USE_SAFE_LOCATION_CHASE = 1093;
Prompt.ROADSIDE_ADDON_EXCESSIVE_USE_DANGEROUS_LOCATION = 1083;
Prompt.TASK_COMPLETED_WITHIN_SEVEN_DAYS_REJECTED_FLP = 1084;
Prompt.SPECIALIST_VEH = 1085;
Prompt.CONFLICTING_RECORDS_FOUND = 1086;
Prompt.ADMIRAL_VEHICLE_GROSS_WEIGHT_OVER_LIMIT = 1087;
Prompt.CUSTOMER_NOT_WITH_VEHICLE_CHAS = 1088;
Prompt.INVALID_VEHICLE_VRN = 1089;
Prompt.UPGRADE_CUV = 1090;
Prompt.SUSPENDED_POLICY = 1091;
Prompt.CHASE_EXCESSIVE_USE_NOTICE = 1092;
Prompt.CUSTOMER_VULNERABILITY = 1093;

module.exports = Prompt;
