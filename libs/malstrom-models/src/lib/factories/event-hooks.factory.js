'use strict';
const { CustomerGroup } = require('@aa/data-models/common');
const EventHook = require('../event-hooks.model');
const EventHookFlags = require('../event-hook-flags.model');

/**
 * Get Roam active or not
 * @param {Task} task
 * @returns {Boolean}
 */
function isRoamActive(task) {
    if (task && task.entitlement() && task.entitlement().customerGroup()) {
        const customerGroupCode = task.entitlement().customerGroup().code();
        if (customerGroupCode) {
            return CustomerGroup.ROAM_ENABLED_CUST_GROUPS.includes(customerGroupCode);
        }
    }
    return EventHook.DEFAULT_ROAM_ACTIVE;
}

/**
 * Get ROAM event hook
 * @param {Task} task
 * @returns {EventHookFlags}
 */
function getRoamEventHook(task) {
    const roamActive = isRoamActive(task);
    const eventHookFlag = new EventHookFlags();
    eventHookFlag.active(roamActive);
    return eventHookFlag;
}

/**
 * set SMS eventHook flags
 * @param {Task} task
 */
function isSmsActive(task) {
    if (task && task.entitlement() && task.entitlement().customerGroup()) {
        const customerGroupCode = task.entitlement().customerGroup().code();
        if (customerGroupCode) {
            if (CustomerGroup.SUPPRESS_SMS_CUST_GROUPS.includes(customerGroupCode)) {
                return false;
            }
            // CUST_GROUP in SUPPRESS_INBOUND_ONLY, check if it is created as SELF_SERVICE
            if (CustomerGroup.SUPPRESS_SMS_INBOUND_ONLY.includes(customerGroupCode) && task.createReason().isSelfServiceApp()) {
                return false;
            }
        }
    }
    return EventHook.DEFAULT_SMS_ACTIVE;
}

/**
 * Get SMS eventHook
 * @param {Task} task
 * @returns {EventHookFlags}
 */
function getSmsEventHook(task) {
    const smsActive = isSmsActive(task);
    const eventHookFlag = new EventHookFlags();
    eventHookFlag.active(smsActive);
    return eventHookFlag;
}

/**
 * check valid create reason to attach eventHook
 * @param {*} task
 * @returns boolean
 */
function isValidCreateReason(task) {
    return task.createReason().isBreakdown() || task.createReason().isHireCar() || task.createReason().isHotel() || task.createReason().isTransport();
}

/**
 * Set event hooks
 * @param {Task} task
 */
function getEventHooks(task) {
    // set eventHooks for RSS only
    if (!task) {
        return null;
    }

    if (isValidCreateReason(task)) {
        const roamEventHook = getRoamEventHook(task);
        const eventHook = new EventHook();
        eventHook.roam(roamEventHook);
        const smsEventHook = getSmsEventHook(task);
        eventHook.sms(smsEventHook);
        return eventHook;
    }
    return null;
}

module.exports = {
    isRoamActive: isRoamActive,
    isSmsActive: isSmsActive,
    getRoamEventHook: getRoamEventHook,
    getSmsEventHook: getSmsEventHook,
    getEventHooks: getEventHooks
};
