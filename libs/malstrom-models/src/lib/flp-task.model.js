'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    AbstractTask = require('./abstract-task.model'),
    Contact = require('./contact.model'),
    Schedule = require('./task-schedule.model'),
    RefCode = require('./ref-code.model');

function FlpTask(raw) {
    var model = this,
        _taskType = new RefCode({
            code: 'FLP',
            name: 'Front line policy task'
        }),
        _schedule = new Schedule(),
        _customerRequestId = 0,
        _remark = null,
        _taskStatusTime = new Date(),
        _taskStartTime = new Date(),
        _furtherActionText = '',
        _reasonText = '',
        _actionTakenText = '',
        _payment = 0,
        _partValue = 0,
        _hireCarCost = 0,
        _otherCosts = 0,
        _originatorForename = '',
        _originatorSurname = '',
        _patrolCallSign = '',
        _originatorId = 0,
        _address = new Address(),
        _contact = new Contact(),
        _cardName = '',
        _callerName = '',
        _operatorId = 0,
        _busLocnId = 0,
        _id = 0,
        _taskTypeId1 = 0,
        _taskId = 0,
        _flpTypeText = '',
        _reasonId = 0,
        _actionTakenId = 0,
        _uacRequestId = '',
        _uacRejected = false;

    if (raw) {
        _customerRequestId = raw.customerRequestId ? raw.customerRequestId : _customerRequestId;
        _remark = raw.remark ? raw.remark : _remark;
        _taskStatusTime = raw.taskStatusTime ? raw.taskStatusTime : _taskStatusTime;
        _taskStartTime = raw.taskStartTime ? raw.taskStartTime : _taskStartTime;
        _furtherActionText = raw.furtherActionText ? raw.furtherActionText : _furtherActionText;
        _reasonText = raw.reasonText ? raw.reasonText : _reasonText;
        _actionTakenText = raw.actionTakenText ? raw.actionTakenText : _actionTakenText;
        _payment = raw.payment ? raw.payment : _payment;
        _partValue = raw.partValue ? raw.partValue : _partValue;
        _hireCarCost = raw.hireCarCost ? raw.hireCarCost : _hireCarCost;
        _otherCosts = raw.otherCosts ? raw.otherCosts : _otherCosts;
        _originatorForename = raw.originatorForename ? raw.originatorForename : _originatorForename;
        _originatorSurname = raw.originatorSurname ? raw.originatorSurname : _originatorSurname;
        _patrolCallSign = raw.patrolCallSign ? raw.patrolCallSign : _patrolCallSign;
        _originatorId = raw.originatorId ? raw.originatorId : _originatorId;
        _address = raw.address ? new Address(raw.address) : _address;
        _cardName = raw.cardName ? raw.cardName : _cardName;
        _callerName = raw.callerName ? raw.callerName : _callerName;
        _operatorId = raw.operatorId ? raw.operatorId : _operatorId;
        _busLocnId = raw.busLocnId ? raw.busLocnId : _busLocnId;
        _id = raw.id ? raw.id : _id;
        _taskTypeId1 = raw.taskTypeId1 ? raw.taskTypeId1 : _taskTypeId1;
        _schedule = raw.schedule ? new Schedule(raw.schedule) : _schedule;
        _contact = raw.contact ? new Contact(raw.contact) : _contact;
        _taskId = raw.taskId ? raw.taskId : _taskId;
        _flpTypeText = raw.flpTypeText ? raw.flpTypeText : _flpTypeText;
        _reasonId = raw.reasonId ? raw.reasonId : _reasonId;
        _actionTakenId = raw.actionTakenId ? raw.actionTakenId : _actionTakenId;
        _uacRequestId = raw.uacRequestId ? raw.uacRequestId : _uacRequestId;
        _uacRejected = raw.uacRejected ? raw.uacRejected : _uacRejected;
    }

    _.extend(model, new AbstractTask(), {
        customerRequestId: function customerRequestIdAccessor(val) {
            return arguments.length ? (_customerRequestId = val) : _customerRequestId;
        },
        taskType: function taskTypeAccessor() {
            return _taskType;
        },
        remark: function remarkAccessor(val) {
            return arguments.length ? (_remark = val) : _remark;
        },
        taskStatusTime: function taskStatusTimeAccessor(val) {
            return arguments.length ? (_taskStatusTime = val) : _taskStatusTime;
        },
        taskStartTime: function taskStartTimeAccessor(val) {
            return arguments.length ? (_taskStartTime = val) : _taskStartTime;
        },
        furtherActionText: function furtherActionTextAccessor(val) {
            return arguments.length ? (_furtherActionText = val) : _furtherActionText;
        },
        reasonText: function reasonTextAccessor(val) {
            return arguments.length ? (_reasonText = val) : _reasonText;
        },
        actionTakenText: function actionTakenTextAccessor(val) {
            return arguments.length ? (_actionTakenText = val) : _actionTakenText;
        },
        payment: function paymentAccessor(val) {
            return arguments.length ? (_payment = val) : _payment;
        },
        partValue: function partValueAccessor(val) {
            return arguments.length ? (_partValue = val) : _partValue;
        },
        hireCarCost: function hireCarCostAccessor(val) {
            return arguments.length ? (_hireCarCost = val) : _hireCarCost;
        },
        otherCosts: function otherCostsAccessor(val) {
            return arguments.length ? (_otherCosts = val) : _otherCosts;
        },
        originatorForename: function originatorForenameAccessor(val) {
            return arguments.length ? (_originatorForename = val) : _originatorForename;
        },
        originatorSurname: function originatorSurnameAccessor(val) {
            return arguments.length ? (_originatorSurname = val) : _originatorSurname;
        },
        patrolCallSign: function patrolCallSignAccessor(val) {
            return arguments.length ? (_patrolCallSign = val) : _patrolCallSign;
        },
        originatorId: function originatorIdAccessor(val) {
            return arguments.length ? (_originatorId = val) : _originatorId;
        },
        address: function addressAccessor(val) {
            return arguments.length ? (_address = val) : _address;
        },
        contact: function contactAccessor(val) {
            return arguments.length ? (_contact = val) : _contact;
        },
        cardName: function cardNameAccessor(val) {
            return arguments.length ? (_cardName = val) : _cardName;
        },
        callerName: function callerNameAccessor(val) {
            return arguments.length ? (_callerName = val) : _callerName;
        },
        operatorId: function operatorIdAccessor(val) {
            return arguments.length ? (_operatorId = val) : _operatorId;
        },
        busLocnId: function busLocnIdAccessor(val) {
            return arguments.length ? (_busLocnId = val) : _busLocnId;
        },
        id: function taskIdAccessor(val) {
            return arguments.length ? (_id = val) : _id;
        },
        taskTypeId1: function taskTypeId1Accessor(val) {
            return arguments.length ? (_taskTypeId1 = val) : _taskTypeId1;
        },
        schedule: function scheduleAccessor(val) {
            return arguments.length ? (_schedule = val) : _schedule;
        },
        taskId: function taskIdAccessor(val) {
            return arguments.length ? (_taskId = val) : _taskId;
        },
        flpTypeText: function flpTypeTextAccessor(val) {
            return arguments.length ? (_flpTypeText = val) : _flpTypeText;
        },
        reasonId: function reasonIdAccessor(val) {
            return arguments.length ? (_reasonId = val) : _reasonId;
        },
        actionTakenId: function actionTakenIdAccessor(val) {
            return arguments.length ? (_actionTakenId = val) : _actionTakenId;
        },
        uacRequestId: function uacRequestIdAccessor(val) {
            return arguments.length ? (_uacRequestId = val) : _uacRequestId;
        },
        uacRejected: function uacRejectedAccessor(val) {
            return arguments.length ? (_uacRejected = val) : _uacRejected;
        },
        toJSON: function toJSON() {
            return {
                customerRequestId: _customerRequestId,
                remark: _remark,
                taskStatusTime: _taskStatusTime,
                taskStartTime: _taskStartTime,
                furtherActionText: _furtherActionText,
                reasonText: _reasonText,
                actionTakenText: _actionTakenText,
                payment: _payment,
                partValue: _partValue,
                hireCarCost: _hireCarCost,
                otherCosts: _otherCosts,
                originatorForename: _originatorForename,
                originatorSurname: _originatorSurname,
                patrolCallSign: _patrolCallSign,
                originatorId: _originatorId,
                address: _address ? _address.toJSON() : null,
                contact: _contact ? _contact.toJSON() : null,
                cardName: _cardName,
                callerName: _callerName,
                operatorId: _operatorId,
                busLocnId: _busLocnId,
                id: _id,
                taskTypeId1: _taskTypeId1,
                schedule: _schedule ? _schedule.toJSON() : null,
                taskType: _taskType.toJSON(),
                taskId: _taskId,
                flpTypeText: _flpTypeText,
                reasonId: _reasonId,
                actionTakenId: _actionTakenId,
                uacRequestId: _uacRequestId,
                uacRejected: _uacRejected
            };
        }
    });
}

module.exports = FlpTask;
