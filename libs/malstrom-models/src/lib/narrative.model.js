'use strict';
var _ = require('lodash');

/**
 * Constructor for raw narrative object
 * @param raw
 * @constructor
 */
function Narrative(raw) {
    var model = this,
        _text = null,
        _date = null,
        _type = null;

    if (raw) {
        _date = raw.date ? new Date(raw.date) : null;
        _text = raw.text;
        _type = raw.type;
    }

    _.extend(model, {
        text: function textAccessor(val) {
            return arguments.length ? (_text = val) : _text;
        },
        date: function dateAccessor(val) {
            return arguments.length ? (_date = val) : _date;
        },
        type: function typeAccessor(val) {
            return arguments.length ? (_type = val) : _type;
        },
        toJSON: function toJSON() {
            return {
                text: _text,
                date: _date,
                type: _type,
            };
        },
    });
}

module.exports = Narrative;
