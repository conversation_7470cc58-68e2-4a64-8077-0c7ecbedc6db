'use strict';

const { Usage } = require('@aa/data-models/common');
var _ = require('lodash'),
    _arrayObjectToJSON = require('./factories/array-object-to-json.factory');
/**
 * @typedef Product
 * @constructor
 */
function Product(raw) {
    const _dateWithTimeZoneOffset = function (dateString) {
        if (_.isDate(dateString)) {
            return dateString;
        }
        return new Date(new Date(dateString).valueOf() + new Date(dateString).getTimezoneOffset() * 60000);
    };

    var model = this,
        _name = null,
        _code = null,
        _claimFromDateTime = null,
        _startDate = null,
        _endDate = null,
        _benefitCode = null,
        _expired = false,
        _coveredVehicle = [],
        _coolOffPeriod = false,
        _additionalProperties = [],
        _usage = [];

    if (raw) {
        _name = raw.name;
        _code = raw.code;
        _claimFromDateTime = raw.claimFromDateTime ? _dateWithTimeZoneOffset(raw.claimFromDateTime) : null;

        _startDate = raw.startDate ? new Date(raw.startDate) : null;
        _endDate = raw.endDate ? new Date(raw.endDate) : null;
        _benefitCode = raw.benefitCode;
        _expired = raw.expired;
        _coveredVehicle = raw.coveredVehicle || _coveredVehicle;
        _coolOffPeriod = raw.coolOffPeriod ? raw.coolOffPeriod : _coolOffPeriod;
        _additionalProperties = raw.additionalProperties || _additionalProperties;

        if (raw.usage) {
            //lodash doesn't really need this but...
            _.forEach(raw.usage, function _forEachUsage(rawUsage) {
                _usage.push(new Usage(rawUsage));
            });
        }
    }

    _.extend(model, {
        /** @param {string} val */
        name: function nameAccessor(val) {
            return arguments.length ? (_name = val) : _name;
        },

        /** @param {string} val */
        code: function codeAccessor(val) {
            return arguments.length ? (_code = val) : _code;
        },

        /** @param {date} val */
        claimFromDateTime: function claimFromDateTimeAccessor(val) {
            return arguments.length ? (_claimFromDateTime = val) : _claimFromDateTime;
        },

        /** @param {date} val */
        startDate: function startDateAccessor(val) {
            return arguments.length ? (_startDate = val) : _startDate;
        },

        /** @param {date} val */
        endDate: function endDateAccessor(val) {
            return arguments.length ? (_endDate = val) : _endDate;
        },

        /** @param {string} val */
        benefitCode: function benefitCodeAccessor(val) {
            return arguments.length ? (_benefitCode = val) : _benefitCode;
        },
        /** @param {string} val */
        expired: function expiredAccessor(val) {
            return arguments.length ? (_expired = val) : _expired;
        },
        coolOffPeriod: function coolOffPeriodAccessor(val) {
            return arguments.length ? (_coolOffPeriod = val) : _coolOffPeriod;
        },
        coveredVehicle: function coveredVehicleAccessor(val) {
            return arguments.length ? (_coveredVehicle = val) : _coveredVehicle;
        },
        additionalProperties: function additionalPropertiesAccessor(val) {
            return arguments.length ? (_additionalProperties = val) : _additionalProperties;
        },
        getAdditionalProperty: function (key) {
            let result = arguments.length ? _.find(_additionalProperties, key) : null;
            return result ? result : null;
        },
        usage: function usageAccessor(val) {
            return arguments.length ? (_usage = val) : _usage;
        },
        isVehicleCovered: function isVehicleCovered(vrn) {
            const _coveredVehicleLowerCase = _.map(_coveredVehicle, function (v) {
                return v.toLowerCase();
            });
            const _vnrLowerCase = vrn.toLowerCase();
            return _.includes(_coveredVehicleLowerCase, _vnrLowerCase) && !_expired;
        },
        isCUV: function isCUV() {
            return _benefitCode === 'CUV';
        },
        isBRC: function isBRC() {
            return _benefitCode === 'BRC';
        },
        toJSON: function toJSON() {
            return {
                name: _name,
                code: _code,
                claimFromDateTime: _claimFromDateTime,
                startDate: _startDate,
                endDate: _endDate,
                benefitCode: _benefitCode,
                expired: _expired,
                coveredVehicle: _coveredVehicle,
                coolOffPeriod: _coolOffPeriod,
                additionalProperties: _additionalProperties,
                usage: _arrayObjectToJSON(_usage)
            };
        }
    });
}

//product codes
Product.RELAY = 'Relay';
Product.HOMESTART = 'HomeStart';
Product.ROADSIDE = 'RoadSide';
Product.CONNECTED_CAR = 'CONC';
Product.CAR_GENIE = 'CONN';
Product.COMMERCIAL_USE = 'Commercial Use';

module.exports = Product;
