'use strict';

const { Product, EntitlementContact } = require('@aa/data-models/aux/entitlement');
const { Policy } = require('@aa/data-models/common');
var _ = require('lodash'),
    _arrayObjectToJSON = require('./factories/array-object-to-json.factory');

/**
 * @typedef BrcEntitlement
 * @constructor
 */
function BrcEntitlement(raw) {
    var model = this,
        _systemId = null,
        _contact = new EntitlementContact(),
        _policy = new Policy(),
        _products = [];

    if (raw) {
        _systemId = raw.systemId;
        _contact = raw.contact ? new EntitlementContact(raw.contact) : _contact;
        _policy = raw.policy ? new Policy(raw.policy) : _policy;

        _.forEach(raw.products, function _forEachProduct(rawProduct) {
            _products.push(new Product(rawProduct));
        });
    }

    _.extend(model, {
        systemId: function systemIdAccessor(val) {
            return arguments.length ? (_systemId = val) : _systemId;
        },
        contact: function contactAccessor(val) {
            return arguments.length ? (_contact = val) : _contact;
        },
        policy: function policyAccessor(val) {
            return arguments.length ? (_policy = val) : _policy;
        },
        products: function productsAccessor(val) {
            return arguments.length ? (_products = val) : _products;
        },
        toJSON: function toJSON() {
            return {
                systemId: _systemId,
                contact: _contact ? _contact.toJSON() : null,
                policy: _policy ? _policy.toJSON() : null,
                products: _arrayObjectToJSON(_products)
            };
        }
    });
}

module.exports = BrcEntitlement;
