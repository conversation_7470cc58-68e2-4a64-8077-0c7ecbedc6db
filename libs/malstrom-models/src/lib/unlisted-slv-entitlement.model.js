'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    UnlistedCover = require('./unlisted-cover.model');

/**
 * @typedef Entitlement
 * @constructor
 */
function UnlistedSlvEntitlement(raw) {
    var model = this,
        _accountBalance = null,
        _cardCover = null,
        _cardNo = null,
        _cardNoFlv = null,
        _cardType = null,
        _cardVariableData = [],
        _company = null,
        _coverAddress = new Address(),
        _customerGroup = null,
        _discountSet = null,
        _driverDetails = [],
        _entitlementVariableData = [], // returning array of vanilla javascript object i.e. no accessor functions
        _extendedProductCategory = null,
        _fairPlay = null,
        _flv = null,
        _internalGroupId = null,
        _invoicePointSet = null,
        _issueNumber = null,
        _membershipInceptionDate = null,
        _membershipNumber = null,
        _membershipType = null,
        _numberOfPassengers = null,
        _payForUse = null,
        _policyId = null,
        _provenanceCode = null,
        _provenanceGroup = null,
        _status = null,
        _supplierOwnRef = null,
        _systemId = null,
        _totalPurchasePrice = null,
        _tripDuration = null,
        _vehicleDetails = [],
        _vipServiceCode = null,
        _isSplit = null,
        _unlistedCover = new UnlistedCover();

    if (raw) {
        _accountBalance = raw.accountBalance;
        _cardCover = raw.cardCover;
        _cardNo = raw.cardNo;
        _cardNoFlv = raw.cardNoFlv;
        _cardType = raw.cardType;
        _cardVariableData = raw.cardVariableData;
        _company = raw.company;
        _coverAddress = raw.coverAddress ? new Address(raw.coverAddress) : _coverAddress;
        _customerGroup = raw.customerGroup;
        _discountSet = raw.discountSet;
        _driverDetails = raw.driverDetails;
        _entitlementVariableData = raw.entitlementVariableData || [];
        _extendedProductCategory = raw.extendedProductCategory;
        _fairPlay = raw.fairPlay;
        _flv = raw.flv;
        _internalGroupId = raw.internalGroupId;
        _invoicePointSet = raw.invoicePointSet;
        _issueNumber = raw.issueNumber;
        _membershipInceptionDate = raw.membershipInceptionDate;
        _membershipNumber = raw.membershipNumber;
        _membershipType = raw.membershipType;
        _numberOfPassengers = raw.numberOfPassengers;
        _payForUse = raw.payForUse;
        _policyId = raw.policyId;
        _provenanceCode = raw.provenanceCode;
        _provenanceGroup = raw.provenanceGroup;
        _status = raw.status;
        _supplierOwnRef = raw.supplierOwnRef;
        _systemId = raw.systemId;
        _totalPurchasePrice = raw.totalPurchasePrice;
        _tripDuration = raw.tripDuration;
        _vehicleDetails = raw.vehicleDetails || [];
        _vipServiceCode = raw.vipServiceCode;
        _isSplit = raw.isSplit;
        _unlistedCover = raw.unlistedCover ? new UnlistedCover(raw.unlistedCover) : _unlistedCover;
    }

    _.extend(model, {
        accountBalance: function accountBalanceAccessor(val) {
            return arguments.length ? (_accountBalance = val) : _accountBalance;
        },
        cardCover: function cardCoverAccessor(val) {
            return arguments.length ? (_cardCover = val) : _cardCover;
        },
        cardNo: function cardNoAccessor(val) {
            return arguments.length ? (_cardNo = val) : _cardNo;
        },
        cardNoFlv: function cardNoFlvAccessor(val) {
            return arguments.length ? (_cardNoFlv = val) : _cardNoFlv;
        },
        cardType: function cardTypeAccessor(val) {
            return arguments.length ? (_cardType = val) : _cardType;
        },
        cardVariableData: function cardVariableDataAccessor(val) {
            return arguments.length ? (_cardVariableData = val) : _cardVariableData;
        },
        company: function companyAccessor(val) {
            return arguments.length ? (_company = val) : _company;
        },
        coverAddress: function coverAddressAccessor(val) {
            return arguments.length ? (_coverAddress = val) : _coverAddress;
        },
        customerGroup: function customerGroupAccessor(val) {
            return arguments.length ? (_customerGroup = val) : _customerGroup;
        },
        entitlementVariableData: function entitlementVariableDataAccessor(val) {
            return arguments.length ? (_entitlementVariableData = val) : _entitlementVariableData;
        },
        discountSet: function discountSetAccessor(val) {
            return arguments.length ? (_discountSet = val) : _discountSet;
        },
        driverDetails: function driverDetailsSetAccessor(val) {
            return arguments.length ? (_driverDetails = val) : _driverDetails;
        },
        extendedProductCategory: function extendedProductCategoryAccessor(val) {
            return arguments.length ? (_extendedProductCategory = val) : _extendedProductCategory;
        },
        fairPlay: function fairPlayAccessor(val) {
            return arguments.length ? (_fairPlay = val) : _fairPlay;
        },
        flv: function flvAccessor(val) {
            return arguments.length ? (_flv = val) : _flv;
        },
        internalGroupId: function internalGroupIdAccessor(val) {
            return arguments.length ? (_internalGroupId = val) : _internalGroupId;
        },
        invoicePointSet: function invoicePointSetAccessor(val) {
            return arguments.length ? (_invoicePointSet = val) : _invoicePointSet;
        },
        issueNumber: function issueNumberAccessor(val) {
            return arguments.length ? (_issueNumber = val) : _issueNumber;
        },
        membershipInceptionDate: function membershipInceptionDateAccessor(val) {
            return arguments.length ? (_membershipInceptionDate = val) : _membershipInceptionDate;
        },
        membershipNumber: function membershipNumberAccessor(val) {
            return arguments.length ? (_membershipNumber = val) : _membershipNumber;
        },
        membershipType: function membershipTypeAccessor(val) {
            return arguments.length ? (_membershipType = val) : _membershipType;
        },
        numberOfPassengers: function numberOfPassengersAccessor(val) {
            return arguments.length ? (_numberOfPassengers = val) : _numberOfPassengers;
        },
        payForUse: function payForUseAccessor(val) {
            return arguments.length ? (_payForUse = val) : _payForUse;
        },
        policyId: function policyIdAccessor(val) {
            return arguments.length ? (_policyId = val) : _policyId;
        },
        provenanceCode: function provenanceCodeAccessor(val) {
            return arguments.length ? (_provenanceCode = val) : _provenanceCode;
        },
        provenanceGroup: function provenanceGroupAccessor(val) {
            return arguments.length ? (_provenanceGroup = val) : _provenanceGroup;
        },
        status: function statusAccessor(val) {
            return arguments.length ? (_status = val) : _status;
        },
        supplierOwnRef: function supplierOwnRefAccessor(val) {
            return arguments.length ? (_supplierOwnRef = val) : _supplierOwnRef;
        },
        systemId: function systemIdAccessor(val) {
            return arguments.length ? (_systemId = val) : _systemId;
        },
        totalPurchasePrice: function totalPurchasePriceAccessor(val) {
            return arguments.length ? (_totalPurchasePrice = val) : _totalPurchasePrice;
        },
        tripDuration: function tripDurationAccessor(val) {
            return arguments.length ? (_tripDuration = val) : _tripDuration;
        },
        vehicleDetails: function vehicleDetailsAccessor(val) {
            return arguments.length ? (_vehicleDetails = val) : _vehicleDetails;
        },
        vipServiceCode: function vipServiceCodeAccessor(val) {
            return arguments.length ? (_vipServiceCode = val) : _vipServiceCode;
        },
        unlistedCover: function unlistedCoverAccessor(val) {
            return arguments.length ? (_unlistedCover = val) : _unlistedCover;
        },
        isSplit: function isSplitAccessor(val) {
            return arguments.length ? (_isSplit = val) : _isSplit;
        },

        toJSON: function toJSON() {
            return {
                accountBalance: _accountBalance,
                cardCover: _cardCover,
                cardNo: _cardNo,
                cardNoFlv: _cardNoFlv,
                cardType: _cardType,
                cardVariableData: _cardVariableData,
                company: _company,
                coverAddress: _coverAddress ? _coverAddress.toJSON() : null,
                customerGroup: _customerGroup,
                discountSet: _discountSet,
                driverDetails: _driverDetails,
                entitlementVariableData: _entitlementVariableData,
                extendedProductCategory: _extendedProductCategory,
                fairPlay: _fairPlay,
                flv: _flv,
                internalGroupId: _internalGroupId,
                invoicePointSet: _invoicePointSet,
                issueNumber: _issueNumber,
                membershipInceptionDate: _membershipInceptionDate,
                membershipNumber: _membershipNumber,
                membershipType: _membershipType,
                numberOfPassengers: _numberOfPassengers,
                payForUse: _payForUse,
                policyId: _policyId,
                provenanceCode: _provenanceCode,
                provenanceGroup: _provenanceGroup,
                status: _status,
                supplierOwnRef: _supplierOwnRef,
                systemId: _systemId,
                totalPurchasePrice: _totalPurchasePrice,
                tripDuration: _tripDuration,
                vehicleDetails: _vehicleDetails,
                vipServiceCode: _vipServiceCode,
                isSplit: _isSplit,
                unlistedCover: _unlistedCover ? _unlistedCover.toJSON() : null
            };
        }
    });
}

module.exports = UnlistedSlvEntitlement;
