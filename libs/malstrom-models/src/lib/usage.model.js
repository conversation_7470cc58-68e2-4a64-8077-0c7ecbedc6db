'use strict';
var _ = require('lodash');

/**
 * @param {object} rawData        [description]
 */
function Usage(rawData) {
    var model = this,
        _unit = null,
        _type = null,
        _use = null;

    if (rawData) {
        _unit = rawData.unit;
        _type = rawData.type;
        _use = rawData.use;
    }

    _.extend(model, {
        unit: function unitAccessor(val) {
            return arguments.length > 0 ? (_unit = val) : _unit;
        },
        type: function typeAccessor(val) {
            return arguments.length > 0 ? (_type = val) : _type;
        },
        use: function useAccessor(val) {
            return arguments.length > 0 ? (_use = val) : _use;
        },
        toJSON: function serialiseObject() {
            return {
                unit: _unit,
                type: _type,
                use: _use,
            };
        },
    });
}

Usage.UNIT_CLAIM = 'CLAIM';
Usage.TYPE_DISTANCE_MILES = 'DISTANCE_MILES';
Usage.TYPE_USAGE = 'USAGE';

module.exports = Usage;
