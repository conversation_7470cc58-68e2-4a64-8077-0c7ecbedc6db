'use strict';
var _ = require('lodash');

/**
 * vehicle colour
 * @param {object} rawData        [description]
 * @param {Boolean} [loadFromLegacy] optional parameter to be defined only if rawData originates from aahelp legacy
 */
function Benefit(rawData) {
    var model = this,
        _id = null,
        _name = null,
        _code = null,
        _promptTextId = null;

    if (rawData) {
        _id = rawData.id;
        _name = rawData.name;
        _code = rawData.code;
        _promptTextId = rawData.promptTextId;
    }

    _.extend(model, {
        id: function idAccessor(val) {
            return arguments.length > 0 ? (_id = val) : _id;
        },
        code: function codeAccessor(val) {
            return arguments.length > 0 ? (_code = val) : _code;
        },
        name: function nameAccessor(val) {
            return arguments.length > 0 ? (_name = val) : _name;
        },
        promptTextId: function promptTextIdAccessor(val) {
            return arguments.length > 0 ? (_promptTextId = val) : _promptTextId;
        },
        toJSON: function serialiseObject() {
            return {
                id: _id,
                code: _code,
                name: _name,
                promptTextId: _promptTextId,
            };
        },
    });
}

module.exports = Benefit;
