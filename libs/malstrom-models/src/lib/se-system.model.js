'use strict';
var _ = require('lodash');

/**
 * service type
 * @param {object} rawData        [description]
 */
function SeSystem(rawData) {
    var model = this,
        _id = 0,
        _name = null,
        _code = null;

    if (rawData) {
        _id = rawData.id;
        _name = rawData.name;
        _code = rawData.code;
    }

    _.extend(model, {
        id: function (val) {
            return arguments.length > 0 ? (_id = val) : _id;
        },
        code: function (val) {
            return arguments.length > 0 ? (_code = val) : _code;
        },
        name: function nameAccessor(val) {
            return arguments.length > 0 ? (_name = val) : _name;
        },
        toJSON: function serialiseObject() {
            return {
                id: _id,
                code: _code,
                name: _name,
            };
        },
    });
}

SeSystem.SYSTEM_ID_TIA = 'TIA';

module.exports = SeSystem;
