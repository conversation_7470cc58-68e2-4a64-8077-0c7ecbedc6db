'use strict';
var _ = require('lodash'),
    PeriodOfTime = require('./period-of-time.model');

function InitialRental(raw) {
    var _period = new PeriodOfTime(),
        _customerInitiated = null,
        _passwordRequired = null,
        _requestOnly = null;

    if (raw) {
        _period = raw.period ? new PeriodOfTime(raw.period) : _period;
        _customerInitiated = raw.customerInitiated; //Boolean
        _passwordRequired = raw.passwordRequired; //Boolean
        _requestOnly = raw.requestOnly; //Boolean
    }

    _.extend(this, {
        period: function periodAccessor(val) {
            return arguments.length ? (_period = val) : _period;
        },
        customerInitiated: function customerInitiatedAccessor(val) {
            return arguments.length ? (_customerInitiated = val) : _customerInitiated;
        },
        passwordRequired: function passwordRequiredAccessor(val) {
            return arguments.length ? (_passwordRequired = val) : _passwordRequired;
        },
        requestOnly: function requestOnlyAccessor(val) {
            return arguments.length ? (_requestOnly = val) : _requestOnly;
        },
        toJSON: function serialise() {
            return {
                period: _period.toJSON(),
                customerInitiated: _customerInitiated,
                passwordRequired: _passwordRequired,
                requestOnly: _requestOnly,
            };
        },
    });
}

module.exports = InitialRental;
