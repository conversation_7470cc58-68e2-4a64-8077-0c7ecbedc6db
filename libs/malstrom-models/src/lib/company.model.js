'use strict';

const { Entitlement } = require('@aa/data-models/aux/entitlement');
var _ = require('lodash'),
    _arrayObjectToJSON = require('./factories/array-object-to-json.factory');
function Company(raw) {
    var _active = false,
        _unlistedSlvEntitlement = [],
        _town = null,
        _id = null,
        _customerGroup = null,
        _memberReference = null,
        _cardReference = null,
        _firstLevelValidation = null,
        _systemId = null,
        _companyName = null,
        _package = null,
        _roadEntitlementSystemUrl = null,
        _secondLevelValidationTypes = [],
        _entitlements = [];

    if (raw) {
        _active = raw.active;
        _.forEach(raw.unlistedSlvEntitlement, function forEachUnListedEntitlement(rawUnListedEntitlement) {
            _unlistedSlvEntitlement.push(new Entitlement(rawUnListedEntitlement));
        });
        _town = raw.town;
        _id = raw.id;
        _customerGroup = raw.customerGroup;
        _memberReference = raw.memberReference;
        _cardReference = raw.cardReference;
        _firstLevelValidation = raw.firstLevelValidation;
        _systemId = raw.systemId;
        _companyName = raw.companyName;
        _package = raw.package;
        _roadEntitlementSystemUrl = raw.roadEntitlementSystemUrl;
        _secondLevelValidationTypes = raw.secondLevelValidationTypes || [];
        _.forEach(raw.entitlements, function forEachEntitlement(rawEntitlement) {
            _entitlements.push(new Entitlement(rawEntitlement));
        });
    }

    _.extend(this, {
        active: function activeAccessor(val) {
            return arguments.length ? (_active = val) : _active;
        },
        unlistedSlvEntitlement: function unlistedSlvEntitlementAccessor(val) {
            return arguments.length ? (_unlistedSlvEntitlement = val) : _unlistedSlvEntitlement;
        },

        town: function townAccessor(val) {
            return arguments.length ? (_town = val) : _town;
        },
        id: function idAccessor(val) {
            return arguments.length ? (_id = val) : _id;
        },
        customerGroup: function customerGroupAccessor(val) {
            return arguments.length ? (_customerGroup = val) : _customerGroup;
        },
        memberReference: function memberReferenceAccessor(val) {
            return arguments.length ? (_memberReference = val) : _memberReference;
        },
        cardReference: function cardReferenceAccessor(val) {
            return arguments.length ? (_cardReference = val) : _cardReference;
        },
        firstLevelValidation: function firstLevelValidationCodeAccessor(val) {
            return arguments.length ? (_firstLevelValidation = val) : _firstLevelValidation;
        },
        systemId: function systemIdAccessor(val) {
            return arguments.length ? (_systemId = val) : _systemId;
        },
        companyName: function companyNameAccessor(val) {
            return arguments.length ? (_companyName = val) : _companyName;
        },
        package: function packageAccessor(val) {
            return arguments.length ? (_package = val) : _package;
        },
        roadEntitlementSystemUrl: function roadEntitlementSystemUrl(val) {
            return arguments.length ? (_roadEntitlementSystemUrl = val) : _roadEntitlementSystemUrl;
        },
        secondLevelValidationTypes: function secondLevelValidationTypesAccessor(val) {
            return arguments.length ? (_secondLevelValidationTypes = val) : _secondLevelValidationTypes;
        },
        entitlements: function entitlementsAccessor(val) {
            return arguments.length ? (_entitlements = val) : _entitlements;
        },
        toJSON: function serialise() {
            return {
                active: _active,
                unlistedSlvEntitlement: _unlistedSlvEntitlement ? _arrayObjectToJSON(_unlistedSlvEntitlement) : null,
                town: _town,
                id: _id,
                customerGroup: _customerGroup,
                memberReference: _memberReference,
                cardReference: _cardReference,
                firstLevelValidation: _firstLevelValidation,
                systemId: _systemId,
                companyName: _companyName,
                package: _package,
                roadEntitlementSystemUrl: _roadEntitlementSystemUrl,
                secondLevelValidationTypes: _secondLevelValidationTypes,
                entitlements: _arrayObjectToJSON(_entitlements)
            };
        }
    });
}

module.exports = Company;
