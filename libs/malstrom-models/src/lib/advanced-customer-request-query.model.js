'use strict';

const { Entitlement } = require('@aa/data-models/aux/entitlement');
var _ = require('lodash');
function AdvancedCustomerRequestQuery(raw) {
    var model = this,
        _startTime = null, //new Date(Date.now() - (1000 * 60 * 60 * 24)),//24hrs ago
        _endTime = null, //new Date(),
        _assistTypeId = 0,
        _jobNo = null,
        _seSystemId = 0,
        _contractKey = '',
        _customerKey = '',
        _memberReference = '',
        _seLocatorId = 0,
        _winterWeather = false,
        _customerRequestId = 0,
        _customerShortName = '',
        _incompleteJobs = false,
        _altSearchKey = '',
        _serviceTypeId = 0,
        _customerGroupCode = null,
        _entitlement = null,
        _taskId = null,
        _town = null,
        _vehicleRegistrationNumber = null,
        _requirePatrolVehicleDetails = false;

    if (raw) {
        _startTime = raw.startTime ? new Date(raw.startTime) : null;
        _endTime = raw.endTime ? new Date(raw.endTime) : null;
        _assistTypeId = raw.assistTypeId || 0;
        _jobNo = raw.jobNo || null;
        _seSystemId = raw.seSystemId || 0;
        _contractKey = raw.contractKey || '';
        _customerKey = raw.customerKey || '';
        _memberReference = raw.memberReference || '';
        _seLocatorId = raw.seLocatorId || 0;
        _winterWeather = raw.winterWeather || false;
        _customerRequestId = raw.customerRequestId || 0;
        _customerShortName = raw.customerShortName || '';
        _incompleteJobs = raw.incompleteJobs || false;
        _altSearchKey = raw.altSearchKey || '';
        _serviceTypeId = raw.serviceTypeId || _serviceTypeId;
        _entitlement = raw.entitlement ? new Entitlement(raw.entitlement) : null;
        _taskId = raw.taskId || null;
        _town = raw.town || null;
        _vehicleRegistrationNumber = raw.vehicleRegistrationNumber || null;
        _requirePatrolVehicleDetails = raw.requirePatrolVehicleDetails || false;
    }

    _.extend(model, {
        startTime: function startTimeAccessor(val) {
            return arguments.length ? (_startTime = val) : _startTime;
        },
        endTime: function endTimeAccessor(val) {
            return arguments.length ? (_endTime = val) : _endTime;
        },
        assistTypeId: function assistTypeIdAccessor(val) {
            return arguments.length ? (_assistTypeId = val) : _assistTypeId;
        },
        jobNo: function jobNoAccessor(val) {
            return arguments.length ? (_jobNo = val) : _jobNo;
        },
        seSystemId: function seSystemIdAccessor(val) {
            return arguments.length ? (_seSystemId = val) : _seSystemId;
        },
        contractKey: function contractKeyAccessor(val) {
            return arguments.length ? (_contractKey = val) : _contractKey;
        },
        customerKey: function customerKeyAccessor(val) {
            return arguments.length ? (_customerKey = val) : _customerKey;
        },
        memberReference: function memberReferenceAccessor(val) {
            return arguments.length ? (_memberReference = val) : _memberReference;
        },
        seLocatorId: function seLocatorIdAccessor(val) {
            return arguments.length ? (_seLocatorId = val) : _seLocatorId;
        },
        winterWeather: function winterWeatherAccessor(val) {
            return arguments.length ? (_winterWeather = val) : _winterWeather;
        },
        customerRequestId: function customerRequestIdAccessor(val) {
            return arguments.length ? (_customerRequestId = val) : _customerRequestId;
        },
        customerShortName: function customerShortNameAccessor(val) {
            return arguments.length ? (_customerShortName = val) : _customerShortName;
        },
        incompleteJobs: function incompleteJobsAccessor(val) {
            return arguments.length ? (_incompleteJobs = val) : _incompleteJobs;
        },
        altSearchKey: function altSearchKeyAccessor(val) {
            return arguments.length ? (_altSearchKey = val) : _altSearchKey;
        },
        serviceTypeId: function serviceTypeIdAccessor(val) {
            return arguments.length ? (_serviceTypeId = val) : _serviceTypeId;
        },
        customerGroupCode: function customerGroupCodeAccessor(val) {
            return arguments.length ? (_customerGroupCode = val) : _customerGroupCode;
        },
        entitlement: function entitlementAccessor(val) {
            return arguments.length ? (_entitlement = val) : _entitlement;
        },
        taskId: function taskIdAccessor(val) {
            return arguments.length ? (_taskId = val) : _taskId;
        },
        town: function townAccessor(val) {
            return arguments.length ? (_town = val) : _town;
        },
        vehicleRegistrationNumber: function vehicleRegistrationNumberAccessor(val) {
            return arguments.length ? (_vehicleRegistrationNumber = val) : _vehicleRegistrationNumber;
        },
        setSearchPeriod: function setSearchPeriod(startSearchOffsetInMinutes) {
            var timeNow = Date.now();
            _endTime = new Date(timeNow);
            _startTime = new Date(timeNow - startSearchOffsetInMinutes * 60 * 1000);
        },
        requirePatrolVehicleDetails: function requirePatrolVehicleDetailsAccessor(val) {
            return arguments.length ? (_requirePatrolVehicleDetails = val) : _requirePatrolVehicleDetails;
        },
        toJSON: function toJSON() {
            return {
                startTime: _startTime || null,
                endTime: _endTime || null,
                assistTypeId: _assistTypeId,
                jobNo: _jobNo,
                seSystemId: _seSystemId,
                contractKey: _contractKey,
                customerKey: _customerKey,
                memberReference: _memberReference,
                seLocatorId: _seLocatorId,
                winterWeather: _winterWeather,
                customerRequestId: _customerRequestId,
                customerShortName: _customerShortName,
                incompleteJobs: _incompleteJobs,
                altSearchKey: _altSearchKey,
                serviceTypeId: _serviceTypeId,
                customerGroupCode: _customerGroupCode,
                entitlement: _entitlement,
                taskId: _taskId,
                town: _town,
                vehicleRegistrationNumber: _vehicleRegistrationNumber,
                requirePatrolVehicleDetails: _requirePatrolVehicleDetails
            };
        }
    });
}

AdvancedCustomerRequestQuery.ADHOC_SEARCH_TYPE = 'adhoc';
AdvancedCustomerRequestQuery.ASSISTANCE_TYPE_SEARCH_TYPE = 'assistance';
AdvancedCustomerRequestQuery.JOB_NUMBER_SEARCH_TYPE = 'jobNumber';
AdvancedCustomerRequestQuery.TASK_ID_SEARCH_TYPE = 'task';
AdvancedCustomerRequestQuery.ALT_JOB_SEARCH_TYPE = 'altJob';
AdvancedCustomerRequestQuery.PAYMENT_SEARCH_TYPE = 'payment';
AdvancedCustomerRequestQuery.REGISTRATION_SEARCH_TYPE = 'registrationNumber';
AdvancedCustomerRequestQuery.CUSTOMER_SEARCH_TYPE = 'customer';

module.exports = AdvancedCustomerRequestQuery;
