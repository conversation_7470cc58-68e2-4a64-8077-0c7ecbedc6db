'use strict';

var _ = require('lodash');

var ExperianDetails = require('./experian-details.model'),
    TrailerDetails = require('./trailer-details.model'),
    VehicleOwnerDetails = require('./vehicle-owner-details.model'),
    RoadworthyDetails = require('./roadworthy.model'),
    VehicleModel = require('./vehicle-model.model');

function Vehicle(rawData) {
    var model = this,
        _registration = null,
        _modelId = -1,
        _makeId = -1,
        _typeId = -1,
        _colour = null,
        _model = new VehicleModel(),
        _experianDetails = new ExperianDetails(),
        _hasTrailer = false,
        _hasOwner = false,
        _trailerDetails = new TrailerDetails(),
        _vehicleOwnerDetails = new VehicleOwnerDetails(),
        _roadworthy = new RoadworthyDetails(),
        _twinnedWheels = null;

    if (rawData) {
        _registration = rawData.registration;
        _modelId = rawData.modelId;
        _colour = rawData.colour;
        _hasTrailer = rawData.hasTrailer;
        _hasOwner = rawData.hasOwner;
        _makeId = rawData.makeId;
        _typeId = rawData.typeId;
        _model = rawData.model ? new VehicleModel(rawData.model) : _model;
        _experianDetails = rawData.experianDetails ? new ExperianDetails(rawData.experianDetails) : _experianDetails;
        _trailerDetails = rawData.trailerDetails ? new TrailerDetails(rawData.trailerDetails) : _trailerDetails;
        _vehicleOwnerDetails = rawData.vehicleOwnerDetails ? new VehicleOwnerDetails(rawData.vehicleOwnerDetails) : _vehicleOwnerDetails;
        _roadworthy = rawData.roadworthy ? new RoadworthyDetails(rawData.roadworthy) : _roadworthy;
        _twinnedWheels = rawData.twinnedWheels ? rawData.twinnedWheels : null;
    }

    _.extend(model, {
        registration: function registrationAccessor(val) {
            return arguments.length ? (_registration = val) : _registration;
        },
        modelId: function modelIdAccessor(val) {
            return arguments.length ? (_modelId = val) : _modelId;
        },
        colour: function colourAccessor(val) {
            return arguments.length ? (_colour = val) : _colour;
        },
        hasTrailer: function hasTrailerAccessor(val) {
            return arguments.length ? (_hasTrailer = val) : _hasTrailer;
        },
        hasOwner: function hasOwnerAccessor(val) {
            return arguments.length ? (_hasOwner = val) : _hasOwner;
        },
        makeId: function makeIddAccessor(val) {
            return arguments.length ? (_makeId = val) : _makeId;
        },
        typeId: function modelIdAccessor(val) {
            return arguments.length ? (_typeId = val) : _typeId;
        },
        model: function modelAccessor(val) {
            return arguments.length ? (_model = val) : _model;
        },
        experianDetails: function experianDetailsAccessor(val) {
            return arguments.length ? (_experianDetails = val) : _experianDetails;
        },
        trailerDetails: function trailerDetailsAccessor(val) {
            return arguments.length ? (_trailerDetails = val) : _trailerDetails;
        },
        vehicleOwnerDetails: function vehicleOwnerDetailsAccessor(val) {
            return arguments.length ? (_vehicleOwnerDetails = val) : _vehicleOwnerDetails;
        },
        roadworthy: function roadworthyAccessor(val) {
            return arguments.length ? (_roadworthy = val) : _roadworthy;
        },
        validVehicle: function validVehicle() {
            return _.isNumber(_typeId) && _typeId > -1 && _.isNumber(_makeId) && _makeId > -1 && _.isNumber(_modelId) && _modelId > -1;
        },
        reset: function reset() {
            _registration = null;
            _modelId = -1;
            _makeId = -1;
            _typeId = -1;
            _colour = null;
            _hasTrailer = false;
            _hasOwner = false;
            _model = new VehicleModel();
            _experianDetails = new ExperianDetails();
            _trailerDetails = new TrailerDetails();
            _vehicleOwnerDetails = new VehicleOwnerDetails();
            _roadworthy = new RoadworthyDetails();
        },
        isValidVehicle: function isValidVehicle() {
            return !_.isEmpty(_registration) && _typeId !== null && _typeId > -1 && _makeId !== null && _makeId > -1 && _modelId !== null && _modelId > -1;
        },
        twinnedWheels: function twinnedWheelsAccessor(val) {
            return arguments.length > 0 ? (_twinnedWheels = val) : _twinnedWheels;
        },
        toJSON: function toJSON() {
            return {
                registration: _registration,
                modelId: _modelId,
                colour: _colour,
                hasTrailer: _hasTrailer,
                hasOwner: _hasOwner,
                makeId: _makeId,
                typeId: _typeId,
                model: _model ? _model.toJSON() : null,
                experianDetails: _experianDetails ? _experianDetails.toJSON() : null,
                trailerDetails: _trailerDetails ? _trailerDetails.toJSON() : null,
                vehicleOwnerDetails: _vehicleOwnerDetails ? _vehicleOwnerDetails.toJSON() : null,
                roadworthy: _roadworthy ? _roadworthy.toJSON() : null,
                twinnedWheels: _twinnedWheels,
            };
        },
    });
}

module.exports = Vehicle;
