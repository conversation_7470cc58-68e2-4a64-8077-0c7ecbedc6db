'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    LatLong = require('./lat-long.model'),
    RefId = require('./ref-id.model'),
    SupplierDetails = require('./supplier-details.model');

function Supplier(raw) {
    var model = this,
        _breakdownOnlyIndicator = false,
        _calculatedCost = null,
        _calculatedMileage = null,
        _deploymentMethodCode = null,
        _ediNumber = null,
        _faxNumber = null,
        _incidentManagementIndicator = false,
        _mdtld1 = null,
        _mdtld2 = null,
        _mdtld3 = null,
        _mdtUnit1 = null,
        _mdtUnit2 = null,
        _mdtUnit3 = null,
        _minsUntilPrompt = null,
        _primeAgentIndicator = false,
        _roadsideAutoPayDistance = null,
        _recoveryAdditionalMileageRate = null,
        _recoveryAutoPayDistance = null,
        _recoveryAutoPayment = null,
        _resourceId = null,
        _roadsideAdditionalMileageRate = null,
        _roadsideAutoPayment = null,
        _supplierAreaName = null,
        _supplierAddress = new Address(),
        _supplierNetworkId = null,
        _supplierNetworkName = null,
        _supplierAccountNo = null,
        _supplierClosedIndicator = false,
        _capabilities = [],
        _supplierName = null,
        _supplierBase = new LatLong(),
        _supplierCover = new LatLong(),
        _trunkRecoveryAutoPay = null,
        _chequesAcceptedIndicator = false,
        _creditCardsAcceptedIndicator = false,
        _issA120FormsIndicator = false,
        _relayPlusVoucherIndicator = false,
        _remarks = null,
        _supplierDetails = new SupplierDetails(),
        _imgtPromptId = null,
        _selfBilling = null,
        _supExtRef1 = null,
        _supExtRef2 = null,
        _supExtRef3 = null,
        _supExtRef4 = null,
        _supplierGrade = null,
        _typeCode = null,
        _supNetworkKey = null,
        _extPartnerCode = null;

    if (raw) {
        _breakdownOnlyIndicator = _.isBoolean(raw.breakdownOnlyIndicator) ? raw.breakdownOnlyIndicator : raw.breakdownOnlyIndicator === 'Y';
        _calculatedCost = raw.calculatedCost ? raw.calculatedCost : null;
        _calculatedMileage = raw.calculatedMileage ? raw.calculatedMileage : null;
        _deploymentMethodCode = raw.deploymentMethodCode ? raw.deploymentMethodCode : null;
        _ediNumber = raw.ediNumber ? raw.ediNumber : null;
        _faxNumber = raw.faxNumber ? raw.faxNumber : null;
        _incidentManagementIndicator = _.isBoolean(raw.incidentManagementIndicator) ? raw.incidentManagementIndicator : raw.incidentManagementIndicator === 'Y';
        _mdtld1 = raw.mdtld1 ? raw.mdtld1 : null;
        _mdtld2 = raw.mdtld2 ? raw.mdtld2 : null;
        _mdtld3 = raw.mdtld3 ? raw.mdtld3 : null;
        _mdtUnit1 = raw.mdtUnit1 ? raw.mdtUnit1 : null;
        _mdtUnit2 = raw.mdtUnit2 ? raw.mdtUnit2 : null;
        _mdtUnit3 = raw.mdtUnit3 ? raw.mdtUnit3 : null;
        _minsUntilPrompt = raw.minsUntilPrompt ? raw.minsUntilPrompt : null;
        _primeAgentIndicator = _.isBoolean(raw.primeAgentIndicator) ? raw.primeAgentIndicator : raw.primeAgentIndicator === 'Y';
        _roadsideAutoPayDistance = raw.roadsideAutoPayDistance ? raw.roadsideAutoPayDistance : null;
        _recoveryAdditionalMileageRate = raw.recoveryAdditionalMileageRate ? raw.recoveryAdditionalMileageRate : null;
        _recoveryAutoPayDistance = raw.recoveryAutoPayDistance ? raw.recoveryAutoPayDistance : null;
        _recoveryAutoPayment = raw.recoveryAutoPayment ? raw.recoveryAutoPayment : null;
        _resourceId = raw.resourceId ? raw.resourceId : null;
        _roadsideAdditionalMileageRate = raw.roadsideAdditionalMileageRate ? raw.roadsideAdditionalMileageRate : null;
        _roadsideAutoPayment = raw.roadsideAutoPayment ? raw.roadsideAutoPayment : null;
        _supplierAreaName = raw.supplierAreaName ? raw.supplierAreaName : null;
        _supplierAddress = raw.supplierAddress ? new Address(raw.supplierAddress) : _supplierAddress;
        _supplierNetworkId = raw.supplierNetworkId ? raw.supplierNetworkId : null;
        _supplierNetworkName = raw.supplierNetworkName ? raw.supplierNetworkName : null;
        _supplierAccountNo = raw.supplierAccountNo ? raw.supplierAccountNo : null;
        _supplierClosedIndicator = _.isBoolean(raw.supplierClosedIndicator) ? raw.supplierClosedIndicator : raw.supplierClosedIndicator === 'Y';
        _supplierName = raw.supplierName ? raw.supplierName : null;
        _supplierBase = raw.supplierBase ? new LatLong(raw.supplierBase) : _supplierBase;
        _supplierCover = raw.supplierCover ? new LatLong(raw.supplierCover) : _supplierCover;
        _trunkRecoveryAutoPay = raw.trunkRecoveryAutoPay ? raw.trunkRecoveryAutoPay : null;
        _chequesAcceptedIndicator = _.isBoolean(raw.chequesAcceptedIndicator) ? raw.chequesAcceptedIndicator : raw.chequesAcceptedIndicator === 'Y';
        _creditCardsAcceptedIndicator = _.isBoolean(raw.creditCardsAcceptedIndicator) ? raw.creditCardsAcceptedIndicator : raw.creditCardsAcceptedIndicator === 'Y';
        _issA120FormsIndicator = _.isBoolean(raw.issA120FormsIndicator) ? raw.issA120FormsIndicator : raw.issA120FormsIndicator === 'Y';
        _relayPlusVoucherIndicator = _.isBoolean(raw.relayPlusVoucherIndicator) ? raw.relayPlusVoucherIndicator : raw.relayPlusVoucherIndicator === 'Y';
        _remarks = raw.remarks ? raw.remarks : null;
        _supplierDetails = raw.supplierDetails ? new SupplierDetails(raw.supplierDetails) : _supplierDetails;

        _.forEach(raw.capabilities, function _forEachCapability(rawCapability) {
            _capabilities.push(new RefId(rawCapability));
        });
        _imgtPromptId = raw.imgtPromptId ? raw.imgtPromptId : null;
        _selfBilling = raw.selfBilling ? raw.selfBilling : null;
        _supExtRef1 = raw.supExtRef1 ? raw.supExtRef1 : null; // armsProfileId
        _supExtRef2 = raw.supExtRef2 ? raw.supExtRef2 : null; // profileName
        _supExtRef3 = raw.supExtRef3 ? raw.supExtRef3 : null; // armsOfficeId
        _supExtRef4 = raw.supExtRef4 ? raw.supExtRef4 : null; // reasonForHire
        _supplierGrade = raw.supplierGrade ? raw.supplierGrade : null;
        _typeCode = raw.typeCode ? raw.typeCode : null;
        _supNetworkKey = raw.supNetworkKey ? raw.supNetworkKey : null;
        _extPartnerCode = raw.extPartnerCode ? raw.extPartnerCode : null;
    }

    function _arrayToJSON(list) {
        var result = [];
        _.forEach(list, function _forEachCapability(capability) {
            result.push(capability.toJSON());
        });
        return result;
    }

    _.extend(model, {
        breakdownOnlyIndicator: function breakdownOnlyIndicatorAccessor(val) {
            return arguments.length ? (_breakdownOnlyIndicator = val) : _breakdownOnlyIndicator;
        },
        calculatedCost: function calculatedCostAccessor(val) {
            return arguments.length ? (_calculatedCost = val) : _calculatedCost;
        },
        calculatedMileage: function calculatedMileageAccessor(val) {
            return arguments.length ? (_calculatedMileage = val) : _calculatedMileage;
        },
        deploymentMethodCode: function deploymentMethodCodeAccessor(val) {
            return arguments.length ? (_deploymentMethodCode = val) : _deploymentMethodCode;
        },
        ediNumber: function ediNumberAccessor(val) {
            return arguments.length ? (_ediNumber = val) : _ediNumber;
        },
        faxNumber: function faxNumberAccessor(val) {
            return arguments.length ? (_faxNumber = val) : _faxNumber;
        },
        incidentManagementIndicator: function incidentManagementIndicatorAccessor(val) {
            return arguments.length ? (_incidentManagementIndicator = val) : _incidentManagementIndicator;
        },
        mdtld1: function mdtld1Accessor(val) {
            return arguments.length ? (_mdtld1 = val) : _mdtld1;
        },
        mdtld2: function mdtld2Accessor(val) {
            return arguments.length ? (_mdtld2 = val) : _mdtld2;
        },
        mdtld3: function mdtld3Accessor(val) {
            return arguments.length ? (_mdtld3 = val) : _mdtld3;
        },
        mdtUnit1: function mdtUnit1Accessor(val) {
            return arguments.length ? (_mdtUnit1 = val) : _mdtUnit1;
        },
        mdtUnit2: function mdtUnit2Accessor(val) {
            return arguments.length ? (_mdtUnit2 = val) : _mdtUnit2;
        },
        mdtUnit3: function mdtUnit3Accessor(val) {
            return arguments.length ? (_mdtUnit3 = val) : _mdtUnit3;
        },
        minsUntilPrompt: function minsUntilPromptAccessor(val) {
            return arguments.length ? (_minsUntilPrompt = val) : _minsUntilPrompt;
        },
        primeAgentIndicator: function primeAgentIndicatorAccessor(val) {
            return arguments.length ? (_primeAgentIndicator = val) : _primeAgentIndicator;
        },
        roadsideAutoPayDistance: function roadsideAutoPayDistanceAccessor(val) {
            return arguments.length ? (_roadsideAutoPayDistance = val) : _roadsideAutoPayDistance;
        },
        recoveryAdditionalMileageRate: function recoveryAdditionalMileageRateAccessor(val) {
            return arguments.length ? (_recoveryAdditionalMileageRate = val) : _recoveryAdditionalMileageRate;
        },
        recoveryAutoPayDistance: function recoveryAutoPayDistanceAccessor(val) {
            return arguments.length ? (_recoveryAutoPayDistance = val) : _recoveryAutoPayDistance;
        },
        recoveryAutoPayment: function recoveryAutoPaymentAccessor(val) {
            return arguments.length ? (_recoveryAutoPayment = val) : _recoveryAutoPayment;
        },
        resourceId: function resourceIdAccessor(val) {
            return arguments.length ? (_resourceId = val) : _resourceId;
        },
        roadsideAdditionalMileageRate: function roadsideAdditionalMileageRateAccessor(val) {
            return arguments.length ? (_roadsideAdditionalMileageRate = val) : _roadsideAdditionalMileageRate;
        },
        roadsideAutoPayment: function roadsideAutoPaymentAccessor(val) {
            return arguments.length ? (_roadsideAutoPayment = val) : _roadsideAutoPayment;
        },
        supplierAreaName: function supplierAreaNameAccessor(val) {
            return arguments.length ? (_supplierAreaName = val) : _supplierAreaName;
        },
        supplierAddress: function supplierAddressAccessor(val) {
            return arguments.length ? (_supplierAddress = val) : _supplierAddress;
        },
        supplierNetworkId: function supplierNetworkIdAccessor(val) {
            return arguments.length ? (_supplierNetworkId = val) : _supplierNetworkId;
        },
        supplierNetworkName: function supplierNetworkNameAccessor(val) {
            return arguments.length ? (_supplierNetworkName = val) : _supplierNetworkName;
        },
        supplierAccountNo: function supplierAccountNoAccessor(val) {
            return arguments.length ? (_supplierAccountNo = val) : _supplierAccountNo;
        },
        supplierClosedIndicator: function supplierClosedIndicatorAccessor(val) {
            return arguments.length ? (_supplierClosedIndicator = val) : _supplierClosedIndicator;
        },
        capabilities: function capabilitiesAccessor(val) {
            return arguments.length ? (_capabilities = val) : _capabilities;
        },
        supplierName: function supplierNameAccessor(val) {
            return arguments.length ? (_supplierName = val) : _supplierName;
        },
        supplierBase: function supplierBaseAccessor(val) {
            return arguments.length ? (_supplierBase = val) : _supplierBase;
        },
        supplierCover: function supplierCoverAccessor(val) {
            return arguments.length ? (_supplierCover = val) : _supplierCover;
        },
        trunkRecoveryAutoPay: function trunkRecoveryAutoPayAccessor(val) {
            return arguments.length ? (_trunkRecoveryAutoPay = val) : _trunkRecoveryAutoPay;
        },
        chequesAcceptedIndicator: function chequesAcceptedIndicatorAccessor(val) {
            return arguments.length ? (_chequesAcceptedIndicator = val) : _chequesAcceptedIndicator;
        },
        creditCardsAcceptedIndicator: function creditCardsAcceptedIndicatorAccessor(val) {
            return arguments.length ? (_creditCardsAcceptedIndicator = val) : _creditCardsAcceptedIndicator;
        },
        issA120FormsIndicator: function issA120FormsIndicatorAccessor(val) {
            return arguments.length ? (_issA120FormsIndicator = val) : _issA120FormsIndicator;
        },
        relayPlusVoucherIndicator: function relayPlusVoucherIndicatorAccessor(val) {
            return arguments.length ? (_relayPlusVoucherIndicator = val) : _relayPlusVoucherIndicator;
        },
        remarks: function remarksAccessor(val) {
            return arguments.length ? (_remarks = val) : _remarks;
        },
        supplierDetails: function supplierDetails(val) {
            return arguments.length ? (_supplierDetails = val) : _supplierDetails;
        },
        imgtPromptId: function imgtPromptIdAccessor(val) {
            return arguments.length ? (_imgtPromptId = val) : _imgtPromptId;
        },
        selfBilling: function selfBillingAccessor(val) {
            return arguments.length ? (_selfBilling = val) : _selfBilling;
        },
        armsProfileId: function armsProfileIdAccessor(val) {
            return arguments.length ? (_supExtRef1 = val) : _supExtRef1;
        },
        profileName: function profileNameAccessor(val) {
            return arguments.length ? (_supExtRef2 = val) : _supExtRef2;
        },
        armsOfficeId: function armsOfficeIdAccessor(val) {
            return arguments.length ? (_supExtRef3 = val) : _supExtRef3;
        },
        reasonForHire: function reasonForHireAccessor(val) {
            return arguments.length ? (_supExtRef4 = val) : _supExtRef4;
        },
        supplierGrade: function supplierGradeAccessor(val) {
            return arguments.length ? (_supplierGrade = val) : _supplierGrade;
        },
        typeCode: function typeCodeAccessor(val) {
            return arguments.length ? (_typeCode = val) : _typeCode;
        },
        supNetworkKey: function supNetworkKeyAccessor(val) {
            return arguments.length ? (_supNetworkKey = val) : _supNetworkKey;
        },
        extPartnerCode: function extPartnerCodeAccessor(val) {
            return arguments.length ? (_extPartnerCode = val) : _extPartnerCode;
        },
        toJSON: function toJSON() {
            return {
                breakdownOnlyIndicator: _breakdownOnlyIndicator,
                calculatedCost: _calculatedCost,
                calculatedMileage: _calculatedMileage,
                deploymentMethodCode: _deploymentMethodCode,
                ediNumber: _ediNumber,
                faxNumber: _faxNumber,
                incidentManagementIndicator: _incidentManagementIndicator,
                mdtld1: _mdtld1,
                mdtld2: _mdtld2,
                mdtld3: _mdtld3,
                mdtUnit1: _mdtUnit1,
                mdtUnit2: _mdtUnit2,
                mdtUnit3: _mdtUnit3,
                minsUntilPrompt: _minsUntilPrompt,
                primeAgentIndicator: _primeAgentIndicator,
                roadsideAutoPayDistance: _roadsideAutoPayDistance,
                recoveryAdditionalMileageRate: _recoveryAdditionalMileageRate,
                recoveryAutoPayDistance: _recoveryAutoPayDistance,
                recoveryAutoPayment: _recoveryAutoPayment,
                resourceId: _resourceId,
                roadsideAdditionalMileageRate: _roadsideAdditionalMileageRate,
                roadsideAutoPayment: _roadsideAutoPayment,
                supplierAreaName: _supplierAreaName,
                supplierAddress: _supplierAddress ? _supplierAddress.toJSON() : null,
                supplierNetworkId: _supplierNetworkId,
                supplierNetworkName: _supplierNetworkName,
                supplierAccountNo: _supplierAccountNo,
                supplierClosedIndicator: _supplierClosedIndicator,
                capabilities: _arrayToJSON(_capabilities),
                supplierName: _supplierName,
                supplierBase: _supplierBase ? _supplierBase.toJSON() : null,
                supplierCover: _supplierCover ? _supplierCover.toJSON() : null,
                trunkRecoveryAutoPay: _trunkRecoveryAutoPay,
                chequesAcceptedIndicator: _chequesAcceptedIndicator,
                creditCardsAcceptedIndicator: _creditCardsAcceptedIndicator,
                issA120FormsIndicator: _issA120FormsIndicator,
                relayPlusVoucherIndicator: _relayPlusVoucherIndicator,
                remarks: _remarks,
                supplierDetails: _supplierDetails ? _supplierDetails.toJSON() : null,
                imgtPromptId: _imgtPromptId,
                selfBilling: _selfBilling,
                supExtRef1: _supExtRef1,
                supExtRef2: _supExtRef2,
                supExtRef3: _supExtRef3,
                supExtRef4: _supExtRef4,
                supplierGrade: _supplierGrade,
                typeCode: _typeCode,
                supNetworkKey: _supNetworkKey,
                extPartnerCode: _extPartnerCode
            };
        }
    });
}

module.exports = Supplier;
