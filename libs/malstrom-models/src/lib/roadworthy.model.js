'use strict';

var _ = require('lodash');

function RoadworthyDetails(rawData) {
    var model = this,
        _registrationNumber = null,
        _yearOfManufacture = null,
        _monthOfFirstRegistration = null,
        _motExpiryDate = null,
        _motStatus = null,
        _taxDueDate = null,
        _taxStatus = null;

    if (rawData) {
        _registrationNumber = rawData.registrationNumber;
        _yearOfManufacture = rawData.yearOfManufacture;
        _monthOfFirstRegistration = rawData.monthOfFirstRegistration ? new Date(rawData.monthOfFirstRegistration) : null;
        _motExpiryDate = rawData.motExpiryDate ? new Date(rawData.motExpiryDate) : null;
        _motStatus = rawData.motStatus;
        _taxDueDate = rawData.taxDueDate ? new Date(rawData.taxDueDate) : null;
        _taxStatus = rawData.taxStatus;
    }

    var _calculateDays = (date) => {
        // reset time component as we are comparing days
        date.setHours(0, 0, 0, 0);
        const present = new Date();
        present.setHours(0, 0, 0, 0);

        return Math.floor((present.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    };

    var _calculateExactDays = (date) => {
        // Will calculate the exact days like 0.5 days
        const present = new Date();
        return (present.getTime() - date.getTime()) / (1000 * 60 * 60 * 24);
    };

    _.extend(model, {
        registrationNumber: function registrationNumberAccessor(val) {
            return arguments.length ? (_registrationNumber = val) : _registrationNumber;
        },
        yearOfManufacture: function yearOfManufactureAccessor(val) {
            return arguments.length ? (_yearOfManufacture = val) : _yearOfManufacture;
        },
        monthOfFirstRegistration: function monthOfFirstRegistrationAccessor(val) {
            if (arguments.length) {
                if (!val || _.isDate(val)) {
                    _monthOfFirstRegistration = val; // if empty or is a date value, set monthofFirstRegistration as val.
                } else {
                    _monthOfFirstRegistration = new Date(val);
                }
            } else {
                return _monthOfFirstRegistration;
            }
        },
        motExpiryDate: function motExpiryDateAccessor(val) {
            if (arguments.length) {
                if (!val || _.isDate(val)) {
                    _motExpiryDate = val; // if empty or is a date value, set _motExpiryDate as val.
                } else {
                    _motExpiryDate = new Date(val);
                }
            } else {
                return _motExpiryDate;
            }
        },
        motStatus: function motStatusAccessor(val) {
            return arguments.length ? (_motStatus = val) : _motStatus;
        },
        taxDueDate: function taxDueDateAccessor(val) {
            if (arguments.length) {
                if (!val || _.isDate(val)) {
                    _taxDueDate = val; // if empty or is a date value, set _taxDueDate as val.
                } else {
                    _taxDueDate = new Date(val);
                }
            } else {
                return _taxDueDate;
            }
        },
        taxStatus: function taxStatusAccessor(val) {
            return arguments.length ? (_taxStatus = val) : _taxStatus;
        },

        // vehicles manufatured/first registered more than 40 years ago are not required to have MOT
        isClassicVehicle: function () {
            if (!_yearOfManufacture) {
                if (!_monthOfFirstRegistration) {
                    return false;
                }

                return _monthOfFirstRegistration.getFullYear() <= new Date().getFullYear() - 40;
            }
            return _yearOfManufacture <= new Date().getFullYear() - 40;
        },

        // Vehicles registered less than 3 years ago are not required to have MOT.
        isNewVehicle: function () {
            if (!_monthOfFirstRegistration) {
                return false;
            }
            return Math.ceil(_calculateDays(_monthOfFirstRegistration) / 365) <= 3;
        },

        isMotValid: function isMotValid() {
            return ['Valid', 'Exempt', 'No details held by DVLA'].includes(_motStatus) || model.isClassicVehicle() || model.isNewVehicle();
        },

        isTaxValid: function isTaxValid() {
            return _taxStatus === 'Taxed';
        },

        isSorn: function isSorn() {
            return _taxStatus === 'SORN';
        },

        isMOTWithinGrace: function () {
            if (_motExpiryDate) {
                return _calculateDays(_motExpiryDate) <= RoadworthyDetails.GRACELENGTH;
            }
            return false;
        },

        isTaxWithinGrace: function () {
            if (_taxDueDate) {
                return _calculateExactDays(_taxDueDate) <= RoadworthyDetails.GRACELENGTH;
            }
            return false;
        },
        reset: function () {
            _registrationNumber = null;
            _yearOfManufacture = null;
            _monthOfFirstRegistration = null;
            _motExpiryDate = null;
            _motStatus = null;
            _taxDueDate = null;
            _taxStatus = null;
        },
        toJSON: function toJSON() {
            return {
                registrationNumber: _registrationNumber,
                yearOfManufacture: _yearOfManufacture,
                monthOfFirstRegistration: _monthOfFirstRegistration,
                motExpiryDate: _motExpiryDate,
                motStatus: _motStatus,
                taxDueDate: _taxDueDate,
                taxStatus: _taxStatus,
            };
        },
    });
}

RoadworthyDetails.GRACELENGTH = 0;

module.exports = RoadworthyDetails;
