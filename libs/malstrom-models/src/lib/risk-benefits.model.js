'use strict';

const { Benefit } = require('@aa/data-models/aux/entitlement');
var _ = require('lodash'),
    _arrayObjectToJSON = require('./factories/array-object-to-json.factory');
function RiskBenefits(raw) {
    var model = this,
        _riskCode,
        _payForUseIndicator = false,
        _benefits = []; //Array of benefit.model

    if (raw) {
        _riskCode = raw.riskCode;
        _payForUseIndicator = raw.payForUseIndicator;
        _.forEach(raw.benefits, function forEachBenefit(rawBenefit) {
            _benefits.push(new Benefit(rawBenefit));
        });
    }

    _.extend(model, {
        benefits: function benefitsAccessor(val) {
            return arguments.length ? (_benefits = val) : _benefits;
        },
        payForUseIndicator: function payForUseIndicatorAccessor(val) {
            return arguments.length ? (_payForUseIndicator = val) : _payForUseIndicator;
        },
        riskCode: function riskCodeAccessor(val) {
            return arguments.length ? (_riskCode = val) : _riskCode;
        },

        toJSON: function toJSON() {
            return {
                benefits: _arrayObjectToJSON(_benefits),
                payForUseIndicator: _payForUseIndicator,
                riskCode: _riskCode
            };
        }
    });
}

module.exports = RiskBenefits;
