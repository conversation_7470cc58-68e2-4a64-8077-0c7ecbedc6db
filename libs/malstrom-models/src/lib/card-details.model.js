'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    Contact = require('./contact.model');

/**
 * @typedef CardDetails
 * @constructor
 */
function CardDetails(raw) {
    let model = this,
        _cardHolderName = '',
        _accountNumber = '',
        _expirationDate = '',
        _cardBrand = '',
        _billingAddress = new Address(),
        _contact = new Contact();

    if (raw) {
        _cardHolderName = raw.cardHolderName || _cardHolderName;
        _accountNumber = raw.accountNumber || _accountNumber;
        _expirationDate = raw.expirationDate || _expirationDate;
        _cardBrand = raw.cardBrand || _cardBrand;
        _billingAddress = raw.billingAddress ? new Address(raw.billingAddress) : _billingAddress;
        _contact = raw.contact ? new Contact(raw.contact) : _contact;
    }

    _.extend(model, {
        cardHolderName: function cardHolderNameAccessor(val) {
            return arguments.length ? (_cardHolderName = val) : _cardHolderName;
        },
        accountNumber: function accountNumberAccessor(val) {
            return arguments.length ? (_accountNumber = val) : _accountNumber;
        },
        expirationDate: function expirationDateAccessor(val) {
            return arguments.length ? (_expirationDate = val) : _expirationDate;
        },
        cardBrand: function cardBrandAccessor(val) {
            return arguments.length ? (_cardBrand = val) : _cardBrand;
        },
        billingAddress: function billingAddressAccessor(val) {
            return arguments.length ? (_billingAddress = val) : _billingAddress;
        },
        contact: function contactAccessor(val) {
            return arguments.length ? (_contact = val) : _contact;
        },
        toJSON: function toJSON() {
            return {
                cardHolderName: _cardHolderName,
                accountNumber: _accountNumber,
                expirationDate: _expirationDate,
                cardBrand: _cardBrand,
                billingAddress: _billingAddress ? _billingAddress.toJSON() : null,
                contact: _contact ? _contact.toJSON() : null
            };
        }
    });
}

module.exports = CardDetails;
