'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    LatLong = require('./lat-long.model'),
    Resource = require('./resource.model');

function FleetDepot(raw) {
    var model = this,
        _address = null,
        _name = null,
        _id = null,
        _resources = [],
        _coordinates = new LatLong();

    if (raw) {
        _id = raw.id ? raw.id : _id;
        _name = raw.name ? raw.name : _name;
        _coordinates = raw.coordinates ? new LatLong(raw.coordinates) : _coordinates;
        _address = raw.address ? new Address(raw.address) : _address;
        _.forEach(raw.resources, function (rawResource) {
            _resources.push(new Resource(rawResource));
        });
    }

    _.extend(model, {
        id: function idAccessor(val) {
            return arguments.length ? (_id = val) : _id;
        },
        name: function nameAccessor(val) {
            return arguments.length ? (_name = val) : _name;
        },
        cleanName: function cleanName() {
            return _name.replace(/ \[[A-Z][0-9]+\]/, '');
        },
        coordinates: function coordinatesAccessor(val) {
            return arguments.length ? (_coordinates = val) : _coordinates;
        },
        address: function addressAcessor() {
            return _address;
        },
        resources: function resourcesAcessor(val) {
            return arguments.length ? (_resources = val) : _resources;
        },
        toJSON: function toJSON() {
            var jsonResources = [];
            _.forEach(_resources, function (resource) {
                jsonResources.push(resource.toJSON());
            });
            var json = {
                id: _id,
                name: _name,
                address: _address ? _address.toJSON() : null,
                coordinates: _coordinates ? _coordinates.toJSON() : null,
                resources: jsonResources
            };

            _.forEach(_resources, function (resource) {
                json.resources.push(resource.toJSON());
            });

            return json;
        }
    });
}

module.exports = FleetDepot;
