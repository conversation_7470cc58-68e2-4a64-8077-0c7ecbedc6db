'use strict';

const _ = require('lodash');
const { Address } = require('@aa/data-models/common');

/**
 * @typedef CorrespondenceAddress
 * @constructor
 */
function CorrespondenceAddress(raw) {
    let _model = this,
        _abstractAddress = new Address(raw),
        _valid = true;

    if (raw) {
        _valid = raw.valid === false ? raw.valid : _valid;
    }

    _.extend(_model, _abstractAddress, {
        valid: function validAccessor(val) {
            return arguments.length ? (_valid = val) : _valid;
        },
        toJSON: function toJSON() {
            return _.extend({}, _abstractAddress.toJSON(), {
                valid: _valid
            });
        }
    });
}

module.exports = CorrespondenceAddress;
