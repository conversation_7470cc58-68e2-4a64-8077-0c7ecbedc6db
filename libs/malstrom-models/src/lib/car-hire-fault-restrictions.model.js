'use strict';
var _ = require('lodash'),
    ConditionalHire = require('./conditional-hire.model');

function CarHireFaultRestrictions(raw) {
    var _rtc = new ConditionalHire(),
        _misfuel = new ConditionalHire(),
        _frozenPartFault = new ConditionalHire(),
        _punctureFault = new ConditionalHire(),
        _heatingSystemFault = new ConditionalHire(),
        _vehicleKeysFault = new ConditionalHire(),
        _adBlueInFuelFault = new ConditionalHire(),
        _stuckInFault = new ConditionalHire(),
        _towingFault = new ConditionalHire(),
        _miscFault = new ConditionalHire(),
        _waterInFuelFault = new ConditionalHire(),
        _insuranceCoveredFaults = new ConditionalHire(),
        _outOfFuelFault = new ConditionalHire();

    if (raw) {
        _rtc = raw.rtc ? new ConditionalHire(raw.rtc) : _rtc;
        _misfuel = raw.misfuel ? new ConditionalHire(raw.misfuel) : _misfuel;
        _frozenPartFault = raw.frozenPartFault ? new ConditionalHire(raw.frozenPartFault) : _frozenPartFault;
        _punctureFault = raw.punctureFault ? new ConditionalHire(raw.punctureFault) : _punctureFault;
        _heatingSystemFault = raw.heatingSystemFault ? new ConditionalHire(raw.heatingSystemFault) : _heatingSystemFault;
        _vehicleKeysFault = raw.vehicleKeysFault ? new ConditionalHire(raw.vehicleKeysFault) : _vehicleKeysFault;
        _adBlueInFuelFault = raw.adBlueInFuelFault ? new ConditionalHire(raw.adBlueInFuelFault) : _adBlueInFuelFault;
        _stuckInFault = raw.stuckInFault ? new ConditionalHire(raw.stuckInFault) : _stuckInFault;
        _towingFault = raw.towingFault ? new ConditionalHire(raw.towingFault) : _towingFault;
        _miscFault = raw.miscFault ? new ConditionalHire(raw.miscFault) : _miscFault;
        _waterInFuelFault = raw.waterInFuelFault ? new ConditionalHire(raw.waterInFuelFault) : _waterInFuelFault;
        _insuranceCoveredFaults = raw.insuranceCoveredFaults ? new ConditionalHire(raw.insuranceCoveredFaults) : _insuranceCoveredFaults;
        _outOfFuelFault = raw.outOfFuelFault ? new ConditionalHire(raw.outOfFuelFault) : _outOfFuelFault;
    }

    _.extend(this, {
        rtc: function rtcAccessor(val) {
            return arguments.length ? (_rtc = val) : _rtc;
        },
        misfuel: function misfuelAccessor(val) {
            return arguments.length ? (_misfuel = val) : _misfuel;
        },
        frozenPartFault: function frozenPartFaultAccessor(val) {
            return arguments.length ? (_frozenPartFault = val) : _frozenPartFault;
        },
        punctureFault: function punctureFaultAccessor(val) {
            return arguments.length ? (_punctureFault = val) : _punctureFault;
        },
        heatingSystemFault: function heatingSystemFaultAccessor(val) {
            return arguments.length ? (_heatingSystemFault = val) : _heatingSystemFault;
        },
        vehicleKeysFault: function vehicleKeysFaultAccessor(val) {
            return arguments.length ? (_vehicleKeysFault = val) : _vehicleKeysFault;
        },
        adBlueInFuelFault: function adBlueInFuelFaultAccessor(val) {
            return arguments.length ? (_adBlueInFuelFault = val) : _adBlueInFuelFault;
        },
        stuckInFault: function stuckInFaultAccessor(val) {
            return arguments.length ? (_stuckInFault = val) : _stuckInFault;
        },
        towingFault: function towingFaultAccessor(val) {
            return arguments.length ? (_towingFault = val) : _towingFault;
        },
        miscFault: function miscFaultAccessor(val) {
            return arguments.length ? (_miscFault = val) : _miscFault;
        },
        waterInFuelFault: function waterInFuelFaultAccessor(val) {
            return arguments.length ? (_waterInFuelFault = val) : _waterInFuelFault;
        },
        insuranceCoveredFaults: function insuranceCoveredFaultsAccessor(val) {
            return arguments.length ? (_insuranceCoveredFaults = val) : _insuranceCoveredFaults;
        },
        outOfFuelFault: function outOfFuelFaultAccessor(val) {
            return arguments.length ? (_outOfFuelFault = val) : _outOfFuelFault;
        },

        toJSON: function serialise() {
            return {
                rtc: _rtc.toJSON(),
                misfuel: _misfuel.toJSON(),
                frozenPartFault: _frozenPartFault.toJSON(),
                punctureFault: _punctureFault.toJSON(),
                heatingSystemFault: _heatingSystemFault.toJSON(),
                vehicleKeysFault: _vehicleKeysFault.toJSON(),
                adBlueInFuelFault: _adBlueInFuelFault.toJSON(),
                stuckInFault: _stuckInFault.toJSON(),
                towingFault: _towingFault.toJSON(),
                miscFault: _miscFault.toJSON(),
                waterInFuelFault: _waterInFuelFault.toJSON(),
                insuranceCoveredFaults: _insuranceCoveredFaults.toJSON(),
                outOfFuelFault: _outOfFuelFault.toJSON(),
            };
        },
    });
}

module.exports = CarHireFaultRestrictions;
