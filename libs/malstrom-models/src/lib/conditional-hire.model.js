'use strict';
var _ = require('lodash'),
    PeriodOfTime = require('./period-of-time.model');

function ConditionalHire(raw) {
    var _period = new PeriodOfTime(),
        _allowed = true;

    if (raw) {
        _period = raw.period ? new PeriodOfTime(raw.period) : _period;
        _allowed = raw.allowed === false ? raw.allowed : _allowed;
    }

    _.extend(this, {
        period: function periodAccessor(val) {
            return arguments.length ? (_period = val) : _period;
        },
        allowed: function allowedAccessor(val) {
            return arguments.length ? (_allowed = val) : _allowed;
        },

        toJSON: function serialise() {
            return {
                period: _period.toJSON(),
                allowed: _allowed,
            };
        },
    });
}

module.exports = ConditionalHire;
