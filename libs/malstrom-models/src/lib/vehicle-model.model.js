'use strict';
var _ = require('lodash');

/**
 * vehicle model definition
 * @param {object}  rawData      data set
 * @param {Boolean} [loadFromLegacy] optional parameter to be defined only if rawData originates from aahelp legacy
 */
function VehicleModel(rawData) {
    var _id = -1,
        _name = null,
        _typeId = -1,
        _makeId = -1,
        _weight = -1,
        _frontLiftWeight = -1,
        _rearLiftWeight = -1,
        _overPrsnWeight = -1,
        _fuel = null,
        _transmission = null;

    if (rawData) {
        _id = rawData.id;
        _name = rawData.name;
        _weight = rawData.weight;
        _makeId = rawData.makeId;
        _typeId = rawData.typeId;
        _frontLiftWeight = rawData.frontLiftWeight;
        _rearLiftWeight = rawData.rearLiftWeight;
        _overPrsnWeight = rawData.overPrsnWeight;
        _fuel = rawData.fuel;
        _transmission = rawData.transmission;
    }

    _.extend(this, {
        id: function idAccessor(val) {
            return arguments.length ? (_id = val) : _id;
        },
        name: function nameAccessor(val) {
            return arguments.length ? (_name = val) : _name;
        },
        typeId: function typeIdAccessor(val) {
            return arguments.length ? (_typeId = val) : _typeId;
        },
        weight: function weightAccessor(val) {
            return arguments.length ? (_weight = val) : _weight;
        },
        frontLiftWeight: function frontLiftWeightAccessor(val) {
            return arguments.length ? (_frontLiftWeight = val) : _frontLiftWeight;
        },
        rearLiftWeight: function rearLiftWeightAccessor(val) {
            return arguments.length ? (_rearLiftWeight = val) : _rearLiftWeight;
        },
        overPrsnWeight: function overPrsnWeightAccessor(val) {
            return arguments.length ? (_overPrsnWeight = val) : _overPrsnWeight;
        },
        makeId: function makeIdAccessor(val) {
            return arguments.length ? (_makeId = val) : _makeId;
        },
        fuel: function fuelAccessor(val) {
            return arguments.length ? (_fuel = val) : _fuel;
        },
        transmission: function transmissionAccessor(val) {
            return arguments.length ? (_transmission = val) : _transmission;
        },
        toJSON: function serialiseObject() {
            return {
                id: _id,
                name: _name,
                weight: _weight,
                typeId: _typeId,
                makeId: _makeId,
                frontLiftWeight: _frontLiftWeight,
                rearLiftWeight: _rearLiftWeight,
                overPrsnWeight: _overPrsnWeight,
                fuel: _fuel,
                transmission: _transmission,
            };
        },
    });
}

module.exports = VehicleModel;
