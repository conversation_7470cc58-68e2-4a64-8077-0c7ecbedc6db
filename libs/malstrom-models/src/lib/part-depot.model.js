'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    Latlong = require('./lat-long.model');

module.exports = function PartDepot(data) {
    var model = this,
        _storeId = null,
        _storeName = null,
        _latLong = new Latlong(),
        _address = new Address();

    if (data) {
        _storeId = data.storeId || _storeId;
        _storeName = data.storeName || _storeName;
        _latLong = data.latLong ? new Latlong(data.latLong) : _latLong;
        _address = data.address ? new Address(data.address) : _address;
    }
    _.extend(model, {
        storeId: function storeIdAccessor(val) {
            return arguments.length > 0 ? (_storeId = val) : _storeId;
        },
        storeName: function storeNameAccessor(val) {
            return arguments.length > 0 ? (_storeName = val) : _storeName;
        },
        latLong: function latLongAccessor(val) {
            return arguments.length > 0 ? (_latLong = val) : _latLong;
        },
        address: function addressAccessor(val) {
            return arguments.length > 0 ? (_address = val) : _address;
        },
        toJSON: function toJSON() {
            return {
                storeId: _storeId,
                storeName: _storeName,
                latLong: _latLong,
                address: _address
            };
        }
    });
};
