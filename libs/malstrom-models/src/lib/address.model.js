'use strict';
var _ = require('lodash');

/**
 * @typedef Address
 * @constructor
 */
function Address(raw) {
    var model = this,
        _houseNoName = null,
        _addressLines = [],
        _postcode = null;
    if (raw) {
        _postcode = raw.postcode || _postcode;
        _houseNoName = raw.houseNoName || _houseNoName;
        _.forEach(raw.addressLines, function _forEachAddressLines(addressLine) {
            _addressLines.push(addressLine);
        });
    }

    _.extend(model, {
        addressLines: function addressLinesAccessor(val) {
            return arguments.length ? (_addressLines = val) : _addressLines;
        },
        postcode: function postcodeAccessor(val) {
            return arguments.length ? (_postcode = val) : _postcode;
        },
        houseNoName: function houseNoNameAccessor(val) {
            return arguments.length ? (_houseNoName = val) : _houseNoName;
        },
        addressAsString: function addressAsStringAccessor() {
            var lines = _addressLines.slice(0);

            if (_postcode) {
                lines.push(_postcode);
            }
            if (_houseNoName && lines[0].indexOf(_houseNoName) < 0) {
                lines[0] = _houseNoName + ' ' + lines[0];
            }

            return lines.join(', ');
        },
        town: function townAccessor() {
            return _addressLines.length > 1 ? _addressLines[_addressLines.length - 1] : '';
        },
        county: function addressAsStringAccessor() {
            return _addressLines.length > 2 ? _addressLines[_addressLines.length - 2] : '';
        },
        toJSON: function toJSON() {
            return {
                addressLines: _addressLines,
                postcode: _postcode,
                houseNoName: _houseNoName,
            };
        },
    });
}

module.exports = Address;
