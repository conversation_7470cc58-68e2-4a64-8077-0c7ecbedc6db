import { AuthProviderType } from '@aa/auth-client';
import { AuthMiddleware } from '@aa/auth-middleware';
import { BackendAuthClient } from '@aa/backend-auth-client';
import { ConfigClient } from '@aa/config-client';
import { Connector } from '@aa/connector';
import { Context } from '@aa/context';
import { DataStore, DataStoreProviderDefinition, DataStoreProviderType } from '@aa/data-store';
import { EventCode, Exception } from '@aa/exception';
import { HttpClient } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Instrumentation } from '@aa/instrumentation';
import { Logger } from '@aa/logger';
import { Server } from '@aa/server';
import { System } from '@aa/system';
import { BackendEnvironment } from '@aa/utils';

/**
 * Base for all microservices
 */
export abstract class Microservice {
    public abstract name: string;
    public abstract application: BackendApplication;
    public environment: BackendEnvironment;
    public instrumentation?: Instrumentation;
    public context: Context;
    public logger: Logger;
    public system: System;
    public connector: Connector;
    public dataStore: DataStore;
    public authClient: BackendAuthClient;
    public authMiddleware: AuthMiddleware;
    public httpClient: HttpClient;
    public configClient!: ConfigClient<any>;
    public server: Server;

    constructor(config: MicroserviceConfig) {
        try {
            const { environment, dataStoreProviders = [], isAuthServer = false, appName, openApi = false } = config;
            const env = environment.environment;

            const {
                connectivity,
                logLevel,
                tracing,
                system: { exitOnException }
            } = environment;
            const { appInsightsKey, sampling } = tracing;

            let instrumentation: Instrumentation | undefined;
            if (appInsightsKey) {
                instrumentation = new Instrumentation({
                    appInsightsKey,
                    appName,
                    environment: env,
                    sampling
                });
                this.instrumentation = instrumentation;
            }

            if (!dataStoreProviders.length) {
                // always provide at least redis & mongodb
                dataStoreProviders.push(DataStoreProviderType.REDIS, DataStoreProviderType.MONGODB, DataStoreProviderType.ORACLE);
            }

            const context = new Context();
            const logger = new Logger({
                context,
                appName,
                instrumentation,
                level: logLevel
            });
            const system = new System({
                process,
                logger,
                environment: environment.environment,
                exitOnException
            });

            // Some microservices dont require all DBs, lets build whats requested
            const providers: DataStoreProviderDefinition[] = dataStoreProviders.map((providerType) => {
                let providerDefinition: DataStoreProviderDefinition;

                switch (providerType) {
                    case DataStoreProviderType.ORACLE:
                        providerDefinition = {
                            type: DataStoreProviderType.ORACLE,
                            config: {
                                logger,
                                system,
                                appName,
                                ...environment.oracle
                            }
                        };
                        break;
                    case DataStoreProviderType.REDIS:
                        providerDefinition = {
                            type: DataStoreProviderType.REDIS,
                            config: {
                                system,
                                logger,
                                ...environment.redis
                            }
                        };
                        break;
                    case DataStoreProviderType.LDAP:
                        providerDefinition = {
                            type: DataStoreProviderType.LDAP,
                            config: { system, logger, ...environment.ldap }
                        };
                        break;
                    case DataStoreProviderType.MONGODB:
                        providerDefinition = {
                            type: DataStoreProviderType.MONGODB,
                            config: {
                                system,
                                logger,
                                appName,
                                ...environment.mongodb
                            }
                        };
                        break;
                    case DataStoreProviderType.PRESTIGE:
                        providerDefinition = {
                            type: DataStoreProviderType.PRESTIGE,
                            config: {
                                logger,
                                system,
                                ...environment.prestige
                            }
                        };
                        break;
                    case DataStoreProviderType.B2QTECH:
                        providerDefinition = {
                            type: DataStoreProviderType.B2QTECH,
                            config: {
                                logger,
                                system,
                                ...environment.b2qTech
                            }
                        };
                        break;
                    case DataStoreProviderType.BLOBSTORAGE:
                        providerDefinition = {
                            type: DataStoreProviderType.BLOBSTORAGE,
                            config: {
                                logger,
                                system,
                                ...environment.blobStorage
                            }
                        };
                        break;
                    case DataStoreProviderType.CATHIE:
                        providerDefinition = {
                            type: DataStoreProviderType.CATHIE,
                            config: {
                                logger,
                                system,
                                connectionConfig: environment.cathie
                            }
                        };
                        break;
                    case DataStoreProviderType.BCAS:
                        providerDefinition = {
                            type: DataStoreProviderType.BCAS,
                            config: {
                                logger,
                                system,
                                connectionConfigBcas: environment.bcas
                            }
                        };
                        break;
                }

                return providerDefinition;
            });

            const dataStore = new DataStore({ logger, providers });

            const authClient = new BackendAuthClient({
                logger,
                context,
                system,
                providers: [
                    {
                        type: AuthProviderType.FORGEROCK,
                        config: { dataStore, logger, ...environment.forgerock }
                    },
                    {
                        type: AuthProviderType.AZURE_AD,
                        config: { dataStore, logger, ...environment.ad }
                    },
                    {
                        type: AuthProviderType.API_KEY,
                        config: { dataStore, logger }
                    },
                    {
                        type: AuthProviderType.LEGACY,
                        config: { dataStore, logger, ...environment.legacy }
                    },
                    {
                        type: AuthProviderType.LEGACY_ALT,
                        config: { dataStore, logger, ...environment.legacy }
                    }
                ]
            });

            const authMiddleware = new AuthMiddleware({
                logger,
                context,
                authClient,
                ...environment.authMiddleware
            });
            const httpClient = new HttpClient({ authClient: authClient });
            const { onSiteDomain } = connectivity;
            const connector = new Connector({
                httpClient,
                onSiteDomain
            });

            this.environment = environment;
            this.context = context;
            this.logger = logger;
            this.system = system;
            this.httpClient = httpClient;
            this.dataStore = dataStore;
            this.authClient = authClient;
            this.authMiddleware = authMiddleware;
            this.connector = connector;

            // create server
            this.server = new Server({
                logger,
                context,
                system,
                authMiddleware,
                isAuthServer,
                instrumentation,
                openApi,
                ...this.environment.server
            });
        } catch (error) {
            throw new Exception({
                sourceName: 'Microservice',
                code: EventCode.MOD_CREATE_FAIL,
                error,
                message: 'Failed creating Microservice'
            });
        }
    }

    public async init(): Promise<void> {
        try {
            // setup config client
            const configClient = new ConfigClient<any>({ httpClient: this.httpClient, application: this.application });
            this.configClient = configClient;
            await configClient.sync();

            // authenticate
            await this.authClient.authenticate(AuthProviderType.AZURE_AD);

            // start server
            this.server.start();
        } catch (error) {
            const exception = new Exception({
                message: 'Error while initialising microservice',
                error,
                code: EventCode.MOD_INIT_FAIL
            });
            this.system.onException(exception, true);
        }
    }
}

export interface MicroserviceConfig {
    appName: string;
    environment: BackendEnvironment;
    // data providers required by app - defaults to REDIS & MONGODB
    dataStoreProviders?: DataStoreProviderType[];
    // defaults to false
    isAuthServer?: boolean;
    // should we enable openapi
    openApi?: boolean;
}
