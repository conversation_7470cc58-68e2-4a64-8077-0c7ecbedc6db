const { Vehicle } = require('@aa/data-models/common');
const _ = require('lodash'),
    Contact = require('@aa/malstrom-models/lib/contact.model');
/**
 * Class representing an incoming recovery to a retailer
 */
/**
 * @class
 * @param  {Object} raw [description]
 * @param  {string} raw.id
 * @param  {Contact} raw.contact
 * @param {Vehicle} raw.vehicle
 * @return {}     [description]
 */
function IncommingRecovery(raw) {
    var model = this,
        _id = null,
        _contact = null,
        _vehicle = null,
        _custReqId = -1;

    if (raw) {
        /**
         * id
         */
        _id = raw.id ? raw.id : _id;

        /**
         * contact
         */
        _contact = raw.contact ? new Contact(raw.contact) : _contact;

        /**
         * vehicle
         */
        _vehicle = raw.vehicle ? new Vehicle(raw.vehicle) : _vehicle;

        _custReqId = raw.customerRequestId ? raw.customerRequestId : _custReqId;
    }

    _.extend(model, {
        /**
         * setter/getter for id
         */
        id: (...args) => (args.length ? (_id = args[0]) : _id),

        /**
         * setter/getter for contact
         */
        contact: (...args) => (args.length ? (_contact = args[0]) : _contact),

        /**
         * setter/getter for vehicle
         */
        vehicle: (...args) => (args.length ? (_vehicle = args[0]) : _vehicle),

        custReqId: (...args) => (args.length ? (_custReqId = args[0]) : _custReqId),

        /**
         * IncommingRecovery serialiser
         * @return {Object} json representation of IncommingRecovery
         */
        toJSON: function serialiseObject() {
            return {
                id: _id,
                contact: _contact.toJSON(),
                vehicle: _vehicle.toJSON(),
                customerRequestId: _custReqId
            };
        }
    });
}

module.exports = IncommingRecovery;
