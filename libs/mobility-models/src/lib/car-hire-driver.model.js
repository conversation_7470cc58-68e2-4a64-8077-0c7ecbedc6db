const { Address } = require('@aa/data-models/common');
const _ = require('lodash'),
    Contact = require('@aa/malstrom-models/lib/contact.model');

const _LOOKUP_RESULT = {
    OK: 0,
    NONE: 1,
    REJECT: 2,
    FAILED: 3
};

/**
 * car hire driver definition
 * @class
 * @param  {Object} raw json representation
 * @param {string} raw.id represents id within insurance system
 * @param {Contact} raw.contact
 * @param {string} raw.licenceType
 * @param {string} raw.licenceNumber
 * @param {Date} raw.licenceExpire
 * @param {Date} raw.dateOfBirth
 * @param {Boolean} raw.isPrimaryDriver
 * @param {Boolean} raw.licenceChecked
 * @param {Boolean} raw.manualCheck
 * @param {Boolean} raw.licenseCheckConcentGiven
 * @param {Address} raw.Address
 * @param {Address} raw.hasDisqualifications
 * @param {Address} raw.hasPenaltyPoints
 * @return {CarHireDriver}     car hire driver model
 */
function CarHireDriver(raw) {
    var model = this,
        _id = -1,
        _contact = new Contact(),
        _licenceType = null,
        _licenceNumber = null,
        _licenceExpire = null,
        _dateOfBirth = null,
        _licenceLookupResult = null,
        _address = new Address(),
        _manualLicenceCheck = false,
        _licenceCheckConsentGiven = null,
        _licenceYrsMoreThanOne = true,
        _isEligible = null,
        _isPrimaryDriver = false,
        _errors = [],
        _hasDisqualifications,
        _hasPenaltyPoints;

    if (raw) {
        /**
         * unique id so driver is identified in insurance
         */
        _id = raw.id ? raw.id : -1;
        /**
         * address details so we store post etc in correct fields and in free format
         */
        _address = raw.address ? new Address(raw.address) : _address;
        /**
         * contat details
         */
        _contact = raw.contact ? new Contact(raw.contact) : _contact;

        /**
         * licenceType
         */
        _licenceType = raw.licenceType ? raw.licenceType : _licenceType;

        /**
         * licenceNumber
         */
        _licenceNumber = raw.licenceNumber ? raw.licenceNumber : _licenceNumber;

        /**
         * licenceExpire
         */
        _licenceExpire = raw.licenceExpire ? raw.licenceExpire : _licenceExpire;

        /**
         * dateOfBirth
         */
        _dateOfBirth = raw.dateOfBirth ? raw.dateOfBirth : _dateOfBirth;

        /**
         * licenceLookupResult - outcome of the licence check
         */
        _licenceLookupResult = raw.licenceLookupResult ? raw.licenceLookupResult : _licenceLookupResult;

        /**
         * manualLicenceCheck
         */
        _manualLicenceCheck = raw.manualLicenceCheck ? raw.manualLicenceCheck : _manualLicenceCheck;

        /**
         * licenceCheckConsentGiven
         */
        _licenceCheckConsentGiven = raw.hasOwnProperty('licenceCheckConsentGiven') ? raw.licenceCheckConsentGiven : _licenceCheckConsentGiven;

        /**
         * _isPrimaryDriver
         */
        _isPrimaryDriver = raw.isPrimaryDriver ? raw.isPrimaryDriver : _isPrimaryDriver;

        /**
         * _licenceYrsMoreThanOne
         */
        _licenceYrsMoreThanOne = raw.hasOwnProperty('licenceYrsMoreThanOne') ? raw.licenceYrsMoreThanOne : _licenceYrsMoreThanOne;

        /**
         * _isEligible to drive this vehicle for insurance purposes. need to deferrentiate between null (not set) to true / false
         */
        _isEligible = raw.hasOwnProperty('isEligible') ? raw.isEligible : null;

        /**
         * _errors also persisting of errors for this driver need this when bulk checking insurance
         */
        _errors = raw.hasOwnProperty('errors') ? raw.errors : [];

        /**
         * hasDisqualifications in the last 5 years
         */
        _hasDisqualifications = raw.hasOwnProperty('hasDisqualifications') ? raw.hasDisqualifications : _hasDisqualifications;
        /**
         * hasPenaltyPoints, has more than 9 penalty points accrued in the last 3 years.
         */
        _hasPenaltyPoints = raw.hasOwnProperty('hasPenaltyPoints') ? raw.hasPenaltyPoints : _hasPenaltyPoints;
    }

    _.extend(model, {
        /**
         * @memberof CarHireDriver#
         * setter/getter for id
         */
        id: (...args) => (args.length ? (_id = args[0]) : _id),
        /**
         * @memberof CarHireDriver#
         * setter/getter for contact
         */
        contact: (...args) => (args.length ? (_contact = args[0]) : _contact),

        /**
         * @memberof CarHireDriver#
         * setter/getter for licenceType
         */
        licenceType: (...args) => (args.length ? (_licenceType = args[0]) : _licenceType),

        /**
         * setter/getter for licenceNumber
         * @memberof CarHireDriver#
         */
        licenceNumber: (...args) => (args.length ? (_licenceNumber = args[0]) : _licenceNumber),

        /**
         * setter/getter for licenceExpire
         * @memberof CarHireDriver#
         */
        licenceExpire: (...args) => (args.length ? (_licenceExpire = args[0]) : _licenceExpire),

        /**
         * setter/getter for dateOfBirth
         * @memberof CarHireDriver#
         */
        dateOfBirth: (...args) => (args.length ? (_dateOfBirth = args[0]) : _dateOfBirth),

        /**
         * @memberof CarHireDriver#
         * setter/getter for licenceLookupResult
         */
        licenceLookupResult: (...args) => (args.length ? (_licenceLookupResult = args[0]) : _licenceLookupResult),

        /**
         * setter/getter for manualLicenceCheck
         * @memberOf CarHireDriver#
         */
        manualLicenceCheck: (...args) => (args.length ? (_manualLicenceCheck = args[0]) : _manualLicenceCheck),

        /**
         * setter/getter for licenceCheckConsentGiven
         * @memberOf CarHireDriver#
         */
        licenceCheckConsentGiven: (...args) => (args.length ? (_licenceCheckConsentGiven = args[0]) : _licenceCheckConsentGiven),

        /**
         * setter/getter for hasDisqualifications
         * @memberOf CarHireDriver#
         */
        hasDisqualifications: (...args) => (args.length ? (_hasDisqualifications = args[0]) : _hasDisqualifications),

        /**
         * setter/getter for hasPenaltyPoints
         * @memberOf CarHireDriver#
         */
        hasPenaltyPoints: (...args) => (args.length ? (_hasPenaltyPoints = args[0]) : _hasPenaltyPoints),

        /**
         * setter/getter for address
         * @memberOf CarHireDriver#
         */
        address: (...args) => (args.length ? (_address = args[0]) : _address),

        /**
         * setter/getter for address
         * @memberOf CarHireDriver#
         */
        isPrimaryDriver: (...args) => (args.length ? (_isPrimaryDriver = args[0]) : _isPrimaryDriver),

        /**
         * setter/getter for licenceYrsMoreThanOne
         * @memberOf CarHireDriver#
         */
        licenceYrsMoreThanOne: (...args) => (args.length ? (_licenceYrsMoreThanOne = args[0]) : _licenceYrsMoreThanOne),

        /**
         * setter/getter for licenceYrsMoreThanOne
         * @memberOf CarHireDriver#
         */
        isEligible: (...args) => (args.length ? (_isEligible = args[0]) : _isEligible),
        /**
         * check if driver has an insurer id
         * @return {boolean} false if _id is -ve  otherwise true
         */
        hasInsurerId: () => _id > 0,

        /**
         * errors against this driver
         * @memberOf CarHireDriver#
         * @param {Array.<string, number>=} args
         */
        errors: (...args) => (args.length ? (_errors = args[0]) : _errors),

        /**
         * return comma delinated list of errors within this driver
         * @memberOf CarHireDriver#
         * @return {String} contain errors otherwise null
         */
        hasError: () => {
            return _errors.length > 0 ? _errors.map((error) => error.message).join(',') : null;
        },
        reset: (...args) => {
            _id = args.length > 0 ? args[0] : -1;
            _isEligible = null;
            _licenceLookupResult = null;
        },
        softReset: () => {
            _id = Date.now();
            _isEligible = null;
            _licenceLookupResult = null;
        },

        /**
         * CarHireDriver serialiser
         * @memberOf CarHireDriver#
         * @return {Object} json representation of CarHireDriver
         */
        toJSON: function serialiseObject() {
            return {
                id: _id,
                contact: _contact.toJSON(),
                address: _address.toJSON(),
                licenceType: _licenceType,
                licenceNumber: _licenceNumber,
                licenceExpire: _licenceExpire,
                dateOfBirth: _dateOfBirth,
                licenceLookupResult: _licenceLookupResult,
                manualLicenceCheck: _manualLicenceCheck,
                licenceCheckConsentGiven: _licenceCheckConsentGiven,
                isPrimaryDriver: _isPrimaryDriver,
                isEligible: _isEligible,
                licenceYrsMoreThanOne: _licenceYrsMoreThanOne,
                errors: _errors,
                hasDisqualifications: _hasDisqualifications,
                hasPenaltyPoints: _hasPenaltyPoints
            };
        }
    });
}

CarHireDriver.TYPES = {
    LOOKUP_RESULT: _LOOKUP_RESULT
};

module.exports = CarHireDriver;
