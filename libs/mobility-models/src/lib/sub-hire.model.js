'use strict';

const { Vehicle } = require('@aa/data-models/common');
const RAFVehicle = require('./raf-vehicle.model'),
    Supplier = require('@aa/malstrom-models/lib/supplier.model'),
    HireAuthorization = require('./hire-authorization.model'),
    _ = require('lodash');

/**
 * @class  SubHire
 * identifies third party hire in the rental object if any
 * @param {Object=} raw json representation of SubHire object
 */
function SubHire(raw) {
    const model = this;
    let _hireVehicleSupplier = null,
        _hireVehicleContractId = null,
        _hireVehicleRef = null,
        _hireVehicle = new RAFVehicle(),
        _experianDetails = new Vehicle(),
        _logicalResourceId = -1,
        _vehicleGroup = null,
        _sameMake = null,
        _authorityCode = null,
        _hasTowbarRequested = false,
        _isEnterprise = false,
        _isManualEntReservation = false,
        _supplier = new Supplier(),
        _enterpriseComment = null,
        _hireAuthorization = new HireAuthorization(),
        _supNetworkCode = null;

    if (raw) {
        _hireVehicleSupplier = raw.hireVehicleSupplier || _hireVehicleSupplier;
        _hireVehicleContractId = raw.hireVehicleContractId || _hireVehicleContractId;
        _hireVehicleRef = raw.hireVehicleRef || _hireVehicleRef;
        _hireVehicle = raw.hireVehicle ? new RAFVehicle(raw.hireVehicle) : _hireVehicle;
        _experianDetails = raw.experianDetails ? new Vehicle(raw.experianDetails) : _experianDetails;
        _logicalResourceId = raw.hasOwnProperty('logicalResourceId') ? raw.logicalResourceId : -1;
        _vehicleGroup = raw.vehicleGroup ? raw.vehicleGroup : _vehicleGroup;
        _sameMake = raw.sameMake ? raw.sameMake : _sameMake;
        _authorityCode = raw.authorityCode ? raw.authorityCode : _authorityCode;
        _hasTowbarRequested = raw.hasTowbarRequested === true ? raw.hasTowbarRequested : _hasTowbarRequested;
        _isEnterprise = raw.isEnterprise || _isEnterprise;
        _isManualEntReservation = raw.isManualEntReservation || _isManualEntReservation;
        _supplier = raw.supplier ? new Supplier(raw.supplier) : new Supplier();
        _enterpriseComment = raw.enterpriseComment || _enterpriseComment;
        _hireAuthorization = raw.hireAuthorization ? new HireAuthorization(raw.hireAuthorization) : _hireAuthorization;
        _supNetworkCode = raw.supplierTypeCode ? raw.supNetworkCode : _supNetworkCode;
    }

    _.extend(model, {
        /**
         * hireVehicleSupplier setter / getter
         * @memberOf subHire#
         * @param  {Number=} args to set
         * @return {Number=} [description]
         */
        hireVehicleSupplier: (...args) => (args.length ? (_hireVehicleSupplier = args[0]) : _hireVehicleSupplier),

        /**
         * description setter / getter
         * @memberOf subHire#
         * @param  {String=} args to set
         * @return {String=}
         */
        hireVehicleContractId: (...args) => (args.length ? (_hireVehicleContractId = args[0]) : _hireVehicleContractId),

        /**
         * hireVehicleRef setter / getter
         * @memberOf subHire#
         * @param  {String=} args to set
         * @return {String=}
         */
        hireVehicleRef: (...args) => (args.length ? (_hireVehicleRef = args[0]) : _hireVehicleRef),

        /**
         * hireVehicle setter / getter
         * @memberOf subHire#
         * @param  {Boolean=} args to set
         * @return {Boolean=}
         */
        hireVehicle: (...args) => (args.length ? (_hireVehicle = args[0]) : _hireVehicle),

        /**
         * experianDetails setter / getter
         * @memberOf subHire#
         * @param  {Vehicle=} args to set
         * @return {Vehicle=}
         */
        experianDetails: (...args) => (args.length ? (_experianDetails = args[0]) : _experianDetails),

        /**
         * logicalResourceId setter / getter
         * @memberOf subHire#
         * @param  {Number=} args to set
         * @return {Number=}
         */
        logicalResourceId: (...args) => {
            return args.length ? (_logicalResourceId = args[0]) : _logicalResourceId;
        },

        /**
         * setter/getter for vehicleGroup
         */
        vehicleGroup: (...args) => (args.length ? (_vehicleGroup = args[0]) : _vehicleGroup),

        /**
         * setter/getter for sameMake
         */
        sameMake: (...args) => (args.length ? (_sameMake = args[0]) : _sameMake),

        /**
         * setter/getter for authorityCode
         */
        authorityCode: (...args) => (args.length ? (_authorityCode = args[0]) : _authorityCode),

        /**
         * return true if logicalResourceId is set this indicates that subHire model is set ...
         * @return {boolean}
         */
        isSet: () => {
            return _logicalResourceId > 0;
        },

        /**
         * return is TPS belongs to self checkout suppliers like ENTERPRISE, LCH
         */
        isSelfCheckout: () => {
            return SubHire.SELF_CHECKOUT_SUPPLIERS.includes(_hireVehicle.supplierTypeCode());
        },
        /** flag to render if the towbar feature is requested for the hire */
        hasTowbarRequested: (...args) => (args.length ? (_hasTowbarRequested = args[0]) : _hasTowbarRequested),

        /** boolean flag to indicate if this hire has been allocated to Enterprise-Rent-A-Car  */
        isEnterprise: (...args) => (args.length ? (_isEnterprise = args[0]) : _isEnterprise),

        /** returns if hire belogns to a like for like car class for hire  */
        isLikeForLike: () => {
            return model.vehicleGroup() === SubHire.L4L_CATEGORY;
        },

        /** boolean flag to indicate if this hire created manually for Enterprise-Rent-A-Car  */
        isManualEntReservation: (...args) => (args.length ? (_isManualEntReservation = args[0]) : _isManualEntReservation),

        /** Supplier details required for Enterprise car-hire  */
        supplier: (...args) => (args.length ? (_supplier = args[0]) : _supplier),

        /** Extra comments for Enterprise car-hire  */
        enterpriseComment: (...args) => (args.length ? (_enterpriseComment = args[0]) : _enterpriseComment),

        /** Extra comments for Enterprise car-hire  */
        hireAuthorization: (...args) => (args.length ? (_hireAuthorization = args[0]) : _hireAuthorization),

        supNetworkCode: (...args) => (args.length ? (_supNetworkCode = args[0]) : _supNetworkCode),

        /**
         * serialiser
         * @memberOf SubHire
         * @return {Object}
         */
        toJSON: () => {
            return {
                hireVehicleSupplier: _hireVehicleSupplier,
                hireVehicleContractId: _hireVehicleContractId,
                logicalResourceId: _logicalResourceId,
                hireVehicleRef: _hireVehicleRef,
                hireVehicle: _hireVehicle.toJSON(),
                experianDetails: _experianDetails.toJSON(),
                vehicleGroup: _vehicleGroup,
                sameMake: _sameMake,
                authorityCode: _authorityCode,
                hasTowbarRequested: _hasTowbarRequested,
                isEnterprise: _isEnterprise,
                isManualEntReservation: _isManualEntReservation,
                supplier: _supplier.toJSON(),
                enterpriseComment: _enterpriseComment,
                hireAuthorization: _hireAuthorization.toJSON(),
                supNetworkCode: _supNetworkCode
            };
        }
    });
}

/**
 *
 * values of third party logical resources
 * @memberof SubHire
 * @typedef LOGICALS
 * @property {string} THRIFTYJAGUAR
 * @property {string} THRIFTYLANDROVER
 * @returns {LOGICALS}
 */
SubHire.LOGICALS = {
    THRIFTY_JAGUAR: 9098334,
    THRIFTY_LANDROVER: 9098335,
    ENTERPRISE_JAGUAR: 9098336,
    ENTERPRISE_LANDROVER: 9098337,
    LCH_JAGUAR: 9098338,
    LCH_LANDROVER: 9098339
};

/**
 * Self checkout TPS suppliers list
 */
SubHire.SELF_CHECKOUT_SUPPLIERS = ['ENTERPRISE', 'LCH', 'enterprise', 'PNC'];
SubHire.L4L_CATEGORY = 'L4L';

module.exports = SubHire;
