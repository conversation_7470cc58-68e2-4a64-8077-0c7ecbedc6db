'use strict';

const { Address } = require('@aa/data-models/common');
const _ = require('lodash'),
    LatLong = require('@aa/malstrom-models/lib/lat-long.model');

/**
 * @class
 * RentalSupplier object
 * @param {Object=} raw JSON representation of RentalSupplier object
 */
function RentalSupplier(raw) {
    const _model = this;
    let _id = -1,
        _groupName = null,
        _regionParId = null,
        _paramCode = null,
        _description = null,
        _name = null,
        _supTypeCode = null,
        _address = new Address(),
        _weekOpenTime = null,
        _weekCloseTime = null,
        _satOpenTime = null,
        _satCloseTime = null,
        _sunOpenTime = null,
        _sunCloseTime = null,
        _acountNo = null,
        _areaName = null,
        _position = new LatLong();

    if (raw) {
        _id = raw.hasOwnProperty('id') ? raw.id : -1;
        _groupName = raw.groupName || null;
        _regionParId = raw.hasOwnProperty('regionParId') ? raw.regionParId : null;
        _paramCode = raw.paramCode || null;
        _description = raw.description || null;
        _name = raw.name || null;
        _supTypeCode = raw.supTypeCode || null;
        _address = raw.address ? new Address(raw.address) : _address;
        _weekOpenTime = raw.weekOpenTime || null;
        _weekCloseTime = raw.weekCloseTime || null;
        _satOpenTime = raw.satOpenTime || null;
        _satCloseTime = raw.satCloseTime || null;
        _sunOpenTime = raw.sunOpenTime || null;
        _sunCloseTime = raw.sunCloseTime || null;
        _acountNo = raw.acountNo || null;
        _areaName = raw.areaName || null;
        _position = raw.position ? new LatLong(raw.position) : _position;
    }

    _.extend(_model, {
        /**
         * getter / setter for id
         * @memberOf RentalSupplier#
         * @param  {Number=} val
         */
        id: (...args) => (args.length ? (_id = args[0]) : _id),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {String=} val
         */
        groupName: (...args) => (args.length ? (_groupName = args[0]) : _groupName),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {String=} val
         */
        regionParId: (...args) => (args.length ? (_regionParId = args[0]) : _regionParId),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {String=} val
         */
        paramCode: (...args) => (args.length ? (_paramCode = args[0]) : _paramCode),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {String=} val
         */
        description: (...args) => (args.length ? (_description = args[0]) : _description),

        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {String=} val
         */
        name: (...args) => (args.length ? (_name = args[0]) : _name),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {Address=} val
         */
        address: (...args) => (args.length ? (_address = args[0]) : _address),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {String=} val
         */
        supTypeCode: (...args) => (args.length ? (_supTypeCode = args[0]) : _supTypeCode),

        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {Number=} val
         */
        weekOpenTime: (...args) => (args.length ? (_weekOpenTime = args[0]) : _weekOpenTime),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {Number=} val
         */
        weekCloseTime: (...args) => (args.length ? (_weekCloseTime = args[0]) : _weekCloseTime),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {Number=} val
         */
        satOpenTime: (...args) => (args.length ? (_satOpenTime = args[0]) : _satOpenTime),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {Number=} val
         */
        satCloseTime: (...args) => (args.length ? (_satCloseTime = args[0]) : _satCloseTime),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {Number=} val
         */
        sunOpenTime: (...args) => (args.length ? (_sunOpenTime = args[0]) : _sunOpenTime),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {String=} val
         */
        sunCloseTime: (...args) => (args.length ? (_sunCloseTime = args[0]) : _sunCloseTime),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {String=} val
         */
        acountNo: (...args) => (args.length ? (_acountNo = args[0]) : _acountNo),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {String=} val
         */
        areaName: (...args) => (args.length ? (_areaName = args[0]) : _areaName),
        /**
         * getter/setter
         * @memberOf RentalSupplier#
         * @param {String=} val
         */
        position: (...args) => (args.length ? (_position = args[0]) : _position),
        /**
         * serialiser
         * @memberOf RentalSupplier#
         * @return {Object} json represetnation of RentalSupplier
         */
        toJSON: () => {
            return {
                id: _id,
                groupName: _groupName,
                regionParId: _regionParId,
                paramCode: _paramCode,
                description: _description,
                name: _name,
                supTypeCode: _supTypeCode,
                address: _address ? _address.toJSON() : null,
                weekOpenTime: _weekOpenTime,
                weekCloseTime: _weekCloseTime,
                satOpenTime: _satOpenTime,
                satCloseTime: _satCloseTime,
                sunOpenTime: _sunOpenTime,
                sunCloseTime: _sunCloseTime,
                acountNo: _acountNo,
                areaName: _areaName,
                position: _position ? _position.toJSON() : null
            };
        }
    });
}

module.exports = RentalSupplier;
