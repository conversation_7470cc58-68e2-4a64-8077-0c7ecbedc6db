const { Vehicle } = require('@aa/data-models/common');
const Contact = require('@aa/malstrom-models/lib/contact.model'),
    Recovery = require('@aa/malstrom-models/lib/recovery.model');
/**
 * TemporaryVehicleRepairEvent model
 */
const _ = require('lodash');
/**
 * @class temp vehicle repair event
 * @param  {Object} raw json representation of TemporaryVehicleRepairEvent
 * @return {MobilityTask}
 */
function TemporaryVehicleRepairEvent(raw) {
    var model = this,
        _id = null,
        _retailer = -1,
        _customerRequest = null,
        _recovery = null,
        _contact = null,
        _vehicle = null,
        _isLocked = false;

    if (raw) {
        /**
         * id
         */
        _id = raw.id ? raw.id : _id;

        /**
         * retailer
         */
        _retailer = raw.retailer ? raw.retailer : _retailer;

        /**
         * customerRequest
         */
        _customerRequest = raw.customerRequest ? raw.customerRequest : _customerRequest;

        /**
         * recovery
         */
        _recovery = raw.recovery ? new Recovery(raw.recovery) : _recovery;

        /**
         * contact
         */
        _contact = raw.contact ? new Contact(raw.contact) : _contact;

        /**
         * vehicle
         */
        _vehicle = raw.vehicle ? new Vehicle(raw.vehicle) : _vehicle;

        /**
         * isLocked
         */
        _isLocked = raw.isLocked ? raw.isLocked : _isLocked;
    }

    _.extend(model, {
        /**
         * setter/getter for id
         */
        id: (...args) => (args.length ? (_id = args[0]) : _id),

        retailer: (...args) => (args.length ? (_retailer = args[0]) : _retailer),

        /**
         * setter/getter for customerRequest
         */
        customerRequest: (...args) => (args.length ? (_customerRequest = args[0]) : _customerRequest),

        /**
         * setter/getter for recovery
         */
        recovery: (...args) => (args.length ? (_recovery = args[0]) : _recovery),

        /**
         * setter/getter for contact
         */
        contact: (...args) => (args.length ? (_contact = args[0]) : _contact),

        /**
         * setter/getter for vehicle
         */
        vehicle: (...args) => (args.length ? (_vehicle = args[0]) : _vehicle),

        /**
         * setter/getter for isLocked
         */
        isLocked: (...args) => (args.length ? (_isLocked = args[0]) : _isLocked),

        /**
         * TemporaryVehicleRepairEvent serialiser
         * @return {Object} json representation of TemporaryVehicleRepairEvent
         */
        toJSON: function serialiseObject() {
            return {
                id: _id,
                retailer: _retailer,
                customerRequest: _customerRequest,
                vehicle: _vehicle.toJSON(),
                contact: _contact.toJSON(),
                isLocked: _isLocked
            };
        }
    });
}

module.exports = TemporaryVehicleRepairEvent;
