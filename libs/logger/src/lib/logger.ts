import { Context } from '@aa/context';
import { EventC<PERSON>, SnapshotLike } from '@aa/exception';
import { Instrumentation } from '@aa/instrumentation';
import { LogLevel, AppType } from '@aa/utils';
import * as BunyanLogger from 'bunyan';
import { createLogger } from 'bunyan';
import { cronJobWriteFn } from './cron-job-write-fn';

export class Logger {
    public driver: BunyanLogger;
    public readonly appName: string;
    public readonly appType?: AppType;
    protected context: Context;
    protected instrumentation?: Instrumentation;

    constructor(config: LoggerConfig) {
        const { context, component = 'main', instrumentation } = config;
        this.context = context;
        this.instrumentation = instrumentation;

        // create main logger or child logger
        if ((config as ChildLoggerConfig).parent) {
            const { parent } = config as ChildLoggerConfig;
            this.appName = parent.appName;
            this.driver = parent.driver.child({ component });
        } else {
            const { appName, appType, level = LogLevel.INFO } = config as MainLoggerConfig;
            this.appName = appName;
            this.appType = appType;
            const streams: BunyanLogger.Stream[] = [{ stream: process.stdout }];
            switch (appType) {
                case 'cron':
                    streams.push({ stream: cronJobWriteFn });
                    break;
                case 'api':
                case 'ui':
                default:
                    break;
            }
            this.driver = createLogger({ name: appName, level, component, streams });
        }
    }

    /**
     * Create a child logger based on current logger
     * @param {string} component
     * @return {Logger}
     */
    public child(component: string): Logger {
        return new Logger({
            parent: this,
            component,
            context: this.context,
            instrumentation: this.instrumentation
        });
    }

    public log(data: string | SnapshotLike): void {
        this.reportException(data);
        const snapshot = this.prepareData(data);
        switch (snapshot.code) {
            case EventCode.UNKNOWN:
            case EventCode.MOD_EXEC_WARN:
                this.warn(snapshot);
                break;
            case EventCode.MOD_EXEC_EVENT:
                this.info(snapshot);
                break;
            case EventCode.MOD_INVALID_CONFIG:
            case EventCode.MOD_CREATE_FAIL:
            case EventCode.MOD_INIT_FAIL:
            case EventCode.MOD_EXEC_FAIL:
            case EventCode.DB_CONNECTION_FAIL:
            case EventCode.DB_OPS_FAIL:
                this.error(snapshot);
                break;
            default:
                this.info(snapshot);
        }
    }

    public debug(data: string | SnapshotLike): void {
        this.reportException(data);
        const snapshot = this.prepareData(data);
        this.driver.debug(snapshot);
    }

    public warn(data: string | SnapshotLike): void {
        this.reportException(data);
        const snapshot = this.prepareData(data);
        this.driver.warn(snapshot);
    }

    public info(data: string | SnapshotLike): void {
        this.reportException(data);
        const snapshot = this.prepareData(data);
        this.driver.info(snapshot);
    }

    public error(data: string | SnapshotLike): void {
        this.reportException(data);
        const snapshot = this.prepareData(data);
        this.driver.error(snapshot);
    }

    protected reportException(data: string | SnapshotLike) {
        // if not exception
        if (typeof data === 'string') {
            return;
        }

        if (data.error && data.error instanceof Error) {
            // log error to get stacktrace
            this.driver.error(data.error);
        }

        // report only if instrumentation provided
        if (!this.instrumentation) {
            return;
        }

        if (data.error && data.error instanceof Error) {
            this.instrumentation.trackException({ exception: data.error });
        }
    }

    protected prepareData(data: string | SnapshotLike): LoggerSnapshot {
        let snapshot: LoggerSnapshot = {};

        // if more than message
        if (typeof data !== 'string') {
            // remove instance just in case we are dealing with cyclic refs
            delete data.source;
            snapshot = { ...data };

            // try to stringify data for logging
            if (typeof snapshot.data === 'object') {
                let snapshotDataError: unknown;
                // try to stringify to prevent logging issues
                try {
                    JSON.stringify(snapshot.data as never);
                } catch (error) {
                    this.warn({ message: (error as Error).message, data: { snapshot }, error });
                    snapshotDataError = `Cyclic data: ${snapshot.data && (snapshot.data as Record<any, any>).toString ? (snapshot.data as Record<any, any>).toString() : '[object]'}`;
                }
                snapshot.data = snapshotDataError || snapshot.data;
            }
        } else {
            snapshot = { data };
        }

        // retrieve additional details from context
        const context = this.context.store;
        const operatorId = Number.parseInt((context?.operatorId || context?.persona?.operatorId) as unknown as string);

        if (operatorId) {
            snapshot.operatorId = operatorId;
        }

        return snapshot;
    }
}

// eslint-disable-next-line
export type LoggerConfig = MainLoggerConfig | ChildLoggerConfig;

export interface MainLoggerConfig extends LoggerDeps {
    // name of component where logging happens e.g. 'RedisProvider', default to 'main'
    component?: string;
    appName: string;
    appType?: AppType;
    // default to INFO
    level?: LogLevel;
}

export interface ChildLoggerConfig extends LoggerDeps {
    // parent logger for which we create a child logger
    parent: Logger;
    // name of component where logging happens e.g. 'RedisProvider', default to 'main'
    component: string;
}

export interface LoggerDeps {
    context: Context;
    instrumentation?: Instrumentation;
}

interface LoggerSnapshot extends Omit<SnapshotLike, 'source'> {
    operatorId?: number;
}
