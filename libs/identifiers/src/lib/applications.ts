/**
 * Type representing an application, which can be a BackendApplication, FrontendApplication, MobileApplication, or a string.
 */
export type Application = BackendApplication | FrontendApplication | MobileApplication | string;

/**
 * Enum representing the list of all backend applications in the estate.
 */
export enum BackendApplication {
    AUTH = 'AUTH',
    ECALL_TASK = 'ECALL_TASK',
    AUDIT = 'AUDIT',
    QUEUE_STATUS = 'QUEUE_STATUS',
    UAC = 'UAC',
    DEMAND_DEFLECT = 'DEMAND_DEFLECT',
    CONFIG = 'CONFIG',
    USER = 'USER',
    AUDIT_WRITER = 'AUDIT_WRITER',
    ECALL_TASK_WRITER = 'ECALL_TASK_WRITER',
    GATEKEEPER = 'GATEKEEPER',
    TASK = 'TASK',
    MOBILITY_TASK = 'MOBILITY_TASK',
    ENTITLEMENT = 'ENTITLEMENT',
    VEHICLE_DETAILS = 'VEHICLE_DETAILS',
    IVR = 'IVR',
    OUTDOOR_INGEST = 'OUTDOOR_INGEST',
    POI_FACADE = 'POI_FACADE',
    UNITY_PROCESSOR = 'UNITY_PROCESSOR',
    OUTBOUND_EMAIL_STREAM = 'OUTBOUND_EMAIL_STREAM',
    SMR_PROCESSOR = 'SMR_PROCESSOR',
    POI = 'POI',
    BATTERY_TEST = 'BATTERY_TEST',
    BATTERY_TEST_MONITOR = 'BATTERY_TEST_MONITOR',
    CATHIE_PROCESSOR = 'CATHIE_PROCESSOR',
    CUV_SERVICE_PROCESSOR = 'CUV_SERVICE_PROCESSOR',
    CASE_SERVICE_PROCESSOR = 'CASE_SERVICE_PROCESSOR',
    EUOPS_OUTBOUND_EMAIL_STREAM = 'EUOPS_OUTBOUND_EMAIL_STREAM',
    EUOPS_INBOUND_EMAIL_STREAM = 'EUOPS_INBOUND_EMAIL_STREAM',
    EDOCS = 'EDOCS',
    EUROHELP_API = 'EUROHELP_API',
    WORKER_API = 'WORKER_API',
    AUDIT_STREAM = 'AUDIT_STREAM',
    ATTACHMENT_RENDERER_STREAM = 'ATTACHMENT_RENDERER_STREAM',
    INBOUND_EMAIL_STREAM = 'INBOUND_EMAIL_STREAM',
    NOTE_API = 'NOTE_API',
    TRIP_API = 'TRIP_API',
    BILLING_API = 'BILLING_API',
    ACTION_API = 'ACTION_API',
    ACTION_STREAM = 'ACTION_STREAM',
    TEAM_API = 'TEAM_API',
    EUOPS_EVENT_INGEST_API = 'EUOPS_EVENT_INGEST_API',
    EUOPS_SPARX_STREAM = 'EUOPS_SPARX_STREAM',
    ENTITLEMENT_BENEFIT_API = 'ENTITLEMENT_BENEFIT_API',
    EUOPS_TASK_STREAM = 'EUOPS_TASK_STREAM',
    TASK_STREAM = 'TASK_STREAM',
    CLOSED_HIRES_API = 'CLOSED_HIRES_API',
    ADMIN_APP_API = 'ADMIN_APP_API',
    BCAS_PROCESSOR = 'BCAS_PROCESSOR',
    PLAYGROUND = 'PLAYGROUND',
    NOTIFICATION_API = 'NOTIFICATION_API',
    NOTIFICATION_STREAM = 'NOTIFICATION_STREAM',
    TRANSACTION_HISTORY = 'TRANSACTION_HISTORY',
    MESSAGE_API = 'MESSAGE_API'
}

/**
 * Enum representing the list of all frontend applications in the estate.
 */
export enum FrontendApplication {
    AAHELP2 = 'AAHELP2',
    RETAILER = 'RETAILER',
    FLEET = 'FLEET',
    EUROHELP = 'EUROHELP',
    ADMINAPP = 'ADMINAPP'
}

/**
 * Enum representing the list of all mobile applications in the estate.
 */
export enum MobileApplication {
    EVA = 'EVA'
}
