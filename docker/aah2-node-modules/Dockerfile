FROM oraclelinux:8 AS build

RUN yum update
RUN yum module enable nodejs:14
RUN yum install -y nodejs
RUN yum install -y wget gnupg2 curl which
RUN yum install -y python3 make gcc gcc-c++
RUN npm install --global node-gyp@9.4.0

WORKDIR /app

RUN curl -sSL https://rvm.io/mpapis.asc | gpg2 --import -
RUN curl -sSL https://rvm.io/pku<PERSON>ynski.asc | gpg2 --import -
RUN curl -sSL https://get.rvm.io | bash -s stable

ENV BRIDGE_CI_DEPS_PATH=/app/node_modules/@aacom/aahelp-js-dependencies

WORKDIR /app
COPY .npmrc .
COPY package.json .
COPY package-lock.json .
COPY libs/bridge/ libs/bridge/
RUN npm ci

WORKDIR /app/libs/bridge/src
RUN node-gyp rebuild
RUN mv build /app

WORKDIR /app
RUN rm -f node_modules/.cache
RUN tar cf - node_modules build | gzip -9 - > ./modules.tar.gz

FROM alpine:3.20

WORKDIR /modules
COPY --from=build /app/modules.tar.gz .
COPY docker/aah2-node-modules/unpack.sh .
RUN chmod +x /modules/unpack.sh
ENTRYPOINT ["/modules/unpack.sh"]
