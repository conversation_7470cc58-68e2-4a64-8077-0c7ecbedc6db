#!/usr/bin/env node
import Actions from './categories/actions.mjs';
import EuopsCommsRoutes from './categories/euopsCommsRoutes.mjs';
import Trips from './categories/trips.mjs';
import Contacts from './categories/contacts.mjs';
import EuopsMessages from './categories/euopsMessages.mjs';
import Notes from './categories/notes.mjs';
import SecureLocations from './categories/secureLocations.mjs';
import EntitlementBenefits from './categories/entitlementBenefit.mjs';
import BenefitLimit from './categories/benefitLimit.mjs';
import TempEuopsMessagesAlreadyAdded from './categories/tempEuopsMessagesAlreadyAdded.mjs';
import TripCost from './categories/tripCosts.mjs';

(async (args) => {
    if (!args.length) {
        console.log();
        console.log('Usage: scaffold <env> <category> <task>');
        console.log('env: uat | int | ver | localhost');
        console.log('category: actions | routes | euopsCommsRoutes entitlementBenefits | all');
        console.log('task: populate | purge');
        console.log();
        console.log('Example: index.mjs uat actions populate');
        console.log();
        return;
    }

    const [env, category, task] = args;

    console.log({ env, category, task });

    if (!['uat', 'localhost', 'int', 'ver'].includes(env)) {
        console.log('Invalid environment 1');
        return;
    }

    if (!['populate', 'purge'].includes(task)) {
        console.log('Invalid task');
        return;
    }
    const actions = new Actions({ env });
    const euopsCommsRoutes = new EuopsCommsRoutes({ env });
    const trips = new Trips({ env });
    const contacts = new Contacts({ env });
    const euopsMessages = new EuopsMessages({ env });
    const notes = new Notes({ env });
    const secureLocations = new SecureLocations({ env });
    const entitlementBenefit = new EntitlementBenefits({ env });
    const benefitLimit = new BenefitLimit({ env });
    const tempEuopsMessagesAlreadyAdded = new TempEuopsMessagesAlreadyAdded({ env });
    //const tripCost = new TripCost({ env });

    switch (category) {
        case 'all':
            if (task === 'purge') {
                await actions.purge();
                await euopsCommsRoutes.purge();
                await trips.purge();
                //await contacts.purge(); don't!!!!
                await euopsMessages.purge();
                await notes.purge();
                await secureLocations.purge();
                await entitlementBenefit.purge();
                await benefitLimit.purge();
                await tempEuopsMessagesAlreadyAdded.purge();
                //await tripCost.purge();
            }
            if (task === 'populate') {
                //await actions.populate();
                await euopsCommsRoutes.populate();
                //await trips.populate();
                //await contacts.populate(); don't!!!!
                //await euopsMessages.populate();
                //await notes.populate();
                //await secureLocations.populate();
                await entitlementBenefit.populate();
                await benefitLimit.populate();
                //await tempEuopsMessagesAlreadyAdded.populate();
                //await tripCost.populate();
            }

            break;
        case 'actions':
            if (task === 'purge') {
                await actions.purge();
            }

            if (task === 'populate') {
                await actions.populate();
            }

            break;
        case 'routes':
            if (task === 'purge') {
                await euopsCommsRoutes.purge();
            }

            if (task === 'populate') {
                await euopsCommsRoutes.populate();
            }

            break;
        case 'euopsCommsRoutes':
            if (task === 'purge') {
                await euopsCommsRoutes.purge();
            }

            if (task === 'populate') {
                await euopsCommsRoutes.populate();
            }

            break;
        case 'entitlementBenefits':
            if (task === 'purge') {
                await entitlementBenefit.purge();
            }

            if (task === 'populate') {
                await entitlementBenefit.populate();
            }

            break;
        default:
            console.log('Invalid task');
            break;
    }
})(process.argv.slice(2));
