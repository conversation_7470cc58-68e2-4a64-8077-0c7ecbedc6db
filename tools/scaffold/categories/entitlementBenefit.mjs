import Mongo from '../lib/mongo.mjs';

const predefinedCustomerGroups = [
    ['BCASP786480', 'ASST'],
    ['BCASP786481', 'ASST'],
    ['BCASP786482', 'ASST'],
    ['BCASP790783', 'ASST'],
    ['BCASP790784', 'ASST'],
    ['BCASP790785', 'ASST'],
    ['BCASP790786', 'ASST'],
    ['BCASP788626', 'TCB'],
    ['BCASP788627', 'TCB'],
    ['BCASP788628', 'TCB'],
    ['BCASP788629', 'ADAC'],
    ['BCASP788630', 'ADAC'],
    ['BCASP788631', 'ADAC'],
    ['BCASP788633', 'AAIR'],
    ['BCASP788634', 'AAIR'],
    ['BCASP788635', 'AAIR'],
    ['BCASP788636', 'AAIR'],
    ['BCASP788638', 'ANWB'],
    ['BCASP788639', 'ANWB'],
    ['BCASP788640', 'ANWB'],
    ['BCASP790790', 'ANWB'],
    ['BCASP790791', 'ANWB'],
    ['BCASP790792', 'ANWB'],
    ['BCASP790793', 'ANWB'],
    ['BCASP788641', 'FOO'],
    ['BCASP788642', 'FOO'],
    ['BCASP788643', 'FOO'],
    ['BCASP788644', 'AAP'],
    ['BCASP788645', 'AAP'],
    ['BCASP788646', 'AAP'],
    ['BCASP788647', 'ACA'],
    ['BCASP788648', 'ACA'],
    ['BCASP788649', 'ACA'],
    ['BCASP788700', 'BIHA'],
    ['BCASP788701', 'BIHA'],
    ['BCASP788702', 'BIHA'],
    ['BCASP788668', 'UAB'],
    ['BCASP788669', 'UAB'],
    ['BCASP788670', 'UAB'],
    ['BCASP788696', 'HAK'],
    ['BCASP788697', 'HAK'],
    ['BCASP788698', 'HAK'],
    ['BCASP788657', 'ODYK'],
    ['BCASP788658', 'ODYK'],
    ['BCASP788659', 'ODYK'],
    ['BCASP788693', 'UAMK'],
    ['BCASP788694', 'UAMK'],
    ['BCASP788695', 'UAMK'],
    ['BCASP788654', 'FDMV'],
    ['BCASP788655', 'FDMV'],
    ['BCASP788656', 'FDMV'],
    ['BCASP788665', 'RGEO'],
    ['BCASP788666', 'RGEO'],
    ['BCASP788667', 'RGEO'],
    ['BCASP788721', 'ALFI'],
    ['BCASP788722', 'ALFI'],
    ['BCASP788723', 'ALFI'],
    ['BCASP788690', 'AEF'],
    ['BCASP788691', 'AEF'],
    ['BCASP788661', 'AEH'],
    ['BCASP788662', 'AEH'],
    ['BCASP788663', 'AEH'],
    ['BCASP788650', 'MAK'],
    ['BCASP788651', 'MAK'],
    ['BCASP788652', 'MAK'],
    ['BCASP788719', 'KROK'],
    ['BCASP788720', 'KROK'],
    ['BCASP788716', 'AGS'],
    ['BCASP788717', 'AGS'],
    ['BCASP788718', 'AGS'],
    ['BCASP788686', 'KTA'],
    ['BCASP788687', 'KTA'],
    ['BCASP788688', 'KTA'],
    ['BCASP788743', 'SIAA'],
    ['BCASP788744', 'SIAA'],
    ['BCASP788745', 'SIAA'],
    ['BCASP788683', 'EAS'],
    ['BCASP788684', 'EAS'],
    ['BCASP788685', 'EAS'],
    ['BCASP788740', 'ACLX'],
    ['BCASP788741', 'ACLX'],
    ['BCASP788742', 'ACLX'],
    ['BCASP788786', 'AMSM'],
    ['BCASP788787', 'AMSM'],
    ['BCASP788788', 'AMSM'],
    ['BCASP788680', 'RMF'],
    ['BCASP788681', 'RMF'],
    ['BCASP788682', 'RMF'],
    ['BCASP788713', 'AMSC'],
    ['BCASP788714', 'AMSC'],
    ['BCASP788715', 'AMSC'],
    ['BCASP788676', 'SOSV'],
    ['BCASP788677', 'SOSV'],
    ['BCASP788678', 'SOSV'],
    ['BCASP788792', 'AEP'],
    ['BCASP788793', 'AEP'],
    ['BCASP788794', 'AEP'],
    ['BCASP788736', 'ACP'],
    ['BCASP788737', 'ACP'],
    ['BCASP788738', 'ACP'],
    ['BCASP788709', 'ATFO'],
    ['BCASP788710', 'ATFO'],
    ['BCASP788711', 'ATFO'],
    ['BCASP788795', 'RAMC'],
    ['BCASP788796', 'RAMC'],
    ['BCASP788797', 'RAMC'],
    ['BCASP788706', 'AMSS'],
    ['BCASP788707', 'AMSS'],
    ['BCASP788708', 'AMSS'],
    ['BCASP788733', 'ASA'],
    ['BCASP788734', 'ASA'],
    ['BCASP788735', 'ASA'],
    ['BCASP788799', 'AMZS'],
    ['BCASP788800', 'AMZS'],
    ['BCASP788801', 'AMZS'],
    ['BCASP788704', 'SOSI'],
    ['BCASP788705', 'SOSI'],
    ['BCASP788730', 'TCS'],
    ['BCASP788731', 'TCS'],
    ['BCASP788732', 'TCS'],
    ['BCASP788727', 'MARM'],
    ['BCASP788728', 'MARM'],
    ['BCASP788817', 'GARA'],
    ['BCASP788818', 'GARA'],
    ['BCASP788819', 'GARA'],
    ['BCASP788724', 'RACE'],
    ['BCASP788725', 'RACE'],
    ['BCASP788726', 'RACE'],
    ['BCASP793236', 'AAIR'],
    ['BCASP793251', 'SIAA'],
    ['BCASP793237', 'SOSV'],
    ['BCASP793238', 'AEP'],
    ['BCASP788643', 'RACE'],
    ['BCASP788642', 'RACE'],
    ['BCASP788641', 'RACE'],
    ['BCASP793254', 'SOSI'],
    ['BCASP792930', 'MARM'],
    ['BCASP792929', 'MARM'],
    ['BCASP793318', 'GSB'],
    ['BCASP793315', 'GAEN'],
    ['BCASP793312', 'FOF'],
    ['BCASP793313', 'RGO'],
    ['BCASP793309', 'ADAE'],
    ['BCASP793305', 'SSF'],
    ['BCASP793304', 'PEN'],
    ['BCASP793302', 'SAST'],
    ['BCASP793301', 'TCT'],
    ['BCASP793300', 'AMZT'],
    ['BCASP793299', 'EAT'],
    ['BCASP793298', 'AGT'],
    ['BCASP793297', 'ADAD'],
    ['BCASP793295', 'AEG'],
    ['BCASP793290', 'TCC'],
    ['BCASP793289', 'ASSQ']
];

export default class Actions {
    constructor({ env }) {
        this.mongo = new Mongo({ env, dbName: 'entities' });
    }

    generateRecord(customerGroup, contractKey) {
        return {
            created: new Date(),
            updated: new Date(),
            customerGroup: customerGroup,
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: contractKey
        };
    }

    async populate() {
        const dbSystemConfig = await this.mongo.connect();
        const coll = await dbSystemConfig.collection('entitlementBenefit');
        console.log(predefinedCustomerGroups);
        const records = predefinedCustomerGroups.map(([contractKey, customerGroup]) => {
            const record = this.generateRecord(customerGroup, contractKey);
            //return Schema.validate(EntitlementBenefitsSchema, record);
            return record;
        });

        await coll.insertMany(records);
    }

    async purge() {
        const dbSystemConfig = await this.mongo.connect();
        const coll = await dbSystemConfig.collection('entitlementBenefit');
        await coll.deleteMany({});
        return;
    }
}
