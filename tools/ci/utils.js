const { promises: fs } = require('fs');
const path = require('path');
const semver = require('semver');

/**
 * Returns true if current commit is first on a branch (relative to master)
 * @param branch
 * @return {Promise<boolean>}
 */
async function isFirstCommit(branch) {
    try {
        let firstSha = await shellExec(`git log origin/master --reverse --oneline | tail -1 | cut -d " " -f1`);
        let currentSha = await shellExec(`git rev-parse --short origin/${branch}`);

        firstSha = firstSha.replace(/\s/g, '');
        currentSha = currentSha.replace(/\s/g, '');
        return firstSha === currentSha;
    } catch (error) {
        console.error('Shell error:', error);
        throw Error('Failed checking if commit first on branch');
    }
}

/**
 * Executing this file produces Github Action matrix
 * with a list of packages in the dist dir
 */
async function getArtefactDigest(distDir = './dist/apps', onlyForPackages = undefined) {
    const matrixList = [];

    try {
        const files = await fs.readdir(distDir);
        for (const file of files) {
            if (onlyForPackages && !onlyForPackages.includes(file)) {
                continue;
            }

            const { version } = require(path.resolve(distDir, file, 'package.json'));
            matrixList.push({ name: file, version });
        }
    } catch (error) {
        console.warn('No artefact dir or error while reading');
        console.debug('Read dir error:', error);
    }

    return matrixList;
}

/**
 * Executing this file produces a list of all apps in the monorepo
 */
async function getAllApps(appDir = './apps') {
    let list = [];

    try {
        list = await fs.readdir(appDir);
    } catch (error) {
        console.warn('No app dir or error while reading');
        console.debug('Read dir error:', error);
    }

    return list;
}

/**
 * Check if SHA belongs to a branch
 */
async function shaExistsOnBranch(sha, branch) {
    if (!sha) {
        throw Error('Missing SHA');
    }
    if (!branch) {
        throw Error('Missing branch name');
    }

    try {
        const result = await shellExec(`git branch --contains ${sha} | grep ${branch}`);
        return !!result;
    } catch (error) {
        console.error('Shell error:', error);
        throw Error('Failed checking sha existence on branch');
    }
}

/**
 * Get last release number
 * @return {Promise<number>}
 */
async function getLastRelease() {
    console.log('getLastRelease: Retrieving last release number');
    try {
        const result = await shellExec(`git branch --list -r 'origin/release/*' | awk -F "/" '{ print $3 }' | sort -nr | head -1`);
        console.log(`getLastRelease: Result: ${result}`);

        if (!result) {
            console.log('getLastRelease: No release branches found, returning 0');
            return 0;
        }

        const branchNumber = result.replace('origin/', '').replace('release/', '');
        const lastRelease = Number.parseInt(branchNumber);
        console.log(`getLastRelease: Last release number: ${lastRelease}`);
        return lastRelease;
    } catch (error) {
        console.error('getLastRelease: Shell error:', error);
        throw Error('Failed checking last release number');
    }
}

/**
 * Write data to file
 * @param {string} path
 * @param {any} data
 * @returns {Promise<void>}
 */
async function writeToFile(path, data) {
    let finalData = data;
    if (typeof data === 'object') {
        finalData = JSON.stringify(data);
    }
    await fs.writeFile(path, finalData);
}

/**
 * Read data from file
 * @param {string} path
 * @returns {Promise<string>}
 */
async function readFromFile(path) {
    const data = await fs.readFile(path);
    return data.toString();
}

/**
 * Get branch type from branch name
 * @param branchName
 * @returns {string}
 */
function branchTypeFromBranchName(branchName) {
    if (branchName === 'master') {
        return 'master';
    } else if (branchName.toLowerCase().match(/^feature\//)) {
        return 'feature';
    } else if (branchName.toLowerCase().match(/^release\//)) {
        return 'release';
    } else if (branchName.toLowerCase().match(/^hotfix\//)) {
        return 'hotfix';
    } else if (branchName.toLowerCase().match(/^bugfix\//)) {
        return 'bugfix';
    } else if (branchName.toLowerCase().match(/^story\//)) {
        return 'story';
    } else if (branchName.toLowerCase().match(/^workstream\//)) {
        return 'workstream';
    } else if (branchName.toLowerCase().match(/^dependency\//)) {
        return 'dependency';
    }

    return 'unknown';
}

/**
 * Returns release number for provided branch name:
 *  - For master branch its the highest release number +1 based on the git history
 *  - For workstream branch its the highest release number +1 based on the git history
 *  - for release branch its branch release number
 *
 * @param {string} branchName
 * @return {Promise<number>} branch releases number
 */
async function getReleaseNumber(branchName) {
    console.log(`getReleaseNumber: Starting computation for branch '${branchName}'`);
    const branchType = branchTypeFromBranchName(branchName);
    console.log(`getReleaseNumber: Determined branch type: '${branchType}'`);

    // for release branch extract number from its name
    if (branchType === 'release') {
        console.log(`getReleaseNumber: Processing release branch`);
        const match = new RegExp('release/([0-9]+)$').exec(branchName);
        if (!match || (match && match.length < 2)) {
            console.error(`getReleaseNumber: Branch has incorrect name: '${branchName}'`);
            throw new Error(`Branch has incorrect name: ${branchName}`);
        }
        const releaseNumber = Number.parseInt(match[1]);
        console.log(`getReleaseNumber: Extracted release number: '${releaseNumber}'`);
        return releaseNumber;
    }

    console.log(`getReleaseNumber: Processing non-release branch`);
    const lastRelease = await getLastRelease();
    console.log(`getReleaseNumber: Retrieved last release number: '${lastRelease}'`);
    const nextRelease = lastRelease + 1;
    console.log(`getReleaseNumber: Computed next release number: '${nextRelease}'`);
    return nextRelease;
}

/**
 * Get final version of the package based on releases
 *
 * Artefact semver follows this format:
 * ```[target-release].[iteration in release].[fix number]```
 *  - 150.1.0                -----> app on master branch
 *  - 150.2.0                -----> app on master branch
 *  - 150.2.1                -----> app on release branch, before live
 *  - 150.2.2                -----> app on release branch, before live
 *  - 150.2.3                -----> app on release branch, after live
 *  - 150.2.4                -----> app on release branch, after live
 *  - 151.1.0                -----> app on master branch
 *  - 151.2.0                -----> app on master branch
 *
 * @param {string} name
 * @param {string} branchName
 * @returns {Promise<{version: string}>}
 */
async function getFinalVersion(name, branchName) {
    console.log(`getFinalVersion: Starting computation for package '${name}' on branch '${branchName}'`);
    const semver = require('semver');
    const release = await getReleaseNumber(branchName);
    console.log(`getFinalVersion: Retrieved release number: ${release}`);
    const branchType = branchTypeFromBranchName(branchName);
    console.log(`getFinalVersion: Determined branch type: '${branchType}'`);

    let finalVersion;
    let latestVersion;

    switch (branchType) {
        case 'master':
            console.log(`getFinalVersion: Processing master branch`);
            latestVersion = await getLatestPackageVersion(name, release, false);
            console.log(`getFinalVersion: Latest version for master branch: '${latestVersion}'`);

            // if no version for release start new package for release
            if (latestVersion) {
                finalVersion = semver.parse(latestVersion).inc('minor').version;
            } else {
                finalVersion = `${release}.1.0`;
            }
            console.log(`getFinalVersion: Computed final version for master branch: '${finalVersion}'`);
            break;
        case 'release':
            console.log(`getFinalVersion: Processing release branch`);
            latestVersion = await getLatestPackageVersion(name, release, false);
            console.log(`getFinalVersion: Latest version for release branch: '${latestVersion}'`);

            // if no version for release start new package for release
            if (latestVersion) {
                finalVersion = semver.parse(latestVersion).inc('patch').version;
            } else {
                finalVersion = `${release}.1.1`;
            }
            console.log(`getFinalVersion: Computed final version for release branch: '${finalVersion}'`);
            break;
        case 'workstream':
            console.log(`getFinalVersion: Processing workstream branch`);
            const suffix = `workstream-${branchName.replace('workstream/', '')}`;
            console.log(`getFinalVersion: Computed suffix for workstream branch: '${suffix}'`);
            latestVersion = await getLatestPackageVersion(name, release, false, suffix);
            console.log(`getFinalVersion: Latest version for workstream branch: '${latestVersion}'`);

            // if no version for release start new package for release
            if (latestVersion) {
                latestVersion = latestVersion.replace(`-${suffix}`, '');
                console.log(`getFinalVersion: Stripped suffix from latest version: '${latestVersion}'`);
                finalVersion = semver.parse(latestVersion).inc('minor').version;
                // When we increment versions prerelease tags are removed, we need to add them back again
                finalVersion = `${finalVersion}-${suffix}`;
                console.log(`getFinalVersion: Incremented minor version for workstream branch: '${finalVersion}'`);
            } else {
                finalVersion = `${release}.1.1-${suffix}`;
            }
            console.log(`getFinalVersion: Computed final version for workstream branch: '${finalVersion}'`);
            break;
        case 'dependency':
        case 'story':
        case 'hotfix':
        case 'bugfix':
        case 'feature':
            console.error(`getFinalVersion: Unsupported branch type '${branchType}' for version update`);
            throw new Error(`Commits to branch of type ${branchType} do not require version update`);
        default:
            console.error(`getFinalVersion: Unsupported branch type '${branchType}'`);
            throw new Error(`Unsupported branch type: ${branchType}`);
    }

    console.log(`getFinalVersion: Final computed version: '${finalVersion}'`);
    return finalVersion;
}

/**
 * Update artefact package.json with updated values
 * @param {string} name
 * @param {string} dir
 * @param {string} branchName
 * @param {string | undefined} owner
 * @returns {Promise<{name: string, version}>}
 */
async function updatePackageVersion(name, dir, branchName, owner = undefined) {
    console.log(`updatePackageVersion: Starting update for package '${name}' in directory '${dir}' on branch '${branchName}'`);
    if (owner) {
        console.log(`updatePackageVersion: Owner provided, updating scope to '@${owner}'`);
    }

    const packageFilePath = path.resolve(dir, 'package.json');
    const packageLockFilePath = path.resolve(dir, 'package-lock.json');
    console.log(`updatePackageVersion: Resolved package file path: '${packageFilePath}'`);
    console.log(`updatePackageVersion: Resolved package lock file path: '${packageLockFilePath}'`);

    const package = require(packageFilePath);
    const packageLock = require(packageLockFilePath);
    console.log(`updatePackageVersion: Loaded package.json: ${JSON.stringify(package)}`);
    console.log(`updatePackageVersion: Loaded package-lock.json: ${JSON.stringify(packageLock)}`);

    const finalVersion = await getFinalVersion(name, branchName);
    console.log(`updatePackageVersion: Computed final version: '${finalVersion}'`);

    // if owner provided lets update scope
    if (owner) {
        package.name = `@${owner}/${package.name}`;
        packageLock.name = `@${owner}/${package.name}`;
        console.log(`updatePackageVersion: Updated package name to '${package.name}'`);
        console.log(`updatePackageVersion: Updated package lock name to '${packageLock.name}'`);
    }

    package.version = finalVersion;
    packageLock.version = finalVersion;
    console.log(`updatePackageVersion: Updated package version to '${package.version}'`);
    console.log(`updatePackageVersion: Updated package lock version to '${packageLock.version}'`);

    await fs.writeFile(packageFilePath, JSON.stringify(package));
    console.log(`updatePackageVersion: Successfully wrote updated package.json to '${packageFilePath}'`);

    await fs.writeFile(packageLockFilePath, JSON.stringify(packageLock));
    console.log(`updatePackageVersion: Successfully wrote updated package-lock.json to '${packageLockFilePath}'`);

    console.log(`updatePackageVersion: Update completed for package '${name}'`);
    return { name: package.name, version: package.version };
}

/**
 * Execute shell script
 * @param {string} command
 * @returns {Promise<string>}
 */
async function shellExec(command) {
    const util = require('node:util');
    const nodeExec = util.promisify(require('node:child_process').exec);

    const { stdout, stderr } = await nodeExec(command);

    return stdout;
}

/**
 * Update artefact package.json script value
 * @param {string} name
 * @param {string} dir
 * @returns {Promise<void>}
 */
async function updatePackageScript(name, dir) {
    const packageFilePath = path.resolve(dir, 'package.json');
    const package = require(packageFilePath);

    package.scripts = { start: 'node main.js' };

    const bridgeApps = ['@aacom/prime-proxy', '@aacom/reference-data', '@aacom/task-audit', '@aacom/task', '@aacom/vehicle-details'];
    if (bridgeApps.includes(package.name)) {
        package.scripts.preinstall = 'node libs/bridge/src/scripts/hide.gyp.js';
    }

    await fs.writeFile(packageFilePath, JSON.stringify(package));
}

/**
 * Get top version of a package for specific release, optionally get versions below this release version
 * @param {string} package
 * @param {number} release
 * @param {boolean} allowPrevious Returns latest version for packages published in prev releases, by default false
 * @param {string | undefined} suffix Whats the prerelease suffix for the version - usually used for identifying workstream packages e.g. 'workstream-vw-p1'
 * @returns {Promise<string | void>}
 */
async function getLatestPackageVersion(package, release, allowPrevious = false, suffix = undefined) {
    try {
        const semver = require('semver');
        const { Octokit } = require('@octokit/action');
        const octokit = new Octokit();

        // get all possible package versions, considering req returns max 100 per page
        let page = 0;
        const allVersions = [];
        let more = true;

        while (more) {
            const { data } = await octokit.request(`GET /orgs/{org}/packages/{package_type}/{package_name}/versions?per_page=100&page=${page}`, {
                package_type: 'npm',
                org: 'aacom',
                headers: { 'X-GitHub-Api-Version': '2022-11-28' },
                package_name: package
            });

            more = !!(data && data.length);
            allVersions.push(...data);
            page++;
        }

        // Top versions can come from release branch and belong to the older release
        // lets sort versions by semver just to be sure
        let topVersions = allVersions
            .filter((entry) => !!entry)
            .map((def) => def.name)
            .sort((versionA, versionB) => {
                const semverA = semver.parse(versionA);
                const semverB = semver.parse(versionB);

                return semverB.compare(semverA);
            })
            .filter((version) => {
                // we want to remove versions that are def not a match
                const match = version.match(new RegExp(`^([0-9]+)\\..*`));
                if (!match) {
                    return false;
                }
                const packageRelease = Number.parseInt(match[1]);

                // remove any versions higher than required release
                // if previous versions not allowed we make strict match to provided release
                if (!allowPrevious) {
                    return packageRelease === release;
                }

                // if prev versions allowed, we accept older version of the package if
                return packageRelease <= release;
            });

        let topVersion = topVersions.find((version) => {
            const packageSemver = semver.parse(version);
            // if suffix provided find first version that has matching all prerelease tags
            if (suffix) {
                return packageSemver.prerelease.includes(suffix);
            }

            // if no suffix provided find first version that has
            // no prerelease tags (ensures we dont pollute release
            // packages with some workstream packages)
            return !packageSemver.prerelease.length;
        });

        // if still no top version but suffix provided we need to fallback on any version
        if (!topVersion && suffix) {
            topVersion = topVersions.find((version) => {
                const packageSemver = semver.parse(version);
                return !packageSemver.prerelease.length;
            });
        }

        return topVersion;
    } catch (error) {
        if (error.status && error.status === 404) {
            return;
        }

        // TODO: throw error via octokit as this one dont stop workflow
        throw error;
    }
}

/**
 * Get workflow id for config
 * @param config
 * @return {Promise<string>}
 */
async function getWorkflowRunId(config) {
    const { Octokit } = require('@octokit/action');
    const octokit = new Octokit();

    // TODO: implement

    const { repoOwner, repoName, workflowRunId, status, headSha } = config;

    const response = await octokit.request('GET /repos/{owner}/{repo}/actions/runs{?status,head_sha,event}', {
        owner: repoOwner,
        repo: repoName,
        status: 'completed', // or success ?
        head_sha: headSha,
        event: 'pull_request', // push ??
        headers: { 'X-GitHub-Api-Version': '2022-11-28' }
    });

    return 'run id';
}

/**
 * Retrieve azure KV entry
 * @param {string} tenantId
 * @param {string} clientId
 * @param {string} clientSecret
 * @param {string} keyVaultName
 * @param {string} secretName
 * @returns {Promise<string>}
 */
async function getAzureKVSecret(tenantId, clientId, clientSecret, keyVaultName, secretName) {
    const { ClientSecretCredential } = require('@azure/identity');
    const { SecretClient } = require('@azure/keyvault-secrets');

    // Authenticate with client secret.
    const credential = new ClientSecretCredential(tenantId, clientId, clientSecret);

    // Get kv secret
    const url = `https://${keyVaultName}.vault.azure.net`;
    const client = new SecretClient(url, credential);
    const latestSecret = await client.getSecret(secretName);
    return latestSecret.value;
}

/**
 * Get ssh key from KV and write it to the file
 * @param {string} tenantId
 * @param {string} clientId
 * @param {string} clientSecret
 * @param {string} keyVaultName
 * @param {string} secretName
 * @param {string} path
 * @returns {Promise<void>}
 */
async function getSSHKeyAsFile(tenantId, clientId, clientSecret, keyVaultName, secretName, path) {
    const secret = await getAzureKVSecret(tenantId, clientId, clientSecret, keyVaultName, secretName);

    // Fix format of ssh key - KV mangles it a bit
    let formattedSecret = secret
        .replace('-----BEGIN OPENSSH PRIVATE KEY-----', 'START')
        .replace('-----END OPENSSH PRIVATE KEY-----', 'END')
        .replaceAll(' ', '\n')
        .replace('START', '-----BEGIN OPENSSH PRIVATE KEY-----')
        .replace('END', '-----END OPENSSH PRIVATE KEY-----');
    formattedSecret += '\n';

    // Write key to file
    await writeToFile(path, formattedSecret);
    await shellExec(`chmod 600 ${path}`);
}

module.exports = {
    getAllApps,
    getReleaseNumber,
    getLastRelease,
    isFirstCommit,
    getArtefactDigest,
    writeToFile,
    readFromFile,
    getLatestPackageVersion,
    updatePackageVersion,
    branchTypeFromBranchName,
    updatePackageScript,
    shellExec,
    getAzureKVSecret,
    getSSHKeyAsFile,
    getFinalVersion
};
