const { getFinalVersion, getLatestPackageVersion } = require('./utils');
//
// getFinalVersion('trip-api', 'master').then((version) => {
//     console.log(`For master: ${version}`);
// });
//
// getFinalVersion('trip-api', 'release/162').then((version) => {
//     console.log(`For release/2: ${version}`);
// });

async function runGetLatestPackageVersionTests() {
    const scenarios = [
        { package: 'bcall', release: 167, allowPrevious: false },
        { package: 'bcall', release: 167, allowPrevious: true },
        { package: 'bcall', release: 168, allowPrevious: false },
        { package: 'bcall', release: 168, allowPrevious: true },
        { package: 'bcall', release: 169, allowPrevious: false },
        { package: 'bcall', release: 169, allowPrevious: true },
        { package: 'bcall', release: 167, allowPrevious: false, suffix: 'invalid' },
        { package: 'bcall', release: 167, allowPrevious: true, suffix: 'invalid' },
        { package: 'bcall', release: 168, allowPrevious: false, suffix: 'invalid' },
        { package: 'bcall', release: 168, allowPrevious: true, suffix: 'invalid' },
        { package: 'bcall', release: 169, allowPrevious: false, suffix: 'invalid' },
        { package: 'bcall', release: 169, allowPrevious: true, suffix: 'invalide' },
        { package: 'bcall', release: 167, allowPrevious: false, suffix: 'workstream-vwg-p1' },
        { package: 'bcall', release: 167, allowPrevious: true, suffix: 'workstream-vwg-p1' },
        { package: 'bcall', release: 168, allowPrevious: false, suffix: 'workstream-vwg-p1' },
        { package: 'bcall', release: 168, allowPrevious: true, suffix: 'workstream-vwg-p1' },
        { package: 'bcall', release: 169, allowPrevious: false, suffix: 'workstream-vwg-p1' }
    ];

    let i = 0;
    for (const { package, release, allowPrevious, suffix } of scenarios) {
        i++;
        console.log(`Test ${i} --------------------------------------`);
        console.log('package', package, 'release', release, 'allowPrevious', allowPrevious, 'suffix', suffix);
        await getLatestPackageVersion(package, release, allowPrevious, suffix).then((version) => {
            console.log(`${package}@${version}`);
        });
    }
}

runGetLatestPackageVersionTests()
    .then(() => {
        console.log('Done!');
    })
    .catch((err) => {
        console.error('Error!', err);
    });
