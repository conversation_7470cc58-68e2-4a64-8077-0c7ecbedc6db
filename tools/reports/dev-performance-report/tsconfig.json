{"compileOnSave": false, "compilerOptions": {"module": "commonjs", "outDir": "dist/out-tsc", "declaration": true, "types": ["node"], "rootDir": ".", "sourceMap": true, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "isolatedModules": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "noImplicitReturns": true, "allowJs": true, "strictBindCallApply": true, "esModuleInterop": true, "target": "es2015", "lib": ["es2021", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "resolveJsonModule": true, "baseUrl": "."}, "exclude": ["node_modules", "tmp"]}