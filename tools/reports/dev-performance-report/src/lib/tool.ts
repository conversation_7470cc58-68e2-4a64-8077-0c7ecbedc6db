import { ChartJSNodeCanvas } from 'chartjs-node-canvas';
import fs from 'fs';
import process from 'node:process';
import { Octokit } from 'octokit';
import puppeteer from 'puppeteer';

/**
 * Main function to run the report generation process.
 * @return {Promise<void>}
 */
async function run(): Promise<void> {
    const githubToken = process.env.GITHUB_TOKEN;
    if (typeof githubToken !== 'string') {
        throw new Error('Missing env variable GITHUB_TOKEN');
    }
    const repoOwner = process.env.REPO_OWNER;
    if (typeof repoOwner !== 'string') {
        throw new Error('Missing env variable REPO_OWNER');
    }
    const repoName = process.env.REPO_NAME;
    if (typeof repoName !== 'string') {
        throw new Error('Missing env variable REPO_NAME');
    }
    const weeks = Number.parseInt(process.env.WEEKS || '');
    if (isNaN(weeks)) {
        throw new Error('Missing env variable WEEKS');
    }
    const reportPath = process.env.REPORT_PATH;
    if (typeof reportPath !== 'string') {
        throw new Error('Missing env variable REPORT_PATH');
    }

    try {
        console.log('Generating report started');

        const githubContributions = await getGithubContributions(weeks, repoOwner, repoName, githubToken);
        const { previous, current } = getContributions(weeks, githubContributions);
        const contribtionStats = getRepoContributionStats(weeks, previous, current);
        const htmlReport = await getHtmlReport(weeks, current, contribtionStats);
        await generatePDFFromHtml(htmlReport, reportPath);
        // Used only for development
        // await generateHtmlPage(htmlReport);

        console.log('Generating report finished');
    } catch (error) {
        console.log(error?.message);
        throw new Error('Failure while running report generation');
    }
}

/**
 * Generates an HTML report with styled content.
 * @param weeks {number} - Number of weeks for the report.
 * @param contributions {Contribution[]} - List of contributions.
 * @param repoContributionStats {RepoContributionStats} - Repository contribution statistics.
 * @return {Promise<string>} - The generated HTML content.
 */
async function getHtmlReport(weeks: number, contributions: Contribution[], repoContributionStats: RepoContributionStats): Promise<string> {
    const dateFrom = new Date(new Date().getTime() - weeks * 7 * 24 * 60 * 60 * 1000);
    const dateTo = new Date();

    const reportHeaderContent = `
        <p><strong>Repository:</strong> ${process.env.REPO_NAME}</p>
        <p><strong>Organization:</strong> ${process.env.REPO_OWNER}</p>
        <p><strong>Time Period:</strong> ${dateFrom.toLocaleDateString()} - ${dateTo.toLocaleDateString()} (Last ${weeks} weeks)</p>
    `;

    const repoContributionStatsContent = `
        <p><strong>Total Lines Added:</strong> <span class="green">${repoContributionStats.linesAdded}</span></p>
        <p><strong>Total Lines Deleted:</strong> <span class="red">${repoContributionStats.linesDeleted}</span></p>
        <p><strong>Total Commits:</strong> ${repoContributionStats.commitCount}</p>
        <p><strong>Change vs previous ${weeks} weeks</strong>
        </br>
        ${
            repoContributionStats.change > 0
                ? `<span class="green">Increase of ${repoContributionStats.change.toFixed(2)}%</span></p>`
                : repoContributionStats.change < 0
                ? `<span class="red">Decrease of ${repoContributionStats.change.toFixed(2)}%</span></p>`
                : `<span class="blue;">No change - ${repoContributionStats.change.toFixed(2)}%</span></p>`
        }
        <p>
            <strong>Active Contributors:</strong> </br>
            <span class="green">${repoContributionStats.activeContributors.map((c) => c.login).join(', ')}</span>
        </p>
        <p>
            <strong>Inactive Contributors:</strong> </br>
            <span class="red">${repoContributionStats.inactiveContributors.map((c) => c.login).join(', ')}</span>
            </p>
    `;

    let contributionsContent = '';
    for (const contribution of contributions) {
        const author = contribution.author;
        const chartData = contribution.weeks.map((week, index) => ({
            label: `Week ${index + 1}`,
            value: week.commitCount
        }));
        const chartImage = await generateBarChartImage('Weekly Commits', chartData);

        contributionsContent += `
            <div class="contributor">
                <div class="details">
                    <h3>${author.login}</h3>
                    <img src="${author.avatar_url}" alt="${author.login}" width="50" height="50" style="border-radius: 50%;" />
                    <p><strong>Commits:</strong> ${contribution.stats.commitCount}</p>
                    <p><strong>Change (prev period):</strong> </br>
                    ${
                        contribution.stats.change > 0
                            ? `<span class="green">Increase of ${contribution.stats.change.toFixed(2)}%</span></p>`
                            : contribution.stats.change < 0
                            ? `<span class="red">Decrease of ${contribution.stats.change.toFixed(2)}%</span></p>`
                            : `<span class="blue;">No change - ${contribution.stats.change.toFixed(2)}%</span></p>`
                    }
                    <p><strong>Lines Added:</strong> <span class="green">${contribution.weeks.reduce((sum, week) => sum + week.linesAdded, 0)}</span></p>
                    <p><strong>Lines Deleted:</strong> <span class="red">${contribution.weeks.reduce((sum, week) => sum + week.linesDeleted, 0)}</span></p>
                </div>
                <img class="contributor-chart" src="data:image/png;base64,${chartImage}" />
            </div>
        `;
    }

    const htmlContent = `
        <html>
            <head>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        line-height: 1.6;
                        margin: 20px;
                    }
                    h1, h2, h3 {
                        color: #333;
                    }
                    p {
                        margin: 5px 0;
                    }
                    div {
                        margin-bottom: 20px;
                    }
                    .contributors {
                        display: grid;
                        grid-template-columns: 33% 33% 33%;
                        gap: 10px
                    }
                    .contributor {
                        border: 1px solid #ccc;
                        padding: 10px;
                        margin-bottom: 10px;
                    }
                    .contributor-chart {
                        max-width: 100%;
                        height: auto;
                    }
                    .red {
                        color: indianred;
                    }
                    .green {
                        color: darkolivegreen;
                    }
                    .blue {
                        color: cadetblue;
                    }
                    @media print {
                        .page, .page-break { break-after: page; }
                        .contributors .contributor:nth-child(6n) {
                          page-break-after: always;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="page">
                    <h1>Developer Activity Report</h1>
                    <hr>
                    <h2>Repository</h2>
                    <div>${reportHeaderContent}</div>
                <div>
                <div class="page">
                    <h2>Stats</h2>
                    <div>${repoContributionStatsContent}</div>
                <hr>
                </div>
                <div class="page">
                    <h2>Stats per Author</h2>
                    <div class="contributors">${contributionsContent}</div>
                </div>
            </body>
        </html>
    `;

    return htmlContent;
}

/**
 * Pauses execution for a specified number of milliseconds.
 * @param ms {number} - The number of milliseconds to sleep.
 * @return {Promise<void>}
 */
async function sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Maps GitHub contributions into previous and current periods.
 * @param weeks {number} - Number of weeks for the report.
 * @param contributions {GithubContribution[]} - List of GitHub contributions.
 * @return {{previous: Contribution[], current: Contribution[]}} - Mapped contributions.
 */
function getContributions(
    weeks: number,
    contributions: GithubContribution[]
): {
    previous: Contribution[];
    current: Contribution[];
} {
    const mapContribution = (weeks: number, weekFrom: number, weekTo: number, contribution: GithubContribution): Contribution => {
        const previousWeeks = contribution.weeks.filter((week) => {
            const weekStartDate = new Date(week.w * 1000);
            const currentDate = new Date();
            const diffInWeeks = Math.floor((currentDate.getTime() - weekStartDate.getTime()) / (7 * 24 * 60 * 60 * 1000));
            return diffInWeeks < weekTo + weeks && diffInWeeks >= weekTo;
        });
        const previousTotalCommits = previousWeeks.reduce((sum, week) => sum + week.c, 0);

        const currentWeeks = contribution.weeks.filter((week) => {
            const weekStartDate = new Date(week.w * 1000);
            const currentDate = new Date();
            const diffInWeeks = Math.floor((currentDate.getTime() - weekStartDate.getTime()) / (7 * 24 * 60 * 60 * 1000));
            return diffInWeeks < weekTo && diffInWeeks >= weekFrom;
        });
        const currentTotalCommits = currentWeeks.reduce((sum, week) => sum + week.c, 0);

        const change = calculateChange(previousTotalCommits, currentTotalCommits);

        return {
            weeks: currentWeeks.map((week) => ({
                startDate: new Date(week.w * 1000),
                linesAdded: week.a,
                linesDeleted: week.d,
                commitCount: week.c
            })),
            stats: {
                commitCount: currentTotalCommits,
                change
            },
            author: contribution.author
        };
    };

    return {
        previous: contributions.map((contribution) => mapContribution(weeks, weeks, weeks * 2, contribution)),
        current: contributions.map((contribution) => mapContribution(weeks, 0, weeks, contribution))
    };
}

/**
 * Calculates the percentage change between two values.
 * @param previous {number} - The previous value.
 * @param current {number} - The current value.
 * @return {number} - The percentage change.
 */
function calculateChange(previous: number, current: number): number {
    let change = 0;
    if (current === previous || (current === 0 && previous === 0)) {
        change = 0;
    } else if (previous === 0) {
        change = 100;
    } else if (current === 0) {
        change = -100;
    } else {
        change = current > previous ? (current / previous - 1) * 100 : -1 * (1 - current / previous) * 100;
    }

    return change;
}

/**
 * Fetches GitHub contributions for a repository.
 * @param weeks {number} - Number of weeks for the report.
 * @param owner {string} - Repository owner.
 * @param repo {string} - Repository name.
 * @param token {string} - GitHub token for authentication.
 * @return {Promise<GithubContribution[]>} - List of GitHub contributions.
 */
async function getGithubContributions(weeks: number, owner: string, repo: string, token: string): Promise<GithubContribution[]> {
    try {
        console.log('Fetching GH contribution data');
        const octokit = new Octokit();

        const result = await octokit.request(`GET /repos/{org}/{repo}/stats/contributors`, {
            org: owner,
            repo: repo,
            headers: {
                Authorization: `Bearer ${token}`,
                'X-GitHub-Api-Version': '2022-11-28'
            }
        });

        if (result.status >= 300) {
            throw new Error(`Failure while fetching contributors - response status ${result.status}`);
        }

        // if awaiting for cache buildup, retrigger fetch
        if (result.status === 202) {
            console.log('Awaiting for GH contribution data to be compiled');
            await sleep(1000);
            return getGithubContributions(weeks, owner, repo, token);
        }

        const contributors: GithubContribution[] = result.data;

        if (!contributors.length) {
            throw new Error('No contributors fund');
        }

        return contributors;
    } catch (error) {
        throw new Error(`Failure while fetching contributors: ${(error as Error).message}`);
    }
}

/**
 * Calculates repository contribution statistics.
 * @param weeks {number} - Number of weeks for the report.
 * @param previousContributions {Contribution[]} - Contributions from the previous period.
 * @param currentContributions {Contribution[]} - Contributions from the current period.
 * @return {RepoContributionStats} - Repository contribution statistics.
 */
function getRepoContributionStats(weeks: number, previousContributions: Contribution[], currentContributions: Contribution[]): RepoContributionStats {
    const previousTotalCommits = previousContributions.reduce((sum, contribution) => {
        return sum + contribution.stats.commitCount;
    }, 0);

    const totalLinesAdded = currentContributions.reduce((sum, contribution) => {
        return sum + contribution.weeks.reduce((weekSum, week) => weekSum + week.linesAdded, 0);
    }, 0);

    const totalLinesDeleted = currentContributions.reduce((sum, contribution) => {
        return sum + contribution.weeks.reduce((weekSum, week) => weekSum + week.linesDeleted, 0);
    }, 0);

    const totalCommits = currentContributions.reduce((sum, contribution) => {
        return sum + contribution.stats.commitCount;
    }, 0);

    const change = calculateChange(previousTotalCommits, totalCommits);
    const activeContributors = currentContributions.filter((contribution) => contribution.stats.commitCount > 0).map((contribution) => contribution.author);

    const inactiveContributors = currentContributions.filter((contribution) => contribution.stats.commitCount === 0).map((contribution) => contribution.author);

    return {
        linesAdded: totalLinesAdded,
        linesDeleted: totalLinesDeleted,
        commitCount: totalCommits,
        change,
        activeContributors,
        inactiveContributors
    };
}

/**
 * Generates a PDF file from HTML content.
 * @param htmlContent {string} - The HTML content to convert to PDF.
 * @param path {string} - The file path to save the PDF.
 * @return {Promise<void>}
 */
async function generatePDFFromHtml(htmlContent: string, path: string): Promise<void> {
    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    await page.setContent(htmlContent);
    const pdfBuffer = await page.pdf({ format: 'A4' });
    await browser.close();

    fs.writeFileSync(path, pdfBuffer);
}

/**
 * Generates an HTML page and saves it to a file.
 * @param htmlContent {string} - The HTML content to save.
 * @return {Promise<void>}
 */
async function generateHtmlPage(htmlContent: string): Promise<void> {
    fs.writeFileSync('./page.html', htmlContent);
}

/**
 * Generates a bar chart image as a base64 string.
 * @param title {string} - The title of the chart.
 * @param data {{label:string, value: number}[]} - The data for the chart.
 * @return {Promise<string>} - The base64-encoded chart image.
 */
async function generateBarChartImage(title: string, data: { label: string; value: number }[]): Promise<string> {
    const chartCanvas = new ChartJSNodeCanvas({ width: 800, height: 600 });
    const chartBuffer = await chartCanvas.renderToBuffer({
        type: 'line',
        data: {
            labels: data.map((d) => d.label),
            datasets: [
                {
                    data: data.map((d) => d.value),
                    borderColor: 'rgba(75, 192, 192, 1)',
                    fill: false,
                    cubicInterpolationMode: 'monotone',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                title: { display: true, text: title, font: { size: 30 } },
                legend: { display: false }
            },
            interaction: {
                intersect: false
            },
            scales: {
                x: {
                    display: true,
                    ticks: {
                        font: { size: 25 }
                    }
                },
                y: {
                    display: true,
                    min: 0,
                    ticks: {
                        font: { size: 25 }
                    }
                }
            }
        }
    });

    return chartBuffer.toString('base64');
}

/**
 * Interface for repository contribution statistics.
 */
interface RepoContributionStats {
    linesAdded: number;
    linesDeleted: number;
    commitCount: number;
    change: number; // Change in percentage
    activeContributors: GithubAuthor[]; // List of active contributors
    inactiveContributors: GithubAuthor[]; // List of inactive contributors
}

/**
 * Interface for a contribution.
 */
interface Contribution {
    weeks: { startDate: Date; linesAdded: number; linesDeleted: number; commitCount: number }[];
    stats: {
        commitCount: number; // Total commit count
        change: number; // Change in percentage
    };
    author: GithubAuthor;
}

/**
 * Interface for GitHub contribution data.
 */
interface GithubContribution {
    weeks: { w: number; a: number; d: number; c: number }[];
    total: number;
    author: GithubAuthor;
}

/**
 * Interface for a GitHub author.
 */
interface GithubAuthor {
    login: string;
    id: number;
    node_id: string;
    avatar_url: string;
    gravatar_id: string;
    url: string;
    html_url: string;
    followers_url: string;
    following_url: string;
    gists_url: string;
    starred_url: string;
    subscriptions_url: string;
    organizations_url: string;
    repos_url: string;
    events_url: string;
    received_events_url: string;
    type: string;
    user_view_type: string;
    site_admin: boolean;
}

export { run };
