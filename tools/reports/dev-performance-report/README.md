# Developer Performance Report

This tool generates a detailed developer activity report for a specified GitHub repository. The report includes
statistics such as total lines added, lines deleted, commits, and contributor activity over a specified time period. The
output is a PDF file containing the report.

## Prerequisites

-   Node.js version 20 or higher
-   A GitHub personal access token with the necessary permissions to access repository statistics

## Installation

1. Clone the repository.
2. Navigate to the `tools/reports/dev-performance-report` directory.
3. Install dependencies:

    ```bash
    npm install
    ```

## Configuration

Create a `.env` file in the `tools/reports/dev-performance-report` directory based on the provided `.env.example` file.
Update the values as needed:

```dotenv
# GitHub personal access token used for authentication with the GitHub API
GITHUB_TOKEN=your_github_token

# The owner of the GitHub repository
REPO_OWNER=repository_owner

# The name of the GitHub repository
REPO_NAME=repository_name

# The file path where the generated developer activity report will be saved
REPORT_PATH=./developer-activity-report.pdf

# The number of weeks to include in the developer activity report
WEEKS=8
```

## Usage

To generate the developer activity report, run the following command:

```bash
npm run tool
```

The tool will fetch data from the GitHub API, process it, and generate a PDF report at the location specified in the
`REPORT_PATH` environment variable.

## Output

The generated PDF report includes:

-   Repository details (name, owner, and time period)
-   Overall repository statistics (lines added, lines deleted, commits, and changes compared to the previous period)
-   Detailed statistics for each contributor, including weekly commit activity and changes compared to the previous period
-   Charts visualizing weekly commit activity for each contributor

**Note:** All changes are computed against the previous reporting period. For example, if the `WEEKS` value is set to 8, the tool compares the last 8 weeks to the 8 weeks prior to that (weeks 8-16).

## Notes

-   Ensure the `GITHUB_TOKEN` has sufficient permissions to access the repository's statistics.
-   If the GitHub API requires time to compile contribution data, the tool will retry fetching the data until it is
    available.
-   The tool is designed for development purposes and may include additional debugging logs.
