name: Build affected
description: Build affected packages

inputs:
    base-branch:
        description: 'Base branch'
        required: true
outputs:
    packages:
        description: 'Build package names'
        value: ${{ steps.stats.outputs.packages }}
    count:
        description: 'Package count'
        value: ${{ steps.stats.outputs.count }}

runs:
    using: 'composite'
    steps:
        - run: |
              npx nx affected --base=origin/${{ inputs.base-branch }}~1 --head=origin/${{ inputs.base-branch }} -t build --parallel=3
          shell: bash
        - name: Output build stats
          id: stats
          uses: actions/github-script@v7
          with:
              script: |
                  const {getArtefactDigest} = require('./tools/ci/utils.js');
                  const digest = await getArtefactDigest( './dist/apps');
                  const packages = digest.map(package => package.name);
                  core.setOutput('packages', packages);
                  core.setOutput('count', packages.length);
