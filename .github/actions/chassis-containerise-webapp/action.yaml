name: Containerise service
description: Containerise aah2 service

inputs:
    image-name:
        description: 'Image name'
        required: true
    image-tag:
        description: 'Image tag'
        required: true
    package-path:
        description: 'Path to the package'
        required: true

runs:
    using: 'composite'
    steps:
        - name: Set up QEMU
          uses: docker/setup-qemu-action@v3
        - name: Set up Docker Buildx
          uses: docker/setup-buildx-action@v3
        - shell: bash
          run: cp docker/aah2-webapp/nginx.conf ${{ inputs.package-path }}/nginx.conf
        - name: Build and push
          uses: docker/build-push-action@v6
          with:
              push: true
              file: docker/aah2-webapp/Dockerfile
              tags: ghcr.io/aacom/${{ inputs.image-name }}:${{ inputs.image-tag }}
              context: ${{ inputs.package-path }}
