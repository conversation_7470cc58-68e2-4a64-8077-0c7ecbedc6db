name: Deploy static web app
description: Deploy static web app via SSH

inputs:
    package-name:
        description: 'Package name e.g. app-1'
        required: true
    package-version:
        description: 'Package version e.g. 1.2.3'
        required: true
    ssh-user:
        description: 'User name for SSH connection'
        required: true
    ssh-server-has-sides:
        description: 'Does server we ssh to have separate A/B sides?'
        required: true
    ssh-server-url:
        description: 'URL for SSH connection'
        required: true
    azure-tenant-id:
        description: 'Tenant ID for KV access'
        required: true
    azure-client-id:
        description: 'Client ID for KV access'
        required: true
    azure-client-secret:
        description: 'Secret for KV access'
        required: true
    azure-key-vault-name:
        description: 'KV name for KV access'
        required: true
    azure-key-value-ssh-secret-name:
        description: 'Secret name for for KV access'
        required: true
    github-token:
        description: 'Github token'
        required: true
    force-side:
        description: Should we force deployment to the specific side (if server have sides)
        default: ''

runs:
    using: 'composite'
    steps:
        - uses: ./.github/actions/utils-node-setup
        - uses: ./.github/actions/setup-ssh-connection
          id: setup-ssh-connection
          with:
              azure-tenant-id: ${{ inputs.azure-tenant-id }}
              azure-client-id: ${{ inputs.azure-client-id }}
              azure-key-vault-name: ${{ inputs.azure-key-vault-name }}
              azure-key-value-ssh-secret-name: ${{ inputs.azure-key-value-ssh-secret-name }}
              azure-client-secret: ${{ inputs.azure-client-secret }}
              ssh-user: ${{ inputs.ssh-user }}
              ssh-server-has-sides: ${{ inputs.ssh-server-has-sides }}
              ssh-server-url: ${{ inputs.ssh-server-url }}
        - uses: ./.github/actions/unpack-npm-package
          with:
              package-name: ${{ inputs.package-name }}
              package-version: ${{ inputs.package-version }}
              github-token: ${{ inputs.github-token }}
              ssh-server-has-sides: ${{ inputs.ssh-server-has-sides }}
              ssh-server-url: ${{ inputs.ssh-server-url }}
              environment: ${{ inputs.environment }}
              target-side: ${{ (inputs.force-side || steps.setup-ssh-connection.outputs.inactive-side) }}

        - uses: ./.github/actions/scp-package
          with:
              package-name: ${{ inputs.package-name }}
              package-version: ${{ inputs.package-version }}
              target-side: ${{ (inputs.force-side || steps.setup-ssh-connection.outputs.inactive-side) }}
              ssh-user: ${{ inputs.ssh-user }}
              ssh-server-has-sides: ${{ inputs.ssh-server-has-sides }}
              ssh-server-url: ${{ inputs.ssh-server-url }}
        - name: Compose deployments name
          id: compose-name
          run: |
              PREFIX=
              SUFFIX=
              RESULT=$PREFIX$PACKAGE$SUFFIX
              echo $RESULT
              echo "name=$RESULT" >> "$GITHUB_OUTPUT"
          shell: bash
          env:
              PACKAGE: ${{ inputs.package-name }}
              ADD_BOLT_PREFIX: ${{contains(fromJSON(inputs.bolt-prefix-apps), inputs.package-name)}}
              IS_NO_PREFIX: ${{ contains(fromJSON(inputs.no-prefix-apps), inputs.package-name) }}
        - uses: ./.github/actions/deployment-pre-install-setup
          with:
              package-name: ${{ inputs.package-name }}
              name: ${{ steps.compose-name.outputs.name }}
              ssh-user: ${{ inputs.ssh-user }}
              ssh-server-has-sides: ${{ inputs.ssh-server-has-sides }}
              ssh-server-url: ${{ inputs.ssh-server-url }}
              target-side: ${{ (inputs.force-side || steps.setup-ssh-connection.outputs.inactive-side) }}
        - name: Set app for target environment
          run: |
              ssh -o StrictHostKeyChecking=no -i ./id_rsa.priv $USER@$SERVER "
                [ -f "~/.bashrc" ] && source ~/.bashrc
                echo "Moving temp app to final dest"
                mv ~/deployment-packages/ongoing/$PACKAGE ~/server-modules/$NAME
              "
          shell: bash
          env:
              USER: ${{ format('{0}{1}', inputs.ssh-user, fromJSON(inputs.ssh-server-has-sides) && (inputs.force-side || steps.setup-ssh-connection.outputs.inactive-side) || '') }}
              SERVER: ${{ inputs.ssh-server-url }}
              PACKAGE: ${{ inputs.package-name }}
              NAME: ${{ steps.compose-name.outputs.name }}
        - uses: ./.github/actions/deployment-post-install-setup
          with:
              package-name: ${{ inputs.package-name }}
              ssh-user: ${{ inputs.ssh-user }}
              ssh-server-has-sides: ${{ inputs.ssh-server-has-sides }}
              ssh-server-url: ${{ inputs.ssh-server-url }}
              target-side: ${{ (inputs.force-side || steps.setup-ssh-connection.outputs.inactive-side) }}
