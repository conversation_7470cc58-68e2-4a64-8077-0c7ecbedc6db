name: Containerise service
description: Containerise aah2 service

inputs:
    image-name:
        description: 'Image name'
        required: true
    image-tag:
        description: 'Image tag'
        required: true
    aah-env-version:
        description: 'aah-env base version'
        required: true
    package-path:
        description: 'Path to the package'
        required: true

runs:
    using: 'composite'
    steps:
        - name: Set up QEMU
          uses: docker/setup-qemu-action@v3
        - name: Set up Docker Buildx
          uses: docker/setup-buildx-action@v3
        - name: Build and push
          uses: docker/build-push-action@v6
          with:
              push: true
              file: docker/aah2-service/Dockerfile
              tags: ghcr.io/aacom/${{ inputs.image-name }}:${{ inputs.image-tag }}
              context: ${{ inputs.package-path }}
              build-args: |
                  base_image=ghcr.io/aacom/aah2-env-base:${{ inputs.aah-env-version }}
