name: Trigger the upgrade deployment manifest workflow
description: Calls a workflow in a roadops-deploy repository to upgrade the manifest and create a pull request.

inputs:
    deployRepo:
        description: 'The roadops-deploy repo to trigger the workflow in'
        required: true
        type: string
    services:
        description: 'Key value pairs of updated services and the target version'
        required: true
        type: string
    webapps:
        description: 'Key value pairs of updated webapps and the target version'
        required: true
        type: string

runs:
    using: 'composite'
    steps:
        - name: Trigger Workflow in Another Repository
          run: |
              payload="{\"services\": \"${{ inputs.services }}\", \"webapps\": \"${{ inputs.webapps }}\"}"
              curl -L \
                -X POST \
                -H "Accept: application/vnd.github+json" \
                -H "Authorization: Bearer ${{ github.token }}" \
                -H "X-GitHub-Api-Version: 2022-11-28" \
                https://api.github.com/repos/aacom/${{ inputs.deployRepo }}/dispatches \
                -d "{\"event_type\": \"trigger-workflow\", \"client_payload\": $payload}"
