name: Unpack npm package
description: Unpack npm package before deployment
inputs:
    package-name:
        description: 'Package name e.g. app-1'
        required: true
    package-version:
        description: 'Package version e.g. 1.2.3'
        required: true
    github-token:
        description: 'Github token'
        required: true
runs:
    using: 'composite'
    steps:
        - name: Prepare package
          id: package_digest
          uses: actions/github-script@v7
          with:
              script: |
                  const {OWNER, PACKAGE, VERSION} = process.env;
                  const { shellExec } = require('./tools/ci/utils.js');

                  await shellExec(`npm pack @${OWNER}/${PACKAGE}@${VERSION}`);
          env:
              NODE_AUTH_TOKEN: ${{ inputs.github-token }}
              OWNER: ${{ github.REPOSITORY_OWNER }}
              PACKAGE: ${{ inputs.package-name }}
              VERSION: ${{ inputs.package-version }}
        - name: Post unpack setup
          run: |
              tar zxvf ${OWNER}-${PACKAGE}-${VERSION}.tgz
              mv package ${PACKAGE}
              ls -la ${PACKAGE}
              mv ${PACKAGE}/package-lock.bkp ${PACKAGE}/package-lock.json
              echo "PACKAGE=${PACKAGE}" >> $GITHUB_ENV
          shell: bash
          env:
              OWNER: ${{ github.REPOSITORY_OWNER }}
              PACKAGE: ${{ inputs.package-name }}
              VERSION: ${{ inputs.package-version }}
        - name: Determine deployment side and set SIDE and PORT
          if: >-
              ${{ inputs.package-name == 'aahelp2-web-app' || inputs.package-name == 'legacy-fleet-web-app' }}
          shell: bash
          run: |
              ENV_TYPE=${{ inputs.environment }}
              if [[ "${{ inputs.ssh-server-has-sides }}" == "true" ]]; then
                SIDE=${{ inputs.target-side }}
                if [[ "$SIDE" == "A" ]]; then
                  echo "PORT=5600" >> $GITHUB_ENV
                  echo "SIDENAME=livA" >> $GITHUB_ENV
                elif [[ "$SIDE" == "B" ]]; then
                  echo "PORT=5601" >> $GITHUB_ENV
                  echo "SIDENAME=livB" >> $GITHUB_ENV
                else
                  echo "Unknown side: $SIDE"
                  exit 1
                fi
              else
               if [[ "$ENV_TYPE" == "Integration" ]]; then
              echo "PORT=7100" >> $GITHUB_ENV
              elif [[ "$ENV_TYPE" == "Verification" ]]; then
              echo "PORT=8100" >> $GITHUB_ENV
              elif [[ "$ENV_TYPE" == "Acceptance" ]]; then
              echo "PORT=5600" >> $GITHUB_ENV
              elif [[ "$ENV_TYPE" == "Acceptance-2" ]]; then
              echo "PORT=8100" >> $GITHUB_ENV
              elif [[ "$ENV_TYPE" == "Hotfix" ]]; then
              echo "PORT=5601" >> $GITHUB_ENV
              else
              # Fallback for unknown environment types
              echo "PORT=" >> $GITHUB_ENV
              fi
              echo "SIDENAME=" >> $GITHUB_ENV  # No sides for non-prod environments
              fi
        - name: Transform aahelp2-web-app centurion socket url
          if: "${{ inputs.package-name == 'aahelp2-web-app'}}"
          shell: bash
          run: |
              PACKAGE_DIR="${PACKAGE}"
              PACKAGE_JSON="${PACKAGE_DIR}/package.json"
              INDEX_HTML="${PACKAGE_DIR}/index.html"
              VERSION_HTML="${PACKAGE_DIR}/partials/routes/status/status.template.html"


              # Ensure package.json exists
              if [ ! -f "$PACKAGE_JSON" ]; then
              echo "Error: package.json not found in ${PACKAGE_DIR}"
              exit 1
              fi

              # Read version from package.json
              VERSION=$(jq -r '.version' "$PACKAGE_JSON")
              if [ -z "$VERSION" ]; then
              echo "Error: Version not found in package.json"
              exit 1
              fi

               # Extract build number from version (e.g., "164" from "164.0.27")
               BUILD_NUMBER=$(echo "$VERSION" | cut -d '.' -f 1)

              # Extract base URL from ssh-server-url (remove .theaa.local)
                BASE_URL=$(echo "${{ inputs.ssh-server-url }}" | sed 's/\.theaa\.local$//')

              # Curate URL based on PORT
              if [ -z "$PORT" ]; then
              URL="https://$BASE_URL"
              else
              URL="https://$BASE_URL:$PORT"
              fi

              echo "Curated URL: $URL"
              echo "Package Version: $VERSION"

              if [ -z "$SIDENAME" ]; then
              VERSION_SIDE_FORMAT="${BUILD_NUMBER} ${BASE_URL}-${VERSION}"
              else
              VERSION_SIDE_FORMAT="${BUILD_NUMBER} ${BASE_URL}-${VERSION}-${SIDENAME}"
              fi

                # Ensure VERSION_HTML exists
               if [ ! -f "$VERSION_HTML" ]; then
               echo "Error: VERSION_HTML not found in ${PACKAGE_DIR}"
               exit 1
               fi

               # Update VERSION_HTML with VERSION_SIDE_FORMAT
               sed -i "s|::VERSION::-::ENV::|$VERSION_SIDE_FORMAT|g" "$VERSION_HTML"
               echo "Updated VERSION_HTML with: $VERSION_SIDE_FORMAT" 

              # Ensure index.html exists
              if [ ! -f "$INDEX_HTML" ]; then
              echo "Error: index.html not found in ${PACKAGE_DIR}"
              exit 1
              fi

              # Modify index.html to include the curated URL
              sed -i "s|REPLACE_WITH_PHOENIX_HOST|$URL|g" "$INDEX_HTML"
              echo "Updated index.html with Phoenix Host: $URL"
          env:
              PACKAGE: ${{ env.PACKAGE }}
              PORT: ${{ env.PORT }}
              SIDENAME: ${{ env.SIDENAME }}

        - name: Transform legacy-fleet-web-app bolt socket url
          if: "${{ inputs.package-name == 'legacy-fleet-web-app'}}"
          shell: bash
          run: |
              PACKAGE_DIR="${PACKAGE}"
              MAIN_JS="${PACKAGE_DIR}/main.js"

              # Extract base URL from ssh-server-url (remove .theaa.local)
               BASE_URL=$(echo "${{ inputs.ssh-server-url }}" | sed 's/\.theaa\.local$//')

              # Curate URL based on PORT
              if [ -z "$PORT" ]; then
              URL="https://$BASE_URL"
              else
              URL="https://$BASE_URL:$PORT"
              fi

              echo "Curated URL: $URL"

              # Ensure main.js exists
              if [ ! -f "$MAIN_JS" ]; then
              echo "Error: main.js not found in ${PACKAGE_DIR}"
              exit 1
              fi

              # Modify main.js to include the curated URL
              sed -i "s|::FLEET_WEBSOCKET_HOST::|$URL|g" "$MAIN_JS"
              echo "Updated main.js with BOLT SOCKET Host: $URL"
          env:
              PACKAGE: ${{ env.PACKAGE }}
              PORT: ${{ env.PORT }}
              SIDENAME: ${{ env.SIDENAME }}
