name: Containerise

on:
    workflow_call:
        inputs:
            packages:
                description: Names of released packages (artefacts should be available to download)
                type: string
                required: true

concurrency:
    group: '${{ github.workflow }}-containerise'
    cancel-in-progress: false

env:
    aah_env_version: 1.1.0-15-gdfa46c2-feature-CHAS-2

jobs:
    prepare:
        runs-on: ripper-light-rootf-nodejs
        if: ${{ inputs.packages != '[]' && inputs.packages != '' }}
        outputs:
            version: ${{ steps.version.outputs.version }}
            aah_env_version: ${{ env.aah_env_version }}
        steps:
            - name: Checkout Code
              uses: actions/checkout@v3
              with:
                  ref: ${{ github.head_ref }}
                  fetch-depth: 0
            - name: Sanitise branch
              if: startsWith(github.ref, 'refs/tags/v') == false
              id: branches
              uses: transferwise/sanitize-branch-name@v1
            - name: Store version
              id: version
              shell: bash
              run: |
                  git config --global --add safe.directory '*'
                  ref=${{ github.ref }}
                  version=$(git describe 2> /dev/null || echo v0.0.0-$(git describe --always))
                  if [[ $ref == feature/CHAS-* ]]; then
                      version=$version-${{ steps.branches.outputs.sanitized-branch-name }}
                  fi
                  echo version=${version:1} >> "$GITHUB_OUTPUT"

    upload_artifacts:
        name: Prepare artifact for Docker
        if: ${{ inputs.packages != '[]' && inputs.packages != '' }}
        runs-on: ripper-light-rootf-nodejs
        needs: [prepare]
        timeout-minutes: 60
        strategy:
            fail-fast: false
            max-parallel: 50
            matrix:
                package: ${{ fromJSON(inputs.packages) }}
        steps:
            - name: Checkout Code
              uses: actions/checkout@v3
              with:
                  ref: ${{ github.head_ref }}
                  fetch-depth: 0
            - uses: ./.github/actions/utils-node-setup
            - uses: ./.github/actions/restore-local-cache
              id: restore-build-artefact
              with:
                  repo: build-artefacts/run-${{ github.run_id }}
                  path: ./artefacts
                  key: ${{ matrix.package }}
            - uses: actions/upload-artifact@v4
              with:
                  name: docker-context-${{ matrix.package }}
                  path: artefacts

    build_image:
        name: Build image ${{ matrix.package }}
        if: ${{ inputs.packages != '[]' && inputs.packages != '' }}
        runs-on: ubuntu-latest
        needs: [prepare, upload_artifacts]
        timeout-minutes: 60
        strategy:
            fail-fast: false
            max-parallel: 50
            matrix:
                package: ${{ fromJSON(inputs.packages) }}
        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 0
            - uses: actions/download-artifact@v4
              with:
                  name: docker-context-${{ matrix.package }}
                  path: artefacts
            - name: Login to GitHub Container Registry
              uses: docker/login-action@v3
              with:
                  registry: ghcr.io
                  username: ${{ github.actor }}
                  password: ${{ secrets.GITHUB_TOKEN }}

            - if: ${{ contains(fromJSON(vars.BACKEND_APPS), matrix.package) }}
              uses: ./.github/actions/chassis-containerise-service
              with:
                  image-name: ${{ matrix.package }}-docker
                  image-tag: ${{ needs.prepare.outputs.version }}
                  aah-env-version: ${{ needs.prepare.outputs.aah_env_version }}
                  package-path: ${{ format('artefacts/{0}', matrix.package ) }}

            - if: ${{ contains(fromJSON(vars.LEGACY_APPS), matrix.package) }}
              uses: ./.github/actions/chassis-containerise-service
              with:
                  image-name: ${{ matrix.package }}-docker
                  image-tag: ${{ needs.prepare.outputs.version }}
                  aah-env-version: ${{ needs.prepare.outputs.aah_env_version }}
                  package-path: ${{ format('artefacts/{0}', matrix.package ) }}
            - if: ${{ contains(fromJSON(vars.STATIC_WEB_APPS), matrix.package) }}
              uses: ./.github/actions/chassis-containerise-webapp
              with:
                  image-name: ${{ matrix.package }}-docker
                  image-tag: ${{ needs.prepare.outputs.version }}
                  package-path: ${{ format('artefacts/{0}', matrix.package ) }}
