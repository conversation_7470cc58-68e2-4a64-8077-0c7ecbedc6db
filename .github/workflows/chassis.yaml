name: <PERSON>ssis Project
on:
    push:
        branches:
            - feature/CHAS-*

concurrency:
    group: ${{ github.workflow }}
    cancel-in-progress: false

jobs:
    verify:
        name: Verify
        uses: ./.github/workflows/chassis-verification.yaml
        with:
            baseBranch: ${{ github.REF_NAME }}
    containerise:
        name: Containeri<PERSON>
        needs: [verify]
        if: ${{ needs.verify.outputs.build-count > 0 }}
        uses: ./.github/workflows/chassis-containerise.yaml
        with:
            packages: ${{ needs.verify.outputs.build-packages }}
    clean:
        name: Clean after verify
        runs-on: ripper-light-rootf-nodejs
        needs: [verify, containerise]
        if: ${{ needs.verify.outputs.build-count > 0 }}
        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 0
            - uses: ./.github/actions/remove-local-cache-dir
              with:
                  repo: build-artefacts/run-${{ github.run_id }}
