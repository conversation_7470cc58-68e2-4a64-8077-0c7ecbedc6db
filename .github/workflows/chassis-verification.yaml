name: Verify
on:
    workflow_call:
        inputs:
            baseBranch:
                description: 'Base branch used for evaluating affected modules by changes in current branch'
                required: false
                type: string
                default: 'master'
        outputs:
            build-packages:
                description: 'Build package names'
                value: ${{ jobs.build.outputs.packages }}
            build-count:
                description: 'Package count'
                value: ${{ jobs.build.outputs.count }}

concurrency:
    group: '${{ github.workflow }}-verification @ ${{ github.event.pull_request.head.label || github.head_ref || github.ref }}'
    # Cancel any build if new changes arrived except for release and master branch
    cancel-in-progress: ${{ !contains(github.ref, 'release/') && !(github.ref == format('refs/heads/{0}', 'master'))}}

jobs:
    verify:
        runs-on: ripper-light-rootf-nodejs
        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 0
            - uses: ./.github/actions/utils-node-setup
            - name: Complies with branch rules
              if: ${{ github.EVENT_NAME == 'pull_request'}}
              uses: ./.github/actions/compliance-check
              id: compliance-check
              with:
                  event: ${{ github.EVENT_NAME }}
                  branch: ${{ github.REF_NAME }}
                  from-branch: ${{ github.HEAD_REF }}
                  to-branch: ${{ github.BASE_REF }}
    lint:
        runs-on: ripper-heavy-rootf-nodejs
        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 0
            - uses: ./.github/actions/node-setup
              with:
                  baseBranch: ${{ inputs.baseBranch || github.BASE_REF || github.REF_NAME }}
            - name: Lints without problems
              uses: ./.github/actions/lint-affected
              id: lint-affected
    test:
        runs-on: ripper-heavy-rootf-nodejs
        needs: [verify, lint]
        strategy:
            fail-fast: true
            max-parallel: 5
            matrix:
                shard: [1, 2, 3, 4, 5]
        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 0
            - uses: ./.github/actions/node-setup
              with:
                  baseBranch: ${{ inputs.baseBranch || github.BASE_REF || github.REF_NAME }}
            - name: Tests passed
              uses: ./.github/actions/test-affected
              with:
                  shard: ${{ matrix.shard }}/${{ strategy.job-total }}
              id: test-affected
    build:
        runs-on: ripper-heavy-rootf-nodejs
        needs: [verify, lint]
        outputs:
            packages: ${{ steps.build-affected.outputs.packages }}
            count: ${{ steps.build-affected.outputs.count }}
        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 0
            - uses: ./.github/actions/node-setup
              with:
                  baseBranch: ${{ inputs.baseBranch || github.BASE_REF || github.REF_NAME }}
            - name: Builds without problems
              uses: ./.github/actions/chassis-build-affected
              id: build-affected
              with:
                  base-branch: ${{ inputs.baseBranch }}
            - name: Archive build artifacts
              if: ${{ steps.build-affected.outputs.count > 0 && github.EVENT_NAME != 'pull_request' }}
              uses: ./.github/actions/persist-local-cache-dir
              with:
                  repo: build-artefacts/run-${{ github.run_id }}
                  path: ./dist/apps
