name: Deploy
run-name: Deploy to ${{ inputs.environment }} by @${{ github.actor }}

on:
    workflow_call:
        inputs:
            packages:
                description: Array of package definitions e.g. [{"name":"app-1","version":"1.2.3"},{"name":"app-2","version":"1.2.3"}]
                type: string
                required: true
            environment:
                description: 'Environment to deploy to'
                type: string
                required: true
            forceSide:
                description: Should we force deployment to the specific side (if server have sides)
                type: string
                default: ''
    workflow_dispatch:
        inputs:
            packages:
                description: Array of package definitions e.g. [{"name":"app-1","version":"1.2.3"},{"name":"app-2","version":"1.2.3"}]
                type: string
                required: true
            environment:
                description: 'Environment to deploy to'
                type: choice
                options:
                    - Integration
                    - Verification
                    - Acceptance
                    - Acceptance-2
                    - Training
                    - Hotfix
                    - Pre-production
                    - Production
                required: true
            forceSide:
                description: Should we force deployment to the specific side (if server have sides)
                type: string
                default: ''
permissions:
    id-token: write
    contents: read

jobs:
    review:
        runs-on: ripper-light-nodejs
        if: ${{ inputs.packages != '[]' && inputs.packages != '' }}
        environment: ${{ inputs.environment }}
        outputs:
            sub-environments: ${{ steps.get-sub-manifest.outputs.sub-environments }}
        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 0
            - uses: ./.github/actions/utils-node-setup
            - uses: ./.github/actions/get-sub-environments
              id: get-sub-manifest
              with:
                  environment: ${{ inputs.environment }}
                  servers: ${{ vars.SERVERS }}
    deploy:
        name: Deploy ${{ matrix.package.name }}@${{ matrix.package.version }}
        runs-on: ripper-light-nodejs
        timeout-minutes: 60
        needs: review
        if: ${{ inputs.packages != '[]' && inputs.packages != '' && needs.review.outputs.sub-environments != '[]' && needs.review.outputs.sub-environments != '' }}
        environment: ${{ matrix.sub-environment }}
        concurrency:
            group: deploy-${{ matrix.package.name }}-${{ matrix.sub-environment }}
            cancel-in-progress: false
        strategy:
            fail-fast: false
            max-parallel: 25
            matrix:
                package: ${{ fromJSON(inputs.packages) }}
                sub-environment: ${{ fromJSON(needs.review.outputs.sub-environments) }}
        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 0
            - if: ${{ contains(fromJSON(vars.BACKEND_APPS), matrix.package.name) }}
              uses: ./.github/actions/deploy-backend-app
              with:
                  package-name: ${{ matrix.package.name }}
                  package-version: ${{ matrix.package.version }}
                  bolt-prefix-apps: ${{ vars.BOLT_PREFIX_APPS }}
                  no-prefix-apps: ${{ vars.NO_PREFIX_APPS }}
                  ssh-user: ${{ vars.SSH_USER }}
                  ssh-server-has-sides: ${{ vars.SSH_SERVER_HAS_SIDES }}
                  ssh-server-url: ${{ vars.SSH_SERVER_URL }}
                  azure-tenant-id: ${{ vars.AZURE_TENANT_ID }}
                  azure-client-id: ${{ vars.AZURE_CLIENT_ID }}
                  azure-key-vault-name: ${{ vars.AZURE_KEY_VAULT_NAME }}
                  azure-key-value-ssh-secret-name: ${{ vars.AZURE_KEY_VALUE_SSH_SECRET_NAME }}
                  azure-client-secret: ${{ secrets.AZURE_CLIENT_SECRET }}
                  github-token: ${{ secrets.GITHUB_TOKEN }}
                  force-side: ${{ inputs.forceSide }}
            - if: ${{ contains(fromJSON(vars.CRON_APPS), matrix.package.name) }}
              uses: ./.github/actions/deploy-cron-app
              with:
                  package-name: ${{ matrix.package.name }}
                  package-version: ${{ matrix.package.version }}
                  bolt-prefix-apps: ${{ vars.BOLT_PREFIX_APPS }}
                  no-prefix-apps: ${{ vars.NO_PREFIX_APPS }}
                  ssh-user: ${{ vars.SSH_USER }}
                  ssh-server-has-sides: ${{ vars.SSH_SERVER_HAS_SIDES }}
                  ssh-server-url: ${{ vars.SSH_SERVER_URL }}
                  azure-tenant-id: ${{ vars.AZURE_TENANT_ID }}
                  azure-client-id: ${{ vars.AZURE_CLIENT_ID }}
                  azure-key-vault-name: ${{ vars.AZURE_KEY_VAULT_NAME }}
                  azure-key-value-ssh-secret-name: ${{ vars.AZURE_KEY_VALUE_SSH_SECRET_NAME }}
                  azure-client-secret: ${{ secrets.AZURE_CLIENT_SECRET }}
                  github-token: ${{ secrets.GITHUB_TOKEN }}
                  force-side: ${{ inputs.forceSide }}
            - if: ${{ contains(fromJSON(vars.STATIC_WEB_APPS), matrix.package.name) }}
              uses: ./.github/actions/deploy-static-web-app
              with:
                  package-name: ${{ matrix.package.name }}
                  package-version: ${{ matrix.package.version }}
                  bolt-prefix-apps: ${{ vars.BOLT_PREFIX_APPS }}
                  no-prefix-apps: ${{ vars.NO_PREFIX_APPS }}
                  ssh-user: ${{ vars.SSH_USER }}
                  ssh-server-has-sides: ${{ vars.SSH_SERVER_HAS_SIDES }}
                  ssh-server-url: ${{ vars.SSH_SERVER_URL }}
                  azure-tenant-id: ${{ vars.AZURE_TENANT_ID }}
                  azure-client-id: ${{ vars.AZURE_CLIENT_ID }}
                  azure-key-vault-name: ${{ vars.AZURE_KEY_VAULT_NAME }}
                  azure-key-value-ssh-secret-name: ${{ vars.AZURE_KEY_VALUE_SSH_SECRET_NAME }}
                  azure-client-secret: ${{ secrets.AZURE_CLIENT_SECRET }}
                  github-token: ${{ secrets.GITHUB_TOKEN }}
                  environment: ${{ inputs.environment }}
                  force-side: ${{ inputs.forceSide }}
            - if: ${{ contains(fromJSON(vars.AZURE_WEB_APPS), matrix.package.name) }}
              uses: ./.github/actions/deploy-azure-web-app
              with:
                  package-name: ${{ matrix.package.name }}
                  package-version: ${{ matrix.package.version }}
                  github-token: ${{ secrets.GITHUB_TOKEN }}
                  azure-web-app-client-id: ${{ vars.AZURE_WEB_APP_CLIENT_ID }}
                  azure-web-app-client-secret: ${{ secrets.AZURE_WEB_APP_CLIENT_SECRET }}
                  azure-web-app-tenant-id: ${{ vars.AZURE_WEB_APP_TENANT_ID }}
                  azure-web-app-subscription-id: ${{ vars.AZURE_WEB_APP_SUBSCRIPTION_ID }}
                  azure-web-app-name: ${{ vars.AZURE_WEB_APP_NAME }}
                  azure-resource-group-name: ${{ vars.AZURE_RESOURCE_GROUP_NAME }}
                  environment: ${{ inputs.environment }}
            - if: ${{ contains(fromJSON(vars.LEGACY_APPS), matrix.package.name) }}
              uses: ./.github/actions/deploy-legacy-app
              with:
                  package-name: ${{ matrix.package.name }}
                  package-version: ${{ matrix.package.version }}
                  bolt-prefix-apps: ${{ vars.BOLT_PREFIX_APPS }}
                  no-prefix-apps: ${{ vars.NO_PREFIX_APPS }}
                  ssh-user: ${{ vars.SSH_USER }}
                  ssh-server-has-sides: ${{ vars.SSH_SERVER_HAS_SIDES }}
                  ssh-server-url: ${{ vars.SSH_SERVER_URL }}
                  azure-tenant-id: ${{ vars.AZURE_TENANT_ID }}
                  azure-client-id: ${{ vars.AZURE_CLIENT_ID }}
                  azure-key-vault-name: ${{ vars.AZURE_KEY_VAULT_NAME }}
                  azure-key-value-ssh-secret-name: ${{ vars.AZURE_KEY_VALUE_SSH_SECRET_NAME }}
                  azure-client-secret: ${{ secrets.AZURE_CLIENT_SECRET }}
                  github-token: ${{ secrets.GITHUB_TOKEN }}
                  force-side: ${{ inputs.forceSide }}

    # TODO: we need to be careful because for live it will be multiple servers where we deploy to unless we allow only 1 sub env for manual deploy
    finalise:
        name: Finalise deployment
        runs-on: ripper-light-nodejs
        needs: [review, deploy]
        if: ${{ inputs.packages != '[]' && inputs.packages != '' && needs.review.outputs.sub-environments != '[]' && needs.review.outputs.sub-environments != '' }}
        environment: ${{ matrix.sub-environment }}
        concurrency:
            group: finalise-deploy-${{ matrix.sub-environment }}
            cancel-in-progress: false
        strategy:
            fail-fast: false
            max-parallel: 25
            matrix:
                sub-environment: ${{ fromJSON(needs.review.outputs.sub-environments) }}
        steps:
            - uses: actions/checkout@v4
            - if: ${{ !contains(fromJSON(vars.AZURE_WEB_APPS), matrix.package.name) }}
              uses: ./.github/actions/deployment-finalise
              name: Finalising deployment for ${{ matrix.sub-environment }}
              with:
                  ssh-user: ${{ vars.SSH_USER }}
                  ssh-server-has-sides: ${{ vars.SSH_SERVER_HAS_SIDES }}
                  ssh-server-url: ${{ vars.SSH_SERVER_URL }}
                  azure-tenant-id: ${{ vars.AZURE_TENANT_ID }}
                  azure-client-id: ${{ vars.AZURE_CLIENT_ID }}
                  azure-key-vault-name: ${{ vars.AZURE_KEY_VAULT_NAME }}
                  azure-key-value-ssh-secret-name: ${{ vars.AZURE_KEY_VALUE_SSH_SECRET_NAME }}
                  azure-client-secret: ${{ secrets.AZURE_CLIENT_SECRET }}
                  force-side: ${{ inputs.forceSide }}
            - name: Finalize Azure deployment
              if: ${{ contains(fromJSON(vars.AZURE_WEB_APPS), matrix.package.name) }}
              run: echo "Finalizing Azure deployment for ${{ matrix.package.name }} in ${{ matrix.sub-environment }} environment."
# TODO: so we can recover from failure, then runs undo using deployment-packages/previous
# TODO: recover from failure, then runs undo
# TODO: on failure of deployment undo, also remove last deployment
#  delete_github_deployments:
#    # runs-on: ripper-light-nodejs
#    needs: deploy
#    if: ${{ always() }}
#    steps:
#      - name: Delete Previous deployments
#        uses: actions/github-script@v7
#        env:
#          GITHUB_SHA_HEAD: ${{ github.event.pull_request.head.sha }}
#        with:
#          script: |
#            const { GITHUB_SHA_HEAD } = process.env
#            const deployments = await github.rest.repos.listDeployments({
#              owner: context.repo.owner,
#              repo: context.repo.repo,
#              sha: GITHUB_SHA_HEAD
#            });
#            await Promise.all(
#              deployments.data.map(async (deployment) => {
#                await github.rest.repos.createDeploymentStatus({
#                  owner: context.repo.owner,
#                  repo: context.repo.repo,
#                  deployment_id: deployment.id,
#                  state: 'inactive'
#                });
#                return github.rest.repos.deleteDeployment({
#                  owner: context.repo.owner,
#                  repo: context.repo.repo,
#                  deployment_id: deployment.id
#                });
#              })
#            );
