import { Cache } from '@aa/cache';
import { ErrorResponse } from '@aa/connector';
import { PaginationQuery, PaginationQueryResult, UserProfile, UserType } from '@aa/data-models/common';
import { AuthStore, DataStoreProviderType, LdapDataProvider, MongodbDataProvider } from '@aa/data-store';
import { Diff } from '@aa/diff';
import { EventCode, Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Interval } from '@aa/interval';
import { Microservice } from '@aa/microservice';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment, Utils } from '@aa/utils';
import { Request, Response } from 'express';

export class App extends Microservice {
    public name = 'User';
    public application = BackendApplication.USER;
    protected cache = new Cache<UserProfile>({ lifespan: '2h' });
    protected interval = new Interval();
    protected store: AuthStore;
    // TODO: convert to RemoteCron
    protected cron = new Interval();
    protected mongo: MongodbDataProvider;
    protected ldap: LdapDataProvider;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName: 'user',
            dataStoreProviders: [DataStoreProviderType.REDIS, DataStoreProviderType.MONGODB, DataStoreProviderType.ORACLE, DataStoreProviderType.LDAP]
        });

        this.store = new AuthStore({
            logger: this.logger,
            dataStore: this.dataStore
        });
        // fetch initial data
        this.hydrate().catch((error) => {
            throw new Exception({
                message: 'Failed while hydrating cache for the first time',
                error
            });
        });

        // refresh cache every day
        this.interval.set(this.hydrate, '1h');

        this.server.post('/users', this.getUsers);
        this.server.post('/user', this.getUser);

        // Periodical sync of all user profiles from mongo with data from CSH
        this.cron.set(this.syncUserProfiles, '3h');

        this.mongo = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
        this.ldap = this.dataStore.getProvider(DataStoreProviderType.LDAP);
    }

    /**
     * Hydrate cache with user data
     * @return {Promise<void>}
     * @protected
     */
    protected hydrate = async () => {
        this.logger.log({
            sourceName: this.name,
            message: 'Cache hydration started'
        });

        try {
            // flush old records
            await this.cache.clear();
            let moreRecords = true;
            let chunksFetched = 0;

            // get all operator ids in the system
            const collection = await this.mongo.collection<UserProfile>('system-config', 'users');
            const allUserProfiles = await collection.find({}, { projection: { operatorId: 1 } }).toArray();
            const allOperatorIds = allUserProfiles.map((user) => {
                return user.operatorId;
            });

            // get all user details in chunks (quite a lot of them)
            while (moreRecords) {
                const query = { limit: 500, skip: chunksFetched * 500, operatorIds: allOperatorIds };
                // prime cache by simulating query for all records
                const { more } = await this.getUserProfiles(query);
                moreRecords = more;

                chunksFetched++;
                this.logger.debug(`Hydrated chunk ${chunksFetched}`);
                await Utils.sleep(1000);
            }

            const { currentCount, currentSize } = await this.cache.stats();
            const sizeInMB = Math.ceil(currentSize / (1024 * 1024));
            this.logger.log({
                sourceName: this.name,
                message: `Cache hydration finished with ${currentCount} records (${sizeInMB}mb in size)`
            });
        } catch (error) {
            throw new Exception({
                message: 'Failed while hydrating cache',
                error
            });
        }
    };

    protected getUsers = async (req: Request, res: Response) => {
        try {
            const {
                operatorIds,
                limit = 1000,
                skip = 0
            } = req.body as {
                operatorIds: number[];
            } & PaginationQuery;

            if (limit && limit > 1000) {
                const response = `Unable to fetch more than ${1000} profiles at a time`;
                return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
            }

            if (!operatorIds || (operatorIds && !operatorIds.length)) {
                const response = `Missing list of operator ids`;
                return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
            }

            const result = await this.getUserProfiles({
                operatorIds,
                limit,
                skip
            });

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Req failure when retrieving user'
            });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    protected async getOperatorIdFromLDAP(userName: string): Promise<number | undefined> {
        const result = await this.ldap.searchUser(userName);
        return result ? result.operatorId : undefined;
    }

    protected getUser = async (req: Request, res: Response) => {
        try {
            const { userName } = req.body as { operatorId?: number; userName?: string };
            let { operatorId } = req.body as { operatorId?: number; userName?: string };

            if (typeof operatorId !== 'number' && !userName) {
                const response = `Missing operator id or user name`;
                return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
            }

            // if no operator id lets use ldap to fallback for user operator id retrieval
            if (typeof operatorId !== 'number' && userName) {
                operatorId = await this.getOperatorIdFromLDAP(userName);
            }

            if (typeof operatorId !== 'number') {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            let result = await this.getUserProfiles({
                operatorIds: [operatorId],
                limit: 1,
                skip: 0
            });

            if (!result.results.length) {
                // if no results for operator id and no username at this point, we have to bail out
                if (!userName) {
                    return getResponse(res, ServerResponseCode.NOT_FOUND);
                }
                // else we can try as a last resort to call ldap to get alternative
                // user operator id as the initial was incorrect
                operatorId = await this.getOperatorIdFromLDAP(userName);
            }

            if (typeof operatorId !== 'number') {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            result = await this.getUserProfiles({
                operatorIds: [operatorId],
                limit: 1,
                skip: 0
            });

            if (!result.results.length) {
                // as a last resort lets create an ldap fallback user profile
                if (!userName) {
                    return getResponse(res, ServerResponseCode.NOT_FOUND);
                }
                // lets get system user from LDAP and create fallback user profile
                const { results: systemUsers } = await this.getLDAPSystemUsers({
                    userNames: [userName],
                    limit: 1,
                    skip: 0
                });

                if (!systemUsers.length) {
                    return getResponse(res, ServerResponseCode.NOT_FOUND);
                }
                const systemUser = { ...systemUsers[0], teams: [], avatar: undefined, appConfigs: {} };

                const collection = await this.mongo.collection<UserProfile>('system-config', 'users');

                await collection.insertOne(systemUser);
                result.results.push(systemUser);
            }

            return getResponse(res, ServerResponseCode.OK, result.results[0]);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Req failure when retrieving user'
            });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    /**
     * Get profiles form cache and DB
     * @param {{operatorIds?: number[] | undefined} & Required<PaginationQuery>} query
     * @return {Promise<PaginationQueryResult<UserProfile>>}
     * @protected
     */
    protected async getUserProfiles(
        query: {
            operatorIds: number[];
        } & Required<PaginationQuery>
    ): Promise<PaginationQueryResult<UserProfile>> {
        const { operatorIds = [], limit, skip } = query;
        // if we are looking for specific entries, they can come either from cache or DB
        const more = skip + limit < operatorIds.length;
        const targetOperatorIds = operatorIds.slice(skip, skip + limit);
        const notCachedIds: number[] = [];
        const results: UserProfile[] = [];

        // if some records not found in the cache we will fetch them from mongodb
        for (const targetOperatorId of targetOperatorIds) {
            const has = await this.cache.has(targetOperatorId);
            if (!has) {
                notCachedIds.push(targetOperatorId);
            }
        }

        // get not cached results
        if (notCachedIds.length) {
            // as we operate on slice that considers pagination, no need to set limits
            const collection = await this.mongo.collection<UserProfile>('system-config', 'users');

            const userProfilesWithId = await collection
                .find({
                    operatorId: { $in: notCachedIds }
                })
                .toArray();

            const userProfiles: UserProfile[] = userProfilesWithId.map(({ _id, ...profile }) => profile);

            if (notCachedIds.length !== userProfiles.length) {
                this.logger.warn({
                    sourceName: this.name,
                    message: 'Not all records found for user query',
                    data: { notCachedIds }
                });
            }

            // let's feed records to the cache
            for (const userProfile of userProfiles) {
                await this.cache.set(userProfile.operatorId, userProfile);
            }
        }

        // now we can get all results from cache
        for (const targetOperatorId of targetOperatorIds) {
            const entry = await this.cache.get(targetOperatorId);
            if (entry) {
                results.push(entry);
            }
        }

        return { more, results };
    }

    protected syncUserProfiles = async () => {
        this.logger.log('Sync of user profiles started');
        try {
            let moreRecords = true;
            let chunksFetched = 0;

            // get all user details in chunks (quite a lot of them)
            while (moreRecords) {
                const query = { limit: 500, skip: chunksFetched * 500 };
                const { more, results } = await this.composeProfiles(query);
                moreRecords = more;

                // preserve in cache
                for (const result of results) {
                    await this.cache.set(result.operatorId, result);
                }

                chunksFetched++;
                this.logger.debug(`Synced chunk ${chunksFetched}`);
                await Utils.sleep(1000);
            }

            // flush old records
            await this.cache.clear();

            // trigger prune to remove all old profiles that does not match active system users
            // TODO: enable when implemented
            await this.pruneUserProfiles();
        } catch (error) {
            this.logger.error({
                error,
                message: `Failure while syncing user profiles`
            });
        }
        this.logger.log('Sync of user profiles finished');
    };

    /**
     * Remove userprofiles for inactive system users
     * @return {Promise<void>}
     * @protected
     */
    protected async pruneUserProfiles() {
        this.logger.log({
            sourceName: this.name,
            message: 'Pruning user profile started'
        });

        try {
            let moreRecords = true;
            let chunksFetched = 0;
            let deletedCount = 0;

            // get all user details in chunks (quite a lot of them)
            while (moreRecords) {
                const query = { limit: 10, skip: chunksFetched * 10 };
                const { more, results } = await this.pruneProfiles(query);
                deletedCount += results[0] ? results[0].deletedCount : 0;
                moreRecords = more;

                chunksFetched++;
                await Utils.sleep(1000);
            }

            this.logger.log({
                sourceName: this.name,
                message: `Pruning user profile finished with ${deletedCount} records removed`
            });
        } catch (error) {
            throw new Exception({
                message: 'Failed while pruning user profiles',
                error
            });
        }
    }

    protected async pruneProfiles(query: PaginationQuery): Promise<PaginationQueryResult<{ deletedCount: number }>> {
        try {
            // TODO: implement with RBAUAA-9131
            // TODO: we need to make sure to not delete user profiles that has been introduced by LDAP fallback
            //  every deleted user needs to be checked against ldap - if its there the bail out
            return { results: [{ deletedCount: 0 }], more: false };
        } catch (error) {
            this.logger.error({
                message: 'Failure while composing user profiles',
                error: new Error(`Failure while composing user profiles: ${(error as Error).message}`),
                data: { query }
            });
            return { more: false, results: [] };
        }
    }

    /**
     * Checks if CSH system user changed compared to the user stored in Mongo,
     * if so updates mongo, returns updated user
     * @param {{operatorIds?: number[]} & Required<PaginationQuery>} query
     * @return {Promise<PaginationQueryResult<UserProfile>>}
     * @protected
     */
    protected async composeProfiles(
        query: {
            operatorIds?: number[];
        } & Required<PaginationQuery>
    ): Promise<PaginationQueryResult<UserProfile>> {
        try {
            const { limit, skip } = query;
            // CSH is the master of user records
            const { results: systemUsers, more } = await this.getCSHSystemUsers({
                operatorIds: query.operatorIds,
                skip,
                limit
            });

            if (!systemUsers.length) {
                return { results: [], more: false };
            }

            const systemUserOperatorIds = systemUsers.map((user) => user.operatorId);
            const operatorIds = query.operatorIds ? query.operatorIds : systemUserOperatorIds;
            const collection = await this.mongo.collection<UserProfile>('system-config', 'users');

            const userProfilesWithId = await collection
                .find({
                    operatorId: { $in: operatorIds }
                })
                .toArray();

            const userProfiles: UserProfile[] = userProfilesWithId.map(({ _id, ...profile }) => profile);

            // for each system user do a diff versus current user profile
            const newUserProfiles: UserProfile[] = [];
            let updatedCount = 0;
            for (const systemUser of systemUsers) {
                const index = userProfiles.findIndex((profile) => {
                    return profile.operatorId === systemUser.operatorId;
                });

                // if no user profile, create new one based on system user
                if (index === -1) {
                    newUserProfiles.push({
                        ...systemUser,
                        teams: [],
                        avatar: undefined,
                        appConfigs: {}
                    });
                } else {
                    // check if anything updated, if so update record
                    const userProfile = userProfiles[index];
                    const diff = new Diff({
                        current: systemUser,
                        previous: userProfile
                    });
                    const updated = diff.updated();

                    if (updated.isChange) {
                        await collection.updateOne({ operatorId: systemUser.operatorId }, { $set: systemUser });
                        updatedCount++;
                    }

                    userProfiles[index] = {
                        ...userProfile,
                        ...systemUser
                    };
                }
            }
            if (updatedCount) {
                this.logger.debug(`Updated ${updatedCount} existing user profiles`);
            }

            if (newUserProfiles.length) {
                try {
                    // add new user profiles
                    await collection.insertMany(newUserProfiles);
                    this.logger.debug(`Added ${newUserProfiles.length} new user profiles`);
                } catch (error) {
                    throw new Error(`Failure while deleting user profiles for non-existent system users: ${(error as Error).message}`);
                }
            }

            return {
                more,
                results: [...userProfiles, ...newUserProfiles]
            };
        } catch (error) {
            this.logger.error({
                message: 'Failure while composing user profiles',
                error: new Error(`Failure while composing user profiles: ${(error as Error).message}`),
                data: { query }
            });
            return { more: false, results: [] };
        }
    }

    /**
     * Get user public details from CSH, max 1000 records at a time, where operator id refer to:
     *  - op_resource_id in the Operator table
     *  - patrol_resource_id in the Patrol table
     *
     * @param {{operatorIds?: number[]} & PaginationQuery} query  If no op id or staff no provided, fetch all records
     * @return {Promise<PaginationQueryResult<SystemUser>>}
     */
    protected async getCSHSystemUsers(
        query: {
            operatorIds?: number[];
        } & Required<PaginationQuery>
    ): Promise<PaginationQueryResult<SystemUser>> {
        // TODO: use user api to get details about users
        const { operatorIds, limit, skip } = query;

        if (limit > 1000) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                message: `Unable to fetch more than ${1000} profiles at a time`
            });
        }

        let sql = `SELECT
            "D1"."RESOURCE_ID" "operatorId",
            "E1"."PERSON_TITLE" "title",
            "E1"."PERSON_INITIALS" "initials",
            "E1"."PERSON_FORENAME" "forename",
            "E1"."PERSON_SURNAME" "surname",
            "E1"."STAFF_NO" "staffNumber",
            "D1"."type"
            FROM (
                SELECT
                "B1"."RESOURCE_ID",
                "B1".PERSON_ID,
                "type"
                FROM (
                    SELECT
                        "A1"."OP_RESOURCE_ID" "RESOURCE_ID",
                        PERSON_ID,
                        'OPERATOR' AS "type"
                    FROM "MSDSDBA"."OPERATOR" "A1"
                    UNION
                    SELECT
                        "A2"."PATROL_RESOURCE_ID" "RESOURCE_ID",
                        PERSON_ID,
                        'PATROL' AS "type"
                    FROM "MSDSDBA"."PATROL" "A2"
                ) "B1"
                JOIN "MSDSDBA"."RESOURCE_TBL" "C1"
                ON "B1"."RESOURCE_ID" = "C1"."RESOURCE_ID"
                WHERE "C1".RESOURCE_INEFF_DATE is NULL
            ) "D1"
            JOIN "MSDSDBA"."PERSON" "E1"
            ON "D1"."PERSON_ID" = "E1"."PERSON_ID"
        `;

        if (operatorIds) {
            sql += ` WHERE "D1"."RESOURCE_ID" IN (${operatorIds.join(',')})`;
        }

        // Do limit + 1 same as in mongo to detect more results
        sql += ` OFFSET ${skip} ROWS FETCH NEXT ${limit + 1} ROWS ONLY`;

        try {
            const oracleProvider = this.dataStore.getProvider(DataStoreProviderType.ORACLE);
            const results = await oracleProvider.execute<SystemUser>(sql, {});

            let more = false;
            if (results.length > limit) {
                more = true;
                // return what's expected
                results.pop();
            }

            if (!results || (results && !results.length)) {
                return { results: [], more: false };
            }

            return { results, more };
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to get operator details'
            });
        }
    }

    /**
     * Get user public details from LDAP, max 100 records at a time, where username is a system username
     *
     * @param {{userNames?: string[]} & PaginationQuery} query
     * @return {Promise<PaginationQueryResult<SystemUser>>}
     */
    protected async getLDAPSystemUsers(
        query: {
            userNames: string[];
        } & Required<PaginationQuery>
    ): Promise<PaginationQueryResult<SystemUser>> {
        // TODO: use user api to get details about users
        const { userNames, limit, skip } = query;
        const start = skip * limit;
        const end = skip * limit + limit;
        const finalUserNames = userNames.slice(start, end);

        try {
            if (limit > 100) {
                throw new Exception({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_FAIL,
                    message: `Unable to fetch more than ${100} profiles at a time`
                });
            }

            const results = await this.ldap.searchUsers(finalUserNames);

            if (!results || (results && !results.length)) {
                return { results: [], more: false };
            }

            const finalResults = results.map((entry) => {
                const { title, initials, operatorId, surname, forename, staffNumber, type } = entry;

                return { title, initials, operatorId, surname, forename, staffNumber, type };
            });
            return { results: finalResults, more: end >= userNames.length };
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to get operator details'
            });
        }
    }
}

interface SystemUser {
    title?: string;
    initials?: string;
    operatorId: number;
    surname?: string;
    forename?: string;
    staffNumber?: number | string;
    type: UserType;
}
