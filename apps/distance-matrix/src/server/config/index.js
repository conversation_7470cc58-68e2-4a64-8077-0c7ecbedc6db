'use strict';

var path = require('path');

// Export the config object based on the NODE_ENV
// ==============================================
module.exports = {
    env: process.env.NODE_ENV || 'development',

    // Root path of server
    root: path.normalize(__dirname + '/../..'),

    ip: '0.0.0.0',

    port: process.env.aahPodHttpPort || process.env.distanceMatrixServicePort || 7107,

    apiEndPoint: process.env.aahPodEndpoint || process.env.distanceMatrixApiEndPoint || '/api/distance-matrix-service',

    osrmEndPoint: process.env.osrmEndPoint || 'http://rh0113p:5000'
};
