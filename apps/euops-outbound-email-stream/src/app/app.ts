import { AuditClient } from '@aa/audit-client';
import {
    AlertAction,
    AlertSeverity,
    EmailFromTemplate,
    EmailStatus,
    EventType,
    isEmailFromTemplate,
    isSendableEmail,
    isWithDataId,
    Namespace,
    NoteType,
    NoteEntityType,
    NoteReason,
    SendableEmail,
    WithDataId,
    WithoutAttachments,
    WithSendableAttachments
} from '@aa/data-models/common';
import { DataStoreProviderType, EuopsEmailStore } from '@aa/data-store';
import { EmailTemplateClient } from '@aa/email-template-client';
import { EventCode, Exception } from '@aa/exception';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubCheckpoint, EventHubReceiver, EventHubReceiverConfig, QueueEventHandler, ServiceBusSender, ServiceBusSenderConfig } from '@aa/queue';
import { BackendEnvironment } from '@aa/utils';
import { ObjectId } from 'mongodb';
import { ActionClient } from '@aa/action-client';

const appName = 'euops-outbound-email-stream';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.EUOPS_OUTBOUND_EMAIL_STREAM;
    protected commsStreamReceiver: EventHubReceiver<EventType.OUTBOUND_EMAIL, WithSendableAttachments<WithDataId<SendableEmail>>, void>;
    protected store: EuopsEmailStore;
    protected attachementRenderSender: ServiceBusSender<EventType.RENDER_ATTACHMENT, WithSendableAttachments<WithDataId<SendableEmail>>>;
    protected auditClient: AuditClient;
    protected emailTemplateClient: EmailTemplateClient;
    protected actionClient: ActionClient;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.MONGODB]
        });

        this.auditClient = new AuditClient({
            application: BackendApplication.EUOPS_OUTBOUND_EMAIL_STREAM,
            connector: this.connector,
            operatorId: -1
        });

        this.actionClient = new ActionClient({
            httpClient: this.httpClient,
            connector: this.connector
        });

        this.store = new EuopsEmailStore({
            dataStore: this.dataStore,
            databaseName: 'entities',
            logger: this.logger
        });

        const checkpoint = new EventHubCheckpoint({
            accountUrl: environment.queue.storageAccountUrl || '',
            accountName: environment.queue.storageAccountName || '',
            accountKey: environment.queue.storageAccountKey || '',
            containerName: 'euops-outbound-email-stream',
            logger: this.logger
        });

        const ehBaseConfig: Omit<EventHubReceiverConfig, 'eventHubName' | 'consumerGroup' | 'checkpoint'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            maxBatchSize: 1, // we want to process even 1
            maxDelayPerBatch: 2 // 2s
        };

        const sbqBaseConfig: Omit<ServiceBusSenderConfig, 'queueName'> = {
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            connectionString: environment.queue.SBQConnectionString
        };

        this.commsStreamReceiver = new EventHubReceiver({
            ...ehBaseConfig,
            checkpoint,
            eventHubName: 'comms-stream',
            consumerGroup: 'euops-outbound-email-stream'
        });

        this.attachementRenderSender = new ServiceBusSender({
            ...sbqBaseConfig,
            queueName: 'attachment-renderer-queue'
        });

        this.emailTemplateClient = new EmailTemplateClient({
            logger: this.logger,
            system: this.system,
            context: this.context
        });

        this.commsStreamReceiver.on(EventType.OUTBOUND_EMAIL, this.outboundEmailHandler);
    }

    protected outboundEmailHandler: QueueEventHandler<EventType.OUTBOUND_EMAIL, WithSendableAttachments<WithDataId<SendableEmail>>, void> = async (context) => {
        const { entry } = context;
        const outboundEmail = entry.data;
        try {
            const { type, to, subject, body } = outboundEmail;

            this.logger.info({
                sourceName: this.name,
                message: 'Outbound email received',
                data: { parentMessageId: outboundEmail.parentMessageId, messageId: outboundEmail._id }
            });

            //Audit trail
            const auditId = type + ':' + to + ':' + subject + ':' + body; //todo: maybe it should be something else.
            // need to make sure
            const trace = this.auditClient.getTrace(Namespace.EUOPS, auditId);

            await this.auditClient.reportAction(trace, {
                message: 'outboundEmailHandler event',
                data: { ...outboundEmail }
            });

            //we're only interested in euops emails to send
            if (!this.shouldProcess(outboundEmail)) {
                this.logger.info({
                    sourceName: this.name,
                    message: 'Email not qualifying for processing',
                    data: { parentMessageId: outboundEmail.parentMessageId, messageId: outboundEmail._id }
                });
                return;
            }

            let sendableEmail: WithoutAttachments<SendableEmail>;

            if (isEmailFromTemplate(outboundEmail)) {
                //body does not exist. This is i/f EmailFromTemplate. We must use moustache/hb to render the body
                let { body, subject } = outboundEmail;
                const { custGroup } = outboundEmail;
                this.logger.info(`Email body does not exist. Getting template for custGroup ${custGroup} and type ${outboundEmail.type}`);

                if (!body || !body.trim()) {
                    body = await this.getBody(outboundEmail);
                }
                if (!subject || !subject.trim()) {
                    subject = await this.getSubject(outboundEmail);
                }

                sendableEmail = { ...outboundEmail, body, subject };
            } else {
                sendableEmail = outboundEmail;
            }

            // This has to be later after above code as we generate body and subject for template emails
            if (!isSendableEmail(sendableEmail)) {
                //body does not exist and no custGroup or bindings found
                this.logger.error({
                    sourceName: this.name,
                    message: 'Email body does not exist and no custGroup or bindings found',
                    data: { email: outboundEmail }
                });
                throw new Exception({
                    sourceName: this.name,
                    code: EventCode.MOD_EXEC_WARN,
                    data: { outboundEmail },
                    message: `Email body does not exist and no template found`
                });
            }

            sendableEmail = { ...sendableEmail, status: EmailStatus.SENDING_PROCESSING };
            let emailToSend: WithDataId<SendableEmail>;

            // If id provided, skip insert
            if (isWithDataId(sendableEmail)) {
                emailToSend = sendableEmail;
                await this.store.updateEmail(sendableEmail);
            } else {
                const _id = await this.store.insertEmail(sendableEmail);

                //not sure if best way to do this.
                emailToSend = {
                    _id: new ObjectId(_id).toString(),
                    ...sendableEmail
                };
            }

            this.logger.info({
                sourceName: this.name,
                message: 'Updated email status',
                data: {
                    parentMessageId: emailToSend.parentMessageId,
                    messageId: emailToSend._id,
                    status: emailToSend.status
                }
            });

            await this.attachementRenderSender.send(EventType.RENDER_ATTACHMENT, emailToSend);
        } catch (error) {
            // update entity status to prevent re-processing
            outboundEmail.status = EmailStatus.TO_SEND;
            await this.store.updateEmail(outboundEmail);

            this.logger.info({
                sourceName: this.name,
                message: 'Email status reset',
                data: { parentMessageId: outboundEmail.parentMessageId, messageId: outboundEmail._id, status: outboundEmail.status }
            });

            throw new Exception({
                message: 'Error while processing euops outbound email',
                data: { context },
                error
            });
        }
    };

    protected async getBody(outboundEmail: EmailFromTemplate): Promise<string> {
        const { custGroup, bindings, type } = outboundEmail;
        const bodyTemplate = await this.emailTemplateClient.getBodyTemplate({ type, custGroup });

        if (!bodyTemplate) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                data: { outboundEmail },
                message: `No template found for custGroup ${custGroup} and type ${type}`
            });
        }
        const body = await this.emailTemplateClient.bind(bodyTemplate, bindings);
        const taskId = bindings?.taskId ?? 'Not found';

        if (!body || !body.trim()) {
            const date = new Date();
            const action: AlertAction = {
                created: date,
                updated: date,
                namespace: Namespace.EUOPS,
                type: NoteType.ALERT,
                title: `Failure while processing email for task with id ${taskId}`,
                content: {
                    name: `Failure while processing email for task with id ${taskId}`,
                    severity: AlertSeverity.LOW,
                    from: this.name,
                    body: `Our system encountered a failure while processing a email for task with id ${taskId}.
                        Please check if ${custGroup ?? 'Not found'} has the relevant template in the database.`
                },
                context: {
                    reason: NoteReason.FAILURE,
                    entities: [{ type: NoteEntityType.TASK, id: Number(taskId) || -1 }]
                }
            };
            this.logger.warn({
                sourceName: this.name,
                message: 'Received empty email body',
                data: { action }
            });
            //send action
            await this.actionClient.insert(action);
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                data: { outboundEmail },
                message: `Email body does not exist and no template found`
            });
        }
        this.logger.info({
            sourceName: this.name,
            message: 'Received the email body',
            data: {
                body
            }
        });
        return body;
    }

    protected async getSubject(outboundEmail: EmailFromTemplate): Promise<string> {
        const { custGroup, bindings, type } = outboundEmail;
        const subjectTemplate = await this.emailTemplateClient.getSubjectTemplate({ type, custGroup });

        if (!subjectTemplate) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                data: { outboundEmail },
                message: `No subject template found for custGroup ${custGroup} and type ${type}`
            });
        }

        const subject = await this.emailTemplateClient.bind(subjectTemplate, bindings);

        if (!subject) {
            this.logger.error('Email subject does not exist and no custGroup or bindings found');
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                data: { outboundEmail },
                message: `Email subject does not exist and no template found`
            });
        }

        return subject;
    }

    private shouldProcess = (outboundEmail: WithSendableAttachments<WithDataId<SendableEmail>>) => {
        const isCorrectNamespace = outboundEmail.namespace === 'EUOPS';
        const isTypeEmail = !!outboundEmail?.type?.match(/_EMAIL$/);
        const isStatusToSend = outboundEmail.status === EmailStatus.TO_SEND;
        return isCorrectNamespace && isTypeEmail && isStatusToSend;
    };
}
