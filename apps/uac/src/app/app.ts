import { ErrorResponse } from '@aa/connector';
import { Context } from '@aa/context';
import { PaginationQuery, UACCodeConfig, UACInitialData, UACNamespaceConfig, UACNamespaces, UACStatus } from '@aa/data-models/common';
import { DataStoreProviderType, UACStore } from '@aa/data-store';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Interval } from '@aa/interval';
import { Microservice } from '@aa/microservice';
import { getResponse } from '@aa/server-utils';
import { SystemEvent } from '@aa/system';
import { BackendEnvironment, Utils } from '@aa/utils';
import { Request, Response } from 'express';

/**
 *
 * Check if invalid namespace
 * @param {number} namespace
 * @return {namespace is UACNamespaces}
 */
function isValidNamespace(namespace: number): namespace is UACNamespaces {
    return Object.values(UACNamespaces).includes(namespace);
}

/**
 * Check if invalid Request id
 * @param {number} requestId
 * @return {requestId is number}
 */
function isValidRequestId(requestId?: string): requestId is string {
    return typeof requestId === 'string';
}

/**
 * Check if invalid operator id
 * @param {number} operatorId
 * @return {operatorId is number}
 */
function isValidOperatorId(operatorId?: number): operatorId is number {
    return typeof operatorId === 'number';
}

/**
 * Handle invalid namespace
 * @param {e.Response} res
 */
function handleInvalidNamespace(res: Response) {
    const response: ErrorResponse = {
        status: 'INVALID_REQ',
        error: { code: 'ERROR', msg: 'Invalid format of namespace id' }
    };
    return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
}

/**
 * Handle invalid request id
 * @param {e.Response} res
 */
function handleInvalidRequestId(res: Response) {
    const response: ErrorResponse = {
        status: 'INVALID_REQ',
        error: { code: 'ERROR', msg: 'Invalid format of request id' }
    };
    return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
}

/**
 * Handle invalid operator id
 * @param {e.Response} res
 */
function handleInvalidOperatorId(res: Response) {
    const response: ErrorResponse = {
        status: 'INVALID_REQ',
        error: { code: 'ERROR', msg: 'Action allowed only for operator' }
    };
    return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
}

/**
 * Handle invalid initial uac req
 * @param {UACInitialData | undefined} body
 * @param req
 * @param context
 * @param codeConfig
 */
function isValidInitialUAC(body: UACInitialData | undefined, req: Request<{ namespace: string }, any, any, any, any>, context: Context, codeConfig: UACCodeConfig): body is UACInitialData {
    if (!body) {
        return false;
    }

    // if namespace wrong
    if (Number.parseInt(req.params.namespace) !== body.namespace) {
        return false;
    }

    // UAC have to be requested by the owner
    if (body.requestor !== context.store.operatorId) {
        return false;
    }

    if (codeConfig.taskDetailsRequired && !(body.taskId || body.vrn)) {
        return false;
    }
    if (typeof body.reasonType === 'undefined') {
        return false;
    }

    return typeof body.concessionType !== 'undefined';
}

export class App extends Microservice {
    public name = 'UAC';
    public application = BackendApplication.UAC;
    protected store: UACStore;
    protected interval: Interval;
    protected expireIntervalUid: string;
    protected voidIntervalUid: string;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName: 'uac',
            dataStoreProviders: [DataStoreProviderType.REDIS, DataStoreProviderType.MONGODB, DataStoreProviderType.ORACLE]
        });

        this.store = new UACStore({
            logger: this.logger,
            dataStore: this.dataStore
        });

        this.interval = new Interval();
        this.expireIntervalUid = this.interval.set(this.expire, '10s');
        this.voidIntervalUid = this.interval.set(this.void, '10s');

        this.system.once(SystemEvent.EXIT, this.stopIntervals);

        // multi-UAC requests
        this.server.post('/uacs/:namespace', this.list);
        this.server.post('/uacs/:namespace/count', this.count);
        this.server.post('/uacs/:namespace/search', this.search);

        // single-UAC requests
        this.server.post('/uac/:namespace', this.create);
        this.server.get('/uac/:namespace/:requestId/claim', this.claim);
        this.server.post('/uac/:namespace/:requestId', this.read);
        this.server.get('/uac/:namespace/:requestId', this.read);
        this.server.post('/uac/:namespace/:requestId/approve', this.approve);
        this.server.post('/uac/:namespace/:requestId/reject', this.reject);
        this.server.get('/uac/:namespace/:requestId/cancel', this.cancel);
        this.server.post('/uac/:namespace/:requestId/verify', this.verify);
        this.server.post('/uac/:namespace/:requestId/redeem', this.redeem);
    }

    protected list = async (
        req: Request<
            { namespace: string },
            any,
            {
                statuses?: number[];
                requestor?: number;
                authoriser?: number;
                from?: string;
                to?: string;
                taskId?: number;
            } & PaginationQuery
        >,
        res: Response
    ) => {
        try {
            const namespace = Number.parseInt(req.params.namespace);
            const { statuses = [UACStatus.REQUESTED], from, to, ...body } = req.body;

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }
            const fromDate = from ? new Date(from) : undefined;
            const toDate = to ? new Date(to) : undefined;
            const uacs = await this.store.list({
                namespace,
                statuses,
                from: fromDate,
                to: toDate,
                ...body
            });

            return getResponse(res, ServerResponseCode.OK, uacs);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while retrieving list of UACs'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected count = async (
        req: Request<
            { namespace: string },
            any,
            {
                statuses?: number[];
                requestor?: number;
                authoriser?: number;
                from?: string;
                to?: string;
            }
        >,
        res: Response
    ) => {
        try {
            const namespace = Number.parseInt(req.params.namespace);
            const { statuses = [UACStatus.REQUESTED], from, to, ...body } = req.body;

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }
            const fromDate = from ? new Date(from) : undefined;
            const toDate = to ? new Date(to) : undefined;
            const count = await this.store.count({
                namespace,
                statuses,
                from: fromDate,
                to: toDate,
                ...body
            });

            return getResponse(res, ServerResponseCode.OK, { count });
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while retrieving count of UACs'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected search = async (req: Request<{ namespace: string }, any, { requestIdLike: string; statuses?: number[] } & PaginationQuery>, res: Response) => {
        try {
            const namespace = Number.parseInt(req.params.namespace);
            const { statuses = [UACStatus.REQUESTED], ...body } = req.body;

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }
            const count = await this.store.search({
                namespace,
                statuses,
                ...body
            });

            return getResponse(res, ServerResponseCode.OK, count);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while searching for UACs'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected create = async (req: Request<{ namespace: string }, any, UACInitialData>, res: Response) => {
        try {
            const namespace = Number.parseInt(req.params.namespace);

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }

            const codeConfig = UACNamespaceConfig[namespace];
            const uacInitialData = req.body;

            const reqValid = isValidInitialUAC(uacInitialData, req, this.context, codeConfig);
            if (!reqValid) {
                const response: ErrorResponse = {
                    status: 'INVALID_REQ',
                    error: {
                        code: 'ERROR',
                        msg: 'Invalid format of initial UAC data'
                    }
                };
                return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
            }

            const uac = await this.store.create(uacInitialData);

            // remove auth code - for security reasons
            uac.authCode = undefined;

            return getResponse(res, ServerResponseCode.OK, uac);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Create UAC request failure'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected claim = async (req: Request<{ namespace: string; requestId: string }>, res: Response) => {
        try {
            const { requestId } = req.params;
            const namespace = Number.parseInt(req.params.namespace);

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }

            if (!isValidRequestId(requestId)) {
                return handleInvalidRequestId(res);
            }

            const operatorId = this.context.store.operatorId;
            if (!isValidOperatorId(operatorId)) {
                return handleInvalidOperatorId(res);
            }

            // get ttl for claimed record (defined per namespace)
            const { claimedTtl } = UACNamespaceConfig[namespace];
            let expiryDate: Date | undefined;
            if (claimedTtl) {
                expiryDate = Utils.generateFutureDate(claimedTtl);
            }

            const result = await this.store.claim({
                namespace,
                requestId,
                operatorId,
                expiryDate
            });

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while claiming UAC'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected read = async (req: Request<{ namespace: string; requestId: string }, any, { status?: number }>, res: Response) => {
        try {
            const { requestId } = req.params;
            const namespace = Number.parseInt(req.params.namespace);
            const { status } = req.body || {};

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }

            if (!isValidRequestId(requestId)) {
                return handleInvalidRequestId(res);
            }

            const uac = await this.store.read({ namespace, requestId, status });

            // if no records found
            if (!uac) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            // UAC auth code can be read only by the authoriser
            if (uac.authoriser !== this.context.store.operatorId) {
                // remove auth code - for security reasons
                uac.authCode = undefined;
            }

            return getResponse(res, ServerResponseCode.OK, uac);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while reading UAC'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected approve = async (req: Request<{ namespace: string; requestId: string }, any, any, { decisionReason: string }>, res: Response) => {
        try {
            const { requestId } = req.params;
            const namespace = Number.parseInt(req.params.namespace);
            const { decisionReason } = req.body;

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }

            if (!isValidRequestId(requestId)) {
                return handleInvalidRequestId(res);
            }

            const operatorId = this.context.store.operatorId;
            if (!isValidOperatorId(operatorId)) {
                return handleInvalidOperatorId(res);
            }

            if (!decisionReason) {
                const response: ErrorResponse = {
                    status: 'INVALID_REQ',
                    error: { code: 'ERROR', msg: 'Missing decision reason' }
                };
                return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
            }

            const result = await this.store.approve({
                namespace,
                requestId,
                operatorId,
                decisionReason
            });

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while approving UAC'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected reject = async (req: Request<{ namespace: string; requestId: string }, any, { decisionReason: string }>, res: Response) => {
        try {
            const { requestId } = req.params;
            const namespace = Number.parseInt(req.params.namespace);
            const { decisionReason } = req.body;

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }

            if (!isValidRequestId(requestId)) {
                return handleInvalidRequestId(res);
            }

            const operatorId = this.context.store.operatorId;
            if (!isValidOperatorId(operatorId)) {
                return handleInvalidOperatorId(res);
            }

            if (!decisionReason) {
                const response: ErrorResponse = {
                    status: 'INVALID_REQ',
                    error: { code: 'ERROR', msg: 'Missing decision reason' }
                };
                return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
            }

            const result = await this.store.reject({
                namespace,
                requestId,
                operatorId,
                decisionReason
            });

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while rejecting UAC'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected cancel = async (req: Request<{ namespace: string; requestId: string }>, res: Response) => {
        try {
            const { requestId } = req.params;
            const namespace = Number.parseInt(req.params.namespace);

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }

            if (!isValidRequestId(requestId)) {
                return handleInvalidRequestId(res);
            }

            const operatorId = this.context.store.operatorId;
            if (!isValidOperatorId(operatorId)) {
                return handleInvalidOperatorId(res);
            }

            const result = await this.store.cancel({
                operatorId,
                namespace,
                requestId
            });

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while cancelling UAC'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected verify = async (req: Request<{ namespace: string; requestId: string }, any, { authCode: string; isEmergency?: boolean }>, res: Response) => {
        try {
            const { requestId } = req.params;
            const namespace = Number.parseInt(req.params.namespace);
            const { authCode, isEmergency = false } = req.body;

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }

            if (!isValidRequestId(requestId)) {
                return handleInvalidRequestId(res);
            }

            // no auth code, verification fails
            if (!authCode) {
                return getResponse(res, ServerResponseCode.OK, false);
            }

            let result: boolean;
            // if emergency and code provided matches emergency code allowed by namespace
            if (isEmergency) {
                result = await this.store.verifyEmergency({
                    namespace,
                    requestId,
                    authCode
                });
            } else {
                result = await this.store.verify({
                    namespace,
                    requestId,
                    authCode
                });
            }

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while verifying UAC'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected redeem = async (req: Request<{ namespace: string; requestId: string }, any, { authCode: string; isEmergency?: boolean }>, res: Response) => {
        try {
            const { requestId } = req.params;
            const namespace = Number.parseInt(req.params.namespace);
            const { authCode, isEmergency = false } = req.body;

            if (!isValidNamespace(namespace)) {
                return handleInvalidNamespace(res);
            }

            if (!isValidRequestId(requestId)) {
                return handleInvalidRequestId(res);
            }

            // no auth code, verification fails
            if (!authCode) {
                return getResponse(res, ServerResponseCode.OK, false);
            }

            let result: boolean;
            // if emergency and code provided matches emergency code allowed by namespace
            if (isEmergency) {
                result = await this.store.redeemEmergency({
                    namespace,
                    requestId,
                    authCode
                });
            } else {
                result = await this.store.redeem({
                    namespace,
                    requestId,
                    authCode
                });
            }

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while redeeming UAC auth code'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Expire requested entries
     * @return {Promise<void>}
     */
    protected expire = async () => {
        try {
            await this.store.expire();
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while expiring UAC records'
            });
            this.logger.error(exception);
        }
    };

    /**
     * Expire unused entries
     * @return {Promise<void>}
     */
    protected void = async () => {
        try {
            await this.store.void();
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while voiding UAC records'
            });
            this.logger.error(exception);
        }
    };

    /**
     * Remove old entries
     * @return {Promise<void>}
     */
    protected clean = async () => {
        try {
            // older than 30d
            await this.store.clean('30d');
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while cleaning old UAC records'
            });
            this.logger.error(exception);
        }
    };

    /**
     * Stop intervals
     */
    protected stopIntervals = () => {
        this.interval.del(this.expireIntervalUid);
        this.interval.del(this.voidIntervalUid);
    };
}
