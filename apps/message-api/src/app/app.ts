import { ActionClient } from '@aa/action-client';
import { AzureEmailClient } from '@aa/azure-email-client';
import {
    Action,
    DownloadedAttachment,
    EmailAction,
    EmailForward,
    EmailNote,
    EmailPreview,
    EmailReply,
    EmailStatus,
    EventType,
    MessageForward,
    MessageForwardSchema,
    MessageReply,
    MessageReplySchema,
    Namespace,
    Note,
    NoteType,
    PaginationQuery,
    SendableEmail,
    StoredEmail,
    WithDataId
} from '@aa/data-models/common';
import { DataStoreProviderType, EuopsEmailStore, MongodbDataProvider } from '@aa/data-store';
import { MongodbUtils } from '@aa/data-store-utils';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubSender, EventHubSenderConfig } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';
import { ObjectId } from 'mongodb';

const appName = 'message-api';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.MESSAGE_API;
    protected mongoProvider: MongodbDataProvider;
    protected actionClient: ActionClient;
    protected euopsEmailStore: EuopsEmailStore;
    protected euopsSenderEmail: string;
    protected azureEmailClient: AzureEmailClient;

    protected commsStreamSender: EventHubSender<EventType.OUTBOUND_EMAIL, WithDataId<SendableEmail>>;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.MONGODB, DataStoreProviderType.REDIS, DataStoreProviderType.ORACLE]
        });

        const evhSenderBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };
        this.azureEmailClient = new AzureEmailClient({ logger: this.logger, authClient: this.authClient });

        this.euopsSenderEmail = environment.emails.euopsOutboundEmail;

        this.mongoProvider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);

        this.actionClient = new ActionClient({
            httpClient: this.httpClient,
            connector: this.connector
        });

        this.euopsEmailStore = new EuopsEmailStore({
            dataStore: this.dataStore,
            databaseName: 'entities',
            logger: this.logger
        });

        this.commsStreamSender = new EventHubSender({
            ...evhSenderBase,
            eventHubName: 'comms-stream'
        });

        this.server.post(`/message/:messageId/reply`, this.replyToEmail, { schema: MessageReplySchema });
        this.server.post('/message/:messageId/forward', this.forwardEmail, { schema: MessageForwardSchema });
        this.server.get('/message/:id', this.getEmail);

        // this.server.post('/email/send', this.sendEmail);
        this.server.get('/email/:id', this.getEmail);
        this.server.post('/emails', this.getEmails);
        this.server.post('/email/graph/send', this.sendGraphEmail);
    }

    protected getEmail = async (
        req: Request<
            {
                id: string;
            },
            StoredEmail
        >,
        res: Response<void>
    ) => {
        try {
            const { id } = req.params;

            this.logger.info({
                sourceName: this.name,
                message: `${this.name}: Get email`,
                data: { messageId: id }
            });

            const collection = await this.mongoProvider.collection<StoredEmail>('entities', 'euopsMessages');
            const email = await collection.findOne({ _id: new ObjectId(id) });

            if (!email) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            return getResponse(res, ServerResponseCode.OK, email);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'Reply failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    protected replyToEmail = async (
        req: Request<
            {
                messageId: string;
            },
            unknown,
            MessageReply
        >,
        res: Response<void>
    ) => {
        try {
            const { messageId } = req.params;
            const { message, actionId } = req.body;

            this.logger.info({
                sourceName: this.name,
                message: `${this.name}: Reply to email`,
                data: { messageId: messageId }
            });

            const actionCollection = await this.mongoProvider.collection<Action>('queue', 'actions');
            const emailAction = await actionCollection.findOne<EmailAction>({
                _id: new ObjectId(actionId),
                type: NoteType.EMAIL
            });

            if (!emailAction) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            const messageCollection = await this.mongoProvider.collection<StoredEmail>('entities', 'euopsMessages');
            const originalEmailMessage = await messageCollection.findOne({ _id: new ObjectId(messageId) });

            if (!originalEmailMessage) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
            if (!originalEmailMessage) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            const date = new Date();

            // compose email reply
            const emailPreview = {
                created: date,
                received: date,
                type: 'REPLY_EMAIL',
                from: this.euopsSenderEmail,
                to: originalEmailMessage.from,
                subject: 'Re: ' + originalEmailMessage.subject,
                reply: true,
                parentMessageId: messageId
            } satisfies Omit<EmailPreview, 'hasAttachments'>;

            const emailMessage = {
                status: EmailStatus.TO_SEND,
                namespace: Namespace.EUOPS,
                updated: emailPreview.created,
                body: message,
                ...emailPreview
            } satisfies EmailReply;

            const _id = await this.euopsEmailStore.insertEmail(emailMessage);

            // Create note to preserve trace of email sent
            const note: EmailNote = {
                created: date,
                updated: date,
                title: `Reply to email with subject: ${originalEmailMessage.subject}`,
                namespace: Namespace.EUOPS,
                type: NoteType.EMAIL,
                content: {
                    ...emailPreview,
                    _id,
                    // initially we dont allow attachments in replies
                    hasAttachments: false
                },
                parentEntity: emailAction?.parentEntity
            };
            const noteCollection = await this.mongoProvider.collection<Note>('entities', 'notes');
            await noteCollection.insertOne(note);

            // dispatch reply email
            await this.commsStreamSender.send(EventType.OUTBOUND_EMAIL, { ...emailMessage, _id });

            return getResponse(res, ServerResponseCode.OK);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'Reply failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    protected forwardEmail = async (
        req: Request<
            {
                messageId: string;
            },
            unknown,
            MessageForward
        >,
        res: Response<void>
    ) => {
        try {
            const { messageId } = req.params;
            const { message, actionId, recipients } = req.body;

            this.logger.info({
                sourceName: this.name,
                message: `${this.name}: Forward to email`,
                data: { messageId: messageId }
            });

            const actionCollection = await this.mongoProvider.collection<Action>('queue', 'actions');
            const emailAction = await actionCollection.findOne<EmailAction>({
                _id: new ObjectId(actionId),
                type: NoteType.EMAIL
            });

            if (!emailAction) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            const messageCollection = await this.mongoProvider.collection<StoredEmail>('entities', 'euopsMessages');
            const originalEmailMessage = await messageCollection.findOne({ _id: new ObjectId(messageId) });

            if (!originalEmailMessage) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            const date = new Date();

            // compose email forward
            const emailPreview = {
                created: date,
                received: date,
                type: 'FORWARD_EMAIL',
                from: this.euopsSenderEmail,
                to: recipients,
                subject: 'Fw: ' + originalEmailMessage.subject,
                forward: true,
                parentMessageId: messageId
            } satisfies Omit<EmailPreview, 'hasAttachments'>;

            const emailMessage = {
                status: EmailStatus.TO_SEND,
                namespace: Namespace.EUOPS,
                body: message,
                updated: emailPreview.created,
                ...emailPreview
            } satisfies EmailForward;

            const _id = await this.euopsEmailStore.insertEmail(emailMessage);

            // Create note to preserve trace of email sent
            const note: EmailNote = {
                created: date,
                updated: date,
                title: `Forward email with subject: ${originalEmailMessage.subject}`,
                namespace: Namespace.EUOPS,
                type: NoteType.EMAIL,
                content: {
                    ...emailPreview,
                    _id,
                    // initially we dont allow attachments in forwards
                    hasAttachments: false
                },
                parentEntity: emailAction?.parentEntity
            };
            const noteCollection = await this.mongoProvider.collection<Note>('entities', 'notes');
            await noteCollection.insertOne(note);

            // dispatch Forward email
            await this.commsStreamSender.send(EventType.OUTBOUND_EMAIL, { ...emailMessage, _id });

            return getResponse(res, ServerResponseCode.OK);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'Forward failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    //
    // /**
    //  * Send test email
    //  * @param {e.Request<unknown, unknown, DraftEmail>} req
    //  * @param {e.Response<WithId<DraftEmail>>} res
    //  * @return {Promise<void>}
    //  */
    // protected sendEmail = async (req: Request<unknown, unknown, WithSendableAttachments<DraftEmail>>, res:
    // Response<WithId<DraftEmail>>): Promise<void> => { try { // TODO: check if valid body const email = req.body;
    // this.logger.info({ sourceName: this.name, message: 'Received email to send', data: { email }, });  // persist
    // email const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB); const collection = await
    // provider.collection<StoredEmail>('entities', 'euopsMessages');  const result = await collection.insertOne({
    // ...email, status: EmailStatus.TO_SEND }); const persistedEmail = { ...email, _id: result.insertedId, } satisfies
    // SendableEmail;  await this.commsStreamSender.send(EventType.OUTBOUND_EMAIL, persistedEmail, false); return
    // getResponse(res, ServerResponseCode.OK, persistedEmail); } catch (error) { this.logger.error({ sourceName:
    // this.name, message: 'Failed sending email', data: { error }, }); return getResponse(res,
    // ServerResponseCode.SERVER_ERROR, error); } };

    /**
     * Get paginated list of emails for query
     * @param {e.Request} req
     * @param {e.Response} res
     * @return {Promise<void>}
     */
    protected getEmails = async (req: Request, res: Response): Promise<void> => {
        const { type, limit, skip } = req.body as {
            type: `${string}_EMAIL`;
        } & PaginationQuery;
        try {
            this.logger.log(`Query received to read emails`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<StoredEmail>('entities', 'euopsMessages');

            const cursor = collection.find({ type });

            const query: PaginationQuery = { limit, skip };
            const result = await MongodbUtils.paginate(cursor, query, 100);

            if (result && result.results.length) {
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed reading emails for query`,
                data: { ...req.body }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    public async sendGraphEmail(req: Request, res: Response): Promise<void> {
        try {
            const { type, status, namespace, from, to, subject, body, attachments } = req.body;

            if (!to || !from || !subject || !body) {
                res.status(400).send({ error: 'Missing required fields.' });
                return;
            }

            const email = {
                type,
                status,
                namespace,
                from,
                to,
                subject,
                body,
                received: new Date(),
                updated: new Date(),
                created: new Date()
            } satisfies SendableEmail;

            // Parse attachments
            const parsedAttachments: DownloadedAttachment[] = (attachments || []).map((attachment: any) => ({
                name: attachment.name,
                mimeType: attachment.mimeType,
                data: attachment.data // Assuming attachment data is already in base64 format
            }));

            // Call the sendMail method from AzureGraphClient
            const inboxId = this.environment.emails.euopsOutboundEmail;
            await this.azureEmailClient.send({ inboxId, email, attachments: parsedAttachments });

            // Send success response
            res.status(200).send({ message: 'Email sent successfully.' });
        } catch (error) {
            console.error('Error sending email:', error);
            res.status(500).send({ error: 'Failed to send email.' });
        }
    }
}
