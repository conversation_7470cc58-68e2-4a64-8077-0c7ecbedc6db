import { AuditClient } from '@aa/audit-client';
import { BackendApplication, MobileApplication } from '@aa/identifiers';
import { Namespace, AuditOperation, CUVData, CUVEvents, CUVOutdoorPayload, CUVSMS, CUVStatus, MICUVData, QueueEntryInput, QueryEntryStatus } from '@aa/data-models/common';
import { DataStore, DataStoreProviderType, DemandDeflectionStore, JobHistoryStore } from '@aa/data-store';
import { BreakdownTask } from '@aa/data-models/entities/breakdown-task';
import { SanitizedEntity } from '@aa/event-source';
import { ServerResponseCode } from '@aa/http-client';
import { Microservice } from '@aa/microservice';
import { EventHubCheckpoint, EventHubReceiver, EventHubReceiverConfig, EventHubSender, QueueEventHandler, ServiceBusSender, ServiceBusSenderConfig } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment, Utils } from '@aa/utils';
import CustomerGroup from '@aa/malstrom-models/lib/customer-group.model';
import { Request, Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';

const appName = 'cuv-processor';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.CUV_SERVICE_PROCESSOR;
    protected auxStreamReceiver: EventHubReceiver<CUVEvents.TASK_UPDATE | CUVEvents.UPDATE_IMAGE | CUVEvents.CREATE_CUV_REPORTING, CUVData | ImageUpdateEventBody | CUVOutdoorPayload, void>;
    protected cathieSender: ServiceBusSender<CUVEvents.TASK_UPDATE, CUVData>;
    protected bcasSender: ServiceBusSender<CUVEvents.TASK_UPDATE, CUVData>;
    protected smsSender: ServiceBusSender<CUVEvents.TASK_UPDATE, CUVSMS>;
    protected miStreamCUVSender: EventHubSender<CUVEvents.MI_CUV, MICUVData>;
    protected legacyDatastore: DataStore;
    protected store: DemandDeflectionStore;
    protected auditClient: AuditClient;
    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.MONGODB]
        });

        this.auditClient = new AuditClient({
            application: BackendApplication.CUV_SERVICE_PROCESSOR,
            connector: this.connector,
            operatorId: -1
        });

        // For cuv we are using old mongodb
        this.legacyDatastore = new DataStore({
            logger: this.logger,
            providers: [
                {
                    type: DataStoreProviderType.MONGODB,
                    config: {
                        logger: this.logger,
                        system: this.system,
                        connectionString: environment.mongodb.mongodbUrl,
                        appName
                    }
                },
                {
                    type: DataStoreProviderType.ORACLE,
                    config: {
                        logger: this.logger,
                        system: this.system,
                        appName,
                        ...environment.oracle
                    }
                }
            ]
        });

        this.store = new DemandDeflectionStore({
            dataStore: this.legacyDatastore,
            databaseName: environment.mongodb.mongoTaskDbName,
            logger: this.logger
        });

        const checkpoint = new EventHubCheckpoint({
            accountUrl: environment.queue.storageAccountUrl || '',
            accountName: environment.queue.storageAccountName || '',
            accountKey: environment.queue.storageAccountKey || '',
            containerName: 'cuv-processor',
            logger: this.logger
        });

        const ehBaseConfig: Omit<EventHubReceiverConfig, 'eventHubName' | 'consumerGroup' | 'checkpoint'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            maxBatchSize: 1, // we want to process even 1
            maxDelayPerBatch: 2 // 2s
        };

        const sbqBaseConfig: Omit<ServiceBusSenderConfig, 'queueName'> = {
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            connectionString: environment.queue.SBQConnectionString
        };

        //from demand-deflection
        this.auxStreamReceiver = new EventHubReceiver({
            ...ehBaseConfig,
            checkpoint,
            eventHubName: 'aux-stream',
            consumerGroup: 'cuv-processor'
        });
        this.auxStreamReceiver.on(CUVEvents.TASK_UPDATE, this.onGenericTaskUpdateReceived);
        this.auxStreamReceiver.on(CUVEvents.UPDATE_IMAGE, this.onGenericImageUpdateReceived);
        this.auxStreamReceiver.on(CUVEvents.CREATE_CUV_REPORTING, this.onGenericReportReceived);

        //to cathie-processor
        this.cathieSender = new ServiceBusSender({
            ...sbqBaseConfig,
            queueName: 'cathie-queue'
        });

        //to outgo-sms-queue
        this.smsSender = new ServiceBusSender({
            ...sbqBaseConfig,
            queueName: 'outgo-sms-queue'
        });

        // Event hub sender for MI/DAVE
        this.miStreamCUVSender = new EventHubSender({
            ...ehBaseConfig,
            eventHubName: 'mi-stream'
        });
        //to bcas-processor
        this.bcasSender = new ServiceBusSender({
            ...sbqBaseConfig,
            queueName: 'bcas-queue'
        });

        //this is for test purposes only
        this.server.get('/test/sms/:taskId', this.testSms);
    }

    protected testSms = async (req: Request, res: Response) => {
        try {
            const taskId = parseInt(req.params.taskId);
            const task = await this.connector.task.find.byId(taskId);

            if (!task) {
                this.logger.warn(`Unable to find task for ${taskId}`);
                return;
            }

            this.logger.info({
                sourceName: this.name,
                message: 'testSms',
                data: { task }
            });

            const phoneNo = (task.contact as any).telephone; //todo: add interface for contact
            const data: CUVSMS = {
                sendTo: phoneNo,
                smsBody: 'test'
            };
            const response = await this.smsSender.send(CUVEvents.TASK_UPDATE, data, false);
            return getResponse(res, ServerResponseCode.OK, response);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'testSms',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /*
    CUV-PROCESSOR
        -listens on aux-stream evhub : onGenericTaskUpdateReceived from demand-deflection
            - publishes to the cathie-queue svcbus TASK_UPDATE event

        -listens on aux-stream evhub: onGenericReportReceived from outdoor-ingester
            - adds cuv event to mongo
            - publishes to the cathie-queue svcbus TASK_UPDATE event
            - publishes to the outgo-sms-queue evhub TASK_UPDATE event

        -listens on aux-stream evhub : onGenericImageUpdateReceived

    */
    protected onGenericTaskUpdateReceived: QueueEventHandler<
        CUVEvents.TASK_UPDATE | CUVEvents.UPDATE_IMAGE | CUVEvents.CREATE_CUV_REPORTING,
        CUVData | ImageUpdateEventBody | CUVOutdoorPayload,
        void
    > = async (context) => {
        this.logger.info({
            sourceName: this.name,
            message: 'onGenericTaskUpdateReceived event received',
            data: { context }
        });
        try {
            const { entry } = context;
            const data = entry.data as CUVData;
            const { taskId } = data;

            const task = await this.connector.task.find.byId(taskId);
            const custGroup = (task as BreakdownTask).entitlement?.customerGroup.code;
            const customerGroup = new (CustomerGroup as any)();
            customerGroup.code(custGroup);
            this.logger.info({
                sourceName: this.name,
                message: 'Processing CUV task update for revoke',
                data: { isbank: customerGroup.isBank(), custGroupCode: custGroup, taskid: taskId }
            });

            if (customerGroup.isBank()) {
                await this.bcasSender.send(CUVEvents.TASK_UPDATE, data, false);
            } else {
                await this.cathieSender.send(CUVEvents.TASK_UPDATE, data, false);
            }

            this.logger.info({
                sourceName: this.name,
                message: 'onGenericTaskUpdateReceived event received',
                data: { data }
            });

            const { membership: _, ...miCuvData } = data;
            await this.miStreamCUVSender.send(CUVEvents.MI_CUV, <MICUVData>miCuvData);
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                message: 'onGenericTaskUpdateReceived event error',
                data: { error }
            });
        }
    };

    protected onGenericImageUpdateReceived: QueueEventHandler<
        CUVEvents.TASK_UPDATE | CUVEvents.UPDATE_IMAGE | CUVEvents.CREATE_CUV_REPORTING,
        CUVData | ImageUpdateEventBody | CUVOutdoorPayload,
        void
    > = async (context) => {
        this.logger.info({
            sourceName: this.name,
            message: 'onGenericImageUpdateReceived event received',
            data: { context }
        });
        try {
            const { entry } = context;
            const { taskId, imageGuid, status } = entry.data as ImageUpdateEventBody;

            const task = await this.connector.task.find.byId(taskId);
            if (!task) {
                this.logger.warn(`Unable to find task for ${taskId}`);
                return;
            }

            const vrn = task.vehicle?.registration;

            if (!vrn) {
                this.logger.warn(`No VRN found for task id ${taskId}`);
                return;
            }

            const entitlement = await this.connector.entitlement.find.byTaskId(taskId);

            if (!entitlement) {
                this.logger.warn(`No entitlement found for task id ${taskId}`);
                return;
            }
            const membership = entitlement.policy.membershipNumber;

            this.logger.info({
                message: 'Processing CUV image upload',
                data: { imageId: imageGuid, status, taskId, membership, vrn }
            });

            const currentCuv = await this.store.getCUV({ vrn, membership });
            this.logger.warn({
                message: `Merging images`,
                data: {
                    imagesIds: currentCuv?.imagesIds,
                    newImage: { status, imageId: imageGuid }
                }
            });
            const imagesIds = this.mergeImages(currentCuv?.imagesIds || [], {
                status,
                imageId: imageGuid
            });

            // if nothing changed, bailout
            if (currentCuv?.imagesIds && this.isSameImageSet(currentCuv.imagesIds, imagesIds)) {
                this.logger.warn({
                    message: `No change detected`,
                    data: { imagesIds, currentCuv }
                });
                return;
            }

            const auditId = membership + ':' + vrn;
            const trace = this.auditClient.getTrace(Namespace.CUV, auditId);

            this.logger.info({
                message: 'Processing CUV image upload',
                data: { imageId: imageGuid, status, taskId, membership, vrn }
            });
            await this.auditClient.reportAction(trace, {
                message: 'Processing CUV image upload',
                data: { imageId: imageGuid, status, taskId, membership, vrn }
            });

            // we are upserting only data that changed or creating initial entry
            const cuvChange: SanitizedEntity<CUVData, 'membership' | 'vrn'> = {
                customerRequestId: task.customerRequestId,
                membership,
                taskId,
                vrn,
                updated: new Date(),
                imagesIds
            };

            // if first report, set creation date
            if (!currentCuv?.status) {
                cuvChange.created = new Date();
                this.logger.warn(`No CUV found for vrn ${vrn} and membership no ${membership}, creating initial entry`);
            }

            await this.store.upsertCUV(cuvChange, {
                action: AuditOperation.UPDATE,
                source: MobileApplication.EVA
            });

            const { membership: _, ...miCuvData } = cuvChange;
            await this.miStreamCUVSender.send(CUVEvents.MI_CUV, <MICUVData>miCuvData);

            this.logger.info({
                sourceName: this.name,
                message: 'onGenericImageUpdateReceived event completed',
                data: entry.data
            });
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                message: 'onGenericImageUpdateReceived event error',
                data: { error }
            });
        }
    };

    /**
     * Returns true if same set of images
     * @param {string[]} setA
     * @param {string[]} setB
     * @return {boolean}
     * @protected
     */
    protected isSameImageSet(setA: string[], setB: string[]): boolean {
        const setACopy = setA.slice().sort();
        const setBCopy = setB.slice().sort();

        return setACopy.length === setBCopy.length && JSON.stringify(setACopy) === JSON.stringify(setBCopy);
    }

    /**
     * Returns sms body as per given customer group
     * @param custGroup
     */
    protected getSMSBody(customerGroup: any): string {
        let templateFilePath: string;

        try {
            if (customerGroup.isBank()) {
                templateFilePath = '../lib/templates/cuv-sms-template-b2b.hbs';
            } else {
                // default to personal if its not a bank customer
                templateFilePath = '../lib/templates/cuv-sms-template-default.hbs';
            }
            const smsBodyTemplate = fs.readFileSync(path.join(__dirname, templateFilePath), 'utf-8');
            return smsBodyTemplate;
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'getSMSBody',
                data: { ex }
            });

            // todo: need to review below, if somehow file read fails, it will fallback to static text for sms
            const defaultSMSBody =
                "You recently called out one of our patrols when you broke down. The vehicle we came out to was a commercial use vehicle and these aren't covered under the terms of your current breakdown policy. We want to keep you on the road so to get you on the right cover for your needs give us a call on 0343 316 4444 and we can talk about the options available to you, like adding commercial use vehicle cover to your existing policy or changing your policy to cover another named vehicle that's not used for commercial reasons." +
                '\n' +
                'https://www.theaa.com/business/breakdown/car-and-van-cover';

            return defaultSMSBody;
        }
    }

    protected onGenericReportReceived: QueueEventHandler<CUVEvents.TASK_UPDATE | CUVEvents.UPDATE_IMAGE | CUVEvents.CREATE_CUV_REPORTING, CUVData | ImageUpdateEventBody | CUVOutdoorPayload, void> =
        async (context) => {
            try {
                const {
                    entry: { data: data }
                } = context;
                const { taskId, membership, vrn, custGroup } = data as CUVOutdoorPayload;
                const task = await this.connector.task.find.byId(taskId);

                if (!task) {
                    this.logger.warn(`Unable to find task for task id ${taskId}`);
                    return;
                }

                const currentCUVStatus = await this.store.getCUVStatus({
                    membership,
                    vrn
                });
                const passedOrFirstReport = !!(currentCUVStatus && currentCUVStatus.passed);

                const cuv: SanitizedEntity<CUVData, 'membership' | 'vrn'> = {
                    ...(data as CUVOutdoorPayload),
                    customerRequestId: task.customerRequestId,
                    // Enable in 156
                    status: passedOrFirstReport ? CUVStatus.YELLOW : CUVStatus.RED,
                    //status: CUVStatus.YELLOW,
                    updated: new Date()
                };

                // if first report or passed, lets check if report exists
                if (passedOrFirstReport) {
                    const currentCUV = await this.store.getCUV({ membership, vrn });

                    // if no cuv with status present, set creation date
                    if (!currentCUV?.status) {
                        cuv.created = new Date();
                    }
                }

                const auditId = membership + ':' + vrn;
                const trace = this.auditClient.getTrace(Namespace.CUV, auditId);

                await this.auditClient.reportAction(trace, {
                    message: 'Processing CUV report',
                    data
                });

                if (passedOrFirstReport) {
                    await this.auditClient.reportAction(trace, {
                        message: 'Flag set as Yellow due to no active CUV record',
                        data
                    });
                } else {
                    await this.auditClient.reportAction(trace, {
                        message: 'Flag set as Red due to existing CUV record',
                        data
                    });
                }

                await this.store.upsertCUV(cuv);
                const customerGroup = new (CustomerGroup as any)();
                customerGroup.code(custGroup);
                this.logger.info({
                    sourceName: this.name,
                    message: 'cuv report received checking if bank customer or PERSONAL',
                    data: { isbank: customerGroup.isBank(), custGroupCode: custGroup }
                });
                if (customerGroup.isBank()) {
                    await this.bcasSender.send(CUVEvents.TASK_UPDATE, <CUVData>cuv, false);
                } else {
                    await this.cathieSender.send(CUVEvents.TASK_UPDATE, <CUVData>cuv, false);
                }
                const { membership: _, ...miCuvData } = cuv;
                await this.miStreamCUVSender.send(CUVEvents.MI_CUV, <MICUVData>miCuvData);

                //todo: add interface for contact
                const sendTo = (task.contact as any).telephone;

                if (sendTo) {
                    const smsBody = this.getSMSBody(customerGroup);

                    const delay = 30 * 60 * 1000; // 30 minutes in milliseconds

                    setTimeout(async () => {
                        const smsPayload: CUVSMS = {
                            sendTo,
                            smsBody
                        };

                        await this.smsSender.send(CUVEvents.TASK_UPDATE, <CUVSMS>smsPayload, false);
                    }, delay);
                }

                this.logger.info({
                    sourceName: this.name,
                    message: 'onGenericReportReceived event processed',
                    data: { taskId: data.taskId, status: cuv.status }
                });
            } catch (error) {
                this.logger.error({
                    sourceName: this.name,
                    message: 'onGenericReportReceived event error',
                    data: { error }
                });
            }
        };

    /**
     * Merge existing image ids with potentially new id. Filters based on status, removes duplicates
     * @param {string[]} baseImageIds
     * @param {{status: ImageUpdateStatus, imageId: string}} newImage
     * @return {string[]}
     * @protected
     */
    protected mergeImages(baseImageIds: string[], newImage: { status: ImageUpdateStatus; imageId: string }): string[] {
        const { status, imageId } = newImage;
        let imagesIds: string[] = [...baseImageIds];
        switch (status) {
            // We are adding pending images despite them not being ready
            case ImageUpdateStatus.PENDING:
            case ImageUpdateStatus.ADDED:
                // add image to the existing one
                imagesIds = Utils.unique([...imagesIds, imageId]);
                break;
            case ImageUpdateStatus.DELETED:
                // remove image guid from the array
                imagesIds = imagesIds.filter((guid) => guid !== imageId);
                break;
        }

        return imagesIds;
    }
}

export enum ImageUpdateStatus {
    PENDING = 'PENDING',
    ADDED = 'ADDED',
    DELETED = 'DELETED'
}

interface ImageUpdateEventBody {
    taskId: number;
    status: ImageUpdateStatus;
    imageGuid: string;
}
