import { applicationBasePaths, ErrorResponse } from '@aa/connector';
import { BackendApplication } from '@aa/identifiers';
import { BatteryTest, BatteryTestStatus, OutdoorEvents, RawBatteryResultSummary } from '@aa/data-models/common';
import { B2QTechDataProvider, Bindings, BlobStorageProvider, ContentType, createPlaceholderHTML, DataStoreProviderType, MongodbDataProvider, OracleDataProvider } from '@aa/data-store';
import { EventCode, Exception } from '@aa/exception';
import { HttpMethod, ServerResponseCode } from '@aa/http-client';
import { Microservice } from '@aa/microservice';
import { EventHubCheckpoint, EventHubReceiver, EventHubReceiverConfig, QueueEventHandler } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment, Utils } from '@aa/utils';
import { Request, Response } from 'express';

const appName = 'battery-test';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.BATTERY_TEST;
    protected mongoProvider!: MongodbDataProvider;
    protected b2qProvider!: B2QTechDataProvider;
    protected blobProvider!: BlobStorageProvider;
    protected oracleProvider: OracleDataProvider;
    protected auxStreamReceiver: EventHubReceiver<OutdoorEvents.START_B2Q | OutdoorEvents.COMPLETE_B2Q, BatteryTest, void>;

    constructor(environment: BackendEnvironment) {
        super({
            appName,
            environment,
            dataStoreProviders: [DataStoreProviderType.ORACLE, DataStoreProviderType.MONGODB, DataStoreProviderType.B2QTECH, DataStoreProviderType.BLOBSTORAGE]
        });

        this.mongoProvider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
        this.b2qProvider = this.dataStore.getProvider(DataStoreProviderType.B2QTECH);
        this.blobProvider = this.dataStore.getProvider(DataStoreProviderType.BLOBSTORAGE);
        this.oracleProvider = this.dataStore.getProvider(DataStoreProviderType.ORACLE);

        const checkpoint = new EventHubCheckpoint({
            accountUrl: environment.queue.storageAccountUrl || '',
            accountName: environment.queue.storageAccountName || '',
            accountKey: environment.queue.storageAccountKey || '',
            containerName: 'battery-test',
            logger: this.logger
        });

        const ehBaseConfig: Omit<EventHubReceiverConfig, 'eventHubName' | 'consumerGroup' | 'checkpoint'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            maxBatchSize: 1, // we want to process even 1
            maxDelayPerBatch: 2 // 2s
        };

        this.auxStreamReceiver = new EventHubReceiver({
            ...ehBaseConfig,
            checkpoint,
            eventHubName: 'aux-stream',
            consumerGroup: 'battery-test'
        });

        this.auxStreamReceiver.on(OutdoorEvents.START_B2Q, this.onStartB2QTask);
        this.auxStreamReceiver.on(OutdoorEvents.COMPLETE_B2Q, this.onCompleteB2QTask);

        this.server.get('/history/:vrn', this.getHistoryByVRN, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.get('/result/:taskId', this.getImageByTaskId, {
            allowedRoles: ['outdoor'],
            protect: false
        });

        this.logger.log({
            sourceName: this.name,
            message: 'Battery Test initialized.'
        });
    }

    protected onStartB2QTask: QueueEventHandler<OutdoorEvents.START_B2Q | OutdoorEvents.COMPLETE_B2Q, BatteryTest, void> = async (context) => {
        const { vrn, taskId, patrolId, status } = context.entry.data;
        this.logger.info({
            sourceName: this.name,
            message: `B2Q Start event started for taskId: ${taskId} and vrn: ${vrn}`,
            data: { context }
        });
        try {
            const batteryTestsCollection = await this.mongoProvider.collection('task', 'battery-test-results');
            const temporaryImageHTML = createPlaceholderHTML();
            const streamResult = await this.b2qProvider.createB2QImageAsStream(temporaryImageHTML);

            const blobName = `${taskId}_${vrn}_${Utils.uuid()}`;
            const imageUrl = await this.blobProvider.uploadBlob(streamResult, blobName, ContentType.IMAGE_PNG);
            await batteryTestsCollection.insertOne({
                vrn,
                taskId,
                patrolId,
                status,
                created: new Date(),
                imageUrl
            });

            const sqlSelectStatusCodeId = `
               SELECT TEST_STATUS_CODE_ID
               FROM MSDSDBA.BATTERY_TEST_STATUS_CODE
               WHERE TEST_STATUS_CODE_CODE = :status
            `;

            const bindingsSelectStatusCodeId: Bindings = {
                status
            };
            const statusCodeId = (await this.oracleProvider.execute(sqlSelectStatusCodeId, bindingsSelectStatusCodeId)) as Array<{ TEST_STATUS_CODE_ID: number }>;

            const sql = `
                INSERT INTO MSDSDBA.BATTERY_TEST_STATUS (
                    TASK_ID,
                    VEHICLE_REG_NO,
                    IMAGE_URL,
                    TEST_STATUS_CODE_ID,
                    FETCH_RESULT
                )
                VALUES (
                    :taskId,
                    :vrn,
                    :imageUrl,
                    :statusCodeId,
                    :fetchResult
                )
            `;

            const bindings: Bindings = {
                taskId,
                vrn,
                imageUrl,
                statusCodeId: statusCodeId[0].TEST_STATUS_CODE_ID,
                fetchResult: 404
            };

            await this.oracleProvider.execute(sql, bindings);

            this.logger.info({
                sourceName: this.name,
                message: `B2Q Start event finished for taskId: ${taskId} and vrn: ${vrn}`,
                data: { context }
            });
        } catch (error) {
            throw new Exception({
                message: `Error while sending B2Q start event for taskId: ${taskId} and vrn: ${vrn}`,
                data: { context },
                error
            });
        }
    };

    protected onCompleteB2QTask: QueueEventHandler<OutdoorEvents.START_B2Q | OutdoorEvents.COMPLETE_B2Q, BatteryTest, void> = async (context) => {
        const { vrn, taskId, patrolId, status } = context.entry.data;
        this.logger.info({
            sourceName: this.name,
            message: `B2Q Complete event started for taskId: ${taskId} and vrn: ${vrn}`,
            data: { context }
        });

        try {
            const batteryTestsCollection = await this.mongoProvider.collection('task', 'battery-test-results');
            const batteryTest: BatteryTest = (await batteryTestsCollection.findOne({
                taskId
            })) as unknown as BatteryTest;

            if (!batteryTest) {
                return;
            }

            await batteryTestsCollection.findOneAndReplace(
                { taskId },
                {
                    vrn,
                    taskId,
                    patrolId,
                    status,
                    created: batteryTest.created,
                    completed: new Date(),
                    imageUrl: batteryTest.imageUrl
                }
            );

            const sqlSelectStatusCodeId = `
                        SELECT TEST_STATUS_CODE_ID
                        FROM MSDSDBA.BATTERY_TEST_STATUS_CODE
                        WHERE TEST_STATUS_CODE_CODE = :status
            `;

            const bindingsSelectStatusCodeId: Bindings = {
                status
            };

            const statusCodeId = (await this.oracleProvider.execute(sqlSelectStatusCodeId, bindingsSelectStatusCodeId)) as Array<{ TEST_STATUS_CODE_ID: number }>;

            const sql = `
                UPDATE
                    MSDSDBA.BATTERY_TEST_STATUS
                SET
                    TEST_STATUS_CODE_ID = :statusCodeId
                WHERE
                    TASK_ID = :taskId
            `;

            const bindings: Bindings = {
                taskId,
                statusCodeId: statusCodeId[0].TEST_STATUS_CODE_ID
            };

            await this.oracleProvider.execute(sql, bindings);

            const onSiteDomain = this.environment.connectivity.onSiteDomain;
            const scheduleString = '/schedule';
            const resourceURL = `${onSiteDomain}${applicationBasePaths[BackendApplication.BATTERY_TEST_MONITOR]}${scheduleString}`;
            const body = {
                vrn,
                taskId,
                patrolId
            };

            await this.httpClient.fetch({
                url: resourceURL,
                method: HttpMethod.POST,
                body,
                authenticate: false
            });

            this.logger.info({
                sourceName: this.name,
                message: `B2Q Complete event finished for taskId: ${taskId} and vrn: ${vrn}`,
                data: { context }
            });
        } catch (error) {
            throw new Exception({
                error,
                sourceName: this.name,
                code: EventCode.MOD_CREATE_FAIL
            });
        }
    };

    protected getImageByTaskId = async (
        req: Request<{
            taskId: number;
        }>,
        res: Response
    ) => {
        const imageGenerationStatusesComplete = [BatteryTestStatus.BT_TESTS_IMAGE_GEN, BatteryTestStatus.BT_TESTS_COMP_MI];
        const taskId = Number(req.params.taskId);

        try {
            if (!taskId) {
                return getResponse(res, ServerResponseCode.BAD_REQUEST);
            }

            const batteryHistoryCollection = await this.mongoProvider.collection<BatteryTest>('task', 'battery-test-results');
            const result = await batteryHistoryCollection.find({ taskId }).project({ _id: 0 }).toArray();

            if (result.length === 0) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            let response = {
                fulfilled: false,
                imageUrl: result[0].imageUrl
            };

            if (imageGenerationStatusesComplete.includes(result[0].status)) {
                response = {
                    fulfilled: true,
                    imageUrl: result[0].imageUrl
                };
            }

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Couldn't get image result for taskId: ${taskId}`
            });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    protected getHistoryByVRN = async (
        req: Request<{
            vrn: string;
        }>,
        res: Response
    ) => {
        const { vrn } = req.params;

        try {
            if (!vrn) {
                return getResponse(res, ServerResponseCode.BAD_REQUEST);
            }

            const batteryHistoryCollection = await this.mongoProvider.collection<BatteryTest>('task', 'battery-test-results');
            const response = await batteryHistoryCollection.find({ vrn }).project({ _id: 0 }).toArray();

            if (response.length === 0) {
                return getResponse(res, ServerResponseCode.NOT_FOUND, []);
            }

            let result: RawBatteryResultSummary[] = [];

            for (let i = 0; i < response.length; i++) {
                if (response[i].results) {
                    const tempResult = {
                        dateTime: response[i].results.r_rdatetime,
                        stateOfCharge: response[i].results.r_SoCPerc,
                        stateOfHealth: response[i].results.r_BLPerc,
                        chargingSystemPassOutcome: response[i].results.r_CST === 0 ? 'Unknown' : response[i].results.r_CST === 1 || response[i].results.r_CST === 2 ? 'Pass' : 'Fail',
                        crankingHealth: response[i].results.r_CHPerc,
                        batteryModel: `${response[i].results.b_brand} ${response[i].results.b_model}`,
                        batterySpecification: this.getBatterySpecificationById(response[i].results.b_battery_type)
                    };
                    result = [...result, tempResult];
                }
            }
            result = result.sort((a, b) => (b.dateTime > a.dateTime ? 1 : -1));

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: `B2Q history failed for ${vrn}`
            });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    protected getBatterySpecificationById(id: number): string {
        switch (id) {
            case 0:
                return 'Wet';
            case 1:
                return 'Mf';
            case 2:
                return 'agm spiral';
            case 3:
                return 'agm flat';
            case 4:
                return 'Gel';
            case 5:
                return '[Depricated]';
            case 6:
                return 'efb startstop';
            default:
                return 'Unknown';
        }
    }
}
