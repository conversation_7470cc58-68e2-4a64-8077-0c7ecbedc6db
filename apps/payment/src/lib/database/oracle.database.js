const aaOracleUtility = require('@aa/oracle-utilities'),
    oracledb = require('oracledb');
oracledb.poolIncrement = 1;

const appName = 'payment';
module.exports = {
    init: () => {
        // connectStrings: process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') :
        //         ['vulexa-scan:1521/AAHLPT_P'],
        return aaOracleUtility.init({
            connectStrings: process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : ['vulexa-scan:1521/AAHLPT_P'],
            user: process.env.cshUser || 'vanda',
            password: process.env.cshPassword || 'va.77.av',
            appName
        });
    },
    connect: () => {
        console.log('connecting');
        return aaOracleUtility.connect();
    },
    disconnect: (db) => {
        console.log('releasing');
        if (db) {
            aaOracleUtility.release(db);
        }
    }
};
