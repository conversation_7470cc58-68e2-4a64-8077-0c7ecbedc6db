import { DataStoreProviderType, MongodbDataProvider } from '@aa/data-store';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { BackendEnvironment } from '@aa/utils';
import { getResponse } from '@aa/server-utils';
import { Request, Response } from 'express';
import { MongodbUtils } from '@aa/data-store-utils';
import { Exception } from '@aa/exception';
import { Note, PaginationQueryResult, PaginationQuery, AuditEvent, TripContact } from '@aa/data-models/common';
import { ObjectId } from 'mongodb';

const appName = 'note-api';
// TypeScript type definitions

type DeepPartial<T> = {
    [P in keyof T]?: DeepPartial<T[P]>;
};

type LogbookQueryPayload = PaginationQuery &
    DeepPartial<TripLogbookEntry> & {
        onlyNotes?: boolean;
    };

type TripLogbookEntry = AuditEvent | Note;

// TODO:to remove & refactor this, duplicate
type LogbookQueryResponse = ExtendedPaginationQueryResult<TripLogbookEntry>;
// TODO: to remove & refactor this, duplicate
interface ExtendedPaginationQueryResult<T> {
    result: T[];
    pagination: {
        totalItems: number;
        totalPages: number;
        currentPage: number;
        pageSize: number;
    };
}

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.NOTE_API;
    protected mongoProvider!: MongodbDataProvider;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.MONGODB, DataStoreProviderType.REDIS]
        });

        this.mongoProvider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);

        this.server.get('/note/:id', this.getNote);
        this.server.post('/note', this.insertNote);
        this.server.post('/note/:id', this.updateNote);
        this.server.post('/notes', this.getNotes);
    }

    protected getNote = async (req: Request<{ id: string }, unknown, unknown>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'getNote',
                data: { id: req.params.id }
            });

            const _id = req.params.id;

            const collection = await this.mongoProvider.collection<Note>('entities', 'notes');

            const note = await collection.findOne({ _id: new ObjectId(_id) });

            return getResponse(res, ServerResponseCode.OK, note);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'getNote',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /**
     * Get trip by tripCode
     * @param {Request} req
     * @param {Response<WithId<Note>>} res
     * @return {Promise<void>}
     */
    protected getNotes = async (req: Request<unknown, unknown, QueryNotesBody, { limit?: string; skip?: string }>, res: Response<PaginationQueryResult<Note>>): Promise<void> => {
        try {
            const noteType: QueryNotesBody = req.body;
            const limit = parseInt(req.query.limit || '100', 10);
            const skip = parseInt(req.query.skip || '0', 10);
            this.logger.log(`Getting all Notes`);
            this.logger.info({
                sourceName: this.name,
                message: `Received `,
                data: { ...noteType }
            });

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Note>('entities', 'notes');

            const notes = collection.find<Note>(noteType);
            const query: PaginationQuery = { limit, skip };
            const result = await MongodbUtils.paginate(notes, query, 100);

            if (result && result.results.length) {
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting notes`,
                data: {}
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected insertNote = async (req: Request<unknown, unknown, Note>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'insertNote',
                data: { body: req.body }
            });

            const note = req.body as Note;

            const collection = await this.mongoProvider.collection<Note>('entities', 'notes');

            const result = await collection.insertOne(note);
            const insertedId = result.insertedId;
            const insertedNote = { ...note, _id: insertedId };

            return getResponse(res, ServerResponseCode.OK, insertedNote);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'insertNote',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    protected updateNote = async (req: Request<{ id: string }, unknown, Note>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'updateNote',
                data: { body: req.body, id: req.params.id }
            });

            const id = req.params.id;
            const note = req.body as Note;

            const collection = await this.mongoProvider.collection<Note>('entities', 'notes');

            await collection.updateOne(
                {
                    _id: new ObjectId(id)
                },
                {
                    $set: note
                }
            );

            const insertedNote = { ...note, _id: id };

            return getResponse(res, ServerResponseCode.OK, insertedNote);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'updateNote',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };
}

type QueryNotesBody = Partial<Omit<Note, 'title' | 'content'>> & PaginationQuery;
