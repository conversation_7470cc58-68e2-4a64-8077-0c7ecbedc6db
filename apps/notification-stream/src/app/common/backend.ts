import { applicationBasePaths } from '@aa/connector';
import { Notification, NotificationEventType } from '@aa/data-models/events/notification-event';
import { DataStoreProviderType } from '@aa/data-store';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubSender, EventHubSenderConfig } from '@aa/queue';
import { environment } from '@aa/system';
import { BackendEnvironment } from '@aa/utils';

class Backend extends Microservice {
    public name: string;
    public application: BackendApplication;
    public notificationStreamSender: EventHubSender<NotificationEventType, Notification>;

    constructor(appName: string, environment: BackendEnvironment, application: BackendApplication, dataStoreProviders = [DataStoreProviderType.MONGODB]) {
        super({
            environment,
            appName,
            dataStoreProviders
        });
        this.name = appName;
        this.application = application;

        const configBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };

        // TODO: below is not an efficient way, we should follow on example of EventEmitterV2
        this.notificationStreamSender = new EventHubSender({
            ...configBase,
            eventHubName: 'notification-stream'
        });
    }
}

environment.server.port = parseInt(process.env.notificationStreamServicePort || process.env.PORT || '') || 7833;
environment.server.basePath = process.env.notificationStreamServiceEndPoint || applicationBasePaths.NOTIFICATION_STREAM;

export const backend = new Backend('notification-stream', environment, BackendApplication.NOTIFICATION_STREAM, [DataStoreProviderType.MONGODB, DataStoreProviderType.REDIS]);
