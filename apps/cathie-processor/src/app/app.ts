import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>s, CUVStatus } from '@aa/data-models/common';
import { CathiePayload, DataStoreProviderType } from '@aa/data-store';
import { Exception } from '@aa/exception';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { QueueEventHandler, ServiceBusReceiver, ServiceBusReceiverConfig, ServiceBusSender, ServiceBusSenderConfig } from '@aa/queue';
import { BackendEnvironment } from '@aa/utils';

const appName = 'cathie-processor';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.CATHIE_PROCESSOR;
    protected taskCompletionSender: ServiceBusSender<CUVEvents.UPDATE_DOCUMENT, CUVData>;
    protected taskUpdateReceiver: ServiceBusReceiver<CUVEvents.TASK_UPDATE, CUVData, void>;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.REDIS, DataStoreProviderType.MONGODB, DataStoreProviderType.CATHIE, DataStoreProviderType.ORACLE]
        });

        const sbqBaseConfigSender: Omit<ServiceBusSenderConfig, 'queueName'> = {
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            connectionString: environment.queue.legacySBQConnectionString
        };

        const sbqBaseConfigReceiver: Omit<ServiceBusReceiverConfig, 'queueName'> = {
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            connectionString: environment.queue.SBQConnectionString,
            hasDeadletter: true
        };

        //todo: set proper config
        // from cuv-processor
        this.taskUpdateReceiver = new ServiceBusReceiver({
            ...sbqBaseConfigReceiver,
            queueName: 'cathie-queue'
        });
        this.taskUpdateReceiver.on(CUVEvents.TASK_UPDATE, this.onTaskUpdateReceived);

        // to aa-edocs-service
        this.taskCompletionSender = new ServiceBusSender({
            ...sbqBaseConfigSender,
            queueName: 'document-upload'
        });

        //this is for dev test only and will be removed
        this.server.get('/token', async (req, res) => {
            try {
                const cathieProvider = this.dataStore.getProvider(DataStoreProviderType.CATHIE);
                const token = await cathieProvider.oAuth2Token();
                res.send({ token });
            } catch (error) {
                res.send({ error });
            }
        });

        //this is for dev test only and will be removed
        this.server.post('/update-policy', async (req, res) => {
            try {
                const cathieProvider = this.dataStore.getProvider(DataStoreProviderType.CATHIE);
                const result = await cathieProvider.updatePolicy(req.body);

                //typical successful response:
                // {
                //     "Code": "200",
                //     "ServiceCaseID": "PM-6010",
                //     "Status": "Success",
                //     "CorrespondenceList": [
                //         {
                //             "Channel": "EMAIL",
                //             "DocumentDescription": "CUV Day 0 Post call-out",
                //             "DocumentGUID": "7b62d02b-4126-4e93-8c7d-ea973c42b84f",
                //             "DocumentId": "RO_006_PH"
                //         },
                //         {
                //             "Channel": "PAPER",
                //             "DocumentDescription": "CUV Day 0 Post call-out",
                //             "DocumentGUID": "88c6d496-7521-4518-a0f8-962a2b6cd84a",
                //             "DocumentId": "RO_006_PH"
                //         }
                //     ]
                // }

                res.send(result);
            } catch (error) {
                res.send({ error });
            }
        });
    }

    /*
    cathie-processor
        - listens on cathie-queue svcbus: onTaskUpdateReceived from cuv-processor
            - calls cathieProvider.updatePolicy
            - publishes to the document-upload svcbus UPDATE_DOCUMENT event downstream to aa-edocs-service


*/

    protected onTaskUpdateReceived: QueueEventHandler<CUVEvents.TASK_UPDATE, CUVData, void> = async (context) => {
        try {
            const cathieProvider = this.dataStore.getProvider(DataStoreProviderType.CATHIE);

            const {
                entry: { data }
            } = context;
            this.logger.info({
                sourceName: this.name,
                message: 'On complete event received',
                data: { data }
            });

            //we need to check if status is REVOKED and send NONE instead because cathie does not know what REVOKED is
            const CardType = data.status === CUVStatus.REVOKED ? CUVStatus.NONE : data.status;

            const payload: CathiePayload = {
                PolicyNumber: data.membership,
                RegNumber: data.vrn,
                CardType,
                CardUpdateDate: <string>data.updated
            };

            this.logger.info({
                sourceName: this.name,
                message: 'Cathie updatePolicy payload',
                data: { payload }
            });
            const result = await cathieProvider.updatePolicy(payload);
            this.logger.info({
                sourceName: this.name,
                message: 'Cathie updatePolicy result received',
                data: { result }
            });

            //our I/F mentions documentId. But the tickets and HLD mention documentGuid. I'm going with documentGuid
            if (Array.isArray(result.CorrespondenceList) && result.CorrespondenceList.length > 0) {
                const guids = result.CorrespondenceList.map((element: any) => element.DocumentGUID);

                guids.forEach((DocumentGUID: string) => {
                    const payload: CUVData = {
                        ...data,
                        documentGuid: DocumentGUID
                    };
                    this.taskCompletionSender.sendRaw(payload);
                });
            }
        } catch (error) {
            throw new Exception({
                message: 'Error while receiveng event',
                data: { context },
                error
            });
        }
    };
}
