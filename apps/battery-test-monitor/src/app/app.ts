import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { BackendEnvironment, Utils } from '@aa/utils';
import { B2QTechDataProvider, Bindings, BlobStorageProvider, ContentType, DataStoreProviderType, MongodbDataProvider, OracleDataProvider } from '@aa/data-store';
import { BatteryTest, BatteryTestStatus, ConfigCode, isValidOutdoorB2Q, OutdoorEvents, OutdoorTaskPayload, RawBatteryResults, RetryStrategy } from '@aa/data-models/common';
import { Request, Response } from 'express';
import { EventCode, Exception } from '@aa/exception';
import { getResponse } from '@aa/server-utils';
import { ServerResponseCode } from '@aa/http-client';
import { ErrorResponse } from '@aa/connector';
import { RemoteQueue } from '@aa/remote-queue';
import { EventHubSender, EventHubSenderConfig } from '@aa/queue';

const appName = 'battery-test-monitor';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.BATTERY_TEST_MONITOR;
    protected mongoProvider!: MongodbDataProvider;
    protected b2qProvider!: B2QTechDataProvider;
    protected blobProvider!: BlobStorageProvider;
    protected oracleProvider!: OracleDataProvider;
    protected headerCodes!: ConfigCode[];
    protected queue!: RemoteQueue<Actions, { vrn: string; taskId: number; patrolId: number }>;
    protected miStreamB2QSender: EventHubSender<OutdoorEvents.MI_B2Q, OutdoorTaskPayload>;

    constructor(environment: BackendEnvironment) {
        super({
            appName,
            environment,
            dataStoreProviders: [DataStoreProviderType.MONGODB, DataStoreProviderType.B2QTECH, DataStoreProviderType.BLOBSTORAGE, DataStoreProviderType.ORACLE]
        });

        const configBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };

        this.miStreamB2QSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });

        this.initialize();
    }

    protected schedule = async (req: Request<unknown, unknown, OutdoorTaskPayload>, res: Response) => {
        try {
            await this.queue.schedule(Actions.B2Q_CHECK, req.body, {
                scheduledAt: Utils.generateFutureDate('5m')
            });
            return getResponse(res, ServerResponseCode.ACCEPTED);
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: `Failed to schedule taskId: ${req.body.taskId} for vrn: ${req.body.vrn}`
            });
        }
    };

    protected processing = async (event: Actions, entry: { vrn: string; taskId: number; patrolId: number }) => {
        try {
            await this.processBatteryTest(entry);
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                message: `Failed to process battery test results for taskId: ${entry.taskId} for vrn: ${entry.vrn}`
            });
        }
    };

    protected processBatteryTest = async (entry: { vrn: string; taskId: number; patrolId: number }) => {
        const { vrn, taskId, patrolId } = entry;

        // Bail out if no vrn or taskId or patrolId
        if (!isValidOutdoorB2Q(entry)) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                message: `Failed to process battery test results because data wasn't valid for taskId: ${taskId} for vrn: ${vrn}`
            });
        }

        const batteryTestsCollection = await this.mongoProvider.collection('task', 'battery-test-results');
        const batteryTest: BatteryTest = (await batteryTestsCollection.findOne({
            taskId
        })) as unknown as BatteryTest;
        if (!batteryTest) {
            this.logger.log({
                sourceName: this.name,
                message: `Battery test was not found in our database for taskId: ${taskId} Retrying!`
            });
            return;
        }

        const rawBatteryResults = await this.b2qProvider.getB2QCSVData(taskId);
        if (!rawBatteryResults) {
            batteryTest.status = BatteryTestStatus.BT_TESTS_NA;
            await batteryTestsCollection.findOneAndReplace(
                { taskId },
                {
                    taskId,
                    vrn,
                    patrolId,
                    created: batteryTest.created,
                    completed: batteryTest.completed,
                    status: batteryTest.status,
                    results: 404
                },
                { upsert: true }
            );
            this.logger.log({
                sourceName: this.name,
                message: `Battery test could not be fetched for taskId: ${taskId} Retrying!`
            });
            return;
        }

        batteryTest.status = BatteryTestStatus.BT_TESTS_RECVD;

        await batteryTestsCollection.findOneAndReplace(
            { taskId },
            {
                taskId,
                vrn,
                patrolId,
                created: batteryTest.created,
                completed: batteryTest.completed,
                imageUrl: batteryTest.imageUrl,
                status: batteryTest.status,
                rawB2QData: rawBatteryResults
            }
        );

        const sqlSelectStatusCodeId = `
              SELECT TEST_STATUS_CODE_ID
              FROM MSDSDBA.BATTERY_TEST_STATUS_CODE
              WHERE TEST_STATUS_CODE_CODE = :status
            `;

        let bindingsSelectStatusCodeId: Bindings = {
            status: batteryTest.status
        };

        let statusCodeId = (await this.oracleProvider.execute(sqlSelectStatusCodeId, bindingsSelectStatusCodeId)) as Array<{ TEST_STATUS_CODE_ID: number }>;

        const sql = `
            UPDATE
                MSDSDBA.BATTERY_TEST_STATUS
            SET
                TEST_STATUS_CODE_ID = :statusCodeId
            WHERE
                TASK_ID = :taskId
        `;

        const bindings: Bindings = {
            statusCodeId: statusCodeId[0].TEST_STATUS_CODE_ID,
            taskId
        };

        await this.oracleProvider.execute(sql, bindings);

        this.logger.log({
            sourceName: this.name,
            message: `Battery test found for taskId: ${taskId} and updated the mongo and oracle with details`
        });

        const batteryTestResultsJSON = this.b2qProvider.CSVToJSON(rawBatteryResults);
        const htmlContentForB2Q = await this.b2qProvider.createHTMLFromCSV(rawBatteryResults, this.headerCodes);
        const streamResult = await this.b2qProvider.createB2QImageAsStream(htmlContentForB2Q);

        const urlParts = batteryTest.imageUrl ? batteryTest.imageUrl.split('/') : `${taskId}_${vrn}_${Utils.uuid()}`;
        const blobName = urlParts[urlParts.length - 1];

        await this.blobProvider.uploadBlob(streamResult, `${blobName}`, ContentType.IMAGE_PNG);

        batteryTest.status = BatteryTestStatus.BT_TESTS_IMAGE_GEN;
        batteryTest.results = batteryTestResultsJSON[0];

        await batteryTestsCollection.findOneAndReplace(
            { taskId },
            {
                taskId,
                vrn,
                patrolId,
                created: batteryTest.created,
                completed: batteryTest.completed,
                imageUrl: batteryTest.imageUrl,
                status: batteryTest.status,
                results: batteryTest.results,
                rawB2QData: rawBatteryResults,
                allResults: batteryTestResultsJSON[1]
            }
        );

        bindingsSelectStatusCodeId = {
            status: batteryTest.status
        };

        statusCodeId = (await this.oracleProvider.execute(sqlSelectStatusCodeId, bindingsSelectStatusCodeId)) as Array<{ TEST_STATUS_CODE_ID: number }>;

        const sqlAfterGeneration = `
            UPDATE
                MSDSDBA.BATTERY_TEST_STATUS
            SET
                FETCH_RESULT = :fetchStatus,
                TEST_STATUS_CODE_ID = :statusCodeId
            WHERE
                TASK_ID = :taskId
        `;

        const bindingsForGeneration: Bindings = {
            fetchStatus: 200,
            statusCodeId: statusCodeId[0].TEST_STATUS_CODE_ID,
            taskId
        };
        await this.oracleProvider.execute(sqlAfterGeneration, bindingsForGeneration);

        this.logger.log({
            sourceName: this.name,
            message: `Battery test image generated for taskId: ${taskId} and updated the mongo and oracle with details`
        });

        await this.miStreamB2QSender.send(OutdoorEvents.MI_B2Q, this.transformDataForMI(batteryTest, batteryTestResultsJSON[1]));

        batteryTest.status = BatteryTestStatus.BT_TESTS_COMP_MI;

        await batteryTestsCollection.findOneAndReplace(
            { taskId },
            {
                taskId,
                vrn,
                patrolId,
                created: batteryTest.created,
                completed: batteryTest.completed,
                imageUrl: batteryTest.imageUrl,
                status: batteryTest.status,
                results: batteryTest.results,
                rawB2QData: rawBatteryResults,
                allResults: batteryTestResultsJSON[1]
            }
        );

        bindingsSelectStatusCodeId = {
            status: batteryTest.status
        };

        statusCodeId = (await this.oracleProvider.execute(sqlSelectStatusCodeId, bindingsSelectStatusCodeId)) as Array<{ TEST_STATUS_CODE_ID: number }>;

        const sqlQueryForMI = `
            UPDATE
                MSDSDBA.BATTERY_TEST_STATUS
            SET
                TEST_STATUS_CODE_ID = :statusCodeId
            WHERE
                TASK_ID = :taskId
        `;

        const bindingsForMI: Bindings = {
            statusCodeId: statusCodeId[0].TEST_STATUS_CODE_ID,
            taskId
        };
        await this.oracleProvider.execute(sqlQueryForMI, bindingsForMI);
        this.logger.log({
            sourceName: this.name,
            message: `Battery test data send to MI for taskId: ${taskId} and updated the mongo and oracle with details`
        });
    };

    protected transformDataForMI(finalResult: BatteryTest, allTestData: RawBatteryResults[]) {
        try {
            const data = {
                taskId: finalResult.taskId,
                vrn: finalResult.vrn,
                patrolId: finalResult.patrolId,
                imageUrl: finalResult.imageUrl,
                status: finalResult.status,
                created: finalResult.created,
                completed: finalResult.completed,
                result: finalResult.results,
                allResults: allTestData
            };
            return data;
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_FAIL,
                error,
                message: `Failed to process battery test MI data for taskId: ${finalResult.taskId} for vrn: ${finalResult.vrn}`
            });
        }
    }

    protected forceCheck = async (req: Request<unknown, unknown, OutdoorTaskPayload>, res: Response) => {
        const { vrn, taskId, patrolId } = req.body;

        // Bail out if no vrn or taskId or patrolId
        if (!isValidOutdoorB2Q(req.body)) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                message: `Failed to process battery test results for taskId: ${taskId} for vrn: ${vrn}`
            });
        }

        try {
            await this.processBatteryTest(req.body);
            return getResponse(res, ServerResponseCode.OK);
        } catch (error) {
            const exception = new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: `Failed to process battery test results for taskId: ${taskId} for vrn: ${vrn}`
            });
            this.logger.error(exception);
            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };
            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    protected initialize = async () => {
        this.mongoProvider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
        this.b2qProvider = this.dataStore.getProvider(DataStoreProviderType.B2QTECH);
        this.blobProvider = this.dataStore.getProvider(DataStoreProviderType.BLOBSTORAGE);
        this.oracleProvider = this.dataStore.getProvider(DataStoreProviderType.ORACLE);

        const headerCodesCollection = await this.mongoProvider.collection('system-config', 'bt-codes');
        this.headerCodes = (await headerCodesCollection.find().toArray()) as unknown as ConfigCode[];

        const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
        this.queue = new RemoteQueue({
            provider,
            logger: this.logger,
            system: this.system,
            collectionName: 'b2q-result-processing',
            dbName: 'queue',
            totalSlots: 5,
            retryConfig: {
                strategy: RetryStrategy.INTERVAL,
                interval: '10m',
                duration: '3d'
            }
        });

        await this.queue.start();

        this.queue.on(Actions.B2Q_CHECK, this.processing);

        this.server.post('/schedule', this.schedule, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.post('/force-check', this.forceCheck, {
            allowedRoles: ['outdoor'],
            protect: false
        });

        this.logger.log({
            sourceName: this.name,
            message: 'Battery Test Monitor initialized.'
        });
    };
}

enum Actions {
    B2Q_CHECK = 'B2Q_CHECK'
}
