'use strict';

const Q = require('q');

const path = require('path');
process.env['PATH'] = path.join(__dirname, '/instantclient-sdk') + ';' + process.env['PATH'];
const aaOracle = require('@aa/oracle-utilities'),
    oracledb = require('oracledb');

const user = process.env.cshUser,
    password = process.env.cshPassword,
    connectStrings = process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : ['sun54:1521/VACSH'],
    appName = 'edocs';

oracledb.fetchAsString = [oracledb.CLOB];

module.exports = {
    init: () => {
        return aaOracle.init({
            user,
            password,
            connectStrings,
            appName
        });
    },
    connect: () => aaOracle.connect(),
    release: (db) => aaOracle.release(db),
    cursorRead: (dbConn, sql, bindvars) => {
        const defer = Q.defer();

        dbConn.execute(
            sql,
            bindvars,
            {
                outFormat: oracledb.OBJECT
            },
            function (err, result) {
                let resultStream = null,
                    results = [];

                if (err) {
                    aaOracle.release(dbConn);
                    defer.reject(err.message);

                    return;
                }
                resultStream = result.outBinds.cursor.toQueryStream();
                resultStream.on('error', function (error) {
                    aaOracle.release(dbConn);
                    defer.reject(error);
                });
                resultStream.on('data', function (data) {
                    results.push(data);
                });
                resultStream.on('end', function () {
                    defer.resolve(results);
                });
            }
        );
        return defer.promise;
    }
};
