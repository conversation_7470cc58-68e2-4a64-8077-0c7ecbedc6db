const docSvc = require('../services/document.service');
const cshEdocsSvc = require('../services/csh-eDocs.service');
const cshEdoconst = require('../constants/events-bus-queue.constant');
const cshCustomerGrpConst = require('../constants/customer-group.constant');
const fs = require('fs');
const logger = require('winston');
const { AAAzureStorage } = require('@aa/azure-storage');
const Q = require('q');
const _WEB_OPERATOR_ID = 9107292,
    securityFeatures = require('@aa/security-checker').security;
const notifyEdocsSvc = require('./notify-eDocs.service');
const cert = require('@aa/security-checker').certificates;

const authToken = {
    jwt: securityFeatures.createSecureToken(
        {
            userName: 'patrol',
            operatorId: _WEB_OPERATOR_ID,
            roleMask: '', // ideally we need a default role mask
            availableRoles: []
        },
        cert.privateKey
    ),
    operatorId: _WEB_OPERATOR_ID
};

var docConfig = {
    aaAzureStorage: process.env.aaDocAzureStorage || 'roadopsvehicleleasingint',
    aaAzureStorageAccessKey: process.env.aaDocAzureStorageAccessKey || '65zAS+Q6NVcZW6sYgMGl7ltOylZCcve6wWLOYS8PmS5sa/C9MZBfkhT16Sbxmf5tb/WPdh9aSeWc+AStFExZJQ==',
    aaAzureBlobContainer: process.env.aaDocAzureBlobContainer || 'jlr'
};

function hasPdfExtension(filename) {
    if (filename.indexOf('.pdf') !== -1) {
        return true;
    }
    return false;
}

function deleteLocalFile(localfile) {
    if (fs.existsSync(localfile)) {
        try {
            fs.unlinkSync(localfile);

            logger.info('Delete File successfully.' + localfile);
        } catch (error) {
            logger.info(error);
        }
    }
}

function processDocument(data) {
    let documentDetails = {};
    if (hasPdfExtension(data.DOCUMENT_ID) || true) {
        let localFileName = `${process.env.HOME}/tmp/` + data.DOCUMENT_ID + '.pdf';
        documentDetails.localFileName = localFileName;
        documentDetails.blobName = data.DOCUMENT_ID;
        documentDetails.processDocument = true;
        documentDetails.attachments = [
            {
                path: localFileName,
                filename: 'document.pdf'
            }
        ];
    }
    return documentDetails;
}

async function base64toBlob(path) {
    let data = fs.readFileSync(path, 'utf8');
    data = data.substr('data:application/pdf;base64,'.length);
    let buff = Buffer.from(data, 'base64');
    await fs.writeFileSync(path, buff);
}

module.exports = {
    sendRejectionEmail: function (body) {
        const emailSubject = process.env.rejectExtenionEmailSubject ? process.env.rejectExtenionEmailSubject : 'Extension Rejected';
        let promises = [];
        let emailsArr = body.emails.split(',');
        let customerGroup = body.customerGroup ? body.customerGroup : '';
        let html = '';
        if (customerGroup === 'POR') {
            html = docSvc.getEmailHtml(body, '../templates/reject-extension-por-email-template.hbs');
        } else if (customerGroup === cshCustomerGrpConst.CUSTOMERGROUP.HYUNDAI) {
            html = docSvc.getEmailHtml(body, '../templates/reject-extension-hyu-email-template.hbs');
        } else {
            html = docSvc.getEmailHtml(body, '../templates/reject-extension-email-template.hbs');
        }

        for (var i = 0; i < emailsArr.length; i++) {
            promises.push(docSvc.email(emailsArr[i], html, emailSubject, null));
        }
        return Q.all(promises);
    },
    sendRebateEmail: function (body) {
        let processData = body;
        let assocTaskId = body.Content.assocTaskId;
        let taskId = body.Content.taskId;
        let customerGroup = body.customerGroup ? body.customerGroup : '';
        let html = '';
        let emailSubject = process.env.rebateEmailSubject ? process.env.rebateEmailSubject : 'Refund Notification';
        const deferred = Q.defer();
        //need to retrive hireTaskId as assocTaskId for refund will be parent task financial taskId
        cshEdocsSvc
            .getCardDetails(taskId)
            .then((cardDataResult) => {
                let cardData = cardDataResult.length ? cardDataResult[0] : null;
                if (cardData !== null) {
                    processData.cardDetails = JSON.parse(cardData.CARD_DETAILS);
                    processData.miscDetails = JSON.parse(cardData.MISCELLANEOUS_DETAILS);
                    processData.cardData = cardData;
                    return cardData;
                }
                return deferred.reject("email can't be sent as card data not available");
            })
            .then((cardData) => {
                return docSvc.readTask(cardData.ASSOC_TASK_ID, authToken);
            })
            .then((task) => {
                let mobilityTask = task.toJSON();
                if (mobilityTask.rental.mainDriver.contact.email) {
                    return mobilityTask;
                } else {
                    deferred.reject('email not available');
                }
            })
            .then((mobilityTask) => {
                processData.mobilityTask = mobilityTask;
                processData.isShowEndDate = mobilityTask.status === 'COMP' || mobilityTask.status === 'CLSD';
                processData.isShowStartDate = mobilityTask.status === 'HIRE' || mobilityTask.status === 'COMP' || mobilityTask.status === 'CLSD';
                if (customerGroup === 'HYU') {
                    html = docSvc.getEmailHtml(processData, '../templates/hyu-payment-refund-email-notification.hbs');
                } else {
                    html = docSvc.getEmailHtml(processData, '../templates/jlr-payment-refund-email-notification.hbs');
                }

                return docSvc.email(processData.mobilityTask.rental.mainDriver.contact.email, html, emailSubject, null);
            })
            .then(() => {
                logger.info(`-------- step1.sendRebateEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -:: call to email service successful and cusomerGroup is- ${customerGroup}`);
                let smsPayload = {
                    phoneNumber: processData.mobilityTask.contact.telephone,
                    text: 'This is to advise that a refund payment will be paid following your recent loan vehicle.Full detail of the amount will be sent via the email address you provided.'
                };
                logger.info(smsPayload);

                if (processData.mobilityTask.contact.telephone) {
                    logger.info(`--------- step2.sendRebateEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -:: telephone number available`);
                    processData.processSms = true;
                    return docSvc.sendSms(smsPayload, authToken);
                }
                return true;
            })
            .then(() => {
                if (processData.hasOwnProperty('processSms')) {
                    logger.info(`---------- step3.sendRebateEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -:: sms sent successfully`);
                }
                logger.info(`----------- step4.sendRebateEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -:: process completed`);

                deferred.resolve('email went successful');
            })
            .catch((err) => {
                deferred.reject('error in sending email' + err);
            });
        return deferred.promise;
    },
    sendSchedulePaymentEmail: function (body) {
        let azureStorage, html;
        azureStorage = new AAAzureStorage(docConfig);

        let processData = body;
        let assocTaskId = body.savedCardTxn.assocTaskId;
        let taskId = body.savedCardTxn.taskId;
        let emailSubject = process.env.schedulePaymentEmailSubject ? process.env.schedulePaymentEmailSubject : 'Payment Scheduled';
        const deferred = Q.defer();

        //need to retrive hireTaskId as assocTaskId for refund will be parent task financial taskId
        cshEdocsSvc
            .getCardDetails(taskId)
            .then((cardDataResult) => {
                let cardData = cardDataResult.length ? cardDataResult[0] : null;
                if (cardData !== null) {
                    processData.cardDetails = JSON.parse(cardData.CARD_DETAILS);
                    processData.miscDetails = JSON.parse(cardData.MISCELLANEOUS_DETAILS);
                    processData.cardData = cardData;
                    return cardData;
                }
                return deferred.reject("email can't be sent as card data not available");
            })
            .then((cardData) => {
                return docSvc.readTask(assocTaskId, authToken);
            })
            .then((task) => {
                let mobilityTask = task.toJSON();
                if (mobilityTask.rental.mainDriver.contact.email) {
                    logger.info(`-------step1.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -email available`);
                    return mobilityTask;
                } else {
                    logger.info(`---------step1.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -email not available`);
                    deferred.reject('email not available');
                }
            })
            .then((mobilityTask) => {
                processData.mobilityTask = mobilityTask;
                processData.isShowEndDate = mobilityTask.status === 'COMP' || mobilityTask.status === 'CLSD';
                processData.isShowStartDate = mobilityTask.status === 'HIRE' || mobilityTask.status === 'COMP' || mobilityTask.status === 'CLSD';
                return cshEdocsSvc.getSupportingDoc(taskId);
            })
            .then(async (docData) => {
                let result = docData.length ? docData[0] : null;
                if (result !== null) {
                    logger.info(`---------step2.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -doc data available`);
                    processData.docDetails = processDocument(result);
                }

                if (processData.hasOwnProperty('docDetails')) {
                    logger.info(`-------step2.1.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -doc data processed`);
                    let params = {
                        localFileName: processData.docDetails.localFileName,
                        blobName: processData.docDetails.blobName
                    };
                    processData.attachments = processData.docDetails.attachments;
                    if (processData.docDetails.localFileName !== undefined) {
                        logger.info(`-------step2.2.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -create local file`);

                        await azureStorage.createLocalFile(params);
                        return base64toBlob(params.localFileName);
                    }
                }
                processData.attachments = null;
                return true;
            })
            .then(() => {
                if (processData.attachments === null) {
                    logger.info(`--------step3.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -doc data processed`);
                } else {
                    logger.info(`----------step3.1.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -document created`);
                }
                processData.savedCardTxn.amount = processData.savedCardTxn.amount || 0;

                processData.cardDetails.accountNumber = processData.cardDetails.accountNumber.slice(-4);

                if (processData.mobilityTask.entitlement.customerGroup.code == cshCustomerGrpConst.CUSTOMERGROUP.HYUNDAI) {
                    html = docSvc.getEmailHtml(processData, '../templates/hyu-payment-schedule-email-notification-template.hbs');
                } else {
                    html = docSvc.getEmailHtml(processData, '../templates/jlr-payment-schedule-email-notification-template.hbs');
                }
                return docSvc.email(processData.mobilityTask.rental.mainDriver.contact.email, html, emailSubject, processData.attachments);
            })
            .then(() => {
                logger.info(`-------- step4.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -:: call to email service successful`);
                let smsPayload = {
                    phoneNumber: processData.mobilityTask.contact.telephone,
                    text: 'This is to advise that a payment is due following your recent loan vehicle. Full detail of the amount outstanding will be sent via the email address you provided.'
                };
                logger.info(smsPayload);

                if (processData.mobilityTask.contact.telephone) {
                    logger.info(`--------- step5.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -:: telephone number available`);
                    processData.processSms = true;
                    return docSvc.sendSms(smsPayload, authToken);
                }
                return true;
            })
            .then(() => {
                if (processData.hasOwnProperty('processSms')) {
                    logger.info(`---------- step5.1.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -:: sms sent successfully`);
                }
                logger.info(`----------- step6.sendSchedulePaymentEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -:: process completed`);
                if (processData.hasOwnProperty('docDetails')) {
                    deleteLocalFile(processData.docDetails.localFileName);
                }
                deferred.resolve('email went successful');
            })
            .catch((err) => {
                deferred.reject('error in sending email' + err);
            });
        return deferred.promise;
    },
    sendPaymentFailedEmail: function (body) {
        let emailSubject = process.env.paymentFailedEmailSubject ? process.env.paymentFailedEmailSubject : 'Failed Payment';
        let jlremail = process.env.jlrEmail;
        let processData = body;
        let assocTaskId = processData.body.Content.savedCardPayment.assocTaskId;
        let taskId = processData.body.Content.savedCardPayment.taskId;
        let customerGroup = body.customerGroup ? body.customerGroup : '';
        let html = '';
        const deferred = Q.defer();
        //need to retrive hireTaskId as assocTaskId for refund will be parent task financial taskId
        cshEdocsSvc
            .getCardDetails(taskId)
            .then((cardDataResult) => {
                let cardData = cardDataResult.length ? cardDataResult[0] : null;
                if (cardData !== null) {
                    processData.cardData = cardData;
                    return cardData;
                }
                return deferred.reject("email can't be sent as card data not available");
            })
            .then((cardData) => {
                return docSvc.readTask(cardData.ASSOC_TASK_ID, authToken);
            })
            .then((task) => {
                let mobilityTask = task.toJSON();
                if (mobilityTask.rental.mainDriver.contact.email) {
                    logger.info(`-------step1.sendPaymentFailedEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -email available`);
                    return mobilityTask;
                } else {
                    logger.info(`---------step1.sendPaymentFailedEmail- for taskId-${taskId} and  assocTaskId-${assocTaskId} -email not available`);
                    deferred.reject('email not available');
                }
            })
            .then((mobilityTask) => {
                processData.mobilityTask = mobilityTask;
                if (customerGroup === cshCustomerGrpConst.CUSTOMERGROUP.HYUNDAI) {
                    jlremail = '<EMAIL>';
                    html = docSvc.getEmailHtml(processData, '../templates/hyu-payment-failed-email-notification.hbs');
                } else {
                    html = docSvc.getEmailHtml(processData, '../templates/jlr-payment-failed-email-notification.hbs');
                }

                return docSvc.email(jlremail, html, emailSubject, null);
            })
            .then(() => {
                logger.info(`sendPaymentFailedEmail:: call to email service successful`);
                deferred.resolve('email went successful');
            })
            .catch((err) => {
                deferred.reject('error in sending email' + err);
            });
        return deferred.promise;
    },
    sendExtensionAutoEmailSummary: function (body, subject) {
        let emailSubject = `${subject.replace(/_/g, ' ')} - ${body.task[0]?.taskId} `;

        let msg = body.task[0];
        let toEmail;

        if (msg?.customerGrpCode === cshCustomerGrpConst.CUSTOMERGROUP.HYUNDAI) {
            toEmail = process.env.hyundaiEmail;
        } else {
            toEmail = process.env.jlrEmail;
        }

        const deferred = Q.defer();

        let html = docSvc.getEmailHtml(body, '../templates/extension-email-summary-template.hbs');
        docSvc
            .email(toEmail, html, emailSubject, null)
            .then(() => {
                logger.info(`sendExtensionAutoEmailSummary:: call to email service successful`);
                deferred.resolve('email notification sent successfully');
            })
            .catch((err) => {
                deferred.reject('error in sending email' + err);
            });
        return deferred.promise;
    },
    sendOffhireAutoEmailSummary: function (body) {
        /*const { fleetType, customerGrpCode, customerName, customerVrn, hireTaskId, hireVrn, returningResId, returningRetailer } = body*/
        const emailSubject = `Check-in - ${body.hireTaskId}`;

        // ToDo: in future below needs to be expanded to handle suppliers other than Hyundai
        const toEmail = process.env.hyundaiEmail;
        const deferred = Q.defer();

        const html = docSvc.getEmailHtml(body, '../templates/vor-checkin-email-notification.hbs');
        docSvc
            .email(toEmail, html, emailSubject, null)
            .then(() => {
                logger.info(`sendOffhireAutoEmailSummary:: call to email service successful`);
                deferred.resolve('email notification sent successfully');
            })
            .catch((err) => {
                deferred.reject('error in sending email' + err);
            });

        return deferred.promise;
    },
    uploadDocument: function (data) {
        return cshEdocsSvc.updateStatus(data).then(() => {
            // Notify about status change
            return notifyEdocsSvc.notify(data);
        });
    }
};
