const logger = require('winston'),
    { Queue } = require('@aa/azure-queue'),
    EventBusConstant = require('../constants/events-bus-queue.constant'),
    eventsBusQueueActionSvc = require('../services/events-bus-queue.action.service'),
    EventsBusQueueRepository = require('../repositories/events-bus-queue.repository');

let _eventsRepository;

const _handleMessage = (message) => {
    if (!message) {
        logger.info(`events-bus-queue-service: _handleMessage:: empty queue`);
        return;
    }

    const queueName = Queue.EDOCS_EMAIL;
    const subject = message.subject ? message.subject : message.body.subject;

    switch (subject) {
        case EventBusConstant.SUBJECT.REJECTION_EMAIL:
            const body = message.body ? JSON.parse(message.body) : null;

            eventsBusQueueActionSvc
                .sendRejectionEmail(body)
                .then(() => {
                    logger.info(`events-bus-queue-service.sendRejectionEmail:: Success  ${body.vehicle}`);
                })
                .catch((err) => {
                    logger.error(`events-bus-queue-service.sendRejectionEmail:: failed ${body.vehicle} :: ${err.msg}`);
                    _eventsRepository.retry(queueName, message);
                });

            break;
        case EventBusConstant.SUBJECT.REBATE_EMAIL:
            eventsBusQueueActionSvc
                .sendRebateEmail(message.body)
                .then(() => {
                    logger.info(`events-bus-queue-service.sendRebateEmail:: Success  Rebate taskId- ${message.body.Content.taskId} --- assoctaskId  ${message.body.Content.assocTaskId}`);
                })
                .catch((err) => {
                    logger.error(`events-bus-queue-service.sendRebateEmail:: failed  Rebate taskId- ${message.body.Content.taskId} --- assoctaskId  ${message.body.Content.assocTaskId} :: ${err}`);
                    _eventsRepository.retry(queueName, message);
                });

            break;
        case EventBusConstant.SUBJECT.SCHEDULE_PAYMENT:
            eventsBusQueueActionSvc
                .sendSchedulePaymentEmail(message.body)
                .then(() => {
                    logger.info(`events-bus-queue-service.sendSchedulePaymentEmail:: Success   taskId- ${message.body.savedCardTxn.taskId} --- assoctaskId  ${message.body.savedCardTxn.assocTaskId}`);
                })
                .catch((err) => {
                    logger.error(
                        `events-bus-queue-service.sendSchedulePaymentEmail:: failed   taskId- ${message.body.savedCardTxn.taskId} --- assoctaskId  ${message.body.savedCardTxn.assocTaskId} :: ${err}`
                    );
                    _eventsRepository.retry(queueName, message);
                });

            break;
        case EventBusConstant.SUBJECT.PAYMENT_FAILED:
            const failedMessage = message.body;
            eventsBusQueueActionSvc
                .sendPaymentFailedEmail(failedMessage)
                .then(() => {
                    logger.info(
                        `events-bus-queue-service.sendPaymentFailedEmail:: Success  taskId- ${failedMessage.body.Content.savedCardPayment.taskId} --- assoctaskId  ${failedMessage.body.Content.savedCardPayment.assocTaskId}`
                    );
                })
                .catch((err) => {
                    logger.error(
                        `events-bus-queue-service.sendPaymentFailedEmail:: failed   taskId- ${failedMessage.body.Content.savedCardPayment.taskId} --- assoctaskId  ${failedMessage.body.Content.savedCardPayment.assocTaskId} :: ${err}`
                    );
                    _eventsRepository.retry(queueName, message);
                });

            break;

        case EventBusConstant.SUBJECT.DELAYED_CUSTOMER_COLLECTION:
        case EventBusConstant.SUBJECT.RECLAIM:
            const data = JSON.parse(message.body);

            logger.info(`Message for extension summary late hire return ${JSON.stringify(data)}`);
            eventsBusQueueActionSvc
                .sendExtensionAutoEmailSummary(data, subject)
                .then(() => {
                    logger.info(`events-bus-queue-service.sendExtensionAutoEmailSummary:: Success - ${data.task[0]?.taskId} `);
                })
                .catch((err) => {
                    logger.error(`events-bus-queue-service.sendExtensionAutoEmailSummary:: failed - ${data.task[0]?.taskId} :: ${err}`);
                    _eventsRepository.retry(queueName, message);
                });
            break;

        case EventBusConstant.SUBJECT.OFFHIRE_AUTOEMAIL_SUMMARY:
            const offhireData = JSON.parse(message.body);

            logger.info(`Message for offhire auto email summary ${JSON.stringify(offhireData)}`);
            eventsBusQueueActionSvc
                .sendOffhireAutoEmailSummary(offhireData)
                .then(() => {
                    logger.info(`events-bus-queue-service.sendOffhireAutoEmailSummary:: Success - ${offhireData.taskId} `);
                })
                .catch((err) => {
                    logger.error(`events-bus-queue-service.sendOffhireAutoEmailSummary:: failed - ${offhireData.taskId} :: ${err}`);
                    _eventsRepository.retry(queueName, message);
                });
            break;

        default:
            break;
    }
};

const _errorHandler = (err) => {
    logger.error(`events-bus-queue-service:: failed to read messages from service bus queue :: ${JSON.stringify(err)}`);
};

module.exports = {
    init: function () {
        _eventsRepository = new EventsBusQueueRepository();
        return _eventsRepository.readStream(_handleMessage, _errorHandler);
    },
    write: function (queueName, message) {
        return _eventsRepository.write(queueName, message);
    }
};
