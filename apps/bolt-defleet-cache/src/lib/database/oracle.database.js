const aaOracleUtility = require('@aa/oracle-utilities'),
    oracledb = require('oracledb');
oracledb.fetchAsString = [oracledb.CLOB];

const appName = 'bolt-defleet-cache';

module.exports = {
    init: async () => {
        return await aaOracleUtility.init({
            connectStrings: process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : ['sun54:1521/VACSH'],
            user: process.env.cshUser || 'Vanda',
            password: process.env.cshPassword || 'Va.77.av',
            appName
        });
    },
    connect: async () => {
        return await aaOracleUtility.connect();
    },
    disconnect: async (db) => {
        await aaOracleUtility.release(db);
    },
    jsonify: (metadata, data) => {
        return metadata.reduce((ac, item, i) => {
            ac[item.name] = data[i];
            return ac;
        }, {});
    }
};
