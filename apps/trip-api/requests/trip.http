< {%
    import { setup } from '../../../tools/webstorm/request-utils';

    setup({ request, client, fallbackPort: 9999 });
%}


# Get all trips with pagination
GET https://rh0113p/api/trip-api-service/trips?limit=100&skip=0
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

###

# Get trip by tripCode
#GET https://rh0113p:5600/api/trip-api-service/trip/TRIP123457
GET http://0.0.0.0:7829/api/trip-api-service/trip/TRIP123456
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

###

# Get trip by customerRequestId
#GET https://rh0113p:5600/api/trip-api-service/trip/by-cr/1001
GET http://0.0.0.0:7829/api/trip-api/trip/by-cr/1011
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

###

# Create new trip
POST http://0.0.0.0:7829/api/trip-api/trip
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "created": "2024-01-01T00:00:00.000Z",
    "updated": "2024-01-01T00:00:00.000Z",
    "code": "TRIP123456",
    "start": "2023-06-01T00:00:00.000Z",
    "end": "2024-01-10T00:00:00.000Z",
    "outwardJourney": {
        "note": "Leaving from JFK Airport",
        "location": {
            "street": "JFK International Airport",
            "city": "New York",
            "state": "NY",
            "postalCode": "11430"
        }
    },
    "returnJourney": {
        "note": "Returning to LAX Airport",
        "location": {
            "street": "Los Angeles International Airport",
            "city": "Los Angeles",
            "state": "CA",
            "postalCode": "90045"
        }
    },
    "seLocatorId": 12345,
    "benefitLimits": ["limit1", "limit2"],
    "externalRef": "INV12345",
    "customerRequestIds": [1011, 1021],
    "partyComposition": {
        "adultCount": 2,
        "childrenCount": 1,
        "pets": true,
        "petCount": 1,
        "petNote": "One dog"
    }
}

###
# Edit trip
PUT http://0.0.0.0:7829/api/trip-api/trip/TRIP123456
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "created": "2024-01-01T00:00:00.000Z",
    "updated": "2024-01-01T00:00:00.000Z",
    "code": "TRIP123456",
    "start": "2023-06-01T00:00:00.000Z",
    "end": "2024-01-10T00:00:00.000Z",
    "outwardJourney": {
        "note": "Leaving f",
        "location": {
            "street": "JFK International Airport",
            "city": "New York",
            "state": "NY",
            "postalCode": "11430"
        }
    },
    "returnJourney": {
        "note": "Returning to",
        "location": {
            "street": "Los Angeles International Airport",
            "city": "Los Angeles",
            "state": "CA",
            "postalCode": "90045"
        }
    },
    "seLocatorId": 12345,
    "benefitLimits": ["limit1", "limit2"],
    "externalRef": "INV12345",
    "customerRequestIds": [1011, 1021],
    "partyComposition": {
        "adultCount": 2,
        "childrenCount": 1,
        "pets": true,
        "petCount": 1,
        "petNote": "One dog"
    }
}

###
# Delete trip
DELETE http://0.0.0.0:7829/api/trip-api/TRIP123457
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

###
# Get all trip faults for given trip code
GET http://0.0.0.0:7829/api/trip-api-service/faults/471d3243-04b4-4232-98d5-e8f80b8a53ed
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

###
# Get all tasks for given trip code
GET http://0.0.0.0:7829/api/trip-api-service/tasks/141c05c7-ede8-4210-8e41-b1bed68e74b6
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

###
# Test service entitlement TODO: Move this
POST http://0.0.0.0:5718/api/service-entitlement/getProductsByCustomerGroupCode
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "customerGroupCode": "ADAC"
}

###
# Get all tasks for given trip code
GET http://0.0.0.0:7829/api/trip-api-service/trip/2137/task/69/link
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

###
# Get tasks for given tripCode and query
POST http://0.0.0.0:7829/api/trip-api-service/tasks
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "limit": 10,
    "skip": 0,
    "tripCode": "07cbb534-ee14-4a8c-b874-f870ef24cbb2",
    "status": [
        "COMP",
        "INIT",
        "CLSD",
        "PLAN"
    ],
    "showOnlyPrimary": false
}

###
# Get trips for given query
POST {{host}}:{{port}}/api/trip-api-service/v2/trips
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "limit": 10,
    "skip": 0,
    "query": "51404482"
}

###
# Get logbook v2 entries for given tripCode
POST http://0.0.0.0:7829/api/trip-api-service/v2/3b7eece7-779e-403b-ae8d-3ecef7c0f367/logbook
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "limit": 100,
    "skip": 0,
    "query": ""
}
