import { AuditClient } from '@aa/audit-client';
import { applicationBasePaths, ErrorResponse } from '@aa/connector';
import { toTaskPreview } from '@aa/converters';
import {
    Action,
    Address,
    AlertNote,
    AlertSeverity,
    AuditEvent,
    BenefitLimit,
    BenefitSummaryResponse,
    BusinessPartnerContact,
    CallInfoNote,
    ContactType,
    CreateReasonCode,
    ExtendedPaginationQueryResult,
    FaultDataResponse,
    ItemLocation,
    LongQueryTrip,
    MiEvents,
    Namespace,
    Note,
    NoteEntityType,
    NoteType,
    PaginationQuery,
    PaginationQueryResult,
    PartyComposition,
    QueryTripLogbook,
    Trip,
    TripBusinessPartnerCategory,
    TripContact,
    TripCost,
    TripCostStatus,
    TripLogbookEntry,
    TripServiceCost,
    TripServiceCostSchema,
    TripStatus
} from '@aa/data-models/common';
import { BreakdownTask, QueryBreakdownTask } from '@aa/data-models/entities/breakdown-task';
import { isBreakdownTaskPreview, isRecoveryTaskPreview, TaskPreview } from '@aa/data-models/entities/task-preview';
import { DataStoreProviderType } from '@aa/data-store';
import { MongodbUtils } from '@aa/data-store-utils';
import { EntitlementClient } from '@aa/entitlement-client';
import { Exception } from '@aa/exception';
import { FetchResponse, HttpClient, HttpMethod, ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { NoteClient } from '@aa/note-client';
import { EntitlementBenefitClient } from '@aa/entitlement-benefit-client';
import { EventHubSender, EventHubSenderConfig } from '@aa/queue';
import { Schema } from '@aa/schema';
import { getResponse } from '@aa/server-utils';
import { TransactionHistoryClient } from '@aa/transaction-history-client';
import { BackendEnvironment, Utils } from '@aa/utils';
import { Request, Response } from 'express';
import { isArray } from 'lodash';
import { Filter, ObjectId, WithId } from 'mongodb';
import { v4 as uuidv4 } from 'uuid';

const appName = 'trip-api';
type DeepPartial<T> = {
    [P in keyof T]?: DeepPartial<T[P]>;
};

type LogbookQueryPayload = PaginationQuery &
    DeepPartial<TripLogbookEntry> & {
        onlyNotes?: boolean;
    };

interface RentalResponse {
    rental: {
        collectLocation: {
            address: [];
            area: [];
        };
    };
}

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.TRIP_API;
    protected auditClient: AuditClient;
    protected entitlementBenefitClient: EntitlementBenefitClient;
    protected transactionHistoryClient: TransactionHistoryClient;
    protected onSiteDomain: string;
    protected miStreamTripSender: EventHubSender<MiEvents.TRIP, Trip>;
    protected miStreamItemLocationSender: EventHubSender<MiEvents.ITEM_LOCATION, ItemLocation>;
    protected miStreamTripContactSender: EventHubSender<MiEvents.TRIP_CONTACT, TripContact | BusinessPartnerContact>;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.MONGODB, DataStoreProviderType.REDIS, DataStoreProviderType.ORACLE]
        });

        this.onSiteDomain = this.connector.config.onSiteDomain;

        this.auditClient = new AuditClient({
            application: BackendApplication.TRIP_API,
            connector: this.connector,
            operatorId: -1
        });

        this.entitlementBenefitClient = new EntitlementBenefitClient({
            httpClient: this.httpClient,
            connector: this.connector
        });

        this.transactionHistoryClient = new TransactionHistoryClient({
            httpClient: this.httpClient,
            connector: this.connector
        });

        const configBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };

        this.miStreamTripSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
        this.miStreamItemLocationSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
        this.miStreamTripContactSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });

        // Trip endpoints
        this.server.get('/trips', this.getTrips);
        this.server.get('/trips/:filter', this.searchTripsByFilter);
        this.server.get('/trip/:tripCode', this.getTripById);
        this.server.get('/trip/by-cr/:customerRequestId', this.getTripByCR);
        this.server.post('/trip', this.createTrip);
        this.server.put('/trip/:tripCode', this.editTrip);
        this.server.delete('/trip/:tripCode', this.deleteTrip);

        // Secure locations endpoints
        this.server.get('/secure-location/:tripCode', this.getSecureLocations);
        this.server.post('/secure-location', this.createNewSecureLocation);
        this.server.put('/secure-location/:id', this.editSecureLocation);
        this.server.delete('/secure-location/:id', this.deleteSecureLocation);

        //trip faults
        this.server.get('/faults/:tripCode', this.getFaults);

        // Trip contact endpoints
        this.server.get('/contacts', this.getContactsHandler);
        this.server.get('/contact/:code', this.getContactByCodeHandler);
        this.server.post('/contact', this.createContactHandler);
        this.server.put('/contact/:code', this.editContactHandler);
        this.server.delete('/contact/:code', this.deleteContactHandler);

        // Club endpoints
        this.server.get('/contact/business-partner/:customerGroup', this.getBusinessPartnerContact);

        //trip tasks
        this.server.get('/tasks/:tripCode', this.getTasks);
        this.server.post('/tasks', this.queryTasks);

        // trip cost endpoints
        this.server.get('/task-costs/:tripCode', this.getTripCostsByTripCode);

        // trip costs benefits
        this.server.get('/trip/:tripCode/benefit-summary', this.getBenefitsSummaryByTripCode);
        /// Action and overdue using trip-code
        this.server.get('/actions-and-overdues/:tripCode', this.getActionsAndOverduesByTripCode);

        this.server.get('/trip/:tripCode/task/:taskId/link', this.linkTaskToTrip);
        this.server.get('/trip/:tripCode/task/:taskId/unlink', this.unlinkTaskToTrip);
        this.server.post('/trip/:tripCode/logbook', this.fetchLogbookEntries);
        this.server.get('/vrn/:vrn', this.getTripByVrn);
        this.server.post(`/v2/trips`, this.queryTrips);
        this.server.post(`/v2/:tripCode/logbook`, this.fetchLogbookEntriesV2);
    }

    protected fetchLogbookEntriesV2 = async (
        req: Request<
            {
                tripCode: string;
            },
            unknown,
            QueryTripLogbook
        >,
        res: Response<ExtendedPaginationQueryResult<TripLogbookEntry>>
    ) => {
        try {
            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const auditCollection = await provider.collection<AuditEvent>('audit', 'euops');
            const notesCollection = await provider.collection<Note>('entities', 'notes');
            const { tripCode } = req.params;
            const { skip = 0, limit = 10, onlyNotes = false, types, query, ...restOfQuery } = req.body;

            let auditDocs: AuditEvent[] = [];
            let notesDocs: Note[] = [];
            let callInfoEvents: CallInfoNote[] = [];

            const trace = this.auditClient.getTrace(Namespace.EUOPS, tripCode);
            const queryObject: Filter<AuditEvent> = { ...restOfQuery };
            const queryObjectNotes: Filter<Note> = { ...restOfQuery };

            if (query) {
                queryObject.$or = [
                    { 'data.data.name': { $regex: new RegExp(query, 'i') } },
                    { 'data.data.subject': { $regex: new RegExp(query, 'i') } },
                    { 'data.data.from': { $regex: new RegExp(query, 'i') } },
                    { 'data.data.title': { $regex: new RegExp(query, 'i') } }
                ];
                queryObjectNotes.$or = [
                    { 'content.subject': { $regex: new RegExp(query, 'i') } },
                    { 'content.from': { $regex: new RegExp(query, 'i') } },
                    { title: { $regex: new RegExp(query, 'i') } }
                ];
            }

            if (onlyNotes) {
                queryObjectNotes['parentEntity.id'] = tripCode;
                const cursor = notesCollection.find({ 'parentEntity.id': tripCode });
                const notesResult = await MongodbUtils.paginate(cursor, { limit, skip });
                notesDocs = notesResult.results;
                const total = notesDocs.length;

                const response = {
                    results: notesDocs,
                    total,
                    more: (skip + 1) * limit < total,
                    totalPages: Math.ceil(total / limit)
                };

                return getResponse(res, ServerResponseCode.OK, response);
            }

            if (types && types.includes('audits')) {
                queryObject['trace'] = trace;
                auditDocs = await auditCollection.find(queryObject).toArray();
            }

            if (types && types.includes('notes')) {
                queryObjectNotes['parentEntity.id'] = tripCode;
                notesDocs = await notesCollection.find(queryObjectNotes).toArray();
            }

            if (types && types.includes('callinfo')) {
                callInfoEvents = await this.getCallInfo(tripCode);
            }

            const allResults = [...auditDocs, ...notesDocs, ...callInfoEvents];
            const sortedResults = allResults.sort((a: AuditEvent | Note, b: AuditEvent | Note) => {
                const getDate = (item: AuditEvent | Note): Date => {
                    if ('date' in item) {
                        return new Date(item.date);
                    } else {
                        return new Date(item.created);
                    }
                };

                return getDate(b).getTime() - getDate(a).getTime();
            });
            const total = sortedResults.length;
            const results = sortedResults.slice(skip * limit, skip * limit + limit);

            const response = {
                results,
                total,
                more: (skip + 1) * limit < total,
                totalPages: Math.ceil(total / limit)
            };

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (err) {
            console.error('Error fetchLogbookEntriesV2:', err);
        }
    };

    /**
     * Get trip contact by code
     * @param {Request} req
     * @param {Response<WithId<BusinessPartnerContact>>} res
     * @return {Promise<void>}
     */
    protected getBusinessPartnerContact = async (
        req: Request<
            {
                customerGroup: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<BusinessPartnerContact>>
    ): Promise<void> => {
        try {
            const { customerGroup } = req.params;
            this.logger.log(`Getting trip club contact for code: ${customerGroup}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<BusinessPartnerContact>('entities', 'contacts');
            const result = await collection.findOne({
                type: ContactType.BUSINESS_PARTNER,
                customerGroupCode: customerGroup
            });

            if (result) {
                this.logger.log(`Success: Get trip club contact for code: ${customerGroup}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Get trip club contact for code: ${customerGroup}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting trip club contact for code: ${req.params.customerGroup || 'no code given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    // this does not fetch trip but some information based on trip, I don't have time to refactor this for now
    // TODO: Fix this
    protected getTripByVrn = async (req: Request<{ vrn: string }, unknown, unknown>, res: any) => {
        const { vrn } = req.params;
        try {
            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Trip>('entities', 'trips');
            const document = await collection.findOne({ vrn });

            if (!document) {
                return res.status(404).json({ message: 'No active trip found' });
            }

            const tasks = (await this.fetchTasksByTripCode(document.code)) || [];
            // we consider trip active if has active tasks in it
            const isInActive = tasks.length > 0 && tasks.every((task) => task.status === 'COMP' || task.status === 'CLSD');
            const isActive = !isInActive;
            if (!isActive) {
                return res.status(404).json({ message: 'No active trip found' });
            }

            res.status(200).json({
                _id: document.code,
                isActive
            });
        } catch (error) {
            console.error('Error finding VRN:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    };

    // TODO: Fix this
    protected fetchLogbookEntries = async (
        req: Request<
            {
                tripCode: string;
            },
            unknown,
            LogbookQueryPayload
        >,
        res: Response
    ) => {
        try {
            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const auditCollection = await provider.collection<AuditEvent>('audit', 'euops');
            const notesCollection = await provider.collection<Note>('entities', 'notes');
            const { tripCode } = req.params;
            const { skip = 0, limit = 10, onlyNotes } = req.body;

            let auditDocs: AuditEvent[] = [];
            let notesDocs: Note[] = [];
            let callInfoEvents: Note[] = [];
            let notesDocsAll: Note[] = [];
            const trace = this.auditClient.getTrace(Namespace.EUOPS, tripCode);

            if (!onlyNotes) {
                const cursor = auditCollection.find({ trace });
                const paginationResult = await MongodbUtils.paginate(cursor, { limit, skip });
                auditDocs = paginationResult.results;
                const auditIds = auditDocs.map((doc) => doc._id.toString());
                const notesDocsRaw = await notesCollection
                    .find({
                        'parentEntity.id': tripCode
                    })
                    .toArray();

                callInfoEvents = await this.getCallInfo(tripCode);
                ///insert callInfo
                notesDocsAll = [...notesDocsRaw, ...callInfoEvents];
                notesDocs = notesDocsAll as unknown as Note[];
            } else {
                const cursor = notesCollection.find({ 'parentEntity.trace': tripCode });
                const paginationResult = await MongodbUtils.paginate(cursor, { limit, skip });
                notesDocs = paginationResult.results;
            }

            const mergedDocs = auditDocs.map((auditDoc) => {
                const matchingNotes = notesDocs.filter((note) => note.parentEntity && note.parentEntity.id === tripCode);
                return {
                    ...auditDoc,
                    notes: matchingNotes
                };
            });

            // const results = onlyNotes ? notesDocs : mergedDocs;
            const allResults = [...auditDocs, ...notesDocs];
            const total = allResults.length;
            const results = allResults.slice(skip * limit, skip * limit + limit);

            // const totalCount = onlyNotes
            //     ? await notesCollection.countDocuments({
            //           'parentEntity.trace': tripCode,
            //       })
            //     : await auditCollection.countDocuments({ trace });

            const response = {
                results,
                total,
                more: (skip + 1) * limit < total,
                totalPages: Math.ceil(total / limit)
            };

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (err) {
            console.error('Error:', err);
        }
    };

    protected getCallInfo = async (tripCode: string): Promise<CallInfoNote[]> => {
        try {
            const tasks = await this.fetchTasksByTripCode(tripCode);

            if (!tasks || tasks.length === 0) {
                return [];
            }

            const breakdownTasks = tasks.filter((task) => task.taskType?.code === 'BRK');

            if (breakdownTasks.length === 0) {
                return [];
            }

            const callInfoNotes: CallInfoNote[] = [];

            await Promise.all(
                breakdownTasks.map(async (task) => {
                    const { id: taskId, customerRequestId } = task;

                    try {
                        const apiRequestData = {
                            customerRequestId,
                            taskId,
                            seLocatorId: task.operatorId || -1
                        };

                        const callInfoData = await this.transactionHistoryClient.view(apiRequestData);
                        const callInfoEvents: CallInfoNote[] = callInfoData.map((callInfoEvent: any) => ({
                            type: NoteType.CALL_INFO,
                            namespace: Namespace.EUOPS,
                            title: 'Call Info Note',
                            createdBy: callInfoEvent.user?.userName,
                            created: callInfoEvent.updateTimeStamp,
                            updated: callInfoEvent.updateTimeStamp,
                            parentEntity: {
                                type: NoteEntityType.TRIP,
                                id: tripCode || ''
                            },
                            content: {
                                description: callInfoEvent.description || '',
                                customerRequestId: callInfoEvent.customerRequestId || -1,
                                status: {
                                    code: callInfoEvent.status?.code || '',
                                    name: callInfoEvent.status?.name || ''
                                },
                                eventType: {
                                    id: callInfoEvent.eventType?.id || -1,
                                    name: callInfoEvent.eventType?.name || ''
                                }
                            }
                        }));

                        callInfoNotes.push(...callInfoEvents);
                    } catch (error) {
                        this.logger.error(`Failed fetching callInfo details for taskId: ${taskId}`);
                    }
                })
            );

            return callInfoNotes;
        } catch (error) {
            this.logger.error(`Failed getting tasks for tripCode: ${tripCode}`);
            return [];
        }
    };

    protected queryTrips = async (req: Request<unknown, unknown, LongQueryTrip, unknown>, res: Response<ExtendedPaginationQueryResult<Trip>>): Promise<void> => {
        try {
            const { limit = 5, skip = 0, query, status, code, ...restOfQuery } = req.body;
            this.logger.log(`Querying trips`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const oracleProvider = this.dataStore.getProvider(DataStoreProviderType.ORACLE);
            const collection = await provider.collection<Trip>('entities', 'trips');

            // check if string contains only numbers
            if (query && /^\d+$/g.test(query)) {
                const aggregateQuery = [
                    {
                        $lookup: {
                            from: 'taskPreview',
                            localField: 'customerRequestIds',
                            foreignField: 'customerRequestId',
                            as: 'matchedTasks'
                        }
                    },
                    {
                        $addFields: {
                            taskIds: {
                                $map: {
                                    input: '$matchedTasks',
                                    as: 'task',
                                    in: '$$task.id'
                                }
                            }
                        }
                    },
                    {
                        $project: {
                            matchedTasks: 0
                        }
                    },
                    {
                        $match: {
                            $or: [
                                { code: { $regex: new RegExp(query, 'i') } },
                                { productCode: { $regex: new RegExp(query, 'i') } },
                                { vrn: { $regex: new RegExp(query, 'i') } },
                                { externalRef: { $regex: new RegExp(query, 'i') } },
                                { customerGroup: { $regex: new RegExp(query, 'i') } },
                                { customerRequestIds: query },
                                { customerKey: query },
                                { contractKey: query },
                                { taskIds: { $in: [Number(query)] } }
                            ]
                        }
                    }
                ];

                const results = await collection.aggregate(aggregateQuery).toArray();
                const total = results.length;

                const response = {
                    results,
                    total,
                    more: (skip + 1) * limit < total,
                    totalPages: Math.ceil(total / limit)
                };

                return getResponse(res, ServerResponseCode.OK, response);
            }

            let cshCustomerRequestIds: number[] = [];
            // check if string doesn't contain only numbers
            if (query && query.length > 3 && !/^\d+$/g.test(query)) {
                const vrn = `%${query}%`;
                const sql = `
                    select unique(t.cust_request_id)
                    from task t, breakdown_task bt
                    where t.task_id = bt.task_id and bt.vehicle_reg_no like :query
                `;

                const bindings = {
                    query: vrn
                };
                cshCustomerRequestIds = await oracleProvider.execute(sql, bindings);
            }

            const queryObject: Filter<Trip> = { ...restOfQuery };

            if (status) {
                queryObject.status = status as TripStatus;
            }

            // This looks kind of disgusting, but I do not have better idea for multiple field search in Mongo
            // Any ideas, let me know. Matt
            if (query) {
                queryObject.$or = [
                    { code: { $regex: new RegExp(query, 'i') } },
                    { productCode: { $regex: new RegExp(query, 'i') } },
                    { vrn: { $regex: new RegExp(query, 'i') } },
                    { externalRef: { $regex: new RegExp(query, 'i') } },
                    { customerGroup: { $regex: new RegExp(query, 'i') } },
                    { customerRequestIds: { $in: cshCustomerRequestIds } }
                ];
            }

            const cursor = collection.find(queryObject).sort({ created: -1 });
            const countResult = await MongodbUtils.count(cursor, limit);

            // if none found
            if (!countResult.total) {
                return getResponse(res, ServerResponseCode.OK, {
                    results: [],
                    more: false,
                    ...countResult
                });
            }
            const paginationResult = await MongodbUtils.paginate(cursor, { limit, skip });

            return getResponse(res, ServerResponseCode.OK, {
                ...paginationResult,
                ...countResult
            });
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting trips`
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**************************************************************************
     *
     * @param req
     * @param res
     * @returns
     *
     *************************************************************************/
    protected linkTaskToTrip = async (
        req: Request<
            {
                tripCode: string;
                taskId: number;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<Trip>>
    ): Promise<void> => {
        try {
            const { tripCode, taskId } = req.params;

            const task: BreakdownTask | void = await this.connector.task.find.byId(taskId);

            if (!task) {
                throw new Error('Task not found');
            }
            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const tripCollection = await provider.collection<Trip>('entities', 'trips');

            const trip = await tripCollection.findOne({ code: tripCode });
            if (!trip) {
                throw new Error('Trip not found');
            }

            const customerRequestId = task.customerRequestId;

            if (!customerRequestId) {
                throw new Error('Task does not have a customerRequestId');
            }

            const tripCustomerRequestIds = trip.customerRequestIds || [];
            const date = new Date();
            if (!tripCustomerRequestIds.includes(customerRequestId)) {
                await tripCollection.updateOne(
                    { code: tripCode },
                    {
                        $addToSet: { customerRequestIds: customerRequestId },
                        $set: { updated: date }
                    }
                );
            }

            const updatedTrip = await tripCollection.findOne({ code: tripCode });

            if (!updatedTrip) {
                throw new Error('Failed while linking trip');
            }

            const withoutId = MongodbUtils.withoutId(updatedTrip);
            return getResponse(res, ServerResponseCode.OK, withoutId);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'Link failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /**************************************************************************
     *
     * @param req
     * @param res
     * @returns
     *
     *************************************************************************/
    protected unlinkTaskToTrip = async (
        req: Request<
            {
                tripCode: string;
                taskId: number;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<Trip>>
    ): Promise<void> => {
        try {
            const { tripCode, taskId } = req.params;

            const task: BreakdownTask | void = await this.connector.task.find.byId(taskId);

            if (!task) {
                throw new Error('Task not found');
            }
            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const tripCollection = await provider.collection<Trip>('entities', 'trips');

            const trip = await tripCollection.findOne({ code: tripCode });
            if (!trip) {
                throw new Error('Trip not found');
            }

            const customerRequestId = task.customerRequestId;

            if (!customerRequestId) {
                throw new Error('Task does not have a customerRequestId');
            }

            const tripCustomerRequestIds = trip.customerRequestIds || [];
            const date = new Date();
            if (tripCustomerRequestIds.includes(customerRequestId)) {
                await tripCollection.updateOne(
                    {
                        code: tripCode
                    },
                    {
                        $pull: { customerRequestIds: customerRequestId },
                        $set: { updated: date }
                    }
                );
            } else {
                throw new Error('Task not linked to trip');
            }

            const updatedTrip = await tripCollection.findOne({ code: tripCode });

            if (!updatedTrip) {
                throw new Error('Failed while unlinking trip');
            }

            const withoutId = MongodbUtils.withoutId(updatedTrip);
            return getResponse(res, ServerResponseCode.OK, withoutId);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'Unlink failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    // TODO: Fix this
    /**
     * Get trip by tripCode
     * @param {Request} req
     * @param {Response<WithId<Trip>>} res
     * @return {Promise<void>}
     */
    protected getActionsAndOverduesByTripCode = async (
        req: Request<
            {
                tripCode: string;
            },
            unknown,
            unknown
        >,
        res: Response<TripActionsAndOverdues>
    ): Promise<void> => {
        try {
            const { tripCode } = req.params;
            this.logger.log(`Getting actions and overdues for tripCode: ${tripCode}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Action>('queue', 'actions');

            const result = await collection
                .find({
                    'parentEntity.id': tripCode
                })
                .toArray();

            if (result) {
                const now = new Date();
                let overdueCount = 0;

                result.forEach((item) => {
                    if (item.expiresAt) {
                        const expiresAt = new Date(item.expiresAt);

                        if (!isNaN(expiresAt.getTime()) && expiresAt < now) {
                            overdueCount++;
                        }
                    }
                });

                // Add `overdueCount` to the result object and return it.
                const responseObject: TripActionsAndOverdues = {
                    actions: result,
                    overdueCount
                };

                return getResponse(res, ServerResponseCode.OK, responseObject);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting trip data for tripCode: ${req.params.tripCode || 'no tripCode given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get trip contact by code
     * @param {Request} req
     * @param {Response<WithId<BenefitSummaryResponse>>} res
     * @return {Promise<void>}
     */
    protected getBenefitsSummaryByTripCode = async (
        req: Request<
            {
                tripCode: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<BenefitSummaryResponse>>
    ): Promise<void> => {
        try {
            const { tripCode } = req.params;
            this.logger.log(`Getting trip benefits summary for tripCode: ${tripCode}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const tripCollection = await provider.collection<Trip>('entities', 'trips');
            const trip = await tripCollection.findOne<Trip>({
                code: tripCode
            });
            if (!trip) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
            const taskCostsCollection = await provider.collection<TripCost>('entities', 'tripCosts');
            const cursor = taskCostsCollection.find<TripCost>({
                customerRequestId: { $in: trip.customerRequestIds }
            });
            const query: PaginationQuery = { limit: 100, skip: 0 };
            const tasksCost = await MongodbUtils.paginate(cursor, query, 100);
            const results = tasksCost.results.filter((result) => {
                return Schema.check(TripServiceCostSchema, result) && result.status !== TripCostStatus.CANCELLED;
            }) as TripServiceCost[];

            //TODO: This is an abomination, I will rewrite it soon for now it "works"
            const grouped = results.reduce((acc: Record<string, any>, curr) => {
                const key = curr.benefitLimitId;
                if (!acc[key]) {
                    acc[key] = {
                        ...curr,
                        taskIds: typeof curr.taskId !== 'undefined' ? [curr.taskId] : [],
                        forecast: {
                            amount: curr.forecast.amount,
                            currency: curr.forecast.currency
                        },
                        forecastNative: {
                            amount: curr.forecastNative.amount,
                            currency: curr.forecastNative.currency
                        },
                        overspend: curr.overspend
                            ? {
                                  amount: curr.overspend.amount,
                                  currency: curr.overspend.currency
                              }
                            : {
                                  amount: 0,
                                  currency: curr.currency
                              }
                    };
                } else {
                    if (typeof curr.taskId !== 'undefined') {
                        acc[key].taskIds.push(curr.taskId);
                    }
                    acc[key].forecast.amount += curr.forecast.amount;
                    acc[key].forecastNative.amount += curr.forecastNative.amount;
                    if (curr.overspend) {
                        if (!acc[key].overspend) {
                            acc[key].overspend = {
                                amount: curr.overspend.amount,
                                currency: curr.overspend.currency
                            };
                        } else {
                            acc[key].overspend.amount += curr.overspend.amount;
                        }
                    }
                }
                return acc;
            }, {});

            const reducedTripCosts = Object.values(grouped);
            const benefitCollection = await provider.collection<BenefitLimit>('entities', 'benefitLimits');

            const result = [];

            for (const tripCost of reducedTripCosts) {
                const benefit = await benefitCollection.findOne<BenefitLimit>({
                    code: tripCost.benefitLimitId
                });

                if (benefit) {
                    result.push({
                        taskIds: tripCost.taskIds,
                        forecast: tripCost.forecast,
                        overspend: tripCost.overspend,
                        tripCode: tripCost.tripCode,
                        forecastNative: tripCost.forecastNative,
                        actual: tripCost.actual,
                        actualNative: tripCost.actualNative,
                        ...benefit
                    });
                }
            }

            if (result) {
                this.logger.log(`Success: Get trip summary benefits for tripCode: ${tripCode}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Get trip summary benefits for tripCode: ${tripCode}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting trip summary benefits for tripCode: ${req.params.tripCode || 'no tripCode given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get trip costs by tripCode with query
     * @param {Request} req
     * @param {Response<WithId<TripCost>>} res
     * @return {Promise<void>}
     */
    protected getTripCostsByTripCode = async (
        req: Request<
            { tripCode: string },
            unknown,
            unknown,
            {
                limit?: string;
                skip?: string;
                name?: string;
                customerRequestId?: number;
                taskId?: number;
                benefitLimitId?: string;
                taskInvoiceCode?: string;
            }
        >,
        res: Response<WithId<TripCost>>
    ): Promise<void> => {
        try {
            const { tripCode } = req.params;
            const limit = parseInt(req.query.limit || '100', 10);
            const skip = parseInt(req.query.skip || '0', 10);
            this.logger.log(`Getting trip costs for tripCode: ${tripCode}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<TripCost>('entities', 'tripCosts');

            const query: Record<string, unknown> = {};
            if (req.query.name) {
                query.name = req.query.name;
            }
            if (req.query.customerRequestId) {
                query.customerRequestId = Number(req.query.customerRequestId);
            }
            if (req.query.taskId) {
                query.taskId = Number(req.query.taskId);
            }
            if (req.query.benefitLimitId) {
                query.benefitLimitId = req.query.benefitLimitId;
            }
            if (req.query.taskInvoiceCode) {
                query.taskInvoiceCode = req.query.taskInvoiceCode;
            }
            query.tripCode = tripCode;

            const cursor = collection.find<TripCost>(query);
            const paginationQuery: PaginationQuery = { limit, skip };
            const results = await MongodbUtils.paginate(cursor, paginationQuery, 100);
            const result = results.results.filter((result) => {
                return Schema.check(TripServiceCostSchema, result) && result.status !== TripCostStatus.CANCELLED;
            });
            if (result) {
                this.logger.log(`Success: Get trip costs`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Get trip costs`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting trip costs for tripCode: ${req.params.tripCode || 'no tripCode given in request'}`,
                data: { ...req.params, ...req.query }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get paginated list of all trip contacts with query
     * @param {Request} req
     * @param {Response<WithId<TripContact | BusinessPartnerContact>>} res
     * @return {Promise<void>}
     */
    protected getContactsHandler = async (
        req: Request<
            unknown,
            unknown,
            unknown,
            {
                limit?: string;
                skip?: string;
                code?: string;
                tripCode?: string;
                name?: string;
                email?: string;
                categories?: TripBusinessPartnerCategory[];
                telephone?: string;
                showOnlyTripContacts?: boolean;
                type?: ContactType[];
            }
        >,
        res: Response<PaginationQueryResult<WithId<TripContact | BusinessPartnerContact>>>
    ): Promise<void> => {
        try {
            // Parse limit and skip from query params (with defaults)
            const limit = parseInt(req.query.limit || '10', 10); // Default to 10 items per page
            const skip = parseInt(req.query.skip || '0', 10); // Default to starting from 0 (first page)

            this.logger.log(`Getting trip contacts with limit: ${limit}, skip: ${skip}`);

            // Get MongoDB provider and collection
            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<TripContact | BusinessPartnerContact>('entities', 'contacts');

            // Build the query based on the filter params (if any)
            // const query: Record<string, unknown> = {};
            // if (req.query.code) query.code = req.query.code;
            // if (req.query.tripCode) query.tripCode = req.query.tripCode;
            // if (req.query.name) query.name = req.query.name;
            // if (req.query.email) query.email = req.query.email;
            // if (req.query.telephone) query.telephone = req.query.telephone;
            // if (req.query.showOnlyTripContacts) query.tripCode = { $exists: true, $ne: null };
            const query: Filter<unknown> = {};
            if (req.query.code) {
                const regexPattern = new RegExp(req.query.code, 'gi');
                query.$or = [{ code: { $regex: regexPattern } }];
            }
            if (req.query.tripCode) {
                query.tripCode = req.query.tripCode;
            }
            if (req.query.name) {
                const regexPattern = new RegExp(req.query.name, 'gi');
                query.$or = [{ name: { $regex: regexPattern } }];
            }
            if (req.query.email) {
                const regexPattern = new RegExp(req.query.email, 'gi');
                query.$or = [{ email: { $regex: regexPattern } }];
            }
            if (req.query.telephone) {
                const regexPattern = new RegExp(req.query.telephone, 'gi');
                query.$or = [{ telephone: { $regex: regexPattern } }];
            }
            if (req.query.showOnlyTripContacts) {
                // query.tripCode = { $exists: true, $ne: null };
                query.$or = [{ tripCode: { $exists: true, $ne: null } }];
            }
            if (req.query.type) {
                const queryString = req.query.type.toString();
                const types = queryString.split(',');
                query.type = { $in: types };
            }
            if (req.query.categories) {
                console.log('query categories', req.query.categories);
                const queryString = req.query.categories.toString();
                const categories = queryString.split(',');
                console.log('categories2', categories);
                query.categories = { $in: categories };
            }
            const cursor = await collection.find(query);
            const countResult = await MongodbUtils.count(cursor, limit);

            if (!countResult.total) {
                return getResponse(res, ServerResponseCode.OK, {
                    results: [],
                    more: false,
                    ...countResult
                });
            }

            const paginationResult = await MongodbUtils.paginate(cursor, { limit, skip });

            return getResponse(res, ServerResponseCode.OK, {
                ...paginationResult,
                ...countResult
            });
        } catch (error) {
            // Handle and log errors
            const exception = new Exception({
                error,
                message: `Failed getting trip contacts`,
                data: { ...req.query }
            });
            this.logger.error(exception);

            // Send server error response
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get trip contact by code
     * @param {Request} req
     * @param {Response<WithId<TripContact | BusinessPartnerContact>>} res
     * @return {Promise<void>}
     */
    protected getContactByCodeHandler = async (
        req: Request<
            {
                code: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<TripContact | BusinessPartnerContact>>
    ): Promise<void> => {
        try {
            const { code } = req.params;
            this.logger.log(`Getting trip contact for code: ${code}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<TripContact | BusinessPartnerContact>('entities', 'contacts');
            const result = await collection.findOne<TripContact | BusinessPartnerContact>({
                code
            });

            if (result) {
                this.logger.log(`Success: Get trip contact for code: ${code}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Get trip contact for code: ${code}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting trip contact for code: ${req.params.code || 'no code given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Create new trip contact in MongoDB
     * @param {e.Request<unknown, unknown, TripContact | BusinessPartnerContact>} req
     * @param {e.Response<WithId<TripContact | BusinessPartnerContact>>} res
     * @return {Promise<void>}
     */
    protected createContactHandler = async (req: Request<unknown, unknown, TripContact | BusinessPartnerContact>, res: Response<WithId<TripContact | BusinessPartnerContact>>): Promise<void> => {
        try {
            const tripContact = req.body;
            this.logger.info({
                sourceName: this.name,
                message: `Received new trip contact for creation in MongoDB`,
                data: { ...tripContact }
            });

            tripContact.code = uuidv4();

            if (!isValidString(tripContact.code)) {
                return handleInvalidRequest('code', res);
            }
            if (tripContact.type !== ContactType.BUSINESS_PARTNER && !isValidString(tripContact.tripCode)) {
                return handleInvalidRequest('tripCode', res);
            }

            if (!isValidString(tripContact.name)) {
                return handleInvalidRequest('name', res);
            }
            if (!isValidString(tripContact.telephone)) {
                return handleInvalidRequest('telephone', res);
            }

            tripContact.created = new Date();
            tripContact.updated = new Date();

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<TripContact | BusinessPartnerContact>('entities', 'contacts');

            const result = await collection.insertOne(tripContact);
            const persistedTripContact: WithId<TripContact | BusinessPartnerContact> = {
                ...tripContact,
                _id: result.insertedId
            };

            if (result && tripContact.type !== ContactType.BUSINESS_PARTNER) {
                this.logger.log(`Success: Create trip contact`);
                // Update trip to link contact record
                const tripCollection = await provider.collection<Trip>('entities', 'trips');

                const date = new Date();
                const tripUpdateResponse = await tripCollection.updateOne(
                    {
                        code: tripContact.tripCode
                    },
                    {
                        $set: {
                            partyContactCode: tripContact.code,
                            updated: date
                        }
                    }
                );

                await this.miStreamTripContactSender.send(MiEvents.TRIP_CONTACT, persistedTripContact);

                //Adding audit logs
                if (tripContact.tripCode) {
                    const trace = this.auditClient.getTrace(Namespace.EUOPS, tripContact.tripCode);
                    await this.auditClient.reportAction(
                        trace,
                        {
                            message: `Trip API: Created New contact for tripCode: ${tripContact.tripCode}`,
                            data: { ...tripContact }
                        },
                        this.context.store.operatorId as number
                    );
                }

                if (tripUpdateResponse) {
                    this.logger.log(`Contact ${tripContact.code} is linked to trip ${tripContact.tripCode}}`);
                } else {
                    this.logger.error(`Unable to link Contact ${tripContact.code} to trip ${tripContact.tripCode}}`);
                }

                return getResponse(res, ServerResponseCode.OK, persistedTripContact);
            } else {
                this.logger.log(`Success: Create trip contact`);
                return getResponse(res, ServerResponseCode.OK, persistedTripContact);
            }
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                message: `Failed creating new trip contact.`,
                data: { ...req.body }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, error);
        }
    };

    /**
     * Edit trip contact in MongoDB
     * @param {e.Request<{id: string}, unknown, TripContact>} req
     * @param {e.Response<WithId<TripContact>>} res
     * @return {Promise<void>}
     */
    protected editContactHandler = async (
        req: Request<
            {
                code: string;
            },
            unknown,
            TripContact
        >,
        res: Response<WithId<TripContact>>
    ): Promise<void> => {
        try {
            const tripContact = req.body;
            const { code } = req.params;
            this.logger.log(`Editing trip contact for code: ${code}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<TripContact>('entities', 'contacts');

            const date = new Date();
            const result = await collection.updateOne(
                { code },
                {
                    $set: {
                        ...tripContact,
                        updated: date
                    }
                },
                { upsert: true }
            );
            // Update trip to link contact record
            const tripCollection = await provider.collection<Trip>('entities', 'trips');

            const tripUpdateResponse = await tripCollection.updateOne(
                {
                    code: tripContact.tripCode
                },
                {
                    $set: { partyContactCode: code, updated: date }
                }
            );

            await this.miStreamTripContactSender.send(MiEvents.TRIP_CONTACT, tripContact);

            if (tripUpdateResponse) {
                this.logger.log(`Contact ${code} is linked to trip ${tripContact.tripCode}}`);
            } else {
                this.logger.error(`Unable to link Contact ${code} to trip ${tripContact.tripCode}}`);
            }

            //Adding audit logs
            if (tripContact.tripCode) {
                const trace = this.auditClient.getTrace(Namespace.EUOPS, tripContact.tripCode);
                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `Trip API: Updated Trip contact for tripCode: ${tripContact.tripCode}`,
                        data: { ...tripContact }
                    },
                    this.context.store.operatorId as number
                );
            }

            if (result) {
                this.logger.log(`Success: Editing trip contact for code: ${code}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Editing trip contact for code: ${code}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to edit trip contact for code: ${req.params.code || 'no code given in request'}`,
                data: { ...req.params, ...req.body }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Delete trip contact by code
     * @param {Request<{code: string}, unknown, unknown>} req
     * @param {Response<WithId<TripContact | BusinessPartnerContact>>} res
     * @return {Promise<void>}
     */
    protected deleteContactHandler = async (
        req: Request<
            {
                code: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<TripContact | BusinessPartnerContact>>
    ): Promise<void> => {
        const { code } = req.params;
        try {
            this.logger.log(`Delete trip contact with code: ${code}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<TripContact | BusinessPartnerContact>('entities', 'contacts');

            const result = collection.findOneAndDelete({
                code
            });

            //Added audit logs
            if (code) {
                const trace = this.auditClient.getTrace(Namespace.EUOPS, code);
                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `TripAPI: Trip contact deleted for Tripcode: ${code}`,
                        data: { ...result }
                    },
                    this.context.store.operatorId as number
                );
            }

            if (result) {
                this.logger.log(`Success: Delete trip contact for code: ${code}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Delete trip contact for code: ${code}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to delete trip contact with code ${code || 'no code given in request'}`,
                data: { code }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected fetchTasksByTripCode = async (tripCode: string): Promise<TaskPreview[] | null> => {
        this.logger.log(`Fetching tasks for tripCode: ${tripCode}`);

        const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<Trip>('entities', 'trips');
        const result = await collection.findOne<Trip>({ code: tripCode });

        if (!result || !isArray(result.customerRequestIds)) {
            return null;
        }

        const customerRequestIds = result.customerRequestIds;

        // Get all tasks for each customerRequestId
        const requests = customerRequestIds.map((id) => {
            const fetchResult = this.httpClient.fetch({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.TASK]}/tasks/tasksByCustomerRequestId/${id}`,
                method: HttpMethod.GET,
                authenticate: true
            });
            return fetchResult;
        });

        const responses = await Promise.all(requests);
        const flatResults = await Promise.all(
            responses.flatMap((response: FetchResponse<unknown>) => {
                if (isArray(response.body)) {
                    return response.body.map(async (task: any) => {
                        if (task.createReason && task.createReason.name.includes('Hire')) {
                            try {
                                const fetchRentalResult = await this.httpClient.fetch<RentalResponse>({
                                    url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.MOBILITY_TASK]}/rental/${task.id}`,
                                    method: HttpMethod.GET,
                                    authenticate: true
                                });

                                const rentalData = fetchRentalResult.body;
                                if (rentalData && rentalData.rental) {
                                    task.location.text = rentalData.rental.collectLocation.address;
                                    task.location.area = rentalData.rental.collectLocation.area ? rentalData.rental.collectLocation.area : 'N/A';
                                }
                            } catch (error) {
                                console.error('Error fetching rental details:', error);
                            }
                        } else {
                            if (task.recovery && task.recovery.destination.text && !task.location?.text) {
                                if (!task.recovery.destination.text.includes('SAFE')) {
                                    task.location = task.recovery.destination;
                                }
                            }
                            if (!task.location?.text) {
                                if (task.hotel?.hotelLocation?.text) {
                                    task.location = task.hotel.hotelLocation;
                                } else if (task.transport?.pickUpLocation?.text) {
                                    task.location = task.transport.pickUpLocation;
                                }
                            }
                        }
                        if (task.entitlement && task.entitlement.customerGroup.name) {
                            task.createReason.customerGroupName = task.entitlement.customerGroup.name;
                            task.customerGroupName = task.entitlement.customerGroup.name;
                        }
                        return toTaskPreview(task);
                    });
                }
                return undefined;
            })
        );

        const activeTasks = flatResults
            .filter((res) => !!res && !(res.status === 'CLSD' || res.status === 'COMP'))
            .sort((a, b) => new Date(b?.schedule?.arrive as string).getTime() - new Date(a?.schedule?.arrive as string).getTime());
        const compTasks = flatResults
            .filter((res) => !!res && (res.status === 'CLSD' || res.status === 'COMP'))
            .sort((a, b) => new Date(b?.schedule?.arrive as string).getTime() - new Date(a?.schedule?.arrive as string).getTime());

        return [...activeTasks, ...compTasks] as TaskPreview[];
    };

    protected getTasks = async (
        req: Request<
            {
                tripCode: string;
            },
            unknown,
            unknown
        >,
        res: Response<TaskPreview[]>
    ): Promise<void> => {
        try {
            const { tripCode } = req.params;
            const tasks = await this.fetchTasksByTripCode(tripCode);

            if (tasks) {
                return getResponse(res, ServerResponseCode.OK, tasks);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting tasks for tripCode: ${req.params.tripCode || 'no tripCode given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected getFaults = async (
        req: Request<
            {
                tripCode: string;
            },
            unknown,
            unknown
        >,
        res: Response<FaultDataResponse[]>
    ): Promise<void> => {
        try {
            const { tripCode } = req.params;
            const tasks = await this.fetchTasksByTripCode(tripCode);
            if (tasks) {
                const faults = tasks
                    .map((task: TaskPreview) => {
                        if (!isBreakdownTaskPreview(task) && !isRecoveryTaskPreview(task)) {
                            return;
                        }

                        return {
                            customerRequestId: task.customerRequestId,
                            taskId: task.id,
                            date: task?.schedule?.create,
                            fault: task.fault
                        };
                    })
                    .filter((entry) => !!entry && entry.fault.id !== -1);

                return getResponse(res, ServerResponseCode.OK, faults);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting faults for tripCode: ${req.params.tripCode || 'no tripCode given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected queryTasks = async (req: Request<unknown, unknown, QueryBreakdownTask, unknown>, res: Response<ExtendedPaginationQueryResult<TaskPreview>>): Promise<void> => {
        try {
            const { limit = 10, skip = 0, tripCode, taskId, status, associatedStatus, showOnlyPrimary } = req.body;
            this.logger.log(`Querying tasks`);

            const tasks = await this.fetchTasksByTripCode(tripCode);

            if (!tasks) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            let result = tasks;

            if (taskId) {
                const searchRegex = new RegExp(taskId.toString(), 'i');
                result = result.filter((task) => searchRegex.test(task?.id?.toString() || ''));

                if (!result) {
                    return getResponse(res, ServerResponseCode.NOT_FOUND);
                }
            }

            if (associatedStatus && associatedStatus.length > 0) {
                result = result?.filter((task) => associatedStatus.includes(task.status));
                if (!result) {
                    return getResponse(res, ServerResponseCode.NOT_FOUND);
                }
            }

            if (showOnlyPrimary) {
                result = result.filter(
                    (task) =>
                        task.createReason?.id === CreateReasonCode.REATTEND || task.createReason?.id === CreateReasonCode.SELF_SERVICE_CAR || task.createReason?.id === CreateReasonCode.INITIAL_TASK
                );
                if (!result) {
                    return getResponse(res, ServerResponseCode.NOT_FOUND);
                }
            }

            const results = result.slice(skip * limit, skip * limit + limit) || [];
            const more = limit * skip + limit < results.length;
            return getResponse(res, ServerResponseCode.OK, {
                results,
                more,
                total: results.length,
                skip,
                limit,
                totalPages: Math.ceil(results.length / limit)
            });
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed querying tasks`
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get paginated list of all trips
     * @param {Request} req
     * @param {Response<WithId<Trip>>} res
     * @return {Promise<void>}
     */
    protected searchTripsByFilter = async (
        req: Request<{ filter: string }, unknown, unknown, { limit?: string; skip?: string }>,
        res: Response<PaginationQueryResult<WithId<Trip>>>
    ): Promise<void> => {
        try {
            const { filter } = req.params;
            const limit = parseInt(req.query.limit || '100', 10);
            const skip = parseInt(req.query.skip || '0', 10);
            this.logger.log(`Getting filtered trips`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Trip>('entities', 'trips');

            const regexPattern = new RegExp(filter, 'gi');
            const query = { code: { $regex: regexPattern } };
            const cursor = collection.find<Trip>(query);
            const paginationQuery: PaginationQuery = { limit, skip };
            const result = await MongodbUtils.paginate(cursor, paginationQuery, 100);

            if (result && result.results.length) {
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting filtered trips`,
                data: {}
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get paginated list of all trips
     * @param {Request} req
     * @param {Response<WithId<Trip>>} res
     * @return {Promise<void>}
     */
    protected getTrips = async (
        req: Request<
            unknown,
            unknown,
            unknown,
            {
                limit?: string;
                skip?: string;
            }
        >,
        res: Response<PaginationQueryResult<WithId<Trip>>>
    ): Promise<void> => {
        try {
            const limit = parseInt(req.query.limit || '100', 10);
            const skip = parseInt(req.query.skip || '0', 10);
            this.logger.log(`Getting all trips`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Trip>('entities', 'trips');

            const cursor = collection.find<Trip>({}).sort({ created: -1 });

            const query: PaginationQuery = { limit, skip };
            const result = await MongodbUtils.paginate(cursor, query, 100);

            if (result && result.results.length) {
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting trips`,
                data: {}
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get trip by tripCode
     * @param {Request} req
     * @param {Response<WithId<Trip>>} res
     * @return {Promise<void>}
     */
    protected getTripById = async (
        req: Request<
            {
                tripCode: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<Trip>>
    ): Promise<void> => {
        try {
            const { tripCode } = req.params;
            this.logger.log(`Getting trip for tripCode: ${tripCode}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Trip>('entities', 'trips');
            const result = await collection.findOne<Trip>({ code: tripCode });

            if (result) {
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting trip data for tripCode: ${req.params.tripCode || 'no tripCode given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get trip by customerRequestId
     * @param {Request} req
     * @param {Response<WithId<Trip>>} res
     * @return {Promise<void>}
     */
    protected getTripByCR = async (
        req: Request<
            {
                customerRequestId: number;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<Trip>>
    ): Promise<void> => {
        try {
            const { customerRequestId } = req.params;
            this.logger.log(`Getting trip for customerRequestId: ${customerRequestId}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Trip>('entities', 'trips');

            const result = await collection.findOne<Trip>({
                customerRequestIds: { $in: [Number(customerRequestId)] }
            });

            if (result) {
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting trip data for customerRequestId: ${req.params.customerRequestId || 'no customerRequestId given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Acquire unique trip code
     * @return {Promise<string>}
     */
    protected acquireTripCode = async (): Promise<string> => {
        const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<Trip>('entities', 'trips');

        try {
            let code = Utils.generateTCID({ period: 'year', space: 5 });
            let attempts = 0;
            let valid = false;

            // check generated code for trip - we need to find unique one
            while (!valid && attempts < 10) {
                // find similar code for current year
                const exists = await collection.findOne({
                    code,
                    created: { $gte: Utils.generateYearDate(new Date()) }
                });
                valid = !exists;
                if (!valid) {
                    // regenerate code
                    code = Utils.generateTCID({ period: 'year', space: 5 });
                }
                attempts++;
            }

            if (!valid) {
                throw new Error('Maximum attempts reached');
            }

            return code;
        } catch (error) {
            throw new Error(`Failure while attempting to generate new trip code: ${(error as Error).message}`);
        }
    };

    /**
     * Create new trip in MongoDB
     * @param {e.Request<unknown, unknown, Trip>} req
     * @param {e.Response<WithId<Trip>>} res
     * @return {Promise<void>}
     */
    protected createTrip = async (req: Request<unknown, unknown, Trip>, res: Response<WithId<Trip>>): Promise<void> => {
        try {
            const trip = req.body;

            // if (!isDateValid(trip.start)) {
            //     return handleInvalidRequest('start', res);
            // }
            // if (!isDateValid(trip.end)) {
            //     return handleInvalidRequest('end', res);
            // }
            // if (!isTripCodeValid(trip.code)) {
            //     return handleInvalidRequest('code', res);
            // }
            // if (!isSeLocatorIdValid(trip.seLocatorId)) {
            //     return handleInvalidRequest('seLocatorId', res);
            // }
            // if (!isCustomerRequestIdValid(trip.customerRequestIds)) {
            //     return handleInvalidRequest('customerRequestIds', res);
            // }
            // if (!isBenefitLimitsValid(trip.benefitLimits)) {
            //     return handleInvalidRequest('benefitLimits', res);
            // }
            // if (!isPartyComposition(trip.partyComposition)) {
            //     return handleInvalidRequest('partyComposition', res);
            // }

            // We need to generate unique, easy to read trip code
            trip.code = await this.acquireTripCode();

            trip.created = new Date();
            trip.updated = new Date();
            trip.status = TripStatus.OPEN;

            this.logger.info({
                sourceName: this.name,
                message: `Received new trip for creation in MongoDB`,
                data: { ...trip }
            });

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Trip>('entities', 'trips');

            // get product benefits limits for the trip
            const entitlementBenefit = await this.entitlementBenefitClient.getEntitlementBenefitByContract(trip.contractKey, trip.customerGroup);
            if (!entitlementBenefit) {
                return getResponse(res, ServerResponseCode.BAD_REQUEST, {
                    message: `Unable to find product benefit for contract ${trip.contractKey}`
                });
            }

            const finalTrip: Trip = { ...trip, benefitLimits: entitlementBenefit.limits };
            const result = await collection.insertOne(finalTrip);
            const persistedTrip: WithId<Trip> = {
                ...finalTrip,
                _id: result.insertedId
            };

            await this.miStreamTripSender.send(MiEvents.TRIP, persistedTrip);

            return getResponse(res, ServerResponseCode.OK, persistedTrip);
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                error,
                message: `Failed creating new trip.`,
                data: { ...req.body }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, error);
        }
    };

    /**
     * Edit trip in MongoDB
     * @param {e.Request<{id: string}, unknown, Trip>} req
     * @param {e.Response<WithId<Trip>>} res
     * @return {Promise<void>}
     */
    protected editTrip = async (
        req: Request<
            {
                tripCode: string;
            },
            unknown,
            Trip
        >,
        res: Response<WithId<Trip>>
    ): Promise<void> => {
        try {
            // TODO: Check if valid body
            const trip = req.body;
            const { tripCode } = req.params;
            this.logger.log(`Editing trip for tripCode: ${tripCode}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Trip>('entities', 'trips');

            const date = new Date();
            trip.created = new Date(trip.created);
            trip.updated = date;

            if (trip.vrn) {
                // Find existing trip by tripCode
                const existingTrip = await collection.findOne<Trip>({
                    code: tripCode
                });
                if (existingTrip && existingTrip.vrn !== trip.vrn) {
                    // Update SeLocatorId when VRN changes
                    const entitlementClient = new EntitlementClient({
                        httpClient: new HttpClient({
                            authClient: this.authClient
                        }),
                        connector: this.connector
                    });

                    // Create note for SE Locator ID update
                    const noteClient = new NoteClient({
                        httpClient: new HttpClient({
                            authClient: this.authClient
                        }),
                        connector: this.connector
                    });
                    // TODO: Update note with correct createdBy when we have users on frontend.
                    const note: AlertNote = {
                        title: 'VRN Changed',
                        type: NoteType.ALERT,
                        createdBy: undefined,
                        updatedBy: undefined,
                        created: date,
                        updated: date,
                        parentEntity: {
                            type: NoteEntityType.TRIP,
                            id: tripCode
                        },
                        namespace: Namespace.EUOPS,
                        content: {
                            name: 'VRN Changed',
                            severity: AlertSeverity.MEDIUM,
                            from: 'system',
                            body: `VRN changed from ${existingTrip.vrn} to ${trip.vrn}`
                        },
                        context: {}
                    };
                    await noteClient.insertNote(note);
                }
            }

            await collection.updateOne(
                { code: tripCode },
                { $set: trip },
                {
                    upsert: true
                }
            );

            await this.miStreamTripSender.send(MiEvents.TRIP, trip);

            //Adding audit logs
            if (tripCode) {
                const trace = this.auditClient.getTrace(Namespace.EUOPS, tripCode);
                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `Trip API: Updated Trip Details for tripCode: ${tripCode}`,
                        data: { ...trip }
                    },
                    this.context.store.operatorId as number
                );
            }

            const result = await collection.findOne({ code: trip.code });
            if (!result) {
                throw new Error(`Failed while finding trip with code: ${trip.code}`);
            }
            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to edit trip for tripCode: ${req.params.tripCode || 'no tripCode given in request'}`,
                data: { ...req.params, ...req.body }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Delete trip by tripCode
     * @param {Request<{tripCode: string}, unknown, unknown>} req
     * @param {Response<WithId<Trip>>} res
     * @return {Promise<void>}
     */
    protected deleteTrip = async (
        req: Request<
            {
                tripCode: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<Trip>>
    ): Promise<void> => {
        const { tripCode } = req.params;
        try {
            this.logger.log(`Delete trip with tripCode: ${tripCode}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Trip>('entities', 'trips');

            const result = collection.findOneAndDelete({
                code: tripCode
            });

            //Added audit logs
            if (tripCode) {
                const trace = this.auditClient.getTrace(Namespace.EUOPS, tripCode);
                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `Trip API: Trip deleted for tripCode: ${tripCode}`,
                        data: { ...result }
                    },
                    this.context.store.operatorId as number
                );
            }

            if (result) {
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to delete trip with tripCode ${tripCode || 'no tripCode given in request'}`,
                data: { tripCode }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get paginated list of secure locations for given tripCode
     * @param {Request} req
     * @param {Response<WithId<ItemLocation>>} res
     * @return {Promise<void>}
     */
    protected getSecureLocations = async (
        req: Request<
            { tripCode: string },
            unknown,
            unknown,
            {
                limit?: string;
                skip?: string;
            }
        >,
        res: Response<WithId<ItemLocation>>
    ): Promise<void> => {
        try {
            const { tripCode } = req.params;
            const limit = parseInt(req.query.limit || '100', 10);
            const skip = parseInt(req.query.skip || '0', 10);
            this.logger.log(`Getting secure locations for tripCode: ${tripCode}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<ItemLocation>('entities', 'secureLocations');

            const cursor = collection.find<ItemLocation>({ tripCode });
            const query: PaginationQuery = { limit, skip };
            const result = await MongodbUtils.paginate(cursor, query, 100);

            if (result && result.results.length) {
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting secure locations for tripCode: ${req.params.tripCode || 'no tripCode given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Create new secure location
     * @param {e.Request<unknown, unknown, ItemLocation>} req
     * @param {e.Response<WithId<ItemLocation>>} res
     * @return {Promise<void>}
     */
    protected createNewSecureLocation = async (req: Request<unknown, unknown, ItemLocation>, res: Response<WithId<ItemLocation>>): Promise<void> => {
        try {
            const secureLocation = req.body;

            if (!isTripCodeValid(secureLocation.tripCode)) {
                return handleInvalidRequest('tripCode', res);
            }
            if (!isNumber(secureLocation.createdBy)) {
                return handleInvalidRequest('createdBy', res);
            }
            if (!isNumber(secureLocation.updatedBy)) {
                return handleInvalidRequest('updatedBy', res);
            }
            if (!isValidString(secureLocation.name)) {
                return handleInvalidRequest('name', res);
            }
            if (!isValidString(secureLocation.note)) {
                return handleInvalidRequest('note', res);
            }
            if (!isSecure(secureLocation.isSecure)) {
                return handleInvalidRequest('isSecure', res);
            }
            if (!isAddress(secureLocation.address)) {
                return handleInvalidRequest('address', res);
            }
            secureLocation.created = new Date();
            secureLocation.updated = new Date();

            this.logger.info({
                sourceName: this.name,
                message: `Received new secure location for creation in MongoDB for tripCode: ${secureLocation.tripCode}`,
                data: { ...secureLocation }
            });

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<ItemLocation>('entities', 'secureLocations');

            const result = await collection.insertOne(secureLocation);
            const persistedSecureLocation: WithId<ItemLocation> = {
                ...secureLocation,
                _id: result.insertedId
            };
            await this.miStreamItemLocationSender.send(MiEvents.ITEM_LOCATION, persistedSecureLocation);

            return getResponse(res, ServerResponseCode.OK, persistedSecureLocation);
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                message: `Failed creating new secure location for tripCode: ${req.body.tripCode || 'no tripCode given in request'}`,
                data: { error }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, error);
        }
    };

    /**
     * Edit secure location in MongoDB
     * @param {e.Request<{id: string}, unknown, ItemLocation>} req
     * @param {e.Response<WithId<ItemLocation>>} res
     * @return {Promise<void>}
     */
    protected editSecureLocation = async (
        req: Request<
            {
                id: string;
            },
            unknown,
            ItemLocation
        >,
        res: Response<WithId<ItemLocation>>
    ): Promise<void> => {
        try {
            // TODO: Check if valid body
            const secureLocation = req.body;
            const { id } = req.params;
            this.logger.log(`Editing secure location for tripCode: ${secureLocation.tripCode} and ObjectId: ${id}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<ItemLocation>('entities', 'secureLocations');

            const date = new Date();
            await collection.updateOne(
                { _id: new ObjectId(id) },
                {
                    $set: secureLocation,
                    updated: date
                },
                { upsert: true }
            );

            const result = await collection.findOne({ _id: new ObjectId(id) });
            if (!result) {
                throw new Error(`Failure while reading trip for id ${id}`);
            }
            await this.miStreamItemLocationSender.send(MiEvents.ITEM_LOCATION, result);

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to edit secure location for ObjectId: ${req.params.id || 'no ObjectId given in request'}`,
                data: { ...req.body }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Delete secure location by ObjectId
     * @param {Request<{id: string}, unknown, unknown>} req
     * @param {Response<WithId<ItemLocation>>} res
     * @return {Promise<void>}
     */
    protected deleteSecureLocation = async (
        req: Request<
            {
                id: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<ItemLocation>>
    ): Promise<void> => {
        const { id } = req.params;
        try {
            this.logger.log(`Delete secure location with ObjectId ${id}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<ItemLocation>('entities', 'secureLocations');

            const result = collection.findOneAndDelete({
                _id: new ObjectId(id)
            });

            //Added audit logs
            if (id) {
                const trace = this.auditClient.getTrace(Namespace.EUOPS, id);
                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `Trip API: Secure location delete audit for id: ${id}`,
                        data: { ...result }
                    },
                    this.context.store.operatorId as number
                );
            }

            if (result) {
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to delete secure location with ObjectId ${id || 'no ObjectId given in request'}`,
                data: { id }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
}

type TripActionsAndOverdues = {
    actions: WithId<Action>[];
    overdueCount: number;
};

/**
 * Handle invalid request
 * @param {string} name
 * @param {e.Response} res
 */
function handleInvalidRequest(name: string, res: Response) {
    const response: ErrorResponse = {
        status: 'INVALID_REQ',
        error: { code: 'ERROR', msg: `Invalid format of ${name}` }
    };
    return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
}

function isTripCodeValid(tripCode: unknown) {
    return typeof tripCode === 'string' && tripCode.length > 0;
}

function isSeLocatorIdValid(id: unknown) {
    return typeof id === 'number';
}

function isCustomerRequestIdValid(customerRequestId: unknown) {
    return isArray(customerRequestId) && customerRequestId.length > 0;
}

function isBenefitLimitsValid(benefitLimits: unknown) {
    return isArray(benefitLimits) && benefitLimits.length > 0 && benefitLimits.every((limit) => typeof limit === 'string');
}

function isDateValid(date: unknown) {
    return date instanceof Date || typeof date === 'string';
}

function isPartyComposition(partyComposition: unknown): partyComposition is PartyComposition {
    const { adultCount, childrenCount, pets, petCount, petNote } = partyComposition as PartyComposition;
    return typeof adultCount === 'number' && typeof childrenCount === 'number' && typeof pets === 'boolean' && typeof petCount === 'number' && typeof petNote === 'string';
}

function isNumber(val: unknown): val is number {
    return typeof val === 'number';
}

function isValidString(val: unknown): val is string {
    return typeof val === 'string' && val.length > 0;
}

function isSecure(isSecure: unknown) {
    return typeof isSecure === 'boolean';
}

function isAddress(address: unknown): address is Address {
    const { addressLines, postcode, houseNoName } = address as Address;
    return Array.isArray(addressLines) && addressLines.every((line) => typeof line === 'string') && (postcode === null || typeof postcode === 'string') && typeof houseNoName === 'string';
}
