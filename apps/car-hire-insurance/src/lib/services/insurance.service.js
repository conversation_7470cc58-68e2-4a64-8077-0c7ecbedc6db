'use strict';

const Q = require('q'),
    _ = require('lodash'),
    logger = require('winston'),
    coopers = require('./coopers/coopers.service'),
    insurerVehicleSvc = require('./insurer-vehicle.service'),
    MobilityInsurance = require('@aa/mobility-models/lib/mobility-insurance.model'),
    appointmentSvc = require('./coopers/coopers-appointment.service'),
    insurerErrorsFactory = require('@aa/mobility-models/lib/factories/insurer-errors.factory'),
    RafVehicle = require('@aa/mobility-models/lib/raf-vehicle.model'),
    _path = coopers.url('/Appointment'),
    _millisecPerMinute = 60 * 1000;

function getAppointment(insuranceRefId, authCode) {
    return coopers.call3(`${_path}/${insuranceRefId}`, coopers.methods.GET, null, authCode).then((jsonInsuranceDetails) => {
        jsonInsuranceDetails.start = new Date(jsonInsuranceDetails.start);
        jsonInsuranceDetails.finish = new Date(jsonInsuranceDetails.finish);
        return jsonInsuranceDetails;
    });
}

function formatAppointment(mobilityTask) {
    const expectedStart = mobilityTask.schedule().arrive() || new Date(),
        expectEndTime = new Date(expectedStart.getTime() + (mobilityTask.fault().repairMinutes() - 1) * _millisecPerMinute);
    return {
        stockId: mobilityTask.rental().insurance().insuredVehicleId(),
        wipNumber: `${mobilityTask.id()}`, // this is the task id ..
        customerRegistration: mobilityTask.vehicle().registration(),
        expectedStart: expectedStart,
        expectedFinish: expectEndTime,
        comments: `hire cr ${mobilityTask.customerRequestId()}`
    };
}

function upsertAppointment(mobilityTask, authCode) {
    const body = formatAppointment(mobilityTask);
    let path = null,
        method = null;

    if (mobilityTask.rental().insurance().id()) {
        path = `${_path}/${mobilityTask.rental().insurance().id()}`;
        method = coopers.methods.PUT;
    } else {
        path = _path;
        method = coopers.methods.POST;
    }
    logger.info(`insurance.service.appointment :: task ${mobilityTask.id()} path ${path} method ${method}`, body);
    return coopers.call3(path, method, body, authCode);
}

function newContract(details) {
    details.contractDetails = {
        id: null,
        stockId: null,
        customerRegistration: null,
        start: null,
        finish: null,
        wipNumber: null,
        comments: null,
        state: null,
        vehicle: {
            registration: null,
            stockReference: null,
            vin: null
        }
    };
    return details;
}

/**
 * read details from coopers
 * @param  {BookingRequest} details
 * @return {Promise<BookingRequest>} on success resolves to BookingRequest
 */
function readHireContract(details) {
    // retreive state of contract if we have a referrence to it
    if (details.mobilityTask.rental().insurance().id()) {
        return getAppointment(details.mobilityTask.rental().insurance().id(), details.authToken)
            .then((contractDetails) => {
                details.contractDetails = contractDetails;
                // now get vehicle on that contract
                return insurerVehicleSvc.vehicleStock(contractDetails.stockId, details.authToken);
            })
            .then((vehicleStockResp) => {
                details.contractDetails.vehicle = vehicleStockResp;
                return details;
            })
            .catch((err) => {
                if (insurerErrorsFactory.notFound(err)) {
                    // we can recover from this
                    details.mobilityTask.rental().insurance().id(null);
                    return Q.resolve(newContract(details));
                }
                // can't recover abort ..
                return Q.reject(err);
            });
    }

    // this is the first time so we create an empty instance
    return Q.resolve(newContract(details));
}

/**
 * register vehicle
 * @param  {BookingRequest} details of the request
 * @return {Promise<BookingRequest>}  on success resolves to BookingRequest
 */
function registerVehicle(details) {
    let stockRef = null;
    logger.info(`car-hire-insurnace-service.registerVehicle :: task id ${details.mobilityTask.id()} vehicle to register ${details.mobilityTask.rental().hireVehicle().regNo()}`);

    switch (details.mobilityTask.rental().hireVehicle().supplierTypeCode()) {
        case RafVehicle.SUPPLIER_TYPE_CODES.AA:
        case RafVehicle.SUPPLIER_TYPE_CODES.OU:
            stockRef = `${details.mobilityTask.rental().hireVehicle().supplierTypeCode()}-${details.mobilityTask.rental().hireVehicle().id()}`;
            break;
        default:
            stockRef = `${details.mobilityTask.rental().hireVehicle().supplierTypeCode().substring(0, 2)}${details.mobilityTask.rental().hireVehicle().regNo()}`;
            break;
    }

    // if the reg no on the contract is the same as that we are hireing out then no need to make any further changes
    if (details.mobilityTask.rental().hireVehicle().regNo() === details.contractDetails.vehicle.registration && details.contractDetails.vehicle.registration !== null) {
        return Q.resolve(details);
    }

    // this should be fairly random number ..and no more than 9 characters ...  limit is 10
    return insurerVehicleSvc.register(stockRef, details.mobilityTask.rental().hireVehicle(), details.authToken).then((insuredVehicleDetails) => {
        logger.info(`car-hire-insurnace-service.registerVehicle :: task id ${details.mobilityTask.id()} registered vehicle  ${insuredVehicleDetails.id} stockRef ${stockRef}`);
        // store the insurrer vehicle referrence we just got to the rental vehicle ...

        if (!details.mobilityTask.rental().insurance().insuredVehicleId()) {
            const existingSupNetworkCode = details.mobilityTask.rental().insurance().supNetworkCode();
            details.mobilityTask.rental().insurance(new MobilityInsurance());
            details.mobilityTask.rental().insurance().supNetworkCode(existingSupNetworkCode);
        }
        details.mobilityTask.rental().insurance().insuredVehicleId(insuredVehicleDetails.id);
        return details;
    });
}

/**
 * create a provisional booking
 * @param  {BookingRequest} details of the requets
 * @return {Promise<BookingRequest>} on success returns booking request
 */
function provisionalBooking(details) {
    // set the appointment to the mobiltity

    logger.info(
        `car-hire-insurnace-service.provisionalBooking :: task id ${details.mobilityTask.id()} attempting to book vehicle ${details.mobilityTask
            .rental()
            .insurance()
            .insuredVehicleId()} for coopers id ${details.mobilityTask.rental().insurance().id() || 'not set'}`
    );

    return upsertAppointment(details.mobilityTask, details.authToken)
        .then((appointmentResp) => {
            // this is contract details ...
            details.mobilityTask.rental().insurance().id(appointmentResp.id);
            details.contractDetails = appointmentResp;
            logger.info(
                `car-hire-insurnace-service.provisionalBooking :: task id ${details.mobilityTask.id()} insurance id ${details.mobilityTask.rental().insurance().id()} vehicle ${details.mobilityTask
                    .rental()
                    .insurance()
                    .insuredVehicleId()} booked for ${details.mobilityTask.rental().insurance().id()}`
            );
            return details;
        })
        .catch((errResp) => {
            // look if we have double booking ...
            const doubleBooked = _.find(errResp.errors, function (obj) {
                return obj.errorCode === 'APPT-142';
            });

            if (doubleBooked) {
                const caseConflict = _.find(doubleBooked.data, 'id');
                return appointmentSvc.getAppointment(caseConflict.id, details.authToken).then((details) => {
                    errResp.errors[0].message = `${errResp.errors[0].message} vehicle already reseverd by task ${details.wipNumber}`;
                    return Q.reject(errResp);
                });
            }

            return Q.reject(errResp);
        });
}

module.exports = {
    /**
     * booking car hire
     * @param  {BookingRequest} details of the booking
     * @return {Promise<ContractDetails>}         on success return ContractDetails
     */
    book: (bookScope) => {
        return readHireContract(bookScope)
            .then(registerVehicle)
            .then(provisionalBooking)
            .then((details) => details.contractDetails);
    },

    deleteAppointment: (insuranceRefId, authCode) => {
        return coopers.call3(`${_path}/${insuranceRefId}`, coopers.methods.DELETE, null, authCode).catch((err) => {
            return insurerErrorsFactory.notFound(err) ? Q.resolve() : Q.reject(err);
        });
    },

    finish: (insuranceRefId, insuranceOptionId, authToken) => {
        let _insuranceOption;
        return appointmentSvc
            .getInsuranceOptions(insuranceRefId, authToken)
            .then((options) => {
                _insuranceOption = _.find(options, (opt) => insuranceOptionId === opt.insuranceID);
                return appointmentSvc.finish(insuranceRefId, authToken);
            })
            .then((outcome) => {
                outcome.insuranceOption = _insuranceOption;
                return outcome;
            });
    },
    getHireContract: readHireContract
};
