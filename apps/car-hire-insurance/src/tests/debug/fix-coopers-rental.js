//require('dotenv').config();

const _WEB_OPERATOR_ID = 9077173;
// process.env.aah2proxy = 'https://aah2rb02';
const authToken = {
    jwt: '475e56fb27d9dd3e3ee9f5eee19b44bb805188d6a69b9d6afab3f27703d35e8ab1499db2d9fe3fa449f111880c2c9637a159c7179f0180b2c4f2520f7cb45226657d6ef6569379902753e8910b8ff3ce6ddc9feb0a42d1ba4a24c7809a7d9d8d980b2d37334ea22fa493a24f737f7494d4b85206aa05178341123ce8c0dc8449a5b1c23a6c6842074144f212156f6be364506faa263c04863ebb88fd098e6c9bc8b2c3065eec85c29b1773345b3f3020ad83703212a14e429ede3948c9818387cd393ce830eb235db0f8ddcd627c732d821d03e25c01ff41eb27c3eb558882f0b446bad271d4312fa2d80cfbbf1814a551d5bb98e2c93a5aff674a99d4ee62abd13efb0efca2c4fc15d3fdd30b5999473a231ed2debc5487060aed6a9032284f6fb375e9b651c424c8ac29c937ed2e7fa43c32109f1a5793281b7e9a79222f33c6636646ca35403c262558f6c41e679d265db89c46ee3441d1f4a7f2cb044a16a14cdc4105c911ea18bc3ef2d3d361be80ad06ea5eea78a1e0a5146512f0592a0c7f4405aed5640a2c911f93f305037cccc4257ef4c4e7b8fb81f73c4d7482373c90ccba10353da1f8a989aaad3046ea8e8d2867dcce546f9c85ca006cc1fff1cb7338e84a37970c9ca1dcdd1344dfc769958d341dcfa2e5abb51fbbb7cf7805cee3d91b0c06c57c57c9e4f2d3b408ce42d94289740158afdf0d1ac0354b935eb5462bbcd378f4955707981afec9116c76f67b25eb230bb9025550240b4b57e0f531cc93dd8b2c81ba216ebdd12659deeaec8d11f27634e587628f3f03832e5fadac1cb378f669dbc3e9be529e48cd5c70b44ddedf812452c79b9e4a77905947e031783739de2c42686a6f0e7cf3d3f0110b50e6c40516d8fea138daed7f32c408ef38e6ad3edc3581050dea113ab7c924660d4ea818a4b49b876f00eaf55abce2ba0759f2e7023351cdfa8b1ca5a12d876e149e43ffe43c5ba18c74390ed5bcc199ae585bed22b64538466dff3568fc224c748b114198713710e31cdc34b1569f663f9de72b505c55e7075a6f1f07a98bb7f74bdf028daba7c81909578f06aa4a19e20555fb974e604bc9ee61f6a1d2bbb7ebf7f78f80dd3cc3c9a7b0b08a76520604a03c347b887fc67cb669d1a18f1258f308e2f2bc89cd1f51e504a99a61803285dd08cbe93b9ce8666d7a2bd974748ecbc403a4ff693e7c5d09194df38d2afa71410b29d583e80454f115a7a259c3d99c33ac2b33899897e4a6c0b7aeae900586b7802f6b27ab226fb67dc8aceb41b369b1288c21f50b66d33499223933997e09c5ec1ed8cb887002c8235ef1eddd52',
    operatorId: _WEB_OPERATOR_ID
};
const MobilityTask = require('@aa/mobility-models-common/lib/mobility-task.model');
const appointmentScv = require('../../lib/services/coopers/coopers-appointment.service');
const vehicleSvc = require('../../lib/services/coopers/coopers-vehicle.service');
const InsuranceTerms = require('@aa/mobility-models/lib/insurance-terms.model');
const InsuranceOption = require('@aa/mobility-models/lib/insurance-option.model');
const CarRental = require('@aa/mobility-models/lib/car-rental.model');
const carHireDriversSvc = require('../../lib/services/car-hire-drivers.service');
const CarHireDriver = require('@aa/mobility-models/lib/car-hire-driver.model');
const { Address } = require('@aa/data-models/common');
const restfulRequest = require('@aa/endpoints').restfulRequest;
const TaskWriteResponse = require('@aa/malstrom-models/lib/task-write-response.model');
const RafVehicle = require('@aa/mobility-models/lib/raf-vehicle.model');
const HireSite = require('@aa/mobility-models/lib/hire-site.model');
const Resource = require('@aa/malstrom-models/lib/resource.model');

function readTask(taskId) {
    const req = {
        baseUrl: process.env.aah2proxy,
        uri: `/api/mobility-task-service/task/${taskId}`,
        method: 'GET',
        headers: {
            'Content-type': 'application/json',
            'auth-token': authToken.jwt
        },
        timeout: 10000
    };

    return restfulRequest(req).then((resp) => {
        return new MobilityTask(resp.data.mobilityTask);
    });
}

function writeTask(task) {
    const req = {
        baseUrl: process.env.aah2proxy,
        uri: `/api/mobility-task-service/task`,
        method: 'POST',
        headers: {
            'Content-type': 'application/json',
            'auth-token': authToken.jwt
        },
        body: {
            mobilityTask: task.toJSON()
        },
        timeout: 10000
    };

    return restfulRequest(req).then((resp) => {
        return new TaskWriteResponse(resp.data);
    });
}

function resourceSearch(regNo) {
    const req = {
        baseUrl: process.env.aah2proxy,
        uri: `/api/mobility-task-service/resource/reg/${regNo}`,
        method: 'GET',
        headers: {
            'Content-type': 'application/json',
            'auth-token': authToken.jwt
        },
        timeout: 10000
    };

    return restfulRequest(req).then((resp) => {
        return {
            hireSite: new HireSite(resp.data.hireSite),
            rafVehicle: new RafVehicle(resp.data.rafVehicle),
            resource: new Resource(resp.data.resource)
        };
    });
}

function createAddress(raw) {
    const addressLines = raw.split(',');
    const postcode = addressLines.pop();
    const x = new Address({
        addressLines,
        postcode
    });
    console.log(x.toJSON());
    return x;
}

function createDrivers(raw) {
    const { address } = raw;
    raw.address = null;
    const d = new CarHireDriver(raw);
    d.contact().name(raw.name);
    d.contact().telephone(raw.phone);
    d.contact().email(raw.email);

    if (address) {
        d.address(createAddress(address));
    }

    return d;
}

const token = {
    operatorId: 12121,
    coopersToken: null
};

let scope = {
    rental: new CarRental()
};

//const taskId = 177599931;
//const taskId = 180525635;
const taskId = parseInt(process.argv[2]);

readTask(taskId)
    .then((task) => {
        scope.task = task;
        console.log(task.schedule().resource().toJSON());
        console.log(task.rental().mainDriver().toJSON());
        return appointmentScv.getAppointmentByWipNumber(taskId, token);
    })
    .then((res) => {
        console.log('------------ cooper appointment --------------');
        console.log(res);
        scope.task.rental().insurance().id(res.id);
        scope.task.rental().insurance().insuredVehicleId(res.stockId);
        console.log(`${scope.task.rental().insurance().id()} - ${scope.task.rental().insurance().insuredVehicleId()}`);
        process.exit(0);
        return appointmentScv.getInsuranceOptions(res.id, token);
    })
    .then((insOpts) => {
        scope.insOpts = insOpts[0];
        console.log('------------ cooper insurance-options ----------------');
        scope.task.rental().insurance().insuranceOption(new InsuranceOption(insOpts[0]));
        scope.task.rental().insurance().insuranceOption().id(insOpts[0].insuranceId);
        return appointmentScv.insuranceTerms(scope.task.rental().insurance().id(), token);
    })
    .then((insTerms) => {
        console.log('------------ cooper insurance-terms ----------------');
        scope.insTerms = insTerms;
        scope.task.rental().insurance().insuredTerms(new InsuranceTerms(insTerms));
        return vehicleSvc.vehicleStock(scope.task.rental().insurance().insuredVehicleId(), token);
    })
    .then((vRes) => {
        console.log('------------ cooper vehicle details ----------------');
        console.log(vRes);

        process.exit(0);
        return resourceSearch(vRes.registration);
    })
    .then((resDetails) => {
        console.log('------------ res details ----------------');
        console.log(resDetails.hireSite.toJSON());
        console.log(resDetails.rafVehicle.toJSON());
        console.log(resDetails.resource.toJSON());
        scope.task.rental().hireVehicle(resDetails.rafVehicle);
        return carHireDriversSvc.getDrivers(scope.task.rental().insurance().id(), token);
    })
    .then((drivers) => {
        console.log('------------ drivers ----------------');
        console.log(drivers);
        if (drivers.mainDriver) {
            scope.task.rental().mainDriver(createDrivers(drivers.mainDriver));
            scope.task.rental().mainDriver().id(scope.task.rental().insurance().id());
        }
        if (drivers.additionalDrivers.length > 0) {
            scope.task.rental().additionalDrivers(drivers.additionalDrivers.map((driver) => createDrivers(driver)));
        }
        console.log(scope.task.rental().toJSON());
        return writeTask(scope.task);
    })
    .then((resp) => {
        console.log(resp.toJSON());
        process.exit(0);
    })
    .catch((err) => {
        console.log(err);
    });
