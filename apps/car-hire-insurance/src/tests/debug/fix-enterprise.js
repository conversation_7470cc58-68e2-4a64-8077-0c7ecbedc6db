//require('dotenv').config();

const _WEB_OPERATOR_ID = 9077173;
// process.env.aah2proxy = 'https://aah2rb02';
const authToken = {
    jwt: '475e56fb27d9dd3e3ee9f5eee19b44bb805188d6a69b9d6afab3f27703d35e8ab1499db2d9fe3fa449f111880c2c9637a159c7179f0180b2c4f2520f7cb45226657d6ef6569379902753e8910b8ff3ce6ddc9feb0a42d1ba4a24c7809a7d9d8d980b2d37334ea22fa493a24f737f7494d4b85206aa05178341123ce8c0dc8449a5b1c23a6c6842074144f212156f6be364506faa263c04863ebb88fd098e6c9bc8b2c3065eec85c29b1773345b3f3020ad83703212a14e429ede3948c9818387cd393ce830eb235db0f8ddcd627c732d821d03e25c01ff41eb27c3eb558882f0b446bad271d4312fa2d80cfbbf1814a551d5bb98e2c93a5aff674a99d4ee62abd13efb0efca2c4fc15d3fdd30b5999473a231ed2debc5487060aed6a9032284f6fb375e9b651c424c8ac29c937ed2e7fa43c32109f1a5793281b7e9a79222f33c6636646ca35403c262558f6c41e679d265db89c46ee3441d1f4a7f2cb044a16a14cdc4105c911ea18bc3ef2d3d361be80ad06ea5eea78a1e0a5146512f0592a0c7f4405aed5640a2c911f93f305037cccc4257ef4c4e7b8fb81f73c4d7482373c90ccba10353da1f8a989aaad3046ea8e8d2867dcce546f9c85ca006cc1fff1cb7338e84a37970c9ca1dcdd1344dfc769958d341dcfa2e5abb51fbbb7cf7805cee3d91b0c06c57c57c9e4f2d3b408ce42d94289740158afdf0d1ac0354b935eb5462bbcd378f4955707981afec9116c76f67b25eb230bb9025550240b4b57e0f531cc93dd8b2c81ba216ebdd12659deeaec8d11f27634e587628f3f03802e61adac1cb378f669dbc3e9be529e48cd5c70b44dded8830a52c79b9e4a0b983571f71a76053aca2548367e790f43cbddf21b6873f4f37136e2c8920ed9e04714cd37c309a2a010900bf8292398104ae0902c49132293249dbcb3d34d0dcbea7c89cfb07c3fd29f0a134fa983fd25b1d826986d508312d2e307788cf632351cf6a5b289e84268d7469445562f69c72920c7666f609e2b25b076141cd443da2dd750db5c049ad03c4b7342c41378723f17aff394a050e530a5e7a2a50f30028e3bc66c42f36147e7a2573872db9f5bafa2f9b78cf2a0d993a5c71ca3c898aac8c018110738b41c080ce556b37f965fd4bb940435e515c7dc9cbbd8393ebf1ff49f68ba20a5f02affee3ba8966a707f3aac6b748cf8f319bfd46d0968782e1160d98f29c2066b1a3ffc869d725f8c6cb58c57d6909553a60c3e97b7a2b5b6fee9cf9d9456b095b02d6901ce1c57ab50d6b6d866e143ae3a817d8b2538f800f1004a34c37004819c44c5b4815a5cf43545e5e9a942',
    operatorId: _WEB_OPERATOR_ID
};
const MobilityTask = require('@aa/mobility-models-common/lib/mobility-task.model');
const CarRental = require('@aa/mobility-models/lib/car-rental.model');
const restfulRequest = require('@aa/endpoints').restfulRequest;
const TaskWriteResponse = require('@aa/malstrom-models/lib/task-write-response.model');
const RafVehicle = require('@aa/mobility-models/lib/raf-vehicle.model');
const HireSite = require('@aa/mobility-models/lib/hire-site.model');
const Resource = require('@aa/malstrom-models/lib/resource.model');
const { Vehicle } = require('@aa/data-models/common');

function readTask(taskId) {
    const req = {
        baseUrl: process.env.aah2proxy,
        uri: `/api/mobility-task-service/task/${taskId}`,
        method: 'GET',
        headers: {
            'Content-type': 'application/json',
            'auth-token': authToken.jwt
        },
        timeout: 10000
    };

    return restfulRequest(req).then((resp) => {
        return new MobilityTask(resp.data.mobilityTask);
    });
}

function writeTask(task) {
    const req = {
        baseUrl: process.env.aah2proxy,
        uri: `/api/mobility-task-service/task`,
        method: 'POST',
        headers: {
            'Content-type': 'application/json',
            'auth-token': authToken.jwt
        },
        body: {
            mobilityTask: task.toJSON()
        },
        timeout: 10000
    };

    return restfulRequest(req).then((resp) => {
        return new TaskWriteResponse(resp.data);
    });
}

function resourceSearch(regNo) {
    const req = {
        baseUrl: process.env.aah2proxy,
        uri: `/api/mobility-task-service/resource/reg/${regNo}`,
        method: 'GET',
        headers: {
            'Content-type': 'application/json',
            'auth-token': authToken.jwt
        },
        timeout: 10000
    };

    return restfulRequest(req).then((resp) => {
        return {
            hireSite: new HireSite(resp.data.hireSite),
            rafVehicle: new RafVehicle(resp.data.rafVehicle),
            resource: new Resource(resp.data.resource)
        };
    });
}

const token = {
    operatorId: 12121,
    coopersToken: null
};
function vehicle(regNo) {
    const req = {
        baseUrl: process.env.aah2proxy,
        uri: `/api/vehicle-details-service/vehicle/${regNo}`,
        method: 'GET',
        headers: {
            'Content-type': 'application/json',
            'auth-token': authToken.jwt
        },
        timeout: 10000
    };

    return restfulRequest(req).then((resp) => {
        return new Vehicle(resp.data.vehicle);
    });
}

let scope = {
    rental: new CarRental()
};

//const taskId = 177599931;
//const taskId = 180525635;
const taskId = parseInt(process.argv[2]);
const regNo = process.argv[3];

const copyRental = require('./rental');

readTask(taskId)
    .then((task) => {
        scope.task = task;
        console.log(`hire reg no ${regNo}`);
        //	scope.task.rental(new CarRental(copyRental.rental));
        scope.task.rental().thirdPartyHire().logicalResourceId(9127183);
        scope.task.rental().thirdPartyHire().hireVehicle().id(9127183);
        scope.task.rental().thirdPartyHire().hireVehicle().resourceId(9127183);
        console.log(scope.task.rental().toJSON());
        //process.exit(0);
        /*
	scope.task.rental().hireVehicle().regNo(regNo);
	scope.task.rental().thirdPartyHire().hireVehicle().regNo(regNo);
	console.log(`rental.isThirdPartyHireSet() ${scope.task.rental().isThirdPartyHireSet()}`);
	console.log(`rental.thirdPartyHire().isSelfCheckout() ${scope.task.rental().thirdPartyHire().isSelfCheckout()}`);
        console.log(rental.thirdPartyHire().toJSON());
procss.exit(1);
	return vehicle(regNo) ;//scope.task.rental().thirdPartyHire().hireVehicle().regNo());
	})
	.then((experianData)=>{
	 scope.task.rental().thirdPartyHire().experianDetails(experianData);
	 console.log(scope.task.rental().toJSON());

process.exit(1);*/
        return writeTask(scope.task);
    })
    .then((resp) => {
        console.log(resp.toJSON());
        process.exit(0);
    })
    .catch((err) => {
        console.log(err);
    });
