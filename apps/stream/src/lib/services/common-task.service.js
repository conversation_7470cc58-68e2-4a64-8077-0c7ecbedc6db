const { Vehicle } = require('@aa/data-models/common');
const Schedule = require('@aa/malstrom-models/lib/task-schedule.model'),
    Recovery = require('@aa/malstrom-models/lib/recovery.model'),
    Appointment = require('@aa/malstrom-models/lib/appointment.model'),
    HireVehicle = require('@aa/mobility-models/lib/raf-vehicle.model');

const svc = {
    /**
     * Update hire car incoming recovery with raw.
     * @param {Object} raw - stream raw object.
     * @param {Model} IncommingRecoverySummary.
     * @returns {Promise<IncommingRecoverySummary>} - return updated model.
     */
    updateHireCarIncomingRecovery: (raw, model) => {
        //

        let RafVehicleRaw = {
            resourceId: raw.schedule.resource.id
        };

        if (raw.schedule.resource.aaVehicle) {
            RafVehicleRaw.regNo = raw.schedule.resource.aaVehicle.registration;
        }

        model.hireAppointment(new Appointment(raw.appointment));

        model.id(raw.id);
        model.tasks().hire = raw.id;
        model.sequence().hire = raw.sequence;

        //we were trying to create a HireVehicle with raw.vehicle. That's incorrect. The RafVehicle id is in
        // raw.schedule.resurce.id other than the recourseId we algo get the current location but it doesn't make sense
        // to place it here as it will be updated on the raf-vehicle channel
        model.hireVehicle(new HireVehicle(RafVehicleRaw));
        model.vehicleSchedule(new Schedule(raw.schedule));
        model.status(raw.status);

        return model;
    },

    /**
     * Update breakdown incoming recovery with raw.
     * @param {Object} raw - stream raw object.
     * @param {Model} IncommingRecoverySummary.
     * @returns {Promise<IncommingRecoverySummary>} - return updated model.
     */
    updateBreakdownIncomingRecovery: (raw, model) => {
        model.tasks().breakdown = raw.id;
        model.sequence().breakdown = raw.sequence;

        if (!model.vehicleSchedule()) {
            model.vehicleSchedule(new Schedule(raw.schedule));
        }
        if (!model.tasks().recovery) {
            model.recovery(new Recovery(raw.recovery));
        }
        return model;
    },

    updateRecovery: (raw, model) => {
        model.recovery(new Recovery(raw.recovery));
        model.vehicleSchedule(new Schedule(raw.schedule));

        model.tasks().recovery = raw.id;
        model.sequence().recovery = raw.sequence;

        if (!model.passengerSchedule()) {
            model.passengerSchedule(new Schedule(raw.schedule));
        }
        if (!model.vehicle()) {
            model.vehicle(new Vehicle(raw.vehicle));
        }
        model.recovery().adults(raw.recovery.adults);
        model.recovery().children(raw.recovery.children);
        model.recovery().dogs(raw.recovery.dogs);

        if (raw.status === 'HEAD' && !model.tasks().hire) {
            model.status(raw.status);
        }

        return model;
    },

    /**
     * update and publish incoming recovery.
     * @param {Object} item - task object to push in redis.
     * @param {Model} Model - task specific model.
     * @returns {Promise<redisResultPromise>} - returns updated.
     */
    updatePassengerRun: (item, model) => {
        model.tasks().passenger = item.id;
        model.sequence().passenger = item.sequence;

        model.passengerSchedule(new Schedule(item.schedule));
        model.vehicle(new Vehicle(item.vehicle));

        if (!model.recovery()) {
            model.recovery(new Recovery(item.recovery));
        }

        if (item.recovery) {
            model.recovery().adults(item.recovery.adults);
            model.recovery().children(item.recovery.children);
            model.recovery().dogs(item.recovery.dogs);
        }

        return model;
    }
};

module.exports = svc;
