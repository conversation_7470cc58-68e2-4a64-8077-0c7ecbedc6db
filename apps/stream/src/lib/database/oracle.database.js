const aaOracleUtility = require('@aa/oracle-utilities');
const oracledb = require('oracledb');

// const oracledb = require('oracledb');
const q = require('q');
const logger = require('winston');
const appName = 'stream';

// TODO: move this whole logic to the service
oracledb.fetchAsString = [oracledb.CLOB];

let cachedConnection = null;

/**
 * WARNING ...
 * ORACLE CONNECTIONS ARE CACHED ...
 * WHEN USING YOU SHOULD NOT CLOSE THE CONNECTION AFTERWARDS
 * THIS NEEDS TO BE FIXED
 *  LOTS OF UNGRY FACESS GO HERE
 */

module.exports = {
    init: () => {
        logger.info('oracle.database.init:: attemptig to connect to oracle');
        return aaOracleUtility
            .init({
                connectStrings: process.env.cshConnectStrings.split(','),
                user: process.env.cshUser,
                password: process.env.cshPassword,
                appName
            })
            .catch((err) => {
                logger.error('oracle.database.init:: failed to connect to oracle ', JSON.stringify(err));
                return q.reject();
            });
    },
    connect: () => {
        let def = q.defer();

        if (!cachedConnection) {
            aaOracleUtility.connect().then((connection) => {
                cachedConnection = connection;
                def.resolve(cachedConnection);
            });
        } else {
            def.resolve(cachedConnection);
        }

        return def.promise;
    },
    streamRead: async function stremRead(dbConn, sql, bindvars = []) {
        return new Promise((resolve, reject) => {
            const stream = dbConn.queryStream(sql, bindvars, { fetchArraySize: 100 });
            const results = [];
            stream.on('error', function (error) {
                reject(error);
            });
            stream.on('data', function (data) {
                results.push(data);
            });
            stream.on('end', function () {
                stream.destroy();
            });
            stream.on('close', function () {
                resolve(results);
            });
        });
    },
    cursorRead: (dbConn, sql, bindvars) => {
        const defer = q.defer();

        dbConn.execute(
            sql,
            bindvars,
            {
                outFormat: oracledb.OBJECT
            },
            function (error, result) {
                const results = [];

                if (error) {
                    defer.reject(error.message);
                    return;
                }
                const resultStream = result.outBinds.cursor.toQueryStream();

                resultStream.on('error', function (error) {
                    defer.reject(error);
                });

                resultStream.on('data', function (data) {
                    results.push(data);
                });

                resultStream.on('end', function () {
                    defer.resolve(results);
                });
            }
        );
        return defer.promise;
    },
    releaseConnection: () => {
        return aaOracleUtility.release(cachedConnection);
    },
    release: async (db) => {
        return await aaOracleUtility.release(db);
    }
};
