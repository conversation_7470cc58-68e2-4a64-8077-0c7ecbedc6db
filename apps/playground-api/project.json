{"name": "playground-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/playground-api/src", "projectType": "application", "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/apps/playground-api", "format": ["cjs"], "bundle": false, "main": "apps/playground-api/src/main.ts", "tsConfig": "apps/playground-api/tsconfig.app.json", "assets": ["apps/playground-api/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "playground-api:build"}, "configurations": {"development": {"buildTarget": "playground-api:build:development"}, "production": {"buildTarget": "playground-api:build:production"}}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/playground-api/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/playground-api/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "tags": []}