< {%
    import { setup } from '../../../tools/webstorm/request-utils';

    setup({ request, client, fallbackPort: 9999 });
%}

# get by task id
GET {{host}}:{{port}}/api/playground-api/document-client/documents/by-task-id/2137
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

###
# get document by id
GET {{host}}:{{port}}/api/playground-api/document-client/document/test1
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

###
# create document
POST {{host}}:{{port}}/api/playground-api/document-client/document
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "docName": "test1",
    "custReqId": 2000,
    "docType": 1,
    "container": "key-assist",
    "taskId": 1000,
    "mimeType": "text/plain"
}

###
# delete document by id
GET {{host}}:{{port}}/api/playground-api/document-client/document/test1
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

###
# update document by id
POST {{host}}:{{port}}/api/playground-api/document-client/document/347c6440-18fb-11ef-9bca-9785cb87414c
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "docName": "test1",
    "custReqId": 2000,
    "docType": 1,
    "container": "bnurp",
    "taskId": 1000,
    "mimeType": "text/plain"
}

###
# upload document
POST {{host}}:{{port}}/api/playground-api/document-client/upload
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "docName": "test1",
    "custReqId": 2000,
    "docType": 1,
    "container": "key-assist",
    "taskId": 1000,
    "mimeType": "text/plain",
    "data": "Hey ho!"
}

###
# download document
POST {{host}}:{{port}}/api/playground-api/document-client/download
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "docName": "test1",
    "container": "key-assist"
}
