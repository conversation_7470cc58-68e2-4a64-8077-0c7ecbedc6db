#< {%
#    import { setup } from '../../../tools/webstorm/request-utils';
#
#    setup({ request, client, fallbackPort: 9999 });
#%}

# Ensure cost updated
#51342705
POST {{host}}:9999/api/playground-api/ensure-cost-updated
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "taskId": 51342705,
    "trip": {
        "customerGroup": "ASST",
        "vrn": "ADC312",
        "externalRef": "ADC312",
        "productCode": "BCASP786482 - Austria Overseas ARC B2B",
        "contractKey": "BCASP786482",
        "customerKey": "BCASP786482",
        "start": "2025-03-04",
        "end": "",
        "customerRequestIds": [
            36286842
        ],
        "code": "CFW-5375",
        "created": "2025-03-04T11:42:14.511Z",
        "updated": "2025-03-04T11:45:02.661Z",
        "status": "OPEN",
        "benefitLimits": [
            "BEN001",
            "BEN002",
            "BEN003",
            "BEN004",
            "BEN005",
            "BEN006"
        ]
    }
}

###

