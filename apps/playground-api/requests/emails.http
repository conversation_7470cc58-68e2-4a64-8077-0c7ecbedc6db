< {%
    import { setup } from '../../../tools/webstorm/request-utils';

    setup({ request, client, fallbackPort: 9999 });
%}

# create document
POST {{host}}:9999/api/playground-api/email-template
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "type": "EUOPS_ON_DB_COMP_EMAIL",
    "custGroup": "ASST"
}

###

# Get email subject
POST {{host}}:9999/api/playground-api/get-email-subject
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "to": "<EMAIL>",
    "from": "<EMAIL>",
    "type": "EUOPS_ON_DB_CANCEL_EMAIL",
    "status": "TO_SEND",
    "namespace": "EUOPS",
    "custGroup": "EUNE",
    "bindings": {
        "name": "EUNE Germany",
        "telephone": "N/A",
        "email": "<EMAIL>",
        "taskId": 51433732,
        "customerRequestId": 36356910,
        "operatorId": -1,
        "customerGroupCode": "EUNE",
        "createReason": {
            "name": "Binding test EUNE"
        }
    },
    "received": "2025-02-25T09:33:52.850Z",
    "updated": "2025-02-25T09:33:52.850Z",
    "created": "2025-02-25T09:33:52.850Z",
    "_id": "67bd8e80e2497b8d9820bfc1"
}




###
