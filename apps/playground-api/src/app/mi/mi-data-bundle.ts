import {
    BenefitLimit,
    BenefitType,
    ContactType,
    CreditNote,
    Currency,
    ExchangeRate,
    Invoice,
    InvoiceLineItem,
    InvoiceStatus,
    ItemLocation,
    MiEvents,
    ProductBenefits,
    RemittanceBatch,
    RemittanceBatchStatus,
    Trip,
    TripClaimCost,
    TripContact,
    TripCostStatus,
    TripCostType,
    TripFeeCost,
    TripServiceCost,
    TripStatus
} from '@aa/data-models/common';

export interface MISendConfig {
    [MiEvents.INVOICE]: Invoice[];
    [MiEvents.REMITTANCE_BATCH]: RemittanceBatch[];
    [MiEvents.INVOICE_LINE_ITEM]: InvoiceLineItem[];
    [MiEvents.SERVICE_COSTS]: TripServiceCost[];
    [MiEvents.FEE_COSTS]: TripFeeCost[];
    [MiEvents.CLAIM_COSTS]: TripClaimCost[];
    [MiEvents.CREDIT_NOTE]: CreditNote[];
    [MiEvents.EXCHANGE_RATE]: ExchangeRate[];
    [MiEvents.PRODUCT_BENEFITS]: ProductBenefits[];
    [MiEvents.BENEFIT_LIMIT]: BenefitLimit[];
    [MiEvents.TRIP]: Trip[];
    [MiEvents.TRIP_CONTACT]: TripContact[];
    [MiEvents.ITEM_LOCATION]: ItemLocation[];
}

export const miDataBundle: MISendConfig = {
    [MiEvents.INVOICE]: [
        {
            ref: 'test1',
            currency: Currency.GBP,
            prePaid: false,
            purchaseOrderRef: '324432-A',
            supplierResourceId: 9200682,
            comments: '',
            net: {
                amount: 800,
                currency: Currency.GBP
            },
            vat: {
                amount: 100,
                currency: Currency.GBP
            },
            gross: {
                amount: 900,
                currency: Currency.GBP
            },
            issued: new Date('2024-10-28T15:28:29.541Z'),
            created: new Date('2024-10-28T15:28:29.541Z'),
            updated: new Date('2024-10-30T18:47:40.798Z'),
            status: InvoiceStatus.COMPLETED,
            code: 'b6beff16-7614-4e4c-998f-2d7bd1c8d014',
            grossNative: {
                amount: 900,
                currency: Currency.GBP
            }
        }
    ],
    [MiEvents.REMITTANCE_BATCH]: [
        {
            ref: 'REM02',
            currency: Currency.GBP,
            supplierResourceId: 9200682,
            fromDate: new Date('2024-10-30T18:47:16.666Z'),
            toDate: new Date('2024-10-30T18:47:16.666Z'),
            net: {
                currency: Currency.GBP,
                amount: 800
            },
            vat: {
                currency: Currency.GBP,
                amount: 100
            },
            gross: {
                currency: Currency.GBP,
                amount: 900
            },
            status: RemittanceBatchStatus.COMPLETED,
            code: '2129cb61-a5af-4bf1-a9c8-12cbc9490198',
            invoiceCodes: ['b6beff16-7614-4e4c-998f-2d7bd1c8d014'],
            creditNoteCodes: [],
            created: new Date('2024-10-30T18:47:36.906Z'),
            updated: new Date('2024-10-30T18:47:45.670Z'),
            grossNative: {
                currency: Currency.GBP,
                amount: 900
            }
        }
    ],
    [MiEvents.INVOICE_LINE_ITEM]: [
        {
            invoiceCode: 'b6beff16-7614-4e4c-998f-2d7bd1c8d014',
            ref: 'line1',
            net: {
                amount: 700,
                currency: Currency.GBP
            },
            gross: {
                amount: 750,
                currency: Currency.GBP
            },
            vat: {
                amount: 50,
                currency: Currency.GBP
            },
            reverseCharge: false,
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            status: 'COMPLETED',
            code: 'f9dd3b79-4bc0-4c12-bb6e-2729801b3aa5',
            created: new Date('2024-10-28T15:29:00.633Z'),
            updated: new Date('2024-10-30T18:46:55.661Z'),
            currency: Currency.GBP,
            grossNative: {
                amount: 750,
                currency: Currency.GBP
            },
            costCode: 'afdebeee-1cac-4da8-adca-e770e1749abc',
            creditNoteCode: 'c9c699af-bf3e-4cfc-a1eb-dd306f6cae8c'
        },
        {
            invoiceCode: 'b6beff16-7614-4e4c-998f-2d7bd1c8d014',
            ref: 'line2',
            net: {
                amount: 100,
                currency: Currency.GBP
            },
            gross: {
                amount: 150,
                currency: Currency.GBP
            },
            vat: {
                amount: 50,
                currency: Currency.GBP
            },
            reverseCharge: false,
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            status: 'COMPLETED',
            code: '64b901c5-760f-485b-955f-6b276d70ba4b',
            created: new Date('2024-10-28T15:29:14.249Z'),
            updated: new Date('2024-10-28T15:38:38.858Z'),
            currency: Currency.GBP,
            grossNative: {
                amount: 150,
                currency: Currency.GBP
            },
            costCode: 'd60c9c34-1c92-4bbd-9bed-0c9d1a79abe9'
        }
    ],
    [MiEvents.SERVICE_COSTS]: [
        {
            extended: true,
            reconciled: false,
            tripCode: '1d48b416-b5b6-4c53-9195-6de818f619e0',
            customerRequestId: 36332421,
            taskId: 51402503,
            name: 'Cost for Initial task for 51402503',
            benefitLimitId: 'BEN001',
            serviceRequesterRef: '51402503',
            currency: Currency.GBP,
            supplierResourceId: 9200682,
            type: TripCostType.SERVICE_COST,
            forecast: {
                amount: 100,
                currency: Currency.GBP
            },
            status: TripCostStatus.COMPLETED,
            code: 'afdebeee-1cac-4da8-adca-e770e1749abc',
            updated: new Date('2024-10-30T18:46:55.870Z'),
            created: new Date('2024-10-28T13:16:32.856Z'),
            forecastNative: {
                amount: 100,
                currency: Currency.GBP
            },
            actual: {
                amount: 150,
                currency: Currency.GBP
            },
            actualNative: {
                amount: 150,
                currency: Currency.GBP
            },
            overspend: {
                amount: 50,
                currency: Currency.GBP
            },
            overspendAuthorization: {
                authorised: true,
                authorisedBy: 9099453
            },
            overspendNative: {
                amount: 50,
                currency: Currency.GBP
            }
        },
        {
            extended: false,
            reconciled: false,
            tripCode: '5722245d-b669-4c27-bb95-1f28a11d585b',
            customerRequestId: 36332453,
            taskId: 51402555,
            name: 'Cost for Initial task for 51402555',
            benefitLimitId: 'BEN001',
            serviceRequesterRef: '51402555',
            currency: Currency.GBP,
            supplierResourceId: 9200682,
            type: TripCostType.SERVICE_COST,
            forecast: {
                amount: 100,
                currency: Currency.GBP
            },
            status: TripCostStatus.COMPLETED,
            code: 'd60c9c34-1c92-4bbd-9bed-0c9d1a79abe9',
            updated: new Date('2024-10-28T15:38:39.005Z'),
            created: new Date('2024-10-28T14:57:28.848Z'),
            forecastNative: {
                amount: 100,
                currency: Currency.GBP
            },
            actual: {
                amount: 150,
                currency: Currency.GBP
            },
            actualNative: {
                amount: 150,
                currency: Currency.GBP
            },
            overspend: {
                amount: 50,
                currency: Currency.GBP
            },
            overspendAuthorization: {
                authorised: true,
                authorisedBy: 9099453
            },
            overspendNative: {
                amount: 50,
                currency: Currency.GBP
            }
        }
    ],
    [MiEvents.FEE_COSTS]: [
        {
            customerRequestId: 36332421,
            taskId: 51402503,
            extended: false,
            reconciled: false,
            tripCode: 'bbcebecf-d081-43a9-8546-ac51b989cddb',
            name: 'Cost for Initial task for 51403113',
            benefitLimitId: 'BEN001',
            serviceRequesterRef: '51403113',
            currency: Currency.GBP,
            supplierResourceId: 9200682,
            type: TripCostType.FEE,
            forecast: {
                amount: 100,
                currency: Currency.GBP
            },
            status: TripCostStatus.READY,
            code: 'a8141a41-c9ac-45a1-9273-8b8f210cc79e',
            updated: new Date('2024-10-30T14:57:41.228Z'),
            created: new Date('2024-10-30T14:57:41.228Z'),
            forecastNative: {
                amount: 100,
                currency: Currency.GBP
            },
            actual: {
                amount: 0,
                currency: Currency.GBP
            },
            actualNative: {
                amount: 0,
                currency: Currency.GBP
            },
            invoiceLineItemCode: 'b6beff16-7614-4e4c-998f-2d7bd1c8d014'
        }
    ],
    [MiEvents.CLAIM_COSTS]: [
        {
            customerRequestId: 36332421,
            taskId: 51402503,
            extended: false,
            reconciled: false,
            tripCode: 'bbcebecf-d081-43a9-8546-ac51b989cddb',
            name: 'Cost for Initial task for 51403113',
            benefitLimitId: 'BEN001',
            serviceRequesterRef: '51403113',
            currency: Currency.GBP,
            supplierResourceId: 9200682,
            type: TripCostType.CLAIM,
            forecast: {
                amount: 100,
                currency: Currency.GBP
            },
            status: TripCostStatus.READY,
            code: 'a8141a41-c9ac-45a1-9273-8b8f210cc79e',
            updated: new Date('2024-10-30T14:57:41.228Z'),
            created: new Date('2024-10-30T14:57:41.228Z'),
            forecastNative: {
                amount: 100,
                currency: Currency.GBP
            },
            actual: {
                amount: 0,
                currency: Currency.GBP
            },
            actualNative: {
                amount: 0,
                currency: Currency.GBP
            }
        }
    ],
    [MiEvents.CREDIT_NOTE]: [
        {
            invoiceCode: 'b6beff16-7614-4e4c-998f-2d7bd1c8d014',
            invoiceLineItemCode: 'f9dd3b79-4bc0-4c12-bb6e-2729801b3aa5',
            ref: 'CN-test1-line1',
            purchaseOrderRef: '324432-A',
            reverseCharge: false,
            supplierResourceId: 9200682,
            net: {
                amount: 500,
                currency: Currency.GBP
            },
            vat: {
                amount: 100,
                currency: Currency.GBP
            },
            gross: {
                amount: 600,
                currency: Currency.GBP
            },
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            status: 'COMPLETED',
            code: 'c9c699af-bf3e-4cfc-a1eb-dd306f6cae8c',
            issued: new Date('2024-10-30T18:27:36.952Z'),
            created: new Date('2024-10-30T18:27:36.952Z'),
            updated: new Date('2024-10-30T18:46:43.029Z'),
            currency: Currency.GBP,
            grossNative: {
                amount: 600,
                currency: Currency.GBP
            }
        }
    ],
    [MiEvents.EXCHANGE_RATE]: [
        {
            created: new Date('2023-10-01T00:00:00.000Z'),
            updated: new Date('2023-10-01T00:00:00.000Z'),
            date: new Date('2023-10-01T00:00:00.000Z'),
            baseCurrency: Currency.EURO,
            currency: Currency.GBP,
            rate: 0.88
        },
        {
            created: new Date('2023-10-01T00:00:00.000Z'),
            updated: new Date('2023-10-01T00:00:00.000Z'),
            date: new Date('2023-10-01T00:00:00.000Z'),
            baseCurrency: Currency.GBP,
            currency: Currency.USD,
            rate: 1.33
        },
        {
            created: new Date('2023-10-01T00:00:00.000Z'),
            updated: new Date('2023-10-01T00:00:00.000Z'),
            date: new Date('2023-10-01T00:00:00.000Z'),
            baseCurrency: Currency.USD,
            currency: Currency.EURO,
            rate: 0.85
        },
        {
            created: new Date('2023-10-01T00:00:00.000Z'),
            updated: new Date('2023-10-01T00:00:00.000Z'),
            date: new Date('2023-10-01T00:00:00.000Z'),
            baseCurrency: Currency.USD,
            currency: Currency.GBP,
            rate: 0.75
        },
        {
            created: new Date('2023-10-01T00:00:00.000Z'),
            updated: new Date('2023-10-01T00:00:00.000Z'),
            date: new Date('2023-10-01T00:00:00.000Z'),
            baseCurrency: Currency.EURO,
            currency: Currency.USD,
            rate: 1.18
        },
        {
            created: new Date('2023-10-01T00:00:00.000Z'),
            updated: new Date('2023-10-01T00:00:00.000Z'),
            date: new Date('2023-10-01T00:00:00.000Z'),
            baseCurrency: Currency.GBP,
            currency: Currency.EURO,
            rate: 1.14
        }
    ],
    [MiEvents.PRODUCT_BENEFITS]: [
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ASST',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP786480'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ASST',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP786481'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ASST',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP786482'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ASST',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP790783'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ASST',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP790784'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ASST',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP790785'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ASST',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP790786'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'TCB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788626'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'TCB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788627'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'TCB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788628'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ADAC',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788629'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ADAC',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788630'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ADAC',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788631'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AAIR',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788633'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AAIR',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788634'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AAIR',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788635'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AAIR',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788636'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ANWB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788638'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ANWB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788639'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ANWB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788640'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ANWB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP790790'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ANWB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP790791'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ANWB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP790792'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ANWB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP790793'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'FOO',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788641'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'FOO',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788642'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'FOO',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788643'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AAP',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788644'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AAP',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788645'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AAP',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788646'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ACA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788647'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ACA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788648'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ACA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788649'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'BIHA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788700'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'BIHA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788701'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'BIHA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788702'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'UAB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788668'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'UAB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788669'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'UAB',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788670'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'HAK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788696'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'HAK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788697'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'HAK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788698'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ODYK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788657'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ODYK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788658'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ODYK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788659'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'UAMK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788693'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'UAMK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788694'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'UAMK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788695'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'FDMV',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788654'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'FDMV',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788655'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'FDMV',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788656'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RGEO',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788665'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RGEO',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788666'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RGEO',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788667'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ALFI',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788721'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ALFI',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788722'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ALFI',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788723'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AEF',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788690'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AEF',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788691'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AEH',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788661'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AEH',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788662'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AEH',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788663'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'MAK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788650'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'MAK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788651'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'MAK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788652'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'KROK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788719'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'KROK',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788720'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AGS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788716'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AGS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788717'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AGS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788718'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'KTA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788686'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'KTA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788687'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'KTA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788688'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'SIAA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788743'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'SIAA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788744'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'SIAA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788745'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'EAS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788683'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'EAS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788684'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'EAS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788685'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ACLX',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788740'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ACLX',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788741'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ACLX',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788742'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMSM',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788786'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMSM',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788787'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMSM',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788788'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RMF',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788680'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RMF',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788681'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RMF',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788682'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMSC',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788713'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMSC',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788714'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMSC',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788715'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'SOSV',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788676'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'SOSV',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788677'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'SOSV',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788678'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AEP',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788792'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AEP',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788793'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AEP',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788794'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ACP',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788736'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ACP',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788737'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ACP',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788738'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ATFO',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788709'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ATFO',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788710'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ATFO',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788711'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RAMC',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788795'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RAMC',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788796'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RAMC',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788797'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMSS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788706'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMSS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788707'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMSS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788708'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ASA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788733'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ASA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788734'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ASA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788735'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMZS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788799'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMZS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788800'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AMZS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788801'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'SOSI',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788704'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'SOSI',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788705'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'TCS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788730'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'TCS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788731'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'TCS',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788732'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'MARM',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788727'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'MARM',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788728'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'GARA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788817'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'GARA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788818'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'GARA',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788819'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RACE',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788724'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RACE',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788725'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'RACE',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788726'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'AEF',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP788689'
        },
        {
            created: new Date('2024-10-23T10:36:28.012Z'),
            updated: new Date('2024-10-23T10:36:28.012Z'),
            customerGroup: 'ADAC',
            code: 'prodCode',
            limits: ['BEN001', 'BEN002'],
            contractKey: 'BCASP389387'
        }
    ],
    [MiEvents.BENEFIT_LIMIT]: [
        {
            code: 'BEN001',
            created: new Date('2021-05-10T09:15:00Z'),
            updated: new Date('2021-05-15T11:30:00Z'),
            type: BenefitType.RSS,
            name: 'RSS',
            amount: 1000,
            currency: Currency.EURO
        },
        {
            code: 'BEN002',
            created: new Date('2021-05-12T14:45:00Z'),
            updated: new Date('2021-05-18T16:20:00Z'),
            type: BenefitType.RECOVERY,
            name: 'RECOVERY',
            amount: 2000,
            currency: Currency.EURO
        },
        {
            code: 'BEN003',
            type: BenefitType.PUBLIC_TRANSPORT,
            name: 'edit test edit 22 07',
            amount: 5,
            currency: Currency.EURO,
            created: new Date('2024-07-22T08:40:41.753Z'),
            updated: new Date('2024-07-22T08:44:19.419Z')
        }
    ],
    [MiEvents.TRIP]: [
        {
            customerGroup: 'ANWB',
            vrn: 'RE69CSV',
            externalRef: 'Test UAT5 301024',
            productCode: 'BCASP790792 - Netherlands ARC Foreign fleet',
            contractKey: 'BCASP790792',
            customerKey: 'BCASP790792',
            start: '2024-10-30',
            end: '2024-10-31',
            customerRequestIds: [36332869],
            partyContactCode: 'e3fab619-26b6-412a-b37c-4b3d05d1a97f',
            code: 'bbcebecf-d081-43a9-8546-ac51b989cddb',
            created: new Date('2024-10-30T14:54:07.394Z'),
            updated: new Date('2024-10-30T14:54:07.394Z'),
            status: TripStatus.OPEN,
            benefitLimits: ['BEN001', 'BEN002']
        }
    ],
    [MiEvents.TRIP_CONTACT]: [
        {
            tripCode: 'bbcebecf-d081-43a9-8546-ac51b989cddb',
            type: ContactType.BUSINESS,
            name: '123 Solutions',
            telephone: '+440543540435123',
            email: '<EMAIL>',
            location: {
                latitude: -1,
                longitude: 1
            },
            note: 'test',
            address: {
                addressLines: ['44 Badgers Bolt', 'Colden Common', 'Winchestr'],
                postcode: 'SO21 2GB',
                houseNoName: '44'
            },
            code: '427183c8-3e0b-4918-9ec4-05a5038a4bdb',
            created: new Date('2024-10-28T12:56:25.549Z'),
            updated: new Date('2024-10-28T12:56:25.549Z')
        },
        {
            tripCode: 'bbcebecf-d081-43a9-8546-ac51b989cddb',
            type: ContactType.PERSON,
            name: 'John Doe',
            telephone: '+4407716759347',
            email: '<EMAIL>',
            location: {
                latitude: 2,
                longitude: 1
            },
            note: 'Some note',
            address: {
                addressLines: ['23 Badgers Bolt', 'Colden Common', 'Winchestr'],
                postcode: 'SO21 1GB',
                houseNoName: '23'
            },
            code: 'e3fab619-26b6-412a-b37c-4b3d05d1a97f',
            created: new Date('2024-10-28T12:58:26.140Z'),
            updated: new Date('2024-10-28T12:58:26.140Z')
        }
    ],
    [MiEvents.ITEM_LOCATION]: [
        {
            created: new Date('2024-10-30T10:00:00.000Z'),
            updated: new Date('2024-10-30T10:00:00.000Z'),
            updatedBy: 1,
            createdBy: 1,
            tripCode: 'tripCode',
            name: 'Location 1',
            address: {
                addressLines: ['123 Main St', 'Suite 100'],
                postcode: '12345',
                houseNoName: '123'
            },
            telephone: '+1234567890',
            coordinates: {
                latitude: 52.3676,
                longitude: 4.9041
            },
            note: 'First location note',
            isSecure: true
        },
        {
            created: new Date('2024-10-30T11:00:00.000Z'),
            updated: new Date('2024-10-30T11:00:00.000Z'),
            updatedBy: 2,
            createdBy: 2,
            tripCode: 'tripCode',
            name: 'Location 2',
            address: {
                addressLines: ['456 Another St', 'Apt 200'],
                postcode: '67890',
                houseNoName: '456'
            },
            telephone: '+0987654321',
            coordinates: {
                latitude: 48.8566,
                longitude: 2.3522
            },
            note: 'Second location note',
            isSecure: false
        }
    ]
};
