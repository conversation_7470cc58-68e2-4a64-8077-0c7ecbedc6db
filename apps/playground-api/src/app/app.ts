import { AzureEmailClient, AzurePublicFolderName } from '@aa/azure-email-client';
import { AzureGraphClient } from '@aa/azure-graph-client';
import {
    DownloadedAttachment,
    EdocsCreateBody,
    EdocsUpdateBody,
    Email,
    EmailFromTemplate,
    EmailStatus,
    EventType,
    MimeType,
    Namespace,
    Policy,
    SendableEmail,
    Trip,
    TripCost,
    WithDataId
} from '@aa/data-models/common';
import { DataStoreProviderType } from '@aa/data-store';
import { Diff } from '@aa/diff';
import { DocumentClient } from '@aa/document-client';
import { EmailSubscriptionManager } from '@aa/email-subscription-manager';
import { EventCode, Exception } from '@aa/exception';
import { HttpClient, ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { NoteClient } from '@aa/note-client';
import { EventHubSender, EventHubSenderConfig, ServiceBusSender, ServiceBusSenderConfig } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment, RequiredBy } from '@aa/utils';
import { writeFileAsync } from 'applicationinsights/out/Library/FileSystemHelper';
import { Request, Response } from 'express';
import { ObjectId, WithId } from 'mongodb';
import { miDataBundle } from './mi/mi-data-bundle';
import { EmailTemplateClient } from '@aa/email-template-client';
import { BillingClient } from 'billing-client';

export class App extends Microservice {
    public name = 'Playground';
    public application = BackendApplication.PLAYGROUND;
    protected miSender: EventHubSender<any, any>;
    protected azureEmailClient: AzureEmailClient;
    protected commsStreamSender: EventHubSender<EventType.OUTBOUND_EMAIL | EventType.INBOUND_EMAIL, WithDataId<SendableEmail>>;
    protected emailTemplateClient: EmailTemplateClient;
    protected billingClient: BillingClient;

    constructor(environment: BackendEnvironment) {
        super({ environment, appName: 'Playground', openApi: true });
        this.azureEmailClient = new AzureEmailClient({ logger: this.logger, authClient: this.authClient });
        this.sendTestEmail = this.sendTestEmail.bind(this);

        const configBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };

        this.emailTemplateClient = new EmailTemplateClient({
            logger: this.logger,
            system: this.system,
            context: this.context
        });

        this.billingClient = new BillingClient({ httpClient: this.httpClient, connector: this.connector });

        this.miSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });

        this.commsStreamSender = new EventHubSender({
            ...configBase,
            eventHubName: 'comms-stream'
        });

        this.server.get('/mi-dump', this.sendMIdump, { protect: false });
        this.server.get('/test', this.test);

        // Document client tests
        this.server.get('/document-client/documents/by-task-id/:id', this.documentClientDocumentsByTaskId);
        this.server.get('/document-client/document/:id', this.documentClientDocumentById);
        this.server.post('/document-client/document', this.documentClientCreateDocument);
        this.server.get('/document-client/document/:id/delete', this.documentClientDeleteDocument);
        this.server.post('/document-client/document/:id', this.documentClientUpdateDocument);
        this.server.post('/document-client/upload', this.documentClientUpload);
        this.server.post('/document-client/download', this.documentClientDownload);
        this.server.get('/attachment-renderer-steam/push-email-to-queue', this.attachmentRendererStreamPushEmailToQueue);

        this.server.get('/graph', this.testGraphCLient);
        this.server.get('/subsribe', this.subscribe);

        //this.domongo();Û

        this.server.get('/diff', this.testDiff);

        this.server.post('/send-test-email', this.sendTestEmail);
        this.server.post('/email-template', this.getBodyTemplate);
        this.server.post('/get-email-subject', this.getSubject);

        this.server.post('/ensure-cost-updated', this.ensureCostUpdated);
    }

    protected ensureCostUpdated = async (req: Request<any, any, any>, res: Response<any>) => {
        const { trip, taskId } = req.body;

        this.logger.info({ message: 'Ensuring cost is updated for trip', data: { taskId, tripCode: trip.code } });

        try {
            const costs = await this.getCostsForTask(trip, taskId);

            if (!costs.length) {
                // if cost missing lets bail out
                return;
            }

            const { estimates } = await this.billingClient.estimateCost(taskId);

            if (!estimates.length) {
                // if no results that's suspicious
                this.logger.warn({
                    message: 'No estimations generated for the cost update, bailing out',
                    data: { trip, taskId }
                });
                return;
            }

            for (const { estimate } of estimates) {
                const cost = costs.find((entry) => {
                    return entry.benefitLimitId === estimate.benefitLimitId;
                });

                if (cost) {
                    const { _id, ...rest } = cost;
                    await this.billingClient.updateCost(cost.code, {
                        ...rest,
                        forecast: estimate.forecast,
                        currency: estimate.currency
                    });
                }
            }

            this.logger.info({
                message: 'Ensuring cost is updated to trip finished',
                data: { taskId, tripCode: trip.code }
            });
        } catch (error) {
            this.logger.error({
                error,
                message: 'Failure while canceling cost',
                data: { taskId }
            });
        }
    };

    protected getCostsForTask = async (trip: Trip, taskId: number): Promise<WithId<TripCost>[]> => {
        try {
            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<TripCost>('entities', 'tripCosts');
            return await collection.find({ tripCode: trip.code, taskId }).toArray();
        } catch (error) {
            this.logger.error({
                error,
                message: `Failed getting cost for trip`,
                data: { taskId, tripCode: trip.code }
            });
            throw new Error(`Failed getting cost for trip: ${(error as Error).message}`);
        }
    };

    protected getSubject = async (req: Request<any, any, any>, res: Response<any>): Promise<any> => {
        const { custGroup, bindings, type } = req.body;
        const subjectTemplate = await this.emailTemplateClient.getSubjectTemplate({ type, custGroup });

        if (!subjectTemplate) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                data: { custGroup, bindings, type },
                message: `No subject template found for custGroup ${custGroup} and type ${type}`
            });
        }

        const subject = await this.emailTemplateClient.bind(subjectTemplate, bindings);

        if (!subject) {
            this.logger.error('Email subject does not exist and no custGroup or bindings found');
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                data: { custGroup, bindings, type },
                message: `Email subject does not exist and no template found`
            });
        }

        return getResponse(res, ServerResponseCode.OK, subject);
    };

    protected getBodyTemplate = async (req: Request<any, any, any>, res: Response<void>): Promise<any> => {
        try {
            const { type, custGroup = 'ANY' } = req.body;
            const currentDate = new Date().toISOString();
            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection('system-config', 'email-templates');
            const mongoQuery = {
                type,
                custGroups: custGroup === 'ANY' ? { $exists: true } : custGroup,
                effectiveFrom: { $lte: currentDate },
                effectiveTo: { $gte: currentDate }
            };
            const result = await collection.findOne(mongoQuery);
            return getResponse(res, ServerResponseCode.OK, result || undefined);
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to get email template'
            });
        }
    };

    protected sendMIdump = async (req: Request<unknown, unknown, unknown>, res: Response<void>): Promise<void> => {
        try {
            this.logger.log(`Sending MI data`);

            for (const [event, data] of Object.entries(miDataBundle)) {
                for (const entry of data) {
                    await this.miSender.send(event, entry);
                }
            }

            return getResponse(res, ServerResponseCode.OK);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failed sending MI data'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected testDiff = async (req: Request, res: Response) => {
        const current = {
            id: '12345',
            customerRequestId: 'CR67890',
            taskType: {
                code: 'repair',
                description: 'Vehicle Repair'
            },
            operatorId: 98765,
            status: 'newStatus',
            location: {
                latitude: 51.5074,
                longitude: -0.1278
            },
            isCompleted: false,
            fault: {
                id: 'fault123'
            },
            createReason: {
                id: 'reason456'
            },
            schedule: {
                create: '2023-04-01T10:00:00Z'
            },
            vehicle: {
                registration: 'XYZ 1234'
            },
            recovery: {
                destination: 'Service Center B'
            },
            vrn: 'XYZ 1234'
        };

        const previous = {};

        const _previous = {
            id: '12345',
            customerRequestId: 'CR67890',
            taskType: {
                code: 'repair',
                description: 'Vehicle Repair'
            },
            operatorId: 98765,
            status: 'oldStatus',
            location: {
                latitude: 51.5074,
                longitude: -0.1278
            },
            isCompleted: false,
            fault: {
                id: 'fault123'
            },
            createReason: {
                id: 'reason456'
            },
            schedule: {
                create: '2023-04-01T10:00:00Z'
            },
            vehicle: {
                registration: 'XYZ 1234'
            },
            recovery: {
                destination: 'Service Center A'
            },
            vrn: 'XYZ 1234'
        };

        const diffInstance = new Diff({ previous, current });
        const result = diffInstance.diff();
        return getResponse(res, ServerResponseCode.OK, result);
    };

    protected domongo = async () => {
        const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);

        const collectionTrip = await provider.collection<Trip>('entities', 'trips');

        const tripsWithDetails = await collectionTrip
            .aggregate([
                {
                    $match: {
                        customerRequestIds: 36308304
                    }
                },
                {
                    $lookup: {
                        from: 'contacts',
                        localField: 'code',
                        foreignField: 'tripCode',
                        as: 'contacts'
                    }
                }
            ])
            .toArray();

        console.log(tripsWithDetails);
    };

    protected testGraphCLient = async (req: Request, res: Response) => {
        console.log('##################################################');
        console.log('testGraphCLient');
        console.log('##################################################');

        //test 1 - get mail
        const messages = await this.azureEmailClient.emails({ userId: this.environment.emails.euopsOutboundEmail, folderId: AzurePublicFolderName.INBOX, limit: 5 });

        //test 2 - send mail
        // await client.sendMail({
        //     userId: accounts.int,
        //     emailAddress: '<EMAIL>',
        //     subject: 'Test',
        //     content: 'Test',
        // });

        return getResponse(res, ServerResponseCode.OK, messages);
        console.log(messages);
    };

    protected subscribe = async () => {
        const l = this.logger;
        const graphClient = new AzureGraphClient({ logger: this.logger, authClient: this.authClient });

        const esm = new EmailSubscriptionManager({
            graphClient,
            logger: l,
            subscriptions: [
                {
                    id: '4cdb31b5-09e0-42d0-9d1f-df7624c38790',
                    notificationUrl: 'EventHub:https://azne-kv-u-road.vault.azure.net/secrets/inboundEmailPolicy?tenantId=theaa.onmicrosoft.com',
                    inbox: this.environment.emails.euopsOutboundEmail
                }
            ]
        });

        esm.createSubscription({
            //notificationUrl:
            // 'EventHub:https://azne-kv-u-road.vault.azure.net/secrets/inboundEmailPolicy/231bbd03e9574b8682fcc9521250d301',
            // notificationUrl:
            // 'EventHub:https://azne-kv-u-road.vault.azure.net/secrets/inboundEmailPolicy?tenantId=theaa.onmicrosoft.com',
            // notificationUrl:
            // 'EventHub:https://azne-kv-u-road.vault.azure.net/secrets/inboundEmailPolicy?tenantId=52da6ceb-c432-45d8-9cee-97902996ced9',
            // notificationUrl:
            // 'sb://azne-evh-roanon-u-aahelp2.servicebus.windows.net/;SharedAccessKeyName=inboundEmailPolicy;SharedAccessKey=xG5wzJgS3P3MTiOAHXhc7IKMx5YRHU8cO+AEhDfX2Kk=',
            // notificationUrl:
            // 'Endpoint=sb://evhuataahelp2.servicebus.windows.net/;SharedAccessKeyName=inboundEmailPolicy;SharedAccessKey=xG5wzJgS3P3MTiOAHXhc7IKMx5YRHU8cO+AEhDfX2Kk=',
            // notificationUrl:
            // 'Endpoint=sb://azne-evh-roanon-u-aahelp2.servicebus.windows.net/;SharedAccessKeyName=inboundEmailPolicyTaskStream;SharedAccessKey=mzUFYU5+L+ds8DqIm16yJoN/6S5om0NQo+AEhJ41sIc=;EntityPath=task-stream',
            notificationUrl: 'EventHub:https://azne-evh-roanon-u-aahelp2.servicebus.windows.net/eventhubname/task-stream?tenantId=theaa.onmicrosoft.com',
            resource: 'users/4cdb31b5-09e0-42d0-9d1f-df7624c38790/messages',
            expirationDateTime: '2024-08-15T10:00:00Z',
            clientState: '123456'
        });
    };

    protected testNoteApi = async () => {
        console.log('##################################################');
        console.log('testNoteApi');
        console.log('##################################################');

        const client = new NoteClient({
            httpClient: new HttpClient({ authClient: this.authClient }),
            connector: this.connector
        });
        const result = await client.getNote('66632f325da132e98b51f350');
        console.log(result);
    };

    protected attachmentRendererStreamPushEmailToQueue = async (req: Request, res: Response) => {
        try {
            const sbqBaseConfigSender: Omit<ServiceBusSenderConfig, 'queueName'> = {
                logger: this.logger,
                context: this.context,
                dataStore: this.dataStore,
                system: this.system,
                connectionString: this.environment.queue.legacySBQConnectionString
            };
            const taskCompletionSender = new ServiceBusSender({
                ...sbqBaseConfigSender,
                queueName: 'attachment-renderer-queue'
            });
            const email: WithId<Email | RequiredBy<EmailFromTemplate, 'body'>> = {
                _id: new ObjectId('66586a982500110089a8516a'),
                body: 'Hey ho',
                from: '<EMAIL>',
                to: '<EMAIL>',
                namespace: Namespace.EUOPS,
                status: EmailStatus.SENDING_ATTACHMENT_RENDERING,
                subject: 'Donkey',
                type: 'CHECKOUT_EMAIL',
                received: new Date(),
                updated: new Date(),
                created: new Date(),
                attachments: [
                    {
                        docId: 'test1',
                        container: 'key-assist',
                        name: 'test.txt',
                        custReqId: 7,
                        taskId: 7,
                        docType: 200,
                        mimeType: MimeType.TXT,
                        data: '<h1>Hello, world!</h1><p>This is some <strong>HTML content</strong> in a PDF.</p>'
                    }
                ]
            };
            await taskCompletionSender.send('OUTBOUND_EMAIL', email);
            return getResponse(res, ServerResponseCode.OK);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected test = async (req: Request, res: Response) => {
        try {
            return getResponse(res, ServerResponseCode.OK, {});
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected documentClientDocumentsByTaskId = async (req: Request<{ id: number }>, res: Response) => {
        try {
            const { id } = req.params;
            this.logger.info(`Getting documents for task with id ${id}`);
            const client = new DocumentClient({
                httpClient: this.httpClient,
                connector: this.connector
            });

            const result = await client.getDocumentsByTaskId(id);

            if (!result?.length) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
    protected documentClientDocumentById = async (req: Request<{ id: string }>, res: Response) => {
        try {
            const { id } = req.params;
            this.logger.info(`Getting documents for task with id ${id}`);
            const client = new DocumentClient({
                httpClient: this.httpClient,
                connector: this.connector
            });

            const result = await client.getDocumentByDocId(id);

            if (!result) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
    protected documentClientCreateDocument = async (req: Request<unknown, unknown, EdocsCreateBody>, res: Response) => {
        try {
            const body = req.body;
            this.logger.info(`Creating document`);
            const client = new DocumentClient({
                httpClient: this.httpClient,
                connector: this.connector
            });

            const result = await client.createDocument(body);
            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
    protected documentClientUpload = async (
        req: Request<
            unknown,
            unknown,
            EdocsCreateBody & {
                data: string;
                overwrite?: boolean;
            }
        >,
        res: Response
    ) => {
        try {
            this.logger.info(`Uploading document`);
            const client = new DocumentClient({
                httpClient: this.httpClient,
                connector: this.connector
            });

            const { data, overwrite = false, ...definition } = req.body;
            const result = await client.upload(definition, data, overwrite);
            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
    protected documentClientDownload = async (
        req: Request<
            unknown,
            unknown,
            {
                docName: string;
                container?: string;
            }
        >,
        res: Response
    ) => {
        try {
            this.logger.info(`Downloading document`);
            const client = new DocumentClient({
                httpClient: this.httpClient,
                connector: this.connector
            });

            const { docName, container } = req.body;
            const result = await client.download(docName, container);
            if (!result) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
            console.log(typeof result.data);
            await writeFileAsync(result.docName, result.data as unknown as string);

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
    protected documentClientDeleteDocument = async (req: Request<{ id: string }>, res: Response) => {
        try {
            const { id } = req.params;
            this.logger.info(`deleting document with id ${id}`);
            const client = new DocumentClient({
                httpClient: this.httpClient,
                connector: this.connector
            });

            const result = await client.deleteDocumentByDocId(id);
            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
    protected documentClientUpdateDocument = async (
        req: Request<
            {
                id: string;
            },
            unknown,
            EdocsUpdateBody
        >,
        res: Response
    ) => {
        try {
            const { id } = req.params;
            const body = req.body;
            this.logger.info(`Updating document with ${id}`);
            const client = new DocumentClient({
                httpClient: this.httpClient,
                connector: this.connector
            });

            const result = await client.updateDocumentByDocId(body);
            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    public async sendTestEmail(req: Request, res: Response): Promise<void> {
        try {
            const { type, status, namespace, from, to, subject, body, attachments } = req.body;

            if (!to || !from || !subject || !body) {
                res.status(400).send({ error: 'Missing required fields.' });
                return;
            }

            const email = {
                type,
                status,
                namespace,
                from,
                to,
                subject,
                body,
                received: new Date(),
                updated: new Date(),
                created: new Date()
            } satisfies SendableEmail;

            // Parse attachments
            const parsedAttachments: DownloadedAttachment[] = (attachments || []).map((attachment: any) => ({
                name: attachment.name,
                mimeType: attachment.mimeType,
                data: attachment.data // Assuming attachment data is already in base64 format
            }));

            // Call the sendMail method from AzureEmailClient
            const inboxId = this.environment.emails.euopsOutboundEmail;
            await this.azureEmailClient.send({ inboxId, email, attachments: parsedAttachments });

            // Send success response
            res.status(200).send({ message: 'Email sent successfully.' });
        } catch (error) {
            console.error('Error sending email:', error);
            res.status(500).send({ error: 'Failed to send email.' });
        }
    }
}
