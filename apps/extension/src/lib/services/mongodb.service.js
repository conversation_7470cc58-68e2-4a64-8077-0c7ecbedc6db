const { MongoClient } = require('mongodb');
const svc = {};
svc.connectDB = connectDB.bind(svc);
svc.getCollection = getCollection.bind(svc);
svc.getAllExtensions = getAllExtensions.bind(svc);
svc.getAllApproved = getAllApprovedExtensions.bind(svc);
svc.getAllRejected = getAllRejectedExtensions.bind(svc);
svc.getAllExpected = getAllExpectedExtensions.bind(svc);
svc.getAllPending = getAllPendingExtensions.bind(svc);
svc.updateExtension = updateExtension.bind(svc);
svc.deleteExtension = deleteExtension.bind(svc);
module.exports = svc;
let client = null;
let logger = require('winston');

async function connectDB({ mongoUrl, dbName }) {
    try {
        mongoUrl = mongoUrl || process.env.mongodbUrl || 'mongodb+srv://aah2intAtlas:<EMAIL>';
        dbName = dbName || 'cache';

        client =
            client ||
            (await MongoClient.connect(mongoUrl, {
                useNewUrlParser: true
            }));
        return client.db(dbName);
    } catch (err) {
        logger.error(`Mongodb client connection failed with error : ${err}`);
    }
}

async function getCollection({ mongoUrl, dbName, collectionName }) {
    const db = await connectDB({ mongoUrl, dbName });
    return db.collection(collectionName);
}

async function getAllExtensions({ collectionName }) {
    try {
        const collection = await getCollection({ collectionName });
        return collection.find({}).toArray();
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}

function getAllApprovedExtensions() {
    return getAllExtensions({ collectionName: 'approved-extension-summary' });
}
function getAllRejectedExtensions() {
    return getAllExtensions({ collectionName: 'rejected-extension-summary' });
}
function getAllExpectedExtensions() {
    return getAllExtensions({ collectionName: 'expected-extension-summary' });
}
function getAllPendingExtensions() {
    return getAllExtensions({ collectionName: 'extension-summary' });
}

async function updateExtension(collectionName, extId, extension) {
    try {
        const collection = await getCollection({ collectionName });
        const filter = { id: extId };
        const options = { upsert: true };
        const updateDoc = { $set: extension };
        return collection.updateOne(filter, updateDoc, options);
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}
async function deleteExtension(collectionName, extId) {
    try {
        const collection = await getCollection({ collectionName });
        const filter = { id: extId };
        return collection.deleteOne(filter);
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}
