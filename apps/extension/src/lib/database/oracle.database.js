const aaOracleUtility = require('@aa/oracle-utilities'),
    oracledb = require('oracledb');
oracledb.poolIncrement = 1;
const appName = 'extension';
module.exports = {
    init: () => {
        return aaOracleUtility.init({
            connectStrings: process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : ['vulexa-scan:1521/AAHLPT_P'], // Oracle connection string
            user: process.env.cshUser || 'vanda',
            password: process.env.cshPassword || 'va.77.av',
            appName
        });
    },
    connect: () => {
        console.log('connecting');
        return aaOracleUtility.connect();
    },
    disconnect: (db) => {
        console.log('releasing');
        if (db) {
            aaOracleUtility.release(db);
        }
    }
};
