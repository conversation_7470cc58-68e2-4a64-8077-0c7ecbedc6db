import { AuditClient } from '@aa/audit-client';
import { EuopsCustomerGroup, EventType, SendableEmail, WithDataId } from '@aa/data-models/common';
import { DataStoreProviderType } from '@aa/data-store';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubReceiverConfig, EventHubSender } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';

const appName = 'eurohelp-api';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.EUROHELP_API;
    protected commsStreamSender: EventHubSender<EventType.OUTBOUND_EMAIL, WithDataId<SendableEmail>>;
    protected auditClient: AuditClient;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.MONGODB, DataStoreProviderType.REDIS]
        });

        this.auditClient = new AuditClient({
            application: BackendApplication.EUROHELP_API,
            connector: this.connector,
            operatorId: -1
        });

        const ehBaseConfig: Omit<EventHubReceiverConfig, 'eventHubName' | 'consumerGroup' | 'checkpoint'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            maxBatchSize: 1, // we want to process even 1
            maxDelayPerBatch: 2 // 2s
        };

        this.commsStreamSender = new EventHubSender({
            ...ehBaseConfig,
            eventHubName: 'comms-stream'
        });

        //this is for test purposes only
        this.server.get('/customer-groups', this.getCustomerGroups);
    }

    protected getCustomerGroups = async (req: Request<unknown, unknown, unknown>, res: Response<EuopsCustomerGroup[]>): Promise<void> => {
        try {
            this.logger.log(`Getting customer groups`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<EuopsCustomerGroup>('system-config', 'euopsCustomerGroup');
            const result = await collection.find().toArray();

            if (result) {
                this.logger.log(`Success: Customer groups found`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Customer groups not found`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting customer groups`,
                data: { message: error }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
}

export enum ImageUpdateStatus {
    PENDING = 'PENDING',
    ADDED = 'ADDED',
    DELETED = 'DELETED'
}

interface ImageUpdateEventBody {
    taskId: number;
    status: ImageUpdateStatus;
    imageGuid: string;
}
