'use strict';

let fs = require('fs'),
    logger = require('winston'),
    express = require('express'),
    hireEntitlementServiceExpress = require('../lib'),
    nconf = require('nconf'),
    bodyParser = require('body-parser'),
    config = require('./config'),
    morgan = require('morgan'),
    aaSecurityChecker = require('@aa/security-checker').tokenHandler,
    aaLogUtilities = require('@aa/utilities/aa-utilities').logging,
    aaServiceCheck = require('@aa/utilities/aa-utilities').serviceCheck,
    aahelp2Svc = require('../lib/services/aahelp2.service'),
    hireFaultCodesSvc = require('../lib/services/hire-fault-codes.service'),
    server;

// Setup server
server = express();

// create a write stream (in append mode)
var accessLogStream = fs.createWriteStream('./access.log', {
    flags: 'a'
});

// setup configuration
nconf.argv().env();
nconf.defaults(config);

server.use(bodyParser.json()); // for parsing application/json
server.use(
    morgan(aaLogUtilities.morganSettings(), {
        stream: accessLogStream
    })
);

logger.handleExceptions(new logger.transports.File(aaLogUtilities.logFileSettings('exceptions.log')));

logger.add(logger.transports.File, aaLogUtilities.logFileSettings('hire-entitlement-service.log'));

logger.info('wiring api on :', config);

// basic health check middleware ..
server.use(nconf.get('apiEndPoint'), aaServiceCheck.health);
// now do the do the security check
server.use(nconf.get('apiEndPoint'), aaSecurityChecker.verifyCredentials);

aahelp2Svc.init({
    loadbalancer: nconf.get('loadbalancer'),
    taskApiEndPoint: nconf.get('taskApiEndPoint'),
    serviceEntitlementApiEndPoint: nconf.get('serviceEntitlementApiEndPoint')
});

const cshConfig = {
    user: nconf.get('cshUser'),
    password: nconf.get('cshPassword'),
    connectStrings: nconf.get('cshConnectStrings').split(',')[0],
    appName: 'hire-entitlement'
};
hireFaultCodesSvc
    .init(cshConfig)
    .then(() => {
        logger.info(`Fault-code mapping table :: Connection successful`);
    })
    .catch((err) => {
        logger.error(`Error while connecting to fault code mapping table: ${err}`);
    });

server.use(nconf.get('apiEndPoint'), hireEntitlementServiceExpress.init());
server.listen(parseInt(nconf.get('port')), nconf.get('ip'), function () {
    logger.info('server is listening', nconf.get('ip'), '@', nconf.get('port'));
});

module.exports = server;
