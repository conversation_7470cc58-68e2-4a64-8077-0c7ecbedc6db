import { ErrorResponse } from '@aa/connector';
import { Capabilitity, CapabilityLabels, POICategoryId, PrimaryCapability } from '@aa/data-models/common';
import { DataStore, DataStoreProviderType, PoiStore, UnityStore } from '@aa/data-store';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Interval } from '@aa/interval';
import { Microservice } from '@aa/microservice';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment, Utils } from '@aa/utils';
import { Request, Response } from 'express';

const appName = 'poi';

interface CapabilityMap {
    id: Capabilitity;
    name: string;
    description: string;
    isPrimary: boolean;
}

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.POI;
    protected interval = new Interval();
    protected legacyPOIDatastore: DataStore;
    protected poiMongodbDatabase: string;
    protected unityStore: UnityStore;
    protected poiStore: PoiStore;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.REDIS, DataStoreProviderType.MONGODB, DataStoreProviderType.PRESTIGE, DataStoreProviderType.ORACLE]
        });
        const {
            mongodb: { poiMongodbDatabase }
        } = environment;

        this.poiMongodbDatabase = poiMongodbDatabase;

        // For POIs we are using old mongodb
        this.legacyPOIDatastore = new DataStore({
            logger: this.logger,
            providers: [
                {
                    type: DataStoreProviderType.MONGODB,
                    config: {
                        logger: this.logger,
                        system: this.system,
                        connectionString: environment.mongodb.mongodbUrl,
                        appName: 'poi'
                    }
                }
            ]
        });

        this.poiStore = new PoiStore({
            dataStore: this.legacyPOIDatastore,
            databaseName: this.poiMongodbDatabase,
            logger: this.logger
        });
        this.unityStore = new UnityStore({
            logger: this.logger,
            dataStore: this.dataStore
        });

        // TODO: we should make sure interval starts always at midnight (add to interval startDate? or better scheduleCallback)
        this.interval.set(this.syncUnity, '1h');

        this.server.get('/garages/capabilities', this.garageCapabilities, {
            protect: false
        });
        this.server.get('/force-sync-unity', this.forceSyncUnity, {
            protect: false
        });
    }

    protected garageCapabilities = async (req: Request, res: Response) => {
        try {
            const labelDefs = Object.entries(CapabilityLabels) as [
                keyof typeof Capabilitity,
                {
                    description: string;
                    name: string;
                }
            ][];

            const capabilityMap: CapabilityMap[] = labelDefs.map(([key, { description, name }]) => {
                const id = Capabilitity[key];
                return {
                    id,
                    name,
                    description,
                    isPrimary: PrimaryCapability.includes(id as number)
                };
            });

            return getResponse(res, ServerResponseCode.OK, capabilityMap);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    protected syncUnity = async () => {
        try {
            // get data from the Unity
            const garages = await this.unityStore.getGarages();
            // Filter out mobile repairers for SMR - RBAUAA-3332
            const pois = garages.filter((garage) => garage.mobileRepairer !== 'true').map(UnityStore.garageToPOIData);

            // if no data found, ignore this sync
            if (!pois.length) {
                this.logger.warn({
                    message: 'No garages retrieved from UNITY, bailing out'
                });
                return;
            }

            this.logger.info({
                message: `Retrieved ${pois.length} garages from unity, syncing with POI DB`
            });

            // replace old pois with new pois
            await this.poiStore.replacePoisForCategory(pois, POICategoryId.PreferredGarage);
        } catch (error) {
            this.logger.error({
                message: 'Error while synchronising Unity garages',
                error
            });
        }
    };

    protected forceSyncUnity = async (req: Request, res: Response) => {
        try {
            await this.syncUnity();
            return getResponse(res, ServerResponseCode.OK);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };
}
