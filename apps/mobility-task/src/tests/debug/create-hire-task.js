'use strict';

const aaSecurityChecker = require('aa-security-checker');
const config = require('dotenv').config();
const cert = aaSecurityChecker.certificates,
    _WEB_OPERATOR_ID = 9107292,
    securityFeatures = aaSecurityChecker.security,
    aahelp2Service = require('../../lib/services/aahelp2.service'),
    thirdPartySupplierSvc = require('../../lib/services/third-party-supplier.service'),
    createReasonFactory = require('@aa/malstrom-models/lib/factories/create-reason.factory');
const aaOracleUtilites = require('@aa/oracle-utilities');

config.parsed.appName = 'mobility-task';
aaOracleUtilites.init(config.parsed);

const mobilityTaskService = require('../../lib/services/mobility-task.service');

// const authToken = {
//     jwt: securityFeatures.createSecureToken({
//         userName: 'patrol',
//         operatorId: _WEB_OPERATOR_ID,
//         roleMask: '', // ideally we need a default role mask
//         availableRoles: [],
//     }, cert.privateKey),
//     operatorId: _WEB_OPERATOR_ID
// };
//
// const MobilityTask = require('@aa/mobility-models-common/lib/mobility-task.model');
//
// const HireScheduleRequest = require('@aa/mobility-models-common/lib/hire-schedule-request.model');
// const {cshConnectStrings, cshUser, cshPassword} = process.env;
//
// require('@aa/oracle-utilities/lib/connection-manager.service').init({
//     connectStrings: cshConnectStrings,
//     user: cshUser,
//     password: cshPassword
// })
//
// const _parentTaskId = 78368436;
//
// // read from prime an active task ...
// aahelp2Service.readTask(_parentTaskId, authToken)
//     .then((rssTask) => {
//         // clone task
//         return aahelp2Service.cloneTask(rssTask, createReasonFactory.carHire(), authToken)
//     })
//     .then((mobilityTask) => {
//         var _hireReq = new HireScheduleRequest({ // first time in .. so that we are doing a location search ...
//             type: HireScheduleRequest.SEARCH_TYPES.LOCATION
//         });
//
//         _hireReq.mobilityTask(mobilityTask);
//
//         _hireReq.searchType(HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE);
//
//         return mobilityTaskService.schedule(_hireReq, authToken);
//     })
//     .then((scheduleResp) => {
//         console.log('############## taskWriteResponse #################');
//         console.log(scheduleResp.taskWriteResponse.toJSON());
//         console.log('############## rental #################');
//         console.log(scheduleResp.rental.toJSON());
//     })
//     .catch((err) => {
//         console.log('#################### error ######################');
//         console.log(err);
//     });

/// new
const authToken = {
    jwt: securityFeatures.createSecureToken(
        {
            userName: 'patrol',
            operatorId: _WEB_OPERATOR_ID,
            roleMask: '', // ideally we need a default role mask
            availableRoles: []
        },
        cert.privateKey
    ),
    operatorId: _WEB_OPERATOR_ID
};

const MobilityTask = require('@aa/mobility-models-common/lib/mobility-task.model');

const HireScheduleRequest = require('@aa/mobility-models-common/lib/hire-schedule-request.model');
let _mobilityTask;
const _parentTaskId = 78368436;
let _hireReq;

// read from prime an active task ...

aahelp2Service
    .readTask(_parentTaskId, authToken)
    .then((rssTask) => {
        // clone task
        return aahelp2Service.cloneTask(rssTask, createReasonFactory.carHire(), authToken);
    })
    .then((mobilityTask) => {
        _mobilityTask = mobilityTask;
        _hireReq = new HireScheduleRequest({
            // first time in .. so that we are doing a location search ...
            searchType: HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE
        });
        _hireReq.mobilityTask(mobilityTask);
        return thirdPartySupplierSvc.initSubHire(_hireReq, authToken);
    })
    .then((subHireResp) => {
        // merge response details back to _mobilityTask.schedule / appointment ... rental
        const responseModel = subHireResp.taskWriteResponse;
        _mobilityTask.sequence(responseModel.sequence());
        _mobilityTask.status(responseModel.status());
        _mobilityTask.schedule(responseModel.schedule());
        _mobilityTask.appointment(responseModel.appointment());
        _mobilityTask.fault().capabilities(responseModel.capabilities());
        _mobilityTask.rental(subHireResp.rental);

        _hireReq.mobilityTask(_mobilityTask);

        return mobilityTaskService.schedule(_hireReq, authToken);
    })
    .then((scheduleResp) => {
        console.log('############## taskWriteResponse #################');
        console.log(scheduleResp.taskWriteResponse);
        console.log('############## rental #################');
        console.log(scheduleResp.rental.toJSON());
    })
    .catch((err) => {
        console.log('#################### error ######################');
        console.log(err);
    });
