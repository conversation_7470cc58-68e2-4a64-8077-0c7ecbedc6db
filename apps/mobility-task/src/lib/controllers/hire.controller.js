'use strict';

const _ = require('lodash'),
    fs = require('fs'),
    mobilityTaskService = require('../services/mobility-task.service'),
    logger = require('winston'),
    HireSignatures = require('@aa/mobility-models/lib/hire-signatures.model'),
    ConditionReport = require('@aa/mobility-models/lib/condition-report.model'),
    TermsAndConditionSvc = require('../services/terms-and-condition.service'),
    dateUtility = require('../utility/date.utility'),
    authTokenSvc = require('../services/auth-token.service'),
    notificationService = require('./../services/notification.service'),
    notificationTypes = require('./../enums/notification-types.enum'),
    cshMobilityTaskService = require('./../services/csh-mobility-task.service'),
    eventsBusQueueSvc = require('../services/events-bus-queue.service'),
    generalConstant = require('../constants/general.constant'),
    eventsBusQueueConstant = require('../constants/events-bus-queue.constant');

const { Queue: EventQueues } = require('@aa/azure-queue'),
    nanoid = require('nanoid');

function renamePhotos(photos, taskId, prefix) {
    const renamed = [];
    const idHex = parseInt(taskId).toString(16);
    photos.forEach((photo) => {
        const newName = `${photo.path}-${idHex}-${prefix}`;
        logger.info(`renamePhotos :: ${taskId} ${photo.path} ${newName}`);
        fs.renameSync(photo.path, newName);
        renamed.push({
            path: newName,
            blobName: `${prefix}-${idHex}-${nanoid(10)}`
        });
    });

    return renamed;
}
/* create a tmp file to store hire start / end requests so that we can re-processo*/
function tmpFile(params) {
    const { operation, taskId } = params;
    return `${process.env.HOME}/tmp/${taskId}.${operation}`;
}

function writeTx(fileName, params) {
    const copy = Object.assign({}, params); // make a copy ...
    copy.report = params.report.toJSON(); // turn these to json ..
    copy.signatures = params.signatures.toJSON();
    fs.writeFileSync(fileName, JSON.stringify(copy));
}

function sendEmailCheckinsVOR(taskId) {
    return cshMobilityTaskService.getVorCheckinNotifySummary(taskId).then((resp) => {
        const allowedCustGroupCodes = generalConstant.VOR_CHECKIN_NOTIFY_ALLOWED_CUSTOMER_GROUPS;
        if (allowedCustGroupCodes.indexOf(resp.customerGrpCode) > -1) {
            let subject = eventsBusQueueConstant.OFFHIRE_AUTOEMAIL_SUMMARY;
            let payload = { ...resp };
            let body = JSON.stringify(payload);
            return eventsBusQueueSvc
                .write(EventQueues.EDOCS_EMAIL, {
                    body,
                    subject
                })
                .then(() => {
                    logger.info(`OFFHIRE_AUTOEMAIL_SUMMARY :: For task id ${taskId} . Message sent to queue successfully!`);
                });
        }
    });
}

module.exports = {
    hireStart: (req, resp) => {
        const authToken = authTokenSvc.create(req.headers);
        const params = {
            taskId: parseInt(req.body.taskId),
            report: new ConditionReport(JSON.parse(req.body.conditionReport)),
            signatures: new HireSignatures(JSON.parse(req.body.signatures)),
            operation: 'start',
            authToken: authToken,
            photos: renamePhotos(req.files, req.body.taskId, 'out') // if rename this to photos upload would process them ...
        };
        const txFile = tmpFile(params);

        params.report.mileage(parseInt(params.report.mileage()));
        params.report.fuel(parseFloat(params.report.fuel()));

        logger.info(`hire.controler.hireStart :: ${params.taskId}`);

        // make a record of the request so that we can restart ...
        writeTx(txFile, params);

        return mobilityTaskService
            .checkOut(params)
            .then((edoc) => {
                // when we are happy that we no longer loss hire starts we will delete the temp file
                resp.status(200).json(edoc).end();
            })
            .catch((err) => {
                logger.error(`hire.controler.hireStart :: ${params.taskId}`, err);
                resp.status(500).json(err).end();
            });
    },

    hireEnd: (req, resp) => {
        const authToken = authTokenSvc.create(req.headers);
        const params = {
            taskId: parseInt(req.body.taskId),
            report: new ConditionReport(JSON.parse(req.body.conditionReport)),
            signatures: new HireSignatures(JSON.parse(req.body.signatures)),
            operation: 'end',
            authToken: authToken,
            photos: renamePhotos(req.files, req.body.taskId, 'in')
        };
        const txFile = tmpFile(params);
        params.report.mileage(parseInt(params.report.mileage()));
        params.report.fuel(parseFloat(params.report.fuel()));

        // make a record of the request so that we can restart ...
        writeTx(txFile, params);

        logger.info(`hire.controler.hireEnd :: ${params.taskId}`);

        return mobilityTaskService
            .checkIn(params)
            .then((edoc) => {
                // when we are happy that we no longer lose hire ends we will delete the temp file
                resp.status(200).json(edoc).end();
                return edoc;
            })
            .then(() => {
                let payload = {
                    taskId: params.taskId,
                    operation: params.operation
                };
                return mobilityTaskService.sapCheckinNotification(payload, authToken);
            })
            .then(() => {
                return mobilityTaskService
                    .getExtensionsOffHireSummary(
                        {
                            taskId: params.taskId,
                            description: 'Delayed Customer Collection',
                            payer: 'Customer'
                        },
                        authToken
                    )
                    .then((resp) => {
                        let subject = eventsBusQueueConstant.EXTENSIONS_OFFHIRE_AUTOEMAIL_SUMMARY.DELAYED_CUSTOMER_COLLECTION;
                        let payload = {
                            task: resp
                        };
                        let body = JSON.stringify(payload);
                        if (resp.length !== 0) {
                            if (['LANE', 'JAGA', 'HYU'].includes(resp[0].customerGrpCode)) {
                                logger.info(`Delayed Customer Collection :: ${body} `);
                                return eventsBusQueueSvc
                                    .write(EventQueues.EDOCS_EMAIL, {
                                        body,
                                        subject
                                    })
                                    .then(() => {
                                        logger.info(`Delayed Customer collection summary :: For task id ${params.taskId} . Message sent to queue successfully!`);
                                    });
                            }
                        } else {
                            logger.info(`Delayed customer collection summary :: For task id ${params.taskId} not found `);
                        }
                    });
            })
            .then(() => {
                return mobilityTaskService
                    .getExtensionsOffHireSummary(
                        {
                            taskId: params.taskId,
                            description: 'Reclaim',
                            payer: 'Customer'
                        },
                        authToken
                    )
                    .then((resp) => {
                        let subject = eventsBusQueueConstant.EXTENSIONS_OFFHIRE_AUTOEMAIL_SUMMARY.RECLAIM;
                        let payload = {
                            task: resp
                        };
                        let body = JSON.stringify(payload);
                        if (resp.length !== 0) {
                            if (resp[0].customerGrpCode === 'LANE' || resp[0].customerGrpCode === 'JAGA') {
                                logger.info(`Reclaims :: ${body} `);
                                return eventsBusQueueSvc
                                    .write(EventQueues.EDOCS_EMAIL, {
                                        body,
                                        subject
                                    })
                                    .then(() => {
                                        logger.info(`Reclaims summary :: For task id ${params.taskId} . Message sent to queue successfully!`);
                                    });
                            }
                        } else {
                            logger.info(`Reclaims summary :: For task id ${params.taskId} not found `);
                        }
                    });
            })
            .then(() => {
                return sendEmailCheckinsVOR(params.taskId);
            })
            .catch((err) => {
                logger.error(`hire.controler.hireEnd :: ${params.taskId}`, err);
                resp.status(500).json(err).end();
            });
    },
    termsAndConditions: (req, resp) => {
        const authToken = authTokenSvc.create(req.headers),
            custReqId = req.body.custReqId,
            taskId = req.body.taskId,
            mainDriverLicenceType = req.body.mainDriverLicenceType;

        logger.info(`hire.controler.termsAndConditions :: ${taskId} mainDriverLicenceType ${mainDriverLicenceType}`);
        TermsAndConditionSvc.update({
            taskId,
            custReqId,
            mainDriverLicenceType,
            authToken
        })
            .then((tcResp) => {
                const body = {
                    insuredTerms: tcResp.insuredTerms ? tcResp.insuredTerms.toJSON() : null,
                    insuranceOption: tcResp.insuranceOption ? tcResp.insuranceOption.toJSON() : null
                };
                resp.status(200).json(body).end();
            })
            .catch((err) => {
                resp.status(500).json(err).end();
            });
    },

    /**
     * Start self checkout hire like Enterprise, LCH
     * @param {Object} req
     * @param {Object} resp
     * @returns {Promise}
     */
    hireStartSelfCheckout: (req, resp) => {
        const authToken = authTokenSvc.create(req.headers);
        const params = {
            taskId: parseInt(req.body.taskId),
            operation: 'start',
            authToken: authToken,
            arrive: new Date(req.body.hireDatetime)
        };

        logger.info(`hire.controler.hireStartSelfCheckout :: ${params.taskId}`);

        return mobilityTaskService
            .openRental(params, req.body, authToken)
            .then(() => {
                return mobilityTaskService.checkOutSelfCheckout(params);
            })
            .then((respData) => {
                resp.status(200).json(respData).end();
            })
            .catch((err) => {
                logger.error(`hire.controller.hireStartSelfCheckout :: ${params.taskId}`, err);
                resp.status(500).json(err).end();
            });
    },

    /**
     * End self checkout hire like Enterprise, LCH
     * @param {Object} req
     * @param {Object} resp
     * @returns {Promise}
     */
    hireEndSelfCheckout: (req, resp) => {
        const authToken = authTokenSvc.create(req.headers);
        const params = {
            taskId: parseInt(req.body.taskId),
            operation: 'end',
            authToken: authToken,
            origin: req.body.origin,
            complete: req.body.hireDatetime ? new Date(req.body.hireDatetime) : new Date()
        };
        let taskDetailsResp = {};
        logger.info(`hire.controler.hireEndSelfCheckout :: ${params.taskId} from origin:${params.origin} and hireDatetime: ${params.complete}`);
        return mobilityTaskService
            .enterpriseRentalClosed(params.taskId, req.body, authToken)
            .then((enterpriseRentalClosedResponse) => {
                if (enterpriseRentalClosedResponse && enterpriseRentalClosedResponse.rentalClosedFromFleet) {
                    return;
                }
                return mobilityTaskService.checkInSelfCheckout(params);
            })
            .then((taskDetails) => {
                taskDetailsResp = taskDetails;
                if (params.origin === 'retailer-app') {
                    let subject = eventsBusQueueConstant.TRANSMISSION_TYPE.RENTAL_CLOSED;
                    let body = JSON.stringify(taskDetails);

                    return eventsBusQueueSvc
                        .write(EventQueues.ENTERPRISE_INBOUND, {
                            body,
                            subject
                        })
                        .then(() => {
                            logger.info(`Outbound Message :: For task id ${taskDetails.taskWriteResponse.taskId()} , transaction type - ${subject} is Success. Message sent to queue successfully!`);
                            // trigger notification for enterprise checkin at retailer only for ENT
                            const rental = taskDetails.mobilityTask.rental();
                            if (rental.hireVehicle().supplierTypeCode() !== 'ENT') {
                                return Promise.resolve();
                            }

                            const payload = {
                                date: new Date().getTime(),
                                type: notificationTypes.TPS_VEHICLE_CHECKED_IN,
                                taskId: taskDetails.taskWriteResponse.taskId()
                            };

                            // if TPS create notification
                            return notificationService.update(payload);
                        });
                }
            })
            .then(() => {
                const respData = {
                    status: 'ok',
                    msg: `Hire ended for ${params.taskId}`
                };
                resp.status(200).json(respData).end();
            })
            .then(() => {
                mobilityTaskService.sendEmail(taskDetailsResp, authToken);
            })
            .then(() => {
                let payload = {
                    taskId: params.taskId,
                    operation: params.operation,
                    origin: params.origin
                };
                return mobilityTaskService.sapCheckinNotification(payload, authToken);
            })
            .then(() => {
                return mobilityTaskService
                    .getExtensionsOffHireSummary(
                        {
                            taskId: params.taskId,
                            description: 'Delayed Customer Collection',
                            payer: 'Customer'
                        },
                        authToken
                    )
                    .then((resp) => {
                        let subject = eventsBusQueueConstant.EXTENSIONS_OFFHIRE_AUTOEMAIL_SUMMARY.DELAYED_CUSTOMER_COLLECTION;
                        let payload = {
                            task: resp
                        };
                        let body = JSON.stringify(payload);
                        if (resp.length !== 0) {
                            if (['LANE', 'JAGA', 'HYU'].includes(resp[0].customerGrpCode)) {
                                logger.info(`Delayed Customer Collection :: ${body} `);
                                return eventsBusQueueSvc
                                    .write(EventQueues.EDOCS_EMAIL, {
                                        body,
                                        subject
                                    })
                                    .then(() => {
                                        logger.info(`Delayed Customer collection summary :: For task id ${params.taskId} . Message sent to queue successfully!`);
                                    });
                            }
                        } else {
                            logger.info(`Delayed customer collection summary :: For task id ${params.taskId} not found `);
                        }
                    });
            })
            .then(() => {
                return mobilityTaskService
                    .getExtensionsOffHireSummary(
                        {
                            taskId: params.taskId,
                            description: 'Reclaim',
                            payer: 'Customer'
                        },
                        authToken
                    )
                    .then((resp) => {
                        let subject = eventsBusQueueConstant.EXTENSIONS_OFFHIRE_AUTOEMAIL_SUMMARY.RECLAIM;
                        let payload = {
                            task: resp
                        };
                        let body = JSON.stringify(payload);
                        if (resp.length !== 0) {
                            if (resp[0].customerGrpCode === 'LANE' || resp[0].customerGrpCode === 'JAGA') {
                                logger.info(`Reclaims :: ${body} `);
                                return eventsBusQueueSvc
                                    .write(EventQueues.EDOCS_EMAIL, {
                                        body,
                                        subject
                                    })
                                    .then(() => {
                                        logger.info(`Reclaims summary :: For task id ${params.taskId} . Message sent to queue successfully!`);
                                    });
                            }
                        } else {
                            logger.info(`Reclaims summary :: For task id ${params.taskId} not found `);
                        }
                    });
            })
            .then(() => {
                return sendEmailCheckinsVOR(params.taskId);
            })
            .catch((err) => {
                logger.error(`hire.controller.hireEndSelfCheckout :: ${params.taskId}`, err);
                resp.status(500).json(err).end();
            });
    },

    /**
     * Confirm third party hire
     * @param {Object} req
     * @param {Object} resp
     * @returns {Promise}
     */
    hireConfirm: (req, resp) => {
        const authToken = authTokenSvc.create(req.headers);
        return mobilityTaskService
            .thirdPartyRentalConfirmed(req.body.taskId, req.body.rentalContractDetails, authToken)
            .then((respData) => {
                resp.json(respData);
            })
            .catch((err) => {
                resp.status(500).json(err);
            });
    },

    /**
     * Get Enterprise-Rent-A-Car branch locations
     * @param {Object} req
     * @param {Object} resp
     * @returns {Promise} Locations list
     */
    getEnterpriseLocations: (req, resp) => {
        const authToken = authTokenSvc.create(req.headers);
        return mobilityTaskService
            .getEnterpriseLocations(req.body, authToken)
            .then((locations) => {
                if (!Array.isArray(locations)) {
                    logger.error(`hire.controller.getEnterpriseLocations :: Unable to get locations from Enterprise :: ${locations.body}`);
                    resp.status(500).send('Unable to get locations from Enterprise');
                    return;
                }
                let branchDetails = locations[0].branchDetails;
                if (branchDetails) {
                    let formatedBranchDetails = branchDetails.map((branch) => {
                        let locationInformation = _.get(branch, 'locationInformation', '');
                        let hoursOfOperation = _.get(branch, 'locationInformation.hoursOfOperation', '');
                        locationInformation.hoursOfOperation = dateUtility.formatEntHoursOfOperation(hoursOfOperation);
                        return locationInformation;
                    });
                    resp.status(200).send(formatedBranchDetails);
                } else {
                    logger.info(`hire.controler.getEnterpriseLocations :: branchDetails ${branchDetails} - Response -${locations}`);
                    resp.status(200).send([]);
                }
            })
            .catch((e) => {
                logger.error(`hire.controler.getEnterpriseLocations :: error ${e}`);
                return resp.status(500).json(e);
            });
    },

    /**
     * Get Enterprise-Rent-A-Car branch locations Using Lat Long
     * @param {Object} req
     * @param {Object} resp
     * @returns {Promise} Locations list
     */
    getEnterpriseLocationsWithLatLong: (req, resp) => {
        /**
         * TODO:- Need to finalise this section after discussion with EnterpriseTeam.
         */
        const defaultGetSearchCriteria = {
                searchCriteria: {
                    countryCode: 'GBR',
                    geographicCoordinates: {
                        latitude: req.query.latitude || 0,
                        longitude: req.query.longitude || 0
                    }
                },
                searchParameters: {
                    maxSearchDistance: 40,
                    maxReturnedLocations: 4,
                    rentalBusinessType: 'Car'
                }
            },
            authToken = authTokenSvc.create(req.headers);
        if (req.query.taskId) {
            defaultGetSearchCriteria.taskId = req.query.taskId;
        }

        req.body = defaultGetSearchCriteria;

        return mobilityTaskService
            .getEnterpriseLocations(req.body, authToken)
            .then((locations) => {
                if (!locations) {
                    const errMsg = 'Error: Invalid/No response from enterprise';
                    logger.error(`hire.controler.getEnterpriseLocationsWithLatLong :: `, errMsg);
                    return resp.status(500).send(errMsg);
                }

                let location = locations[0];
                if (location) {
                    let branchDetailsResponse = location.branchDetails.map((branch) => {
                        let locationDetails = _.get(branch, 'locationInformation.locationDetails', '');
                        let hoursOfOperation = _.get(branch, 'locationInformation.hoursOfOperation', '');
                        let locationDetail = {
                            locationId: locationDetails.locationId,
                            locationName: locationDetails.locationName,
                            street: locationDetails.address.street1,
                            city: locationDetails.address.city,
                            county: locationDetails.address.county,
                            postalCode: locationDetails.address.postalCode,
                            telephoneNumber: locationDetails.telephoneNumber.number,
                            latitude: locationDetails.geographicCoordinates.latitude,
                            longitude: locationDetails.geographicCoordinates.longitude,
                            hoursOfOperation: dateUtility.formatEntHoursOfOperation(hoursOfOperation)
                        };
                        return locationDetail;
                    });
                    return resp.json(branchDetailsResponse);
                }
                logger.info(`hire.controler.getEnterpriseLocationsWithLatLong :: Response - ${locations}`);
                return resp.json([]);
            })
            .catch((e) => {
                logger.error(`hire.controler.getEnterpriseLocationsWithLatLong :: error ${e}`);
                return resp.status(500).json(e);
            });
    },
    /**
     * getOnHireThirdPartyVehicles
     * @param {Object} req
     * @param {Object} resp
     * @returns {Promise} Number of records found
     */
    getOnHireThirdPartyVehicles: (req, resp) => {
        return mobilityTaskService
            .onHireThirdPartyVehicles(req.params.regNumber)
            .then((records) => {
                logger.info(`hire.controler.getOnHireThirdPartyVehicles :: Response - ${records}`);
                return resp.json(records.rows[0][0] > 0 ? true : false);
            })
            .catch((e) => {
                logger.error(`hire.controler.getOnHireThirdPartyVehicles :: error ${e}`);
                return resp.status(500).json(e);
            });
    },
    sapCheckin: (req, resp) => {
        let authToken = authTokenSvc.create();
        let payload = {
            taskId: parseInt(req.body.taskId),
            operation: 'end',
            origin: ''
        };
        return mobilityTaskService.sapCheckinNotification(payload, authToken).then(() => {
            resp.status(200).json({}).end();
        });
    }
};
