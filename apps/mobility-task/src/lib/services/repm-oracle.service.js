'use strict';

const ConnectionPoolSvc = require('@aa/oracle-utilities').ConnectionPoolService,
    oracledb = require('oracledb');

const user = process.env.cshUser,
    password = process.env.cshPassword,
    appName = 'mobility-task';

let connectString = process.env.repMConnectionStrings.split(',')[0];

let repmPool = null;
oracledb.fetchAsString = [oracledb.CLOB];

module.exports = {
    init: () => {
        return ConnectionPoolSvc.getInstance({
            user,
            password,
            connectString,
            appName
        }).then((pool) => {
            repmPool = pool;
            return;
        });
    },
    connect: () => {
        return repmPool.connect().catch((err) => {
            //if first connection string is not working then check for second
            connectString = process.env.repMConnectionStrings.split(',').length > 1 ? process.env.repMConnectionStrings.split(',')[1] : null;
            if (connectString) {
                return ConnectionPoolSvc.getInstance({
                    user,
                    password,
                    connectString
                }).then((pool) => {
                    repmPool = pool;
                    return repmPool.connect();
                });
            }
            return Promise.reject(err);
        });
    },
    release: (dbConn) => {
        return repmPool.release(dbConn);
    },
    cursorRead: (dbConn, sql, bindvars) => {
        return new Promise((resolve, reject) => {
            dbConn
                .execute(sql, bindvars, {
                    outFormat: oracledb.OBJECT
                })
                .then((result) => {
                    let resultStream = null,
                        results = [];

                    resultStream = result.outBinds.cursor.toQueryStream();

                    resultStream.on('error', function (error) {
                        reject(error);
                    });

                    resultStream.on('data', function (data) {
                        results.push(data);
                    });

                    resultStream.on('end', () => {
                        resultStream.destroy();
                    });

                    resultStream.on('close', function () {
                        resolve(results);
                    });
                })
                .then((results) => {
                    repmPool.release(dbConn);
                    return results;
                })
                .catch((err) => {
                    if (dbConn) {
                        repmPool.release(dbConn);
                    }
                    return Promise.reject(err);
                });
        });
    },
    stats: () => {
        return repmPool.stats();
    }
};
