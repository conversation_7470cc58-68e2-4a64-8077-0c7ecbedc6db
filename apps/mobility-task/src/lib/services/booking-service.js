'use strict';

const Q = require('q'),
    logger = require('winston'),
    MobilityInsurance = require('@aa/mobility-models/lib/mobility-insurance.model'),
    HireScheduleRequest = require('@aa/mobility-models-common/lib/hire-schedule-request.model'),
    InsurerErrorsFactory = require('@aa/mobility-models/lib/factories/insurer-errors.factory'),
    Resource = require('@aa/malstrom-models/lib/resource.model'),
    TaskWriteResponse = require('@aa/malstrom-models/lib/task-write-response.model'),
    aahelp2Service = require('aahelp2.service'),
    insurerServices = require('insure.service'),
    cshMobilityTask = require('./csh-mobility-task.service'),
    cshResource = require('./csh-resource.service'),
    cshSupplier = require('./csh-supplier.service');

function _storeRental(scope) {
    return cshMobilityTask.write(scope.hireScheduleReq.mobilityTask(), scope.authToken).then(() => {
        return scope;
    });
}

function _cancelExistingInsurance(scope) {
    if (scope.hireScheduleReq.mobilityTask().rental().isSelfInsured() || !scope.hireScheduleReq.mobilityTask().rental().insurance().id()) {
        return Q.resolve(scope);
    }

    return insurerServices.deleteInsurance(scope.hireScheduleReq.mobilityTask().rental().insurance().id(), scope.authToken).then(() => {
        // reset mobiltity insruance
        const existingSupNetworkCode = scope.hireScheduleReq.mobilityTask().rental().insurance().supNetworkCode();
        scope.hireScheduleReq.mobilityTask().rental().insurance(new MobilityInsurance());
        scope.hireScheduleReq.mobilityTask().rental().insurance().supNetworkCode(existingSupNetworkCode);
        return scope;
    });
}

function _resourceDetails(scope) {
    if (!scope.primeResponse.schedule().resource().isSet()) {
        return Q.resolve(scope);
    }

    return cshResource
        .getVehicle({
            resourceId: scope.primeResponse.schedule().resource().id(),
            authToken: scope.authToken
        })
        .then((cshVehicleScope) => {
            scope.hireScheduleReq.mobilityTask().rental().hireVehicle(cshVehicleScope.hireVehicle);
            return scope;
        })
        .catch((err) => {
            logger.error(`booking-service._vehicleDetails :: failed to to details for resource ${scope.primeResponse.schedule().resource().id()}`, err);
            // but we continue ...
            return scope;
        });
}

function _supplierDetails(scope) {
    if (!scope.primeResponse.schedule().resource().isSet()) {
        return Q.resolve(scope);
    }

    return cshSupplier
        .read(scope.primeResponse.schedule().resource().managerId())
        .then((cshSupplierScope) => {
            scope.hireScheduleReq.mobilityTask().rental().collectLocation(cshSupplierScope);

            if (!scope.hireScheduleReq.mobilityTask().rental().repairLocation().isSet()) {
                scope.hireScheduleReq.mobilityTask().rental().repairLocation(cshSupplierScope);
            }
            if (scope.hireScheduleReq.mobilityTask().recovery().isDestResourceIdSet()) {
                scope.hireScheduleReq.mobilityTask().recovery().destResourceId(cshSupplierScope.id());
            }

            return aahelp2Service.bookHireCar(scope.hireScheduleReq.mobilityTask(), scope.authToken);
        })
        .catch((err) => {
            logger.error(
                `booking-.service_supplierDetails :: failed to get supplier details for resource ${scope.primeResponse.schedule().resource().id()} @ ${scope.primeResponse
                    .schedule()
                    .resource()
                    .managerId()}`,
                err
            );
            // but we continue ...
            return scope;
        });
}

function _bookResource(scope) {
    scope.hireScheduleReq.mobilityTask().indicators().authorised(true);

    // if that is not new and we want to assign a particular resource then set the status to PLAN
    if (!scope.hireScheduleReq.mobilityTask().isNew() && scope.hireScheduleReq.searchType() === HireScheduleRequest.SEARCH_TYPES.VEHICLE) {
        scope.hireScheduleReq.mobilityTask().status('PLAN'); // change status in PLAN so prime tries to assign requested resource
    } else {
        scope.hireScheduleReq.mobilityTask().schedule().resource(new Resource());
        scope.hireScheduleReq.mobilityTask().status('UNAC'); // change status in UNAC so prime searches for a resource
    }

    return aahelp2Service
        .writeTask(scope.hireScheduleReq.mobilityTask(), scope.authToken)
        .then((primeResponse) => {
            scope.primeResponse = primeResponse;
            scope.hireScheduleReq.mobilityTask().sequence(primeResponse.sequence());
        })
        .catch((err) => {
            logger.error('booking-service._bookResource:: failed to commicate with task-service ... ', err);
            // save state ...
            return _storeRental(scope).then(() => {
                scope.primeResponse = new TaskWriteResponse({
                    msg: 'failed to communicate with prime',
                    id: -200
                });
                return scope;
            });
        });
}

function _bookInsurance(scope) {
    if (!scope.primeResponse.schedule().resource().isSet()) {
        return Q.resolve(scope);
    }

    return insurerServices
        .bookInsurance(scope.hireScheduleReq.mobilityTask(), scope.authToken)
        .then((insurerScope) => {
            // persist insurance details ..
            scope.hireScheduleReq.mobilityTask().rental().insurance().id(insurerScope.id);
            scope.hireScheduleReq.mobilityTask().rental().insurance().insuredVehicleId(insurerScope.stockId);
        })
        .catch((err) => {
            const msg = `Insurer reports vehicle reservation error :  task ${scope.hireScheduleReq.mobilityTask().id()} '${InsurerErrorsFactory.hasError(err)} - ${scope.primeResponse.primeMsg()}`;
            // place task in UNAC and status in not authorised
            scope.primeResponse.msg(msg);
            scope.hireScheduleReq.mobilityTask().status('UNAC');
            scope.hireScheduleReq.mobilityTask().indicators().authorised(false);
            return aahelp2Service
                .writeTask(scope.hireScheduleReq.mobilityTask(), scope.authToken)
                .then((primeResponse) => {
                    scope.primeResponse = primeResponse;
                    scope.primeResponse.msg(msg);
                    return scope;
                })
                .catch((err) => {
                    logger.error('booking-service._bookInsurance :: failed to cancel resource in prime ..', err);
                    return scope;
                });
        });
}

module.exports = {
    schedule: (hireScheduleReq, authToken) => {
        const scope = {
            hireScheduleReq,
            authToken
        };

        return _cancelExistingInsurance(scope)
            .then(_bookResource)
            .then(_resourceDetails)
            .then(_bookInsurance)
            .then(_supplierDetails)
            .then(_storeRental)
            .then((bookedScope) => {
                return {
                    taskWriteResponse: bookedScope.primeResponse,
                    rental: bookedScope.hireScheduleReq.mobilityTask().rental()
                };
            });
    }
};
