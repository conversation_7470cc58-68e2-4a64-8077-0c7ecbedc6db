'use strict';
/**@module services-mobility-task */
const rentalDataSvc = require('./rental-data.service'),
    Q = require('q'),
    _ = require('lodash'),
    moment = require('moment'),
    taskAuditSvc = require('./task-audit.service'),
    errors = require('../constants/error.constant'),
    logger = require('winston'),
    repmService = require('../services/repm-resource.service'),
    aahelp2Service = require('./aahelp2.service'),
    hireVehicleSvc = require('./hire-vehicle.service'),
    HireScheduleRequest = require('@aa/mobility-models-common/lib/hire-schedule-request.model'),
    MobilityTask = require('@aa/mobility-models-common/lib/mobility-task.model'),
    Task = require('@aa/malstrom-models/lib/task.model'),
    HireSite = require('@aa/mobility-models/lib/hire-site.model'),
    cshResource = require('./csh-resource.service'),
    cshSupplier = require('./csh-supplier.service'),
    createReasonFactory = require('@aa/malstrom-models/lib/factories/create-reason.factory'),
    cshHireConditionSvc = require('./csh-hire-condition.service'),
    uploadFilesSvc = require('./upload-files.service'),
    InsurerErrorsFactory = require('@aa/mobility-models/lib/factories/insurer-errors.factory'),
    MobilityInsurance = require('@aa/mobility-models/lib/mobility-insurance.model'),
    TaskWriteResponse = require('@aa/malstrom-models/lib/task-write-response.model'),
    EnterpriseSubHire = require('@aa/mobility-models/lib/sub-hire.enterprise.model'),
    Resource = require('@aa/malstrom-models/lib/resource.model'),
    hireDocsSvc = require('./hire-documents.service'),
    thirdPartySupplierSvc = require('./third-party-supplier.service'),
    supplierSvc = require('./supplier.service'),
    extensionSvc = require('./extension.service'),
    Queue = require('better-queue'),
    enterpriseHireService = require('./enterprise-hire.service'),
    SubHire = require('@aa/mobility-models/lib/sub-hire.model'),
    AAHelpService = require('../services/aahelp2.service'),
    generalConstant = require('../constants/general.constant'),
    EnterpriseRentedVehicle = require('@aa/mobility-models/lib/enterprise-rented-vehicle.model'),
    notificationService = require('./../services/notification.service'),
    notificationTypes = require('./../enums/notification-types.enum'),
    incidentManaged = require('./incident-managed.service'),
    RefId = require('@aa/malstrom-models/lib/ref-id.model'),
    RafVehicle = require('@aa/mobility-models/lib/raf-vehicle.model'),
    appointmentChangeSvc = require('./appointment-change.service'),
    EventsBusQueueRepository = require('../repositories/events-bus-queue.repository'),
    eventsBusQueueConstant = require('../constants/events-bus-queue.constant'),
    enterpriseMobilitySvc = require('./enterprise-mobility-task.service'),
    cshMobilityTaskSvc = require('./csh-mobility-task.service'),
    entitlementSvc = require('./entitlement.service'),
    aahelp2UrlsConstant = require('../constants/aahelp2-urls.constant'),
    uuidv4 = require('uuid').v4,
    Capabilities = require('@aa/malstrom-models/lib/constants/capabilities.constants');
const { Vehicle } = require('@aa/data-models/common');
const { Queue: EventQueues } = require('@aa/azure-queue');
const enterpriseMobilityTaskService = require('./enterprise-mobility-task.service');

const _checkoutQueue = new Queue(
    (details, cb) => {
        const _details = details;
        logger.info(`mobility-task-service._checkoutQueue :: processing ${_details.taskId}`);
        uploadFilesSvc
            .process(_details)
            .then(() => {
                logger.info(`mobility-task-service._checkoutQueue :: processing ${_details.taskId} done upload`);
                _details.mobilityTask.rental().checkoutReport(_details.report);
                _details.mobilityTask.rental().checkoutSignatures(_details.signatures);
                return cshHireConditionSvc.checkOut(_details);
            })
            .then(() => {
                // return rentalDataSvc.writeRedisOnly(_details.mobilityTask);
                return rentalDataSvc.write(_details.mobilityTask);
            })
            .then(() => {
                logger.info(`mobility-task-service._checkoutQueue :: processing ${_details.taskId} wrote checkout report`);
                cb();
            })
            .catch((err) => {
                logger.error(`mobility-task-service._checkoutQueue :: processing ${_details.taskId}`, err);
                cb();
            });
    },
    {
        concurrent: 3
    }
);

const _checkinQueue = new Queue(
    (details, cb) => {
        const _details = details;
        logger.info(`mobility-task-service._checkinQueue :: processing ${_details.taskId}`);
        uploadFilesSvc
            .process(_details)
            .then(() => {
                logger.info(`mobility-task-service._checkinQueue :: processing ${_details.taskId} done upload`);
                _details.mobilityTask.rental().checkinReport(_details.report);
                _details.mobilityTask.rental().checkinSignatures(_details.signatures);
                return cshHireConditionSvc.checkIn(_details);
            })
            .then(() => {
                return rentalDataSvc.writeRedisOnly(_details.mobilityTask);
            })
            .then(() => {
                return hireVehicleSvc.relocateResource(_details.mobilityTask, _details.authToken);
            })
            .then(() => {
                logger.info(`mobility-task-service._checkinQueue :: processing ${_details.taskId} wrote checkin report`);
                cb();
            })
            .catch((err) => {
                logger.error(`mobility-task-service._checkinQueue :: processing ${_details.taskId}`, err);
                cb();
            });
    },
    {
        concurrent: 3
    }
);

function _isCCPEnabled() {
    return /true/.test(process.env.enableCCP);
}

function _isCheckoutExtentionGroup(mobilityTask) {
    return mobilityTask.entitlement().customerGroup().isCheckoutExtensionGroup();
}

function _write(mobilityTask, authToken) {
    let _taskWriteResp;

    return aahelp2Service
        .writeTask(mobilityTask, authToken)
        .then((taskWriteResp) => {
            _taskWriteResp = taskWriteResp;
            _taskWriteResp.appointment(mobilityTask.appointment()); // keep that the same ...
            logger.info(`mobility-task-service.write :: write outcome prime ${taskWriteResp.status()}`);
            if (!mobilityTask.rental().insurance().id()) {
                mobilityTask.rental().mainDriver().softReset();
                mobilityTask
                    .rental()
                    .additionalDrivers()
                    .forEach((driver, idx) => {
                        setTimeout(() => {
                            driver.softReset();
                        }, 100 * idx);
                    });
            }
            return rentalDataSvc.write(mobilityTask, authToken);
        })
        .then((repoResp) => {
            logger.info(`mobility-task-service.write :: write repo ${repoResp}`);
            return _taskWriteResp;
        })
        .catch((err) => {
            logger.error(`mobility-task.write:: add task id ${mobilityTask.id()} seqNo ${mobilityTask.sequence()}:: ${err.msg}`);
            return Q.reject(errors.create(2001, 'write error', err));
        });
}

function _read(id, authToken) {
    logger.info(`MobilityTask.read :: task id ${id} `);

    if (!id) {
        let err = `mobility-task.read:: task id ${id} :: task id is not valid`;
        logger.error(err);
        return Q.reject(errors.create(2002, 'read error', err));
    }

    return Q.all([aahelp2Service.readTask(id, authToken), rentalDataSvc.read(id, authToken)])
        .spread((aah2Task, rental) => {
            const model = new MobilityTask(aah2Task.toJSON()); // lazy cloning ...
            model.rental(rental);
            return model;
        })
        .catch((err) => {
            logger.error(`mobility-task.read:: read task id ${id} :: ${err.msg}`);
            return Q.reject(errors.create(2002, 'read error', err));
        });
}

/**
 * To get Supplier details for Enterprise Car Hire containing profiles details based on hirerNetworkId
 * @param  {Object} mobilityTask
 * @param  {Object} authToken
 * @return array of Suppliers
 */
function _setEnterpriseResource(mobilityTask, searchType, authToken) {
    const resourceId = mobilityTask.rental().thirdPartyHire().supplier().resourceId();
    _setEnterpriseResourceId(mobilityTask, resourceId);

    return _overwiteEnterpriseProfile(mobilityTask, searchType, authToken)
        .then(() => {
            logger.info(`mobility-task-service.setEnterpriseResource ${mobilityTask.tag()}`);
            return aahelp2Service.writeTask(mobilityTask, authToken);
        })
        .catch((err) => {
            logger.error(`mobility-task-service.setEnterpriseResource ${mobilityTask.tag()} :: ${err.message}`);
            return Q.reject({
                msg: `Unable to set resource for enterprise  :: ${err.message}`
            });
        });
}

/**
 * Overwrite supplier for PORSCHE and SMA customer groups
 * @param  {Object} mobilityTask
 */
function _overwiteEnterpriseProfile(mobilityTask, searchType, authToken) {
    const deferred = Q.defer();
    if (![HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE].includes(searchType)) {
        deferred.resolve();
        return deferred.promise;
    }
    let overwriteProfiles = generalConstant.OVERWRITE_PROFILES;
    let selectedProfile = overwriteProfiles[mobilityTask.entitlement().customerGroup().code()];
    if (selectedProfile) {
        switch (selectedProfile.customerGroup) {
            case 'POR':
                //Overwrite supplier profile if selected fault is RTC/VANDALISM
                let faultName = mobilityTask.fault().name() || '';
                let faultMappingInDb = faultName.toLowerCase().includes(generalConstant.FAULT.VANDALISM) ? generalConstant.FAULT_MAPPING.VANDALISM : generalConstant.FAULT_MAPPING.RTC;

                const postData = {
                    customerGroupCode: selectedProfile.customerGroup,
                    fault: faultMappingInDb
                };
                return entitlementSvc
                    .getFaultCodesbyCustGroup(postData, authToken)
                    .then((data) => {
                        if (data && data.length > 0 && data.includes(mobilityTask.fault().code().code())) {
                            _setOverwiteEnterpriseProfile(mobilityTask, selectedProfile);
                        }
                    })
                    .catch((err) => {
                        logger.error(`mobility-task-service.getFaultCodesbyCustGroup ${mobilityTask.tag()} unable to get faults based on customer groups:: ${postData} :: ${err.message}`);
                        return Q.reject({
                            message: `getFaultCodesbyCustGroup :: unable to get faults based on customer groups`
                        });
                    });
            case 'SMA':
                //Overwrite supplier for non-electric car
                if (mobilityTask.vehicle().experianDetails().fuel() && mobilityTask.vehicle().experianDetails().fuel().toUpperCase() !== generalConstant.FUEL_TYPE.ELECTRIC) {
                    _setOverwiteEnterpriseProfile(mobilityTask, selectedProfile);
                }
                break;
            default:
                break;
        }
    }
    deferred.resolve();

    return deferred.promise;
}

function _setOverwiteEnterpriseProfile(mobilityTask, profile) {
    mobilityTask.rental().thirdPartyHire().supplier().profileName(profile.profileName);
    mobilityTask.rental().thirdPartyHire().supplier().armsProfileId(profile.armsProfileId);
    mobilityTask.rental().thirdPartyHire().supplier().reasonForHire(profile.reasonForHire);
    mobilityTask.rental().thirdPartyHire().supplier().armsOfficeId(profile.armsOfficeId);
}

function _setEnterpriseResourceId(mobilityTask, logicalId) {
    mobilityTask.schedule().resource(
        new Resource({
            id: logicalId
        })
    );
    mobilityTask.rental().hireVehicle().resourceId(logicalId);
    mobilityTask.rental().thirdPartyHire().logicalResourceId(logicalId);
    mobilityTask.rental().hireVehicle().resourceId(logicalId);
    mobilityTask.rental().thirdPartyHire().hireVehicle().resourceId(logicalId);

    mobilityTask.indicators().incidentManaged(false);
    // make sure we in plan so that we allocation can work ...
    mobilityTask.status('PLAN');
}

function _delayPromise(duration) {
    return new Promise(function (resolve) {
        setTimeout(function () {
            resolve();
        }, duration);
    });
}

function _fetchUpdatedMobilityTask(taskId, authToken) {
    /**
     * Adding this delay after discussing with eva-lite and Agile team as
     * Sometimes, system is not able to read updated task in first attempt (i.e. without delay)
     *
     */
    return _read(taskId, authToken)
        .then((mobilityTask) => {
            return Q.resolve(mobilityTask);
        })
        .catch(() => {
            logger.info('mobilityTaskService.fetchUpdatedMobilityTask:: Error while immediate read - Adding delay in read');
            return _delayPromise(500).then(() => {
                return _read(taskId, authToken);
            });
        });
}

/**
 * checkout aka vehicle hire start
 * @param  {Object} details
 * @param {Number} details.id of the task we process
 * @param {MobilityTask=} details.mobilityTask persists details of the task we read from both prime and MB_HIRE_TASK
 *     table
 * @param {ConditionReport} details.report of the vehicle condition at checkout
 * @param {HireSignatures} details.signatures of the main driver and retailer to received the vehicle
 * @param {String} details.operation=start identifies that we are starting the hire
 * @param {Object} details.authToken details of the end user credentials
 * @return {Promise}
 */
function _checkOut(details) {
    const checkoutTime = new Date();
    let estimatedCompDate = new Date(checkoutTime),
        docName;
    logger.info(`mobility-task-service._checkOut::checking out ${details.taskId} `);
    details.report.docName(uuidv4());
    return _read(details.taskId, details.authToken)
        .then((mobilityTask) => {
            // adjust repairTime so that estimated hire finishes at 23:59 of the day is supposed to return ..
            estimatedCompDate.setUTCMinutes(estimatedCompDate.getUTCMinutes() + mobilityTask.fault().repairMinutes());
            estimatedCompDate.setUTCHours(23);
            estimatedCompDate.setUTCMinutes(59);
            estimatedCompDate.setUTCSeconds(0);
            estimatedCompDate.setUTCMilliseconds(0);

            // let's hope prime allows us to adjust repair time just as we arrive
            mobilityTask.fault().repairMinutes(Math.round((estimatedCompDate.getTime() - checkoutTime.getTime()) / 60000));

            // we need to adjust completeion time so that is midnight

            taskAuditSvc.write(mobilityTask.id(), mobilityTask.customerRequestId(), `Hire start ${mobilityTask.rental().hireVehicle().regNo()}`, details.authToken);

            logger.info(`mobility-task-service._checkOut::checking out ${mobilityTask.tag()} and repairMinutes - ${mobilityTask.fault().repairMinutes()}`);
            if (mobilityTask.status() !== 'HEAD' && mobilityTask.status() !== 'GDET') {
                logger.error(`mobility-task-service._checkOut::checking out ${mobilityTask.tag()} NOT in HEAD OR GDET`);
                return Q.reject({
                    msg: `task in state of ${mobilityTask.status()} can not proceed to checkout`
                });
            }
            details.mobilityTask = mobilityTask;
            details.mobilityTask.schedule().arrive(checkoutTime);
            details.mobilityTask.rental().checkoutReport().docName(details.report.docName());

            //Update firstHireStart first time only if not exist
            if (!details.mobilityTask.rental().firstHireStart()) {
                details.mobilityTask.rental().firstHireStart(checkoutTime);
            }

            details.custReqId = mobilityTask.customerRequestId(); // add that here becuase we need it
            return aahelp2Service.startCarHire(details.mobilityTask, details.authToken);
        })
        .then(() => {
            if (details.mobilityTask.rental().isThirdPartyHireSet()) {
                details.mobilityTask.status('GARR'); // special for third party
                details.mobilityTask.schedule().arrive(new Date(checkoutTime)); // need to do this for third party hires ..
            } else {
                details.mobilityTask.status('HIRE');
            }
            logger.info(`mobility-task-service._checkOut::update prime out ${details.mobilityTask.tag()}`);
            return aahelp2Service.writeTask(details.mobilityTask, details.authToken);
        })
        .then((taskWriteResp) => {
            details.mobilityTask.status(taskWriteResp.status());
            details.mobilityTask.schedule(taskWriteResp.schedule());
            // render service needs these..
            details.mobilityTask.rental().checkoutReport(details.report);
            details.mobilityTask.rental().checkoutSignatures(details.signatures);
            _checkoutQueue.push(details);
            return aahelp2Service.docRender(details);
        })
        .then((dName) => {
            docName = dName;
            return _swapCarCheckout(details.mobilityTask, details.authToken);
        })
        .then(() => {
            _swapCheckoutMailTrigger(details.mobilityTask, details.authToken);
        })
        .then(async () => {
            let report = await hireDocsSvc.generateSASToken(docName);
            return {
                report
            };
        });
}

function _swapCheckoutMailTrigger(mobilityTask, authToken) {
    let taskArrFinal;
    //Checking if there are more than 1 HIRE in a customer Req Id
    cshMobilityTaskSvc.getHiresByCustRequestId(mobilityTask.id()).then((tasksArr) => {
        logger.info(`MobilityTask.HireTasks before filter :: task ids ${tasksArr} `);
        taskArrFinal = tasksArr.filter((arr) => arr !== mobilityTask.id());

        logger.info(`MobilityTask.HireTasks after filter:: task ids length ${taskArrFinal.length} `);
        //To trigger a mail if there are more than 1 HIRE in a customer Req Id
        if (taskArrFinal.length > 0) {
            _sendSwapEmail(mobilityTask, authToken, taskArrFinal);
        }
    });
}

function _swapCarCheckout(mobilityTask, authToken) {
    return aahelp2Service.checkoutMessageToSBQ(mobilityTask, authToken);
}

/**
 * check in aka vehicle returns from hire ..
 * @param  {Object} details
 * @param {Number} details.id of the task we process
 * @param {MobilityTask=} details.mobilityTask persists details of the task we read from both prime and MB_HIRE_TASK
 *     table
 * @param {ConditionReport} details.report of the vehicle condition
 * @param {HireSignatures} details.signatures of the main driver and retailer to received the vehicle
 * @param {String} details.operation=end identifies that we are
 * @param {Object} details.authToken details of the end user credentials
 * @return {Promise}
 */
function _checkIn(details) {
    details.report.docName(uuidv4());
    logger.info(`mobility-task-service._checkIn::checking in ${details.taskId}`);
    return _read(details.taskId, details.authToken)
        .then((mobilityTask) => {
            details.mobilityTask = mobilityTask;
            // set the document name
            details.mobilityTask.rental().checkinReport().docName(details.report.docName());
            logger.info(`mobility-task-service._checkIn::checking in ${details.taskId} docName ${details.mobilityTask.rental().checkinReport().docName()}`);

            taskAuditSvc.write(mobilityTask.id(), mobilityTask.customerRequestId(), `Hire end ${mobilityTask.rental().hireVehicle().regNo()}`, details.authToken);

            details.custReqId = mobilityTask.customerRequestId(); // add that here becuase we need it
            logger.info(`mobility-task-service._checkIn::checking in ${details.taskId} status ${mobilityTask.status()}`);
            return aahelp2Service.endCarHire(mobilityTask.rental(), details.authToken);
        })
        .then((endCarHireResponse) => {
            details.insuranceOption = endCarHireResponse.insuranceOption;
            logger.info(`mobility-task-service._checkIn::checking in ${details.taskId} doing coopers`);
            return aahelp2Service.completeTask(details.mobilityTask, 'RR', details.authToken);
        })
        .then(() => {
            // trigger notification for allocation request notification only for TPS
            const rental = details.mobilityTask.rental();
            if (['AA', 'RAF', 'OUV', 'OU', 'L460'].includes(rental.hireVehicle().supplierTypeCode())) {
                return Promise.resolve();
            }

            const payload = {
                date: new Date().getTime(),
                type: notificationTypes.TPS_VEHICLE_CHECKED_IN,
                taskId: details.taskId
            };

            const dropOffLocation = rental.dropOffLocation();
            const region = dropOffLocation && dropOffLocation.regionCode() ? dropOffLocation.regionCode() : null;

            if (region) {
                payload.region = region;
            }

            // if TPS create notification
            return notificationService.update(payload);
        })
        .then(() => {
            return aahelp2Service.closeNonApprovedOpenExtensions(details.taskId, details.authToken);
        })
        .then(() => {
            details.mobilityTask.rental().checkinReport(details.report);
            details.mobilityTask.rental().checkinSignatures(details.signatures);
            _checkinQueue.push(details);
            return aahelp2Service.docRender(details);
        })
        .then(async (docName) => {
            let report = await hireDocsSvc.generateSASToken(docName);
            return {
                report
            };
        });
}

/**
 * Validate passed arrive/completion date and return msg
 * @param {Date} scheduleDate
 * @param {Date} createdDate
 * @param {Date} arrivedDate
 * @returns {Object}
 */
function _selfCheckOutDateValidation(scheduleDate, createdDate, arrivedDate) {
    let checkOutDate = {
        isValid: false,
        msg: ''
    };

    if (scheduleDate instanceof Date && !isNaN(scheduleDate)) {
        // checking date format
        if (new Date(scheduleDate).getTime() < new Date().getTime()) {
            // arrive date or completion date should not be in future
            if (new Date(createdDate).getTime() < new Date(scheduleDate).getTime()) {
                // arrive date or completion date should not be smaller than task creation date
                if (arrivedDate && new Date(scheduleDate).getTime() < new Date(arrivedDate).getTime()) {
                    // completion date should be bigger than arrive date
                    checkOutDate.msg = 'datetime cannot be smaller than breakdown datetime.';
                } else {
                    checkOutDate.isValid = true;
                    checkOutDate.msg = 'All checks passed!';
                }
            } else {
                checkOutDate.msg = 'datetime cannot be smaller than breakdown datetime.';
            }
        } else {
            checkOutDate.msg = 'datetime cannot be greater than current datetime.';
        }
    } else {
        checkOutDate.msg = 'is not a valid date format.';
    }
    return checkOutDate;
}

function _fetchSupplierFromTask(taskId, searchType, authToken) {
    let hirerNetworkId;
    return aahelp2Service
        .readTask(taskId, authToken)
        .then((parentTask) => {
            hirerNetworkId = parentTask.entitlement().customerGroup().hirerNetworkId();
            return supplierSvc.getEnterpriseSupplier(hirerNetworkId, searchType, authToken);
        })
        .then((supplier) => {
            let hireSupplier = {
                transactionId: taskId,
                tradingPartnerId: supplier.supExtRef1,
                officeId: supplier.supExtRef3
            };
            return hireSupplier;
        })
        .catch((err) => {
            logger.error(`supplier-service:: unable to fetch supplier for hirerNetworkId ${hirerNetworkId} :: ${err.message}`);
            return Q.reject({
                msg: `Unable to fetch supplier for hirerNetworkId ${hirerNetworkId} :: ${err.message}`
            });
        });
}

function _isPNC(task) {
    return task.rental().hireVehicle().supplierTypeCode() === 'PNC';
}

function extendWorker(taskId, endDate, authToken, noSevenAM = false, extensionId = '', taskIdToUpdateSapWith) {
    let _mobilityTask;
    // all extensions got to 7am this needs to be local time ...
    if (!noSevenAM) {
        endDate.setHours(7);
        endDate.setMinutes(0);
        endDate.setSeconds(0);
        endDate.setMilliseconds(0);
    }
    return _read(taskId, authToken)
        .then((mobilityTask) => {
            if (!['HIRE', 'GARR', 'ARVD'].includes(mobilityTask.status())) {
                return Q.reject(
                    new TaskWriteResponse({
                        primeMsg: `Can't extend hire : status ${mobilityTask.status()}`,
                        sequence: mobilityTask.sequence(),
                        result: -1
                    })
                );
            }
            _mobilityTask = mobilityTask;
            let period = (endDate.getTime() - _mobilityTask.schedule().arrive().getTime()) / (60 * 1000);
            _mobilityTask.fault().repairMinutes(Math.round(period));
            logger.info(`mobility-task.service.extend :: ${taskId} repairMinutes ${Math.round(period)}`);
        })
        .then(() => {
            logger.info(`mobility-task.service.extend :: ${_mobilityTask.tag()}:: extension endDate is ${endDate}`);
            if (
                _mobilityTask.rental().isSelfInsured() ||
                (_mobilityTask.rental().isThirdPartyHireSet() && _mobilityTask.rental().thirdPartyHire().isSelfCheckout()) ||
                !['HIRE', 'GARR', 'ARVD'].includes(_mobilityTask.status())
            ) {
                return enterpriseMobilitySvc.extendCarHire(_mobilityTask, endDate);
            } else {
                /* pushes message to queue later this will subscribed by car-hire-insurance to extend a started appointment's expected finish,can also be used to shorten as well. */
                if (taskIdToUpdateSapWith === _mobilityTask.id()) {
                    return aahelp2Service.addExtendHireToQueue(_mobilityTask, endDate);
                }
            }
        })
        .then(() => {
            return aahelp2Service.writeTask(_mobilityTask, authToken);
        })
        .then((primeResp) => {
            logger.info(`mobility-task.service.extend.addExtendHireToSAPQueue :: ${_mobilityTask.tag()}:: extension endDate is ${endDate}`);
            if (taskIdToUpdateSapWith === _mobilityTask.id()) {
                aahelp2Service.addExtendHireToSAPQueue(_mobilityTask, endDate, extensionId);
            }
            return primeResp;
        });
}

function _sendSwapEmail(taskDetailsResp, authToken, taskArrFinal) {
    let mobilityTask = taskDetailsResp;
    let mailAddress, ParentTasksOnHire;

    if (mobilityTask.entitlement().customerGroup().code() === 'JAG' || 'JAGA' || 'LAND' || 'LANE') {
        mailAddress = process.env.jlrEmail;
    }
    if (mobilityTask.entitlement().customerGroup().code() === 'POR') {
        mailAddress = process.env.porscheEmail;
    }
    if (mobilityTask.entitlement().customerGroup().code() === 'HYU') {
        mailAddress = process.env.hyundaiEmail;
    }

    ParentTasksOnHire = taskArrFinal.toString();

    const data = {
        customerGroup: mobilityTask.entitlement().customerGroup().code(),
        emailAddress: mailAddress,
        childLocation: [mobilityTask.rental().repairLocation().name(), mobilityTask.rental().repairLocation().address()].join(', '),
        customerReqId: mobilityTask.customerRequestId(),
        childTaskId: mobilityTask.id(),
        parentTaskId: ParentTasksOnHire,
        hireVehicleRegNo: mobilityTask.rental().hireVehicle().regNo(),
        vorRegNo: mobilityTask.vehicle().registration(),
        contactName: mobilityTask.contact().name()
    };
    logger.info(`mobility-task-service.sendSwapEmail :: sending email when 2 HIRE with Same CustReqId ${JSON.stringify(data)}`);
    aahelp2Service.sendEmail(data, aahelp2UrlsConstant.SEND_SWAP_CHECKOUT_EMAIL, authToken);
}

module.exports = {
    /**
     * write a mobility task. It writes to both aahelp as well as mobility schema. If task is to be written the first
     * time after a 'clone' the system is calling addTask function
     * @param  {MobilityTask} mobilityTask
     * @param  {string} authToken    jwt token
     * @return {Promise}              on sucess return TaskWriteResponse
     */
    write: (mobilityTask, authToken) => {
        if (mobilityTask.indicators().incidentManaged()) {
            mobilityTask.indicators().incidentManaged(false);
        }

        if (mobilityTask.status() === 'CHCK' && mobilityTask.indicators().authorised() && mobilityTask.rental().isThirdPartyHireSet() && mobilityTask.rental().insurance().isAuthorised()) {
            //mobilityTask.status('GDET');
            mobilityTask.indicators().tel(true);
        }
        return _write(mobilityTask, authToken);
        //            .then((primeResponse) => _resource2Head(primeResponse, mobilityTask, authToken));
    },

    /**
     * read mobility task hireScheduleReq
     * @param {number} id of that task to read
     * @param {string} authToken string jwt token
     * @return {Promise}    on success return MobilityTask
     */
    read: (id, authToken) => {
        return _read(id, authToken).then((task) => {
            return hireDocsSvc.setAzureLinks(task, authToken);
        });
    },

    /**
     * read rental task details
     * @param {number} id of that task to read
     * @param {Object} authToken string jwt token
     * @return {Promise}    on success return CarRental
     */
    readRental: rentalDataSvc.read,

    cloneAndSplit: (hireScheduleReq, authToken) => {
        let _parentTask = null,
            _hireTask = null;
        return aahelp2Service
            .readTask(hireScheduleReq.taskId, authToken)
            .then((parentTask) => {
                _parentTask = parentTask;

                return aahelp2Service.cloneTask(parentTask, createReasonFactory.carHire(), authToken);
            })
            .then((hireTask) => {
                _hireTask = hireTask;

                if (hireScheduleReq.mainDriver) {
                    hireTask.rental().mainDriver(hireScheduleReq.mainDriver);
                }

                hireTask.rental().additionalDrivers(hireScheduleReq.additionalDrivers || []);

                return _write(_hireTask, authToken); //aahelp2Service.addTask(_hireTask, _parentTask, authToken);
            })
            .then((taskWriteResp) => {
                return taskWriteResp;
            });
    },

    clone: (params) => {
        const { taskId, createReason, authToken } = params;
        return aahelp2Service.readTask(taskId, authToken).then((parentTask) => {
            logger.info(`mobility-task-service.clone ${parentTask.tag()} reading rss`);
            logger.info(`mobility-task-service.clone ${parentTask.tag()} ready to clone cr ${createReason.id()}`);
            return aahelp2Service.cloneTask(parentTask, createReason, authToken);
        });
    },

    createX: (hireScheduleReq, authToken) => {
        let _mobilityTask = null;

        logger.info(`mobility-task-service.create ${_mobilityTask.tag()} task cloned`);

        _mobilityTask.status('HOLD'); // wonder if this will work ...
        _mobilityTask.rental().mainDriver(hireScheduleReq.mainDriver);
        if (Array.isArray(hireScheduleReq.additionalDrivers)) {
            hireScheduleReq.additionalDrivers.forEach((driver, idx) => {
                // create artifitial -ve ids
                driver.id(-idx);
                // indicates that entry has not been run against the insurer
                // while alos allowing driver ui to identify the entries...
            });
            _mobilityTask.rental().additionalDrivers(hireScheduleReq.additionalDrivers);
        }

        if (hireScheduleReq.fleetType === generalConstant.HIRE_TYPES.ENTERPRISE.toLowerCase()) {
            logger.info(`mobility-task-service.create ${_mobilityTask.tag()} set capabilities `);
            // set capabilities ...
            _mobilityTask.fault().capabilities([
                new RefId({
                    id: 90,
                    name: 'thirdparty'
                })
            ]);

            // Add additionalDetails and location details into rental object coming from evalite
            _mobilityTask.rental().thirdPartyHire(new SubHire());
            _mobilityTask.rental().thirdPartyHire().authorityCode(_mobilityTask.id());
            _mobilityTask.rental().hireVehicle().supplierTypeCode('ENTERPRISE');
            _mobilityTask.rental().thirdPartyHire().hireVehicle().supplierTypeCode('ENTERPRISE'); //TODO:isEnterpise true

            logger.info(`mobility-task-service.create ${_mobilityTask.tag()} setting rental params`);
            if (hireScheduleReq.patrolId) {
                _mobilityTask.operatorId(hireScheduleReq.patrolId);
            }
            if (hireScheduleReq.additionalDetails) {
                _mobilityTask.rental().additionalDetails(hireScheduleReq.additionalDetails);
            }
            if (hireScheduleReq.pickupLocation) {
                /* isEnterpriseBranch : For Evalite, we are getting supplierId for Enterprise locations.
                                        For other locations getting 0, null or undefined as supplierId
                */
                let location = hireScheduleReq.pickupLocation;
                let supplierIds = [0, null, undefined];
                let collectLocation = {
                    id: location.supplierId ? location.supplierId : null,
                    address: location.area ? location.area : null,
                    location: {
                        latitude: location.latLong ? (location.latLong.lat ? location.latLong.lat : null) : null,
                        longitude: location.latLong ? (location.latLong.long ? location.latLong.long : null) : null
                    },
                    region: location.location ? location.location : null,
                    regionCode: location.postalCode ? location.postalCode : null,
                    isEnterpriseBranch: supplierIds.includes(location.supplierId) ? false : true
                };
                _mobilityTask.rental().collectLocation(new HireSite(collectLocation));
            }
        }

        return _write(_mobilityTask, authToken)
            .then((taskWriteResp) => {
                _mobilityTask.sequence(taskWriteResp.sequence());
                _mobilityTask.schedule(taskWriteResp.schedule());
                _mobilityTask.appointment(taskWriteResp.appointment());
                _mobilityTask.indicators(taskWriteResp.indicators());
                _mobilityTask.jobNoToday(taskWriteResp.jobNoToday());
                logger.info(`mobility-task-service.create ${_mobilityTask.tag()} updated ..`);
                return {
                    response: {
                        taskId: _mobilityTask.id(),
                        status: taskWriteResp.status(),
                        msg: taskWriteResp.primeMsg(),
                        result: taskWriteResp.result()
                    },
                    mobilityTask: _mobilityTask
                };
            })
            .catch((err) => {
                logger.error('mobility-task-service.create : error ' + JSON.stringify(err));
                Q.reject(err);
            });
    },
    /**
     * create new mobility hire task
     * @param  {Object} hireScheduleReq
     * @param {number} hireScheduleReq.taskId source task id
     * @param {CarHireDriver} hireScheduleReq.mainDriver identifies main driver
     * @param {Array.<CarHireDriver>} hireScheduleReq.additionalDrivers list of addtional drivers
     * @param {number} hireScheduleReq.patrolId itenify patrol who requests task to be created
     * @param  {string} authToken [description]=
     * @return {Promise<Object>} [description]
     */
    create: (hireScheduleReq, authToken) => {
        const createReason = createReasonFactory.carHire();
        let _mobilityTask = null;
        return aahelp2Service
            .readTask(hireScheduleReq.taskId, authToken)
            .then((parentTask) => {
                logger.info(`mobility-task-service.create ${parentTask.tag()} reading rss`);
                logger.info(`mobility-task-service.create ${parentTask.tag()} ready to clone cr ${createReason.id()}`);
                return aahelp2Service.cloneTask(parentTask, createReason, authToken);
            })
            .then((mobilityTask) => {
                _mobilityTask = mobilityTask;
                logger.info(`mobility-task-service.create ${_mobilityTask.tag()} task cloned`);
                _mobilityTask.status('HOLD'); // wonder if this will work ...
                _mobilityTask.rental().mainDriver(hireScheduleReq.mainDriver);
                if (Array.isArray(hireScheduleReq.additionalDrivers)) {
                    hireScheduleReq.additionalDrivers.forEach((driver, idx) => {
                        // create artifitial -ve ids
                        driver.id(-idx);
                        // indicates that entry has not been run against the insurer
                        // while alos allowing driver ui to identify the entries...
                    });
                    _mobilityTask.rental().additionalDrivers(hireScheduleReq.additionalDrivers);
                }

                if (hireScheduleReq.fleetType === generalConstant.HIRE_TYPES.ENTERPRISE.toLowerCase()) {
                    logger.info(`mobility-task-service.create ${_mobilityTask.tag()} set capabilities `);
                    // set capabilities ...
                    _mobilityTask.fault().capabilities([
                        new RefId({
                            id: 90,
                            name: 'thirdparty'
                        })
                    ]);

                    // Add additionalDetails and location details into rental object coming from evalite
                    _mobilityTask.rental().thirdPartyHire(new SubHire());
                    _mobilityTask.rental().thirdPartyHire().authorityCode(mobilityTask.id());
                    _mobilityTask.rental().hireVehicle().supplierTypeCode('ENTERPRISE');
                    _mobilityTask.rental().thirdPartyHire().hireVehicle().supplierTypeCode('ENTERPRISE'); //TODO:isEnterpise true

                    logger.info(`mobility-task-service.create ${_mobilityTask.tag()} setting rental params`);
                    if (hireScheduleReq.patrolId) {
                        _mobilityTask.operatorId(hireScheduleReq.patrolId);
                    }
                    if (hireScheduleReq.additionalDetails) {
                        _mobilityTask.rental().additionalDetails(hireScheduleReq.additionalDetails);
                    }
                    if (hireScheduleReq.pickupLocation) {
                        /* isEnterpriseBranch : For Evalite, we are getting supplierId for Enterprise locations.
                                                For other locations getting 0, null or undefined as supplierId
                        */
                        let location = hireScheduleReq.pickupLocation;
                        let supplierIds = [0, null, undefined];
                        let collectLocation = {
                            id: location.supplierId ? location.supplierId : null,
                            address: location.area ? location.area : null,
                            location: {
                                latitude: location.latLong ? (location.latLong.lat ? location.latLong.lat : null) : null,
                                longitude: location.latLong ? (location.latLong.long ? location.latLong.long : null) : null
                            },
                            region: location.location ? location.location : null,
                            regionCode: location.postalCode ? location.postalCode : null,
                            isEnterpriseBranch: supplierIds.includes(location.supplierId) ? false : true
                        };
                        _mobilityTask.rental().collectLocation(new HireSite(collectLocation));
                    }
                }

                return _write(_mobilityTask, authToken);
            })
            .then((taskWriteResp) => {
                _mobilityTask.sequence(taskWriteResp.sequence());
                _mobilityTask.schedule(taskWriteResp.schedule());
                _mobilityTask.appointment(taskWriteResp.appointment());
                _mobilityTask.indicators(taskWriteResp.indicators());
                _mobilityTask.jobNoToday(taskWriteResp.jobNoToday());
                logger.info(`mobility-task-service.create ${_mobilityTask.tag()} updated ..`);
                return {
                    response: {
                        taskId: _mobilityTask.id(),
                        status: taskWriteResp.status(),
                        msg: taskWriteResp.primeMsg(),
                        result: taskWriteResp.result()
                    },
                    mobilityTask: _mobilityTask
                };
            })
            .catch((err) => {
                logger.error('mobility-task-service.create : error ' + JSON.stringify(err));
                Q.reject(err);
            });
    },
    /**
     * schedule
     * @param  {HireScheduleRequest} hireScheduleReq contains details of the hire
     * @param  {Object} authToken
     * @return {Promise}           on success return
     */
    schedule: (hireScheduleReq, authToken) => {
        let _primeResponse = null,
            _mobilityTask = null,
            _insurerScope = null;

        //set authorised indicator to false everytime you schedule hire car
        hireScheduleReq.mobilityTask().indicators().authorised(false);

        // keep track
        _mobilityTask = hireScheduleReq.mobilityTask();

        if (_mobilityTask.rental().hireVehicle().regNo()) {
            taskAuditSvc.write(_mobilityTask.id(), _mobilityTask.customerRequestId(), `Removing insurance on ${_mobilityTask.rental().hireVehicle().regNo()}`, authToken);
        }

        logger.info(`mobility-task-service.schedule ${_mobilityTask.tag()} scheduling request ${hireScheduleReq.searchType()}`);

        return aahelp2Service
            .deleteCover(_mobilityTask, authToken)
            .then((delMobilityTask) => {
                return appointmentChangeSvc.taskAppointmentChanged(delMobilityTask, authToken);
            })
            .then((task) => {
                _mobilityTask = task;

                // make sure incident managment indicator is false .. .
                incidentManaged.check(_mobilityTask);

                switch (hireScheduleReq.searchType()) {
                    case HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY:
                    case HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_THRIFTY:
                    case HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE:
                    case HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_LCH:
                    case HireScheduleRequest.SEARCH_TYPES.PNC:
                        thirdPartySupplierSvc.setResource(hireScheduleReq);
                        break;
                    case HireScheduleRequest.SEARCH_TYPES.VEHICLE:
                        if (_mobilityTask.rental().insurance().id()) {
                            //If the hire request dont have insurance we cannot elevate the status, as Prime is doing +1
                            _mobilityTask.status('PLAN');
                        }
                        logger.info(
                            `mobility-task-service.schedule :: vehicle schedule current ${_mobilityTask.id()}: EXID ${_mobilityTask.appointment().exclusiveResourceId()}: RESID resid-${_mobilityTask
                                .schedule()
                                .resource()
                                .id()}:${_mobilityTask.status()}`
                        );

                        _mobilityTask.schedule().resource(
                            new Resource({
                                id: _mobilityTask.appointment().exclusiveResourceId()
                            })
                        );
                        // todo : the following statement 'breaks' the OUV allocation
                        // the code depends on the UI to post the 'correct' capabilities ...
                        // however I am not sure why I cleared it
                        //_mobilityTask.fault().capabilities([]);
                        _mobilityTask.indicators().incidentManaged(false);

                        logger.info(
                            `mobility-task-service.schedule :: vehicle schedule req ${_mobilityTask.id()}: EXID ${_mobilityTask.appointment().exclusiveResourceId()}: RESID ${_mobilityTask
                                .schedule()
                                .resource()
                                .id()}:${_mobilityTask.status()}`
                        );
                        thirdPartySupplierSvc.removeCapability(hireScheduleReq);

                        _mobilityTask.indicators().incidentManaged(false);
                        _mobilityTask.schedule().resource(
                            new Resource({
                                id: _mobilityTask.appointment().exclusiveResourceId()
                            })
                        );

                        _mobilityTask.rental().dropOffLocation(new HireSite());
                        _mobilityTask.rental().repairLocation(new HireSite());
                        _mobilityTask.recovery().destResourceId(null);

                        break;
                    case HireScheduleRequest.SEARCH_TYPES.LOCATION:
                        _mobilityTask.status('UNAC'); // change status in UNAC so prime searches for a resource
                        // reset properties that infiuence scheduling
                        _mobilityTask.appointment().exclusiveResourceId(null);
                        _mobilityTask.schedule().resource(new Resource());

                        _mobilityTask.indicators().incidentManaged(false);

                        thirdPartySupplierSvc.removeCapability(hireScheduleReq);
                        break;
                    case HireScheduleRequest.SEARCH_TYPES.TIME:
                        break;
                    default:
                        break;
                }

                logger.info(`mobility-task-service.schedule {$_mobilityTask.tag()} ready to schedule `);

                if ([HireScheduleRequest.SEARCH_TYPES.PNC, HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE].includes(hireScheduleReq.searchType())) {
                    return _setEnterpriseResource(_mobilityTask, hireScheduleReq.searchType(), authToken);
                } else {
                    return aahelp2Service.writeTask(_mobilityTask, authToken);
                }
            })
            .then((primeResponse) => {
                _primeResponse = primeResponse;

                logger.info(
                    `mobility-task-service.schedule :: prime resp ${_mobilityTask.id()}: RESID ${_primeResponse.schedule().resource().id()}:${_primeResponse.status()} ${_primeResponse
                        .indicators()
                        .tag()}: primeMsg ${_primeResponse.primeMsg()}`
                );

                if (_mobilityTask.rental().isThirdPartyHireSet()) {
                    if (_primeResponse.schedule().resource().id() !== _mobilityTask.rental().thirdPartyHire().logicalResourceId()) {
                        let isEnterpiseOrPNC = [HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE, HireScheduleRequest.SEARCH_TYPES.PNC].includes(hireScheduleReq.searchType());
                        return Q.resolve({
                            hireVehicle: isEnterpiseOrPNC ? _mobilityTask.rental().thirdPartyHire().hireVehicle() : cshResource.getFailedResource()
                        });
                    }
                    _mobilityTask.rental().thirdPartyHire().hireVehicle().id(primeResponse.schedule().resource().id());
                    // for third party we know the vehicle
                    return Q.resolve({
                        hireVehicle: _mobilityTask.rental().thirdPartyHire().hireVehicle()
                    });
                }

                logger.info(`mobility-task-service.schedule ${_mobilityTask.tag()} searching for resource ${primeResponse.schedule().resource().id()}`);
                return cshResource.getVehicle({
                    resourceId: primeResponse.schedule().resource().id(),
                    managerId: primeResponse.schedule().resource().managerId(),
                    authToken: authToken,
                    rental: _mobilityTask.rental(),
                    supResourceId: _primeResponse.schedule().resource().managerId()
                });
            })
            .then((cshVehicleScope) => {
                let isOU;

                if (_mobilityTask.rental().hireVehicle().supplierTypeCode() !== generalConstant.HIRE_TYPES.ENTERPRISE) {
                    _mobilityTask.rental().hireVehicle(cshVehicleScope.hireVehicle);
                }

                if (_mobilityTask.rental().isThirdPartyHireSet()) {
                    // ui has set this already ... so skip ..
                    return Q.resolve(_mobilityTask.rental().collectLocation());
                }

                // look for OU
                isOU = _.find(_primeResponse.schedule().resource().capabilities(), (item) => {
                    return item.id() === Capabilities.CAP_MANUAL_ALLOCATION.id;
                });

                if (isOU) {
                    _mobilityTask.rental().hireVehicle().supplierTypeCode('OU');
                }

                // look for L460
                let isL460 = _.find(_primeResponse.schedule().resource().capabilities(), (item) => {
                    return item.id() === Capabilities.CAP_L460.id;
                });
                if (isL460) {
                    _mobilityTask.rental().hireVehicle().supplierTypeCode('L460');
                }

                if (_mobilityTask.rental().hireVehicle().regNo()) {
                    _primeResponse.appointment().exclusiveResourceId(cshVehicleScope.hireVehicle.id());
                    taskAuditSvc.write(_mobilityTask.id(), _mobilityTask.customerRequestId(), `Reserving vehicle ${_mobilityTask.rental().hireVehicle().regNo()}`, authToken);
                }

                logger.info(`mobility-task-service.schedule :: task ${_mobilityTask.id()} searching for manager id ${_primeResponse.schedule().resource().managerId()}`);
                return cshSupplier.read(_primeResponse.schedule().resource().managerId());
            })
            .then((cshSupplierScope) => {
                if (!_mobilityTask.rental().collectLocation().isSet()) {
                    _mobilityTask.rental().collectLocation(cshSupplierScope);
                }

                if (!_mobilityTask.rental().dropOffLocation().isSet()) {
                    _mobilityTask.rental().dropOffLocation(cshSupplierScope);
                }

                if (!_mobilityTask.rental().isThirdPartyHireSet() && !_mobilityTask.rental().repairLocation().isSet()) {
                    _mobilityTask.rental().repairLocation(cshSupplierScope);
                }

                if (!_mobilityTask.recovery().isDestResourceIdSet()) {
                    _mobilityTask.recovery().destResourceId(cshSupplierScope.id());
                }

                if (_mobilityTask.rental().hireVehicle().id()) {
                    _mobilityTask.schedule().arrive(_primeResponse.schedule().arrive());
                    _mobilityTask.schedule().complete(_primeResponse.schedule().complete());
                } else {
                    _mobilityTask.schedule().arrive(_mobilityTask.appointment().earliest());
                }

                return aahelp2Service.bookHireCar(_mobilityTask, authToken);
            })
            .then((insurerScope) => {
                _insurerScope = insurerScope;
                if (hireScheduleReq.searchType() === HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE && !hireScheduleReq.mobilityTask().rental().thirdPartyHire().hireVehicleRef()) {
                    logger.info(`mobility-task-service.schedule ${_mobilityTask.tag()} arms request `);
                    return enterpriseHireService
                        .rentalReservationRequest(hireScheduleReq, authToken)
                        .then((updatedHireScheduleRequest) => {
                            const { thirdPartyHire } = updatedHireScheduleRequest.mobilityTask;
                            logger.info(`mobility-task-service.schedule ${_mobilityTask.tag()} arms repose ${thirdPartyHire.rentalConfirmationNumber} `);
                            if (thirdPartyHire) {
                                _mobilityTask.rental().thirdPartyHire(new EnterpriseSubHire(_mobilityTask.rental().thirdPartyHire().toJSON()));
                                _mobilityTask.rental().thirdPartyHire().isEnterprise(thirdPartyHire.isEnterprise);
                                _mobilityTask.rental().thirdPartyHire().logicalResourceId(thirdPartyHire.logicalResourceId);
                                _mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber(thirdPartyHire.rentalConfirmationNumber);
                            }
                        })
                        .catch((err) => {
                            logger.error(`mobility-task-service.schedule ${_mobilityTask.tag()} arms request failed`);
                            logger.error(`mobility-task-service.schedule ${_mobilityTask.id()} error`, err);
                            return Q.reject({
                                errors: [],
                                id: 8008,
                                msg: 'failed to communicate with Enterprise'
                            });
                        });
                }

                return Q.resolve(insurerScope);
            })
            .then((insurerScope) => {
                // persist insurance details ..
                if (insurerScope && insurerScope.id > 0) {
                    // this assumes that we are doing coopers ...
                    _mobilityTask.rental().insurance().id(insurerScope.id);
                    _mobilityTask.rental().insurance().insuredVehicleId(insurerScope.stockId);
                }
                const isEnterpiseOrPNC = _mobilityTask.rental().isThirdPartyHireSet() && (_mobilityTask.rental().thirdPartyHire().isEnterprise() || _isPNC(_mobilityTask));
                if (_mobilityTask.rental().hireVehicle().regNo() && _mobilityTask.rental().isThirdPartyHireSet() && !isEnterpiseOrPNC) {
                    taskAuditSvc.write(_mobilityTask.id(), _mobilityTask.customerRequestId(), `Booked insurance on vehicle ${_mobilityTask.rental().hireVehicle().regNo()}`, authToken);
                }

                return rentalDataSvc.write(_mobilityTask, authToken);
            })
            .then(() => {
                let msg;

                if (_mobilityTask.rental().hireVehicle().regNo()) {
                    msg = `<b>${_mobilityTask.rental().hireVehicle().supplierTypeCode()} vehicle ${_mobilityTask.rental().hireVehicle().regNo()} reserved</b><br>`;
                } else if (_mobilityTask.rental().thirdPartyHire() && _mobilityTask.rental().thirdPartyHire().hireVehicleRef && _mobilityTask.rental().thirdPartyHire().hireVehicleRef()) {
                    msg = `<b>${_mobilityTask.rental().hireVehicle().supplierTypeCode()} Rental created : ${_mobilityTask.rental().thirdPartyHire().hireVehicleRef()}</b><br>`;
                } else if (
                    _mobilityTask.rental().thirdPartyHire() &&
                    _mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber &&
                    _mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber()
                ) {
                    msg = `<b>Enterprise Rental confirmed : ${_mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber()}</b><br>`;
                } else {
                    msg = `<b>unable to schedule a vehicle</b><br>`;
                }

                _primeResponse.primeMsg(`${msg} ${_primeResponse.primeMsg() || ''}`);

                if (
                    (hireScheduleReq.searchType() === HireScheduleRequest.SEARCH_TYPES.LOCATION || hireScheduleReq.searchType() === HireScheduleRequest.SEARCH_TYPES.VEHICLE) &&
                    _primeResponse.schedule().resource().id() > 0
                ) {
                    _primeResponse.appointment().exclusiveResourceId(_primeResponse.schedule().resource().id());
                    logger.info(
                        `mobility-task-service.schedule :: force excl res to appointment ${_mobilityTask.id()}: RESID ${_primeResponse
                            .appointment()
                            .exclusiveResourceId()}:${_primeResponse.status()}:${_primeResponse.primeMsg()}`
                    );
                }

                return {
                    taskWriteResponse: _primeResponse,
                    rental: _mobilityTask.rental()
                };
            })
            .catch((err) => {
                if (err.errors || err.id === 1008 || err.id === 8008) {
                    // coopers error ...or enterprise error
                    const errMsg = InsurerErrorsFactory.hasError(err.rootCause) || err.msg;
                    logger.info(`mobility-task-service.schedule  ${_mobilityTask.tag()} 3rd party error ${errMsg}`);
                    _mobilityTask.sequence(_primeResponse.sequence()); // make sure we are at the same number ..
                    _mobilityTask.status('HOLD');
                    _mobilityTask.appointment().exclusiveResourceId(null);
                    _mobilityTask.indicators().authorised(false);
                    _mobilityTask.rental().hireVehicle(new RafVehicle()); // reset hire vehicle as we no longer have it ...
                    return aahelp2Service
                        .writeTask(_mobilityTask, authToken)
                        .then((primeResponse) => {
                            const pMsg = `<b>Insurer msg:</b> ${errMsg} <br><b>Prime msg:</b> ${primeResponse.primeMsg()}`;
                            logger.info(`mobility-task-service.schedule :: task ${_mobilityTask.id()} prime resp ${pMsg}`);
                            primeResponse.primeMsg(pMsg);
                            return Q.resolve({
                                taskWriteResponse: primeResponse, // keeps ctrl simpler ..
                                rental: _mobilityTask.rental()
                            });
                        })
                        .catch((err) => {
                            logger.error(`mobility-task-service.schedule :: task ${_mobilityTask.id()} failed to unac task`, err);
                            return Q.reject(errors.create(2005, 'scheduling request failed', err));
                        });
                }
                logger.error(`mobility-task-service.schedule :: task ${_mobilityTask.id()} prime resp`, err);
                return Q.reject(err);
            });
    },
    /*resource: (details) => {
        return vehicleRepo.find(details.primeResponse.schedule().resource().id(), details.authToken)
            .then((rafVehicle) => {
                details.vehicle = rafVehicle;
                return details;
            });
    },*/

    /**
     * set the insurance type to be MobilityInsured ...
     * @param  {Object} details
     * @param {Number} details.taskId identifies task we want to get the insurance options for
     * @return {Promise}
     */
    /*    insuranceType: (details) => {
            return _read(details.taskId, details.authToken)
                .then((mobilityTask) => {
                    mobilityTask.rental().insurance(details.insurance);
                    return rentalDataSvc.writeRental(details.taskId, mobilityTask.rental());
                });
        },
    */
    deallocateResource: (hireScheduleReq, authToken) => {
        let _mobilityTask = null;
        let _primeResponse = null;

        return aahelp2Service
            .getCoverDetails(hireScheduleReq.mobilityTask(), authToken)
            .then((response) => {
                let contractId = _.get(response, 'data.contractDetails.id', null);
                let rental = _.isFunction(response.rental) ? response.rental() : null;

                if (rental && rental.isSelfInsured()) {
                    logger.info(`mobility-task-service.deallocateResource :: rental is SelfInsured`);
                    return Q.resolve(response);
                }
                if (!contractId) {
                    logger.error(`mobility-task-service.deallocateResource :: Deallocatopn aborted... No contract found for given task`);
                    return Q.reject({
                        msg: `Deallocatopn aborted... No contract found for given task`
                    });
                }
                logger.info(`mobility-task-service.deallocateResource :: Contract found ${contractId}`);
                return aahelp2Service.deleteCover(hireScheduleReq.mobilityTask(), authToken);
            })
            .then((delMobilityTask) => {
                _mobilityTask = delMobilityTask;
                // reset details hire details ..
                _mobilityTask.rental().collectLocation(new HireSite());
                _mobilityTask.rental().dropOffLocation(new HireSite());
                _mobilityTask.rental().repairLocation(new HireSite());

                _mobilityTask.rental().hireVehicle(new RafVehicle());

                _mobilityTask.status('HOLD');

                // we need to clear the resource
                _mobilityTask.schedule().resource(new Resource());
                _mobilityTask.appointment().exclusiveResourceId(null);

                return _write(_mobilityTask, authToken);
            })
            .then((primeResponse) => {
                _primeResponse = primeResponse;
                logger.info(`mobility-task-service.deallocateResource ${hireScheduleReq.mobilityTask().tag()} updated mb_hire`);
                return {
                    taskWriteResponse: _primeResponse,
                    rental: _mobilityTask.rental()
                };
            });
    },

    selfInsured: (details) => {
        details.mobilityTask = details.task;
        return Q.when(details.task.rental().insurance().id() ? aahelp2Service.deleteCover(details.task, details.authToken) : details.task)
            .then(() => {
                details.insurance.id(details.mobilityTask.id());
                details.mobilityTask.rental().insurance(details.insurance);
                details.mobilityTask.rental().mainDriver(details.driverDetails);
                details.mobilityTask.rental().mainDriver().id(details.task.id());
                details.mobilityTask.rental().additionalDrivers([]); // more addtional drivers ..
                return rentalDataSvc.write(details.mobilityTask);
            })
            .then(() => {
                const primeResponse = new TaskWriteResponse({
                    primeMsg: '<b>Self insurance appointment confirmed</b>'
                });

                taskAuditSvc.write(
                    details.mobilityTask.id(),
                    details.mobilityTask.customerRequestId(),
                    `Self insuring vehicle ${details.mobilityTask.rental().hireVehicle().regNo()}`,
                    details.authToken
                );
                primeResponse.capabilities(details.mobilityTask.fault().capabilities());
                primeResponse.schedule(details.mobilityTask.schedule());
                primeResponse.appointment(details.mobilityTask.appointment());
                primeResponse.status(details.mobilityTask.status());
                primeResponse.sequence(details.mobilityTask.sequence());
                primeResponse.indicators(details.mobilityTask.indicators());
                return {
                    taskWriteResponse: primeResponse,
                    rental: details.mobilityTask.rental()
                };
            });
    },

    mobilityInsurance: (details) => {
        let notChanged;
        return _read(details.taskId, details.authToken)
            .then((mobilityTask) => {
                details.mobilityTask = mobilityTask;

                notChanged = !details.mobilityTask.rental().isSelfInsured();
                if (notChanged) {
                    return Q.resolve();
                }
                const existingSupNetworkCode = details.mobilityTask.rental().insurance().supNetworkCode();
                details.mobilityTask.rental().insurance(new MobilityInsurance());
                details.mobilityTask.rental().insurance().supNetworkCode(existingSupNetworkCode);

                taskAuditSvc.write(
                    details.mobilityTask.id(),
                    details.mobilityTask.customerRequestId(),
                    `Removing self insurance on vehicle ${details.mobilityTask.rental().hireVehicle().regNo()}`,
                    details.authToken
                );

                return aahelp2Service.bookHireCar(details.mobilityTask, details.authToken);
            })
            .then((insurerScope) => {
                if (notChanged) {
                    return Q.resolve();
                }

                details.mobilityTask.rental().insurance().id(insurerScope.id);
                details.mobilityTask.rental().insurance().insuredVehicleId(insurerScope.stockId);

                // now remove any ids from the dirvers ..
                details.mobilityTask.rental().mainDriver().reset();
                details.mobilityTask.rental().mainDriver().id(-1);

                details.mobilityTask
                    .rental()
                    .additionalDrivers()
                    .forEach((driver) => {
                        driver.reset();
                        driver.id(-1);
                    });
                return rentalDataSvc.write(details.mobilityTask, details.authToken);
            })
            .then(() => {
                const primeResponse = new TaskWriteResponse({
                    primeMsg: '<b>Appointment booked with the insurer</b>'
                });

                taskAuditSvc.write(
                    details.mobilityTask.id(),
                    details.mobilityTask.customerRequestId(),
                    `Booked insurance on vehicle ${details.mobilityTask.rental().hireVehicle().regNo()}`,
                    details.authToken
                );

                primeResponse.schedule(details.mobilityTask.schedule());
                primeResponse.appointment(details.mobilityTask.appointment());
                primeResponse.status(details.mobilityTask.status());
                primeResponse.sequence(details.mobilityTask.sequence());
                primeResponse.indicators(details.mobilityTask.indicators());
                return {
                    taskWriteResponse: primeResponse,
                    rental: details.mobilityTask.rental()
                };
            })
            .catch((err) => {
                if (err.errors) {
                    details.mobilityTask.status('UNAC');
                    details.mobilityTask.indicators().authorised(false);
                    return Q.allSettled([aahelp2Service.writeTask(details.mobilityTask, details.authToken), rentalDataSvc.write(details.mobilityTask, details.authToken)]).spread((taskWriteResp) => {
                        // ,repoResp is not needed so we don't have it ..
                        const msg = `<b>Appointment with insurer failed</b><br><b>Insurer msg : ${InsurerErrorsFactory.hasError(err)}</b></br>${taskWriteResp.primeMsg()}`;
                        taskWriteResp.primeMsg(msg);
                        return {
                            taskWriteResponse: taskWriteResp,
                            rental: details.mobilityTask.rental()
                        };
                    });
                }

                return Q.reject(err);
            });
    },
    saveRental: (details) => {
        return rentalDataSvc.write(details.mobilityTask);
    },

    checkOut: (details) => _checkOut(details),

    checkIn: (details) => _checkIn(details),

    /**
     * Check out selfCheckout hire task
     * @param {Object} details
     * @returns {Promise}
     */
    checkOutSelfCheckout: (details) => {
        let _mobilityTask = null;
        logger.info(`mobility-task-service.checkOutSelfCheckout::checking out ${details.taskId} `);
        return _read(details.taskId, details.authToken)
            .then((mobilityTask) => {
                let rental = mobilityTask.rental();
                _mobilityTask = mobilityTask;
                if (rental.isThirdPartyHireSet() && rental.thirdPartyHire().isSelfCheckout()) {
                    taskAuditSvc.write(mobilityTask.id(), mobilityTask.customerRequestId(), `Hire start ${mobilityTask.rental().hireVehicle().regNo()}`, details.authToken);

                    logger.info(`mobility-task-service.checkOutSelfCheckout::checking out ${details.taskId} status ${mobilityTask.status()}`);
                    if (!['HEAD', 'GDET'].includes(mobilityTask.status())) {
                        // ToDo: should below log be changed to info instead, need to confirm
                        logger.error(`mobility-task-service.checkOutSelfCheckout::checking out ${details.taskId} NOT in HEAD OR GDET`);
                        let invalidTaskStatus = {
                            msg: `Task in state of ${mobilityTask.status()} can not proceed to checkout`
                        };
                        if (mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                            invalidTaskStatus.enterpriseErrorRespnse = {
                                invalidTaskStatus: true
                            };
                        }
                        return Q.reject(invalidTaskStatus);
                    }

                    // To start SelfCheckout hire, set status to GARR
                    mobilityTask.status('GARR');

                    // Update firstHireStart first time only if not exist
                    if (!mobilityTask.rental().firstHireStart()) {
                        mobilityTask.rental().firstHireStart(new Date());
                    }

                    // Set arrive time
                    let dateValidation = _selfCheckOutDateValidation(details.arrive, mobilityTask.schedule().create());
                    if (dateValidation.isValid) {
                        mobilityTask.schedule().arrive(details.arrive);
                    } else {
                        logger.info(`mobility-task-service.checkInSelfCheckout::Invalid arrive date received. ${dateValidation.msg}. TaskId:${details.taskId}`);
                        let invalidDateError = {
                            msg: `Checkout ${dateValidation.msg}`
                        };
                        if (mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                            invalidDateError.enterpriseErrorRespnse = {
                                invalidDateTime: true
                            };
                        }
                        return Q.reject(invalidDateError);
                    }

                    logger.info(`mobility-task-service.checkOutSelfCheckout::update prime out ${details.taskId} status ${mobilityTask.status()}`);
                    return aahelp2Service.writeTask(mobilityTask, details.authToken);
                } else {
                    logger.error(`mobility-task-service.checkOutSelfCheckout::checking out ${details.taskId} thirdParty not set.`);
                    return Q.reject({
                        msg: `thirdParty details are not set on taskId:${details.taskId} or task does not belongs to SelfCheckout group like ENTERPRISE, LCH`
                    });
                }
            })
            .then((respData) => {
                //need to update with latest calculated complete time as new arrive time  sent in request.
                _mobilityTask.schedule().complete(respData.schedule().complete());
                // Update rental in csh and redis
                return rentalDataSvc.write(_mobilityTask);
            })
            .then(() => {
                // default 48hr extension entry in MB_EXTENSION for CCP
                // return _isCCPEnabled() ? ccpSvc.checkout(_mobilityTask, details.authToken) : null;
                return _swapCarCheckout(_mobilityTask, details.authToken);
            })
            .then(() => {
                _swapCheckoutMailTrigger(_mobilityTask, details.authToken);
            })
            .then(() => {
                return {
                    status: 'ok',
                    msg: `Hire started for ${details.taskId}`
                };
            })
            .catch((err) => {
                logger.info(`mobility-task-service.checkOutSelfCheckout::failed to checkout ${details.taskId}`);
                return Q.reject({
                    status: 'fail',
                    msg: `Checkout failed for taskId:${details.taskId}. ${err.msg}`,
                    err: err
                });
            });
    },

    /**
     * Check in SelfCheckout hire task
     * @param {Object} details
     * @returns {Promise}
     */
    checkInSelfCheckout: (details) => {
        logger.info(`mobility-task-service.checkInSelfCheckout::checking in ${details.taskId} `);
        return _read(details.taskId, details.authToken)
            .then((mobilityTask) => {
                let rental = mobilityTask.rental();
                if (mobilityTask.rental().isThirdPartyHireSet() && rental.thirdPartyHire().isSelfCheckout()) {
                    taskAuditSvc.write(mobilityTask.id(), mobilityTask.customerRequestId(), `Hire end ${mobilityTask.rental().hireVehicle().regNo()}`, details.authToken);

                    logger.info(`mobility-task-service.checkInSelfCheckout::checking in ${details.taskId} status ${mobilityTask.status()}`);
                    if (mobilityTask.status() !== 'GARR') {
                        logger.error(`mobility-task-service.checkInSelfCheckout::checking in ${details.taskId} NOT in GARR`);
                        let invalidTaskStatus = {
                            msg: `Task in state of ${mobilityTask.status()} can not proceed to checkin`
                        };
                        if (mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                            invalidTaskStatus.enterpriseErrorRespnse = {
                                invalidTaskStatus: true
                            };
                        }
                        return Q.reject(invalidTaskStatus);
                    } else if (mobilityTask.status() === 'COMP') {
                        // TODO: This needs to be get updated
                        // mobilityTask.rental().thirdPartyHire().rentedVehicleDetails(details.rentedVehicleDetails)
                        // rentalDataSvc.write(mobilityTask);
                    }
                    // Set complete time
                    let dateValidation = _selfCheckOutDateValidation(details.complete, mobilityTask.schedule().create(), mobilityTask.schedule().arrive());
                    if (dateValidation.isValid) {
                        mobilityTask.schedule().complete(details.complete);
                    } else {
                        logger.info(`mobility-task-service.checkInSelfCheckout::Invalid arrive date received. ${dateValidation.msg}. TaskId:${details.taskId}`);
                        let invalidDateError = {
                            msg: `Checkin ${dateValidation.msg}`
                        };
                        if (mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                            invalidDateError.enterpriseErrorRespnse = {
                                invalidDateTime: true
                            };
                        }
                        return Q.reject(invalidDateError);
                    }

                    logger.info(`mobility-task-service.checkInSelfCheckout::update prime out ${details.taskId} status ${mobilityTask.status()}`);
                    return aahelp2Service.completeTask(mobilityTask, 'RR', details.authToken).then((taskWriteResponse) => {
                        return {
                            details: details,
                            taskWriteResponse: taskWriteResponse,
                            mobilityTask: mobilityTask
                        };
                    });
                } else {
                    logger.error(`mobility-task-service.checkInSelfCheckout::checking in ${details.taskId} thirdParty not set.`);
                    return Q.reject({
                        msg: `thirdParty details are not set on taskId:${details.taskId} or task does not belongs to SelfCheckout group like ENTERPRISE, LCH`
                    });
                }
            })
            .catch((err) => {
                logger.info(`mobility-task-service.checkInSelfCheckout::failed to checkin ${details.taskId}`);
                return Q.reject({
                    status: 'fail',
                    msg: `Checkin failed for taskId:${details.taskId}. ${err.msg}`,
                    err: err
                });
            });
    },

    /**
     * cancel hire task assumes that it has not started yet ..
     * @param  {Object} details
     * @param {Number} details.taskId
     * @param {Object} details.authToken
     * @param {Number} details.cancelRentalRequestFromENT {optional}
     * @return {Promise}
     */
    cancelHireTask: (details) => {
        return _read(details.taskId, details.authToken)
            .then((mobilityTask) => {
                details.mobilityTask = mobilityTask;
                if (['ARVD', 'HIRE', 'GARR'].includes(mobilityTask.status())) {
                    let invalidStatusError = {
                        msg: 'Hire in progress can not be cancelled'
                    };
                    if (details.mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                        invalidStatusError.enterpriseErrorRespnse = {
                            invalidTaskStatus: true
                        };
                    }
                    return Q.reject(invalidStatusError);
                }
                return aahelp2Service.addDeleteCoverToQueue(mobilityTask);
            })
            .then(() => {
                logger.info(`mobility-task.service.cancelHireTask :: insurance cancled`);
                let completeCode = details.cancelRentalRequestFromENT && details.mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE ? 'EN' : '80';
                return aahelp2Service.completeTask(details.mobilityTask, completeCode, details.authToken);
            })
            .then(() => {
                logger.info(`mobility-task.service.cancelHireTask :: prime canceled`);
                return {
                    msg: 'Hire canceled successfully'
                };
            });
    },
    setInsurance: (taskId, option, authToken) => {
        let scope = {
            rental: null
        };

        return rentalDataSvc
            .read(taskId, authToken)
            .then((carRental) => {
                scope.rental = carRental;
                return aahelp2Service.setInsuranceOption(carRental, option, authToken);
            })
            .then((insuranceProp) => {
                scope.rental.insurance().insuredTerms(insuranceProp.insuredTerms);
                scope.rental.insurance().insuranceOption(insuranceProp.insuranceOption);

                return rentalDataSvc.writeRental(taskId, scope.rental);
            });
    },

    extend: (taskId, endDate, authToken, noSevenAM = false, extensionId = '') => {
        const deferred = Q.defer();
        cshMobilityTaskSvc.getHiresByCustRequestId(taskId).then((taskIds) => {
            taskIds.forEach((item) => {
                let res = extendWorker(
                    item,
                    endDate,
                    authToken,
                    noSevenAM,
                    extensionId,
                    taskId // taskIdToUpdateSapWith
                );
                if (taskId === item) {
                    deferred.resolve(res);
                }
            });
        });
        return deferred.promise;
    },
    updatePickupTime: (hireScheduleReq, authToken) => {
        const scope = {
            primeResponse: null,
            rental: hireScheduleReq.mobilityTask().rental()
        };
        return aahelp2Service
            .updatePickupTime(hireScheduleReq.mobilityTask(), authToken)
            .then(() => {
                scope.rental = hireScheduleReq.mobilityTask().rental();
                return aahelp2Service.writeTask(hireScheduleReq.mobilityTask(), authToken);
            })
            .then((taskWriteResponse) => {
                scope.taskWriteResponse = taskWriteResponse;
                scope.taskWriteResponse.appointment(hireScheduleReq.mobilityTask().appointment());
                return scope;
            });
    },
    // THIS HAS BEEN LEFT HERE TO REMMBER THE LOGIC THAT WAS HERE ..
    updatePickupTime_DONT_USE: (hireScheduleReq, authToken) => {
        const scope = {
            taskWriteResponse: null,
            rental: hireScheduleReq.mobilityTask().rental()
        };
        logger.info(
            `mobility-task-service.updatePickupTime:: ${hireScheduleReq.mobilityTask().tag()} update third party ${hireScheduleReq
                .mobilityTask()
                .appointment()
                .earliest()
                .toISOString()}-${hireScheduleReq.mobilityTask().appointment().latest().toISOString()}`
        );
        return aahelp2Service
            .updatePickupTime(hireScheduleReq.mobilityTask(), authToken)
            .then(() => {
                incidentManaged.check(hireScheduleReq.mobilityTask());
                logger.info(
                    `mobility-task-service.updatePickupTime:: ${hireScheduleReq.mobilityTask().tag()} updating prime ${hireScheduleReq
                        .mobilityTask()
                        .appointment()
                        .earliest()
                        .toISOString()}-${hireScheduleReq.mobilityTask().appointment().latest().toISOString()}`
                );
                return aahelp2Service.writeTask(hireScheduleReq.mobilityTask(), authToken);
            })
            .then((taskWriteResponse) => {
                if (taskWriteResponse.result() === 0 && taskWriteResponse.schedule().resource().id() !== hireScheduleReq.mobilityTask().schedule().resource().id()) {
                    if (hireScheduleReq.mobilityTask().rental().hireVehicle().resourceId() !== null) {
                        hireScheduleReq
                            .mobilityTask()
                            .schedule()
                            .resource(
                                new Resource({
                                    id: hireScheduleReq.mobilityTask().rental().hireVehicle().resourceId()
                                })
                            );
                    }
                    hireScheduleReq.mobilityTask().sequence(taskWriteResponse.sequence());
                    logger.info(`mobility-task-service.updatePickupTime:: ${hireScheduleReq.mobilityTask().tag()} updating prime to accept change of appointment`);

                    return aahelp2Service.writeTask(hireScheduleReq.mobilityTask(), authToken);
                }
                return taskWriteResponse;
            })
            .then((taskWriteResponse) => {
                scope.taskWriteResponse = taskWriteResponse;
                return scope;
            });
    },
    openRental: (params, body, authToken, task) => {
        const deferred = Q.defer();

        if (!body.enterpriseHire) {
            deferred.resolve();
        } else {
            const { taskId } = params,
                scope = {},
                openRentalDetails = Object.assign({}, body.rentalContractDetails, {
                    hireDatetime: body.hireDatetime
                }),
                { vehicleLicenseNumber } = openRentalDetails.rentedVehicleDetails || {},
                { hireDatetime } = openRentalDetails;

            let promise = null;
            promise = task ? Q.resolve(task) : _read(taskId, authToken);

            return promise
                .then((mobilityTask) => {
                    return AAHelpService.getVehicleByReg(vehicleLicenseNumber, authToken)
                        .then((vehicleResp) => vehicleResp.vehicle)
                        .then((vehicle) => {
                            scope.vehicle = vehicle;
                            return mobilityTask;
                        })
                        .catch((error) => {
                            logger.error(`mobility-task-service.openRental::unable to get vehicle data from experian for vehicle reg :${vehicleLicenseNumber}\n Error :: ${error}`);
                            //Set UNKNOWN value to make and model, as this vehicle is not found in experian.
                            scope.vehicle = {
                                registration: vehicleLicenseNumber,
                                makeId: 187, //UNKNOWN
                                modelId: 605, //CAR UNKNOWN
                                typeId: 1,
                                experianDetails: {
                                    make: 'UNKNOWN',
                                    model: 'CAR UNKNOWN'
                                }
                            };
                            return mobilityTask;
                        });
                })
                .then((mobilityTask) => {
                    const { vehicle } = scope;
                    let { VIN } = openRentalDetails.rentedVehicleDetails;
                    let { vehicleAuthorization } = openRentalDetails.authorizationDetails ? openRentalDetails.authorizationDetails : null;
                    let { billingStartDate, billingStartTime, rentalStartDate, rentalStartTime, rentalContractNumber } = openRentalDetails.rentalContractDetails;

                    if (mobilityTask.rental().thirdPartyHire().isManualEntReservation && mobilityTask.rental().thirdPartyHire().isManualEntReservation()) {
                        /**
                         * If hire is created using Manual flow, then thirdPartyRentalConfirmed notification won't execute.
                         * In this case we will not EnterpriseSubHire deatils in mobilityTask
                         * Adding the required details.
                         */
                        let enterpriseSubHire = mobilityTask.rental().thirdPartyHire().toJSON();
                        mobilityTask.rental().thirdPartyHire(new EnterpriseSubHire(enterpriseSubHire));
                    }

                    mobilityTask.rental().thirdPartyHire().hireVehicle().regNo(vehicle.registration);
                    mobilityTask.rental().thirdPartyHire().hireVehicle().transmissionType(vehicle.experianDetails.transmission);
                    mobilityTask.rental().thirdPartyHire().hireVehicle().fuelType(vehicle.experianDetails.fuel);
                    mobilityTask.rental().thirdPartyHire().hireVehicle().colour(vehicle.experianDetails.colour);
                    mobilityTask.rental().thirdPartyHire().hireVehicle().seatNumber(vehicle.experianDetails.seatNumber);
                    mobilityTask.rental().thirdPartyHire().hireVehicle().vin(VIN);
                    if (vehicle.makeId) {
                        mobilityTask.rental().thirdPartyHire().hireVehicle().make().id(vehicle.makeId);
                    }
                    mobilityTask.rental().thirdPartyHire().hireVehicle().make().name(vehicle.experianDetails.make);
                    if (vehicle.modelId) {
                        mobilityTask.rental().thirdPartyHire().hireVehicle().model().id(vehicle.modelId);
                    }
                    mobilityTask.rental().thirdPartyHire().hireVehicle().supplierTypeCode(generalConstant.HIRE_TYPES.ENTERPRISE);
                    mobilityTask.rental().thirdPartyHire().hireVehicle().model().name(vehicle.experianDetails.model);

                    if (!mobilityTask.rental().thirdPartyHire().vehicleGroup() && vehicleAuthorization.vehicleClass) {
                        mobilityTask.rental().thirdPartyHire().vehicleGroup(vehicleAuthorization.vehicleClass);
                    }

                    if (mobilityTask.rental().hireVehicle().regNo() == null && mobilityTask.rental().thirdPartyHire().hireVehicle().regNo() != null) {
                        mobilityTask.rental().hireVehicle(mobilityTask.rental().thirdPartyHire().hireVehicle());
                    }

                    if (!mobilityTask.rental().thirdPartyHire().hireVehicleRef() && mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber()) {
                        mobilityTask.rental().thirdPartyHire().hireVehicleRef(mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber());
                    }

                    mobilityTask.rental().thirdPartyHire().experianDetails(new Vehicle(vehicle));

                    mobilityTask.rental().thirdPartyHire().rentalContractNumber(rentalContractNumber);
                    // mobilityTask.rental().thirdPartyHire().additionalDriverName();
                    mobilityTask.rental().thirdPartyHire().rentalStartDate(rentalStartDate);
                    mobilityTask.rental().thirdPartyHire().rentalStartTime(_.get(rentalStartTime, '$value', ''));
                    mobilityTask.rental().thirdPartyHire().timeZoneOffset(_.get(rentalStartTime, 'attributes.timeZoneOffset', ''));
                    mobilityTask.rental().thirdPartyHire().billingStartDate(billingStartDate);
                    mobilityTask.rental().thirdPartyHire().billingStartTime(_.get(billingStartTime, '$value', ''));

                    mobilityTask.schedule().arrive(new Date(hireDatetime)); //update arrive date as HireDateTime before sending request to ARM

                    logger.info(`mobility-task-service.openRental:: writing to prime : ${taskId} :: rental :  ${mobilityTask.rental()} `);
                    return enterpriseMobilityTaskService.adjustHireDuration(mobilityTask, hireDatetime, authToken);
                })
                .then((mobilityTask) => {
                    return _write(mobilityTask, authToken);
                })
                .catch((error) => {
                    logger.error(`mobility-task-service.openRental::failed to perform RentalOpened for taskId:${taskId}\n Error :: ${error}`);
                    return Q.reject({
                        status: 'fail',
                        msg: `RentalOpened failed for taskId:${taskId}.`,
                        err: error
                    });
                });
        }
        return deferred.promise;
    },
    thirdPartyRentalConfirmed: (taskId, rentalContractDetails, authToken) => {
        logger.info(`mobility-task-service.thirdPartyRentalConfirmed:: processing taskId : ${taskId}`);
        return _fetchUpdatedMobilityTask(taskId, authToken)
            .then((mobilityTask) => {
                const msg = `Enterprise confirmed reservation : ${rentalContractDetails.rentalConfirmationNumber}`;
                logger.info(`mobility-task-service.thirdPartyRentalConfirmed:: processing taskId : ${taskId} msg : ${msg}`);
                //TODO initSubHire needs to set thirdPartyHire to EnterpriseSubHire
                const mergedHireData = Object.assign({}, mobilityTask.rental().thirdPartyHire().toJSON(), rentalContractDetails);
                mobilityTask.rental().thirdPartyHire(new EnterpriseSubHire(mergedHireData));
                mobilityTask.rental().thirdPartyHire().hireVehicleRef(rentalContractDetails.rentalConfirmationNumber);

                logger.info(`mobility-task-service.thirdPartyRentalConfirmed::resourceID ${mobilityTask.schedule().resource().id()}.`);
                return Q.all([
                    _write(mobilityTask, authToken),
                    aahelp2Service.taskAudit({
                        taskId: mobilityTask.id(),
                        custReqId: mobilityTask.customerRequestId(),
                        text: msg,
                        authToken: authToken
                    })
                ]);
            })
            .catch((error) => {
                logger.error(`mobility-task-service.thirdPartyRentalConfirmed::failed to perform RentalConfirmedCreate ${JSON.stringify(error)}`);
                return Q.reject({
                    status: 'fail',
                    err: error
                });
            });
    },
    enterpriseRentalClosed: (taskId, enterpriseRentalClosedReq, authToken) => {
        let rentalClosedFromFleet;
        return _read(taskId, authToken)
            .then((mobilityTask) => {
                if (mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                    let closeRequest = enterpriseRentalClosedReq.closeRequest;
                    if (['GARR', 'COMP', 'CLSD'].includes(mobilityTask.status())) {
                        if (closeRequest) {
                            let rentedVehicleDetails = closeRequest.rentedVehicleDetails;
                            let enterpriseRentedVehicles = _.isArray(rentedVehicleDetails)
                                ? _.map(closeRequest.rentedVehicleDetails, (vehicle) => new EnterpriseRentedVehicle(vehicle))
                                : [new EnterpriseRentedVehicle(rentedVehicleDetails)];
                            mobilityTask.rental().thirdPartyHire().rentedVehicleDetails(enterpriseRentedVehicles);
                        }
                        if (['COMP', 'CLSD'].includes(mobilityTask.status())) {
                            /** Including 'COMP', 'CLSD' status in case if checkin is already done by Feet/Retailer app
                             * and RentalClosed update notification received from Enterprise to update the rented vehicle data
                             * and to skip checkInSelfCheckout().
                             */
                            rentalClosedFromFleet = true;
                        }
                        return _write(mobilityTask, authToken);
                    } else {
                        logger.error(`mobility-task-service.enterpriseRentalClosed::checking in ${taskId} NOT in GARR`);
                        return Q.reject({
                            msg: `Task in state of ${mobilityTask.status()} can not proceed to checkin`,
                            enterpriseErrorRespnse: {
                                invalidTaskStatus: true
                            }
                        });
                    }
                } else {
                    return Q.resolve({
                        isEnableEnterpriseHire: false
                    });
                }
            })
            .then((taskWriteResponse) => {
                return Q.resolve({
                    rentalClosedFromFleet,
                    taskWriteResponse
                });
            })
            .catch((error) => {
                logger.error(`mobility-task-service.enterpriseRentalClosed::failed to perform EnterpriseRentalClosed ${error}`);
                return Q.reject({
                    status: 'fail',
                    err: error
                });
            });
    },
    enterpriseCancelRental: (task, cancelRentalRequest) => {
        let taskId = task.id();
        let completionReason = _.get(cancelRentalRequest, 'completionReason', null);
        if (task.isArrived()) {
            let invalidStatusError = {
                msg: 'Hire in progress can not be cancelled',
                invalidTaskStatus: true
            };
            return Q.reject(invalidStatusError);
        }
        if (completionReason) {
            const supplier = {
                transactionId: taskId,
                tradingPartnerId: task.rental().thirdPartyHire().supplier().armsProfileId(),
                officeId: task.rental().thirdPartyHire().supplier().armsOfficeId()
            };

            const cancelDetails = {
                cancelDate: new Date(),
                cancelType: 'Other',
                cancelReason: completionReason
            };
            const cancelRentalRequest = { supplier, cancelDetails };
            logger.info(`mobility-task-service.enterpriseCancelRental ${task.tag()} canceling rental request ${completionReason}`);

            let body = JSON.stringify(cancelRentalRequest);
            let subject = eventsBusQueueConstant.TRANSMISSION_TYPE.RENTAL_CANCEL;
            let eventsRepository = new EventsBusQueueRepository();
            return eventsRepository
                .write(EventQueues.ENTERPRISE_INBOUND, { body, subject })
                .then((res) => {
                    logger.info(`Outbound Message :: For task id ${taskId} , transaction type - ${subject} is Success. Message sent to queue successfully!`);
                    return res;
                })
                .catch((err) => {
                    logger.error(`mobility-task-service.enterpriseCancelRental:: unable to store request in Service bus queue for TaskId= ${taskId}::`, err);
                    return Q.reject({
                        msg: `Cancel rental failed for task ${taskId}`
                    });
                });
        } else {
            return Q.resolve({
                cancelRentalRequestFromENT: true
            });
        }
    },
    getEnterpriseLocations: (searchData, authToken) => {
        if (searchData && searchData.supplier) {
            return enterpriseHireService.getLocations(searchData);
        } else {
            let taskId = searchData && searchData.taskId;
            let searchType = HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE;
            return _fetchSupplierFromTask(taskId, searchType, authToken).then((supplier) => {
                searchData.supplier = supplier;
                return enterpriseHireService.getLocations(searchData);
            });
        }
    },
    addDays: (data, authToken) => {
        return extensionSvc
            .addDays(data, authToken)
            .then((res) => {
                return res;
            })
            .catch((err) => {
                logger.error(`mobility-task-service.addDays:: unable to calculate next working day for TaskId= ${data.taskId} :: ${err.message}`);
                return Q.reject({
                    msg: `unable to calculate next working day for  ${data.taskId} :: ${err.message}`
                });
            });
    },
    getNextWorkingDay: (data, authToken) => {
        return extensionSvc
            .getNextWorkingDay(data, authToken)
            .then((res) => {
                return res;
            })
            .catch((err) => {
                logger.error(`mobility-task-service.getNextWokingDay:: unable to calculate next working day for TaskId= ${data.taskId} :: ${err.message}`);
                return Q.reject({
                    msg: `unable to calculate next working day for  ${data.taskId} :: ${err.message}`
                });
            });
    },
    fetchUpdatedMobilityTask: _fetchUpdatedMobilityTask,
    getEvaliteFallBackResponse: (taskId, authToken) => {
        /**
         * If the task has been created and any issue occured while reading/writing it
         * this function will return a generic response to eva-lite,
         * So eva-lite will not allow user to create multiple tasks and will ask to call to oldbury(AAHelp2)
         * for further actions.
         */
        return _fetchUpdatedMobilityTask(taskId, authToken).then((mobilityTask) => {
            if (mobilityTask && ['INIT', 'CHCK', 'GDET'].includes(mobilityTask.status())) {
                let response = {
                    taskId: mobilityTask.id(),
                    status: mobilityTask.status(),
                    msg: null,
                    result: 0
                };
                return response.taskId ? response : null;
            }
        });
    },
    triggerAllocateVehicleNotification: (notification) => {
        // trigger notification for allocation request notification
        const payload = {
            date: new Date().getTime(),
            type: notification.type ? notification.type : notificationTypes.ALLOCATE_VEHICLE_REQUEST,
            taskId: notification.taskId ? notification.taskId : null,
            status: notification.status ? notification.status : null,
            assignedUserId: notification.operatorId ? notification.operatorId : null
        };

        const repairLocationId = parseInt(notification.recovery.destResourceId());

        if (Number.isInteger(repairLocationId)) {
            // if repair location available we can set region
            return cshSupplier.read(repairLocationId).then((supplier) => {
                // if we have received supplier
                if (supplier) {
                    // get region from supplier
                    payload.region = supplier.regionCode();
                }
                return notificationService.update(payload);
            });
        }

        return notificationService.update(payload);
    },
    updateEstimatedRepairDate: (params) => {
        return cshMobilityTaskSvc.updateEstimatedRepairDate(params);
    },
    validateTaskBeforeWrite: (task) => {
        // Dont allow for task write if vehicle reg not exists
        if (Task.ARRIVED.includes(task.status()) || Task.HEADING.includes(task.status())) {
            const regNo = task.rental().thirdPartyHire() ? task.rental().thirdPartyHire().hireVehicle().regNo() : task.rental().hireVehicle().regNo();
            if (!regNo) {
                logger.error(`Error while saving the task. No Hire vehicle registration found for ${task.id()}`);
                return Q.reject({
                    errMsg: `Changes not saved. Please retain any details, click update and re-input.`
                });
            }
        }
        return Q.resolve(true);
    },
    sendSMS: (rentalContractDetails, authToken) => {
        return _fetchUpdatedMobilityTask(rentalContractDetails.taskId, authToken)
            .then((mobilityTask) => {
                return enterpriseMobilitySvc.sendSMS(mobilityTask, rentalContractDetails);
            })
            .catch((err) => {
                logger.error(`mobilityTaskService.SendSMS:: Error in task read :: ${rentalContractDetails.taskId} - `, err);
            });
    },
    isPNC: _isPNC,
    sendEmail: (taskDetailsResp, authToken) => {
        let { mobilityTask } = taskDetailsResp;
        if (mobilityTask && _isPNC(mobilityTask)) {
            const thirdPartyObj = mobilityTask.rental().thirdPartyHire();
            const data = {
                taskId: mobilityTask.id(),
                email: process.env.payAndClaimEmail,
                vehicleReg: thirdPartyObj ? thirdPartyObj.hireVehicle().regNo() : null,
                repairRetailer: [mobilityTask.rental().repairLocation().name(), mobilityTask.rental().repairLocation().address()].join(', '),
                authorizedBy: thirdPartyObj ? thirdPartyObj.hireAuthorization().authorizedBy() : null,
                amount: thirdPartyObj ? thirdPartyObj.hireAuthorization().amount() : null
            };
            logger.info(`mobility-task-service.sendEmail :: sending email for PNC ${JSON.stringify(data)}`);
            aahelp2Service.sendEmail(data, aahelp2UrlsConstant.SEND_EMAIL_PAY_AND_CLAIM, authToken);
        }
    },
    onHireThirdPartyVehicles: (regNo) => {
        return repmService.getOnHireThirdPartyVehicles(regNo);
    },
    isCCPEnabled: _isCCPEnabled,
    isCheckoutExtentionGroup: _isCheckoutExtentionGroup,
    getExtensionsOffHireSummary: (data, authToken) => {
        return extensionSvc
            .getExtensionsOffHireSummary(data, authToken)
            .then((res) => {
                return res;
            })
            .catch((err) => {
                logger.error(`mobility-task-service.getExtensionsOffHireSummary:: unable to find offhire summary for TaskId= ${data.taskId} :: ${err.message}`);
                return Q.reject({
                    msg: `unable to calculate next working day for  ${data.taskId} :: ${err.message}`
                });
            });
    },
    sapCheckinNotification: (payload, authToken) => {
        let { taskId } = payload;
        let latestExtn,
            hireState,
            notificationType,
            approvedDays = 0,
            mobilityTask;
        return _read(taskId, authToken)
            .then((mTask) => {
                mobilityTask = mTask;
                return extensionSvc.getLatestExtension(taskId, authToken);
            })
            .then((res) => {
                let srcRentalTaskId = mobilityTask.rental().srcRentalTaskId();
                return Object.keys(res).length ? res : extensionSvc.getLatestExtension(srcRentalTaskId, authToken); // fetch parent hire extn if child doesnt have
            })
            .then((res) => {
                latestExtn = res;
                if (!Object.keys(latestExtn).length) {
                    logger.info(`mobility-task-service.sapCheckinNotification:: Extension not found - ${taskId}`);
                }
                logger.info(`mobility-task-service.sapCheckinNotification :: getLatestExtension -  ${JSON.stringify(latestExtn)}`);
                return extensionSvc.getHireState(taskId, authToken);
            })
            .then((res) => {
                hireState = res;
                let date = Object.keys(latestExtn).length ? latestExtn.dateApprovedUntil : null;
                // scenarios - daysDiff < 0 only when early checkin happens
                let daysDiff = date ? moment(moment().format('YYYY-MM-DD')).diff(moment(moment(date).format('YYYY-MM-DD')), 'days') : 0;
                if (!hireState.outPendingHires && !hireState.outActiveHires) {
                    approvedDays = daysDiff;
                    notificationType = eventsBusQueueConstant.SAP_MESSAGE.COMPLETION;
                } else {
                    // check for latest extension still in effect
                    // daysDiff - negative when dateApprovedUntil is in future
                    if (daysDiff < 0) {
                        approvedDays = daysDiff;
                        notificationType = eventsBusQueueConstant.SAP_MESSAGE.SWAP_COMPLETION;
                    }
                }

                if (!notificationType) {
                    logger.info(`mobility-task-service.sapCheckinNotification :: no matching scenario found. Not sending checkin notification for ${taskId}`);
                    return;
                }

                //send message into SAP Message SBQ
                let subject = notificationType;

                payload.approvedDays = approvedDays;
                if (Object.keys(latestExtn).length) {
                    payload.extensionId = latestExtn.id;
                    payload.extensionDetails = latestExtn;
                }

                let body = JSON.stringify(payload);
                logger.info(`mobility-task-service.sapCheckinNotification :: sending message to queue -  ${body} - ${subject}`);

                let eventsRepository = new EventsBusQueueRepository();
                return eventsRepository.write(EventQueues.SAP_MESSAGE, { body, subject }).then(() => {
                    logger.info(`mobility-task-service.sapCheckinNotification :: For task id ${taskId} , notification type - ${subject} is Success. Message sent to queue successfully!`);
                    return;
                });
            })
            .catch((err) => {
                logger.error(`mobility-task-service.sapCheckinNotification:: failed for TaskId= ${taskId} :: ${err.message}`);
            });
    }
};
