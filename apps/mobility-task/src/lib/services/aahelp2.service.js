/** @module services-aahelp2 */
const restfulRequest = require('@aa/endpoints').restfulRequest,
    _ = require('lodash'),
    Q = require('q'),
    uuidv4 = require('uuid').v4,
    nanoid = require('nanoid'),
    logger = require('winston'),
    moment = require('moment'),
    { Queue } = require('@aa/azure-queue'),
    errors = require('../constants/error.constant'),
    Task = require('@aa/malstrom-models/lib/task.model'),
    generalConstant = require('../constants/general.constant'),
    eventsBusQueueConstant = require('../constants/events-bus-queue.constant'),
    MobilityTask = require('@aa/mobility-models-common/lib/mobility-task.model'),
    TaskWriteResponse = require('@aa/malstrom-models/lib/task-write-response.model'),
    InsurerErrorsFactory = require('@aa/mobility-models/lib/factories/insurer-errors.factory'),
    AAHelpTaskFactory = require('@aa/mobility-models-common/lib/factories/aahelp-task.factory'),
    InsuranceTerms = require('@aa/mobility-models/lib/insurance-terms.model'),
    InsuranceOption = require('@aa/mobility-models/lib/insurance-option.model'),
    MobilityInsurance = require('@aa/mobility-models/lib/mobility-insurance.model'),
    CarHireDriver = require('@aa/mobility-models/lib/car-hire-driver.model'),
    EventsBusQueueRepository = require('../repositories/events-bus-queue.repository'),
    fs = require('fs');

let aah2Proxy = 'https://' + process.env.loadbalancer;

/**
 * build get query block
 * @param  {AuthCode} authToken contains encrypted JTW
 * @param  {string} url       request to post
 * @return {Object}           details to pass to the request
 */
const _getQueryBuilder = (url, authToken, qs) => {
    if (process.env.TRACE) {
        fs.writeFileSync(`${__dirname}/${uuidv4()}.log`, {
            uri: aah2Proxy + url,
            method: 'get',
            body: JSON.stringify(qs)
        });
    }

    return {
        uri: aah2Proxy + url,
        qs: qs,
        method: 'GET',
        strictSSL: false, // don't attempt to validate ssl certificate
        headers: {
            'content-type': 'application/json',
            'cache-control': 'no-cache',
            'auth-token': authToken.jwt
        },
        json: true
    };
};

/**
 * build a post query block
 * @param  {string} url       identifies the method to call
 * @param  {object} body      cotnains details to be posted
 * @param  {AuthCode} authToken contains encrypted JTW
 * @return {Object}           details to pass to the request
 */
const _postQueryBuilder = (url, authToken, body) => {
    if (process.env.TRACE) {
        fs.writeFileSync(`${__dirname}/${uuidv4()}.log`, {
            uri: aah2Proxy + url,
            method: 'post',
            body: JSON.stringify(body)
        });
    }

    return {
        uri: aah2Proxy + url,
        method: 'POST',
        strictSSL: false, // don't attempt to validate ssl certificate
        headers: {
            'content-type': 'application/json',
            'cache-control': 'no-cache',
            'auth-token': authToken.jwt
        },
        json: true,
        body: body
    };
};

const _deleteQueryBuilder = (url, authToken, qs) => {
    if (process.env.TRACE) {
        fs.writeFileSync(`${__dirname}/${uuidv4()}.log`, {
            uri: aah2Proxy + url,
            method: 'delete',
            body: JSON.stringify(qs)
        });
    }

    return {
        uri: aah2Proxy + url,
        method: 'DELETE',
        strictSSL: false, // don't attempt to validate ssl certificate
        headers: {
            'content-type': 'application/json',
            'cache-control': 'no-cache',
            'auth-token': authToken.jwt
        },
        json: true,
        qs: qs
    };
};

function pushMessageToQueue(body, label) {
    let eventsRepository = new EventsBusQueueRepository();
    let queueName = eventsBusQueueConstant.QUEUE_NAME.HIRE_VEHICLE_INBOUND;
    return eventsRepository.write(queueName, { body, label });
}

function _resetRentalInsuranceDetails(mobilityTask) {
    const existingSupNetworkCode = mobilityTask.rental().insurance().supNetworkCode();
    mobilityTask.rental().insurance(new MobilityInsurance());
    mobilityTask.rental().insurance().supNetworkCode(existingSupNetworkCode);
    if (mobilityTask.rental().isSelfInsured() || (!!mobilityTask.rental().thirdPartyHire() && mobilityTask.rental().thirdPartyHire().isSelfCheckout())) {
        mobilityTask.rental().mainDriver().softReset();
        mobilityTask.rental().additionalDrivers([]);
    } else {
        mobilityTask.rental().mainDriver().softReset();
        mobilityTask
            .rental()
            .additionalDrivers()
            .forEach((driver, idx) => {
                setTimeout(() => {
                    driver.softReset();
                }, 100 * idx);
            });
    }
}

function _writeTask(task, authToken) {
    const body = {
        task: null
    };
    let path = '/task';

    // for unknown reasons excludeResourceId sometimes contains a -ve value ... that is wrong ...
    task.appointment().excludeResourceId(null);
    body.task = task.toJSON();

    // if new then send it via the authorise end point ...
    if (task.isNew()) {
        task.indicators().authorised(true); // also auth task

        path = '/task/authorise';
        body.cr = {
            // and add a mock cr
            id: task.customerRequestId(),
            seLocatorId: 1 // stops service from trying to 'write the customer request ...'
        };
    }

    return restfulRequest(_postQueryBuilder(`/api/task-service${path}`, authToken, body))
        .then((resp) => {
            return new TaskWriteResponse(resp.data);
        })
        .catch((err) => {
            logger.error(`aahelp2-service.write:: read task id ${task.id()} seqNo ${task.sequence()} :: ${err.msg}`);
            return Q.reject(errors.create(1002, `task write error ${task.id()} seqNo ${task.sequence()}`, err));
        });
}

function _writeCarRentalReconcileTask(task, authToken) {
    const defered = Q.defer();
    const body = task.toJSON();
    const path = '/task/update/reconcile';

    restfulRequest(_postQueryBuilder(`/api/task-service${path}`, authToken, body))
        .then((resp) => {
            defered.resolve(resp.data);
        })
        .catch((error) => {
            logger.error(`aahelp2-service._writeCarRentalReconcileTask:: read task id ${task.id()} :: ${error.data}`);
            defered.reject(error.data);
        });

    return defered.promise;
}

/**
 * Get date for the hire
 * @param date
 * @param days
 * @param rule
 * @param authToken
 * @return {Promise<T | any>}
 */
function getHireStartDay(date, days, rule, authToken, initialHireDaysType = '') {
    const initialHireDaysTypeVal = initialHireDaysType === undefined ? generalConstant.HIRE_DAYS_TYPES.WORKING_DAYS : initialHireDaysType;

    let url = '/api/extension-service/dates/addDays';
    if (initialHireDaysTypeVal === generalConstant.HIRE_DAYS_TYPES.DAYS) {
        url = '/api/extension-service/dates/addcontinuousDays';
    }
    logger.info('url' + url);
    return restfulRequest(
        _postQueryBuilder(url, authToken, {
            date,
            days,
            rule
        })
    ).then((res) => {
        return res.data;
    });
}

function _termsAndConditions(rental, mainDriverLicenceType, authToken) {
    let insuranceRefId = rental.insurance().id(),
        supNetCode = rental.insurance().supNetworkCode();

    return restfulRequest(
        _postQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/hire/termsAndConditions`, authToken, {
            insuranceRefId,
            mainDriverLicenceType
        })
    )
        .then((insuranceOptionsResp) => {
            return {
                insuredTerms: new InsuranceTerms(insuranceOptionsResp.data.insuranceTerms),
                insuranceOption: new InsuranceOption(insuranceOptionsResp.data.insuranceOption)
            };
        })
        .catch((err) => {
            return Q.reject(err.data.errors ? err.data : err);
        });
}
module.exports = {
    /**
     * read task from prime / csh .
     * @param  {number} taskId    of task to read
     * @param  {AuthCode} authToken
     * @return {Promise}           on success returns Task
     */
    readTask: (taskId, authToken) => {
        logger.info(`aahelp2.service.readTask :: searching for ${taskId}`);
        if (process.env.mockPrime) {
            return Q.resolve(
                new Task({
                    id: taskId,
                    sequence: 2,
                    status: 'UNAC'
                })
            );
        }
        return restfulRequest(_getQueryBuilder(`/api/task-service/task/${taskId}`, authToken, null))
            .then((resp) => {
                logger.info(`aahelp2.service.readTask :: found ${resp.data.id}`);
                return new AAHelpTaskFactory.create(resp.data);
            })
            .catch((err) => {
                logger.error(`aahelp2-service.readTask:: failed to read task id ${taskId} :: ${err.msg}`);
                return Q.reject(errors.create(1001, `task read error for ${taskId}`, err));
            });
    },
    /**
     * write task to prime - this is to be used for the second time we write a task seq no > -1
     * @param  {Task} task      to persist in prime
     * @param  {AuthCode} authToken
     * @return {Promise.<TaskWriteResponse>}           on success returns TaskWriteResponse
     */
    writeTask: _writeTask,
    completeTask: (task, code, authToken) => {
        const body = {
            task: null
        };
        task.fault()
            .outcome()
            .completionCode(code || '80');
        body.task = task.toJSON();

        return restfulRequest(_postQueryBuilder(`/api/task-service/task/complete`, authToken, body))
            .then((resp) => {
                return new TaskWriteResponse(resp.data);
            })
            .catch((err) => {
                logger.error(`aahelp2-service.write:: read task id ${task.id()} seqNo ${task.sequence()} :: ${err.msg}`);
                return Q.reject(errors.create(1002, `task write error ${task.id()} seqNo ${task.sequence()}`, err));
            });
    },
    /**
     * add task - first time we post to prime a task that has been split ...
     * @param  {Task} task fragment of task to persist in prime
     * @param {AuthCode} authToken
     * @return {Promise.<TaskWriteResponse>}      on success returns TaskWriteResponse
     */
    addTask: (task, parentTask, authToken) => {
        const body = {
            task: task.toJSON()
        };
        // hack to get this working
        body.task.rental = null;
        console.log(body.task);
        return restfulRequest(_postQueryBuilder('/api/task-service/task/add', authToken, body))
            .then((resp) => {
                return new TaskWriteResponse(resp.data);
            })
            .catch((err) => {
                logger.error(`aahelp2-service.addTask:: add task id ${task.id()} seqNo ${task.sequence()}:: ${err.msg}`);
                return Q.reject(errors.create(1003, `add task id ${task.id()} seqNo ${task.sequence()}`, err));
            });
    },

    /**
     * write in audit
     * @param  {number} taskId    refers to the task we want the audit to be logged against
     * @param  {string} msg   of the message we want to add in the audit
     * @param  {AuthCode} authToken
     * @return {Promise.<Object>}           on success returns true
     */
    audit: (taskId, customerRequestId, msg, authToken) => {
        const body = {
            taskId: taskId,
            msg: msg
        };
        logger.info(`AAHELP2Service.audit ::  write audit :: ${taskId}, ${msg}`);

        return restfulRequest(_postQueryBuilder('/api/audit/write', authToken, body))
            .then((resp) => {
                return resp.data;
            })
            .catch((err) => {
                logger.error(`aahelp2-service.audit:: add audit ${taskId} :: ${err.msg}`);
                return Q.reject(errors.create(1004, 'write audit error', err));
            });
    },

    /**
     * write call info aka remark
     * @param  {number} crId      refers to the customer request id the remark will be logged against
     * @param  {number} taskId    refers to the task we want the remark to be logged agaisnt
     * @param  {string} msg   of message we want to write
     * @param  {AuthCode} authToken
     * @return {Promise.<Object>}           on success return true
     */
    writeCallInfo: (crId, taskId, msg, authToken) => {
        const body = {
            taskId: taskId,
            crId: crId,
            msg: msg
        };
        logger.info(`aahelp2-service.writeCallInfo :: write call info :: ${crId}, ${taskId}, ${msg}`);

        return restfulRequest(_postQueryBuilder('/api/call-info/write', authToken, body))
            .then((resp) => {
                return resp.data;
            })
            .catch((err) => {
                logger.error(`aahelp2-service.writeCallInfo:: add callinfo error for task ${taskId} :: ${err.msg}`);
                return Q.reject(errors.create(1005, `add callinfo error for task ${taskId}`, err));
            });
    },
    /**
     * clone tasnk
     * @param  {Task} parentTask   [description]
     * @param  {CreateReason} createReason [description]
     * @param  {AuthCode} authToken
     * @return {Promise.<MobilityTask>} on sucess resolves to MobilityTask
     */
    cloneTask: (parentTask, createReason, authToken) => {
        console.log(authToken);
        const body = {
            task: parentTask.toJSON(),
            reason: createReason.toJSON()
        };
        logger.info(`aahelp2.service.cloneTask :: cloning task ${parentTask.id()} for create reason ${createReason.id()}`, authToken);
        return restfulRequest(_postQueryBuilder('/api/task-service/task/clone', authToken, body))
            .then((resp) => {
                return new MobilityTask(resp.data);
            })
            .catch((err) => {
                logger.error(`aahelp2-service.cloneTask:: failed to clone task ${parentTask.id()} :: ${err.msg}`);
                return Q.reject(errors.create(1006, `failed to clone task ${parentTask.id()}`, err));
            });
    },
    /**
     * get tasks within a customer request id ...
     * @param  {Number} custReqId
     * @param  {AuthToken} authToken
     * @return {Promise.<Array.<Object>>}  on success return an array of tasks
     */
    caseRead: (custReqId, authToken) => {
        console.log('start case read');
        return restfulRequest(_getQueryBuilder(`/api/task-service/tasks/tasksByCustomerRequestId/${custReqId}`, authToken))
            .then((resp) => {
                console.log('end case read');
                const _tasks = resp.data.map((task) => AAHelpTaskFactory.create(task));
                return _tasks;
            })
            .catch((err) => {
                logger.error(`aahelp2-service.customerRequestId:: failed to read customer rquest ${custReqId} :: ${err.msg}`);
                return Q.reject(errors.create(1006, `failed to clone task ${custReqId}`, err));
            });
    },
    insureDriver: (rental, driver, authToken) => {
        const body = {
            insuranceRefId: rental.insurance().id(),
            driver: driver.toJSON()
        };
        let insuredDriver,
            supNetCode = rental.insurance().supNetworkCode();

        return restfulRequest(_postQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/driver`, authToken, body))
            .then((resp) => {
                insuredDriver = new CarHireDriver(resp.data);
                logger.info(`insurer.serivce.writeDriver :: found ${resp.data.id}`);
                if (!insuredDriver.isEligible()) {
                    insuredDriver.isEligible(false);
                }
                if (!driver.isPrimaryDriver() || insuredDriver.hasError()) {
                    return Q.resolve({
                        insuredTerms: null,
                        insuranceOption: null
                    });
                }
                return _termsAndConditions(rental, driver.licenceType(), authToken);
            })
            .then((resp) => {
                //insert/update only if main driver
                if (driver.isPrimaryDriver()) {
                    rental.insurance().insuredTerms(resp.insuredTerms);
                    rental.insurance().insuranceOption(resp.insuranceOption);
                }
                return {
                    driver: insuredDriver,
                    rental: rental
                };
            })
            .catch((err) => {
                const coopersError = err.data || null;
                if (coopersError && coopersError.hasOwnProperty('data')) {
                    return Q.reject(coopersError.data);
                }

                logger.error(`insurer.serivce.writeDriver:: failed to read task id ${rental.insurance().id()} :: ${err.msg}`);
                return err.errors ? Q.reject(err) : Q.reject(errors.create(1007, `write driver for ${rental.insurance().id()} failed`, err));
            });
    },

    insureDriverWip: (insuranceRefId, driver, wip, authToken) => {
        const body = {
            insuranceRefId: insuranceRefId,
            driver: driver.toJSON()
        };

        return restfulRequest(
            _getQueryBuilder(`/api/car-hire-insurance-service/insuranceRef/`, authToken, {
                wip: wip
            })
        ).then((resp) => {
            logger.info(`insurer.serivce.insuranceRefId :: found ${resp.data.id}`);
            if (!body.insuranceRefId) {
                body.insuranceRefId = resp.data.id;
            }
            return restfulRequest(_postQueryBuilder(`/api/car-hire-insurance-service/driver`, authToken, body))
                .then((resp) => {
                    logger.info(`insurer.serivce.writeDriver :: found ${resp.data.id}`);
                    return new CarHireDriver(resp.data);
                })
                .catch((err) => {
                    const coopersError = err.data || null;
                    if (coopersError && coopersError.hasOwnProperty('data')) {
                        return Q.reject(coopersError.data);
                    }

                    logger.error(`insurer.serivce.writeDriver:: failed to read task id ${insuranceRefId} :: ${err.msg}`);
                    return err.errors ? Q.reject(err) : Q.reject(errors.create(1007, `write driver for ${insuranceRefId} failed`, err));
                });
        });
    },

    deleteInsuredDriver: (rental, driver, authToken) => {
        let insuranceRefId = rental.insurance().id(),
            supNetCode = rental.insurance().supNetworkCode();
        if (driver === null || driver.isPrimaryDriver() || !driver.hasInsurerId()) {
            return Q.resolve(); // we never delete primary drivers ..
        }

        return restfulRequest(
            _deleteQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/driver`, authToken, {
                insuranceRefId: insuranceRefId,
                driverId: driver.id()
            })
        ).catch((err) => {
            return err.data ? Q.reject(err.data) : Q.reject(errors.create(1008, `deleteInsuredDriver ${insuranceRefId} failed`, err));
        });
    },

    uploadDocuments: (payload, authToken) => {
        const path = '/api/edocs-service/render/checkout';

        return restfulRequest(_postQueryBuilder(path, authToken, payload)).then((eDocsResp) => {
            return eDocsResp.data; // we get back the document name we saved ..
        });
    },

    bookHireCar: (mobilityTask, authToken) => {
        let body = null,
            supNetCode = mobilityTask.rental().insurance().supNetworkCode();

        if (mobilityTask.rental().isSelfInsured() || (mobilityTask.rental().thirdPartyHire() && mobilityTask.rental().thirdPartyHire().isSelfCheckout())) {
            return Q.resolve();
        }

        // make sure we have the correct insurance type .. .
        if (mobilityTask.rental().insurance().type() !== MobilityInsurance.TYPE) {
            mobilityTask.rental().insurance(new MobilityInsurance());
            mobilityTask.rental().insurance().supNetworkCode(supNetCode);
        }

        // check if we have a hire vehicle
        if (mobilityTask.rental().hireVehicle().id() === null) {
            logger.info(`aaherlp2.service::bookHireCar  ${mobilityTask.tag()} we have no hire vehicle`);
            return Q.resolve({
                id: null,
                stockId: null
            });
        }

        body = {
            mobilityTask: mobilityTask.toJSON()
        };

        logger.info(`aaherlp2.service::bookHireCar ${mobilityTask.tag()} reg no ${mobilityTask.rental().hireVehicle().id()}`);

        return restfulRequest(_postQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/book`, authToken, body))
            .then((bookHireResp) => {
                return bookHireResp.data;
            })
            .catch((err) => {
                logger.error(`aaherlp2.service::bookHireCar :: ${mobilityTask.tag()} failed to communicate with insurrer`, err.data);
                return Q.reject(errors.create(1008, `Communication with insurrer failed for task ${mobilityTask.id()}@${mobilityTask.rental().hireVehicle().regNo()}`, err.data));
            });
    },

    updatePickupTime: (mobilityTask, authToken) => {
        let body,
            supNetCode = mobilityTask.rental().insurance().supNetworkCode();

        const isEnterpriseHire = mobilityTask.rental().thirdPartyHire()
            ? mobilityTask.rental().thirdPartyHire().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE || mobilityTask.rental().thirdPartyHire().isEnterprise()
            : false;

        if (mobilityTask.rental().isSelfInsured() || isEnterpriseHire || !mobilityTask.rental().insurance().isSet()) {
            return Q.resolve();
        }

        body = {
            insuranceRefId: mobilityTask.rental().insurance().id(),
            startTime: mobilityTask.appointment().earliest(),
            endTime: new Date(mobilityTask.appointment().earliest().getTime() + mobilityTask.fault().repairMinutes() * 60 * 1000)
        };

        return restfulRequest(_postQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/hire/updateTimes`, authToken, body)).catch((err) => {
            return err.data ? Q.reject(err.data) : Q.reject(errors.create(1008, `updatePickupTime ${mobilityTask.id()} failed`, err));
        });
    },

    startCarHire: (task, authToken) => {
        const rental = task.rental(),
            supNetCode = task.rental().insurance().supNetworkCode();

        logger.info(`ahelp2-service.startCarHire :: start hire for ${rental.insurance().id()}`);

        let numerOfDays = task.rental().initialHireDays() || 1;
        let initialHireDaysType = task.rental().initialHireDaysType() || generalConstant.HIRE_DAYS_TYPES.WORKING_DAYS;
        let has3PMRule = initialHireDaysType === generalConstant.HIRE_DAYS_TYPES.WORKING_DAYS; // for Hire Days Type 'd', dont apply 3pm rule

        // we need to retrieve new date of hire end with initial
        // assumption it will be working 1 days from now as per BLTS-340
        return getHireStartDay(Date.now(), numerOfDays, has3PMRule, authToken, initialHireDaysType)
            .then((endDate) => {
                // as there is a chance client is picking vehicle later than already defined time
                // we need to recalculate total time of hire (repairMinutes)
                endDate = new Date(endDate);
                const hirePeriod = (endDate.getTime() - task.schedule().arrive().getTime()) / (60 * 1000);
                task.fault().repairMinutes(Math.round(hirePeriod));
                logger.info(`ahelp2-service.startCarHire :: recalculated repairMinutes for ${task.id()} - ${Math.round(hirePeriod)}`);

                if (rental.isSelfInsured()) {
                    return Q.resolve(true);
                }
                // notify insurance provider about hire start
                return restfulRequest(
                    _postQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/hire/start`, authToken, {
                        insuranceRefId: rental.insurance().id(),
                        endDate: endDate
                    })
                );
            })
            .catch((err) => {
                logger.error(`aahelp2.services.startCarHire::failed to start ${rental.insurance().id()}`, JSON.stringify(err));
                return Q.reject(err.data && err.data.errors ? err.data : err);
            });
    },

    getHireStartDay: getHireStartDay,

    /**
     * if not self insured the complete hire / else return a mock object to keep flow
     * @param  {CarRental} rental contains details of rental
     * @param  {Object} authToken contains details
     * @return {Promise}           on success resolves to  {insuranceOption : {premium:0}}
     */
    endCarHire: (rental, authToken) => {
        let supNetCode = rental.insurance().supNetworkCode();
        if (rental.isSelfInsured()) {
            return Q.resolve({
                insuranceOption: {
                    premium: 0
                }
            });
        }

        logger.info(` ahelp2-service.endCarHire :: for ${rental.insurance().id()}`);
        // need to send the insuranceOption so that we find the premium ..
        return restfulRequest(
            _getQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/hire/finish`, authToken, {
                insuranceRefId: rental.insurance().id(),
                insuranceOption: rental.insurance().insuranceOption().id()
            })
        )
            .then((resp) => resp.data)
            .catch((err) => {
                logger.error(`aahelp2.services.endCarHire:: for ${rental.insurance().id()}`, JSON.stringify(err));
                return Q.reject(err.data.errors ? err.data : err);
            });
    },
    extendHire: (mobilityTask, endDate, authToken) => {
        let supNetCode = mobilityTask.rental().insurance().supNetworkCode();
        logger.info(`aahelp2.services.extendHire:: for ${mobilityTask.id()}-${mobilityTask.rental().insurance().id()}`);
        return restfulRequest(
            _postQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/hire/extend`, authToken, {
                insuranceRefId: mobilityTask.rental().insurance().id(),
                endDate: endDate
            })
        ).catch((err) => {
            let errResp = err;
            logger.error(`aahelp2.services.extendHire:: failed ${mobilityTask.id()}-${mobilityTask.rental().insurance().id()}`, JSON.stringify(err));
            if (err.data.hasOwnProperty('errors')) {
                errResp = new TaskWriteResponse({
                    result: -1,
                    primeMsg: InsurerErrorsFactory.hasError(err.data),
                    sequence: mobilityTask.sequence(),
                    status: mobilityTask.status()
                });
            }
            return Q.reject(errResp);
        });
    },
    addExtendHireToQueue: (mobilityTask, endDate) => {
        let supNetCode = mobilityTask.rental().insurance().supNetworkCode();

        let label = eventsBusQueueConstant.HIRE_TYPE.HIRE_EXTENSION;
        let body = JSON.stringify({
            insuranceRefId: mobilityTask.rental().insurance().id(),
            endDate: endDate,
            subject: label,
            supNetCode
        });

        logger.info(`aahelp2.services.extendHire:: for ${mobilityTask.id()}-${mobilityTask.rental().insurance().id()}`);
        return pushMessageToQueue(body, label)
            .then(() => {
                logger.info(
                    `Hire Inbound Message :: For task id ${mobilityTask.id()} , transaction type - ${eventsBusQueueConstant.HIRE_TYPE.HIRE_EXTENSION} is Success. Message sent to queue successfully!`
                );
            })
            .catch((err) => {
                logger.info(
                    `Hire Inbound Message :: For task id ${mobilityTask.id()} , transaction type - ${eventsBusQueueConstant.HIRE_TYPE.HIRE_EXTENSION} is failed to store message in a service bus queue`
                );
                return err;
            });
    },
    getInsuranceOptions: (rental, authToken) => {
        let supNetCode = rental.insurance().supNetworkCode(),
            insuranceRefId = rental.insurance().id();
        return restfulRequest(
            _getQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/hire/insurance`, authToken, {
                insuranceRefId
            })
        ).then((jsOptions) => {
            return jsOptions.data.map((opt) => new InsuranceOption(opt));
        });
    },
    addExtendHireToSAPQueue: (mobilityTask, endDate, extensionId = '') => {
        if (extensionId === null || extensionId === '') {
            //since for maladmin we are already pushing in queue
            return Q.resolve(true);
        }

        let subject = eventsBusQueueConstant.SAP_MESSAGE.MANUAL_EXTENSION;
        let body = JSON.stringify({
            taskId: mobilityTask.id(),
            endDate: endDate,
            extensionId: extensionId
        });

        logger.info(`aahelp2.services.addExtendHireToSAPQueue:: for ${mobilityTask.id()}`);

        let eventsRepository = new EventsBusQueueRepository();
        let queueName = eventsBusQueueConstant.QUEUE_NAME.SAP_MESSAGE;
        return eventsRepository
            .write(queueName, { body, subject })
            .then(() => {
                logger.info(
                    `SAP Message :: For task id ${mobilityTask.id()} , transaction type - ${eventsBusQueueConstant.SAP_MESSAGE.MANUAL_EXTENSION} is Success. Message sent to queue successfully!`
                );
                return Q.resolve(true);
            })
            .catch((err) => {
                logger.info(
                    `SAP Message :: For task id ${mobilityTask.id()} , transaction type - ${eventsBusQueueConstant.SAP_MESSAGE.MANUAL_EXTENSION} is failed to store message in a service bus queue`
                );
                return Q.reject(err);
            });
    },
    termsAndConditions: _termsAndConditions,

    setInsuranceOption: (rental, optionId, authToken) => {
        let supNetCode = rental.insurance().supNetworkCode(),
            insuranceRefId = rental.insurance().id();
        return restfulRequest(
            _postQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/hire/insurance`, authToken, {
                insuranceRefId,
                optionId
            })
        ).then((insuranceOptionsResp) => {
            return {
                insuredTerms: new InsuranceTerms(insuranceOptionsResp.data.insuranceTerms),
                insuranceOption: new InsuranceOption(insuranceOptionsResp.data.insuranceOption)
            };
        });
    },

    swapMainDriver: (rental, oldMainDriver, newMainDriver, authToken) => {
        let insuranceRefId = rental.insurance().id(),
            supNetCode = rental.insurance().supNetworkCode();
        return restfulRequest(
            _postQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/swapMainDriver`, authToken, {
                insuranceRefId: insuranceRefId,
                oldMainDriver: oldMainDriver.toJSON(),
                newMainDriver: newMainDriver.toJSON()
            })
        )
            .then((swapMainDriverResp) => {
                swapMainDriverResp.data.mainDriver.isPrimaryDriver = true;
                return {
                    mainDriver: new CarHireDriver(swapMainDriverResp.data.mainDriver),
                    additionalDriver: new CarHireDriver(swapMainDriverResp.data.additionalDriver)
                };
            })
            .catch((err) => {
                return Q.reject(err.data.errors ? err.data : err);
            });
    },
    // assume that cover has not started yet ..
    deleteCover: (mobilityTask, authToken) => {
        let supNetCode = mobilityTask.rental().insurance().supNetworkCode();
        logger.info(`aahelp2.services.deleteCover ${mobilityTask.tag()} selfinsured ${mobilityTask.rental().isSelfInsured()} insurance id ${mobilityTask.rental().insurance().id()}`);
        if (mobilityTask.rental().isSelfInsured() || mobilityTask.rental().insurance().id() === null) {
            return Q.resolve(mobilityTask);
        }
        logger.info(`aahelp2.services.deleteCover ${mobilityTask.tag()} delete hire ${mobilityTask.rental().insurance().id()}`);
        return restfulRequest(
            _deleteQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/hire`, authToken, {
                insuranceRefId: mobilityTask.rental().insurance().id()
            })
        )
            .then(() => {
                _resetRentalInsuranceDetails(mobilityTask);
                return mobilityTask;
            })
            .catch((err) => {
                return Q.reject(err.data && err.data.errors ? err.data : err);
            });
    },
    getCoverDetails: (mobilityTask, authToken) => {
        let supNetCode = mobilityTask.rental().insurance().supNetworkCode();
        logger.info(`aahelp2.services.getCoverDetails ${mobilityTask.tag()} selfinsured ${mobilityTask.rental().isSelfInsured()} insurance id ${mobilityTask.rental().insurance().id()}`);
        if (mobilityTask.rental().isSelfInsured() || mobilityTask.rental().insurance().id() === null) {
            return Q.resolve(mobilityTask);
        }
        logger.info(`aahelp2.services.getCoverDetails ${mobilityTask.tag()} get hire ${mobilityTask.rental().insurance().id()}`);
        return restfulRequest(
            _postQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/insurance/details`, authToken, {
                mobilityTask
            })
        )
            .then((coverResponse) => coverResponse)
            .catch((err) => {
                return Q.reject(err.data && err.data.errors ? err.data : err);
            });
    },
    addDeleteCoverToQueue: function (mobilityTask) {
        let supNetCode = mobilityTask.rental().insurance().supNetworkCode();
        let label = eventsBusQueueConstant.HIRE_TYPE.HIRE_CANCEL;
        let body = JSON.stringify({
            insuranceRefId: mobilityTask.rental().insurance().id(),
            subject: label,
            supNetCode
        });

        logger.info(`aahelp2.services.addCovertoQueue ${mobilityTask.tag()} selfinsured ${mobilityTask.rental().isSelfInsured()} insurance id ${mobilityTask.rental().insurance().id()}`);
        if (mobilityTask.rental().isSelfInsured() || mobilityTask.rental().insurance().id() === null) {
            return Q.resolve(mobilityTask);
        }
        logger.info(`aahelp2.services.addCovertoQueue  delete hire ${mobilityTask.rental().insurance().id()}`);
        return pushMessageToQueue(body, label)
            .then(() => {
                logger.info(
                    `hire Inbound Message :: For task id ${mobilityTask.id()}, transaction type - ${eventsBusQueueConstant.HIRE_TYPE.HIRE_CANCEL} is Success. Message sent to queue successfully!`
                );
                _resetRentalInsuranceDetails(mobilityTask);
                return mobilityTask;
            })
            .catch((err) => {
                logger.info(
                    ` hire Inbound Message :: For task id ${mobilityTask.id()}, transaction type - ${eventsBusQueueConstant.HIRE_TYPE.HIRE_CANCEL} is failed to store message in a service bus queue`
                );
                return Q.reject(err);
            });
    },
    addDrivers: (mobilityTask, authToken) => {
        if (mobilityTask.rental().isSelfInsured()) {
            return Q.resolve(true);
        }

        let supNetCode = mobilityTask.rental().insurance().supNetworkCode();

        return restfulRequest(
            _postQueryBuilder(`/api/car-hire-insurance-service/${supNetCode}/add/drivers`, authToken, {
                rental: mobilityTask.rental().toJSON()
            })
        ).then((result) => {
            mobilityTask.rental().mainDriver(new CarHireDriver(result.mainDriver));
            mobilityTask.rental().additionalDrivers(result.additionalDrivers.map((driver) => new CarHireDriver(driver)));
            return mobilityTask;
        });
    },
    url: () => aah2Proxy,
    getResource: (id, authCode) => {
        const query = JSON.stringify({
            resource: {
                id: id
            }
        });
        const body = {
            primeQuery: query
        };

        if (!_.isNumber(id) || id < 0) {
            return Q.resolve({});
        }

        return restfulRequest(_postQueryBuilder('/api/prime-proxy/get', authCode, body)).then((resp) => {
            return resp.data.primeResp ? JSON.parse(resp.data.primeResp).resource : null;
        });
    },
    getResourceHistory: (resourceID, authcode) => {
        const body = {
            primeQuery: JSON.stringify({
                resourceAudit: {
                    resourceId: resourceID,
                    startTime: moment().subtract(1, 'months'),
                    endTime: new Date()
                }
            })
        };
        return restfulRequest(_postQueryBuilder('/api/prime-proxy/get', authcode, body))
            .then((resp) => {
                return resp.data.primeResp ? JSON.parse(resp.data.primeResp) : Q.reject(JSON.parse(resp.data.primeError));
            })
            .catch((err) => {
                return err;
            });
    },
    setResource: (resourceDetails, authCode) => {
        let query, body;
        resourceDetails.sequence++;
        resourceDetails.operatorId = authCode.operatorId;
        query = {
            updateResource: resourceDetails
        };
        body = {
            primeQuery: JSON.stringify(query)
        };

        return restfulRequest(_postQueryBuilder('/api/prime-proxy/put', authCode, body)).then((resp) => {
            return resp.data.primeResp ? JSON.parse(resp.data.primeResp) : Q.reject(JSON.parse(resp.data.primeError));
        });
    },
    resourcePlan: (resourceId, authCode) => {
        let query, body;

        query = {
            resourcePlan: {
                resourceId: resourceId
            }
        };
        body = {
            primeQuery: JSON.stringify(query)
        };
        console.log(body);
        return restfulRequest(_postQueryBuilder('/api/prime-proxy/get', authCode, body))
            .then((resp) => {
                return resp.data.primeResp ? JSON.parse(resp.data.primeResp) : Q.reject(JSON.parse(resp.data.primeError));
            })
            .catch((err) => {
                return err;
            });
    },
    /**
     * create task audit entry ..
     * @param  {Object} params details we want to pass
     * @param {number} params.taskId
     * @param {number} params.custReqId
     * @param {Object} params.authCode
     * @param {string} params.text we want to log
     */
    taskAudit: (params) => {
        const primeQuery = {
                callInfo: {
                    entityId: 31,
                    customerRequestId: params.custReqId,
                    taskId: params.taskId,
                    operatorId: params.authToken.operatorId,
                    remarkTypeId: 8,
                    text: params.text
                }
            },
            body = {
                primeQuery: JSON.stringify(primeQuery)
            };

        return restfulRequest(_postQueryBuilder('/api/prime-proxy/put', params.authToken, body)).then(() => {
            return true;
        });
    },
    docRender: (params) => {
        const tmpFile = `${process.env.HOME}/tmp/edocs-render-${params.mobilityTask.id()}.${nanoid(10)}.${params.operation}`;
        const path = `/api/edocs-service/render/${params.operation === 'start' ? 'checkout' : 'checkin'}`;

        const payload = {
            task: params.mobilityTask.toJSON()
        };

        fs.writeFileSync(tmpFile, JSON.stringify(payload));
        return restfulRequest(_postQueryBuilder(path, params.authToken, payload))
            .then((eDocsResp) => {
                fs.unlinkSync(tmpFile);
                return eDocsResp.data; // we get back the document name we saved ..
            })
            .catch((err) => {
                if (params.operation === 'start') {
                    let body = params;

                    let subject = eventsBusQueueConstant.DOCUMENTS.CHECKOUT_DOCUMENT;

                    let eventsRepository = new EventsBusQueueRepository();

                    let queueName = eventsBusQueueConstant.QUEUE_NAME.ENTERPRISE_OUTBOUND;

                    return eventsRepository
                        .write(queueName, { body, subject })

                        .then((res) => {
                            logger.info(` Message :: For task id ${body.taskId} , transaction type - ${subject} is Success. Message sent to queue successfully!`);
                            fs.unlinkSync(tmpFile);
                            return res;
                        })

                        .catch((err) => {
                            logger.error('error message is pushed to queue', err);

                            return Q.reject({
                                msg: `CheckOut failed for task ${body.taskId}`
                            });
                        });
                } else {
                    logger.error(`error at docRender`, err);

                    return Q.reject(err);
                }
            });
    },

    readMobTask: (params) => {
        const primeQuery = {
                genericTask: {
                    id: params.id
                }
            },
            body = {
                primeQuery: JSON.stringify(primeQuery)
            };
        return restfulRequest(_postQueryBuilder('/api/prime-proxy/put', params.authToken, body)).then(() => {
            return true;
        });
    },
    closeNonApprovedOpenExtensions: (taskId, authToken) => {
        logger.info(`aahelp2.service.closeNonApprovedOpenExtensions :: updating taskID ${taskId}`);
        return restfulRequest(_getQueryBuilder(`/api/extension-service/extensions/clean/${taskId}`, authToken, null))
            .then(() => {
                logger.info(`aahelp2.service.closeNonApprovedOpenExtensions :: updated taskId ${taskId}`);
                return Q.resolve(true);
            })
            .catch((err) => {
                logger.error(`aahelp2-service.closeNonApprovedOpenExtensions:: failed to update taskId ${taskId} :: ${err.msg}. Resolving anyway.`);
                return Q.resolve(true);
            });
    },
    getVehicleByReg: (reg, authToken) => {
        const url = `/api/vehicle-details-service/vehicle/${reg}`;
        const defered = Q.defer();

        restfulRequest(_getQueryBuilder(url, authToken, null))
            .then((resp) => {
                logger.info(`aahelp2-service.getVehicleByReg:: fetching vehicle details success for vehicle reg :: ${reg}`);
                defered.resolve(resp.data);
            })
            .catch((errors) => {
                logger.info(`aahelp2-service.getVehicleByReg:: fetching vehicle details failed for vehicle reg :: ${reg}`);
                defered.reject(errors);
            });

        return defered.promise;
    },
    writeCarRentalReconcileTask: _writeCarRentalReconcileTask,
    sendEmail: (body, url, authToken) => {
        logger.info(`aahelp2-service.sendEmail :: /api/edocs-service${url} - ${JSON.stringify(body)}`);
        return restfulRequest(_postQueryBuilder(`/api/edocs-service${url}`, authToken, body))
            .then((res) => {
                logger.info(`aahelp2-service.sendEmail :: email sent success - ${JSON.stringify(res)}`);
                return res;
            })
            .catch((err) => {
                logger.error(`aahelp2-service.sendEmail :: Failed to send email - error :: ${JSON.stringify(err)}`);
                return Q.reject(err);
            });
    },
    mockProxy: (val) => {
        // need this for unit tests .
        aah2Proxy = val;
    },

    getBreakdownTask: (taskId, authToken) => {
        return restfulRequest(_getQueryBuilder(`/api/task-service/task/${taskId}`, authToken, null))
            .then((resp) => {
                logger.info(`aahelp2.service.readTask :: found ${resp.data}`);
                return resp.data;
            })
            .catch((err) => {
                logger.error(`aahelp2-service.readTask:: failed to read task id ${taskId} :: ${err.msg}`);
            });
    },

    checkoutMessageToSBQ: (mobilityTask, authToken) => {
        let eventsRepository = new EventsBusQueueRepository();
        // In DB table => retailerId is a number
        // For RAF => mobilityTask.rental().repairLocation().id()
        // For Enterprise => mobilityTask.rental().repairLocation().id() => it could be number or string, thats why assigning -1 if its otherthan number to avoid exceptions
        let retailerId = _.isNumber(mobilityTask.rental().repairLocation().id()) ? mobilityTask.rental().repairLocation().id() : -1;
        let payload = {
            taskId: mobilityTask.id(),
            srcRentalTaskId: mobilityTask.rental().srcRentalTaskId(),
            custReqId: mobilityTask.customerRequestId(),
            approveUntilDate: mobilityTask.schedule().complete(),
            custGroup: mobilityTask.entitlement().customerGroup().code(),
            retailerId,
            extensionDuration: Math.round(mobilityTask.fault().repairMinutes() / (24 * 60)),
            operatorId: authToken.operatorId,
            authToken: authToken.jwt
        };

        let body = JSON.stringify(payload);
        let subject = eventsBusQueueConstant.CHECKOUT;

        return eventsRepository
            .write(Queue.HIRE_AUTO_EXTEND_PROCESSOR, { body, subject })
            .then((res) => {
                logger.info(`aahelp2.services.checkout :: For task id ${mobilityTask.id()} , transaction type - ${subject} is Success. Message sent to queue successfully!`);
                return res;
            })
            .catch((err) => {
                logger.error(`aahelp2.services.checkout:: unable to send message in Service bus queue for TaskId= ${mobilityTask.id()} , transaction type - ${subject}::`, err);
                return Q.reject(err);
            });
    }
};
