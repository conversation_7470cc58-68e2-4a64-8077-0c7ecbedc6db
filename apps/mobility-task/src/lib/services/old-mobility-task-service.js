'use strict';
/**@module services-mobility-task */
const rentalDataSvc = require('./rental-data.service'),
    Q = require('q'),
    _ = require('lodash'),
    taskAuditSvc = require('./task-audit.service'),
    errors = require('../constants/error.constant'),
    logger = require('winston'),
    aahelp2Service = require('./aahelp2.service'),
    hireVehicleSvc = require('./hire-vehicle.service'),
    HireScheduleRequest = require('@aa/mobility-models-common/lib/hire-schedule-request.model'),
    MobilityTask = require('@aa/mobility-models-common/lib/mobility-task.model'),
    HireSite = require('@aa/mobility-models/lib/hire-site.model'),
    cshResource = require('./csh-resource.service'),
    cshSupplier = require('./csh-supplier.service'),
    createReasonFactory = require('@aa/malstrom-models/lib/factories/create-reason.factory'),
    cshHireConditionSvc = require('./csh-hire-condition.service'),
    uploadFilesSvc = require('./upload-files.service'),
    InsurerErrorsFactory = require('@aa/mobility-models/lib/factories/insurer-errors.factory'),
    MobilityInsurance = require('@aa/mobility-models/lib/mobility-insurance.model'),
    TaskWriteResponse = require('@aa/malstrom-models/lib/task-write-response.model'),
    EnterpriseSubHire = require('@aa/mobility-models/lib/sub-hire.enterprise.model'),
    Resource = require('@aa/malstrom-models/lib/resource.model'),
    hireDocsSvc = require('./hire-documents.service'),
    thirdPartySupplierSvc = require('./third-party-supplier.service'),
    supplierSvc = require('./supplier.service'),
    businessRuleSvc = require('./entitlement.service'),
    extensionSvc = require('./extension.service'),
    Queue = require('better-queue'),
    enterpriseHireService = require('./enterprise-hire.service'),
    SubHire = require('@aa/mobility-models/lib/sub-hire.model'),
    AAHelpService = require('../services/aahelp2.service'),
    Supplier = require('@aa/malstrom-models/lib/supplier.model'),
    generalConstant = require('../constants/general.constant'),
    EnterpriseRentedVehicle = require('@aa/mobility-models/lib/enterprise-rented-vehicle.model'),
    notificationService = require('./../services/notification.service'),
    notificationTypes = require('./../enums/notification-types.enum'),
    incidentManaged = require('./incident-managed.service'),
    taskTag = require('../factories/task-tag.factory');
const { Vehicle } = require('@aa/data-models/common');

const _checkoutQueue = new Queue(
    (details, cb) => {
        const _details = details;
        logger.info(`mobility-task-service._checkoutQueue :: processing ${_details.taskId}`);
        uploadFilesSvc
            .process(_details)
            .then(() => {
                logger.info(`mobility-task-service._checkoutQueue :: processing ${_details.taskId} done upload`);
                _details.mobilityTask.rental().checkoutReport(_details.report);
                _details.mobilityTask.rental().checkoutSignatures(_details.signatures);
                return cshHireConditionSvc.checkOut(_details);
            })
            .then(() => {
                return rentalDataSvc.writeRedisOnly(_details.mobilityTask);
            })
            .then(() => {
                logger.info(`mobility-task-service._checkoutQueue :: processing ${_details.taskId} wrote checkout report`);
                cb();
            })
            .catch((err) => {
                logger.error(`mobility-task-service._checkoutQueue :: processing ${_details.taskId}`, err);
                cb();
            });
    },
    {
        concurrent: 3
    }
);

const _checkinQueue = new Queue(
    (details, cb) => {
        const _details = details;
        logger.info(`mobility-task-service._checkinQueue :: processing ${_details.taskId}`);
        uploadFilesSvc
            .process(_details)
            .then(() => {
                logger.info(`mobility-task-service._checkinQueue :: processing ${_details.taskId} done upload`);
                _details.mobilityTask.rental().checkinReport(_details.report);
                _details.mobilityTask.rental().checkinSignatures(_details.signatures);
                return cshHireConditionSvc.checkIn(_details);
            })
            .then(() => {
                return rentalDataSvc.writeRedisOnly(_details.mobilityTask);
            })
            .then(() => {
                return hireVehicleSvc.relocateResource(_details.mobilityTask, _details.authToken);
            })
            .then(() => {
                logger.info(`mobility-task-service._checkinQueue :: processing ${_details.taskId} wrote checkin report`);
                cb();
            })
            .catch((err) => {
                logger.error(`mobility-task-service._checkinQueue :: processing ${_details.taskId}`, err);
                cb();
            });
    },
    {
        concurrent: 3
    }
);

function _write(mobilityTask, authToken) {
    let _taskWriteResp;

    return aahelp2Service
        .writeTask(mobilityTask, authToken)
        .then((taskWriteResp) => {
            _taskWriteResp = taskWriteResp;
            _taskWriteResp.appointment(mobilityTask.appointment()); // keep that the same ...
            logger.info(`mobility-task-service.write :: write outcome prime ${taskWriteResp.status()}`);
            if (!mobilityTask.rental().insurance().id()) {
                mobilityTask.rental().mainDriver().softReset();
                mobilityTask
                    .rental()
                    .additionalDrivers()
                    .forEach((driver, idx) => {
                        setTimeout(() => {
                            driver.softReset();
                        }, 100 * idx);
                    });
            }
            return rentalDataSvc.write(mobilityTask, authToken);
        })
        .then((repoResp) => {
            logger.info(`mobility-task-service.write :: write repo ${repoResp}`);
            return _taskWriteResp;
        })
        .catch((err) => {
            logger.error(`mobility-task.write:: add task id ${mobilityTask.id()} seqNo ${mobilityTask.sequence()}:: ${err.msg}`);
            return Q.reject(errors.create(2001, 'write error', err));
        });
}

function _read(id, authToken) {
    logger.info(`MobilityTask.read :: task id ${id} `);

    return Q.all([aahelp2Service.readTask(id, authToken), rentalDataSvc.read(id, authToken)])
        .spread((aah2Task, rental) => {
            const model = new MobilityTask(aah2Task.toJSON()); // lazy cloning ...
            model.rental(rental);
            return model;
        })
        .catch((err) => {
            logger.error(`mobility-task.read:: read task id ${id} :: ${err.msg}`);
            return Q.reject(errors.create(2002, 'read error', err));
        });
}

/**
 * To get Supplier details for Enterprise Car Hire containing profiles details based on hirerNetworkId
 * @param  {Object} mobilityTask
 * @param  {Object} authToken
 * @return array of Suppliers
 */
function _getEnterpriseSupplier(mobilityTask, authToken) {
    // TODO:: Need to remove hard coded value once data is setup for aall suppliers.
    let hirerNetworkId = mobilityTask.entitlement().customerGroup().hirerNetworkId();
    return supplierSvc
        .getEnterpriseSupplier(hirerNetworkId, authToken)
        .then((suppliers) => {
            let logicalId = suppliers[0].resourceId;
            mobilityTask.rental().thirdPartyHire().supplier(new Supplier(suppliers[0]));
            _setEnterpriseResourceId(mobilityTask, logicalId);
            return aahelp2Service.writeTask(mobilityTask, authToken);
        })
        .catch((err) => {
            logger.error(`supplier-service:: unable to fetch supplier for hirerNetworkId ${hirerNetworkId} :: ${err.message}`); //TODO
            return Q.reject({
                msg: `Unable to fetch supplier for hirerNetworkId ${hirerNetworkId} :: ${err.message}`
            });
        });
}

function _setEnterpriseResourceId(mobilityTask, logicalId) {
    mobilityTask.schedule().resource(
        new Resource({
            id: logicalId
        })
    );
    mobilityTask.rental().hireVehicle().resourceId(logicalId);
    mobilityTask.rental().thirdPartyHire().logicalResourceId(logicalId);
    mobilityTask.rental().thirdPartyHire().hireVehicle().resourceId(logicalId);
    mobilityTask.status('PLAN');
}

function _delayPromise(duration) {
    return new Promise(function (resolve) {
        setTimeout(function () {
            resolve();
        }, duration);
    });
}

function _fetchUpdatedMobilityTask(taskId, authToken) {
    /**
     * Adding this delay after discussing with eva-lite and Agile team as
     * Sometimes, system is not able to read updated task in first attempt (i.e. without delay)
     *
     */
    return _read(taskId, authToken)
        .then((mobilityTask) => {
            return Q.resolve(mobilityTask);
        })
        .catch(() => {
            logger.info('mobilityTaskService.fetchUpdatedMobilityTask:: Error while immediate read - Adding delay in read');
            return _delayPromise(500).then(() => {
                return _read(taskId, authToken);
            });
        });
}

/**
 * checkout aka vehicle hire start
 * @param  {Object} details
 * @param {Number} details.id of the task we process
 * @param {MobilityTask=} details.mobilityTask persists details of the task we read from both prime and MB_HIRE_TASK
 *     table
 * @param {ConditionReport} details.report of the vehicle condition at checkout
 * @param {HireSignatures} details.signatures of the main driver and retailer to received the vehicle
 * @param {String} details.operation=start identifies that we are starting the hire
 * @param {Object} details.authToken details of the end user credentials
 * @return {Promise}
 */
function _checkOut(details) {
    logger.info(`mobility-task-service._checkOut::checking out ${details.taskId} `);
    return _read(details.taskId, details.authToken)
        .then((mobilityTask) => {
            taskAuditSvc.write(mobilityTask.id(), mobilityTask.customerRequestId(), `Hire start ${mobilityTask.rental().hireVehicle().regNo()}`, details.authToken);

            logger.info(`mobility-task-service._checkOut::checking out ${details.taskId} status ${mobilityTask.status()}`);
            if (mobilityTask.status() !== 'HEAD' && mobilityTask.status() !== 'GDET') {
                logger.error(`mobility-task-service._checkOut::checking out ${details.taskId} NOT in HEAD OR GDET`);
                return Q.reject({
                    msg: `task in state of ${mobilityTask.status()} can not proceed to checkout`
                });
            }
            details.mobilityTask = mobilityTask;

            details.custReqId = mobilityTask.customerRequestId(); // add that here becuase we need it
            return aahelp2Service.startCarHire(details.mobilityTask, details.authToken);
        })
        .then(() => {
            if (details.mobilityTask.rental().isThirdPartyHireSet()) {
                details.mobilityTask.status('GARR'); // special for third party
                details.mobilityTask.schedule().arrive(new Date()); // need to do this for third party hires ..
            } else {
                details.mobilityTask.status('HIRE');
            }
            logger.info(`mobility-task-service._checkOut::update prime out ${details.taskId} status ${details.mobilityTask.status()}`);
            return aahelp2Service.writeTask(details.mobilityTask, details.authToken);
        })
        .then((taskWriteResp) => {
            details.mobilityTask.status(taskWriteResp.status());
            details.mobilityTask.schedule(taskWriteResp.schedule());
            // render service needs these..
            details.mobilityTask.rental().checkoutReport(details.report);
            details.mobilityTask.rental().checkoutSignatures(details.signatures);
            return aahelp2Service.docRender(details);
        })
        .then((docName) => {
            details.report.docName(docName);
            details.mobilityTask.rental().checkoutReport().docName(docName);
            _checkoutQueue.push(details);
            return {
                report: hireDocsSvc.generateSASToken(docName)
            };
        });
}

/**
 * check in aka vehicle returns from hire ..
 * @param  {Object} details
 * @param {Number} details.id of the task we process
 * @param {MobilityTask=} details.mobilityTask persists details of the task we read from both prime and MB_HIRE_TASK
 *     table
 * @param {ConditionReport} details.report of the vehicle condition
 * @param {HireSignatures} details.signatures of the main driver and retailer to received the vehicle
 * @param {String} details.operation=end identifies that we are
 * @param {Object} details.authToken details of the end user credentials
 * @return {Promise}
 */
function _checkIn(details) {
    logger.info(`mobility-task-service._checkIn::checking in ${details.taskId} `);
    return _read(details.taskId, details.authToken)
        .then((mobilityTask) => {
            details.mobilityTask = mobilityTask;

            taskAuditSvc.write(mobilityTask.id(), mobilityTask.customerRequestId(), `Hire end ${mobilityTask.rental().hireVehicle().regNo()}`, details.authToken);

            details.custReqId = mobilityTask.customerRequestId(); // add that here becuase we need it
            logger.info(`mobility-task-service._checkIn::checking in ${details.taskId} status ${mobilityTask.status()}`);
            return aahelp2Service.endCarHire(mobilityTask.rental(), details.authToken);
        })
        .then((endCarHireResponse) => {
            details.insuranceOption = endCarHireResponse.insuranceOption;
            logger.info(`mobility-task-service._checkIn::checking in ${details.taskId} doing coopers`);
            return aahelp2Service.completeTask(details.mobilityTask, 'RR', details.authToken);
        })
        .then(() => {
            // trigger notification for allocation request notification only for TPS
            const rental = details.mobilityTask.rental();
            if (['AA', 'RAF', 'OUV', 'OU'].includes(rental.hireVehicle().supplierTypeCode())) {
                return Promise.resolve();
            }

            const payload = {
                date: new Date().getTime(),
                type: notificationTypes.TPS_VEHICLE_CHECKED_IN,
                taskId: details.taskId
            };

            const dropOffLocation = rental.dropOffLocation();
            const region = dropOffLocation && dropOffLocation.regionCode() ? dropOffLocation.regionCode() : null;

            if (region) {
                payload.region = region;
            }

            // if TPS create notification
            return notificationService.update(payload);
        })
        .then(() => {
            return aahelp2Service.closeNonApprovedOpenExtensions(details.taskId, details.authToken);
        })
        .then(() => {
            details.mobilityTask.rental().checkinReport(details.report);
            details.mobilityTask.rental().checkinSignatures(details.signatures);
            return aahelp2Service.docRender(details);
        })
        .then((docName) => {
            details.report.docName(docName);
            details.mobilityTask.rental().checkinReport().docName(docName);
            _checkinQueue.push(details);
            return {
                report: hireDocsSvc.generateSASToken(docName)
            };
        });
}

/**
 * Validate passed arrive/completion date and return msg
 * @param {Date} scheduleDate
 * @param {Date} createdDate
 * @param {Date} arrivedDate
 * @returns {Object}
 */
function _selfCheckOutDateValidation(scheduleDate, createdDate, arrivedDate) {
    let checkOutDate = {
        isValid: false,
        msg: ''
    };

    if (scheduleDate instanceof Date && !isNaN(scheduleDate)) {
        // checking date format
        if (new Date(scheduleDate).getTime() < new Date().getTime()) {
            // arrive date or completion date should not be in future
            if (new Date(createdDate).getTime() < new Date(scheduleDate).getTime()) {
                // arrive date or completion date should not be smaller than task creation date
                if (arrivedDate && new Date(scheduleDate).getTime() < new Date(arrivedDate).getTime()) {
                    // completion date should be bigger than arrive date
                    checkOutDate.msg = 'datetime cannot be smaller than breakdown datetime.';
                } else {
                    checkOutDate.isValid = true;
                    checkOutDate.msg = 'All checks passed!';
                }
            } else {
                checkOutDate.msg = 'datetime cannot be smaller than breakdown datetime.';
            }
        } else {
            checkOutDate.msg = 'datetime cannot be greater than current datetime.';
        }
    } else {
        checkOutDate.msg = 'is not a valid date format.';
    }
    return checkOutDate;
}

function _fetchSupplierFromTask(taskId, authToken) {
    let hirerNetworkId;
    return aahelp2Service
        .readTask(taskId, authToken)
        .then((parentTask) => {
            hirerNetworkId = parentTask.entitlement().customerGroup().hirerNetworkId();
            return supplierSvc.getEnterpriseSupplier(hirerNetworkId, authToken);
        })
        .then((supplier) => {
            let hireSupplier = {
                transactionId: taskId,
                tradingPartnerId: supplier.supExtRef1,
                officeId: supplier.supExtRef3
            };
            return hireSupplier;
        })
        .catch((err) => {
            logger.error(`supplier-service:: unable to fetch supplier for hirerNetworkId ${hirerNetworkId} :: ${err.message}`);
            return Q.reject({
                msg: `Unable to fetch supplier for hirerNetworkId ${hirerNetworkId} :: ${err.message}`
            });
        });
}

module.exports = {
    /**
     * write a mobility task. It writes to both aahelp as well as mobility schema. If task is to be written the first
     * time after a 'clone' the system is calling addTask function
     * @param  {MobilityTask} mobilityTask
     * @param  {string} authToken    jwt token
     * @return {Promise}              on sucess return TaskWriteResponse
     */
    write: (mobilityTask, authToken) => {
        if (mobilityTask.indicators().incidentManaged()) {
            mobilityTask.indicators().incidentManaged(false);
        }

        if (mobilityTask.status() === 'CHCK' && mobilityTask.indicators().authorised() && mobilityTask.rental().isThirdPartyHireSet() && mobilityTask.rental().insurance().isAuthorised()) {
            //mobilityTask.status('GDET');
            mobilityTask.indicators().tel(true);
        }
        return _write(mobilityTask, authToken);
        //            .then((primeResponse) => _resource2Head(primeResponse, mobilityTask, authToken));
    },

    /**
     * read mobility task hireScheduleReq
     * @param {number} id of that task to read
     * @param {string} authToken string jwt token
     * @return {Promise}    on success return MobilityTask
     */
    read: (id, authToken) => {
        return _read(id, authToken).then((task) => {
            hireDocsSvc.setAzureLinks(task);
            return task;
        });
    },

    /**
     * read rental task details
     * @param {number} id of that task to read
     * @param {Object} authToken string jwt token
     * @return {Promise}    on success return CarRental
     */
    readRental: rentalDataSvc.read,

    cloneAndSplit: (hireScheduleReq, authToken) => {
        let _parentTask = null,
            _hireTask = null;
        return aahelp2Service
            .readTask(hireScheduleReq.taskId, authToken)
            .then((parentTask) => {
                _parentTask = parentTask;

                return aahelp2Service.cloneTask(parentTask, createReasonFactory.carHire(), authToken);
            })
            .then((hireTask) => {
                _hireTask = hireTask;

                if (hireScheduleReq.mainDriver) {
                    hireTask.rental().mainDriver(hireScheduleReq.mainDriver);
                }

                hireTask.rental().additionalDrivers(hireScheduleReq.additionalDrivers || []);

                return _write(_hireTask, authToken); //aahelp2Service.addTask(_hireTask, _parentTask, authToken);
            })
            .then((taskWriteResp) => {
                return taskWriteResp;
            });
    },

    /**
     * @typedef {}
     */

    /**
     * create new mobility hire task
     * @param  {Object} hireScheduleReq
     * @param {number} hireScheduleReq.taskId source task id
     * @param {CarHireDriver} hireScheduleReq.mainDriver identifies main driver
     * @param {Array.<CarHireDriver>} hireScheduleReq.additionalDrivers list of addtional drivers
     * @param {number} hireScheduleReq.patrolId itenify patrol who requests task to be created
     * @param  {string} authToken [description]=
     * @return {Promise<Object>} [description]
     */
    create: (hireScheduleReq, authToken) => {
        const createReason = createReasonFactory.carHire();
        let _mobilityTask = null;
        return aahelp2Service
            .readTask(hireScheduleReq.taskId, authToken)
            .then((parentTask) => {
                return aahelp2Service.cloneTask(parentTask, createReason, authToken);
            })
            .then((mobilityTask) => {
                _mobilityTask = mobilityTask;
                _mobilityTask.status('INIT'); // wonder if this will work ...
                mobilityTask.rental().mainDriver(hireScheduleReq.mainDriver);
                if (Array.isArray(hireScheduleReq.additionalDrivers)) {
                    hireScheduleReq.additionalDrivers.forEach((driver, idx) => {
                        // create artifitial -ve ids
                        driver.id(-idx);
                        // indicates that entry has not been run against the insurer
                        // while alos allowing driver ui to identify the entries...
                    });
                    mobilityTask.rental().additionalDrivers(hireScheduleReq.additionalDrivers);
                }

                if (enterpriseHireService.isEnableEnterpriseHire() && hireScheduleReq.fleetType === generalConstant.HIRE_TYPES.ENTERPRISE.toLowerCase()) {
                    // Add additionalDetails and location details into rental object coming from evalite
                    mobilityTask.rental().thirdPartyHire(new SubHire());
                    mobilityTask.rental().thirdPartyHire().authorityCode(mobilityTask.id());
                    mobilityTask.rental().hireVehicle().supplierTypeCode('ENTERPRISE');
                    mobilityTask.rental().thirdPartyHire().hireVehicle().supplierTypeCode('ENTERPRISE'); //TODO:isEnterpise true
                    if (hireScheduleReq.patrolId) {
                        mobilityTask.operatorId(hireScheduleReq.patrolId);
                    }
                    if (hireScheduleReq.additionalDetails) {
                        mobilityTask.rental().additionalDetails(hireScheduleReq.additionalDetails);
                    }
                    if (hireScheduleReq.pickupLocation) {
                        /* isEnterpriseBranch : For Evalite, we are getting supplierId for Enterprise locations.
                                                For other locations getting 0, null or undefined as supplierId
                        */
                        let location = hireScheduleReq.pickupLocation;
                        let supplierIds = [0, null, undefined];
                        let collectLocation = {
                            id: location.supplierId ? location.supplierId : null,
                            address: location.area ? location.area : null,
                            location: {
                                latitude: location.latLong ? (location.latLong.lat ? location.latLong.lat : null) : null,
                                longitude: location.latLong ? (location.latLong.long ? location.latLong.long : null) : null
                            },
                            region: location.location ? location.location : null,
                            regionCode: location.postalCode ? location.postalCode : null,
                            isEnterpriseBranch: supplierIds.includes(location.supplierId) ? false : true
                        };
                        mobilityTask.rental().collectLocation(new HireSite(collectLocation));
                    }
                }

                return _write(mobilityTask, authToken);
            })
            .then((taskWriteResp) => {
                return {
                    response: {
                        taskId: _mobilityTask.id(),
                        status: taskWriteResp.status(),
                        msg: taskWriteResp.primeMsg(),
                        result: taskWriteResp.result()
                    },
                    mobilityTask: _mobilityTask
                };
            });
    },
    /**
     * schedule
     * @param  {HireScheduleRequest} hireScheduleReq contains details of the hire
     * @param  {Object} authToken
     * @return {Promise}           on success return
     */
    schedule: (hireScheduleReq, authToken) => {
        let _primeResponse = null,
            _mobilityTask = null,
            _insurerScope = null;

        //set authorised indicator to false everytime you schedule hire car
        hireScheduleReq.mobilityTask().indicators().authorised(false);

        // keep track
        _mobilityTask = hireScheduleReq.mobilityTask();

        if (_mobilityTask.rental().hireVehicle().regNo()) {
            taskAuditSvc.write(_mobilityTask.id(), _mobilityTask.customerRequestId(), `Removing insurance on ${_mobilityTask.rental().hireVehicle().regNo()}`, authToken);
        }

        return aahelp2Service
            .deleteCover(_mobilityTask, authToken)
            .then((delMobilityTask) => {
                _mobilityTask = delMobilityTask;

                // make sure incident managment indicator is false .. .
                incidentManaged.check(_mobilityTask);

                switch (hireScheduleReq.searchType()) {
                    case HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY:
                    case HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_THRIFTY:
                    case HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE:
                    case HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_LCH:
                        thirdPartySupplierSvc.setResource(hireScheduleReq);
                        break;
                    case HireScheduleRequest.SEARCH_TYPES.VEHICLE:
                        _mobilityTask.status('PLAN');
                        logger.info(
                            `mobility-task-service.schedule :: vehicle schedule current ${_mobilityTask.id()}: EXID ${_mobilityTask.appointment().exclusiveResourceId()}: RESID resid-${_mobilityTask
                                .schedule()
                                .resource()
                                .id()}:${_mobilityTask.status()}`
                        );
                        _mobilityTask.schedule().resource().id(_mobilityTask.appointment().exclusiveResourceId());
                        logger.info(
                            `mobility-task-service.schedule :: vehicle schedule req ${_mobilityTask.id()}: EXID ${_mobilityTask.appointment().exclusiveResourceId()}: RESID ${_mobilityTask
                                .schedule()
                                .resource()
                                .id()}:${_mobilityTask.status()}`
                        );
                        thirdPartySupplierSvc.removeCapability(hireScheduleReq);

                        _mobilityTask.rental().dropOffLocation(new HireSite());
                        _mobilityTask.rental().repairLocation(new HireSite());
                        _mobilityTask.recovery().destResourceId(null);

                        break;
                    case HireScheduleRequest.SEARCH_TYPES.LOCATION:
                        _mobilityTask.appointment().exclusiveResourceId(null);
                        _mobilityTask.schedule().resource(new Resource());
                        _mobilityTask.status('UNAC'); // change status in UNAC so prime searches for a resource
                        thirdPartySupplierSvc.removeCapability(hireScheduleReq);
                        break;
                    case HireScheduleRequest.SEARCH_TYPES.TIME:
                        break;
                    default:
                        break;
                }
                if (enterpriseHireService.isEnableEnterpriseHire() && hireScheduleReq.searchType() === HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE) {
                    return _getEnterpriseSupplier(_mobilityTask, authToken);
                } else {
                    return aahelp2Service.writeTask(_mobilityTask, authToken);
                }
            })
            .then((primeResponse) => {
                _primeResponse = primeResponse;

                logger.info(
                    `mobility-task-service.schedule :: prime resp ${_mobilityTask.id()}: RESID ${_primeResponse.schedule().resource().id()}:${_primeResponse.status()}:${_primeResponse.primeMsg()}`
                );

                if (_mobilityTask.rental().isThirdPartyHireSet()) {
                    if (_primeResponse.schedule().resource().id() !== _mobilityTask.rental().thirdPartyHire().logicalResourceId()) {
                        let isEnterpise = enterpriseHireService.isEnableEnterpriseHire() && hireScheduleReq.searchType() === HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE;
                        return Q.resolve({
                            hireVehicle: isEnterpise ? _mobilityTask.rental().thirdPartyHire().hireVehicle() : cshResource.getFailedResource()
                        });
                    }
                    _mobilityTask.rental().thirdPartyHire().hireVehicle().id(primeResponse.schedule().resource().id());
                    // for third party we know the vehicle
                    return Q.resolve({
                        hireVehicle: _mobilityTask.rental().thirdPartyHire().hireVehicle()
                    });
                }

                logger.info(`mobility-task-service.schedule :: task ${_mobilityTask.id()} searching for resource ${primeResponse.schedule().resource().id()}`);
                return cshResource.getVehicle({
                    resourceId: primeResponse.schedule().resource().id(),
                    managerId: primeResponse.schedule().resource().managerId(),
                    authToken: authToken,
                    rental: _mobilityTask.rental(),
                    supResourceId: _primeResponse.schedule().resource().managerId()
                });
            })
            .then((cshVehicleScope) => {
                let isOU;
                _mobilityTask.rental().hireVehicle(cshVehicleScope.hireVehicle);

                if (_mobilityTask.rental().isThirdPartyHireSet()) {
                    // ui has set this already ... so skip ..
                    return Q.resolve(_mobilityTask.rental().collectLocation());
                }

                // look for OU
                isOU = _.find(_primeResponse.schedule().resource().capabilities(), (item) => {
                    return item.id() === 159;
                });

                if (isOU) {
                    _mobilityTask.rental().hireVehicle().supplierTypeCode('OU');
                }

                if (_mobilityTask.rental().hireVehicle().regNo()) {
                    _primeResponse.appointment().exclusiveResourceId(cshVehicleScope.hireVehicle.id());
                    taskAuditSvc.write(_mobilityTask.id(), _mobilityTask.customerRequestId(), `Reserving vehicle ${_mobilityTask.rental().hireVehicle().regNo()}`, authToken);
                }

                logger.info(`mobility-task-service.schedule :: task ${_mobilityTask.id()} searching for manager id ${_primeResponse.schedule().resource().managerId()}`);
                return cshSupplier.read(_primeResponse.schedule().resource().managerId());
            })
            .then((cshSupplierScope) => {
                if (!_mobilityTask.rental().collectLocation().isSet()) {
                    _mobilityTask.rental().collectLocation(cshSupplierScope);
                }

                if (!_mobilityTask.rental().dropOffLocation().isSet()) {
                    _mobilityTask.rental().dropOffLocation(cshSupplierScope);
                }

                if (!_mobilityTask.rental().isThirdPartyHireSet() && !_mobilityTask.rental().repairLocation().isSet()) {
                    _mobilityTask.rental().repairLocation(cshSupplierScope);
                }

                if (!_mobilityTask.recovery().isDestResourceIdSet()) {
                    _mobilityTask.recovery().destResourceId(cshSupplierScope.id());
                }

                if (_mobilityTask.rental().hireVehicle().id()) {
                    _mobilityTask.schedule().arrive(_primeResponse.schedule().arrive());
                    _mobilityTask.schedule().complete(_primeResponse.schedule().complete());
                } else {
                    _mobilityTask.schedule().arrive(_mobilityTask.appointment().earliest());
                }

                return aahelp2Service.bookHireCar(_mobilityTask, authToken);
            })
            .then((insurerScope) => {
                if (enterpriseHireService.isEnableEnterpriseHire()) {
                    _insurerScope = insurerScope;
                    if (hireScheduleReq.searchType() === HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE && !hireScheduleReq.mobilityTask().rental().thirdPartyHire().hireVehicleRef()) {
                        return enterpriseHireService.rentalReservationRequest(hireScheduleReq).then((updatedHireScheduleRequest) => {
                            logger.info('Enterprise RentalReservationRequest Response :::' + updatedHireScheduleRequest);
                            const { thirdPartyHire } = updatedHireScheduleRequest.mobilityTask;
                            if (thirdPartyHire) {
                                _mobilityTask.rental().thirdPartyHire(new EnterpriseSubHire(_mobilityTask.rental().thirdPartyHire().toJSON()));
                                _mobilityTask.rental().thirdPartyHire().isEnterprise(thirdPartyHire.isEnterprise);
                                _mobilityTask.rental().thirdPartyHire().logicalResourceId(thirdPartyHire.logicalResourceId);
                                _mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber(thirdPartyHire.rentalConfirmationNumber);
                            }
                            return rentalDataSvc.write(_mobilityTask, authToken);
                        });
                    }
                }
                return Q.resolve(insurerScope);
            })
            .then((insurerScope) => {
                // persist insurance details ..
                if (insurerScope && insurerScope.id > 0) {
                    _mobilityTask.rental().insurance().id(insurerScope.id);
                    _mobilityTask.rental().insurance().insuredVehicleId(insurerScope.stockId);
                }

                if (_mobilityTask.rental().hireVehicle().regNo()) {
                    taskAuditSvc.write(_mobilityTask.id(), _mobilityTask.customerRequestId(), `Booked insurance on vehicle ${_mobilityTask.rental().hireVehicle().regNo()}`, authToken);
                }
                return rentalDataSvc.write(_mobilityTask, authToken);
            })
            .then(() => {
                let msg;

                if (_mobilityTask.rental().hireVehicle().regNo()) {
                    msg = `<b>Vehicle ${_mobilityTask.rental().hireVehicle().regNo()} reserved</b><br>`;
                } else if (_mobilityTask.rental().thirdPartyHire() && _mobilityTask.rental().thirdPartyHire().hireVehicleRef && _mobilityTask.rental().thirdPartyHire().hireVehicleRef()) {
                    msg = `<b>Enterprise Rental created : ${_mobilityTask.rental().thirdPartyHire().hireVehicleRef()}</b><br>`;
                } else if (
                    _mobilityTask.rental().thirdPartyHire() &&
                    _mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber &&
                    _mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber()
                ) {
                    msg = `<b>Enterprise Rental confirmed : ${_mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber()}</b><br>`;
                } else {
                    msg = `<b>unable to schedule a vehicle</b><br>`;
                }

                _primeResponse.primeMsg(`${msg} ${_primeResponse.primeMsg() || ''}`);

                if (
                    (hireScheduleReq.searchType() === HireScheduleRequest.SEARCH_TYPES.LOCATION || hireScheduleReq.searchType() === HireScheduleRequest.SEARCH_TYPES.VEHICLE) &&
                    _primeResponse.schedule().resource().id() > 0
                ) {
                    _primeResponse.appointment().exclusiveResourceId(_primeResponse.schedule().resource().id());
                    logger.info(
                        `mobility-task-service.schedule :: force excl res to appointment ${_mobilityTask.id()}: RESID ${_primeResponse
                            .appointment()
                            .exclusiveResourceId()}:${_primeResponse.status()}:${_primeResponse.primeMsg()}`
                    );
                }

                return {
                    taskWriteResponse: _primeResponse,
                    rental: _mobilityTask.rental()
                };
            })
            .catch((err) => {
                if (err.errors || err.id === 1008) {
                    // coopers error ...
                    logger.info('mobility-task-service.schedule :: task ${_mobilityTask.id()} coopers problem', err.errors);
                    _mobilityTask.sequence(_primeResponse.sequence()); // make sure we are at the same number ..
                    _mobilityTask.status('UNAC');
                    _mobilityTask.indicators().authorised(false);
                    return aahelp2Service
                        .writeTask(_mobilityTask, authToken)
                        .then((primeResponse) => {
                            const msg = `<b>Insurer msg:</b> ${InsurerErrorsFactory.hasError(err)} <br><b>Prime msg:</b> ${primeResponse.primeMsg()}`;
                            logger.error(`mobility-task-service.schedule :: task ${_mobilityTask.id()} prime resp ${msg}`);
                            primeResponse.primeMsg(msg);
                            return Q.resolve({
                                taskWriteResponse: primeResponse, // keeps ctrl simpler ..
                                rental: _mobilityTask.rental()
                            });
                        })
                        .catch((err) => {
                            logger.error(`mobility-task-service.schedule :: task ${_mobilityTask.id()} failed to unac task`, err);
                            return Q.reject(errors.create(2005, 'scheduling request failed', err));
                        });
                }
                logger.error(`mobility-task-service.schedule :: task ${_mobilityTask.id()} prime resp`, err);
                return Q.reject(err);
            });
    },
    /*resource: (details) => {
        return vehicleRepo.find(details.primeResponse.schedule().resource().id(), details.authToken)
            .then((rafVehicle) => {
                details.vehicle = rafVehicle;
                return details;
            });
    },*/

    /**
     * set the insurance type to be MobilityInsured ...
     * @param  {Object} details
     * @param {Number} details.taskId identifies task we want to get the insurance options for
     * @return {Promise}
     */
    /*    insuranceType: (details) => {
            return _read(details.taskId, details.authToken)
                .then((mobilityTask) => {
                    mobilityTask.rental().insurance(details.insurance);
                    return rentalDataSvc.writeRental(details.taskId, mobilityTask.rental());
                });
        },
    */

    selfInsured: (details) => {
        details.mobilityTask = details.task;
        return Q.when(details.task.rental().insurance().id() ? aahelp2Service.deleteCover(details.task, details.authToken) : details.task)
            .then(() => {
                details.insurance.id(details.mobilityTask.id());
                details.mobilityTask.rental().insurance(details.insurance);
                details.mobilityTask.rental().mainDriver(details.driverDetails);
                details.mobilityTask.rental().mainDriver().id(details.task.id());
                details.mobilityTask.rental().additionalDrivers([]); // more addtional drivers ..
                return rentalDataSvc.write(details.mobilityTask);
            })
            .then(() => {
                const primeResponse = new TaskWriteResponse({
                    primeMsg: '<b>Self insurance appointment confirmed</b>'
                });

                taskAuditSvc.write(
                    details.mobilityTask.id(),
                    details.mobilityTask.customerRequestId(),
                    `Self insuring vehicle ${details.mobilityTask.rental().hireVehicle().regNo()}`,
                    details.authToken
                );
                primeResponse.capabilities(details.mobilityTask.fault().capabilities());
                primeResponse.schedule(details.mobilityTask.schedule());
                primeResponse.appointment(details.mobilityTask.appointment());
                primeResponse.status(details.mobilityTask.status());
                primeResponse.sequence(details.mobilityTask.sequence());
                primeResponse.indicators(details.mobilityTask.indicators());
                return {
                    taskWriteResponse: primeResponse,
                    rental: details.mobilityTask.rental()
                };
            });
    },

    mobilityInsurance: (details) => {
        let notChanged;
        return _read(details.taskId, details.authToken)
            .then((mobilityTask) => {
                details.mobilityTask = mobilityTask;

                notChanged = !details.mobilityTask.rental().isSelfInsured();
                if (notChanged) {
                    return Q.resolve();
                }
                details.mobilityTask.rental().insurance(new MobilityInsurance());

                taskAuditSvc.write(
                    details.mobilityTask.id(),
                    details.mobilityTask.customerRequestId(),
                    `Removing self insurance on vehicle ${details.mobilityTask.rental().hireVehicle().regNo()}`,
                    details.authToken
                );

                return aahelp2Service.bookHireCar(details.mobilityTask, details.authToken);
            })
            .then((insurerScope) => {
                if (notChanged) {
                    return Q.resolve();
                }

                details.mobilityTask.rental().insurance().id(insurerScope.id);
                details.mobilityTask.rental().insurance().insuredVehicleId(insurerScope.stockId);

                // now remove any ids from the dirvers ..
                details.mobilityTask.rental().mainDriver().reset();
                details.mobilityTask.rental().mainDriver().id(-1);

                details.mobilityTask
                    .rental()
                    .additionalDrivers()
                    .forEach((driver, idx) => {
                        driver.reset();
                        driver.id(-2 - idx);
                    });

                return rentalDataSvc.write(details.mobilityTask, details.authToken);
            })
            .then(() => {
                const primeResponse = new TaskWriteResponse({
                    primeMsg: '<b>Appointment booked with the insurer</b>'
                });

                taskAuditSvc.write(
                    details.mobilityTask.id(),
                    details.mobilityTask.customerRequestId(),
                    `Booked insurance on vehicle ${details.mobilityTask.rental().hireVehicle().regNo()}`,
                    details.authToken
                );

                primeResponse.schedule(details.mobilityTask.schedule());
                primeResponse.appointment(details.mobilityTask.appointment());
                primeResponse.status(details.mobilityTask.status());
                primeResponse.sequence(details.mobilityTask.sequence());
                primeResponse.indicators(details.mobilityTask.indicators());
                return {
                    taskWriteResponse: primeResponse,
                    rental: details.mobilityTask.rental()
                };
            })
            .catch((err) => {
                if (err.errors) {
                    details.mobilityTask.status('UNAC');
                    details.mobilityTask.indicators().authorised(false);
                    return Q.allSettled([aahelp2Service.writeTask(details.mobilityTask, details.authToken), rentalDataSvc.write(details.mobilityTask, details.authToken)]).spread((taskWriteResp) => {
                        // ,repoResp is not needed so we don't have it ..
                        const msg = `<b>Appointment with insurer failed</b><br><b>Insurer msg : ${InsurerErrorsFactory.hasError(err)}</b></br>${taskWriteResp.primeMsg()}`;
                        taskWriteResp.primeMsg(msg);
                        return {
                            taskWriteResponse: taskWriteResp,
                            rental: details.mobilityTask.rental()
                        };
                    });
                }

                return Q.reject(err);
            });
    },
    saveRental: (details) => {
        return rentalDataSvc.write(details.mobilityTask);
    },

    checkOut: (details) => _checkOut(details),

    checkIn: (details) => _checkIn(details),

    /**
     * Check out selfCheckout hire task
     * @param {Object} details
     * @returns {Promise}
     */
    checkOutSelfCheckout: (details) => {
        logger.info(`mobility-task-service.checkOutSelfCheckout::checking out ${details.taskId} `);
        return _read(details.taskId, details.authToken)
            .then((mobilityTask) => {
                let rental = mobilityTask.rental();
                if (rental.isThirdPartyHireSet() && rental.thirdPartyHire().isSelfCheckout()) {
                    taskAuditSvc.write(mobilityTask.id(), mobilityTask.customerRequestId(), `Hire start ${mobilityTask.rental().hireVehicle().regNo()}`, details.authToken);

                    logger.info(`mobility-task-service.checkOutSelfCheckout::checking out ${details.taskId} status ${mobilityTask.status()}`);
                    if (!['HEAD', 'GDET'].includes(mobilityTask.status())) {
                        logger.error(`mobility-task-service.checkOutSelfCheckout::checking out ${details.taskId} NOT in HEAD OR GDET`);
                        let invalidTaskStatus = {
                            msg: `Task in state of ${mobilityTask.status()} can not proceed to checkout`
                        };
                        if (enterpriseHireService.isEnableEnterpriseHire() && mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                            invalidTaskStatus.enterpriseErrorRespnse = {
                                invalidTaskStatus: true
                            };
                        }
                        return Q.reject(invalidTaskStatus);
                    }

                    // To start SelfCheckout hire, set status to GARR
                    mobilityTask.status('GARR');

                    // Set arrive time
                    let dateValidation = _selfCheckOutDateValidation(details.arrive, mobilityTask.schedule().create());
                    if (dateValidation.isValid) {
                        mobilityTask.schedule().arrive(details.arrive);
                    } else {
                        logger.info(`mobility-task-service.checkInSelfCheckout::Invalid arrive date received. ${dateValidation.msg}. TaskId:${details.taskId}`);
                        let invalidDateError = {
                            msg: `Checkout ${dateValidation.msg}`
                        };
                        if (enterpriseHireService.isEnableEnterpriseHire() && mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                            invalidDateError.enterpriseErrorRespnse = {
                                invalidDateTime: true
                            };
                        }
                        return Q.reject(invalidDateError);
                    }

                    logger.info(`mobility-task-service.checkOutSelfCheckout::update prime out ${details.taskId} status ${mobilityTask.status()}`);
                    return aahelp2Service.writeTask(mobilityTask, details.authToken);
                } else {
                    logger.error(`mobility-task-service.checkOutSelfCheckout::checking out ${details.taskId} thirdParty not set.`);
                    return Q.reject({
                        msg: `thirdParty details are not set on taskId:${details.taskId} or task does not belongs to SelfCheckout group like ENTERPRISE, LCH`
                    });
                }
            })
            .then(() => {
                return {
                    status: 'ok',
                    msg: `Hire started for ${details.taskId}`
                };
            })
            .catch((err) => {
                logger.info(`mobility-task-service.checkOutSelfCheckout::failed to checkout ${details.taskId}`);
                return Q.reject({
                    status: 'fail',
                    msg: `Checkout failed for taskId:${details.taskId}. ${err.msg}`,
                    err: err
                });
            });
    },

    /**
     * Check in SelfCheckout hire task
     * @param {Object} details
     * @returns {Promise}
     */
    checkInSelfCheckout: (details) => {
        logger.info(`mobility-task-service.checkInSelfCheckout::checking in ${details.taskId} `);
        return _read(details.taskId, details.authToken)
            .then((mobilityTask) => {
                let rental = mobilityTask.rental();
                if (mobilityTask.rental().isThirdPartyHireSet() && rental.thirdPartyHire().isSelfCheckout()) {
                    taskAuditSvc.write(mobilityTask.id(), mobilityTask.customerRequestId(), `Hire end ${mobilityTask.rental().hireVehicle().regNo()}`, details.authToken);

                    logger.info(`mobility-task-service.checkInSelfCheckout::checking in ${details.taskId} status ${mobilityTask.status()}`);
                    if (mobilityTask.status() !== 'GARR') {
                        logger.error(`mobility-task-service.checkInSelfCheckout::checking in ${details.taskId} NOT in GARR`);
                        let invalidTaskStatus = {
                            msg: `Task in state of ${mobilityTask.status()} can not proceed to checkin`
                        };
                        if (enterpriseHireService.isEnableEnterpriseHire() && mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                            invalidTaskStatus.enterpriseErrorRespnse = {
                                invalidTaskStatus: true
                            };
                        }
                        return Q.reject(invalidTaskStatus);
                    } else if (mobilityTask.status() === 'COMP' && enterpriseHireService.isEnableEnterpriseHire()) {
                        // TODO: This needs to be get updated
                        // mobilityTask.rental().thirdPartyHire().rentedVehicleDetails(details.rentedVehicleDetails)
                        // rentalDataSvc.write(mobilityTask);
                    }
                    // Set complete time
                    let dateValidation = _selfCheckOutDateValidation(details.complete, mobilityTask.schedule().create(), mobilityTask.schedule().arrive());
                    if (dateValidation.isValid) {
                        mobilityTask.schedule().complete(details.complete);
                    } else {
                        logger.info(`mobility-task-service.checkInSelfCheckout::Invalid arrive date received. ${dateValidation.msg}. TaskId:${details.taskId}`);
                        let invalidDateError = {
                            msg: `Checkin ${dateValidation.msg}`
                        };
                        if (enterpriseHireService.isEnableEnterpriseHire() && mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                            invalidDateError.enterpriseErrorRespnse = {
                                invalidDateTime: true
                            };
                        }
                        return Q.reject(invalidDateError);
                    }

                    logger.info(`mobility-task-service.checkInSelfCheckout::update prime out ${details.taskId} status ${mobilityTask.status()}`);
                    return aahelp2Service.completeTask(mobilityTask, 'RR', details.authToken).then((taskWriteResponse) => {
                        return {
                            taskWriteResponse: taskWriteResponse,
                            mobilityTask: mobilityTask
                        };
                    });
                } else {
                    logger.error(`mobility-task-service.checkInSelfCheckout::checking in ${details.taskId} thirdParty not set.`);
                    return Q.reject({
                        msg: `thirdParty details are not set on taskId:${details.taskId} or task does not belongs to SelfCheckout group like ENTERPRISE, LCH`
                    });
                }
            })
            .then((taskDetails) => {
                if (enterpriseHireService.isEnableEnterpriseHire()) {
                    return enterpriseHireService.rentalCompleteRequest(taskDetails).then(() => {
                        // trigger notification for enterprise checkin at retailer only for ENT
                        const rental = taskDetails.mobilityTask.rental();
                        if (rental.hireVehicle().supplierTypeCode() !== 'ENT') {
                            return Promise.resolve();
                        }

                        const payload = {
                            date: new Date().getTime(),
                            type: notificationTypes.TPS_VEHICLE_CHECKED_IN,
                            taskId: taskDetails.taskWriteResponse.taskId()
                        };

                        // if TPS create notification
                        return notificationService.update(payload);
                    });
                }
            })
            .then(() => {
                return {
                    status: 'ok',
                    msg: `Hire ended for ${details.taskId}`
                };
            })
            .catch((err) => {
                logger.info(`mobility-task-service.checkInSelfCheckout::failed to checkin ${details.taskId}`);
                return Q.reject({
                    status: 'fail',
                    msg: `Checkin failed for taskId:${details.taskId}. ${err.msg}`,
                    err: err
                });
            });
    },

    /**
     * cancel hire task assumes that it has not started yet ..
     * @param  {Object} details
     * @param {Number} details.taskId
     * @param {Object} details.authToken
     * @param {Number} details.cancelRentalRequestFromENT {optional}
     * @return {Promise}
     */
    cancelHireTask: (details) => {
        return _read(details.taskId, details.authToken)
            .then((mobilityTask) => {
                details.mobilityTask = mobilityTask;
                if (['ARVD', 'HIRE', 'GARR'].includes(mobilityTask.status())) {
                    let invalidStatusError = {
                        msg: 'Hire in progress can not be cancelled'
                    };
                    if (enterpriseHireService.isEnableEnterpriseHire() && details.mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                        invalidStatusError.enterpriseErrorRespnse = {
                            invalidTaskStatus: true
                        };
                    }
                    return Q.reject(invalidStatusError);
                }
                return aahelp2Service.deleteCover(mobilityTask, details.authToken);
            })
            .then(() => {
                logger.info(`mobility-task.service.cancelHireTask :: insurance cancled`);
                let completeCode =
                    enterpriseHireService.isEnableEnterpriseHire() &&
                    details.cancelRentalRequestFromENT &&
                    details.mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE
                        ? 'EN'
                        : '80';
                return aahelp2Service.completeTask(details.mobilityTask, completeCode, details.authToken);
            })
            .then(() => {
                logger.info(`mobility-task.service.cancelHireTask :: prime canceled`);
                return {
                    msg: 'Hire canceled successfully'
                };
            });
    },
    setInsurance: (taskId, option, authToken) => {
        let scope = {
            rental: null
        };

        return rentalDataSvc
            .read(taskId, authToken)
            .then((carRental) => {
                scope.rental = carRental;
                return aahelp2Service.setInsuranceOption(carRental, option, authToken);
            })
            .then((insuranceProp) => {
                scope.rental.insurance().insuredTerms(insuranceProp.insuredTerms);
                scope.rental.insurance().insuranceOption(insuranceProp.insuranceOption);

                return rentalDataSvc.writeRental(taskId, scope.rental);
            });
    },

    extend: (taskId, endDate, authToken) => {
        let _mobilityTask;
        return _read(taskId, authToken)
            .then((mobilityTask) => {
                if (!['HIRE', 'GARR', 'ARVD'].includes(mobilityTask.status())) {
                    return Q.reject(
                        new TaskWriteResponse({
                            primeMsg: `Can't extend hire : status ${mobilityTask.status()}`,
                            sequence: mobilityTask.sequence(),
                            result: -1
                        })
                    );
                }
                _mobilityTask = mobilityTask;
                let period = (endDate.getTime() - _mobilityTask.schedule().arrive().getTime()) / (60 * 1000);
                _mobilityTask.fault().repairMinutes(Math.round(period));
                return aahelp2Service.extendHire(_mobilityTask, endDate, authToken);
            })
            .then(() => {
                return aahelp2Service.writeTask(_mobilityTask, authToken);
            });
    },
    updatePickupTime: (hireScheduleReq, authToken) => {
        const scope = {
            taskWriteResponse: null,
            rental: hireScheduleReq.mobilityTask().rental()
        };
        logger.info(
            `mobility-task-service.updatePickupTime:: ${taskTag.format(hireScheduleReq.mobilityTask())} update third party ${hireScheduleReq
                .mobilityTask()
                .appointment()
                .earliest()
                .toISOString()}-${hireScheduleReq.mobilityTask().appointment().latest().toISOString()}`
        );
        return aahelp2Service
            .updatePickupTime(hireScheduleReq.mobilityTask(), authToken)
            .then(() => {
                incidentManaged.check(hireScheduleReq.mobilityTask());
                logger.info(
                    `mobility-task-service.updatePickupTime:: ${taskTag.format(hireScheduleReq.mobilityTask())} updating prime ${hireScheduleReq
                        .mobilityTask()
                        .appointment()
                        .earliest()
                        .toISOString()}-${hireScheduleReq.mobilityTask().appointment().latest().toISOString()}`
                );
                return aahelp2Service.writeTask(hireScheduleReq.mobilityTask(), authToken);
            })
            .then((taskWriteResponse) => {
                if (taskWriteResponse.result() === 0 && taskWriteResponse.schedule().resource().id() !== hireScheduleReq.mobilityTask().schedule().resource().id()) {
                    if (hireScheduleReq.mobilityTask().rental().hireVehicle().resourceId() !== null) {
                        hireScheduleReq
                            .mobilityTask()
                            .schedule()
                            .resource(
                                new Resource({
                                    id: hireScheduleReq.mobilityTask().rental().hireVehicle().resourceId()
                                })
                            );
                    }
                    hireScheduleReq.mobilityTask().sequence(taskWriteResponse.sequence());
                    logger.info(`mobility-task-service.updatePickupTime:: ${taskTag.format(hireScheduleReq.mobilityTask())} updating prime to accept change of appointment`);

                    return aahelp2Service.writeTask(hireScheduleReq.mobilityTask(), authToken);
                }
                return taskWriteResponse;
            })
            .then((taskWriteResponse) => {
                scope.taskWriteResponse = taskWriteResponse;
                return scope;
            });
    },
    openRental: (taskId, openRentalDetails, authToken) => {
        let { vehicleLicenseNumber } = openRentalDetails.rentedVehicleDetails;
        return AAHelpService.getVehicleByReg(vehicleLicenseNumber, authToken)
            .then((vehicleResp) => vehicleResp.vehicle)
            .then((vehicle) => {
                return _read(taskId, authToken).then((mobilityTask) => {
                    return {
                        mobilityTask,
                        vehicle
                    };
                });
            })
            .then(({ mobilityTask, vehicle }) => {
                let { VIN } = openRentalDetails.rentedVehicleDetails;
                let { vehicleAuthorization } = openRentalDetails.authorizationDetails ? openRentalDetails.authorizationDetails : null;
                let { billingStartDate, billingStartTime, rentalStartDate, rentalStartTime, rentalContractNumber } = openRentalDetails.rentalContractDetails;

                if (mobilityTask.rental().thirdPartyHire().isManualEntReservation && mobilityTask.rental().thirdPartyHire().isManualEntReservation()) {
                    /**
                     * If hire is created using Manual flow, then thirdPartyRentalConfirmed notification won't execute.
                     * In this case we will not EnterpriseSubHire deatils in mobilityTask
                     * Adding the required details.
                     */
                    let enterpriseSubHire = mobilityTask.rental().thirdPartyHire().toJSON();
                    mobilityTask.rental().thirdPartyHire(new EnterpriseSubHire(enterpriseSubHire));
                }

                mobilityTask.rental().thirdPartyHire().hireVehicle().regNo(vehicle.registration);
                mobilityTask.rental().thirdPartyHire().hireVehicle().transmissionType(vehicle.experianDetails.transmission);
                mobilityTask.rental().thirdPartyHire().hireVehicle().fuelType(vehicle.experianDetails.fuel);
                mobilityTask.rental().thirdPartyHire().hireVehicle().colour(vehicle.experianDetails.colour);
                mobilityTask.rental().thirdPartyHire().hireVehicle().seatNumber(vehicle.experianDetails.seatNumber);
                mobilityTask.rental().thirdPartyHire().hireVehicle().vin(VIN);
                mobilityTask.rental().thirdPartyHire().hireVehicle().make().id(vehicle.makeId);
                mobilityTask.rental().thirdPartyHire().hireVehicle().make().name(vehicle.experianDetails.make);
                mobilityTask.rental().thirdPartyHire().hireVehicle().model().id(vehicle.modelId);
                mobilityTask.rental().thirdPartyHire().hireVehicle().supplierTypeCode(generalConstant.HIRE_TYPES.ENTERPRISE);
                mobilityTask.rental().thirdPartyHire().hireVehicle().model().name(vehicle.experianDetails.model);

                if (!mobilityTask.rental().thirdPartyHire().vehicleGroup() && vehicleAuthorization.vehicleClass) {
                    mobilityTask.rental().thirdPartyHire().vehicleGroup(vehicleAuthorization.vehicleClass);
                }

                if (mobilityTask.rental().hireVehicle().regNo() == null && mobilityTask.rental().thirdPartyHire().hireVehicle().regNo() != null) {
                    mobilityTask.rental().hireVehicle(mobilityTask.rental().thirdPartyHire().hireVehicle());
                }

                if (!mobilityTask.rental().thirdPartyHire().hireVehicleRef() && mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber()) {
                    mobilityTask.rental().thirdPartyHire().hireVehicleRef(mobilityTask.rental().thirdPartyHire().rentalConfirmationNumber());
                }

                mobilityTask.rental().thirdPartyHire().experianDetails(new Vehicle(vehicle));

                mobilityTask.rental().thirdPartyHire().rentalContractNumber(rentalContractNumber);
                // mobilityTask.rental().thirdPartyHire().additionalDriverName();
                mobilityTask.rental().thirdPartyHire().rentalStartDate(rentalStartDate);
                mobilityTask.rental().thirdPartyHire().rentalStartTime(_.get(rentalStartTime, '$value', ''));
                mobilityTask.rental().thirdPartyHire().timeZoneOffset(_.get(rentalStartTime, 'attributes.timeZoneOffset', ''));
                mobilityTask.rental().thirdPartyHire().billingStartDate(billingStartDate);
                mobilityTask.rental().thirdPartyHire().billingStartTime(_.get(billingStartTime, '$value', ''));
                logger.info(`mobility-task-service.openRental:: writing to prime : ${taskId} :: rental :  ${mobilityTask.rental()} `);
                return _write(mobilityTask, authToken);
            })
            .catch((error) => {
                logger.error(`mobility-task-service.openRental::failed to perform RentalOpened for taskId:${taskId}\n Error :: ${error}`);
                return {
                    status: 'fail',
                    msg: `RentalOpened failed for taskId:${taskId}.`,
                    err: error
                };
            });
    },
    thirdPartyRentalConfirmed: (taskId, rentalContractDetails, authToken) => {
        logger.info(`mobility-task-service.thirdPartyRentalConfirmed:: processing taskId : ${taskId}`);
        return _fetchUpdatedMobilityTask(taskId, authToken)
            .then((mobilityTask) => {
                const msg = `Enterprise confirmed reservation : ${rentalContractDetails.rentalConfirmationNumber}`;
                logger.info(`mobility-task-service.thirdPartyRentalConfirmed:: processing taskId : ${taskId} msg : ${msg}`);
                //TODO initSubHire needs to set thirdPartyHire to EnterpriseSubHire
                const mergedHireData = Object.assign({}, mobilityTask.rental().thirdPartyHire().toJSON(), rentalContractDetails);
                mobilityTask.rental().thirdPartyHire(new EnterpriseSubHire(mergedHireData));
                mobilityTask.rental().thirdPartyHire().hireVehicleRef(rentalContractDetails.rentalConfirmationNumber);

                logger.info(`mobility-task-service.thirdPartyRentalConfirmed::resourceID ${mobilityTask.schedule().resource().id()}.`);
                return Q.all([
                    _write(mobilityTask, authToken),
                    aahelp2Service.taskAudit({
                        taskId: mobilityTask.id(),
                        custReqId: mobilityTask.customerRequestId(),
                        text: msg,
                        authToken: authToken
                    })
                ]);
            })
            .catch((error) => {
                logger.error(`mobility-task-service.thirdPartyRentalConfirmed::failed to perform RentalConfirmedCreate ${JSON.stringify(error)}`);
                return Q.reject({
                    status: 'fail',
                    err: error
                });
            });
    },
    enterpriseRentalClosed: (taskId, enterpriseRentalClosedReq, authToken) => {
        return _read(taskId, authToken)
            .then((mobilityTask) => {
                if (enterpriseHireService.isEnableEnterpriseHire() && mobilityTask.rental().hireVehicle().supplierTypeCode() === generalConstant.HIRE_TYPES.ENTERPRISE) {
                    let closeRequest = enterpriseRentalClosedReq.closeRequest;
                    if (mobilityTask.status() === 'GARR') {
                        if (closeRequest) {
                            let rentedVehicleDetails = closeRequest.rentedVehicleDetails;
                            let enterpriseRentedVehicles = _.isArray(rentedVehicleDetails)
                                ? _.map(closeRequest.rentedVehicleDetails, (vehicle) => new EnterpriseRentedVehicle(vehicle))
                                : [new EnterpriseRentedVehicle(rentedVehicleDetails)];
                            mobilityTask.rental().thirdPartyHire().rentedVehicleDetails(enterpriseRentedVehicles);
                        }
                        return _write(mobilityTask, authToken);
                    } else {
                        logger.error(`mobility-task-service.enterpriseRentalClosed::checking in ${taskId} NOT in GARR`);
                        return Q.reject({
                            msg: `Task in state of ${mobilityTask.status()} can not proceed to checkin`,
                            enterpriseErrorRespnse: {
                                invalidTaskStatus: true
                            }
                        });
                    }
                } else {
                    Q.resolve({
                        isEnableEnterpriseHire: false
                    });
                }
            })
            .catch((error) => {
                logger.error(`mobility-task-service.enterpriseRentalClosed::failed to perform EnterpriseRentalClosed ${error}`);
                return Q.reject({
                    status: 'fail',
                    err: error
                });
            });
    },
    enterpriseCancelRental: (task, cancelRentalRequest, authToken) => {
        let taskId = task.id();
        let completionReason = _.get(cancelRentalRequest, 'completionReason', null);
        if (completionReason) {
            return _fetchSupplierFromTask(taskId, authToken).then((supplier) => {
                let cancelRentalRequest = {};
                let cancelDetails = {
                    cancelDate: task.schedule().complete().toDateString(),
                    cancelTime: task.schedule().complete().getTime(),
                    cancelType: 'Other',
                    cancelReason: completionReason
                };
                cancelRentalRequest.supplier = supplier;
                cancelRentalRequest.cancelDetails = cancelDetails;
                return enterpriseHireService.cancelRentalRequest(cancelRentalRequest, authToken);
            });
        } else {
            return Q.resolve({
                cancelRentalRequestFromENT: true
            });
        }
    },
    getEnterpriseLocations: (searchData, authToken) => {
        if (searchData && searchData.supplier) {
            return enterpriseHireService.getLocations(searchData);
        } else {
            let taskId = searchData && searchData.taskId;
            return _fetchSupplierFromTask(taskId, authToken).then((supplier) => {
                searchData.supplier = supplier;
                return enterpriseHireService.getLocations(searchData);
            });
        }
    },
    getEnterpriseSupplier: (mobilityTask, authToken) => {
        return _getEnterpriseSupplier(mobilityTask, authToken);
    },
    enterpriseBusinessRules: (mobilityTask, authToken) => {
        if (mobilityTask) {
            let postData = {
                taskId: mobilityTask.rental().srcRssTaskId()
            };
            return businessRuleSvc
                .getBusinessRulesForEnterprise(postData, authToken)
                .then((resp) => {
                    return resp;
                })
                .catch((err) => {
                    logger.error(`entitlement-service:: unable to fetch business rules for ${postData.taskId} :: ${err.message}`);
                    return Q.reject({
                        msg: `unable to fetch business rules for ${postData.taskId} :: ${err.message}`
                    });
                });
        }
    },
    getNextWorkingDay: (data, authToken) => {
        return extensionSvc
            .getNextWorkingDay(data, authToken)
            .then((res) => {
                return res;
            })
            .catch((err) => {
                logger.error(`extension-service:: unable to calculate next working day for TaskId= ${data.taskId} :: ${err.message}`);
                return Q.reject({
                    msg: `unable to calculate next working day for  ${data.taskId} :: ${err.message}`
                });
            });
    },
    fetchUpdatedMobilityTask: _fetchUpdatedMobilityTask,
    getEvaliteFallBackResponse: (taskId, authToken) => {
        /**
         * If the task has been created and any issue occured while reading/writing it
         * this function will return a generic response to eva-lite,
         * So eva-lite will not allow user to create multiple tasks and will ask to call to oldbury(AAHelp2)
         * for further actions.
         */
        return _fetchUpdatedMobilityTask(taskId, authToken).then((mobilityTask) => {
            if (mobilityTask && ['INIT', 'CHCK', 'GDET'].includes(mobilityTask.status())) {
                let response = {
                    taskId: mobilityTask.id(),
                    status: mobilityTask.status(),
                    msg: null,
                    result: 0
                };
                return response.taskId ? response : null;
            }
        });
    }
};
