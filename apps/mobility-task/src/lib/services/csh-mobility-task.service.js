'use strict';
/** @module services.csh-mobilty-task */
const cshDataStore = require('./csh-oracle.service'),
    oracledb = require('oracledb'),
    RafVehicle = require('@aa/mobility-models/lib/raf-vehicle.model'),
    Q = require('q'),
    CarRental = require('@aa/mobility-models/lib/car-rental.model'),
    ConditionReport = require('@aa/mobility-models/lib/condition-report.model'),
    HireSignatures = require('@aa/mobility-models/lib/hire-signatures.model'),
    MobilityInsurance = require('@aa/mobility-models/lib/mobility-insurance.model'),
    ThirdPartyInsurance = require('@aa/mobility-models/lib/third-party-insurance.model'),
    logger = require('winston');

const mb_read = process.env.mb_read;

const _taskUpdateInsert =
    'BEGIN MSDSDBA.MB_UPSERT_HIRE_TASK(' +
    ':taskId, :custReqId, ' +
    ':rental,:insuranceParId,:insAppointmentRef, :insApprovalRef, ' +
    ':insuranceTerms,:checkIn,:checkOut, :vorSupplierId, :vehModelId, ' +
    ':vehRegNo, :vehVinNo, :vehHireType, :vehValue); commit; END;';

const _writeOptions = {
    autoCommit: true,
    bindDefs: {
        hrId: {
            type: oracledb.NUMBER
        },
        taskId: {
            type: oracledb.NUMBER
        },
        custReqId: {
            type: oracledb.NUMBER
        },
        rental: {
            type: oracledb.CLOB
        },
        insuranceParId: {
            type: oracledb.NUMBER
        },
        insAppointmentRef: {
            type: oracledb.DB_TYPE_VARCHAR
        },
        insApprovalRef: {
            type: oracledb.DB_TYPE_VARCHAR
        },
        insuranceTerms: {
            type: oracledb.CLOB
        },
        checkIn: {
            type: oracledb.CLOB
        },
        checkOut: {
            type: oracledb.CLOB
        },
        vorSupplierId: {
            type: oracledb.NUMBER
        },
        vehModelId: {
            type: oracledb.NUMBER
        },
        vehRegNo: {
            type: oracledb.DB_TYPE_VARCHAR
        },
        vehVinNo: {
            type: oracledb.DB_TYPE_VARCHAR
        },
        vehHireType: {
            type: oracledb.NUMBER
        },
        vehValue: {
            type: oracledb.NUMBER
        }
    }
};

function _bindWriteVars(taskId, customerRequestId, rental) {
    const checkIn = JSON.stringify({
            report: rental.checkinReport().toJSON(),
            signatures: rental.checkinSignatures().toJSON()
        }),
        checkOut = JSON.stringify({
            report: rental.checkoutReport().toJSON(),
            signatures: rental.checkoutSignatures().toJSON()
        }),
        jsRental = rental.toJSON(),
        jsVehicleType = RafVehicle.SUPPLIER_TYPE_CODES.hasOwnProperty(rental.hireVehicle().supplierTypeCode()) ? RafVehicle.SUPPLIER_TYPE_CODES[rental.hireVehicle().supplierTypeCode()] : -1;

    // remove items that are stored seperatly ...
    jsRental.checkinReport = null;
    jsRental.checkinSignatures = null;
    jsRental.checkoutReport = null;
    jsRental.checkoutSignatures = null;

    jsRental.insurance = null;

    return {
        taskId: taskId,
        custReqId: customerRequestId,
        rental: JSON.stringify(jsRental),
        insuranceParId: rental.insurance().type(),
        insAppointmentRef: rental.insurance().id(),
        insApprovalRef: 'no idea',
        insuranceTerms: JSON.stringify(rental.insurance().toJSON()),
        checkIn: checkIn,
        checkOut: checkOut,
        vorSupplierId: rental.hireVehicle().homeDepotId(),
        vehModelId: rental.hireVehicle().model().id(),
        vehRegNo: rental.hireVehicle().regNo(),
        vehVinNo: rental.hireVehicle().vin(),
        vehHireType: jsVehicleType,
        vehValue: rental.hireVehicle().vehicleValue()
    };
}

/**
 * write trental task to
 * @param  {HireScheduleReq} scope contain details of the data we need to perist
 * @param  {Object} dbConn database connection instance ...
 * @return {Promise}    on success return database response
 */
function _writeHireTask(taskId, crId, rental, dbConn) {
    const defer = Q.defer();
    const bindvars = _bindWriteVars(taskId, crId, rental);

    logger.info(`csh-mobilty-task-service._writeOptions:: task ${taskId}`);
    dbConn.execute(_taskUpdateInsert, bindvars, _writeOptions, (err, result) => {
        cshDataStore.release(dbConn);
        if (err) {
            logger.error(`csh-mobilty-task-service._writeOptions:: task ${taskId} oracle msg ${err.message}`, err);
            defer.reject(err);
        } else {
            logger.info(`csh-mobilty-task-service._writeOptions:: task ${taskId} success`);
            defer.resolve(result);
        }
    });

    return defer.promise;
}

/**
 * read hire task from database
 * @param  {Object} scope
 * @param  {Number} scope.taskId
 * @param  {Object} dbConn [description]
 * @return {Promise}        [description]
 */
function _readHireTask(scope, dbConn) {
    const defer = Q.defer();
    const bindvars = {
        taskId: {
            dir: oracledb.BIND_IN,
            type: oracledb.NUMBER,
            val: scope.taskId ? scope.taskId : -1
        },
        crId: {
            dir: oracledb.BIND_IN,
            type: oracledb.NUMBER,
            val: scope.crId ? scope.crId : -1
        },
        cursor: {
            dir: oracledb.BIND_OUT,
            type: oracledb.CURSOR
        }
    };
    logger.info(`csh-mobilty-task-service._readHireTask:: task ${scope.taskId} custReqId  ${scope.crId}`);
    dbConn.execute(
        'BEGIN MSDSDBA.MB_GET_HIRE_TASK(:taskId, :crId, :cursor); END;',
        bindvars,
        {
            outFormat: oracledb.OBJECT
        },
        function (err, result) {
            let resultStream = null,
                results = [];

            if (err) {
                logger.error(`csh-mobilty-task-service._readHireTask:: task ${scope.taskId} oracle msg ${err.message}`, err);
                cshDataStore.release(dbConn);
                defer.reject(err.message);
                return;
            }

            resultStream = result.outBinds.cursor.toQueryStream();

            resultStream.on('error', function (error) {
                logger.error(`csh-mobilty-task-service._readHireTask:: task ${scope.taskId} results stream err`, error);
                cshDataStore.release(dbConn);
                defer.reject(error);
            });

            resultStream.on('data', function (data) {
                results.push(data);
            });

            resultStream.on('end', () => {
                resultStream.destroy();
            });

            resultStream.on('end', function () {
                logger.info(`csh-mobilty-task-service._readHireTask:: task ${scope.taskId} success`);
                cshDataStore.release(dbConn);
                defer.resolve(results);
            });
        }
    );

    return defer.promise;
}

/**
 * get breakdown task id from database
 * @param  {Object} scope
 * @param  {Number} scope.taskId
 * @param  {Object} dbConn [description]
 * @return {Promise}        [description]
 */
function _getBreakdownTaskId(scope, dbConn) {
    const defer = Q.defer();
    const bindvars = {
        taskId: {
            dir: oracledb.BIND_IN,
            type: oracledb.NUMBER,
            val: scope.taskId ? scope.taskId : -1
        }
    };
    const sql =
        `` + `select task_id from (` + `select * from task ` + `where cust_request_id = (select cust_request_id from task where task_id =:taskId) ` + `order by task_id` + `) ` + `where ROWNUM = 1`;

    logger.info(`csh-mobilty-task-service._getBreakdownTaskId:: task ${scope.taskId}`);
    dbConn.execute(sql, bindvars, { outFormat: oracledb.OBJECT }, function (err, result) {
        cshDataStore.release(dbConn);
        if (err) {
            logger.error(`csh-mobilty-task-service._getBreakdownTaskId:: task ${scope.taskId} oracle msg ${err.message}`, err);
            defer.reject(err.message);
            return;
        }

        if (result.rows.length) {
            let breakdownTaskId = result.rows[0].TASK_ID;
            logger.info(`csh-mobilty-task-service._getBreakdownTaskId:: execution finished and close: task ${scope.taskId}, breakdownTaskId ${breakdownTaskId}`);
            defer.resolve(breakdownTaskId);
        } else {
            logger.error(`csh-mobilty-task-service._getBreakdownTaskId:: execution finished and close`);
            defer.reject('not found');
        }
    });
    return defer.promise;
}

/**
 * get hire task ids from database
 * @param  {Object} scope
 * @param  {Number} scope.taskId
 * @param  {Object} dbConn [description]
 * @return {Promise}        [description]
 */
function _getHiresByCustRequestId(scope, dbConn) {
    const defer = Q.defer();
    const bindvars = {
        taskId: {
            dir: oracledb.BIND_IN,
            type: oracledb.NUMBER,
            val: scope.taskId ? scope.taskId : -1
        }
    };
    const sql =
        `` +
        `SELECT T.TASK_ID ` +
        `FROM  task T ` +
        `JOIN  task_status S on S.TASK_STATUS_ID=T.TASK_STATUS_ID ` +
        `WHERE T.CUST_REQUEST_ID = (select cust_request_id from task where task_id =:taskId) ` +
        `AND S.TASK_STATUS_CODE IN ('HIRE','GARR')`;

    logger.info(`csh-mobilty-task-service._getHireTaskIds:: task ${scope.taskId}`);
    dbConn.execute(sql, bindvars, { outFormat: oracledb.OBJECT }, function (err, result) {
        cshDataStore.release(dbConn);
        if (err) {
            logger.error(`csh-mobilty-task-service._getHireTaskIds:: task ${scope.taskId} oracle msg ${err.message}`, err);
            defer.reject(err.message);
            return;
        }

        if (result.rows.length) {
            let taskIds = result.rows.map((item) => item.TASK_ID);
            logger.info(`csh-mobilty-task-service._getHireTaskIds:: execution finished and close: task ${scope.taskId}`);
            defer.resolve(taskIds);
        } else {
            logger.error(`csh-mobilty-task-service._getHireTaskIds:: execution finished and close: task ${scope.taskId}`);
            defer.reject('not found');
        }
    });
    return defer.promise;
}

function _cshParser(dataSet) {
    let rental = new CarRental(),
        _insurance = JSON.parse(dataSet.insurance),
        _checkIn = JSON.parse(dataSet.checkIn),
        _checkOut = JSON.parse(dataSet.checkOut);

    if (dataSet) {
        rental = new CarRental(JSON.parse(dataSet.rental));

        if (_checkIn) {
            rental.checkinReport(new ConditionReport(_checkIn.report));
            rental.checkinSignatures(new HireSignatures(_checkIn.signatures));
        }
        if (_checkOut) {
            rental.checkoutReport(new ConditionReport(_checkOut.report));
            rental.checkoutSignatures(new HireSignatures(_checkOut.signatures));
        }
        if (_insurance) {
            rental.insurance(_insurance.type === CarRental.INSURANCE_TYPES.MOBILITY ? new MobilityInsurance(_insurance) : new ThirdPartyInsurance(_insurance));
        }
    }
    return rental;
}

module.exports = {
    /**
     * read car rental from csh mb_hire_task table
     * @param  {Object} readScope [description]
     * @oaram {Number} readScope.taskId identifies task to read
     * @return {Promise}           [description]
     */
    read: (taskId) => {
        return cshDataStore
            .connect()
            .then((dbConn) =>
                _readHireTask(
                    {
                        taskId
                    },
                    dbConn
                )
            )
            .then((dataSet) => {
                return dataSet.length === 1 ? _cshParser(dataSet[0]) : new CarRental();
            });
    },
    write: (mobilityTask) => {
        return cshDataStore.connect().then((dbConn) => _writeHireTask(mobilityTask.id(), mobilityTask.customerRequestId(), mobilityTask.rental(), dbConn));
    },
    writeRental: (taskId, custReqId, rental) => {
        return cshDataStore.connect().then((dbConn) => {
            return _writeHireTask(taskId, custReqId ? custReqId : -1, rental, dbConn); // on update we don't set cr again ...
        });
    },
    /**
     * read car rentals for a case ... usually ony one but
     * @param  {Object} readScope [description]
     * @oaram {Number} readScope.customerRequestId defines customer request id
     * @return {Promise}           [description]
     */
    caseRead: (crId) => {
        return cshDataStore
            .connect()
            .then((dbConn) =>
                _readHireTask(
                    {
                        crId
                    },
                    dbConn
                )
            )
            .then((dataSet) => {
                let hires = new Map();
                dataSet.map((hire) => hires.set(hire.taskId, _cshParser(hire)));
                return hires;
            });
    },
    findCompletedTasks: () => {
        return cshDataStore.connect().then((dbConn) => {
            const defer = Q.defer();
            const deletedTasks = [];
            const qSream = dbConn.queryStream('select ht.TASK_ID from MSDSDBA.MB_HIRE_TASK ht join task t on ht.task_id=t.task_id where t.task_status_id in (29,1);');

            qSream.on('data', (data) => {
                deletedTasks.push(data[0]);
            });

            qSream.on('error', (err) => {
                cshDataStore.release(dbConn);
                defer.reject(err);
            });

            qSream.on('end', () => {
                cshDataStore.release(dbConn);
                defer.resolve(deletedTasks);
            });

            return defer.promise;
        });
    },
    /**
     * Update estimated repair date
     * @param {Object} params - params to store
     * @param {Number} params.taskId  - task id
     * @param {Date} params.estRepairDate  - date to update
     * @return {Promise<any>}
     */
    updateEstimatedRepairDate: function updateEstimatedRepairDate(params) {
        const sql = `UPDATE
            MSDSDBA.mb_hire_task
            SET
            est_repair_date = :estRepairDate
            WHERE
            task_id = :taskId
        `;

        const _updateOptions = {
            autoCommit: true,
            bindDefs: {
                taskId: {
                    type: oracledb.NUMBER
                },
                estRepairDate: {
                    type: oracledb.DATE
                }
            }
        };

        let bindvars = {
            estRepairDate: new Date(params.estRepairDate),
            taskId: parseInt(params.taskId)
        };

        return cshDataStore.connect().then((dbConn) => {
            return dbConn.execute(sql, bindvars, _updateOptions);
        });
    },

    getBreakdownTaskId(taskId) {
        return cshDataStore
            .connect()
            .then((dbConn) =>
                _getBreakdownTaskId(
                    {
                        taskId
                    },
                    dbConn
                )
            )
            .then((breakdownTaskId) => {
                return breakdownTaskId;
            });
    },

    getHiresByCustRequestId(taskId) {
        return cshDataStore
            .connect()
            .then((dbConn) => _getHiresByCustRequestId({ taskId }, dbConn))
            .then((taskIds) => {
                return taskIds;
            });
    },

    /**
     * get tasks with in a CR - returns arrival-time ...
     * @param  {Number} crId    identify customer request
     * @return {Promise.<Array>} on success resolves to array of tasks
     */
    arrivalTimes: (crId) => {
        const sql =
            `` +
            ` SELECT mht.TASK_ID, mht.CUST_REQUEST_ID, bt.RES_ARRIVAL_TIME,` +
            ` S.TASK_STATUS_CODE,` +
            ` json_value(CAR_RENTAL_DETAILS,'$.hireVehicle.regNo') RAF_VEH_REG,` +
            ` json_value(CAR_RENTAL_DETAILS,'$.thirdPartyHire.hireVehicle.regNo') ENT_VEH_REG,` +
            ` json_value(CAR_RENTAL_DETAILS,'$.srcRentalTaskId') SRC_RENTAL_TASK_ID` +
            ` FROM  mb_hire_task mht` +
            ` JOIN  task t ON  t.task_id = mht.task_id` +
            ` JOIN  task_status s on s.TASK_STATUS_ID=t.TASK_STATUS_ID` +
            ` JOIN BREAKDOWN_TASK bt on bt.TASK_ID = mht.TASK_ID` +
            ` WHERE mht.CUST_REQUEST_ID = :crId` +
            // ` AND S.TASK_STATUS_CODE IN ('HIRE','GARR')`+
            ` and json_value(CAR_RENTAL_DETAILS,'$.srcRentalTaskId') > 0` +
            ` order by bt.RES_ARRIVAL_TIME desc`;

        const bindvars = {
            crId: {
                dir: oracledb.BIND_IN,
                type: oracledb.NUMBER,
                val: crId
            }
        };

        return cshDataStore
            .execute(sql, bindvars)
            .then((dataSet) => {
                return dataSet.map((item) => {
                    return {
                        TASK_ID: item.TASK_ID,
                        CUST_REQUEST_ID: item.CUST_REQUEST_ID,
                        RES_ARRIVAL_TIME: item.RES_ARRIVAL_TIME,
                        VEH_REG: item.RAF_VEH_REG || item.ENT_VEH_REG,
                        TASK_STATUS_CODE: item.TASK_STATUS_CODE,
                        SRC_RENTAL_TASK_ID: item.SRC_RENTAL_TASK_ID
                    };
                });
            })
            .catch((err) => {
                logger.error(`csh-mobilty-task-service.arrivalTimes:: crId ${crId}: sql ${sql} oracle msg ${err.message}`, err);
            });
    },
    getVorCheckinNotifySummary: (taskId) => {
        const sql = `BEGIN ${mb_read}.VOR_CHECKIN_NOTIFY_SUMMARY(:pTaskId, :cursor); END;`;
        const bindvars = {
            pTaskId: {
                dir: oracledb.BIND_IN,
                type: oracledb.NUMBER,
                val: parseInt(taskId)
            },
            cursor: {
                dir: oracledb.BIND_OUT,
                type: oracledb.CURSOR
            }
        };
        return cshDataStore.connect().then((dbConn) => {
            return cshDataStore.cursorRead(dbConn, sql, bindvars).then((dataSet) => {
                logger.info(`csh-mobilty-task-service.getVorCheckinNotifySummary:: task ${taskId}, dataSet ${JSON.stringify(dataSet)}`);
                return dataSet.length === 1 ? dataSet[0] : {};
            });
        });
    }
};
