'use strict';

module.exports = {
    TRANSMISSION_TYPE: {
        RENTAL_CONFIRMED_CREATE: 'RentalConfirmedCreate',
        RENTAL_OPENED: 'RentalOpened',
        RENTAL_CLOSED: 'RentalClosed',
        REN<PERSON><PERSON>_CANCEL: 'RentalCancel',
        REN<PERSON><PERSON>_UPDATE: 'UpdateRental'
    },
    EXTENSION: {
        INBOUND: 'CarHireExtension'
    },
    HIRE_TYPE: {
        HIRE_CANCEL: 'HireCancel',
        HIRE_COMPLETE: 'HireComplete',
        HIRE_EXTENSION: 'HireExtension'
    },
    DOCUMENTS: {
        CHECKOUT_DOCUMENT: 'CheckoutDocument'
    },
    QUEUE_NAME: {
        HIRE_VEHICLE_INBOUND: 'hire-inbound-message',
        ENTERPRISE_OUTBOUND: 'enterprise-outbound-message',
        SAP_MESSAGE: 'sap-message'
    },
    CHECKOUT: 'CHECKOUT',
    SAP_MESSAGE: {
        MANU<PERSON>_EXTENSION: 'REQUEST_EXTN',
        <PERSON><PERSON><PERSON><PERSON>: 'MAL_ADMIN',
        COMPLETION: 'COMPLETION',
        SWAP_COMPLETION: 'SWAP_COMPLETION'
    },
    EXTENSIONS_OFFHIRE_AUTOEMAIL_SUMMARY: {
        DELAYED_CUSTOMER_COLLECTION: 'DELAYED_CUSTOMER_COLLECTION',
        RECLAIM: 'RECLAIM'
    },
    OFFHIRE_AUTOEMAIL_SUMMARY: 'OFFHIRE_AUTOEMAIL_SUMMARY'
};
/*Hire_TYPE and QUEUE_NAME can be removed once we upgrade the aa-azure-queue package as those constants are available in it */
