'use strict';

module.exports = {
    defleetEndDuration: 15, // Defleet end duration in days
    dateFormats: {
        YYYYMMDD: 'YYYYMMDD'
    },
    HIRE_TYPES: {
        AA: 'AA',
        OU: 'OU',
        RAF: 'RAF',
        THRIFTY: 'THRIFTY',
        ENTERPRISE: 'ENTERPRISE',
        LCH: 'LCH',
        L460: 'L460'
    },
    HIRING_DURATION: {
        DEFAULT: 3
    },
    HIRE_DAYS_TYPES: {
        DAYS: 'd',
        WORKING_DAYS: 'wd'
    },
    VEHICLE_CATEGORY: {
        DEFAULT: 'UD'
    },
    OVERWRITE_PROFILES: {
        POR: {
            customerGroup: 'POR',
            profileName: 'Porsche UK',
            armsProfileId: 'PO941',
            reasonForHire: 'Porsche Assistance RTA (AA)',
            armsOfficeId: '02'
        },
        SMA: {
            customerGroup: 'SMA',
            profileName: 'AA Main Profile',
            armsProfileId: 'AA940',
            reasonForHire: 'Manufacturer - Smart (Non-Electric)',
            armsOfficeId: '25'
        }
    },
    FUEL_TYPE: {
        ELECTRIC: 'ELECTRIC'
    },
    FAULT: {
        VANDALISM: 'vandalism'
    },
    FAULT_MAPPING: {
        RTC: 'rtc',
        VANDALISM: 'insuranceCoveredFaults'
    },
    MAN_TO_AUTO_VEHICLE_GROUPS: {
        UA: 'VA',
        UB: 'VB',
        UC: 'VC',
        UD: 'VD',
        UE: 'VE',
        UF: 'VF'
    },
    VOR_CHECKIN_NOTIFY_ALLOWED_CUSTOMER_GROUPS: ['HYU']
};
