const aaOracleUtility = require('@aa/oracle-utilities'),
    oracledb = require('oracledb');

oracledb.fetchAsString = [oracledb.CLOB];
const appName = 'read-blob-cron';

module.exports = {
    init: () => {
        return aaOracleUtility.init({
            connectStrings: process.env.cshConnectStrings.split(','),
            user: process.env.cshUser,
            password: process.env.cshPassword,
            appName
        });
    },
    connect: () => {
        return aaOracleUtility.connect();
    },
    disconnect: (db) => {
        aaOracleUtility.release(db);
    },
    jsonify: (metadata, data) => {
        return metadata.reduce((ac, item, i) => {
            ac[item.name] = data[i];
            return ac;
        }, {});
    }
};
