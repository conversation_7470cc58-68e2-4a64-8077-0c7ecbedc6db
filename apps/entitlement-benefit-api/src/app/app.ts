import { AuditClient } from '@aa/audit-client';
import { applicationBasePaths, ErrorResponse } from '@aa/connector';
import { BenefitLimit, MiEvents, Namespace, PaginationQuery, PaginationQueryResult, EntitlementBenefits } from '@aa/data-models/common';
import { EntitlementData, ProductData, Product } from '@aa/data-models/aux/entitlement';
import { DataStoreProviderType } from '@aa/data-store';
import { MongodbUtils } from '@aa/data-store-utils';
import { Exception } from '@aa/exception';
import { HttpMethod, ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubSender, EventHubSenderConfig } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment, Utils } from '@aa/utils';
import { Request, Response } from 'express';
import { isArray } from 'lodash';
import { WithId } from 'mongodb';

const appName = 'Entitlement Benefit API';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.ENTITLEMENT_BENEFIT_API;
    protected auditClient: AuditClient;
    protected onSiteDomain: string;
    protected miStreamEntitlementBenefitSender: EventHubSender<MiEvents.ENTITLEMENT_BENEFITS, EntitlementBenefits>;
    protected miStreamBenefitLimitSender: EventHubSender<MiEvents.BENEFIT_LIMIT, BenefitLimit>;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.MONGODB, DataStoreProviderType.REDIS]
        });

        this.auditClient = new AuditClient({
            application: BackendApplication.ENTITLEMENT_BENEFIT_API,
            connector: this.connector,
            operatorId: -1
        });

        this.onSiteDomain = this.connector.config.onSiteDomain;

        const configBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };

        this.miStreamEntitlementBenefitSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
        this.miStreamBenefitLimitSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });

        // Product benefits endpoints
        this.server.get('/entitlements-benefits', this.getEntitlementBenefits);
        this.server.get('/entitlement-benefit/:contractKey/:customerGroup', this.getEntitlementBenefitsByContract);
        this.server.get('/entitlement-benefit/:code', this.getEntitlementBenefitsByCode);
        this.server.post('/entitlement-benefit', this.createEntitlementBenefits);
        this.server.post('/entitlement-benefit/:code/associate', this.associateBenefitsWithProduct);
        this.server.put('/entitlement-benefit/:code', this.editEntitlementBenefits);
        this.server.delete('/entitlement-benefit/:code', this.deleteEntitlementBenefits);
        this.server.get('/entitlement-benefit', this.matchEntitlementBenefits);

        // Benefit limit endpoints
        this.server.get('/benefits', this.getBenefitsLimits);
        this.server.get('/benefit-limits/:code', this.getBenefitLimitByCode);
        this.server.post('/benefit-limits', this.createBenefitLimit);
        this.server.put('/benefit-limits/:code', this.editBenefitLimit);
        this.server.delete('/benefit-limits/:code', this.deleteBenefitLimit);
    }

    /**
     * Get paginated list of all products benefits with query
     * @param {Request} req
     * @param {Response<WithId<EntitlementBenefits>>} res
     * @return {Promise<void>}
     */
    protected getEntitlementBenefits = async (
        req: Request<
            unknown,
            unknown,
            unknown,
            {
                limit?: string;
                skip?: string;
                customerGroup?: string;
                code?: string;
                benefits?: string;
            }
        >,
        res: Response<PaginationQueryResult<WithId<EntitlementBenefits>>>
    ): Promise<void> => {
        try {
            const limit = parseInt(req.query.limit || '100', 10);
            const skip = parseInt(req.query.skip || '0', 10);
            this.logger.log(`Getting all products benefits`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<EntitlementBenefits>('entities', 'entitlementBenefit');

            const query: Record<string, unknown> = {};
            if (req.query.customerGroup) {
                query.customerGroup = req.query.customerGroup;
            }
            if (req.query.code) {
                query.code = req.query.code;
            }
            if (req.query.benefits) {
                query.benefits = { $in: req.query.benefits.split(',') };
            }

            const cursor = collection.find<EntitlementBenefits>(query);
            const paginationQuery: PaginationQuery = { limit, skip };
            const result = await MongodbUtils.paginate(cursor, paginationQuery, 100);

            if (result) {
                this.logger.log(`Success: Get products benefits`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Get products benefits`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting products benefits`,
                data: { ...req.query }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get product benefit by code
     * @param {Request} req
     * @param {Response<WithId<EntitlementBenefits>>} res
     * @return {Promise<void>}
     */
    protected getEntitlementBenefitsByCode = async (
        req: Request<
            {
                code: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<EntitlementBenefits>>
    ): Promise<void> => {
        try {
            const { code } = req.params;
            this.logger.log(`Getting product benefit for code: ${code}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<EntitlementBenefits>('entities', 'entitlementBenefit');
            const result = await collection.findOne<EntitlementBenefits>({
                code
            });

            if (result) {
                this.logger.log(`Success: Get product benefit for code: ${code}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Get product benefit for code: ${code}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting product benefit for code: ${req.params.code || 'no code given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get product benefit by contract
     * @param {Request} req
     * @param {Response<WithId<EntitlementBenefits>>} res
     * @return {Promise<void>}
     */
    protected getEntitlementBenefitsByContract = async (
        req: Request<
            {
                contractKey: string;
                customerGroup: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<EntitlementBenefits>>
    ): Promise<void> => {
        try {
            const { contractKey, customerGroup } = req.params;
            this.logger.log(`Getting product benefit for contractKey: ${contractKey} customerGroup: ${customerGroup}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<EntitlementBenefits>('entities', 'entitlementBenefit');
            const result = await collection.findOne<EntitlementBenefits>({
                contractKey,
                customerGroup
            });

            if (result) {
                this.logger.log(`Success: Get product benefit for contractKey: ${contractKey} customerGroup: ${customerGroup}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Get product benefit for contractKey: ${contractKey} customerGroup: ${customerGroup}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting product benefit for contractKey: ${req.params.contractKey || 'no contractKey given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get product benefit by BCSAP Number & CustomerGroupcode
     * @param {Request} req
     * @param {Response<WithId<EntitlementBenefits>>} res
     * @return {Promise<void>}
     */
    protected matchEntitlementBenefits = async (req: Request<unknown, unknown>, res: Response<WithId<EntitlementBenefits>>) => {
        try {
            const { customerGroupCode } = req.body;
            const { policyNumber } = req.body;
            this.logger.log(`Getting product benefit for customer group: ${customerGroupCode} and bscap number: ${policyNumber}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<EntitlementBenefits>('entities', 'entitlementBenefit');

            const fetchProducts = await this.httpClient.fetch<EntitlementData>({
                url: `${this.onSiteDomain}${applicationBasePaths[BackendApplication.ENTITLEMENT]}/getProductsByCustomerGroupCode`,
                method: HttpMethod.POST,
                authenticate: true,
                body: req.body
            });

            if (!fetchProducts.ok) {
                this.logger.log(`No results: No products in EntitlementData for customerGroupcode: ${customerGroupCode} and bcasp number: ${policyNumber}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            } else {
                const filteredResponse: ProductData[] = [];
                const products = fetchProducts.body?.products as ProductData[];

                for (const product of products) {
                    const matchingProduct = await collection.findOne({ productCode: product.code });
                    if (matchingProduct) {
                        filteredResponse.push(product);
                    }
                }
                if (filteredResponse.length !== 0) {
                    this.logger.log(`Success: Fetched matching products with product benefits for customer group: ${customerGroupCode} and bscap number: ${policyNumber}`);
                    return getResponse(res, ServerResponseCode.OK, filteredResponse);
                } else {
                    this.logger.log(`No results: No products match with product benefit entity for customerGroupcode: ${customerGroupCode} and bcasp number: ${policyNumber}`);
                    return getResponse(res, ServerResponseCode.NOT_FOUND);
                }
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to match products with product benefit entity`,
                data: { ...req }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Create new product benefits in MongoDB
     * @param {e.Request<unknown, unknown, EntitlementBenefits>} req
     * @param {e.Response<WithId<EntitlementBenefits>>} res
     * @return {Promise<void>}
     */
    protected createEntitlementBenefits = async (req: Request<unknown, unknown, EntitlementBenefits>, res: Response<WithId<EntitlementBenefits>>): Promise<void> => {
        try {
            const entitlementBenefit = req.body;
            this.logger.info({
                sourceName: this.name,
                message: `Received new product benefit for creation in MongoDB`,
                data: { ...entitlementBenefit }
            });

            if (!isContractKeyValid(entitlementBenefit.contractKey)) {
                return handleInvalidRequest('contractKey', res);
            }
            if (!isCustomerRequestIdValid(entitlementBenefit.customerGroup)) {
                return handleInvalidRequest('customerGroup', res);
            }
            if (!isBenefitLimitsValid(entitlementBenefit.limits)) {
                return handleInvalidRequest('limits', res);
            }

            entitlementBenefit.code = Utils.uuid();
            const date = new Date();
            entitlementBenefit.created = date;
            entitlementBenefit.updated = date;

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<EntitlementBenefits>('entities', 'entitlementBenefit');

            const result = await collection.insertOne(entitlementBenefit);
            const persistedSecureLocation: WithId<EntitlementBenefits> = {
                ...entitlementBenefit,
                _id: result.insertedId
            };
            await this.miStreamEntitlementBenefitSender.send(MiEvents.ENTITLEMENT_BENEFITS, persistedSecureLocation);

            if (result) {
                this.logger.log(`Success: Create product benefit`);
                return getResponse(res, ServerResponseCode.OK, persistedSecureLocation);
            } else {
                this.logger.log(`No results: Create product benefit`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                message: `Failed creating new product benefit.`,
                data: { ...req.body }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, error);
        }
    };

    /**
     * Associate benefits with product in MongoDB
     * @param {e.Request<{code: string}, unknown, EntitlementBenefits>} req
     * @param {e.Response<WithId<EntitlementBenefits>>} res
     * @return {Promise<void>}
     */
    protected associateBenefitsWithProduct = async (
        req: Request<
            {
                code: string;
            },
            unknown,
            Array<string>
        >,
        res: Response<WithId<EntitlementBenefits>>
    ): Promise<void> => {
        try {
            // TODO: Check if valid body
            const benefitCodes = req.body;
            const { code } = req.params;
            this.logger.log(`Associating product benefit for code: ${code} with benefitCodes: ${benefitCodes}`);

            if (!isBenefitLimitsValid(benefitCodes)) {
                return handleInvalidRequest('limits', res);
            }

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);

            const collection = await provider.collection<EntitlementBenefits>('entities', 'entitlementBenefit');

            const entitlementBenefits = await collection.findOne<EntitlementBenefits>({
                code
            });
            let previousBenefits: Array<string> = [];
            if (entitlementBenefits) {
                if (entitlementBenefits.limits) {
                    previousBenefits = entitlementBenefits.limits;
                }
            }
            const updatedBenefits = [...previousBenefits, ...benefitCodes];
            const updatedBenefitsWithoutDuplicates = [...new Set(updatedBenefits)];
            const associatedEntitlementBenefits = {
                ...entitlementBenefits,
                benefits: updatedBenefitsWithoutDuplicates
            };
            associatedEntitlementBenefits.updated = new Date();

            const result = await collection.updateOne(
                { code },
                { $set: associatedEntitlementBenefits },
                {
                    upsert: true
                }
            );

            if (result) {
                this.logger.log(`Success: Associated product benefit for code: ${code}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Associated product benefit for code: ${code}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to associate product benefits for code: ${req.params.code || 'no code given in request'}`,
                data: { ...req.params, ...req.body }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Edit product benefits in MongoDB
     * @param {e.Request<{id: string}, unknown, EntitlementBenefits>} req
     * @param {e.Response<WithId<EntitlementBenefits>>} res
     * @return {Promise<void>}
     */
    protected editEntitlementBenefits = async (
        req: Request<
            {
                code: string;
            },
            unknown,
            EntitlementBenefits
        >,
        res: Response<WithId<EntitlementBenefits>>
    ): Promise<void> => {
        try {
            const entitlementBenefits = req.body;
            const { code } = req.params;

            if (!isContractKeyValid(entitlementBenefits.code)) {
                return handleInvalidRequest('code', res);
            }
            if (!isCustomerRequestIdValid(entitlementBenefits.customerGroup)) {
                return handleInvalidRequest('customerGroup', res);
            }
            if (!isBenefitLimitsValid(entitlementBenefits.limits)) {
                return handleInvalidRequest('limits', res);
            }

            entitlementBenefits.updated = new Date();

            this.logger.log(`Editing product benefit for code: ${code}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<EntitlementBenefits>('entities', 'entitlementBenefit');

            // TODO: add check if exists
            const result = await collection.updateOne({ code }, { $set: entitlementBenefits });
            await this.miStreamEntitlementBenefitSender.send(MiEvents.ENTITLEMENT_BENEFITS, entitlementBenefits);

            if (result) {
                this.logger.log(`Success: Editing product benefit for code: ${code}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Editing product benefit for code: ${code}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to edit product benefits for code: ${req.params.code || 'no code given in request'}`,
                data: { ...req.params, ...req.body }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Delete product benefits by code
     * @param {Request<{code: string}, unknown, unknown>} req
     * @param {Response<WithId<EntitlementBenefits>>} res
     * @return {Promise<void>}
     */
    protected deleteEntitlementBenefits = async (
        req: Request<
            {
                code: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<EntitlementBenefits>>
    ): Promise<void> => {
        const { code } = req.params;
        try {
            this.logger.log(`Delete product benefits with code: ${code}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<EntitlementBenefits>('entities', 'entitlementBenefit');

            const result = collection.findOneAndDelete({
                code
            });

            const trace = this.auditClient.getTrace(Namespace.EUOPS, code);
            await this.auditClient.reportAction(trace, {
                message: `EntitlementBenefitApi: Product benefits delete audit for code: ${code}`,
                data: { ...result }
            });

            if (result) {
                this.logger.log(`Success: Delete product benefit for code: ${code}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Delete product benefit for code: ${code}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to delete product benefits with code ${code || 'no code given in request'}`,
                data: { code }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get paginated list of all benefits limits with query
     * @param {Request} req
     * @param {Response<WithId<BenefitLimit>>} res
     * @return {Promise<void>}
     */
    protected getBenefitsLimits = async (
        req: Request<
            unknown,
            unknown,
            unknown,
            {
                limit?: string;
                skip?: string;
                code?: string;
                name?: string;
                type?: string;
                currency?: string;
                amount?: number;
            }
        >,
        res: Response<PaginationQueryResult<WithId<BenefitLimit>>>
    ): Promise<void> => {
        try {
            const limit = parseInt(req.query.limit || '100', 10);
            const skip = parseInt(req.query.skip || '0', 10);
            this.logger.log(`Getting all benefits limits`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<BenefitLimit>('entities', 'benefitLimits');

            const query: Record<string, unknown> = {};
            if (req.query.code) {
                query.code = req.query.code;
            }
            if (req.query.name) {
                query.name = req.query.name;
            }
            if (req.query.type) {
                query.type = req.query.type;
            }
            if (req.query.currency) {
                query.currency = req.query.currency;
            }
            if (req.query.amount) {
                query.amount = Number(req.query.amount);
            }

            const cursor = collection.find<BenefitLimit>(query);
            const queryPagination: PaginationQuery = { limit, skip };
            const result = await MongodbUtils.paginate(cursor, queryPagination, 100);

            if (result) {
                this.logger.log(`Success: Get benefits limits`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Get benefits limits`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting products benefits`,
                data: {}
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get benefit limit by benefit code
     * @param {Request} req
     * @param {Response<WithId<BenefitLimit>>} res
     * @return {Promise<void>}
     */
    protected getBenefitLimitByCode = async (
        req: Request<
            {
                code: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<BenefitLimit>>
    ): Promise<void> => {
        try {
            const { code } = req.params;
            this.logger.log(`Getting benefit limit for code: ${code}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<BenefitLimit>('entities', 'benefitLimits');
            const result = await collection.findOne<BenefitLimit>({ code });

            if (result) {
                this.logger.log(`Success: Get benefits limits with code: ${code}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Get benefits limits with code: ${code}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting benefit limit for code: ${req.params.code || 'no code given in request'}`,
                data: { ...req.params }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Create new benefit limit in MongoDB
     * @param {e.Request<unknown, unknown, BenefitLimit>} req
     * @param {e.Response<WithId<BenefitLimit>>} res
     * @return {Promise<void>}
     */
    protected createBenefitLimit = async (req: Request<unknown, unknown, BenefitLimit>, res: Response<WithId<BenefitLimit>>): Promise<void> => {
        try {
            const benefitLimit = req.body;
            this.logger.info({
                sourceName: this.name,
                message: `Received new benefit limit for creation in MongoDB`,
                data: { ...benefitLimit }
            });

            if (!isContractKeyValid(benefitLimit.code)) {
                return handleInvalidRequest('code', res);
            }
            if (!isValidString(benefitLimit.name)) {
                return handleInvalidRequest('name', res);
            }
            if (!isValidString(benefitLimit.type)) {
                return handleInvalidRequest('type', res);
            }
            if (!isValidString(benefitLimit.currency)) {
                return handleInvalidRequest('currency', res);
            }
            if (!isNumber(benefitLimit.amount)) {
                return handleInvalidRequest('amount', res);
            }
            benefitLimit.created = new Date();
            benefitLimit.updated = new Date();

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<BenefitLimit>('entities', 'benefitLimits');

            const result = await collection.insertOne(benefitLimit);
            const persistedBenefitLimit: WithId<BenefitLimit> = {
                ...benefitLimit,
                _id: result.insertedId
            };
            await this.miStreamBenefitLimitSender.send(MiEvents.BENEFIT_LIMIT, persistedBenefitLimit);

            if (persistedBenefitLimit) {
                this.logger.log(`Success: Create benefit limits with _id: ${result.insertedId}`);
                return getResponse(res, ServerResponseCode.OK, persistedBenefitLimit);
            } else {
                this.logger.log(`No results: Create benefit limits`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                message: `Failed creating new benefit limit.`,
                data: { ...req.body }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, error);
        }
    };

    /**
     * Edit benefit limit in MongoDB
     * @param {e.Request<{code: string}, unknown, BenefitLimit>} req
     * @param {e.Response<WithId<BenefitLimit>>} res
     * @return {Promise<void>}
     */
    protected editBenefitLimit = async (
        req: Request<
            {
                code: string;
            },
            unknown,
            BenefitLimit
        >,
        res: Response<WithId<BenefitLimit>>
    ): Promise<void> => {
        try {
            const benefitLimit = req.body;
            const { code } = req.params;
            this.logger.log(`Editing benefit limit for code: ${code}`);

            if (!isContractKeyValid(benefitLimit.code)) {
                return handleInvalidRequest('code', res);
            }
            if (!isValidString(benefitLimit.name)) {
                return handleInvalidRequest('name', res);
            }
            if (!isValidString(benefitLimit.type)) {
                return handleInvalidRequest('type', res);
            }
            if (!isValidString(benefitLimit.currency)) {
                return handleInvalidRequest('currency', res);
            }
            if (!isNumber(benefitLimit.amount)) {
                return handleInvalidRequest('amount', res);
            }
            benefitLimit.updated = new Date();

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<BenefitLimit>('entities', 'benefitLimits');

            const result = await collection.updateOne(
                { code },
                { $set: benefitLimit },
                {
                    upsert: true
                }
            );
            await this.miStreamBenefitLimitSender.send(MiEvents.BENEFIT_LIMIT, benefitLimit);

            if (result) {
                this.logger.log(`Success: Editing benefit limit for code: ${code}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Editing benefit limit for code: ${code}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to edit benefit limit for code: ${req.params.code || 'no code given in request'}`,
                data: { ...req.params, ...req.body }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Delete benefit limit by code
     * @param {Request<{code: string}, unknown, unknown>} req
     * @param {Response<WithId<BenefitLimit>>} res
     * @return {Promise<void>}
     */
    protected deleteBenefitLimit = async (
        req: Request<
            {
                code: string;
            },
            unknown,
            unknown
        >,
        res: Response<WithId<BenefitLimit>>
    ): Promise<void> => {
        const { code } = req.params;
        try {
            this.logger.log(`Delete benefit limit with code: ${code}`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<BenefitLimit>('entities', 'benefitLimits');

            const result = collection.findOneAndDelete({
                code
            });

            const trace = this.auditClient.getTrace(Namespace.EUOPS, code);
            await this.auditClient.reportAction(trace, {
                message: `EntitlementBenefitApi: Benefit limit delete audit for code: ${code}`,
                data: { ...result }
            });

            if (result) {
                this.logger.log(`Success: Delete benefit limit for code: ${code}`);
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                this.logger.log(`No results: Delete benefit limit for code: ${code}`);
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed to delete benefit limit with code ${code || 'no code given in request'}`,
                data: { code }
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
}

/**
 * Handle invalid request
 * @param {string} name
 * @param {e.Response} res
 */
function handleInvalidRequest(name: string, res: Response) {
    const response: ErrorResponse = {
        status: 'INVALID_REQ',
        error: { code: 'ERROR', msg: `Invalid format of ${name}` }
    };
    return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
}

function isContractKeyValid(code: unknown) {
    return typeof code === 'string' && code.length > 0;
}

function isCustomerRequestIdValid(customerRequestId: unknown) {
    return isArray(customerRequestId) && customerRequestId.length > 0;
}

function isBenefitLimitsValid(benefitLimits: unknown) {
    return isArray(benefitLimits) && benefitLimits.length > 0 && benefitLimits.every((limit) => typeof limit === 'string');
}

function isNumber(val: unknown): val is number {
    return typeof val === 'number';
}

function isValidString(val: unknown): val is string {
    return typeof val === 'string' && val.length > 0;
}
