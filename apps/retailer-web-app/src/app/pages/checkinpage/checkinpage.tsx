import React, { useCallback, useEffect, useState } from 'react';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '../../components/ui/dialog';
import { But<PERSON> } from '../../components/ui/button';
import { fetchDataByIdCheckins } from '../../services/generalService';
import { useAuth } from '../../contexts/AuthContext';
import Condition from '../condition-report/condition-report';
import Checklist from '../checklist/checklist';
import Termsandcondition from '../terms&condition/termsandcondition';
import Icon from '../../../assets/icomoon/icon';
import { useCommonStore } from '../../contexts/CommonStore';
import { checkInPostEndHire, entCheckin } from '../../services/generalService';
import PopUp from '../../components/PopUp/PopUp';

const stepsData = [
    { name: 'Condition Report', complete: false },
    { name: 'Checklist', complete: false },
    { name: 'Terms and Conditions', complete: false }
];
interface Step {
    name: string;
    complete: boolean;
}

export default function Checkinpage(props: any) {
    const authContext = useAuth();
    const [steps, setSteps] = useState(stepsData);
    const [checkInData, setCheckInData] = useState<any | null>(null);
    const [currentStep, setCurrentStep] = useState(0);
    const isComplete = (step: Step): boolean => step.complete;
    const current = (index: number): boolean => index === steps.findIndex((step: Step) => !step.complete);
    const { selectedRetailerData, checkInPageDetails, setCheckInPageDetails } = useCommonStore() || {};
    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
    const [hireTaskId, setHireTaskId] = useState(null);
    const [isSecondDialogOpen, setIsSecondDialogOpen] = useState(false);
    const confirmNotifyEnterpriseCheckin = (hireTaskId: any) => {
        setHireTaskId(hireTaskId);
        setIsConfirmDialogOpen(true);
    };
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [apiStatus, setApiStatus] = useState<number>();
    const _tasks: any[] = []; // Define _tasks

    const deleteTask = (key: string) => {
        const index = _tasks.findIndex((item) => item.id === key);

        if (index >= 0) {
            console.debug(`deleting hire-task: ${key} x ${index}`);
            _tasks.splice(index, 1);
        }
    };
    const notifyEnterpriseCheckin = (hireTaskId: any) => {
        entCheckin(hireTaskId, authContext?.authUser?.data.authorization)
            .then(() => {
                deleteTask(hireTaskId);
                // window.location.reload();
            })
            .catch(() => {
                alert('Checkin unsuccessful - Enterprise Check in Notification failed');
            });
    };
    const checkInUserValue = async () => {
        resetStepper();
        setCurrentStep(0);
    };

    useEffect(() => {
        checkInUserValue();
    }, []);
    const resetStepper = () => {
        setSteps(stepsData.map((step) => ({ ...step, complete: false })));
        setCurrentStep(0);
    };
    const [validated, setValidated] = useState(false);
    const [termsValidated, setTermsValidated] = useState(false);

    const validateFields = useCallback(
        (dt: any) => {
            setValidated(dt);
        },
        [validated]
    );

    const termsCondValidateFields = useCallback(
        (dt: any) => {
            setTermsValidated(dt);
        },
        [validated]
    );

    const renderCheckInStep = (data?: any) => {
        switch (currentStep) {
            case 0:
                return (
                    <Condition
                        data={data}
                        validateFields={validateFields}
                    />
                );
            case 1:
                return <Checklist />;
            case 2:
                return <Termsandcondition termsCondValidateFields={termsCondValidateFields} />;
            default:
                return null;
        }
    };
    const prevStep = (from: string) => {
        if (from === 'checkin') {
            if (currentStep > 0) {
                setSteps((prevSteps) => prevSteps.map((step, index) => (index === currentStep - 1 ? { ...step, complete: false } : step)));
                setCurrentStep((prevStep) => prevStep - 1);
            }
        }
    };
    const nextStep = (from?: string) => {
        if (from === 'checkin' && validated) {
            setSteps((prevSteps) => prevSteps.map((step, index) => (index === currentStep ? { ...step, complete: true } : step)));
            setCurrentStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
        }
        if (from === 'final' && termsValidated) {
            checkInPostEndHire(checkInPageDetails, authContext?.authUser?.data.authorization, selectedRetailerData.id, setApiStatus)
                .then(() => {
                    setIsOpen(true);

                    setCheckInPageDetails && setCheckInPageDetails({});
                    setTimeout(() => {
                        document.getElementById('cancelBtn')?.click();
                    }, 1000);
                    if (checkInPageDetails?.taskId) {
                        const element = document.getElementById(`vehicle-returning-${checkInPageDetails?.taskId}`);
                        if (element) {
                            setTimeout(() => {
                                element.remove();
                            }, 2000);
                        }
                    }
                })
                .catch(() => {
                    setIsOpen(true);

                    setCheckInPageDetails && setCheckInPageDetails({});
                    setTimeout(() => {
                        document.getElementById('cancelBtn')?.click();
                    }, 1000);
                });
        }
    };
    const handleCancel = () => {
        setCheckInPageDetails && setCheckInPageDetails({});
        setValidated(false);

        props.callbackdata(true);
    };
    function handleClose() {
        setIsSecondDialogOpen(false);
    }
    const getHireData = async () => {
        try {
            const dataOnCheckInClick = await fetchDataByIdCheckins(props.id, selectedRetailerData.id, authContext?.authUser?.data.authorization);
            setCheckInData(dataOnCheckInClick?.mobilityTask);
        } catch (error) {
            console.error('Error fetching data:', error);
        }
    };
    return (
        <>
            {props.showCheckin && (
                <div className="row-tb-gutter col-xs-12">
                    <Dialog>
                        <DialogTrigger asChild>
                            {props.checkinBtn && !props.isEnterprise && (
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        checkInUserValue();
                                        getHireData();
                                    }}
                                    className="rounded-none w-full aaButton"
                                    data-testid="checkinBtn"
                                    style={{ cursor: props.isLCH || props.pncCheck ? 'not-allowed' : 'pointer', opacity: props.isLCH || props.pncCheck ? 0.5 : 1 }}
                                    disabled={props.isLCH || props.pncCheck}
                                >
                                    Check in
                                </Button>
                            )}
                        </DialogTrigger>
                        {checkInData && (
                            <DialogContent className="p-0 rounded-none max-w-full h-screen dialogContent">
                                <DialogHeader>
                                    <DialogTitle>
                                        <div className="modal-header">
                                            <h3 className="modal-title">Checking in {checkInData?.rental?.hireVehicle?.regNo}</h3>
                                        </div>
                                    </DialogTitle>
                                </DialogHeader>
                                <div className="grid">
                                    <div className="modal-steps">
                                        <div className="steps">
                                            {steps.map((step, index) => (
                                                <div
                                                    key={index}
                                                    className={`step-container ${step.complete ? 'complete' : ''}`}
                                                >
                                                    <span className="step">
                                                        {isComplete(step) && !current(index) ? <div className="border"></div> : <span>{index + 1}</span>}
                                                        {isComplete(step) && !current(index) && (
                                                            <Icon
                                                                icon="tick"
                                                                style={{ width: '15px', height: '18px' }}
                                                                color="#07828d"
                                                                className="flex100"
                                                            />
                                                        )}
                                                    </span>
                                                    {isComplete(step) && !current(index) && <div className="stepper"></div>}
                                                    <div>{step.name}</div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                    {renderCheckInStep(checkInData)}
                                </div>

                                <DialogFooter className="px-4 dialogFooter">
                                    <DialogClose asChild>
                                        <Button
                                            id="cancelBtn"
                                            onClick={handleCancel}
                                            className="mr-7 btn-default-aa"
                                            style={{ border: '1px solid #07828d' }}
                                        >
                                            Cancel
                                        </Button>
                                    </DialogClose>

                                    <Button
                                        onClick={() => prevStep('checkin')}
                                        className="mr-7 btn-default-aa"
                                        disabled={currentStep === 0}
                                        data-testid="prevBtn"
                                    >
                                        Prev
                                    </Button>
                                    {currentStep === steps.length - 1 ? (
                                        <Button
                                            data-testid="finishBtn"
                                            disabled={termsValidated ? false : true}
                                            style={{ opacity: termsValidated ? 1 : 0.5 }}
                                            className="px-4 py-2 btn-primary-aa"
                                            onClick={() => {
                                                nextStep('final');
                                                const finishBtn = document.querySelector('[data-testid="finishBtn"]');
                                                if (finishBtn) {
                                                    finishBtn.textContent = 'Please wait...';
                                                    (finishBtn as HTMLElement).style.opacity = '0.5';
                                                }
                                            }}
                                        >
                                            Finish
                                        </Button>
                                    ) : (
                                        <Button
                                            disabled={validated ? false : true}
                                            style={{ opacity: validated ? 1 : 0.5 }}
                                            onClick={() => nextStep('checkin')}
                                            className="px-4 py-2 btn-primary-aa"
                                            data-testid="nextBtn"
                                        >
                                            Next
                                        </Button>
                                    )}
                                </DialogFooter>
                            </DialogContent>
                        )}
                    </Dialog>
                    {props.isEnterprise && (
                        <Button
                            data-testid="notifyEntBtn"
                            variant="outline"
                            onClick={() => confirmNotifyEnterpriseCheckin(checkInData)}
                            className="rounded-none w-full aaButton"
                        >
                            {' '}
                            Notify Enterprise Check in
                        </Button>
                    )}
                </div>
            )}
            <Dialog
                open={isConfirmDialogOpen}
                onOpenChange={setIsConfirmDialogOpen}
            >
                <DialogContent className="top-40 left-1/2 fixed w-[300px] transform -translate-x-1/2">
                    <DialogHeader className="border-[#E5E5E5] bg-[#08828D] p-4 border-b h-[56px] text-sm leading-5">
                        <DialogTitle className="text-white">Confirm</DialogTitle>
                    </DialogHeader>
                    <DialogDescription className="p-4 w-[300px] h-[99px] max-h-fit">
                        This will notify the 3rd party to end the hire. Are you sure you want to Check-In the 3rd party hire?
                    </DialogDescription>
                    <DialogFooter className="border-[#E5E5E5] p-[15px] border-t w-[300px] h-[71px]">
                        <Button
                            data-testid="cancelBtn"
                            variant="outline"
                            onClick={() => setIsConfirmDialogOpen(false)}
                            className="border-[#07829D] bg-[#E6E6E6] p-[6px_20px] border rounded-none w-[84px] h-[40px] cursor-pointer"
                        >
                            Cancel
                        </Button>
                        <Button
                            data-testid="confirmBtn"
                            onClick={() => {
                                notifyEnterpriseCheckin(hireTaskId);
                                setIsConfirmDialogOpen(false);
                                setIsSecondDialogOpen(true);
                            }}
                            className="border-[#3363B] bg-[#05565D] m-0 ml-[30px] p-[6px_20px] border rounded-none w-[91px] h-[40px]"
                        >
                            Confirm
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
            <Dialog
                open={isSecondDialogOpen}
                onOpenChange={setIsSecondDialogOpen}
            >
                <DialogContent className="top-[36%] shadow-lg sm:max-w-[90vw] lg:max-w-[65vw] h-[400px]">
                    <div className="flex flex-col h-full text-center status-modal">
                        <div className={`h-2/3 p-4 bg-[#08828d] header`}>
                            <DialogHeader className="my-[45px] text-center">
                                <DialogTitle>
                                    <div className="flex justify-center items-center text-center">
                                        <div className="outer-circle">
                                            <Icon
                                                icon="tick"
                                                size={60}
                                                color="#fff"
                                                className="flex100 icon"
                                            />
                                        </div>
                                    </div>
                                </DialogTitle>
                                <p className="mt-8 title">Checkin Successful</p>
                            </DialogHeader>
                        </div>
                        <div className="bg-[#e1e1e1] p-[35px_0_0_0] border-l min-w-auto h-[155px] min-h-auto">
                            <div>
                                {' '}
                                {/* Add your content here */}
                                <h5 className="mb-4 text-[#000000]">Notified Enterprise Check in</h5>
                                <Button
                                    onClick={handleClose}
                                    className="bg-[#08828d] w-36 h-10"
                                >
                                    Ok
                                </Button>
                            </div>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
            {isOpen && (
                <PopUp
                    isOpen={isOpen}
                    header={`${apiStatus === 200 ? 'Checkin Successfully' : 'Error in Checkin'}`}
                    setIsOpen={setIsOpen}
                    message={`${apiStatus === 200 ? 'Vehicle Checkin Successfully' : 'Vehicle checkin Unsuccessful'}`}
                    response={apiStatus ?? 0}
                />
            )}
        </>
    );
}
