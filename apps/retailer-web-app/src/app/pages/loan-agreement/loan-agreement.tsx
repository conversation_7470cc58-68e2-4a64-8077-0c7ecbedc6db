import React from 'react';
import './loanagreement.css';
import Moment from 'moment';
import { useCommonStore } from '../../contexts/CommonStore';

export default function LoanAgreement(props: any) {
    const { networkCode } = useCommonStore() || {};
    console.log('>>>>>>>>>>>>>>>>>>', props);
    return (
        <div
            className="px-4 manualHeightMainloan"
            id="loanagreement"
        >
            <div className="h-full row scroll-wrapper">
                <div
                    className="px-4 py-3 h-full"
                    ng-controller="loanAgreementController as ctrl"
                    ng-init="ctrl.init()"
                >
                    <fieldset>
                        <legend className="section-title">Hirer details</legend>
                        <div className="row">
                            <div className="">
                                <span className="block key">Name:</span>
                                <span className="block val">{props?.data?.rental?.collectLocation?.name}</span>
                            </div>
                        </div>
                        <div className="row">
                            <div className="">
                                <span className="block key">Address:</span>
                                <span className="block val">{props?.data?.rental?.collectLocation?.address}</span>
                            </div>
                        </div>
                        <div className="gap-4 grid grid-cols-9">
                            <div className="col-span-3">
                                <span className="block key">E-Mail:</span>
                                <span className="block val">{props?.data?.rental?.collectLocation?.email || '-'}</span>
                            </div>
                            <div className="col-span-3">
                                <span className="block key">Telephone:</span>
                                <span className="block val">{props?.data?.rental?.collectLocation?.telephone}</span>
                            </div>
                        </div>
                        <hr />
                    </fieldset>

                    <fieldset>
                        <legend className="section-title">Loanee / Main Driver Information</legend>

                        <div className="gap-4 grid grid-cols-9">
                            <div className="col-span-3">
                                <span className="block key">Name:</span>
                                <span className="block val">{props?.data?.rental?.mainDriver?.contact?.name}</span>
                            </div>
                            <div className="col-span-3">
                                <span className="block key">Address:</span>
                                <span className="block val">{props?.data?.rental?.mainDriver?.address?.addressLines.join(', ')}</span>
                            </div>
                        </div>
                        <div className="gap-4 grid grid-cols-9">
                            <div className="col-span-3">
                                <span className="block key">DOB:</span>
                                <span className="block val">{Moment(props?.data?.rental?.mainDriver?.dateOfBirth).format('DD MMM YYYY')}</span>
                            </div>
                            <div className="col-span-3">
                                <span className="block key">Telephone:</span>
                                <span className="block val">{props?.data?.rental?.mainDriver?.contact?.telephone}</span>
                            </div>
                            <div className="col-span-3">
                                <span className="block key">E-Mail:</span>
                                <span className="block val">{props?.data?.rental?.mainDriver?.contact?.email}</span>
                            </div>
                        </div>
                        <div className="gap-4 grid grid-cols-9">
                            <div className="col-span-3">
                                <span className="block key">Licence number:</span>
                                <span className="block val">{props?.data?.rental?.mainDriver?.licenceNumber}</span>
                            </div>
                            <div className="col-span-3">
                                <span className="block key">Expiry date:</span>
                                <span className="block val">{Moment(props?.data?.rental?.mainDriver?.licenceExpire).format('DD MMM YYYY')}</span>
                            </div>
                            <div className="col-span-3">
                                <span className="block key">Issued at:</span>
                                <span className="block val">{props?.data?.rental?.mainDriver?.licenceType}</span>
                            </div>
                        </div>
                        <hr />
                    </fieldset>
                    {props?.data?.rental?.additionalDrivers?.length > 0 && (
                        <fieldset>
                            <div
                                className="no-border"
                                ng-if="ctrl.report().rental().additionalDrivers().length"
                            >
                                <legend className="section-title">Additional drivers</legend>
                                {props?.data?.rental?.additionalDrivers?.map((driver: any, index: number) => (
                                    <div
                                        key={index}
                                        className="additional-driver"
                                    >
                                        <div className="gap-4 grid grid-cols-9">
                                            <div className="col-span-3">
                                                <span className="block key">Name:</span>
                                                <span className="block val">{driver?.contact?.name}</span>
                                            </div>
                                            <div className="col-span-3">
                                                <span className="block key">DOB:</span>
                                                <span className="block val">{Moment(driver?.dateOfBirth).format('DD MMM YYYY')}</span>
                                            </div>
                                            <div className="col-span-3">
                                                <span className="block key">Driver type:</span>
                                                <span className="block val">Additional</span>
                                            </div>
                                        </div>

                                        <div className="gap-4 grid grid-cols-9">
                                            <div className="col-span-3">
                                                <span className="block key">Licence number:</span>
                                                <span className="block val">{driver?.licenceNumber}</span>
                                            </div>
                                            <div className="col-span-3">
                                                <span className="block key">Expiry date:</span>
                                                <span className="block val">{Moment(driver?.licenceExpire).format('DD MMM YYYY')}</span>
                                            </div>
                                            <div className="col-span-3">
                                                <span className="block key">Issued at:</span>
                                                <span className="block val">{driver?.licenceType}</span>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <hr />
                        </fieldset>
                    )}

                    <fieldset>
                        <div>
                            <legend className="section-title">Vehicle details</legend>

                            <div className="gap-4 grid grid-cols-9">
                                <div className="col-span-3">
                                    <span className="block key">Registration:</span>
                                    <span className="block val">{props?.data?.rental?.hireVehicle?.regNo}</span>
                                </div>
                                <div className="col-span-3">
                                    <span className="block key">Make:</span>
                                    <span className="block val">{props?.data?.rental?.hireVehicle?.make?.name}</span>
                                </div>
                                <div className="col-span-3">
                                    <span className="block key">Model:</span>
                                    <span className="block val">{props?.data?.rental?.hireVehicle?.model?.name}</span>
                                </div>
                                {networkCode === 'POR' && (
                                    <div className="col-span-3">
                                        <span className="block key">Vin:</span>
                                        <span className="block val">{props?.data?.rental?.hireVehicle?.vin}</span>
                                    </div>
                                )}
                            </div>
                            <div className="gap-4 grid grid-cols-9">
                                <div className="col-span-3">
                                    <span className="block key">Colour:</span>
                                    <span className="block val">{props?.data?.rental?.hireVehicle?.colour}</span>
                                </div>
                                <div className="col-span-3">
                                    <span className="block key">Transmission:</span>
                                    <span className="block val">{props?.data?.rental?.hireVehicle?.transmissionType}</span>
                                </div>
                                <div className="col-span-3">
                                    <span className="block key">Fuel type:</span>
                                    <span className="block val">{props?.data?.rental?.hireVehicle?.fuelType}</span>
                                </div>
                            </div>
                        </div>
                        <hr />
                    </fieldset>
                    <fieldset>
                        <div>
                            <div className="">
                                <div className="col-sm-12">
                                    <span className="block key">Pickup address:</span>
                                    <span className="block val">
                                        <span>{props?.data?.rental?.collectLocation?.name}, </span>
                                        <span>{props?.data?.rental?.collectLocation?.address}</span>
                                    </span>
                                </div>
                                <div className="col-sm-12">
                                    <span className="block key">Repairing address:</span>
                                    <span className="block val">
                                        <span>{props?.data?.rental?.repairLocation?.name}, </span>
                                        <span>{props?.data?.rental?.repairLocation?.address}</span>
                                    </span>
                                </div>
                                {props?.data?.recovery?.destination?.name && (
                                    <div className="col-sm-12">
                                        <span className="block key">Return address:</span>
                                        <span className="block val">
                                            <span>{props?.data?.recovery?.destination?.name ? props?.data?.recovery?.destination?.name + ', ' : ''} </span>
                                            <span>{props?.data?.recovery?.destination?.text - props?.data?.recovery?.destination?.area} </span>
                                        </span>
                                    </div>
                                )}
                                {!props?.data?.recovery?.destination?.name && (
                                    <div className="col-sm-12">
                                        <span className="block key">Return address:</span>
                                        <span className="block val">
                                            <span>{props?.data?.rental?.dropOffLocation?.name ? props?.data?.rental?.dropOffLocation?.name + ', ' : ''} </span>
                                            <span>{props?.data?.rental?.dropOffLocation?.address}</span>
                                        </span>
                                    </div>
                                )}

                                <div className="col-sm-12">
                                    <span className="block key">Insurance excess amount:</span>
                                    <span className="block val">&pound;{props?.data?.rental?.insurance?.insuranceOption?.excess}.00</span>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>
        </div>
    );
}
