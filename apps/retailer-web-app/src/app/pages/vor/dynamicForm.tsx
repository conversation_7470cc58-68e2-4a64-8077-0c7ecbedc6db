'use client';

import React, { useState, useImperative<PERSON><PERSON><PERSON>, forwardRef, useEffect } from 'react';
import { FormField, FormData, FormErrors } from './form';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { Label } from '../../components/ui/label';
import { Checkbox } from '../../components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '../../components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { format } from 'date-fns';
import Icon from '../../../assets/icomoon/icon';
import ReactDatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface TemplateOptions {
    label?: string;
    placeholder?: string;
    required?: boolean;
    minlength?: string;
    maxlength?: string;
    datepickerPopup?: string;
    options?: { value: string; label: string }[];
    fields?: any;
}

interface DynamicFormProps {
    data?: any;
    fields: FormField[];
    model?: any;
    isPorsche?: boolean;
    onSubmit?: (data: FormData) => void;
    onValidityChange?: (isValid: boolean) => void;
    disableAllField?: boolean;
}

// Mock incoming props for test cases

export interface DynamicFormRef {
    submitForm: () => void;
    isFormValid: () => boolean;
}

export const DynamicForm = forwardRef<DynamicFormRef, DynamicFormProps>(({ data, fields, isPorsche, onSubmit, onValidityChange, disableAllField }, ref) => {
    const [formData, setFormData] = useState<FormData>({});
    const [errors, setErrors] = useState<FormErrors>({});
    const [items, setItems] = useState<any[]>([]);
    // const [listItem, setListItem] = useState<any>({"fields":[]})
    const [dateState, setDateState] = useState<{ [key: string]: Date | null }>({});
    const [timeState, setTimeState] = useState<{ [key: string]: string }>({});
    const [isOpenState, setIsOpenState] = useState<{ [key: string]: boolean }>({});
    let listItem: any = { fields: [] };
    const handleDateChange = (key: string, date: Date | null, datepickerPopup?: string) => {
        setDateState((prevState) => ({ ...prevState, [key]: date }));
        const formattedDate = date ? format(date, 'yyyy-MM-dd') : '';
        handleInputChange(key, formattedDate);
        setIsOpenState((prevState) => ({ ...prevState, [key]: false }));
    };

    useEffect(() => {
        if (data.referenceData?.['parts']) {
            console.log(listItem, 'listItemnew');
            const newItems = data.referenceData['parts'].map((item: any) => ({
                fields: item.map((element: any, index: number) => ({
                    key: listItem.fields[index]?.key,
                    templateOptions: listItem.fields[index]?.templateOptions,
                    type: listItem.fields[index]?.type,
                    assignValue: element
                }))
            }));
            setItems(newItems);
        }
    }, [data.referenceData]);
    // setItems(data.referenceData?.['parts'] || [])

    const handleInputChange = (key: string, value: string | boolean | string[], dateHour: any = '') => {
        if (dateHour === '-hours') {
            key = key.replace('-hours', '');
            const currentDate = new Date(formData[key] as string);
            currentDate.setHours(Number(value));
            value = currentDate.toISOString();
        } else if (dateHour === '-minutes') {
            key = key.replace('-minutes', '');
            const currentDate = new Date(formData[key] as string);
            currentDate.setMinutes(Number(value));
            value = currentDate.toISOString();
        }
        const newFormData = { ...formData, [key]: value };
        setFormData(newFormData);
        if (errors[key]) {
            const newErrors = { ...errors };
            delete newErrors[key];
            setErrors(newErrors);
        }
        validateForm(newFormData);
    };

    const validateForm = (data: FormData = formData): boolean => {
        const newErrors: FormErrors = {};
        fields.forEach((field) => {
            const { key, templateOptions, type } = field;
            const value = data[key];

            if (templateOptions.required && (value === undefined || value === '')) {
                newErrors[key] = 'This field is required';
            } else if (type === 'text' || type === 'textarea') {
                const minLength = parseInt(templateOptions.minlength || '0');
                const maxLength = parseInt(templateOptions.maxlength || '2000');
                const strValue = value as string;
                if (strValue && strValue.length < minLength) {
                    newErrors[key] = `Minimum length is ${minLength} characters`;
                } else if (strValue && strValue.length > maxLength) {
                    newErrors[key] = `Maximum length is ${maxLength} characters`;
                }
            } else if (type === 'datetimepicker') {
                const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                if (!dateRegex.test(value as string)) {
                    newErrors[key] = 'Date must be in the format YYYY-MM-DD';
                }
            }
        });

        setErrors(newErrors);
        const isValid = Object.keys(newErrors).length === 0;
        onValidityChange?.(isValid);
        return isValid;
    };

    const submitForm = () => {
        if (validateForm()) {
            const itemNew = items.map((item) => item.fields.map((field: any) => field.value));
            if (itemNew.length) {
                formData['parts'] = itemNew;
            }
            // items.forEach((item, index) => {
            // })
            onSubmit?.(formData);
        } else {
            console.log('Form has errors');
        }
    };

    useImperativeHandle(ref, () => ({
        submitForm,
        isFormValid: () => validateForm()
    }));

    const addItem = () => {
        setItems([...items, listItem]);
    };

    const removeLast = () => {
        setItems(items.slice(0, -1));
    };

    const handleItemChange = (itemIndex: number, fieldKey: string, newValue: string) => {
        setItems((prevItems) =>
            prevItems.map((item, index) => {
                if (index === itemIndex) {
                    return {
                        ...item,
                        fields: item.fields.map((field: any) => (field.key === fieldKey ? { ...field, value: newValue } : field))
                    };
                }
                return item;
            })
        );
    };

    const renderField = (field: FormField) => {
        const { type, key, templateOptions } = field;
        const { label, placeholder, required, minlength, maxlength, datepickerPopup, options } = templateOptions;
        const error = errors[key];
        if (type === 'list') {
            listItem = { fields: (templateOptions as TemplateOptions).fields };
        }
        const commonProps = {
            id: key,
            required,
            'aria-invalid': !!error,
            'aria-describedby': error ? `${key}-error` : undefined
        };

        const errorClass = error ? 'border-red-500' : '';
        switch (type) {
            case 'text':
            case 'input':
                return (
                    <div
                        key={key}
                        className="mb-4"
                    >
                        <Label htmlFor={key}>{label} *</Label>
                        <Input
                            type="text"
                            placeholder={placeholder}
                            minLength={parseInt(minlength || '0')}
                            maxLength={parseInt(maxlength || '2000')}
                            disabled={disableAllField}
                            value={(formData[key] as string) || data?.referenceData?.[key] || ''}
                            onChange={(e) => handleInputChange(key, e.target.value)}
                            className={`mt-1 bg-white rounded-none ${errorClass}`}
                            {...commonProps}
                        />
                        {/* {error && <p id={`${key}-error`} className="mt-1 text-xs text-red-500">{error}</p>} */}
                    </div>
                );
            case 'textarea':
                return (
                    <div
                        key={key}
                        className="mb-4"
                    >
                        <Label htmlFor={key}>{label} *</Label>
                        <Textarea
                            placeholder={placeholder}
                            minLength={parseInt(minlength || '0')}
                            maxLength={parseInt(maxlength || '2000')}
                            disabled={disableAllField}
                            value={(formData[key] as string) || data?.referenceData?.[key] || ''}
                            defaultValue={data?.referenceData?.[key] || ''}
                            onChange={(e) => handleInputChange(key, e.target.value)}
                            className={`mt-1 bg-white rounded-none ${errorClass} ${disableAllField ? 'bg-gray-200' : ''}`}
                            {...commonProps}
                        />
                        {/* {error && <p id={`${key}-error`} className="mt-1 text-xs text-red-500">{error}</p>} */}
                    </div>
                );
            case 'checkbox':
                return (
                    <div
                        key={key}
                        className="mb-4 flex items-center space-x-2"
                    >
                        <Checkbox
                            checked={(formData[key] as boolean) || false}
                            onCheckedChange={(checked) => handleInputChange(key, checked)}
                            className={errorClass}
                            {...commonProps}
                        />
                        <Label htmlFor={key}>{label}</Label>
                        {/* {error && <p id={`${key}-error`} className="ml-2 text-xs text-red-500">{error}</p>} */}
                    </div>
                );
            case 'radio':
                return (
                    <div
                        key={key}
                        className="mb-4"
                    >
                        <Label>{label}</Label>
                        <RadioGroup
                            onValueChange={(value) => handleInputChange(key, value)}
                            value={formData[key] as string}
                            className="mt-1"
                        >
                            {options?.map((option) => (
                                <div
                                    key={option.value}
                                    className="flex items-center space-x-2"
                                >
                                    <RadioGroupItem
                                        value={option.value}
                                        id={`${key}-${option.value}`}
                                        className={errorClass}
                                    />
                                    <Label htmlFor={`${key}-${option.value}`}>{option.label}</Label>
                                </div>
                            ))}
                        </RadioGroup>
                        {/* {error && <p id={`${key}-error`} className="mt-1 text-xs text-red-500">{error}</p>} */}
                    </div>
                );
            case 'select':
                return (
                    <div
                        key={key}
                        className="mb-4"
                    >
                        <Label htmlFor={key}>{label} *</Label>
                        <Select
                            data-testid="selectItem"
                            onValueChange={(value) => handleInputChange(key, value)}
                            value={formData[key] as string}
                        >
                            <SelectTrigger
                                className={`mt-1 ${errorClass}`}
                                {...commonProps}
                            >
                                <SelectValue placeholder={placeholder} />
                            </SelectTrigger>
                            <SelectContent>
                                {options?.map((option) => (
                                    <SelectItem
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {/* {error && <p id={`${key}-error`} className="mt-1 text-xs text-red-500">{error}</p>} */}
                    </div>
                );
            case 'datetimepicker':
                return (
                    <div
                        key={key}
                        className="mb-4 dynamicForm"
                    >
                        <Label htmlFor={key}>{label} *</Label>
                        <div className="relative flex items-center space-x-4 ">
                            <div className="input-group w-72 rounded-none">
                                <Input
                                    type="text"
                                    data-testid="datePicker"
                                    placeholder={placeholder}
                                    disabled={disableAllField}
                                    value={formData[key] || data?.referenceData?.[key] || ''}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        handleInputChange(key, value);
                                        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                                        if (!dateRegex.test(value)) {
                                            setErrors((prevErrors) => ({ ...prevErrors, [key]: '' }));
                                        } else {
                                            setErrors((prevErrors) => {
                                                const newErrors = { ...prevErrors };
                                                delete newErrors[key];
                                                return newErrors;
                                            });
                                        }
                                    }}
                                    className={`form-control w-20 ${errorClass} bg-[white] rounded-none`}
                                    {...commonProps}
                                />
                                <span className="input-group-btn ">
                                    <button
                                        type="button"
                                        data-testid="datePickerIcon"
                                        className="btn btn-default rounded-none"
                                        onClick={() => setIsOpenState((prevState) => ({ ...prevState, [key]: !prevState[key] }))}
                                    >
                                        <Icon
                                            icon="calendar"
                                            size={20}
                                        />
                                    </button>
                                    {isOpenState[key] && (
                                        <div
                                            className="fixed inset-0 z-10"
                                            onClick={() => setIsOpenState((prevState) => ({ ...prevState, [key]: false }))}
                                        />
                                    )}
                                </span>
                            </div>
                            <div className="relative">
                                {isOpenState[key] && (
                                    <div className="absolute z-10 mt-2">
                                        <ReactDatePicker
                                            data-testid="reactDatePicker"
                                            selected={dateState[key]}
                                            onChange={(date: Date | null) => handleDateChange(key, date, datepickerPopup)}
                                            inline
                                            dateFormat={datepickerPopup || 'yyyy-MM-dd'}
                                        />
                                    </div>
                                )}
                            </div>
                            <div className="flex items-center space-x-0">
                                <Input
                                    type="number"
                                    data-testid="hours"
                                    value={timeState[`${key}-hours`] ? timeState[`${key}-hours`] : data?.referenceData?.[key] ? format(data?.referenceData?.[key], 'HH') : ''}
                                    // value={timeState[`${key}-hours`] || ''}
                                    placeholder="HH"
                                    min={0}
                                    disabled={disableAllField}
                                    max={23}
                                    onChange={(e) => {
                                        const hours = e.target.value;
                                        if (Number(e.target.value) <= 23) {
                                            setTimeState((prevState) => ({ ...prevState, [`${key}-hours`]: hours }));
                                            handleInputChange(`${key}-hours`, hours, '-hours');
                                        }
                                    }}
                                    className={`w-14 ${errorClass} bg-[white] rounded-none`}
                                    {...commonProps}
                                />
                                <span>:</span>
                                <Input
                                    data-testid="minutes"
                                    type="number"
                                    value={timeState[`${key}-minutes`] ? timeState[`${key}-minutes`] : data?.referenceData?.[key] ? format(data?.referenceData?.[key], 'mm') : ''}
                                    placeholder="MM"
                                    min={0}
                                    disabled={disableAllField}
                                    max={59}
                                    onChange={(e) => {
                                        if (Number(e.target.value) <= 59) {
                                            const minutes = e.target.value;
                                            setTimeState((prevState) => ({ ...prevState, [`${key}-minutes`]: minutes }));
                                            handleInputChange(`${key}-minutes`, minutes, '-minutes');
                                        }
                                    }}
                                    className={` w-14 ${errorClass} bg-[white] ] rounded-none`}
                                    {...commonProps}
                                />
                            </div>
                        </div>
                        {/* {error && <p id={`${key}-error`} className="mt-1 text-xs text-red-500">{error}</p>} */}
                    </div>
                );
            case 'list':
                return (
                    <div
                        key={key}
                        className="mb-4"
                    >
                        <div className="col-sm-10 flex justify-between mt-4">
                            <Label
                                htmlFor={key}
                                className="flex items-center text-gray-800 font-bold text-sm font-new-transport"
                            >
                                {label}
                            </Label>
                            <div className="col-sm-2">
                                <button
                                    type="button"
                                    className={`dyn dyn-Primary ${data?.referenceData ? 'cursor-not-allowed opacity-50' : ''}`}
                                    onClick={addItem}
                                >
                                    +
                                </button>
                                <button
                                    type="button"
                                    className={`dyn dyn-Danger ${data?.referenceData ? 'cursor-not-allowed opacity-50' : ''}`}
                                    onClick={removeLast}
                                >
                                    -
                                </button>
                            </div>
                        </div>
                        <div className="row flex justify-between text-[#333333] font-bold text-sm font-new-transport mt-3">
                            {items.length !== 0 &&
                                listItem.fields.map((item: any, index: number) => (
                                    <div
                                        key={index}
                                        className={`w-1/${Math.floor(listItem?.fields?.length)} ml-2`}
                                    >
                                        <b>{item?.templateOptions?.label}</b>
                                    </div>
                                ))}
                        </div>
                        {items.map((item, parentIndex) => (
                            <div
                                key={parentIndex}
                                className="flex justify-between space-x-4 mt-3"
                            >
                                {item?.fields.map((itemNew: any, index: number) => (
                                    <div
                                        key={index}
                                        className={`w-1/${Math.floor(item?.fields?.length)}`}
                                    >
                                        <Input
                                            type="text"
                                            value={itemNew.assignValue}
                                            onChange={(e) => handleItemChange(parentIndex, itemNew.key, e.target.value)}
                                            placeholder={itemNew?.templateOptions?.placeholder}
                                            disabled={disableAllField}
                                            className="form-control bg-white rounded-none"
                                            {...commonProps}
                                        />
                                    </div>
                                ))}
                            </div>
                        ))}
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <div className={`sub-panel ${isPorsche ? 'hide-estrepairdate' : ''}`}>
            <form
                className="space-y-6"
                noValidate
            >
                {fields.map(renderField)}
            </form>
        </div>
    );
});

DynamicForm.displayName = 'DynamicForm';
