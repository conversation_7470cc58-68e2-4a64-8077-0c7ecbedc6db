'use client';
import './vor.css';
import * as React from 'react';
import { Helmet } from 'react-helmet';
import { Search } from 'lucide-react';
import { format } from 'date-fns';
import { Button } from '../../components/ui/button';

import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Textarea } from '../../components/ui/textarea';
import { CaseHistory } from './caseHistory';
import { useEffect, useRef, useState } from 'react';
import { useEvent } from '../../components/Eventcontext';
import { DynamicForm, DynamicFormRef } from './dynamicForm';
import { FormData } from './form';
import { ReasonCodeDialog } from './reason-code-dialog';
import { useCommonStore } from '../../contexts/CommonStore';
import {
    fetchAddDays,
    fetchCarSwap,
    fetchDataById,
    fetchExtension,
    fetchExtensionUniq,
    fetchpreAuth,
    fetchRule,
    fetchVORFormData,
    postExtensionData,
    postpreAuth
} from '../../services/generalService';
import { useAuth } from '../../contexts/AuthContext';
import PopUp from '../../components/PopUp/PopUp';
import { useLocation } from 'react-router-dom';

export default function Vor() {
    const [isOpen, setIsOpen] = React.useState(false);
    const formRef = useRef<DynamicFormRef>(null);
    const [isFormValid, setIsFormValid] = useState(false);
    const { hireTasksData, networkCode, extensions, selectedRetailerData, pendingExtensions, setPendingExtensions } = useCommonStore() || {};
    const authContext = useAuth();
    const [dialogRecords, setDialogRecords] = useState([]);
    const [firstHireTask, setFirstHireTask] = useState<any>();
    const [selectedData, setSelectedData] = useState<any>({});
    const [disableFormFields, setDisableFormFields] = useState(false);
    const [comment, setComment] = useState('');
    const [commentTrigger, setCommentTrigger] = useState(false);
    const [apiStatus, setApiStatus] = useState<number>();
    const [dataFromDashboard, setDataFromDashboard] = useState();
    const [ccpData, setCcpData] = useState<any>({});
    const location = useLocation();
    const [isDisabledActiveExtn, setIsDisabledActiveExtn] = useState(false);
    const getVehicleTypeName = (supCode: any) => {
        if (supCode) {
            switch (supCode) {
                case 'AA':
                    return networkCode === 'POR' ? 'PMF' : networkCode === 'HYUA' ? 'HAF' : 'RAF';
                // return 'RAF';
                case 'OU':
                    return 'OUV';
                default:
                    return supCode.substring(0, 3);
            }
        }
    };
    const diffDaysBetweenDates = (dt1: any, dt2: any) => {
        const timeDiff = new Date(dt1).getTime() - new Date(dt2).getTime();
        return Math.abs(Math.round(timeDiff / (1000 * 3600 * 24)));
    };
    let hiretaskList = hireTasksData.map((item: any) => ({
        id: item.id,
        status: item.status,
        contactName: item.hireTask.rental.mainDriver?.contact?.name,
        vehicleRegistration: item.vehicle.registration,
        hireVehicleRegNo: item?.hireTask?.rental?.hireVehicle?.regNo,
        supplierTypeCode: getVehicleTypeName(item?.hireTask?.rental?.hireVehicle.supplierTypeCode),
        age: diffDaysBetweenDates(item.hireTask.rental.firstHireStart || item.vehicleSchedule.arrive, item.vehicleSchedule.complete),
        completeDate: item.vehicleSchedule.complete,
        customerRequestId: item.customerRequestId,
        supnetworkId: item.hireTask.rental.collectLocation.supplierNet.id,
        makeName: item.hireTask.rental.hireVehicle.make.name,
        newBooking: item.hireTask.rental.firstVorCreated || item.vehicleSchedule.create,
        hireStart: item.hireTask.rental.firstHireStart || item.vehicleSchedule.arrive,
        bookingEnd: item.vehicleSchedule.complete,
        customerGroup: item.hireTask.entitlement.customerGroup,
        customerGroupCode: item.hireTask.entitlement.customerGroup.code,
        arrive: item.vehicleSchedule.arrive,
        retailId: item.hireTask.rental.repairLocation.id
    }));

    //to filter hiretaskList to get latest swap tasks
    const filterLatestSwapTasks = (hireTasks: any) => {
        //crList have largest taskId for CrId
        const crList: { [key: string]: any } = {};
        //get latest taskId for CrId
        hireTasks.forEach((task: any) => {
            crList[task.customerRequestId] = crList[task.customerRequestId] || task;
            if (crList[task.customerRequestId].arrive < task.arrive) {
                crList[task.customerRequestId] = task;
            }
        });

        return hireTasks.filter((task: any) => {
            if (crList[task.customerRequestId].id !== task.id) {
                return false;
            }
            if (crList[task.customerRequestId]?.retailId !== selectedRetailerData.id) {
                return false;
            }
            return true;
        });
    };

    hiretaskList = filterLatestSwapTasks(hiretaskList);

    const groups = hiretaskList.map((i: any) => {
        return i.customerGroup;
    });
    const customerGroup: any = [...new Map(groups.map((item: any) => [item.code, item])).values()];

    const diffDay = (dt: Date) => {
        const now = new Date();
        now.setHours(0, 0, 0); // Set hours, minutes seconds to 0
        const timeDiff = new Date(dt).getTime() - now.getTime();
        const diff = timeDiff / (1000 * 3600 * 24);
        return Math.floor(diff) < 1;
    };

    const hasPending = (id: any) => {
        let flag = false;
        extensions.forEach((element: any) => {
            if (element.taskId === id && element.status === 1) {
                flag = true;
            }
        });
        if (pendingExtensions.some((pending: any) => pending === id)) {
            flag = true;
        }
        return flag;
    };

    const getExtensionColor = (data: any, id: any) => {
        if (diffDay(data) && !hasPending(id)) {
            return <span style={{ color: '#9e1b33' }}>Due for extension</span>;
        } else if (hasPending(id)) {
            return <span style={{ color: '#00a5e6' }}>Extension pending</span>;
        } else if (!diffDay(data) && !hasPending(id)) {
            return <span style={{ color: '#08828d' }}>Active extension</span>;
        }
        return null;
    };

    const isActiveExtn = (data: any, id: any) => {
        if (!diffDay(data) && !hasPending(id)) {
            setIsDisabledActiveExtn(true);
        } else {
            setIsDisabledActiveExtn(false);
        }
    };

    const [activeData, setActiveData] = useState<number>(0);
    const { eventTriggered, triggerFleetEvent, fleetEventTriggered, triggerEvent, triggerShowIconEvent } = useEvent();
    const [open, setOpen] = useState(false);
    const [selectedVorRecord, setSelectedVorRecord] = useState<any>({
        extraFields: [
            {
                templateOptions: {
                    maxlength: '2000',
                    required: true,
                    label: 'Action Taken',
                    placeholder: 'Enter what has been done so far to find the fault',
                    minlength: '10'
                },
                type: 'textarea',
                key: 'actionTaken'
            },
            {
                key: 'actionNext',
                type: 'textarea',
                templateOptions: {
                    placeholder: 'That your next action will be',
                    minlength: '10',
                    maxlength: '2000',
                    required: true,
                    label: 'Next Action'
                }
            }
        ]
    });
    const [defaultVorRecord, setDefaultVorRecord] = useState<any>({
        extraFields: []
    });
    const [historyData, setHistoryData] = useState<any>([]);
    const [filteredHiretaskList, setFilteredHiretaskList] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [searchDrop, setsearchDrop] = useState('');
    const [extensionDays, setExtensionDays] = useState('');
    const [showCCPFieds, setShowCCPFieds] = useState<boolean>(false);
    const [validAuthCodeTrigger, setValidAuthCodeTrigger] = useState<boolean>(false);
    const [validDaysRequestedTrigger, setValidDaysRequestedTrigger] = useState<boolean>(false);
    const [validCommentsTrigger, setValidCommentsTrigger] = useState<boolean>(false);
    // useEffect(() => {
    //  if(selectedExtraField){
    //   setSelectedExtraField(selectedExtraField);
    //  }
    // }, [selectedExtraField]);

    useEffect(() => {
        const initialize = async () => {
            if (hiretaskList.length > 0) {
                handleHistory(hiretaskList[0]);

                setFilteredHiretaskList(hiretaskList);
            } else {
                setDisableFormFields(true);
            }
        };
        if (location.state) {
            setDataFromDashboard(location.state);
        }
        initialize();
    }, []);

    useEffect(() => {
        // Filter the hiretaskList based on the search term
        // if(searchTerm){
        let filteredList = hiretaskList.filter(
            (item: any) =>
                item.customerGroupCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.vehicleRegistration.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.hireVehicleRegNo.toLowerCase().includes(searchTerm.toLowerCase())
        );
        if (searchDrop && searchDrop !== 'all') {
            filteredList = filteredList.filter((item: any) => item.customerGroupCode === searchDrop);
        }

        filteredList = filteredList.filter((task: any) => {
            if (dataFromDashboard) {
                if (hasPending(task.id)) {
                    return false;
                } else {
                    return diffDay(task.completeDate);
                }
            }
            return true;
        });

        setFilteredHiretaskList(filteredList);
        //  }
    }, [searchTerm, searchDrop, dataFromDashboard]);

    const setActiveIndex = (index: number): void => {
        setActiveData(index);
    };

    const handleHistory = async (hiretask: any) => {
        setDisableFormFields(false);
        setCommentTrigger(false);
        triggerEvent(false);
        triggerFleetEvent(true);
        triggerShowIconEvent(true);
        setFirstHireTask(hiretask);

        const hireTaskData = await fetchDataById(hiretask.customerRequestId, selectedRetailerData.id, authContext?.authUser?.data.authorization);
        const hireTasks = hireTaskData.filter((task: any) => {
            return task.createReason.id === 78 && (task.status === 'HIRE' || task.status === 'GARR');
        });

        if (hireTasks.length) {
            const historyRecords = await fetchExtension(authContext?.authUser?.data.authorization, selectedRetailerData.id, hireTasks[0].id);
            let historyRec = historyRecords.data;

            const ruleIds: any = [];
            historyRec.forEach((item: any) => {
                ruleIds.push(item.extensionRuleId);
            });
            const uniqueRuleIds = [...new Set(ruleIds)];
            uniqueRuleIds.forEach((ruleId) => {
                fetchRule(authContext?.authUser?.data.authorization, selectedRetailerData.id, ruleId);
            });

            const carSwapTasks: any = await fetchCarSwap(authContext?.authUser?.data.authorization, selectedRetailerData.id, hiretask.customerRequestId);
            const arrivalTimes: any = carSwapTasks.data.filter((i: any) => ['HIRE', 'GARR', 'COMP', 'CLSD'].includes(i.TASK_STATUS_CODE));

            if (arrivalTimes.length) {
                const carSwapMarkers = arrivalTimes.map((task: any) => {
                    return {
                        isCarSwapMarker: true,
                        taskId: task.TASK_ID,
                        // hireVrn: task.VEH_REG,
                        dateRequested: task.RES_ARRIVAL_TIME,
                        id: task.TASK_ID
                    };
                });
                historyRec = [...historyRec, ...carSwapMarkers];
            }
            historyRec = historyRec.sort((a: any, b: any) => new Date(a.dateRequested).getTime() - new Date(b.dateRequested).getTime());
            const initArr = [
                {
                    name: 'New Booking',
                    static: true,
                    dateRequested: hiretask.newBooking
                },
                {
                    name: 'Hire Start',
                    static: true,
                    dateRequested: hiretask.hireStart
                }
            ];
            const endArr = [
                {
                    name: 'Booking End',
                    static: true,
                    dateRequested: hiretask.bookingEnd
                }
            ];
            historyRec = [...initArr, ...historyRec];
            historyRec = [...historyRec, ...endArr];
            setHistoryData(historyRec);

            // setVorExtrafields(selectedVorRecord);

            // setHistoryData(historyRecords.data);
        }

        const popupData = await fetchVORFormData(authContext?.authUser?.data.authorization, selectedRetailerData.id, hiretask.supnetworkId || -1, hiretask.customerRequestId);
        setDialogRecords(popupData);
        if (hasPending(hiretask.id)) {
            const selectedDt = await fetchExtensionUniq(authContext?.authUser?.data.authorization, selectedRetailerData.id, hireTasks[0].id);
            setSelectedData(selectedDt.data);

            setComment(selectedDt.data.reviewComments);
            const pendingTaskList: any = await fetchRule(authContext?.authUser?.data.authorization, selectedRetailerData.id, selectedDt.data.extensionRuleId);
            let extrafields = pendingTaskList.data;
            if (!extrafields) {
                return;
            }
            extrafields = removeExtraDateField(extrafields);
            extrafields.hireTaskId = hireTasks[0].id;
            setSelectedVorRecord(extrafields);
            isActiveExtn(hireTasks[0].schedule?.complete, hireTasks[0].id);
            setDefaultVorRecord(extrafields);
        } else {
            setComment('');
            setCcpData({ authCode: '', daysRequested: '', comments: '' });
            setSelectedData({});
            popupData[0] = removeExtraDateField(popupData[0]);
            popupData[0].hireTaskId = hireTasks[0].id;
            setSelectedVorRecord(popupData[0]);
            isActiveExtn(hireTasks[0].schedule?.complete, hireTasks[0].id);
            setDefaultVorRecord(popupData[0]);
        }

        const addDaysData = {
            date: hiretask.bookingEnd,
            days: popupData[0].maxExtensions,
            rule: false
        };
        const addDays: any = await fetchAddDays(authContext?.authUser?.data.authorization, selectedRetailerData.id, addDaysData);
        setExtensionDays(addDays.data);

        const preAuth: any = await fetchpreAuth(authContext?.authUser?.data.authorization, selectedRetailerData.id, hireTasks[0].id, hiretask?.customerGroup?.code);
        if (preAuth.data[0]?.status !== 0) {
            setCcpData(preAuth.data[0]);
            setShowCCPFieds(true);
        } else {
            setShowCCPFieds(false);
        }
    };

    const removeExtraDateField = (extrafields: any) => {
        try {
            const estRepairDateIndex = extrafields.extraFields.findIndex((fields: any) => fields.key === 'estRepairDate');
            if (estRepairDateIndex > -1) {
                extrafields.extraFields.splice(estRepairDateIndex, 1);
            }
            return extrafields;
        } catch (e) {
            console.log(e);
            return {};
        }
    };

    const handleSubmit = async (data: FormData) => {
        // Here you can send the data to an API or perform any other action
        const extensionData = historyData[historyData.length - 2];
        const dtArray = {
            id: -1,
            taskId: extensionData.taskId,
            extensionRuleId: selectedVorRecord.id,
            previousExtensionRuleId: extensionData.extensionRuleId,
            extensionDuration: extensionData.extensionDuration,
            requestOpResourceId: authContext?.authUser?.data.operatorId || -1,
            dateApprovedUntil: extensionDays,
            referenceData: data,
            reviewComments: comment,
            requestOpForename: extensionData.requestOpForename,
            requestOpSurname: extensionData.requestOpSurname,
            isLocked: false,
            dateRequested: null,
            dateClosed: null,
            approved: null,
            approveOpResourceId: null,
            approveOpForename: null,
            approveOpSurname: null,
            retailerId: -1,
            estRepairDate: null,
            resDoingTaskId: null,
            repairerResId: null,
            hireVrn: null,
            payer: null
        };

        try {
            await postExtensionData(authContext?.authUser?.data.authorization, selectedRetailerData.id, dtArray, setApiStatus);
            setPendingExtensions?.([...pendingExtensions, extensionData.taskId]);
            setIsOpen(true);
            setSelectedData(dtArray);
            if (showCCPFieds) {
                await postpreAuth(authContext?.authUser?.data.authorization, selectedRetailerData.id, ccpData);
            }
        } catch (e) {
            setIsOpen(true);
            console.log(e);
        }
    };

    const isCCP = (customerGroupCode: any) => {
        return ['JAGA', 'LANE'].indexOf(customerGroupCode) > -1;
    };

    const handleExternalSubmit = () => {
        formRef.current?.submitForm();
    };

    const cancelClick = () => {
        setDisableFormFields(true);
        defaultVorRecord.hireTaskId = 344123234;
        setComment('');
        setCcpData({ authCode: '', daysRequested: '', comments: '' });
        setSelectedVorRecord(defaultVorRecord);
        isActiveExtn(defaultVorRecord?.completeDate, defaultVorRecord?.hireTaskId);
    };

    const handleValidityChange = (isValid: boolean) => {
        setIsFormValid(isValid);
    };

    const handleClearFilter = () => {
        setDataFromDashboard(undefined);
    };

    const ValidateFields = (value: string, fields: string) => {
        if (value) {
            switch (fields) {
                case 'authCode':
                    setValidAuthCodeTrigger(false);
                    break;
                case 'daysRequested':
                    setValidDaysRequestedTrigger(false);
                    break;
                case 'comments':
                    setValidCommentsTrigger(false);
                    break;
            }
        } else {
            switch (fields) {
                case 'authCode':
                    setValidAuthCodeTrigger(true);
                    break;
                case 'daysRequested':
                    setValidDaysRequestedTrigger(true);
                    break;
                case 'comments':
                    setValidCommentsTrigger(true);
                    break;
            }
        }
    };

    return (
        <>
            <Helmet>
                <title>VOR</title>
            </Helmet>
            <div className="m-4 h-full shadow">
                <div className="grid sm:grid-cols-1 lg:grid-cols-12 ">
                    <div className={`${fleetEventTriggered ? 'lg:col-span-9' : eventTriggered ? 'lg:col-span-9' : 'col-span-9  md:col-span-12 sm:col-span-12'}`}>
                        <div className="flex sm:gap-2 sm:flex-col lg:flex-row  rounded-lg bg-white sm:col-span-1 lg:h-[calc(100vh-105px)] sm:h-auto md:h-auto">
                            <div
                                className={`border-r rounded-bl-lg bg-gray-50/40 ${
                                    fleetEventTriggered ? 'sm:w-full lg:w-1/4' : eventTriggered ? 'sm:w-full lg:w-1/4' : 'sm:w-full w-1/3 lg:w-1/4'
                                }  sm:col-span-12 border-r rounded-bl-lg bg-gray-50/40 forSelect`}
                            >
                                {/* Left Sidebar */}
                                {/* <div
              className={`w-1/4 border-r rounded-bl-lg bg-gray-50/40 h-full`}
            > */}
                                <Select
                                    defaultValue="all"
                                    onValueChange={(value) => setsearchDrop(value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All customer groups" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All customer groups</SelectItem>
                                        {customerGroup.map((group: any, index: number) => (
                                            <SelectItem
                                                key={index}
                                                value={group?.code}
                                            >
                                                {group.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <div className=" border-b  sticky">
                                    <div
                                        id="searchInput"
                                        className=" px-3 py-1.5 relative flex "
                                    >
                                        <Input
                                            className="border-none  p-0 shadow-none focus:border-none rounded-none focus:outline-none focus-visible:border-none searchBox placeholder:text-[10px]"
                                            placeholder="Search by VRN, Customer Name"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            // onChange={(e) => searchVehicle(e.target.value)}
                                        />
                                        <Search
                                            className="absolute right-2 top-2 h-7 w-7 text-muted-foreground rounded-none "
                                            color="#07828d"
                                        />
                                    </div>
                                </div>
                                {dataFromDashboard && (
                                    <div className="sticky ">
                                        <div
                                            id="searchInput"
                                            className="relative flex flex-row justify-between p-[5px] border-b"
                                        >
                                            <h5>Extensions Due Today</h5>
                                            <Button
                                                className="bg-[#777777] px-[5px] py-[1px] border h-full rounded-none text-white"
                                                onClick={handleClearFilter}
                                            >
                                                Clear
                                            </Button>
                                        </div>
                                    </div>
                                )}
                                <div className="overflow-y-auto border-r lg:h-[calc(100vh-195px)] sm:h-[70vh] rounded-bl-lg">
                                    {/* {filteredFleetData &&
                  filteredFleetData?.map((vehicle: Vehicles, index: number) => ( */}
                                    {filteredHiretaskList.map((item: any, index: number) => (
                                        <div
                                            onClick={() => {
                                                handleHistory(item);
                                                setActiveIndex(index);
                                                if (window.innerWidth < 1024) {
                                                    document.getElementById('VORMainSection')?.scrollIntoView({ behavior: 'smooth' });
                                                }
                                            }}
                                            key={item.id}
                                            className={`cursor-pointer flex flex-row items-start border-b border-transparent pl-4 border-b-gray-200 border-r-gray-200 bg-gray-50 focus:bg-#fffae6 h-[110px] overflow-y-hidden ${
                                                activeData === index ? 'bg-org' : ''
                                            }`}
                                        >
                                            <div className="flex-1 flex">
                                                <div className="pr-4 flex items-center">
                                                    <div className="flex items-center justify-center w-12 h-12 rounded-full bg-white border border-[#07828d]">
                                                        <div className="text-center text-[#07828d]">
                                                            <span className="block text-[16px]">{item.age}</span>
                                                            <span className="block text-[12px] font-bold">day(s)</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className=" flex flex-col justify-center h-full items-center mt-3 ml-4">
                                                    <div className="items-center">
                                                        <div className="font-bold">
                                                            {item.vehicleRegistration} {isCCP(item.customerGroup.code) ? '- CCP' : ''}
                                                        </div>
                                                        <div className="text-sm text-muted-foreground">{item.contactName || 'N/A'}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {item.supplierTypeCode} - {item.hireVehicleRegNo}
                                                        </div>
                                                        <div>{getExtensionColor(item.completeDate, item.id)}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                    {/* ))} */}
                                </div>
                            </div>

                            {/* Main Content */}
                            <div
                                id="VORMainSection"
                                className="flex-1 sm:grid-cols-12 md:grid-cols-12 p-6 overflow-y-auto text-clr lg:h-[calc(100vh-105px)]"
                            >
                                <h1 className="text-2xl font-normal mb-6 section-title">
                                    {firstHireTask?.vehicleRegistration ? 'VOR - ' + firstHireTask?.vehicleRegistration + ', ' + firstHireTask?.makeName : 'Comments'}
                                </h1>

                                <div className="space-y-4 mb-2">
                                    {showCCPFieds && (
                                        <>
                                            <div className="space-y-4">
                                                <Label className="text-black">Ccp Auth Code *</Label>
                                                <Input
                                                    type="text"
                                                    className={`bg-white rounded-none noOutline ${validAuthCodeTrigger ? 'border-red-500' : ' text-gray-400'}`}
                                                    value={ccpData?.authCode}
                                                    disabled={disableFormFields}
                                                    onChange={(e) => {
                                                        setCcpData({ ...ccpData, authCode: e.target.value });
                                                        ValidateFields(e.target.value, 'authCode');
                                                    }}
                                                />
                                            </div>

                                            <div className="space-y-4">
                                                <Label className="text-black">Days Requested *</Label>
                                                <Input
                                                    type="text"
                                                    className={`bg-white rounded-none noOutline ${validDaysRequestedTrigger ? 'border-red-500' : ' text-gray-400'}`}
                                                    value={ccpData?.daysRequested}
                                                    disabled={disableFormFields}
                                                    onChange={(e) => {
                                                        setCcpData({ ...ccpData, daysRequested: e.target.value });
                                                        ValidateFields(e.target.value, 'daysRequested');
                                                    }}
                                                />
                                            </div>

                                            <div className="space-y-4">
                                                <Label className="text-black">Comments *</Label>
                                                <Textarea
                                                    placeholder="Enter additional comments"
                                                    className={`min-h-[70px] bg-white rounded-none noOutline ${validCommentsTrigger ? 'border-red-500' : ' text-gray-400'}`}
                                                    required={true}
                                                    disabled={disableFormFields}
                                                    value={ccpData?.comments}
                                                    onChange={(e) => {
                                                        setCcpData({ ...ccpData, comments: e.target.value });
                                                        ValidateFields(e.target.value, 'comments');
                                                    }}
                                                />
                                            </div>
                                        </>
                                    )}
                                    <div className="flex justify-between items-center">
                                        <Label>Reason code</Label>
                                    </div>
                                    <div className="border h-8 w-full">
                                        <div className="flex justify-between items-center h-full">
                                            <span className="pl-3">{selectedVorRecord?.description || 'Please select task from VRN'}</span>
                                            <a
                                                className={`underline pr-3 cursor-pointer ${!selectedVorRecord?.description || disableFormFields ? 'pointer-events-none text-gray-400' : 'select-cls'}`}
                                                onClick={() => setOpen(true)}
                                            >
                                                Select
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div className="border border-gray-300  bg-[#fafafa] p-4  bg-fafafa">
                                    <div className="space-y-4">
                                        <Label className="text-black">Comments *</Label>
                                        <Textarea
                                            placeholder="Enter additional comments "
                                            className={`min-h-[150px] bg-white rounded-none ${
                                                !selectedVorRecord?.description || disableFormFields ? 'bg-gray-200 borde' : comment === '' && commentTrigger ? 'border-red-500 border' : ''
                                            } `}
                                            disabled={!selectedVorRecord?.description || disableFormFields}
                                            value={comment}
                                            onChange={(e) => {
                                                setComment(e.target.value);
                                                setCommentTrigger(true);
                                            }}
                                        />
                                    </div>

                                    <div className="flex justify-between items-center text-sm mt-3">
                                        <span style={{ color: '#9e1b33' }}>Daily charge: £{selectedVorRecord?.dailyCost}</span>
                                        <span className="text-black">Total charges: £{selectedVorRecord?.totalCost}</span>
                                    </div>
                                </div>

                                <div className="border border-gray-300 mt-4 bg-[#fafafa] p-4  bg-fafafa">
                                    <DynamicForm
                                        data={selectedData}
                                        ref={formRef}
                                        fields={selectedVorRecord?.extraFields}
                                        onSubmit={handleSubmit}
                                        onValidityChange={handleValidityChange}
                                        key={selectedVorRecord?.hireTaskId} // Add a key to refresh the form
                                        disableAllField={disableFormFields}
                                    />
                                    <div className="mt-6">
                                        <Label className="text-gray-400 mb-1">Current Booking</Label>
                                        <div className="text-black">{firstHireTask?.completeDate ? format(new Date(firstHireTask.completeDate), 'd MMM yyyy, HH:mm') : ''}</div>
                                    </div>
                                    <div className="mt-4">
                                        <Label className="text-gray-400 mb-1">Extension</Label>
                                        <div className="text-black">{extensionDays ? format(new Date(extensionDays), 'd MMM yyyy, HH:mm') : ''}</div>
                                    </div>
                                </div>

                                <div className="flex justify-end mt-4">
                                    <Button
                                        onClick={cancelClick}
                                        className="bg-white text-gray-500 px-3 py-1.5 rounded-none border border-gray-500 hover:bg-gray-300 mr-4"
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        onClick={handleExternalSubmit}
                                        className={`bg-teal-600 text-white px-3 py-1.5 rounded-none border hover:bg-teal-1000 ${
                                            (!isFormValid && selectedVorRecord?.extraFields.length) ||
                                            comment === '' ||
                                            Object.keys(selectedData).length > 0 ||
                                            (showCCPFieds && (!ccpData.authCode || !ccpData.daysRequested || !ccpData.comments)) ||
                                            isDisabledActiveExtn
                                                ? 'cursor-not-allowed pointer-disabld pointer-events-visible'
                                                : ''
                                        }`}
                                        disabled={
                                            (!isFormValid && selectedVorRecord?.extraFields.length) ||
                                            comment === '' ||
                                            Object.keys(selectedData).length > 0 ||
                                            (showCCPFieds && (!ccpData.authCode || !ccpData.daysRequested || !ccpData.comments)) ||
                                            isDisabledActiveExtn
                                        }
                                    >
                                        Request Extension
                                    </Button>
                                </div>
                            </div>

                            {/* Right Sidebar */}
                        </div>
                    </div>
                    {fleetEventTriggered && <CaseHistory events={historyData} />}
                </div>
            </div>

            <ReasonCodeDialog
                open={open}
                onOpenChange={setOpen}
                dialogData={dialogRecords}
                confirmDialogData={setSelectedVorRecord}
            />
            {isOpen && (
                <PopUp
                    isOpen={isOpen}
                    header={`${apiStatus === 200 ? 'Extension requested ' : 'Error in Extension Request'}`}
                    setIsOpen={setIsOpen}
                    message={`${apiStatus === 200 ? 'You have requested an extension, this will be reviewed by the VOR team and actioned accordingly.' : ' Extension request is Unsuccessful'}`}
                    response={apiStatus ?? 0}
                />
            )}
        </>
    );
}
