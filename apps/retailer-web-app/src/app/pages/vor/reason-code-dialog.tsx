'use client';
import * as React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '../../components/ui/dialog';
import { Button } from '../../components/ui/button';

interface ReasonCodeDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    dialogData: any;
    confirmDialogData: (data: any) => void;
}

export function ReasonCodeDialog({ open, onOpenChange, dialogData, confirmDialogData }: ReasonCodeDialogProps) {
    const [selectedCode, setSelectedCode] = React.useState<string | null>(null);

    const handleConfirm = () => {
        if (selectedCode) {
            onOpenChange(false);
            const confirmData = dialogData.filter((code: any) => {
                return code.id === selectedCode;
            });
            const estRepairDateIndex = confirmData[0].extraFields.findIndex((fields: { key: string }) => fields.key === 'estRepairDate');
            if (estRepairDateIndex > -1) {
                confirmData[0].extraFields.splice(estRepairDateIndex, 1);
            }
            confirmDialogData(confirmData[0]);
            setSelectedCode(null);
        } else {
            onOpenChange(false);
        }
    };

    return (
        <Dialog
            open={open}
            onOpenChange={onOpenChange}
        >
            <DialogContent
                id="VORDialogContent"
                className="gap-0 h-[83vh] lg:max-w-[65vw] sm:max-w-[90vw]  top-[46%] shadow-lg"
            >
                <DialogHeader>
                    <DialogTitle className="bg-[#08828d] text-white font-medium p-4 ">Select reason code</DialogTitle>
                </DialogHeader>
                <div
                    id="vorMainDialog"
                    className="space-y-4  overflow-y-auto  m-[15px]"
                >
                    <div className="grid grid-cols-[1fr,100px,100px,50px] gap-4 px-2 font-medium border-b border-gray-200 ">
                        <div>Reason code</div>
                        <div className="text-center">Free days left</div>
                        <div className="text-center">Charge</div>
                        <div></div>
                    </div>
                    {dialogData.map((code: { id: string; description: string; whenToUse: string; freeDaysLeft: number; retailerCharge: number; extraFields: { key: string }[] }) => (
                        <div
                            key={code.id}
                            className="grid grid-cols-[1fr,100px,100px,50px] p-[15px] items-start border-gray-200 mt-0"
                        >
                            <div>
                                <h3 className="font-medium text-[#08828d]">{code.description}</h3>
                                <p className="text-sm text-gray-600 mt-1">{code.whenToUse}</p>
                            </div>
                            <div className="text-center">{code.freeDaysLeft}</div>
                            <div className="text-center">£{code.retailerCharge}</div>
                            <div className="flex justify-center pt-1">
                                <input
                                    data-testid="vorCheckBox"
                                    type="radio"
                                    id="vorCheckBox"
                                    className="radio-custom w-[30px] h-[30px] border border-gray-300 rounded-none "
                                    checked={selectedCode === code.id}
                                    onChange={(e) => {
                                        setSelectedCode(e.target.checked ? code.id : null);
                                    }}
                                />
                            </div>
                        </div>
                    ))}
                </div>
                <DialogFooter className="gap-2  p-[15px] border-t">
                    <Button
                        data-testid="cancelButton"
                        variant="outline"
                        className="rounded-none border-teal-600"
                        onClick={() => onOpenChange(false)}
                    >
                        Cancel
                    </Button>
                    <Button
                        data-testid="confirmButton"
                        onClick={handleConfirm}
                        disabled={!selectedCode}
                        className="bg-teal-600 hover:bg-teal-700 rounded-none text-white"
                    >
                        Confirm
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
