import React, { useCallback, useEffect, useState } from 'react';
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '../../components/ui/dialog';
import { Button } from '../../components/ui/button';
import { checkOutStartHire, fetchDataByIdCheckins } from '../../services/generalService';
import { useAuth } from '../../contexts/AuthContext';
import Icon from '../../../assets/icomoon/icon';
import { useCommonStore } from '../../contexts/CommonStore';
import LoanAgreement from '../loan-agreement/loan-agreement';
import ConditionReportCheckout from '../condition-report-checkout/condition-report-checkout';
import ChecklistCheckout from '../checklist-checkout/checklist-checkout';
import TermsandconditionCheckout from '../termsandcondition-checkout/termsandcondition-checkout';
import PopUp from '../../components/PopUp/PopUp';

export default function Checkoutpage(props: any) {
    const authContext = useAuth();
    const { selectedRetailerData, checkInPageDetails, setCheckInPageDetails } = useCommonStore() || {};
    const [isOpen, setIsOpen] = useState(false);
    const [apiStatus, setApiStatus] = useState<number>(0);
    interface Step {
        name: string;
        complete: boolean;
    }
    console.log(props, 'props');
    const checkoutSteps = [
        { name: 'Loan Details', complete: false },
        { name: 'Condition Report', complete: false },
        { name: 'Checklist', complete: false },
        { name: 'Terms and Conditions', complete: false }
    ];

    const [stepsCheckout, setCheckoutSteps] = useState(checkoutSteps);
    const [currentCheckoutStep, setCurrentCheckoutStep] = useState(0);
    const isCheckoutComplete = (step: Step): boolean => step.complete;
    const [checkOutData, setCheckOutData] = useState<any>();
    const [validated, setValidated] = useState(false);
    const [termsValidated, setTermsValidated] = useState(false);
    const checkoutCurrentcurrent = (index: number): boolean => index === stepsCheckout.findIndex((step: Step) => !step.complete);
    useEffect(() => {
        checkOutUserValue();
    }, []);
    const resetStepper = () => {
        setCheckoutSteps(checkoutSteps.map((step) => ({ ...step, complete: false })));
        setCurrentCheckoutStep(0);
    };

    const nextStep = (from?: string) => {
        if (from === 'checkout') {
            setCheckoutSteps((prevSteps) => prevSteps.map((step, index) => (index === currentCheckoutStep ? { ...step, complete: true } : step)));
            setCurrentCheckoutStep((prevStep) => Math.min(prevStep + 1, stepsCheckout.length - 1));
        }
        if (from === 'final' && termsValidated) {
            console.log('checkInPageDetails final : ', checkInPageDetails);
            checkOutStartHire(checkInPageDetails, authContext?.authUser?.data.authorization, selectedRetailerData.id, setApiStatus)
                .then(() => {
                    setApiStatus(200);
                    setIsOpen(true);
                    setCheckInPageDetails && setCheckInPageDetails({});
                    document.getElementById('cancelBtn')?.click();
                    if (checkInPageDetails?.taskId) {
                        const element = document.getElementById(`incoming-activity-${checkInPageDetails?.taskId}`);
                        if (element) {
                            setTimeout(() => {
                                element.remove();
                            }, 2000);
                        }
                    }
                })
                .catch(() => {
                    setIsOpen(true);
                    setApiStatus(500);
                    setCheckInPageDetails && setCheckInPageDetails({});
                    document.getElementById('cancelBtn')?.click();
                });
        }
    };

    const checkOutUserValue = async () => {
        resetStepper();
        setCurrentCheckoutStep(0);
    };

    const prevStep = (from: string) => {
        if (from === 'checkout') {
            if (currentCheckoutStep > 0) {
                setCheckoutSteps((prevSteps) => prevSteps.map((step, index) => (index === currentCheckoutStep - 1 ? { ...step, complete: false } : step)));
                setCurrentCheckoutStep((prevStep) => prevStep - 1);
            }
        }
    };

    const validateFields = useCallback(
        (dt: any) => {
            setValidated(dt);
        },
        [validated]
    );

    const termsCondValidateFields = useCallback(
        (dt: any) => {
            setTermsValidated(dt);
        },
        [validated]
    );

    const renderCheckOutStep = (data?: any) => {
        switch (currentCheckoutStep) {
            case 0:
                return <LoanAgreement data={data} />;
            case 1:
                return (
                    <ConditionReportCheckout
                        details={data}
                        validateFields={validateFields}
                    />
                );
            case 2:
                return <ChecklistCheckout />;
            case 3:
                return (
                    <TermsandconditionCheckout
                        data={data}
                        termsCondValidateFields={termsCondValidateFields}
                    />
                );
            default:
                return null;
        }
    };

    const handleCancel = () => {
        setCheckInPageDetails &&
            setCheckInPageDetails({
                taskId: '',
                conditionReport: {
                    mileage: '',
                    fuel: '',
                    photos: [],
                    homedelivery: { isHomeDelivery: false },
                    homecollection: { isHomeCollection: true, collectedBy: '' },
                    damages: [],
                    comments: null,
                    extras: {
                        spareKeyFob: false,
                        euroPack: false,
                        lockingWheelNut: false,
                        detachableTowBar: false,
                        tracker: false,
                        porscheHandbook: false,
                        key: false,
                        hvchargingCable: false,
                        bootPack: false,
                        iccbChargingCable: false,
                        type2ConnectorChargingCable: false,
                        manuals: false,
                        parcelShelf: false,
                        punctureRepairKit: false,
                        carpetMats: false,
                        comments: ''
                    },
                    docName: null,
                    eDocName: null,
                    eDocLink: null
                },
                signatures: '',
                photos: []
            });
        setValidated(false);
    };
    const getHireData = async () => {
        try {
            const dataOnCheckInClick = await fetchDataByIdCheckins(props.checkoutId, selectedRetailerData.id, authContext?.authUser?.data.authorization);
            setCheckOutData(dataOnCheckInClick?.mobilityTask);
        } catch (error) {
            console.error('Error fetching data:', error);
        }
    };
    return (
        <>
            <Dialog>
                <DialogTrigger asChild>
                    {props.showCheckout(props.checkOutData) && (
                        <Button
                            variant="outline"
                            data-testid="checkoutBtn"
                            onClick={() => {
                                checkOutUserValue();
                                getHireData();
                            }}
                            className="w-full aaButton"
                            //style={{ cursor: !props.checkoutIsReady ? 'not-allowed' : 'pointer', opacity: !props.checkoutIsReady ? 0.5 : 1 }}
                            //disabled={!props.checkoutIsReady || props.isSelfCheckout}
                            style={{
                                cursor: !props.checkoutIsReady || props.isSelfCheckout || props.isLCH || props.pncCheck || props.isAnotherHire ? 'not-allowed' : 'pointer',
                                opacity: !props.checkoutIsReady || props.isSelfCheckout || props.isLCH || props.pncCheck || props.isAnotherHire ? 0.5 : 1
                            }}
                            disabled={!props.checkoutIsReady || props.isSelfCheckout || props.isLCH || props.pncCheck || (props.isAnotherHire && props.isSwapHire)}
                        >
                            Check out
                        </Button>
                    )}
                </DialogTrigger>
                {props.checkOutData && (
                    <DialogContent className="p-0 rounded-none max-w-full h-screen dialogContent">
                        <DialogHeader>
                            <DialogTitle>
                                <div className="modal-header">
                                    <h3 className="modal-title">Checking out {checkOutData && checkOutData?.rental?.hireVehicle?.regNo}</h3>
                                </div>
                            </DialogTitle>
                        </DialogHeader>
                        <div className="grid">
                            <div className="modal-steps">
                                <div className="steps">
                                    {stepsCheckout.map((step, index) => (
                                        <div
                                            key={index}
                                            className={`step-container ${step.complete ? 'complete' : ''}`}
                                        >
                                            <span className="step">
                                                {isCheckoutComplete(step) && !checkoutCurrentcurrent(index) ? <div className="border"></div> : <span>{index + 1}</span>}
                                                {isCheckoutComplete(step) && !checkoutCurrentcurrent(index) && (
                                                    <Icon
                                                        icon="tick"
                                                        style={{ width: '15px', height: '18px' }}
                                                        color="#07828d"
                                                        className="flex100"
                                                    />
                                                )}
                                            </span>
                                            {isCheckoutComplete(step) && !checkoutCurrentcurrent(index) && <div className="stepper"></div>}
                                            <div>{step.name}</div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            {renderCheckOutStep(checkOutData)}
                        </div>

                        <DialogFooter className="px-4 dialogFooter">
                            <DialogClose asChild>
                                <Button
                                    id="cancelBtn"
                                    data-testid="cancelBtn"
                                    onClick={handleCancel}
                                    className="mr-7 btn-default-aa"
                                    style={{ border: '1px solid #07828d' }}
                                >
                                    Cancel
                                </Button>
                            </DialogClose>
                            <Button
                                onClick={() => prevStep('checkout')}
                                className="mr-7 btn-default-aa"
                                disabled={currentCheckoutStep === 0}
                            >
                                Prev
                            </Button>
                            {currentCheckoutStep === stepsCheckout.length - 1 ? (
                                <Button
                                    disabled={!termsValidated}
                                    style={{ opacity: termsValidated ? 1 : 0.5 }}
                                    onClick={() => nextStep('final')}
                                    className="px-4 py-2 btn-primary-aa"
                                >
                                    Finish
                                </Button>
                            ) : currentCheckoutStep === 1 ? (
                                <Button
                                    disabled={!validated}
                                    style={{ opacity: validated ? 1 : 0.5 }}
                                    onClick={() => nextStep('checkout')}
                                    className="px-4 py-2 btn-primary-aa"
                                >
                                    Next
                                </Button>
                            ) : (
                                <Button
                                    onClick={() => nextStep('checkout')}
                                    className="px-4 py-2 btn-primary-aa"
                                >
                                    Next
                                </Button>
                            )}
                        </DialogFooter>
                    </DialogContent>
                )}
            </Dialog>
            {isOpen && (
                <PopUp
                    isOpen={isOpen}
                    header={`${apiStatus === 200 ? 'Check-out Successful' : 'Checkout failed'}`}
                    setIsOpen={setIsOpen}
                    message={`${apiStatus === 200 ? 'Vehicle insurance initiated' : 'Vehicle not insured. Please contact AA prestige team.'}`}
                    response={apiStatus}
                />
            )}
        </>
    );
}
