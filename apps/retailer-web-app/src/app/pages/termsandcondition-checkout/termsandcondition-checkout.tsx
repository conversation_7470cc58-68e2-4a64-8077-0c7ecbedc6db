import React, { useEffect, useRef, useState } from 'react';
import './termsandcondition-checkout.css';
import SignatureCanvas from 'react-signature-canvas';
import { memo } from 'react';
import { useCommonStore } from '../../contexts/CommonStore';
import Moment from 'moment';

interface TermsandconditionCheckoutProps {
    data: any; // Replace 'any' with the appropriate type
    termsCondValidateFields: (isValid: boolean) => void;
}

function TermsandconditionCheckout({ data, termsCondValidateFields }: TermsandconditionCheckoutProps) {
    const { networkCode, checkInPageDetails, setCheckInPageDetails } = useCommonStore() || {};
    const retailSignPad = useRef<SignatureCanvas>(null);
    const custSignPad = useRef<SignatureCanvas>(null);
    const [disableCustSign, setdisableCustSign] = useState<boolean>(checkInPageDetails?.conditionReport?.homecollection?.isHomeCollection);

    const [retalierSignature, setRetailerSignature] = useState<string>('');
    const [customerSignature, setCustomerSignature] = useState<string>('');

    const [collectedBy, setCollectedBy] = useState<string>(checkInPageDetails?.conditionReport?.homecollection?.collectedBy);
    const [deliveredBy, setDeliveredBy] = useState<string>('');
    const clear = (from: string) => {
        if (from === 'retailer') {
            retailSignPad.current?.clear();
            setRetailerSignature('');
        } else if (from === 'customer') {
            custSignPad.current?.clear();
            setCustomerSignature('');
        }
        termsCondValidateFields(false);
    };

    const processCollectedBy = (value: any) => {
        if (value) {
            custSignPad.current?.off();
            custSignPad.current?.clear();
            setdisableCustSign(true);
            setCustomerSignature('data:image/png;base64,iVBAvXwjBl5E27pwAAAABJRU5ErkJggg==');
        } else {
            custSignPad.current?.on();
            setdisableCustSign(false);
            setCheckInPageDetails &&
                setCheckInPageDetails({
                    ...checkInPageDetails,
                    conditionReport: {
                        ...checkInPageDetails.conditionReport,
                        homedelivery: {
                            ...checkInPageDetails.conditionReport.homedelivery,
                            isHomeDelivery: value,
                            deliveredBy: ''
                        }
                    }
                });
        }
    };

    const isCCP = (code: string) => {
        return ['JAGA', 'LANE'].indexOf(code) > -1;
    };

    useEffect(() => {
        if ((retalierSignature && customerSignature) || (retalierSignature && disableCustSign)) {
            termsCondValidateFields(true);
            setCheckInPageDetails && setCheckInPageDetails({ ...checkInPageDetails, signatures: { ...checkInPageDetails.signatures, loaner: retalierSignature, loanee: customerSignature } });
        } else {
            termsCondValidateFields(false);
        }
    }, [retalierSignature, customerSignature, disableCustSign]);

    return (
        <div className="px-4">
            <div
                className="py-4 mainDivChecklist scroll-wrapper"
                id="termsandcondition-checkout"
            >
                <hr />
                <div className="px-4 h-full">
                    <div className="row">
                        <div className="col-sm-12">
                            <h2>Customer</h2>
                            <p className="mb-3">Name: {data?.rental?.mainDriver?.contact?.name} </p>
                            <hr />
                            <div className="signatures">
                                <div className="contract-info">
                                    <p className="mb-3">
                                        A printable copy of the loan agreement, condition report and terms will be sent to you at
                                        <b> {data?.rental?.mainDriver?.contact?.email}</b>
                                    </p>
                                </div>

                                <p className="mb-3">Address: {data?.rental?.mainDriver?.address?.addressLines.join(', ')}</p>
                                <p className="mb-3">Phone number: {data?.rental?.mainDriver?.contact?.telephone}</p>
                                <p className="mb-3">Driving licence number: {data?.rental?.mainDriver?.licenceNumber}</p>
                                <p className="mb-3">Driving licence expiry date: {Moment(data?.rental?.mainDriver?.licenceExpire).format('DD MMM YYYY')}</p>
                                <p className="mb-3">DOB: {Moment(data?.rental?.mainDriver?.dateOfBirth).format('DD MMM YYYY')}</p>
                                <p className="mb-3">Email address:{data?.rental?.mainDriver?.contact?.email}</p>
                                <p className="mb-3">Additional Drivers: </p>
                                <h2>Vehicle</h2>
                                <p className="mb-3">Registration: {data?.rental?.hireVehicle?.regNo}</p>
                                <p className="mb-3">Model: {data?.rental?.hireVehicle?.model?.name}</p>
                                {networkCode === 'POR' && <p className="mb-3">Vin: {data?.rental?.hireVehicle?.vin}</p>}
                                <p className="mb-3">Collection Time: {Moment(data?.appointment?.earliest).format('DD-MM-YYYY HH:MM')}</p>
                                <p className="mb-3">Collection Address: {data?.rental?.collectLocation?.address}</p>
                                <p className="mb-3">Return Address: {data?.rental?.dropOffLocation?.address}</p>
                                <p className="mb-3">insurance excess: {data?.rental?.insurance?.insuranceOption?.excess}</p>
                            </div>
                        </div>
                        {isCCP(data?.entitlement?.customerGroup.code) && (
                            <div
                                className="col-sm-12"
                                ng-if="ctrl.isCCP()"
                            >
                                <h2>Security deposit</h2>
                                <p className="mb-3">Security deposit amount £1000.00</p>
                            </div>
                        )}

                        {/* have to check with data */}
                        {data?.rental?.insurance?.insuredTerms?.termsOfInsurance && (
                            <div
                                className="col-sm-12"
                                ng-if="!ctrl.thirdParty()"
                            >
                                <h2>Terms of Insurance</h2>
                                <p
                                    className="mb-3"
                                    dangerouslySetInnerHTML={{ __html: data?.rental?.insurance?.insuredTerms?.termsOfInsurance }}
                                ></p>
                            </div>
                        )}
                        {data?.rental?.insurance?.insuredTerms?.conditionsOfLoan && (
                            <div
                                className="col-sm-12"
                                ng-if="!ctrl.thirdParty()"
                            >
                                <h2>Insurance Terms and Condition</h2>
                                <p
                                    id="insuranceTermsCond"
                                    className="mb-3 termsandconditions"
                                    dangerouslySetInnerHTML={{ __html: data?.rental?.insurance?.insuredTerms?.conditionsOfLoan }}
                                ></p>
                            </div>
                        )}
                        {data?.rental?.insurance?.type === 2 && (
                            <div
                                className="col-sm-12"
                                ng-if="ctrl.thirdParty()"
                            >
                                <h2>Insurance Terms and Conditions</h2>

                                <p>
                                    I confirm that I have arranged for my own insurance policy to cover the above vehicle on a comprehensive basis, and I will ensure that cover remains in force for
                                    the duration of the hire.
                                </p>

                                <p>
                                    I confirm that, if I have any health condition or disability that could affect my ability to drive safely, I have notified the relevant driver licencing authority,
                                    and such authority has authorised me to drive. I will pay any charges for loss/damage as a result of not using the correct fuel (this is a [Fuel Type –
                                    petrol/diesel/electric] vehicle). I accept that these terms also apply to any additional authorised driver.
                                </p>
                            </div>
                        )}
                        <div className="col-sm-12">
                            <h2>Terms and Conditions</h2>

                            <ol id="conditions">
                                <li>
                                    GENERAL
                                    <ol>
                                        <li>
                                            In these terms and conditions the following words have the following meanings:
                                            <dl className="clearfix dl-col">
                                                <dt>Additional Driver</dt>
                                                <dd>any additional driver named on the Loan Agreement;</dd>

                                                <dt>Collection Address</dt>
                                                <dd>the address from which the Vehicle is collected, as shown on the front of the Loan Agreement;</dd>

                                                <dt>Collection Time</dt>
                                                <dd>the date and time the Vehicle is available for collection, as shown on the front of the Loan Agreement;</dd>

                                                <dt>Condition Report</dt>
                                                <dd>the report attached to the Loan Agreement showing the condition of the Vehicle;</dd>

                                                {networkCode === 'HYUA' && <dt>Hyundai Retailer</dt>}
                                                {networkCode === 'HYUA' && <dd>the Hyundai dealership from where you collect the Vehicle;</dd>}

                                                <dt>Insurance Excess</dt>
                                                <dd>the insurance excess applicable to the Vehicle as shown on the Loan Agreement;</dd>

                                                <dt>Loan Agreement</dt>
                                                <dd>the agreement between you and us for the loan of the Vehicle, which these terms and conditions form part of;</dd>

                                                <dt>Return Address</dt>
                                                <dd>the address to which the Vehicle must be returned on or before the Return Time, as shown on the front of the Loan Agreement;</dd>

                                                <dt>Return Time</dt>
                                                <dd>the date and time on which the Vehicle must be returned, as notified under clause 2.3;</dd>

                                                <dt>Vehicle</dt>
                                                <dd>
                                                    the loan vehicle identified in the Loan Agreement, and any replacement vehicle and all tyres, tools, accessories, parts and equipment relating to
                                                    such vehicle;
                                                </dd>

                                                <dt>We, us, our</dt>
                                                {networkCode !== 'POR' && networkCode !== 'HYUA' && (
                                                    <dd>Automobile Association Developments Limited, being the Roadside Assistance Provider for JLR Mobility;</dd>
                                                )}
                                                {(networkCode === 'POR' || networkCode === 'HYUA') && <dd>Automobile Association Developments Limited;</dd>}
                                                <dt>You, your</dt>
                                                <dd>the person named as the customer on the Loan Agreement;</dd>
                                            </dl>
                                        </li>
                                    </ol>
                                </li>

                                <li>
                                    LOAN OF VEHICLE
                                    <ol>
                                        <li>We agree to loan and you agree to take on loan the Vehicle upon and subject to the terms and conditions of the Loan Agreement.</li>

                                        <li>
                                            The period of loan shall commence on the date set out on the Loan Agreement and shall continue until the Return Time, unless you return the Vehicle to us
                                            earlier.
                                        </li>

                                        {!isCCP(data?.entitlement?.customerGroup.code) && networkCode === 'POR' && (
                                            <li ng-if="ctrl.showPorscheAndCCPReturnTime()">
                                                The Return Time is the end of the period of 48 hours from the Collection Time, or such later time as we may notify you, either by telephone or email,
                                                but in any event the Return Time is no later than the time when repairs to your own vehicle have been completed.
                                            </li>
                                        )}
                                        {networkCode !== 'HYUA' && isCCP(data?.entitlement?.customerGroup.code) && (
                                            <li>
                                                The Return Time Is the end of the period of 24 hours from the Collection Time, or such other time as we may notify you, either by telephone or email,
                                                but in any event the Return Time is no later than the time when repairs to your own vehicle have been completed.
                                            </li>
                                        )}
                                        {networkCode === 'HYUA' && (
                                            <li>
                                                The Return Time is the end of the period indicated to you by the Hyundai Retailer from time to time, but in any event the Return Time is no later than
                                                the time when repairs to your own vehicle have been completed.
                                            </li>
                                        )}
                                        {networkCode !== 'HYUA' && (
                                            <li>
                                                The maximum duration of the Loan Agreement is three months. If you need the Vehicle for longer than this, we will need to enter into new agreement with
                                                you for the additional period.
                                            </li>
                                        )}
                                    </ol>
                                </li>

                                <li>
                                    USE OF THE VEHICLE
                                    <ol>
                                        {networkCode !== 'HYUA' && (
                                            <li>
                                                You must use the Vehicle only for personal use and not for the carriage of passengers and/or property, for loan or reward ,unless otherwise agreed in
                                                writing by us.
                                            </li>
                                        )}
                                        {networkCode === 'HYUA' && <li>You must use the Vehicle only for personal use and not for the carriage of passengers and/or property, for loan or reward.</li>}
                                        <li>You must not use the Vehicle or allow the Vehicle to be used for any purpose for which it is not designed or suitable.</li>

                                        <li>
                                            You must not use the Vehicle:
                                            <ol>
                                                <li>for any unlawful, hazardous or unusual purpose;</li>

                                                {networkCode !== 'HYUA' && (
                                                    <li>
                                                        for propelling or towing any other vehicle or trailer or for any similar purpose, unless appropriate towing equipment is installed on the
                                                        Vehicle;
                                                    </li>
                                                )}

                                                <li>for racing, rallying, pace-making, reliability, speed testing or other trials, competitions of any sort of driving tuition;</li>

                                                <li>
                                                    for carrying a number of passengers and/or property which would cause the Vehicle to be overloaded or would cause any applicable restriction to be
                                                    exceeded;
                                                </li>

                                                <li>
                                                    on any surface other than roads with a tarmacadam or concrete surface over which there is a right of way (public or private) for motor vehicles.
                                                </li>
                                            </ol>
                                        </li>

                                        <li>
                                            You and any Additional Driver(s) are allowed to drive the Vehicle. You must not allow the Vehicle to be driven by any other person. If you wish to add any
                                            Additional Driver(s) you must first get our written permission.
                                        </li>

                                        <li>
                                            You must not take or allow the Vehicle to be taken outside the United Kingdom unless the front page of the Loan Agreement shows that overseas use is
                                            permitted and we have issued you with an overseas driving pack. Any overseas use is conditional on you complying with the requirements set out in the
                                            overseas driving pack.
                                        </li>
                                    </ol>
                                </li>

                                <li>
                                    COSTS, FEES AND CHARGES
                                    <ol>
                                        <li>We do not charge any hire charge for lending you the Vehicle.</li>

                                        <li>
                                            You are responsible for paying the following costs, fees and charges relating to the period whilst the Vehicle is in your possession:
                                            <ol>
                                                {networkCode !== 'HYUA' && <li>all fuel costs;</li>}
                                                {networkCode === 'HYUA' && <li>all fuel costs and If applicable, charging costs;;</li>}
                                                <li>any tolls;</li>

                                                <li>all fines, penalties, costs, charges and liabilities relating to parking, road traffic or other road or driving offences or contraventions;</li>

                                                <li>
                                                    the cost of having the Vehicle released if it is impounded by any relevant authority (such as the Police, HMRC or parking enforcement authorities),
                                                    together with reasonable compensation to cover any loss of use we suffer whilst the Vehicle is impounded (unless the reason for the impounding was
                                                    caused by us);
                                                </li>

                                                <li>
                                                    the cost of refuelling the Vehicle if returned to us with less fuel than was contained in the Vehicle's fuel tank at the Collection Time (or with
                                                    less charge in the case of an electric vehicle) together with our current tariff for refuelling service charges;
                                                </li>

                                                {networkCode !== 'HYUA' && (
                                                    <li>our costs incurred in recovering the Vehicle in the event you fail to return it to us in accordance with the terms of the Loan Agreement;</li>
                                                )}

                                                {networkCode === 'HYUA' && (
                                                    <li>
                                                        our costs incurred in recovering the Vehicle in the event you fail to return it to the retailer in accordance with the terms of the Loan
                                                        Agreement;
                                                    </li>
                                                )}
                                                {networkCode !== 'HYUA' && <li>if the Vehicle is stolen, lost or damaged for any reason, the Insurance Excess amount shown on the Loan Agreement;</li>}
                                                {networkCode === 'HYUA' && (
                                                    <li>
                                                        if the Vehicle is stolen, lost or damaged for any reason, or if liability is incurred to a third party, the Insurance Excess amount shown on the
                                                        Loan Agreement;
                                                    </li>
                                                )}

                                                <li>any value added tax or local or other taxes payable in respect of any of the above.</li>
                                            </ol>
                                        </li>

                                        {networkCode !== 'HYUA' && (
                                            <li>
                                                If we incur any of the costs, fees or charges specified in clause 4.2, we are entitled to:
                                                <ol>
                                                    {networkCode === 'POR' && !isCCP(data?.entitlement?.customerGroup.code) && <li>claim them from you;</li>}
                                                    {isCCP(data?.entitlement?.customerGroup.code) && <li>claim them from you and/or deduct them from the security deposit you provide to us;</li>}

                                                    <li>apply a processing charge of &pound;100 per claim.</li>
                                                </ol>
                                            </li>
                                        )}
                                        {networkCode === 'HYUA' && (
                                            <li>
                                                If we incur any of the costs, fees or charges specified in clause 4.2, we are entitled to claim them from you. In the event that such costs have not
                                                been recovered from you following our first request, we reserve the right to apply a processing charge of up to £100 per claim.
                                            </li>
                                        )}
                                        <li>You must pay all amounts that you owe us within 30 days from the date we claim them from you.</li>

                                        {networkCode !== 'HYUA' && (
                                            <li>
                                                All overdue payments shall bear interest on the amount overdue, at the rate prevailing laid down by the Government, from the date such sums become due
                                                to the date of actual payment.
                                            </li>
                                        )}
                                        {networkCode === 'HYUA' && (
                                            <li>
                                                All overdue payments shall bear interest on the amount overdue, at the rate of 4% per annum, from the date such sums become due to the date of actual
                                                payment.
                                            </li>
                                        )}
                                    </ol>
                                </li>
                                {isCCP(data?.entitlement?.customerGroup.code) && (
                                    <li>
                                        SECURITY DEPOSIT
                                        <ol>
                                            <li>
                                                We may require you to provide a security deposit prior to the commencement of the loan of the Vehicle. The amount of the security deposit is as shown on
                                                the front of the Loan Agreement.
                                            </li>
                                            <li>The security deposit may be used by us as security for any future claims that we may have against you in connection with the Loan Agreement.</li>
                                            <li>
                                                We will collect the security deposit by credit card [or debit card], as follows:
                                                <ol>
                                                    <li>
                                                        credit card: we will request an electronic authorisation from your bank to ring-fence the security deposit amount against your account. The full
                                                        security deposit amount will not actually be drawn by us as funds, but the available credit on your account will be reduced by the security
                                                        deposit amount.
                                                    </li>
                                                    <li>
                                                        [debit card: we will request an electronic authorisation from your bank to ring-fence the security deposit amount against your account. The full
                                                        security deposit amount will not actually be drawn by us as funds, but you must still have sufficient funds in your account to cover the
                                                        security deposit amount. Funds available to you in the bank account used for the security deposit will be reduced by the pre-authorised amount
                                                        of the security deposit.]
                                                    </li>
                                                </ol>
                                            </li>
                                            <li>
                                                You agree that at any time during or after the loan period we may offset any amounts owed under the Loan Agreement (including those items listed at
                                                clause 4.2) against the security deposit. Where the security deposit is insufficient to cover such amounts, we are entitled to claim them from you.
                                            </li>
                                            <li>
                                                After you have returned the Vehicle to us and if there are no additional charges owed or amounts due to us under the Loan Agreement, we will release the
                                                security deposit amount back to you. Note that it may take [at least 10 working days] for the security deposit to be released and the security deposit
                                                amount to be available to you, depending on your bank or credit provider.
                                            </li>
                                        </ol>
                                    </li>
                                )}

                                <li>
                                    YOUR OBLIGATIONS
                                    <p>You must at all times during the term of the Loan Agreement:</p>
                                    <ol>
                                        <li>
                                            Take proper care of the Vehicle and in particular (without limitation) keep the Vehicle locked when not in use and ensure that ignition keys and/or security
                                            arming devices are not left in the Vehicle when unattended;
                                        </li>

                                        <li>Comply with the insurance conditions set out in the Loan Agreement;</li>

                                        {networkCode !== 'HYUA' && <li>Ensure the Vehicle is used in a lawful and reasonable manner;</li>}
                                        {networkCode === 'HYUA' && (
                                            <li>
                                                Ensure the Vehicle is used in a lawful, reasonable and safe manner which does not pose any risk to persons or property and indemnify the Company against
                                                any action or claims for damages (including costs) which may be brought against it, and or its representatives, agents and servants arising out of any
                                                reckless or irresponsible driving on your part;
                                            </li>
                                        )}
                                        <li>
                                            Immediately report any accident, loss or damage involving the Vehicle to us and the police or other proper authority and, at our request, complete our
                                            accident report form without delay;
                                        </li>

                                        <li>
                                            Immediately report any breakdown, fault or defect, reasonably requiring repair to us, and, in the case of a defect or fault which makes the Vehicle
                                            unroadworthy or liable, to cause damage or danger to persons or property, or further damage to the Vehicle, cease use of the Vehicle until such defect or
                                            fault has been repaired or corrected;
                                        </li>

                                        <li>Take all reasonable steps to prevent or mitigate any loss or damage occurring to the Vehicle;</li>

                                        {networkCode !== 'HYUA' && <li>Not make or allow to be made any repairs to the Vehicle except those we authorise;</li>}
                                        {networkCode === 'HYUA' && (
                                            <li>
                                                Not make or allow to be made any repairs to the Vehicle except those we authorise, nor place any signage or advertising on, or mark in any way, the
                                                Vehicle;
                                            </li>
                                        )}

                                        <li>Not remove or interfere with any the Vehicle parts or spares or with any identification marks or plates affixed to the Vehicle;</li>

                                        {networkCode !== 'HYUA' && <li>Inform us immediately upon request of the whereabouts of the Vehicle;</li>}
                                        {networkCode === 'HYUA' && (
                                            <li>
                                                Inform us immediately upon request of the whereabouts of the Vehicle and allow us to inspect and have reasonable access to the Vehicle at any time it is
                                                requested;
                                            </li>
                                        )}
                                        {networkCode !== 'HYUA' && (
                                            <li>
                                                Not sell, mortgage, charge, pledge, assign, underlet, lend or otherwise dispose of or part with possession of the Vehicle at any time or contract to do
                                                so or otherwise deal with the Vehicle in any manner inconsistent with our rights;
                                            </li>
                                        )}
                                        {networkCode === 'HYUA' && (
                                            <li>
                                                Not sell, mortgage, charge, pledge, assign, underlet, lend or otherwise dispose of or part with possession of the Vehicle at any time or contract to do
                                                so or otherwise deal with the Vehicle in any manner inconsistent with our rights. Title to the Vehicle will not in any circumstances transfer to you;
                                            </li>
                                        )}

                                        <li>Maintain all oil and fluid levels and tyre pressures in accordance with the manufacturer's recommendations;</li>
                                        <li>Not smoke or vape within the vehicle;</li>

                                        <li>
                                            At our request assist us in enforcing any rights or remedies we may have against third parties in respect of any loss or damage to or in connection with the
                                            Vehicle arising during the term of this Agreement.
                                        </li>
                                        {networkCode === 'HYUA' && <li>At all times comply with applicable road traffic laws.</li>}
                                        {networkCode === 'HYUA' && (
                                            <li>Advise your retailer if you are going on holiday, or be away for a period of time as your repair may be completed during this time.</li>
                                        )}
                                    </ol>
                                </li>

                                <li>
                                    LIABILITY
                                    <ol>
                                        <li>
                                            Nothing in this Loan Agreement will restrict or exclude our liability for death or personal injury caused by our negligence or any other liability which we
                                            cannot limit or exclude as a matter of law.
                                        </li>

                                        <li>
                                            We will not be liable to you for any indirect or consequential loss or damage (including loss of revenue), costs, expenses, liabilities or any other claims
                                            or demands arising out of or in respect of:
                                            <ol>
                                                <li>any breakdown, malfunction, failure or defect of the Vehicle;</li>

                                                <li>
                                                    any property left, stored or transported by you or by any other person in or upon the Vehicle either before or after the return of the Vehicle to
                                                    us.
                                                </li>
                                            </ol>
                                        </li>
                                    </ol>
                                </li>

                                <li>
                                    ENDING THE LOAN AGREEMENT EARLY
                                    <ol>
                                        {networkCode !== 'HYUA' && (
                                            <li>You can end the Loan Agreement early at any time by calling us. If you want to end the Loan Agreement early, you must agree a Return Time with us.</li>
                                        )}
                                        {networkCode === 'HYUA' && (
                                            <li>
                                                You can end the Loan Agreement early at any time by calling us. If you want to end the Loan Agreement early, you must agree a Return Time with your
                                                retailer.
                                            </li>
                                        )}
                                        <li>
                                            We can end the Loan Agreement early if:
                                            <ol>
                                                <li>you commit any breach of the Loan Agreement;</li>

                                                <li>any statement, representation or warranty made by you in the Loan Agreement in respect of yourself or any Additional Driver is incorrect; or</li>

                                                <li>you become bankrupt or suffer any similar form of insolvency.</li>
                                            </ol>
                                        </li>
                                        <li>If we end the Loan Agreement early under clause 7.2 we will notify you and we will inform you of the Return Date.</li>
                                    </ol>
                                </li>

                                <li>
                                    RETURNING THE VEHICLE AT THE END OF THE LOAN AGREEMENT
                                    <ol>
                                        <li>
                                            At the end of the Loan Agreement (whether it has ended early or otherwise), you must:
                                            <ol>
                                                <li>
                                                    return the Vehicle to us in the same condition as when received (fair wear and tear only excepted) as evidenced by your signature on the Condition
                                                    Report;
                                                </li>

                                                {networkCode !== 'HYUA' && (
                                                    <li>return the Vehicle to the Return Address (or such other place as we agree) on or before the Return Time, during our normal working hours.</li>
                                                )}
                                                {networkCode === 'HYUA' && (
                                                    <li>
                                                        return the Vehicle to the Return Address (or such other place as we agree) on or before the Return Time, during your retailer's normal working
                                                        hours.
                                                    </li>
                                                )}
                                            </ol>
                                        </li>

                                        <li>
                                            If you fail to return the Vehicle on or before the Return Time, we may, without notice, retake possession of the Vehicle together with any of its
                                            accompanying documentation and for such purpose may enter upon any premises belonging to you or in your occupation or control.
                                        </li>
                                    </ol>
                                </li>

                                <li>
                                    {' '}
                                    DATA PROTECTION
                                    <ol>
                                        <li>We will use the information that you provide to us to carry out our obligations under the Loan Agreement.</li>

                                        <li>
                                            We may pass your information to third parties as necessary to perform our obligations or enforce our rights under the Loan Agreement. Such third parties may
                                            include:
                                            <ol>
                                                <li>the Driver and Vehicle Licensing Agency (DVLA);</li>

                                                <li>the provider from time to time of our insurance policy;</li>

                                                {networkCode !== 'HYUA' && networkCode !== 'POR' && <li>Jaguar Land Rover Limited and/or any other company within the Jaguar Land Rover Group;</li>}
                                                {networkCode === 'HYUA' && <li>Hyundai Motor UK Limited and any Hyundai car dealership/retailer;</li>}
                                                {networkCode === 'POR' && <li>Porsche Cars Great Britain Limited and any Porsche car dealership;</li>}

                                                <li>any other company within our corporate group;</li>

                                                <li>any company from whom we have ourselves hired the Vehicle.</li>
                                            </ol>
                                        </li>

                                        <li>
                                            The Vehicle is fitted with telemetry systems or other similar devices that will track the Vehicle location, mileage and fuel use. This is to maintain and
                                            protect the Vehicle.
                                        </li>

                                        <li>
                                            We use your personal data for purposes including the following: managing products and services relating to the product or service, for direct marketing
                                            communications and related profiling, to develop new products and services, to review and improve current products and services, to comply with legal and
                                            regulatory obligations, requirements and guidance, to provide insight and analysis of our customers both for ourselves and for the benefit of business
                                            partners, and for certain other purposes described in our privacy notice. Our full privacy notice is available at -
                                            <a
                                                style={{ color: '#08828d', textDecoration: 'none' }}
                                                href="https://www.theaa.com/privacy-notice"
                                                target="_blank"
                                                rel="noreferrer"
                                                onMouseOver={(e) => (e.currentTarget.style.textDecoration = 'underline')}
                                                onMouseOut={(e) => (e.currentTarget.style.textDecoration = 'none')}
                                            >
                                                https://www.theaa.com/privacy-notice
                                            </a>
                                            . This also details your rights and choices.
                                        </li>
                                    </ol>
                                </li>

                                <li>
                                    ENTIRE AGREEMENT
                                    <ol>
                                        <li>This Loan Agreement constitutes the entire agreement and understanding between you and us.</li>

                                        <li>No variation to this Agreement shall be binding unless agreed in writing by us.</li>
                                    </ol>
                                </li>
                            </ol>
                        </div>
                        <div className="contract-info">
                            <div className="col-sm-12">
                                {networkCode === 'POR' && (
                                    <div
                                        className="termsofhire-por"
                                        ng-if="ctrl.isPorsche()"
                                    >
                                        <h2 ng-if="!ctrl.isHyundai()">Terms of hire </h2>
                                        <h3>Customer declaration</h3>
                                        <p>
                                            I am over 21, and I have held a full, valid UK driving licence applicable to the vehicle for at least 12 months (or a driving licence issued in the EU,
                                            Switzerland, Norway, USA, Canada, Australia or New Zealand for at least 12 months).
                                        </p>
                                        I have not:
                                        <ol
                                            className="checkoutlastList"
                                            style={{ listStyle: 'disc' }}
                                        >
                                            <li>been refused motor insurance or had any insurance policy cancelled;</li>
                                            <li>accumulated more than 9 penalty points in the last 3 years;</li>
                                            <li>been disqualified from driving for a period exceeding 3 months in the last year;</li>
                                            <li> been disqualified from driving for a period exceeding 6 months in the last 3 years;</li>
                                            <li>
                                                {' '}
                                                been disqualified from driving for no more than 12 months in the last 3 years, extended to 5 years where any driving disqualification exceeds 12 months;{' '}
                                            </li>
                                            <li>been involved in more than three accidents in the last 3 years;</li>
                                            <li>made more than two theft claims in the last 3 years;</li>
                                            <li>had special conditions or increased premiums requested due to previous accidents or claims.</li>
                                        </ol>
                                        <p>I am not a professional sportsperson, member of the entertainment profession, model or itinerant worker.</p>
                                        <p>
                                            I do not have any of the following convictions on my licence: UT50, CD40, CD50, CD60, CD70, CD71, DD40, DD60, DD80, DR10, DR20, DR30, DR31, DR40, DR50,
                                            DR60, DR61, DR70, DR80 or DR90.
                                        </p>
                                        <p>
                                            I confirm that, if I have any health condition or disability that could affect my ability to drive safely, I have notified the DVLA, and the DVLA has
                                            authorised me to drive. I have given consent for the Company or Company representative to carry out a driving licence check and I understand that should I
                                            incur any pending or new endorsements or restrictions during the period of loan, failure to inform the Company immediately may invalidate the insurance. I
                                            will pay any charges for loss/damage as a result of not using the correct fuel. I accept that these terms also apply to any additional authorised driver.
                                        </p>
                                        <p>I confirm that any Additional Driver (as set out in the Customer Details section above) also meets all of the above-mentioned criteria.</p>
                                    </div>
                                )}
                                {networkCode !== 'POR' && (
                                    <div>
                                        <h2>Customer declaration</h2>
                                    </div>
                                )}
                                <ol className="checkoutlastList">
                                    <li>I acknowledge that I am being loaned the above vehicle solely as a temporary replacement whilst my own vehicle is being repaired.</li>
                                    <li>I confirm the above details are complete and accurate.</li>
                                    <li>I agree to the insurance terms and conditions above, the condition report and the terms and conditions below.</li>
                                    {isCCP(data?.entitlement?.customerGroup.code) && (
                                        <li>
                                            I agree that I will be required to provide a security deposit to the value set out above and in accordance with the terms and conditions below prior to the
                                            loan of the Vehicle.
                                        </li>
                                    )}
                                    {networkCode !== 'HYUA' && <li>I consent to my personal information being processed to fulfil this Loan Agreement.</li>}
                                    {networkCode === 'HYUA' && (
                                        <li>
                                            I consent to my personal information being processed to fulfil this Loan Agreement and acknowledge that the vehicle is fitted with a tracking device. I
                                            consent that the data collected from the tracking device during the loan may be processed.
                                        </li>
                                    )}

                                    {!isCCP(data?.entitlement?.customerGroup.code) && (
                                        <li>
                                            I agree to return the loan vehicle promptly on completion of repairs to my own vehicle, and I acknowledge that the loan vehicle may be repossessed if I fail
                                            to do so.
                                        </li>
                                    )}

                                    {isCCP(data?.entitlement?.customerGroup.code) && (
                                        <li>
                                            I agree to return the loan vehicle promptly on completion of repairs to my own vehicle or as otherwise instructed and I acknowledge that the loan vehicle
                                            may be repossessed if I fail to do so
                                        </li>
                                    )}
                                </ol>
                            </div>
                        </div>
                        <div className="signatures">
                            <div className="mt-4">
                                <div className="flex gap-4">
                                    <div className="relative top-1 mb-4 w-2/6">
                                        <label className="checkoutRetailerSign">Retailer signature</label>
                                        <SignatureCanvas
                                            ref={retailSignPad}
                                            penColor="black"
                                            onEnd={() => {
                                                const retSignatureData = retailSignPad.current?.toDataURL();
                                                console.log('Retailer Signature:', retSignatureData);
                                                setRetailerSignature(retSignatureData || '');
                                            }}
                                            canvasProps={{ className: 'form-control signature' }}
                                            {...({} as any)}
                                        />
                                        <span
                                            data-testid="ClearRetailerSignature"
                                            className="clear-signature"
                                            onClick={() => clear('retailer')}
                                        >
                                            Clear
                                        </span>
                                    </div>

                                    <div className="relative top-1 mb-4 w-2/6 no-gutter">
                                        <label className="block checkoutCustSign">
                                            Customer signature
                                            <span className="float-right">
                                                <input
                                                    className="w-auto"
                                                    type="checkbox"
                                                    onChange={(e) => processCollectedBy(e.target.checked)}
                                                />
                                                &nbsp;Home delivery
                                            </span>
                                        </label>
                                        <SignatureCanvas
                                            ref={custSignPad}
                                            penColor="black"
                                            onEnd={() => {
                                                const custSignatureData = custSignPad.current?.toDataURL();
                                                console.log('Customer Signature:', custSignatureData);
                                                setCustomerSignature(custSignatureData || '');
                                            }}
                                            canvasProps={{ className: `form-control signature ${disableCustSign ? 'disbledColor' : ''}` }}
                                            {...({} as any)}
                                        />
                                        <span
                                            data-testid="ClearCustomerSignature"
                                            className="clear-signature"
                                            onClick={() => clear('customer')}
                                        >
                                            Clear
                                        </span>
                                    </div>

                                    <div className="w-2/6 form-group">
                                        <div className="row">
                                            <div className="col-sm-12">
                                                <label className="key">Delivered by</label>
                                                <input
                                                    onChange={(e) => {
                                                        setDeliveredBy(e.target.value);
                                                        setCheckInPageDetails &&
                                                            setCheckInPageDetails({
                                                                ...checkInPageDetails,
                                                                conditionReport: {
                                                                    ...checkInPageDetails.conditionReport,
                                                                    homedelivery: {
                                                                        ...checkInPageDetails.conditionReport.homedelivery,
                                                                        isHomeDelivery: true,
                                                                        deliveredBy: e.target.value
                                                                    }
                                                                }
                                                            });
                                                    }}
                                                    className={`form-control ${disableCustSign ? '' : 'disbledColor'}`}
                                                    value={deliveredBy}
                                                />
                                            </div>
                                        </div>

                                        <div className="gutter-mt-20 row">
                                            <div className="col-sm-12">
                                                <label className="mt-6 key">
                                                    <input
                                                        className="w-auto"
                                                        type="checkbox"
                                                    />
                                                    &nbsp;See paperwork for customer signature
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
export default memo(TermsandconditionCheckout);
