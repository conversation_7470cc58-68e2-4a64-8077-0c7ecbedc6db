import { useEffect, useRef, useState } from 'react';
import './condition-report-checkout.css';
import Icon from '../../../assets/icomoon/icon';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '../../components/ui/carousel';
import { Dialog, DialogContent, DialogTrigger } from '../../components/ui/dialog';
import { useCommonStore } from '../../contexts/CommonStore';
import { memo } from 'react';
import { CSSProperties } from 'react';
import { ReactSVG } from 'react-svg';

const formSchema = z.object({
    username: z.string().min(2, {
        message: 'Username must be at least 2 characters.'
    })
});

interface ConditionReportCheckoutProps {
    details: any;
    validateFields: (isValid: boolean) => void;
}

function ConditionReportCheckout({ details, validateFields }: ConditionReportCheckoutProps) {
    const { networkCode, checkInPageDetails, setCheckInPageDetails } = useCommonStore() || {};
    const [selectedDamage, setSelectedDamage] = useState('');
    const [mileage, setMileage] = useState(checkInPageDetails?.conditionReport?.mileage ? checkInPageDetails.conditionReport.mileage : details?.mileage || '');
    const [damages, setDamages] = useState(checkInPageDetails?.conditionReport?.damages.length > 0 ? checkInPageDetails?.conditionReport?.damages : details?.damages || []);

    const [fuel, setFuel] = useState(checkInPageDetails?.conditionReport?.fuel !== '' ? checkInPageDetails?.conditionReport?.fuel : details?.rental?.checkoutReport?.fuel);
    const [files, setFiles] = useState<any[]>(checkInPageDetails?.photos ? checkInPageDetails?.photos : []);
    const [photos, setPhotos] = useState(details?.photos || []);
    const [checkinObservation, setCheckinObservation] = useState(checkInPageDetails?.checkInObs ? checkInPageDetails?.checkInObs : details?.rental?.checkinReport?.comments);
    const [chekOutbservation, setCheckOutObservation] = useState(details?.rental?.checkoutReport?.comments || '');

    const [validationObj, setValidationObj] = useState({
        mileageMsg: '',
        checkinPhotoMsg: '',
        checkFuelMsg: ''
    });

    useEffect(() => {
        validateField();
    }, [mileage, files, fuel, damages, checkinObservation]);

    const validateField = () => {
        setValidationObj({
            mileageMsg: mileage === '' ? 'This field is required.' : '',
            checkFuelMsg: fuel === '' || fuel === undefined || fuel === null ? 'This field is required.' : '',
            checkinPhotoMsg: files.length < 4 ? 'At least 4 photos required.' : ''
        });
        if (mileage && files.length >= 4 && fuel !== '' && fuel !== undefined && fuel !== null) {
            const photos: any = [];
            files.forEach((file) => photos.push(file));

            setCheckInPageDetails &&
                setCheckInPageDetails({
                    ...checkInPageDetails,
                    taskId: details?.id,
                    conditionReport: {
                        mileage: mileage,
                        fuel: fuel,
                        photos: [],
                        homedelivery: { isHomeDelivery: false },
                        homecollection: {
                            isHomeCollection: details?.rental?.checkinReport?.homecollection?.isHomeCollection,
                            collectedBy: details?.rental?.checkinReport?.homecollection?.collectedBy || ''
                        },
                        damages: damages.length > 0 ? damages : [],
                        comments: checkinObservation,
                        extras: {
                            spareKeyFob: checkInPageDetails?.conditionReport?.extras?.spareKeyFob ? checkInPageDetails.conditionReport.extras.spareKeyFob : false,
                            euroPack: checkInPageDetails?.conditionReport?.extras?.euroPack ? checkInPageDetails.conditionReport.extras.euroPack : false,
                            lockingWheelNut: checkInPageDetails?.conditionReport?.extras?.lockingWheelNut ? checkInPageDetails.conditionReport.extras.lockingWheelNut : false,
                            detachableTowBar: checkInPageDetails?.conditionReport?.extras?.detachableTowBar ? checkInPageDetails.conditionReport.extras.detachableTowBar : false,
                            tracker: checkInPageDetails?.conditionReport?.extras?.tracker ? checkInPageDetails.conditionReport.extras.tracker : false,
                            porscheHandbook: checkInPageDetails?.conditionReport?.extras?.porscheHandbook ? checkInPageDetails.conditionReport.extras.porscheHandbook : false,
                            key: checkInPageDetails?.conditionReport?.extras?.key ? checkInPageDetails.conditionReport.extras.key : false,
                            hvchargingCable: checkInPageDetails?.conditionReport?.extras?.hvchargingCable ? checkInPageDetails.conditionReport.extras.hvchargingCable : false,
                            bootPack: checkInPageDetails?.conditionReport?.extras?.bootPack ? checkInPageDetails.conditionReport.extras.bootPack : false,
                            iccbChargingCable: checkInPageDetails?.conditionReport?.extras?.iccbChargingCable ? checkInPageDetails.conditionReport.extras.iccbChargingCable : false,
                            type2ConnectorChargingCable: checkInPageDetails?.conditionReport?.extras?.type2ConnectorChargingCable
                                ? checkInPageDetails.conditionReport.extras.type2ConnectorChargingCable
                                : false,
                            manuals: checkInPageDetails?.conditionReport?.extras?.manuals ? checkInPageDetails.conditionReport.extras.manuals : false,
                            parcelShelf: checkInPageDetails?.conditionReport?.extras?.parcelShelf ? checkInPageDetails.conditionReport.extras.parcelShelf : false,
                            punctureRepairKit: checkInPageDetails?.conditionReport?.extras?.punctureRepairKit ? checkInPageDetails.conditionReport.extras.punctureRepairKit : false,
                            carpetMats: checkInPageDetails?.conditionReport?.extras?.carpetMats ? checkInPageDetails.conditionReport.extras.carpetMats : false,
                            comments: checkInPageDetails?.conditionReport?.extras?.comments ? checkInPageDetails.conditionReport.extras.comments : ''
                        },
                        docName: null,
                        eDocName: null,
                        eDocLink: null
                    },
                    signatures: {
                        loaner: checkInPageDetails?.signatures?.loaner ? checkInPageDetails.signatures.loaner : '',
                        loanee: checkInPageDetails?.signatures?.loanee ? checkInPageDetails.signatures.loanee : ''
                    },
                    photos,
                    emailToSendPrintableCopy: details?.rental?.mainDriver?.contact?.email || '',
                    checkInObs: checkinObservation
                });
            validateFields(true);
        } else {
            validateFields(false);
        }
    };

    const getEventXY = (event: any, end?: boolean) => {
        const parent = document.getElementById('drop-zone')?.getBoundingClientRect();

        if (!parent) return { x: 0, y: 0 };

        if (event.clientX !== undefined) {
            return {
                x: event.clientX - parent.left,
                y: event.clientY - parent.top
            };
        }

        let touches;

        if (!end) {
            touches = event.touches;
        } else {
            touches = event.changedTouches;
        }

        return {
            x: touches[0].clientX - parent.left,
            y: touches[0].clientY - parent.top
        };
    };

    const getRelativePosition = (parent: DOMRect, position: { x: number; y: number; left?: number; top?: number }) => {
        const el = document.getElementById('dimline')?.getBoundingClientRect();
        if (!el) return position;

        const height = el.height;
        const width = el.width;

        if (height > width) {
            position.y = position.y - (parent.height - height) / 2;
        } else {
            position.x = position.x - (parent.width - width) / 2;
        }

        position.left = (position.x / width) * 100 - 50;
        position.top = (position.y / height) * 100 - 50;

        return { ...position };
    };

    const handleMouseDown = (e: any) => {
        const plan = document.getElementById('drop-zone');
        const node = e.currentTarget.cloneNode(true);
        const xy = getEventXY(e);

        node.style.top = `${xy.y - 15}px`;
        node.style.left = `${xy.x - 15}px`;

        plan?.appendChild(node);

        const handleMouseMove = (e: any) => {
            const lastKnownXY = getEventXY(e);
            const lastKnownPosition = { x: lastKnownXY.x, y: lastKnownXY.y };

            getRelativePosition(plan!.getBoundingClientRect(), lastKnownPosition);

            node.style.top = `${lastKnownXY.y - 15}px`;
            node.style.left = `${lastKnownXY.x - 15}px`;
        };

        const handleMouseUp = (e: any) => {
            let lastKnownXY;
            let lastKnownPosition;
            const _lastMoveEvent = e;

            try {
                lastKnownXY = getEventXY(e);
                lastKnownPosition = { x: lastKnownXY.x, y: lastKnownXY.y };
            } catch (err) {
                lastKnownXY = getEventXY(_lastMoveEvent);
                lastKnownPosition = { x: lastKnownXY.x, y: lastKnownXY.y };
            }

            const dataPosition = getRelativePosition(plan!.getBoundingClientRect(), lastKnownPosition);
            const left = dataPosition.left;
            const top = dataPosition.top;

            const newDamage = {
                id: 'null',
                type: e.srcElement.classList[0],
                location: {
                    x: left,
                    y: top
                }
            };

            if (lastKnownPosition.y <= 750) {
                setDamages((prevDamages: any) => [...prevDamages, newDamage]);
                setSelectedDamage('');
            }

            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);

            node.remove();
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    };

    useEffect(() => {
        const symbols = document.querySelectorAll('.key .symbol');
        symbols.forEach((symbol) => {
            symbol.addEventListener('mousedown', handleMouseDown);
        });

        return () => {
            symbols.forEach((symbol) => {
                symbol.removeEventListener('mousedown', handleMouseDown);
            });
        };
    }, []);
    // 1. Define your form.
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            username: ''
        }
    });

    // 2. Define a submit handler.
    function onSubmit(values: z.infer<typeof formSchema>) {
        // Do something with the form values.
        //  This will be type-safe and validated.
        //console.log(values);
    }

    const getSilhouette = (report: any, enablePorscheHire?: boolean, enableHyundaiHire?: boolean): string => {
        let retVal = 'generic';

        if (networkCode === 'POR' && report?.entitlement.customerGroup.name === 'PORSCHE') {
            retVal = 'porsche';
        } else if (networkCode === 'HYUA' && report?.entitlement?.customerGroup?.name === 'HYUNDAI') {
            retVal = report?.rental?.hireVehicle?.model?.name === 'CAR UNKNOWN' ? 'hyundai' : report?.rental?.hireVehicle?.model?.name?.toLowerCase();
        } else if (report?.rental?.hireVehicle.supplierTypeCode !== 'THRIFTY') {
            retVal = report?.rental?.hireVehicle?.make?.name?.toLowerCase().replace(' ', '-');
        }

        return `${retVal}.svg`;
    };

    const handleDragOver = (e: any) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        const damageType = e.dataTransfer.getData('text/plain');
        if (damageType === 'scratch') {
            e.target.innerText = 'S';
        } else if (damageType === 'chip') {
            e.target.innerText = 'C';
        } else if (damageType === 'dent') {
            e.target.innerText = 'D';
        }
        e.dataTransfer.dropEffect = 'move';
    };
    const canvasRef = useRef<HTMLDivElement>(null);

    const handleDragStart = (e: any) => {
        setSelectedDamage(e.target.classList[0]);
    };

    const handleInputChange = (e: any) => {
        const value = e.target.value;
        const regex = /^(0|[1-9][0-9]*)$/;

        if (regex.test(value) && value.length <= 6) {
            setMileage(value);
        } else if (value === '') {
            setMileage('');
        }
    };

    const handleFuelClick = (level: any) => {
        setFuel(level);
    };

    const openLightboxModal = (index: number, flag: any) => {
        // Implement the logic to open the lightbox modal
    };

    const removecheckinImage = (photo: any) => {
        // Implement the logic to remove the image
        setPhotos(photos.filter((p: any) => p !== photo));
        //console.log(`Removing photo: ${photo.url}`);
    };

    const getStyle = (damage: any): CSSProperties => {
        const el = document.getElementById('dimline')?.getBoundingClientRect();
        const parent = document.getElementById('drop-zone')?.getBoundingClientRect();

        if (!el || !parent) return {};

        const x = damage.location.x + 50;
        const xDiff = (parent.width - el.width) / 2;

        const y = damage.location.y + 50;
        const yDiff = (parent.height - el.height) / 2;

        return {
            left: `calc(${xDiff}px + ${(el.width / 100) * x}px - 15px)`,
            top: `calc(${yDiff}px + ${(el.height / 100) * y}px - 30px)`
        };
    };

    return (
        <>
            <div className="h-full container-fluid manualHeightMain">
                <div className="h-full row">
                    <div className="float-left px-4 w-full sm:w-2/3 manualHeight">
                        <div className="content-shadow h-full main scroll-wrapper">
                            <div
                                className="plan"
                                id="drop-zone"
                                ref={canvasRef}
                                //onDragOver={handleDragOver}
                                //onDrop={handleDrop}
                            >
                                <div className="center picture">
                                    <input
                                        data-testid="file-input-center"
                                        type="file"
                                        multiple
                                        onChange={(e) => {
                                            const selectedFiles = e.target.files ? Array.from(e.target.files) : [];
                                            setFiles((prevFiles) => [...prevFiles, ...selectedFiles]);
                                        }}
                                    />
                                    <button className="btn btn-flat btn-primary">
                                        <span>
                                            <Icon
                                                icon="camera"
                                                size={30}
                                                color="#3e3d3d"
                                                className="flex100"
                                            />
                                        </span>
                                    </button>
                                </div>

                                <div className="top picture">
                                    <input
                                        data-testid="file-input-top"
                                        type="file"
                                        multiple
                                        onChange={(e) => {
                                            const selectedFiles = e.target.files ? Array.from(e.target.files) : [];
                                            setFiles((prevFiles) => [...prevFiles, ...selectedFiles]);
                                        }}
                                    />
                                    <button className="btn btn-flat btn-primary">
                                        <span>
                                            <Icon
                                                icon="camera"
                                                size={30}
                                                color="#3e3d3d"
                                                className="flex100"
                                            />
                                        </span>
                                    </button>
                                </div>

                                <div className="right picture">
                                    <input
                                        data-testid="file-input-right"
                                        type="file"
                                        multiple
                                        onChange={(e) => {
                                            const selectedFiles = e.target.files ? Array.from(e.target.files) : [];
                                            setFiles((prevFiles) => [...prevFiles, ...selectedFiles]);
                                        }}
                                    />
                                    <button className="btn btn-flat btn-primary">
                                        <span>
                                            <Icon
                                                icon="camera"
                                                size={30}
                                                color="#3e3d3d"
                                                className="flex100"
                                            />
                                        </span>
                                    </button>
                                </div>

                                <div className="bottom picture">
                                    <input
                                        data-testid="file-input-bottom"
                                        type="file"
                                        multiple
                                        onChange={(e) => {
                                            const selectedFiles = e.target.files ? Array.from(e.target.files) : [];
                                            setFiles((prevFiles) => [...prevFiles, ...selectedFiles]);
                                        }}
                                    />
                                    <button className="btn btn-flat btn-primary">
                                        <span>
                                            <Icon
                                                icon="camera"
                                                size={30}
                                                color="#3e3d3d"
                                                className="flex100"
                                            />
                                        </span>
                                    </button>
                                </div>

                                <div className="left picture">
                                    <input
                                        data-testid="file-input-left"
                                        type="file"
                                        multiple
                                        onChange={(e) => {
                                            const selectedFiles = e.target.files ? Array.from(e.target.files) : [];
                                            setFiles((prevFiles) => [...prevFiles, ...selectedFiles]);
                                        }}
                                    />
                                    <button className="btn btn-flat btn-primary">
                                        <span>
                                            <Icon
                                                icon="camera"
                                                size={30}
                                                color="#3e3d3d"
                                                className="flex100"
                                            />
                                        </span>
                                    </button>
                                </div>
                                <div className="svg-wrapper">
                                    <ReactSVG
                                        className="svgMaindiv"
                                        style={{ height: '100%' }}
                                        src={`./assets/images/${getSilhouette(details)}`}
                                    />
                                </div>
                                {damages?.map((damage: any, index: number) => (
                                    <div
                                        onDoubleClick={() => setDamages(damages.filter((_: any, i: number) => i !== index))}
                                        key={index}
                                        className={`${damage.type} symbol`}
                                        style={getStyle(damage)}
                                    >
                                        {damage.type === 'scratch' && 'S'}
                                        {damage.type === 'chip' && 'C'}
                                        {damage.type === 'dent' && 'D'}
                                    </div>
                                ))}
                            </div>
                            <div className="legend">
                                <div className="key">
                                    <div
                                        className="scratch symbol"
                                        //draggable
                                        //onDragStart={handleDragStart}
                                    >
                                        S
                                    </div>
                                    Scratch
                                </div>

                                <div className="key">
                                    <div
                                        className="chip symbol"
                                        //draggable
                                        //onDragStart={handleDragStart}
                                    >
                                        C
                                    </div>
                                    Chip
                                </div>

                                <div className="key">
                                    <div
                                        className="dent symbol"
                                        //draggable
                                        //onDragStart={handleDragStart}
                                    >
                                        D
                                    </div>
                                    Dent
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="float-right px-4 w-full sm:w-1/3 manualHeight">
                        <div
                            className="content-shadow h-full scroll-wrapper"
                            id="condition-report-checkout"
                        >
                            <form name="conditionReportForm">
                                <div className="">
                                    <div
                                        className="relative py-5 bordered mileage row shrink-0"
                                        ng-className="{ 'has-error' : $ctrl.validateFormField('mileage') }"
                                    >
                                        <div className="p-0 w-full">
                                            <label className="left-label">Mileage</label>
                                        </div>
                                        <div className="mt-2 p-0 w-full">
                                            <input
                                                data-testid="mileage"
                                                type="text"
                                                name="mileage"
                                                className="form-control-input"
                                                value={mileage}
                                                onChange={handleInputChange}
                                                required
                                            />
                                            {validationObj.mileageMsg !== '' && <span className="error-msg">This field is required.</span>}
                                        </div>
                                    </div>

                                    <div
                                        className="relative py-5 bordered row shrink-0"
                                        ng-className="{ 'has-error' :  $ctrl.validateFormField('fuel') }"
                                    >
                                        <label>Fuel/Battery</label>
                                        <div className="flex mt-2 pb-2 list-row">
                                            <div
                                                data-testid="empty"
                                                className={`fuel-select ${fuel === 0 ? 'active' : ''}`}
                                                onClick={() => handleFuelClick(0)}
                                            >
                                                Empty
                                            </div>
                                            <div
                                                data-testid="quarter"
                                                className={`fuel-select ${fuel === 25 ? 'active' : ''}`}
                                                onClick={() => handleFuelClick(25)}
                                            >
                                                1/4
                                            </div>
                                            <div
                                                data-testid="half"
                                                className={`fuel-select ${fuel === 50 ? 'active' : ''}`}
                                                onClick={() => handleFuelClick(50)}
                                            >
                                                1/2
                                            </div>
                                            <div
                                                data-testid="three-quarter"
                                                className={`fuel-select ${fuel === 75 ? 'active' : ''}`}
                                                onClick={() => handleFuelClick(75)}
                                            >
                                                3/4
                                            </div>
                                            <div
                                                data-testid="full"
                                                className={`fuel-select ${fuel === 100 ? 'active' : ''}`}
                                                onClick={() => handleFuelClick(100)}
                                            >
                                                Full
                                            </div>
                                        </div>

                                        {validationObj.checkFuelMsg != '' && <span className="error-msg">This field is required.</span>}
                                    </div>
                                    <div
                                        className="relative py-5 bordered row shrink-0"
                                        ng-className="{ 'has-error' : $ctrl.validateFormField('rawPhotos') }"
                                    >
                                        <div>
                                            {/* Image Dialog */}
                                            <label>Check-out photos</label>

                                            <div className="w-1/2 images-scroll">
                                                <div className="flex pb-1 w-1/2 list-row">
                                                    {files.map((file: any, index: any) => {
                                                        const reader = new FileReader();
                                                        reader.readAsDataURL(file);
                                                        return (
                                                            <Dialog>
                                                                <DialogTrigger asChild>
                                                                    <img
                                                                        key={index}
                                                                        src={URL.createObjectURL(file)}
                                                                        alt={`Photo ${index + 1}`}
                                                                        style={{ cursor: 'pointer' }}
                                                                    />
                                                                </DialogTrigger>
                                                                <DialogContent className="">
                                                                    <div className="grid py-4">
                                                                        <Carousel className="w-full">
                                                                            <CarouselContent>
                                                                                {files.map((innerfile, innerindex) => (
                                                                                    <CarouselItem key={innerindex}>
                                                                                        <div className="">
                                                                                            <img
                                                                                                key={innerindex}
                                                                                                src={URL.createObjectURL(innerfile)}
                                                                                                alt={`Photo ${index + 1}`}
                                                                                                style={{ cursor: 'pointer' }}
                                                                                            />
                                                                                        </div>
                                                                                    </CarouselItem>
                                                                                ))}
                                                                            </CarouselContent>
                                                                            <CarouselPrevious />
                                                                            <CarouselNext />
                                                                        </Carousel>
                                                                    </div>
                                                                </DialogContent>
                                                            </Dialog>
                                                        );
                                                    })}
                                                </div>
                                            </div>
                                        </div>
                                        {validationObj.checkinPhotoMsg != '' && <span className="error-msg">At least 4 photos required.</span>}
                                    </div>

                                    <div className="py-5 shrink-0">
                                        <div className="h-full previousImg">
                                            <label className="block">Checkout observations</label>
                                            <textarea
                                                onChange={(e) => setCheckinObservation(e.target.value)}
                                                className="report-textarea textarea-sharp"
                                                placeholder="Enter notes here"
                                                ng-model-options="{getterSetter: true}"
                                                value={checkinObservation}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
export default memo(ConditionReportCheckout);
