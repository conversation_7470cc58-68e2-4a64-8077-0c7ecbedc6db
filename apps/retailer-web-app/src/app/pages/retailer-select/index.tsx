import { useEffect, useState } from 'react';
import './retailer-select.css';
import { fetchDefleetHire, fetchHireTasks, fetchRafVehicles, fetchSuppliers, fetchTempRepairData } from '../../services/generalService';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useCommonStore } from '../../contexts/CommonStore';
import { fetchIncomingRecoveries, getExtensions } from '../../services/generalService';

export const RetailerSelect = () => {
    const authContext = useAuth();

    const navigate = useNavigate();
    const [inputValue, setInputValue] = useState('');
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    interface Retailer {
        id: string | number;
        name: string;
        address: string;
    }

    const [retailers, setRetailers] = useState<Retailer[]>([]);
    const { selectedRetailerData, setSelectedRetailerData, setNetworkCode, setIncomingRecoveries, setHireTasksData, setRafVehiclesData, setTempVehiclesData, setDeFleetedData, setExtensions } =
        useCommonStore() || {};

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        e.preventDefault();
        const value = e.target.value;

        setInputValue(value);
        setIsDropdownOpen(value.length >= 1);
    };

    const handleSelectRetailer = (event: React.MouseEvent<HTMLLIElement>, retailer: any) => {
        event.preventDefault();
        setSelectedRetailerData && setSelectedRetailerData(retailer);
        setNetworkCode && setNetworkCode(retailer?.supplierNet?.supNetworkCode);
        setInputValue(retailer.name);
        setIsDropdownOpen(false);
    };

    //const filteredRetailers = retailers.filter((ret) => ret?.name?.toLowerCase().includes(inputValue?.toLowerCase()));
    const filteredRetailers = retailers.filter(
        (ret) =>
            ret?.name?.toLowerCase().includes(inputValue?.toLowerCase()) ||
            ret?.address?.toLowerCase().includes(inputValue?.toLowerCase()) ||
            (typeof ret?.id === 'number' && ret.id.toString().toLowerCase().includes(inputValue?.toLowerCase()))
    );

    const redirectHome = async () => {
        try {
            if (!authContext) {
                throw new Error('authContext is null');
            }
            const data = await fetchIncomingRecoveries(authContext.authUser.data.authorization, selectedRetailerData.id);
            const hireTaskDt = await fetchHireTasks(authContext.authUser.data.authorization, selectedRetailerData.id);
            const rafVehicledt = await fetchRafVehicles(authContext.authUser.data.authorization, selectedRetailerData.id);
            const tempRepair = await fetchTempRepairData(authContext.authUser.data.authorization, selectedRetailerData.id);
            const DefleetedData = await fetchDefleetHire(authContext.authUser.data.authorization, selectedRetailerData.id);
            //const extensions = await getExtensions(authContext.authUser.data.authorization, selectedRetailerData.id);
            if (data && hireTaskDt) {
                setIncomingRecoveries && setIncomingRecoveries(data);
                setHireTasksData?.(hireTaskDt);
                setRafVehiclesData?.(rafVehicledt);
                setTempVehiclesData?.(tempRepair);
                setDeFleetedData?.(DefleetedData);
                //setExtensions?.(extensions);
                navigate('/dashboard');
            }
        } catch (error) {
            console.error('Failed to fetch data:', error);
        }
    };
    useEffect(() => {
        const authToken = authContext?.authUser?.data?.authorization; // Replace with actual token
        fetchSuppliers(authToken)
            .then((res: any) => {
                setRetailers(res);
            })
            .catch((err: any) => {
                console.log(err);
            });
    }, []);

    return (
        <div
            className="splash"
            id="retailerSelect"
        >
            <div
                className="container"
                style={{ padding: 0 }}
            >
                <div className="header">
                    <img
                        src={'../../../assets/images/retailer-app-logo.png'}
                        alt=""
                    />{' '}
                </div>
                <div className="fields">
                    <div>
                        <span className="select_box">
                            <label>Login as</label>

                            <input
                                type="text"
                                className="typeahead"
                                role="combobox"
                                aria-expanded={isDropdownOpen}
                                value={inputValue}
                                onChange={handleInputChange}
                                onFocus={() => setIsDropdownOpen(inputValue.length >= 1)}
                                data-testid="retailer-select-input"
                            />

                            {isDropdownOpen && (
                                <ul
                                    id="supplierList"
                                    className="dropdown-menu typeahead"
                                    role="listbox"
                                    style={{ top: '310px', left: '110px', display: 'none1', width: '342px' }}
                                >
                                    {filteredRetailers.map((country, index) => (
                                        <li
                                            key={index}
                                            className="dropdown-item"
                                            tabIndex={index}
                                            data-testid="retailer-select-item"
                                            onClick={(event) => handleSelectRetailer(event, country)}
                                            data-hs-combo-box-output-item=""
                                        >
                                            <a
                                                className="dropdown-item"
                                                href="#"
                                                role="option"
                                            >
                                                {country.name.split(new RegExp(`(${inputValue})`, 'gi')).map((part, index) =>
                                                    part.toLowerCase() === inputValue.toLowerCase() ? (
                                                        <b
                                                            className="font-black "
                                                            key={index}
                                                        >
                                                            {part}
                                                        </b>
                                                    ) : (
                                                        part
                                                    )
                                                )}
                                                <br />
                                                <span className="discreet">{country.address}</span>
                                            </a>
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </span>

                        <span>
                            <input
                                name="Login"
                                type="button"
                                onClick={async (e) => {
                                    const button = e.currentTarget;
                                    button.disabled = true;
                                    button.value = 'Loading...';
                                    await redirectHome();
                                    button.disabled = false;
                                    button.value = 'Login';
                                }}
                                value="Login"
                            />
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
};
