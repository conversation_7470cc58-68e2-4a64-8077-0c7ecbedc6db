'use strict';

const { Vehicle } = require('@aa/data-models/common');
var _ = require('lodash'),
    logger = require('winston'),
    VehicleTypeFactory = require('./vehicle-type.factory');

function _getValue(raw) {
    return raw[0]['$'] === undefined ? raw[0] : null;
}

function _getInt(raw) {
    return raw[0]['$'] === undefined ? raw[0] >> 0 : 0;
}

function parseRegistrationDate(dateVal) {
    if (dateVal) {
        return dateVal.substring(0, 10).replace(/-/g, ''); //change date to from YYYY-MM-DD to YYYYMMDD
    }

    return null;
}

function mb01VehicleRegistration(raw, experianDetails) {
    //		_getValue(raw.ABI),

    /*	_getValue(raw.DOORPLANCODE),
		_getInt(raw.GROSSWEIGHT)));
		experianDetails.typeId(VehicleTypeFactory.typeId(experianDetails.type()));
	*/

    experianDetails.make(_getValue(raw.MAKE));
    experianDetails.model(_getValue(raw.MODEL));
    experianDetails.colour(_getValue(raw.COLOUR));

    experianDetails.flag(true);
    experianDetails.wrongDetailsFlag(false);

    experianDetails.transmission(_getValue(raw.TRANSMISSIONCODE));

    experianDetails.doorPlanCode(_getValue(raw.DOORPLAN));
    experianDetails.doorPlan(_getValue(raw.DOORPLANLITERAL));

    experianDetails.grossWeight(_getValue(raw.GROSSWEIGHT));

    experianDetails.dateFirstRegistered(parseRegistrationDate(_getValue(raw.DATEFIRSTREGISTERED)));
    experianDetails.ukDateFirstRegistered(parseRegistrationDate(_getValue(raw.FIRSTREGISTERED)));

    experianDetails.gears(_getValue(raw.GEARS));
    experianDetails.importNonEu(_getInt(raw.IMPORTED));

    experianDetails.vin(_getValue(raw.VIN));

    experianDetails.vinConfirmFlag(_getInt(raw.VINCONFERMATIONFLAG));

    experianDetails.engineSize(_getValue(raw.ENGINECAPACITY));
    experianDetails.yrOfManufacture(_getValue(raw.YEAROFMANUFACTURE));

    experianDetails.fuel(_getValue(raw.FUEL));
    experianDetails.wheelPlan(_getValue(raw.WHEELPLAN));
    experianDetails.imported(_getInt(raw.IMPORTED) === 1);
    experianDetails.importNonEu(_getInt(raw.IMPORTNONEU) === 1);
    experianDetails.abiBrokernetCode(_getValue(raw.ABIBROKERNETCODE));
    experianDetails.engineNo(_getValue(raw.ENGINENUMBER));
    experianDetails.co2Emissions(_getValue(raw.CO2EMISSIONS));

    return experianDetails;
}

function rbCodes(raw, experianDetails) {
    var model = {
            message: _getValue(raw.Message),
            rbcodecount: _getInt(raw.RBCODECOUNT),
            rbcodes: []
        },
        rbcodedetails = _getValue(raw.RBCODEDETAILS),
        rbcodeDetail = null;

    if (model.rbcodecount > 0) {
        if (rbcodedetails) {
            rbcodeDetail = _getValue(rbcodedetails.RBCodeDetail);
        }

        if (rbcodeDetail) {
            _.forEach(rbcodeDetail.RBCode, function (rbcode) {
                model.rbcodes.push(rbcode);
            });
            experianDetails.rbCodes(_.join(model.rbcodes, ':'));
        }
    }

    return experianDetails;
}

function mb33DVLASMMT(raw, experianDetails) {
    experianDetails.mvrisCode(_getValue(raw.MB33_SMMTMVRISMAKECODE) + ' ' + _getValue(raw.MB33_SMMTMVRISMODELCODE));
    experianDetails.smmtRange(_getValue(raw.MB33_SMMTRANGE));

    // the following are not used but keep them just in case

    //	mb33DVLADTPMAKECODE: _getValue(raw.MB33_DVLADTPMAKECODE),
    //	mb33DVLADTPMODELCODE: _getValue(raw.MB33_DVLADTPMODELCODE),
    //	mb33DVLAMAKE: _getValue(raw.MB33_DVLAMAKE),
    //	mb33DVLAMODEL: _getValue(raw.MB33_DVLAMODEL),
    //	mb33SMMTMAKE: _getValue(raw.MB33_SMMTMAKE),
    //	mb33SMMTMVRISMAKECODE: _getValue(raw.MB33_SMMTMVRISMAKECODE),
    //	mb33SMMTTRIM: _getValue(raw.MB33_SMMTTRIM)
    return experianDetails;
}

function mbabadditionalSMMTData(raw, experianDetails) {
    experianDetails.bodyStyle(_getValue(raw.MBAB_BODY_STYLE));
    experianDetails.modelVariant(_getValue(raw.MBAB_MODEL_VARIANT));
    experianDetails.series(_getValue(raw.MBAB_SERIES));
    experianDetails.driveType(_getValue(raw.MBAB_DRIVE_TYPE));
    // the following properties are not use but keep them for referrence

    //	mbabcabtype: _getValue(raw.MBAB_CAB_TYPE),
    //	mbabcountryoforigin: _getValue(raw.MBAB_COUNTRY_OF_ORIGIN),
    //	mbabdrivetype: _getValue(raw.MBAB_DRIVE_TYPE),
    //	mbabenginecapacity: _getValue(raw.MBAB_ENGINE_CAPACITY),
    //	mbabfueltype: _getValue(raw.MBAB_FUEL_TYPE),
    //	mbabmarket_sector_code: _getValue(raw.MBAB_MARKET_SECTOR_CODE),
    //	mbabmarque: _getValue(raw.MBAB_MARQUE),
    //	mbabmarque_code: _getValue(raw.MBAB_MARQUE_CODE),
    //	mbabmodelcode: _getValue(raw.MBAB_MODEL_CODE),
    //	mbabnominalenginecapacity: _getValue(raw.MBAB_NOMINAL_ENGINE_CAPACITY),
    //	mbabnumofdoors: _getValue(raw.MBAB_NUM_OF_DOORS),
    //	mbabnumofgears: _getValue(raw.MBAB_NUM_OF_GEARS),
    //	mbabrange: _getValue(raw.MBAB_RANGE),
    //	mbabsyssetupdate: _getValue(raw.MBAB_SYS_SETUP_DATE),
    //	mbabterminate_date: _getValue(raw.MBAB_TERMINATE_DATE),
    //	mbabtransmission: _getValue(raw.MBAB_TRANSMISSION),
    //	mbabversionnumber: _getValue(raw.MBAB_VERSIONNUMBER),
    //	mbabvisiblitydate: _getValue(raw.MBAB_VISIBLITY_DATE),
    return experianDetails;
}

function mbaeweightAndDimensionData(raw, experianDetails) {
    experianDetails.length(_getValue(raw.MBAE_CAR_LENGTH));
    experianDetails.kerbWeight(_getValue(raw.MBAE_KERB_WEIGHT));
    experianDetails.seatNumber(_getValue(raw.MBAE_NUM_OF_SEATS));
    experianDetails.height(_getValue(raw.MBAE_HEIGHT));
    experianDetails.width(_getValue(raw.MBAE_WIDTH));
    experianDetails.length(_getValue(raw.MBAE_CAR_LENGTH));
    // these properties are not used but keep them for reference

    // mbaebodyshape: _getValue(raw.MBAE_BODY_SHAPE),
    // mbaegrosscombinedweight: _getValue(raw.MBAE_GROSS_COMBINED_WEIGHT),
    // mbaegrosstrainweight: _getValue(raw.MBAE_GROSS_TRAIN_WEIGHT),
    // mbaegrossvehicleweight: _getValue(raw.MBAE_GROSS_VEHICLE_WEIGHT),
    // mbaeheight: _getValue(raw.MBAE_HEIGHT),
    // mbaeloadlength: _getValue(raw.MBAE_LOAD_LENGTH),
    // mbaenumofaxles: _getValue(raw.MBAE_NUM_OF_AXLES),
    // mbaenumofseats: _getValue(raw.MBAE_NUM_OF_SEATS),
    // mbaepayloadvolume: _getValue(raw.MBAE_PAYLOAD_VOLUME),
    // mbaepayloadweight: _getValue(raw.MBAE_PAYLOAD_WEIGHT),
    // mbaerigidartic: _getValue(raw.MBAE_RIGID_ARTIC),
    // mbaeunladenweight: _getValue(raw.MBAE_UNLADEN_WEIGHT),
    // mbaeversionnumber: _getValue(raw.MBAE_VERSIONNUMBER),
    // mbaewheelbase: _getValue(raw.MBAE_WHEEL_BASE),
    // mbaewidth: _getValue(raw.MBAE_WIDTH),

    return experianDetails;
}

module.exports = {
    parse: function parse(raw) {
        var model = {
                message: _getValue(raw.ErrorMessage),
                errored: _getValue(raw.Errored),
                mb01VehicleRegistration: null,
                mb33DVLASMMT: null,
                mbabadditionalSMMTData: null,
                mbaeweightAndDimensionData: null,
                requestId: _getValue(raw.RequestID),
                rbcodes: null
            },
            property,
            vehicleType,
            vModel = null;

        if (model.errored === 'true') {
            logger.error('MalstromVehicleFactory.parse :: request to experian failed ::', raw);
            return null;
        }

        // to get here we assume that we have a valid response and start processing
        property = _getValue(raw.MB01_VehicleRegistration);
        if (property) {
            vModel = new Vehicle();
            vModel.registration(_getValue(property.VRM));
            vModel.colour(_getValue(property.COLOUR));
            vModel.model().fuel(_getValue(property.FUEL));
            vModel.model().transmission(_getValue(property.TRANSMISSIONCODE));

            vehicleType = VehicleTypeFactory.mapExperianVehicleType(_getValue(property.DOORPLANCODE), _getInt(property.GROSSWEIGHT));
            vModel.typeId(VehicleTypeFactory.typeId(vehicleType));

            mb01VehicleRegistration(property, vModel.experianDetails());

            property = _getValue(raw.MB33_DVLASMMT);
            if (property) {
                mb33DVLASMMT(property, vModel.experianDetails());
            } else {
                logger.warn('MalstromVehicleFactory.parse:: MB33_DVLASMMT missing from response', raw);
            }

            property = _getValue(raw.MBAB_AdditionalSMMTData);
            if (property) {
                mbabadditionalSMMTData(property, vModel.experianDetails());
            } else {
                logger.warn('MalstromVehicleFactory.parse:: MBAB_AdditionalSMMTData missing from response', raw);
            }

            property = _getValue(raw.MBAE_WeightAndDimensionData);
            if (property) {
                mbaeweightAndDimensionData(property, vModel.experianDetails());
            } else {
                logger.warn('MalstromVehicleFactory.parse:: mbaeweightAndDimensionData missing from response', raw);
            }

            property = _getValue(raw.RBCodes);
            if (property) {
                rbCodes(property, vModel.experianDetails());
            } // no warn as these are optional
        } else {
            logger.error('MalstromVehicleFactory.parse :: MB01_VehicleRegistration is mssing from response :: ', raw);
        }

        return vModel;
    }
};
