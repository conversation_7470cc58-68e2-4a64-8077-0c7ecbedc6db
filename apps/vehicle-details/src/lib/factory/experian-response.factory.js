'use strict';
var _ = require('lodash');
const { Vehicle } = require('@aa/data-models/common');
var VehicleTypeFactory = require('./vehicle-type.factory');

function parseRegistrationDate(dateVal) {
    if (dateVal) {
        return dateVal.substring(0, 10).replace(/-/g, ''); //change date to from YYYY-MM-DD to YYYYMMDD
    }

    return null;
}

function parse(raw) {
    var vehDetails = {},
        mb01VehicleRegistration = raw.mb01VehicleRegistration,
        mbabadditionalSMMTData = raw.mbabadditionalSMMTData,
        mbaeweightAndDimensionData = raw.mbaeweightAndDimensionData,
        mb33DVLASMMT = raw.mb33DVLASMMT,
        rbCodes = raw.rbcodes;

    // for a valid response mb01VehicleRegistration should always be there
    if (mb01VehicleRegistration) {
        vehDetails.regNo = mb01VehicleRegistration.vrm;

        vehDetails.type = VehicleTypeFactory.mapExperianVehicleType(mb01VehicleRegistration.doorplancode, mb01VehicleRegistration.grossweight);
        vehDetails.typeId = VehicleTypeFactory.typeId(vehDetails.type);

        vehDetails.make = mb01VehicleRegistration.make;
        vehDetails.model = mb01VehicleRegistration.model;

        vehDetails.colour = mb01VehicleRegistration.colour;

        vehDetails.flag = true;
        vehDetails.wrongDetailsFlag = false;

        vehDetails.transmission = mb01VehicleRegistration.transmissioncode;

        vehDetails.doorPlanCode = mb01VehicleRegistration.doorplan; // this contains the code 'int' ...
        vehDetails.doorPlan = mb01VehicleRegistration.doorplanliteral; // and this contains the string representation ...

        vehDetails.grossWeight = mb01VehicleRegistration.grossweight.toString();

        vehDetails.dateFirstRegistered = parseRegistrationDate(mb01VehicleRegistration.datefirstregistered);
        vehDetails.ukDateFirstRegistered = parseRegistrationDate(mb01VehicleRegistration.firstregistered);

        vehDetails.gears = mb01VehicleRegistration.gears.toString();
        vehDetails.importNonEu = mb01VehicleRegistration.importnoneu === 1;

        vehDetails.vin = mb01VehicleRegistration.vin;
        vehDetails.vinConfirmFlag = mb01VehicleRegistration.vinconfermationflag === 1;
        vehDetails.engineSize = mb01VehicleRegistration.enginecapacity.toString();
        vehDetails.yrOfManufacture = mb01VehicleRegistration.yearofmanufacture ? mb01VehicleRegistration.yearofmanufacture.toString() : '';
        vehDetails.fuel = mb01VehicleRegistration.fuel;
        vehDetails.wheelPlan = mb01VehicleRegistration.wheelplan;
        vehDetails.imported = mb01VehicleRegistration.imported === 1;

        vehDetails.abiBrokernetCode = mb01VehicleRegistration.abibrokernetcode;
        vehDetails.engineNo = mb01VehicleRegistration.enginenumber;
        vehDetails.co2Emissions = mb01VehicleRegistration.co2EMISSIONS;

        vehDetails.makeModel = mb01VehicleRegistration.makemodel;

        if (mbabadditionalSMMTData) {
            vehDetails.bodyStyle = mbabadditionalSMMTData.mbabbodystyle;
            vehDetails.modelVariant = mbabadditionalSMMTData.mbabmodelvariant;
            vehDetails.series = mbabadditionalSMMTData.mbabseries;
        }

        if (mb33DVLASMMT) {
            vehDetails.smmtRange = mb33DVLASMMT.mb33SMMTRANGE;
            vehDetails.mvrisCode = mb33DVLASMMT.mb33SMMTMVRISMAKECODE + ' ' + mb33DVLASMMT.mb33SMMTMVRISMODELCODE;
        }

        if (rbCodes && rbCodes.rbcodecount > 0 && rbCodes.rbcodedetails.rbcodeDetail.length > 0) {
            vehDetails.rbCodes = '';
            _.forEach(rbCodes.rbcodedetails.rbcodeDetail, function (item, idx) {
                if (idx > 0) {
                    vehDetails.rbCodes += ':';
                }
                vehDetails.rbCodes += item.rbcode;
            });
        }

        //vehDetails.glassesModelId = null; //this is MID which comes from a different system

        if (mbaeweightAndDimensionData) {
            vehDetails.kerbWeight = mbaeweightAndDimensionData.mbaekerbweight ? mbaeweightAndDimensionData.mbaekerbweight.toString() : null;
            vehDetails.length = mbaeweightAndDimensionData.mbaecarlength ? mbaeweightAndDimensionData.mbaecarlength.toString() : null;
        }
    }

    return new Vehicle({
        registration: vehDetails.regNo,
        colour: vehDetails.colour,
        modelId: 1212,
        typeId: vehDetails.typeId,
        experianDetails: vehDetails
    });
}

module.exports = {
    parse: parse
};
