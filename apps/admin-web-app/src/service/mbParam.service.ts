import axios from 'axios';
import { ApiResponse, MBParamInsertInterface, MBParamResponseInterface, MBParamUpdateInterface } from '../model';
import { HttpMethod } from '@aa/http-client';
import { useHttpClient } from '@aa/ui/hooks/use-http-client';

export const useMbParamService = () => {
    const [httpClient] = useHttpClient();
    const MBPARAM_API_URL = '/api/admin-api/mb-params';

    const insert = async (data: MBParamInsertInterface): Promise<MBParamInsertInterface> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${MBPARAM_API_URL}/insert`,
                method: HttpMethod.POST,
                body: data
            });
            return response.body;
        } catch (error) {
            console.error('Error inserting mbparam data:', error);
            throw error;
        }
    };

    const fetchAll = async (customerGroup: string | null): Promise<ApiResponse<MBParamResponseInterface[]>> => {
        try {
            let url = MBPARAM_API_URL;
            if (customerGroup) {
                url = `${MBPARAM_API_URL}?custGrp=${customerGroup}`;
            }
            const response: any = await httpClient.fetch({
                url: url,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching mbparam data:', error);
            throw error;
        }
    };

    const update = async (data: MBParamUpdateInterface): Promise<string> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${MBPARAM_API_URL}/update`,
                method: HttpMethod.POST,
                body: data
            });
            return response.body;
        } catch (error) {
            console.error('Error updating mbparam data:', error);
            throw error;
        }
    };

    return { insert, fetchAll, update };
};
