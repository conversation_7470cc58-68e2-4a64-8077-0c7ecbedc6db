import { httpWrapper } from '../helpers';

// Define types for the request and response data structures
interface HireVehicleClass {
    mbHireVehicleClassId: number;
    hireSupplierCode: string;
    custGroupCode: string;
    vehicleMakeId: number;
    vehicleModelId: number;
    sameMake: string;
    hireVehicleClass: string;
    classEffDate: string;
    classIneffDate: string;
    transmissionType: string;
}

interface GetParams {
    [key: string]: any; // You can be more specific with this if you know the expected parameters
}

interface CreateOrUpdateData {
    name: string;
    // Add other properties based on your data structure for creating/updating
}

const baseUrl = '/api/hire-vehicle-class-service';

export const HireVehicleClassService = {
    getAll,
    get,
    create,
    update
};

// Function to get all Hire Vehicle Classes
function getAll(params: string): Promise<HireVehicleClass[]> {
    let url = `${baseUrl}/allHireVehicleClasses`;
    if (params) {
        url += `?${params}`;
    }
    return httpWrapper.get(url, {});
}

// Function to get a specific Hire Vehicle Class with parameters
function get(params: GetParams): Promise<HireVehicleClass> {
    return httpWrapper.get(`${baseUrl}/hireVehicleClass`, params);
}

// Function to create a new Hire Vehicle Class
function create(data: CreateOrUpdateData): Promise<HireVehicleClass> {
    return httpWrapper.post(`${baseUrl}/hireVehicleClass`, data);
}

// Function to update an existing Hire Vehicle Class
function update(data: any): Promise<any> {
    return httpWrapper.put(`${baseUrl}/hireVehicleClass`, data);
}
