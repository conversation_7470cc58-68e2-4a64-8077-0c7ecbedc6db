import { httpWrapper } from '../helpers';

const baseUrl = '/api/reference-data-service/refData';

// Define interfaces for the response types if you know the structure
interface CustomerGroup {
    id: number;
    name: string;
}

interface VehicleMake {
    id: number;
    name: string;
}

interface VehicleModel {
    id: number;
    name: string;
}

// The data for these variables could be arrays or objects based on the API response
let _customerGroups: CustomerGroup[] | undefined;
let _vehicleMakes: VehicleMake[] | undefined;
let _vehicleModels: VehicleModel[] | undefined;

export const ReferenceService = {
    getCustomerGroups,
    getVehicleMakes,
    getVehicleModelsByMake
};

function getCustomerGroups(): Promise<CustomerGroup[] | undefined> {
    if (_customerGroups) {
        return Promise.resolve(_customerGroups);
    }
    return httpWrapper.getWithoutHeaders(`${baseUrl}/customerGroups`, {}).then((res) => {
        _customerGroups = res;
        return _customerGroups;
    });
}

function getVehicleMakes(): Promise<VehicleMake[] | undefined> {
    if (_vehicleMakes) {
        return Promise.resolve(_vehicleMakes);
    }
    return httpWrapper.getWithoutHeaders(`${baseUrl}/vehicleMakes`, {}).then((res) => {
        _vehicleMakes = res;
        return _vehicleMakes;
    });
}

function getVehicleModelsByMake(): Promise<VehicleModel[] | undefined> {
    if (_vehicleModels) {
        return Promise.resolve(_vehicleModels);
    }
    return httpWrapper.post(`${baseUrl}/vehicleModels`, {}).then((res) => {
        _vehicleModels = res;
        return _vehicleModels;
    });
}
