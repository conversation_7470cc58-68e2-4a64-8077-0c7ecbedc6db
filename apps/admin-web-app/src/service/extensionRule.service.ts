import axios from 'axios';
import { ExtensionRule, ApiResponse, ExtensionRuleEditInterface } from '../model';
import { HttpMethod } from '@aa/http-client';
import { useHttpClient } from '@aa/ui/hooks/use-http-client';

export const useExtentionRuleService = () => {
    const [httpClient] = useHttpClient();
    const API_URL = '/api/admin-api/extension-rules';

    const fetchExtensionRulesByCustomerGroup = async (customerGroup: string): Promise<ApiResponse<ExtensionRule[]>> => {
        try {
            const finalApiUrl = `${API_URL}?custGrp=${customerGroup}`;
            const response: any = await httpClient.fetch({
                url: finalApiUrl,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error(`Error while fetching extension rules for customer group: ${customerGroup}, Error: ${error}`);
            throw error;
        }
    };

    const insertExtentionRule = async (data: any): Promise<string> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${API_URL}/insert`,
                method: HttpMethod.POST,
                body: data
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching data:', error);
            throw error;
        }
    };

    const updateExtensionRule = async (exrId: number, updateBody: ExtensionRuleEditInterface): Promise<ApiResponse<string>> => {
        try {
            const finalApiUrl = `${API_URL}/updateExtensionRules`;
            const reqBody = {
                ...updateBody,
                ruleEffDate: updateBody.ruleEffDate ? updateBody.ruleEffDate : null,
                ruleIneffDate: updateBody.ruleIneffDate ? updateBody.ruleIneffDate : null,
                exrId
            };
            const response: any = await httpClient.fetch({
                url: finalApiUrl,
                method: HttpMethod.POST,
                body: reqBody
            });
            return response.body;
        } catch (error) {
            console.error(`Error while updating extension rule: ${exrId}, Error: ${error}`);
            throw error;
        }
    };

    return {
        fetchExtensionRulesByCustomerGroup,
        insertExtentionRule,
        updateExtensionRule
    };
};
