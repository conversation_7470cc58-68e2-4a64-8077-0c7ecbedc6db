import axios from 'axios';
import { ApproverGroupsInterface, CustomerGroup, RequesterGroupsInterface, SupplierNetwork } from '../model';
import { HttpMethod } from '@aa/http-client';
import { useHttpClient } from '@aa/ui/hooks/use-http-client';

export const useSupplierService = () => {
    const API_URL = '/api/supplier-service';
    const [httpClient] = useHttpClient();

    const fetchSupplierNetworks = async (): Promise<SupplierNetwork[]> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${API_URL}/bolt-suppliers`,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error(`Error while fetching supplier networks, Error: ${error}`);
            throw error;
        }
    };

    const fetchRequesterGroups = async (): Promise<RequesterGroupsInterface[]> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${API_URL}/mb-params/requesterGroups`,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching data:', error);
            throw error;
        }
    };

    const fetchApproverGroups = async (): Promise<ApproverGroupsInterface[]> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${API_URL}/mb-params/approverGroups`,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching data:', error);
            throw error;
        }
    };

    const fetchMbParamGroups = async (): Promise<string[]> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${API_URL}/mb-params/paramGroups`,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching data:', error);
            throw error;
        }
    };

    const fetchRetailerCSHRecords = async (supplierNetworkCode: string | undefined): Promise<any> => {
        try {
            if (supplierNetworkCode) {
                const response: any = await httpClient.fetch({
                    url: `${API_URL}/bolt/suppliers/${supplierNetworkCode}`,
                    method: HttpMethod.GET
                });
                return response.body;
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            throw error;
        }
    };

    const updateRetailerCSHRecords = async (data: any): Promise<any> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${API_URL}/bolt/supplier`,
                method: HttpMethod.PATCH,
                body: data
            });
            return response.body;
        } catch (error) {
            console.error('Error inserting retailer data:', error);
            throw error;
        }
    };

    const fetchCustomerGroups = async (): Promise<CustomerGroup[]> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${API_URL}/bolt-customer-grps`,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching data:', error);
            throw error;
        }
    };

    return {
        fetchSupplierNetworks,
        fetchRequesterGroups,
        fetchApproverGroups,
        fetchMbParamGroups,
        fetchRetailerCSHRecords,
        updateRetailerCSHRecords,
        fetchCustomerGroups
    };
};
