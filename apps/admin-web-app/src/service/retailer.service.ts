import axios from 'axios';

import { VehicleData, SupplierData } from '@aa/admin-helpers';
import { ApiResponse, BulkUploadInterface, SupplierInsertInterface, TableRow } from '../model';
import { HttpMethod } from '@aa/http-client';
import { useHttpClient } from '@aa/ui/hooks/use-http-client';

export const useRetailerService = () => {
    const [httpClient] = useHttpClient();
    const RETAILER_BULK_TEMP_INSERT_API_URL = '/api/admin-api/retailers/upload/generateSnapshot';
    const RETAILER_TEMP_INSERT_API_URL = '/api/admin-api/retailers/record/upsert';
    const RETAILER_TEMP_VIEW_API_URL = '/api/admin-api/retailers/upload/getSnapshot/';
    const RETAILER_LIST_SNAPSHOT_URL = '/api/admin-api/retailers/listSnapshots';
    const RETAILER_UPLOAD_TO_CSH_URL = '/api/admin-api/retailers/upload/uploadSnapshot/';

    const insertRetailers = async (data: BulkUploadInterface): Promise<BulkUploadInterface> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${RETAILER_BULK_TEMP_INSERT_API_URL}`,
                method: HttpMethod.POST,
                body: data,
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.body;
        } catch (error) {
            console.error('Error inserting retailer data:', error);
            throw error;
        }
    };

    const insertRetailer = async (data: SupplierInsertInterface): Promise<SupplierInsertInterface> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${RETAILER_TEMP_INSERT_API_URL}`,
                method: HttpMethod.POST,
                body: data
            });
            return response.body;
        } catch (error) {
            console.error('Error inserting retailer data:', error);
            throw error;
        }
    };

    const insertRetailersToCSH = async (snapshotId: string): Promise<string> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${RETAILER_UPLOAD_TO_CSH_URL}` + snapshotId,
                method: HttpMethod.POST,
                body: { snapshotId }
            });
            return response.body;
        } catch (error) {
            console.error('Error inserting retailer data:', error);
            throw error;
        }
    };

    const viewRetailers = async (id: string): Promise<ApiResponse<TableRow<SupplierData>[]>> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${RETAILER_TEMP_VIEW_API_URL}` + id,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching retailer data:', error);
            throw error;
        }
    };

    const listSnapshots = async (): Promise<{ _id: string; timestamp: string }[]> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${RETAILER_LIST_SNAPSHOT_URL}`,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching vehicle snapshot data:', error);
            throw error;
        }
    };

    return {
        insertRetailers,
        insertRetailer,
        insertRetailersToCSH,
        viewRetailers,
        listSnapshots
    };
};
