import axios from 'axios';
import { Poi, PoiAddResponseInterface, PoiUpdateResponseInterface } from '../model';
import { HttpMethod } from '@aa/http-client';
import { useHttpClient } from '@aa/ui/hooks/use-http-client';

export const usePoiService = () => {
    const [httpClient] = useHttpClient();
    const API_URL = '/api/mapping-service/search/pois';

    const fetchPoisByCatgeory = async (categoryId: string): Promise<Poi[]> => {
        try {
            const finalApiUrl = `${API_URL}?categoryId=${categoryId}`;
            const response: any = await httpClient.fetch({
                url: finalApiUrl,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error(`Error while fetching pois for category: ${categoryId}, Error: ${error}`);
            throw error;
        }
    };

    const insertBulkPois = async (categoryId: number, supNetworkCode: string): Promise<PoiAddResponseInterface> => {
        try {
            const finalApiUrl = `${API_URL}/upsert`;
            const response: any = await httpClient.fetch({
                url: finalApiUrl,
                method: HttpMethod.POST,
                body: { categoryId, supNetworkCode, supResourceId: 0 }
            });
            return response.body;
        } catch (error) {
            console.error(`Error while inserting bulk pois for category: ${categoryId} and supNetworkCode: ${supNetworkCode}, Error: ${error}`);
            throw error;
        }
    };

    const updateSinglePoi = async (categoryId: number, supResourceId: number): Promise<PoiUpdateResponseInterface> => {
        try {
            const finalApiUrl = `${API_URL}/upsert`;
            const response: any = await httpClient.fetch({
                url: finalApiUrl,
                method: HttpMethod.POST,
                body: { categoryId, supResourceId, supNetworkCode: 'HYUA' }
            });
            return response.body;
        } catch (error) {
            console.error(`Error while updating single poi for category: ${categoryId} and supResourceId: ${supResourceId}, Error: ${error}`);
            throw error;
        }
    };

    return {
        fetchPoisByCatgeory,
        insertBulkPois,
        updateSinglePoi
    };
};
