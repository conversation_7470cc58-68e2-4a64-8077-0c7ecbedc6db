import axios from 'axios';
import { SelectedCatgory, SelectedCatgoryResponse } from '../model';
import { HttpMethod } from '@aa/http-client';
import { useHttpClient } from '@aa/ui/hooks/use-http-client';

export const useCategoryService = () => {
    const [httpClient] = useHttpClient();
    const API_URL = '/api/mapping-service/selectedCategories';

    const fetchAllSelectedCategories = async (): Promise<SelectedCatgory[]> => {
        try {
            const response: any = await httpClient.fetch({
                url: API_URL,
                method: HttpMethod.GET
            });
            return response.body.map((cat: SelectedCatgoryResponse) => ({
                ...cat,
                categoryId: cat['category-id']
            }));
        } catch (error) {
            console.error('Error while fetching all categories:', error);
            throw error;
        }
    };
    return {
        fetchAllSelectedCategories
    };
};
