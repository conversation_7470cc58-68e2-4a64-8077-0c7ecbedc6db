import axios from 'axios';

import { VehicleData, VehicleMongoInsertInterface } from '@aa/admin-helpers';
import { CshRecordInterface, ApiResponse, TableRow, BulkUploadInterface } from '../model';
import { useHttpClient } from '@aa/ui/hooks/use-http-client';
import { HttpMethod } from '@aa/http-client';

export const useVehicleService = () => {
    const VEHICLE_TEMP_BULK_INSERT_API_URL = '/api/admin-api/vehicles/upload/generateSnapshot';
    const VEHICLE_TEMP_INSERT_API_URL = '/api/admin-api/vehicles/record/upsert';
    const VEHICLE_TEMP_VIEW_API_URL = '/api/admin-api/vehicles/upload/getSnapshot/';
    const VEHICLE_LIST_SNAPSHOT_URL = '/api/admin-api/vehicles/listSnapshots';
    const VEHICLE_CSH_URL = '/api/admin-api/vehicles/csh';
    const VEHICLE_UPLOAD_TO_CSH_URL = '/api/admin-api/vehicles/upload/uploadSnapshot';

    const [httpClient] = useHttpClient();

    const insertTempVehicles = async (custGroup: string, data: BulkUploadInterface): Promise<BulkUploadInterface> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${VEHICLE_TEMP_BULK_INSERT_API_URL}?custGroup=${custGroup}`,
                method: HttpMethod.POST,
                body: data,
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.body;
        } catch (error) {
            console.error('Error inserting vehicle data:', error);
            throw error;
        }
    };

    const upsertTempVehicle = async (data: VehicleMongoInsertInterface): Promise<ApiResponse<{ vehicleRecord: VehicleData }>> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${VEHICLE_TEMP_INSERT_API_URL}`,
                method: HttpMethod.POST,
                body: data
            });
            return response.body;
        } catch (error) {
            console.error('Error inserting retailer data:', error);
            throw error;
        }
    };

    const getTempVehicles = async (id: string): Promise<ApiResponse<TableRow<VehicleData>[]>> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${VEHICLE_TEMP_VIEW_API_URL}` + id,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching vehicles data:', error);
            throw error;
        }
    };

    const insertVehiclesToCSH = async (snapshotId: string): Promise<string> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${VEHICLE_UPLOAD_TO_CSH_URL}`,
                method: HttpMethod.GET,
                body: { snapshotId }
            });
            return response.body;
        } catch (error) {
            console.error('Error inserting vehicle data:', error);
            throw error;
        }
    };

    const listVehicleSnapshots = async (): Promise<{ _id: string; timestamp: string; custGroup: string }[]> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${VEHICLE_LIST_SNAPSHOT_URL}`,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching retailer snapshot data:', error);
            throw error;
        }
    };

    const getCshVehicles = async (supplierNetworkId: number | null): Promise<CshRecordInterface[]> => {
        try {
            let url = VEHICLE_CSH_URL;
            if (supplierNetworkId) {
                url = `${VEHICLE_CSH_URL}?supNetworkId=${supplierNetworkId}`;
            }
            const response: any = await httpClient.fetch({
                url: url,
                method: HttpMethod.GET
            });
            return response.body;
        } catch (error) {
            console.error('Error fetching vehicles data:', error);
            throw error;
        }
    };

    const updateCshVehicle = async (data: any): Promise<string> => {
        try {
            const response: any = await httpClient.fetch({
                url: `${VEHICLE_CSH_URL}`,
                method: HttpMethod.POST,
                body: data
            });
            return response.body;
        } catch (error) {
            console.error('Error inserting retailer data:', error);
            throw error;
        }
    };

    return {
        insertTempVehicles,
        upsertTempVehicle,
        getTempVehicles,
        insertVehiclesToCSH,
        listVehicleSnapshots,
        getCshVehicles,
        updateCshVehicle
    };
};
