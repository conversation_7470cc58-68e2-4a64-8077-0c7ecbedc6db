'use client';

import React, { useState, useEffect } from 'react';
import { HireVehicleClassService } from '../../../service/hire-vehicle.service';
import Select, { components } from 'react-select';
import { Button, Input } from '../../components/ui-component/ui';
import SimpleReactValidator from 'simple-react-validator';
import { Calendar } from '../../components/ui-component/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../../components/ui-component/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '../../lib/utils';
import { toast } from 'sonner';
import { FixedSizeList as List } from 'react-window';
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle } from '../../components/ui-component/ui/dialog';
import { DropdownOptions } from './ViewModify';

interface EditViewProps {
    enterpriseData: any;
    refreshCallback: () => void;
    dropDownOptions: DropdownOptions;
}

const EditView: React.FC<EditViewProps> = ({ enterpriseData, refreshCallback, dropDownOptions }) => {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [validator] = useState(new SimpleReactValidator());
    const [formAttributes, setFormAttributes] = useState({
        mbHireVehicleClassId: enterpriseData.mbHireVehicleClassId,
        hireSupplier: {},
        vehicleMake: {},
        customerGroup: {},
        vehicleModel: {},
        vehicleClass: '',
        classEffectiveDate: '',
        classInEffectiveDate: '',
        isSameMake: false,
        transmissionType: ''
    });
    const [isSubmitted, setIsSubmitted] = useState<boolean>(false);

    const setEditFields = (rowData: any) => {
        HireVehicleClassService.get({ mbHireVehicleClassId: rowData.mbHireVehicleClassId }).then((hireVehicleClass) => {
            setFormAttributes((prev) => ({
                ...prev,
                hireSupplier: hireVehicleClass.hireSupplierCode,
                vehicleMake: dropDownOptions.vehicleMakeOpt.filter((vehicleMake) => vehicleMake?.value === hireVehicleClass.vehicleMakeId)[0],
                customerGroup: dropDownOptions.customerGroupOpt.filter((customerGroup) => customerGroup?.value === hireVehicleClass.custGroupCode)[0],
                vehicleModel: dropDownOptions.vehicleModelOpt.filter((vehicleModel) => vehicleModel?.value === hireVehicleClass.vehicleModelId)[0],
                vehicleClass: hireVehicleClass.hireVehicleClass,
                classEffectiveDate: format(hireVehicleClass.classEffDate, 'yyyy-MM-dd'),
                classInEffectiveDate: format(hireVehicleClass.classIneffDate, 'yyyy-MM-dd'),
                isSameMake: hireVehicleClass.sameMake === 'Y',
                transmissionType: hireVehicleClass.transmissionType
            }));
        });
    };

    useEffect(() => {
        if (isDialogOpen && enterpriseData) {
            setEditFields(enterpriseData);
        }
        validator.showMessages();
    }, [enterpriseData, isDialogOpen]);

    const vehicleModelList = (props: any) => {
        const { selectProps, innerRef, children, ...rest } = props;
        const menuHeight = 400; // Height of the dropdown
        const optionHeight = 35; // Height of each option
        const itemCount = dropDownOptions.vehicleModelOpt.length;

        return (
            <components.MenuList {...props}>
                <List
                    height={menuHeight}
                    itemCount={itemCount}
                    itemSize={optionHeight}
                    width="100%"
                >
                    {({ index, style }) => (
                        <div
                            style={style}
                            onClick={() => handleDropdownChange(dropDownOptions.vehicleModelOpt[index], 'vehicleModel')}
                        >
                            <div
                                className="select-option"
                                key={dropDownOptions.vehicleModelOpt[index]?.value}
                            >
                                <span>{dropDownOptions.vehicleModelOpt[index]?.label}</span>
                            </div>
                        </div>
                    )}
                </List>
            </components.MenuList>
        );
    };

    const editClicked = (): void => {
        setIsDialogOpen(true);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormAttributes((prev) => ({
            ...prev,
            [name]: value
        }));
    };

    const handleDropdownChange = (selectedOption: any, name: string) => {
        setFormAttributes((prev) => ({
            ...prev,
            [name]: selectedOption
        }));
    };

    const handleDateChange = (field: string, date: Date) => {
        const today = new Date();
        if (date === undefined) return;
        if (format(date, 'yyyy-MM-dd') >= format(today, 'yyyy-MM-dd')) {
            setFormAttributes((prev) => ({
                ...prev,
                [field]: date
            }));
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitted(true);
        validator.showMessages();

        const isFormValid = validator.allValid();
        if (!isFormValid) {
            return;
        }

        const payload = {
            classEffDate: format(formAttributes.classEffectiveDate, 'yyyy-MM-dd'),
            classIneffDate: format(formAttributes.classInEffectiveDate, 'yyyy-MM-dd'),
            mbHireVehicleClassId: formAttributes.mbHireVehicleClassId,
            hireSupplierCode: formAttributes.hireSupplier,
            custGroupCode: formAttributes.customerGroup?.value,
            vehicleMakeId: formAttributes.vehicleMake?.value,
            vehicleModelId: formAttributes.vehicleModel?.value,
            hireVehicleClass: formAttributes.vehicleClass,
            sameMake: formAttributes.isSameMake ? 'Y' : 'N'
        };

        HireVehicleClassService.update(payload)
            .then(() => {
                refreshCallback();
                toast.success(`Enterprise record updated successfully!`);
            })
            .catch(() => {
                toast.error(`Something went wrong :-( !`);
            })
            .finally(() => {
                setIsDialogOpen(false);
            });
    };

    return (
        <>
            <Dialog>
                <DialogTrigger asChild>
                    <Button
                        variant="outline"
                        size="sm"
                        className="bg-blue-600 text-white"
                        onClick={editClicked}
                    >
                        Edit
                    </Button>
                </DialogTrigger>
                {isDialogOpen && (
                    <DialogContent className="bg-white text-black">
                        <DialogHeader>
                            <DialogTitle>Edit Record</DialogTitle>
                        </DialogHeader>
                        <div>
                            <form
                                onSubmit={handleSubmit}
                                className="space-y-6"
                            >
                                <div>
                                    <label
                                        htmlFor="param-group"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Customer Group*
                                    </label>
                                    <Select
                                        name="customerGroup"
                                        options={dropDownOptions.customerGroupOpt}
                                        value={formAttributes.customerGroup}
                                        onChange={(selectedOption: any) => handleDropdownChange(selectedOption, 'customerGroup')}
                                        placeholder="Select mb param group"
                                        className="react-select-container"
                                        classNamePrefix="react-select"
                                    />
                                    {isSubmitted && validator.message('customerGroup', formAttributes.customerGroup, 'required')}
                                </div>
                                <div>
                                    <label
                                        htmlFor="param-group"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Vehicle Make*
                                    </label>
                                    <Select
                                        name="vehicleMake"
                                        options={dropDownOptions.vehicleMakeOpt}
                                        value={formAttributes.vehicleMake}
                                        onChange={(selectedOption: any) => handleDropdownChange(selectedOption, 'vehicleMake')}
                                        placeholder="Select mb param group"
                                        className="react-select-container"
                                        classNamePrefix="react-select"
                                    />
                                    {isSubmitted && validator.message('vehicleMake', formAttributes.vehicleMake, 'required')}
                                </div>
                                <div>
                                    <label
                                        htmlFor="param-group"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Vehicle Model*
                                    </label>
                                    <Select
                                        name="vehicleModel"
                                        value={formAttributes.vehicleModel}
                                        cacheOptions
                                        components={{ MenuList: vehicleModelList }}
                                        onChange={handleChange}
                                        placeholder="Select vehicle model"
                                        options={dropDownOptions.vehicleModelOpt}
                                    />
                                    {isSubmitted && validator.message('vehicleModel', formAttributes.vehicleModel, 'required')}
                                </div>
                                <div>
                                    <label
                                        htmlFor="vehicle-class"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Vehicle Class*
                                    </label>
                                    <Input
                                        id="vehicle-class"
                                        name="vehicleClass"
                                        value={formAttributes.vehicleClass}
                                        onChange={handleChange}
                                        placeholder="Enter param code"
                                        className="w-full bg-white border border-gray-300 text-gray-700 focus:ring-2 focus:ring-yellow-500"
                                    />
                                    {isSubmitted && validator.message('vehicleClass', formAttributes.vehicleClass, 'required|max:4')}
                                </div>
                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-gray-700">Class Effective Date*</label>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button
                                                variant="outline"
                                                className={cn(
                                                    'w-[215px] justify-start text-left font-normal bg-white text-gray-700 border border-gray-300 focus:ring-2 focus:ring-yellow-500',
                                                    !formAttributes.classEffectiveDate && 'text-muted-foreground'
                                                )}
                                            >
                                                <CalendarIcon className="mr-2" />
                                                {formAttributes.classEffectiveDate ? <div>{format(formAttributes.classEffectiveDate, 'PPP')}</div> : <span>Pick a date</span>}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto bg-white border border-gray-300 shadow-lg p-0 rounded-lg">
                                            <Calendar
                                                mode="single"
                                                selected={formAttributes.classEffectiveDate || undefined}
                                                onSelect={(date) => handleDateChange('.classEffectiveDate', date as Date)}
                                                initialFocus
                                                // minDate={new Date()}
                                                className="text-gray-700"
                                            />
                                        </PopoverContent>
                                    </Popover>
                                    {isSubmitted &&
                                        formAttributes.classEffectiveDate &&
                                        formAttributes.classInEffectiveDate &&
                                        formAttributes.classEffectiveDate >= formAttributes.classInEffectiveDate && <div className="text-danger mt-2">Start date should be less than end date</div>}
                                </div>
                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-gray-700">Class Ineffective Date*</label>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button
                                                variant="outline"
                                                className={cn(
                                                    'w-[215px] justify-start text-left font-normal bg-white text-gray-700 border border-gray-300 focus:ring-2 focus:ring-yellow-500',
                                                    !formAttributes.classInEffectiveDate && 'text-muted-foreground'
                                                )}
                                            >
                                                <CalendarIcon className="mr-2" />
                                                {formAttributes.classInEffectiveDate ? <div>{format(formAttributes.classInEffectiveDate, 'PPP')}</div> : <span>Pick a date</span>}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto bg-white border border-gray-300 shadow-lg p-0 rounded-lg">
                                            <Calendar
                                                mode="single"
                                                selected={formAttributes.classInEffectiveDate || undefined}
                                                onSelect={(date) => handleDateChange('classInEffectiveDate', date as Date)}
                                                initialFocus
                                                className="text-gray-700"
                                            />
                                        </PopoverContent>
                                    </Popover>
                                </div>
                                <div>
                                    <label
                                        htmlFor="same-make"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Same Make
                                    </label>
                                    <input
                                        type="checkbox"
                                        id="same-make"
                                        name="isSameMake"
                                        checked={formAttributes.isSameMake}
                                        onChange={handleChange}
                                        className="big-checkbox w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                    />
                                    {/* {validator.message('isSameMake', formAttributes.isSameMake, 'required')} */}
                                </div>
                                <div>
                                    <Button
                                        type="submit"
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 w-full"
                                    >
                                        Submit
                                    </Button>
                                </div>
                            </form>
                        </div>
                    </DialogContent>
                )}
            </Dialog>
        </>
    );
};

export default EditView;
