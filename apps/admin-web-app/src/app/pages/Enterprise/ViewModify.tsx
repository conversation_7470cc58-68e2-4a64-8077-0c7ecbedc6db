'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useTable, useSortBy, useGlobalFilter, useFilters, usePagination } from 'react-table';
import { ChevronDown, ChevronUp } from 'lucide-react';
import Select from 'react-select';
import { format } from 'date-fns';
import { Spinner } from '@aa/ui/core/spinner';
import { ReferenceService } from '../../../service/aa-reference.service';
import { Button, Input } from '../../components/ui-component/ui';
import { HireVehicleClassService } from '../../../service';
import EditView from './EditEnterprise';
import { SelectInterface } from '../../../model';

export interface DropdownOptions {
    hireSupplierOpt: SelectInterface[];
    vehicleMakeOpt: SelectInterface[];
    customerGroupOpt: SelectInterface[];
    vehicleModelOpt: SelectInterface[];
}

const hireSupplierOptions = [
    {
        label: 'ENTERPRISE',
        value: 'ENTERPRISE'
    }
];

const initialFormAttributes = {
    hireSupplier: null,
    vehicleMake: null,
    customerGroup: null
};

const EnterpriseView = () => {
    const [formAttributes, setFormAttributes] = useState(initialFormAttributes);
    const [searchValue, setSearchValue] = useState<string>('');
    const [hireVehicleClasses, setHireVehicleClasses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [dropDownOptions, setDropDwownOptions] = useState<DropdownOptions>({
        hireSupplierOpt: hireSupplierOptions,
        vehicleMakeOpt: [],
        customerGroupOpt: [],
        vehicleModelOpt: []
    });

    const submitFilters = () => {
        let queryParams = '';
        if (formAttributes.hireSupplier) {
            queryParams = `hireSupplier=${formAttributes.hireSupplier.value}`;
        }
        if (formAttributes.customerGroup) {
            queryParams = `custGroupCode=${formAttributes.customerGroup.value}`;
        }
        if (formAttributes.vehicleMake) {
            queryParams = `vehicleMakeId=${formAttributes.vehicleMake.value}`;
        }
        setGlobalFilter('');
        setSearchValue('');
        fetchEnterpriseData(queryParams);
    };

    useEffect(() => {
        setLoading(true);
        ReferenceService.getCustomerGroups().then((resp) => {
            const customerGroups = Object.values(resp).map((group: any) => ({ label: group.name, value: group.code }));
            setDropDwownOptions((prev) => ({ ...prev, customerGroupOpt: customerGroups }));
        });
        ReferenceService.getVehicleMakes().then((resp) => {
            const vehicleMakes = Object.values(resp).map((vehMake: any) => ({ label: vehMake.name, value: vehMake.id }));
            setDropDwownOptions((prev) => ({ ...prev, vehicleMakeOpt: vehicleMakes }));
        });
        ReferenceService.getVehicleModelsByMake()
            .then((resp) => {
                setLoading(true);
                const vehicleModels = Object.values(resp).map((vehModel: any) => ({ label: vehModel.name, value: vehModel.id }));
                setDropDwownOptions((prev) => ({ ...prev, vehicleModelOpt: vehicleModels }));
            })
            .catch(() => {})
            .finally(() => setLoading(false));
    }, []);

    const columns = useMemo(() => {
        return [
            {
                Header: 'Id',
                accessor: 'mbHireVehicleClassId'
            },
            {
                Header: 'Hire Supplier',
                accessor: 'hireSupplierCode'
            },
            {
                Header: 'Customer Group',
                accessor: 'custGroupName'
            },
            {
                Header: 'Vehicle Make',
                accessor: 'vehicleMakeName'
            },
            {
                Header: 'Vehicle Model',
                accessor: 'vehicleModelName'
            },
            {
                Header: 'Vehicle Class',
                accessor: 'hireVehicleClass'
            },
            {
                Header: 'Same Make',
                accessor: 'sameMake',
                Cell: ({ value }: any) => (value == 'Y' ? 'Yes' : 'No')
            },
            {
                Header: 'Effective Date',
                accessor: 'classEffDate',
                Cell: ({ value }: any) => (value ? format(new Date(value), 'dd-MM-yyyy') : '--')
            },
            {
                Header: 'Ineffective Date',
                accessor: 'classIneffDate',
                Cell: ({ value }: any) => (value ? format(new Date(value), 'dd-MM-yyyy') : '--')
            },
            {
                Header: 'Actions',
                Cell: ({ row }: any) => (
                    <EditView
                        enterpriseData={row.original}
                        refreshCallback={refreshCallback}
                        dropDownOptions={dropDownOptions}
                    />
                )
            }
        ];
    }, [dropDownOptions]);

    const refreshCallback = () => fetchEnterpriseData('');

    const fetchEnterpriseData = (params: string) => {
        setLoading(true);
        HireVehicleClassService.getAll(params)
            .then((resp) => {
                setHireVehicleClasses(resp);
            })
            .catch((error) => console.error(error))
            .finally(() => setLoading(false));
    };

    useEffect(() => {
        fetchEnterpriseData('');
    }, []);

    const tableInstance = useTable(
        {
            columns,
            data: hireVehicleClasses,
            initialState: { pageIndex: 0, pageSize: 10 }
        },
        useFilters,
        useGlobalFilter,
        useSortBy,
        usePagination
    );

    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        prepareRow,
        setGlobalFilter,
        page,
        canPreviousPage,
        canNextPage,
        pageOptions,
        pageCount,
        gotoPage,
        nextPage,
        previousPage,
        setPageSize,
        state: { pageIndex, pageSize }
    } = tableInstance;

    const handleDropdownChange = (selectedOption: any, name: string) => {
        setFormAttributes((prev) => ({
            ...prev,
            [name]: selectedOption
        }));
    };

    const resetFilters = () => {
        setGlobalFilter('');
        setSearchValue('');
        setFormAttributes(initialFormAttributes);
        fetchEnterpriseData('');
    };

    return (
        <div className="space-y-8 p-4">
            <div>
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold">Enterprise</h2>
                    <div className="grid grid-cols-2 gap-4 mb-3">
                        <Button
                            type="submit"
                            onClick={submitFilters}
                            className="bg-yellow-500 text-white hover:bg-yellow-600 w-full"
                        >
                            Apply
                        </Button>
                        <Button
                            type="submit"
                            onClick={resetFilters}
                            className="bg-yellow-500 text-white hover:bg-yellow-600 w-full"
                        >
                            Reset
                        </Button>
                    </div>
                </div>
                <div className="flex items-center justify-between mb-4">
                    <div className="grid grid-cols-4 gap-4 mb-3">
                        <Select
                            name="hireSupplier"
                            options={dropDownOptions.hireSupplierOpt}
                            value={formAttributes.hireSupplier}
                            onChange={(selectedOption: any) => handleDropdownChange(selectedOption, 'hireSupplier')}
                            placeholder="Select Hire Supplier"
                            className="react-select-container"
                            classNamePrefix="react-select"
                        />
                        <Select
                            name="customerGroup"
                            options={dropDownOptions.customerGroupOpt}
                            value={formAttributes.customerGroup}
                            onChange={(selectedOption: any) => handleDropdownChange(selectedOption, 'customerGroup')}
                            placeholder="Select customer group"
                            className="react-select-container"
                            classNamePrefix="react-select"
                        />
                        <Select
                            name="vehicleMake"
                            options={dropDownOptions.vehicleMakeOpt}
                            value={formAttributes.vehicleMake}
                            onChange={(selectedOption: any) => handleDropdownChange(selectedOption, 'vehicleMake')}
                            placeholder="Select Vehicle Make"
                            className="react-select-container"
                            classNamePrefix="react-select"
                        />
                        <Input
                            placeholder="Search..."
                            onChange={(e) => {
                                setGlobalFilter(e.target.value);
                                setSearchValue(e.target.value);
                            }}
                            // className=" ml-2"
                            value={searchValue}
                        />
                    </div>
                </div>

                <div className="overflow-x-auto bg-white shadow-lg rounded-lg">
                    {loading ? (
                        <div className="text-center">
                            <br />
                            <br />
                            Loading MB Params..
                            <br />
                            <br />
                            Please wait...
                            <Spinner
                                className="mr-3"
                                style={{ display: 'inline' }}
                            />
                            <br />
                            <br />
                            <br />
                        </div>
                    ) : (
                        <>
                            <table
                                {...getTableProps()}
                                className="min-w-full table-auto"
                            >
                                <thead>
                                    {headerGroups.map((headerGroup) => (
                                        <tr
                                            {...headerGroup.getHeaderGroupProps()}
                                            className="border-b"
                                        >
                                            {headerGroup.headers.map((column) => (
                                                <th
                                                    {...column.getHeaderProps(column.getSortByToggleProps())}
                                                    className="py-3 px-4 text-left text-sm font-semibold cursor-pointer text-muted"
                                                >
                                                    {column.render('Header')}
                                                    <span className="ml-1">
                                                        {column.isSorted ? column.isSortedDesc ? <ChevronDown className="inline w-4 h-4" /> : <ChevronUp className="inline w-4 h-4" /> : null}
                                                    </span>
                                                </th>
                                            ))}
                                        </tr>
                                    ))}
                                </thead>
                                <tbody {...getTableBodyProps()}>
                                    {page.map((row) => {
                                        prepareRow(row);
                                        return (
                                            <tr
                                                {...row.getRowProps()}
                                                className="border-b hover:bg-muted/50"
                                            >
                                                {row.cells.map((cell) => (
                                                    <td
                                                        {...cell.getCellProps()}
                                                        className="py-2 px-4 text-sm text-muted"
                                                    >
                                                        {cell.render('Cell')}
                                                    </td>
                                                ))}
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                            {/* Pagination controls */}
                            <div className="flex items-center justify-between p-4">
                                <div>
                                    <Button
                                        onClick={() => gotoPage(0)}
                                        disabled={!canPreviousPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600"
                                    >
                                        {'<<'}
                                    </Button>
                                    <Button
                                        onClick={() => previousPage()}
                                        disabled={!canPreviousPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'<'}
                                    </Button>
                                    <Button
                                        onClick={() => nextPage()}
                                        disabled={!canNextPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'>'}
                                    </Button>
                                    <Button
                                        onClick={() => gotoPage(pageCount - 1)}
                                        disabled={!canNextPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'>>'}
                                    </Button>
                                </div>
                                <div>
                                    <span>
                                        Page&nbsp;
                                        <strong>
                                            {pageIndex + 1} of {pageOptions.length}&nbsp;
                                        </strong>
                                    </span>
                                    <span>
                                        | Go to page:&nbsp;
                                        <input
                                            type="number"
                                            value={pageIndex + 1}
                                            onChange={(e) => {
                                                const page = e.target.value ? Number(e.target.value) - 1 : 0;
                                                gotoPage(page);
                                            }}
                                            style={{ width: '50px' }}
                                            className="border border-input px-1 py-1 text-center"
                                        />
                                    </span>
                                </div>
                                <div>
                                    Rows per page:&nbsp;
                                    <select
                                        value={pageSize}
                                        onChange={(e) => {
                                            setPageSize(Number(e.target.value));
                                        }}
                                        className="border border-input px-3 py-1"
                                    >
                                        {[10, 20, 30, 40, 50].map((pageSize) => (
                                            <option
                                                key={pageSize}
                                                value={pageSize}
                                            >
                                                Show {pageSize}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default EnterpriseView;
