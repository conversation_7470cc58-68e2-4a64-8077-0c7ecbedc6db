'use client';

import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTable, useSortBy, useGlobalFilter, useFilters, usePagination } from 'react-table';
import { ChevronDown, ChevronUp } from 'lucide-react';
import Select from 'react-select';
import { format } from 'date-fns';

import { Spinner } from '@aa/ui/core/spinner';
import { Button, Input } from '../../components/ui-component/ui';
import { useExtentionRuleService, useSupplierService } from '../../../service';
import { SelectInterface, ExtensionRuleListView, ExtensionRule, ExtensionRuleExtraField } from '../../../model';
import { ExtensionRuleEdit } from './ExtensionRuleEdit';

// Columns for Extension Rules Table
const createColumns = (updateCallback: () => void) => [
    {
        Header: 'Cust Grp',
        accessor: 'custGroupCode'
    },
    {
        Header: 'Exr Id',
        accessor: 'exrId'
    },
    // {
    //     Header: 'Sup Network Id',
    //     accessor: 'supNetworkId',
    // },
    {
        Header: 'Description',
        accessor: 'description'
    },
    {
        Header: 'When to Use',
        accessor: 'whentoUse'
    },
    // {
    //     Header: 'Request By PAR ID',
    //     accessor: 'requestByParId',
    // },
    // {
    //     Header: 'Approve By PAR ID',
    //     accessor: 'approveByParId',
    // },
    // {
    //     Header: 'Start Date',
    //     accessor: 'startDate',
    //     Cell: ({ value }) => format(new Date(value), 'dd-MM-yyyy'),
    // },
    // {
    //     Header: 'End Date',
    //     accessor: 'endDate',
    //     Cell: ({ value }) => (value ? format(new Date(value), 'dd-MM-yyyy') : 'N/A'),
    // },
    {
        Header: 'Dates',
        accessor: 'dates',
        Cell: ({ row }) => {
            const fields = [
                { key: 'Start', value: row.original.startDate ? format(new Date(row.original.startDate), 'dd-MM-yyyy') : 'Not Set' },
                { key: 'End', value: row.original.endDate ? format(new Date(row.original.endDate), 'dd-MM-yyyy') : 'Not Set' },
                { key: 'Eff. Start', value: row.original.ruleEffDate ? format(new Date(row.original.ruleEffDate), 'dd-MM-yyyy') : 'Not Set' },
                { key: 'Eff. End', value: row.original.ruleIneffDate ? format(new Date(row.original.ruleIneffDate), 'dd-MM-yyyy') : 'Not Set' }
            ];
            return (
                <>
                    {fields.map((field, idx) => (
                        <div
                            key={field.key}
                            className={'whitespace-nowrap'}
                        >
                            <i>{field.key}</i>: <span className="border-b">{field.value}</span>
                        </div>
                    ))}
                </>
            );
        },
        disableSortBy: true
    },
    {
        Header: 'VOR Days',
        accessor: 'noDaysVOR'
    },
    {
        Header: 'Retailer Fields',
        accessor: 'retailerFields',
        Cell: ({ row }) => {
            const retailerFields = [
                { key: 'Days', value: row.original.noDaysRetailer },
                { key: 'Charge', value: row.original.retailerCharge },
                { key: 'Charge Free Days', value: row.original.retailerChargeFreeDays },
                { key: 'Charge Cost', value: row.original.retailerChargeCost },
                { key: 'Show to Retailer', value: row.original.showToRetailer ? 'Yes' : 'No' }
            ];
            return (
                <>
                    {retailerFields.map((field) => (
                        <div
                            key={field.key}
                            className="flex items-center whitespace-nowrap"
                        >
                            <span>
                                <i>{field.key}</i>:{' '}
                            </span>
                            <span className="ml-1 border-b px-1">{field.value}</span>
                        </div>
                    ))}
                </>
            );
        },
        disableSortBy: true
    },
    {
        Header: 'Extra Fields',
        accessor: 'extraFields',
        Cell: ({ value }) => {
            const extraFields: ExtensionRuleExtraField[] = value ? JSON.parse(value) : [];
            return (
                <>
                    {extraFields.map((field, index) => {
                        const label: string = field.templateOptions && field.templateOptions.label ? field.templateOptions.label : field.key;
                        const typeText: string = field.type;
                        const requiredText: string = field.templateOptions && field.templateOptions.required ? 'Yes' : 'No';
                        let subfields: JSX.Element[] = [];
                        if (field.type === 'list' && field.templateOptions && field.templateOptions.fields) {
                            const fields = field.templateOptions.fields;
                            subfields = fields.map((subField, subIndex) => {
                                const subLabel: string = subField.templateOptions && subField.templateOptions.label ? subField.templateOptions.label : subField.key;
                                const subTypeText: string = subField.type;
                                return (
                                    <span
                                        key={subField.key}
                                        className="whitespace-nowrap"
                                    >
                                        {subIndex + 1} | {subLabel} | {subTypeText} <br />
                                    </span>
                                );
                            });
                        }
                        return (
                            <div
                                key={field.key}
                                className="whitespace-nowrap"
                            >
                                <div>
                                    <i>Field {index + 1}</i>: <span className="border-b px-1">{label}</span>
                                </div>
                                <div>
                                    <i>Type</i>: <span className="border-b px-1">{typeText}</span>
                                </div>
                                <div>
                                    <i>Required</i>: <span className="border-b px-1">{requiredText}</span>
                                </div>
                                {subfields.length > 0 ? (
                                    <>
                                        <div>
                                            <i>List fields</i>:
                                        </div>
                                        <div className="pl-5">{subfields}</div>
                                    </>
                                ) : null}
                                {index < extraFields.length - 1 ? <hr className="mt-3" /> : null}
                            </div>
                        );
                    })}
                </>
            );
        },
        disableSortBy: true
    },
    // {
    //     Header: 'Max Extensions VOR',
    //     accessor: 'maxExtensionsVOR',
    // },
    // {
    //     Header: 'Max Extensions Retailer',
    //     accessor: 'maxExtensionsRetailer',
    // },
    // {
    //     Header: 'Display Order',
    //     accessor: 'displayOrder',
    // },
    // {
    //     Header: 'Eff. Start Date',
    //     accessor: 'ruleEffDate',
    //     Cell: ({ value }) => (value ? format(new Date(value), 'dd-MM-yyyy') : 'N/A'),
    // },
    // {
    //     Header: 'Eff. End Date',
    //     accessor: 'ruleIneffDate',
    //     Cell: ({ value }) => (value ? format(new Date(value), 'dd-MM-yyyy') : 'N/A'),
    // },
    {
        Header: 'Actions',
        accessor: 'actions',
        Cell: ({ row }) => (
            <ExtensionRuleEdit
                record={row.original}
                updateCallback={updateCallback}
            />
        )
    }
];

const ExtensionRulesViewModify = () => {
    const [customerGroupOptions, setCustomerGroupOptions] = useState<SelectInterface[]>([]);
    const [selectedCustomerGroups, setSelectedCustomerGroups] = useState([]);

    const [searchValue, setSearchValue] = useState<string>('');
    const [extensionRulesData, setExtensionRulesData] = useState<ExtensionRuleListView[]>([]);
    const [loading, setLoading] = useState(true);
    const SupplierService = useSupplierService();
    const ExtentionRuleService = useExtentionRuleService();

    const handleCustomerGroupChange = (selectedOptions) => {
        setSearchValue('');
        setGlobalFilter('');
        setSelectedCustomerGroups(selectedOptions);
        fetchExtensionRuleData(selectedOptions);
    };

    const mapExtensionRuleToExtensionRuleListView = (extensionRule: ExtensionRule): ExtensionRuleListView => {
        const tmpStartDate = extensionRule.START_DATE.length > 10 ? extensionRule.START_DATE.substring(0, 10) : extensionRule.START_DATE;
        let tmpEndDate: string | null = null,
            tmpRuleEffDate: string | null = null,
            tmpRuleIneffDate: string | null = null;
        if (extensionRule.END_DATE && extensionRule.END_DATE.length > 0) {
            tmpEndDate = extensionRule.END_DATE.length > 10 ? extensionRule.END_DATE.substring(0, 10) : extensionRule.END_DATE;
        }
        if (extensionRule.RULE_EFF_DATE && extensionRule.RULE_EFF_DATE.length > 0) {
            tmpRuleEffDate = extensionRule.RULE_EFF_DATE.length > 10 ? extensionRule.RULE_EFF_DATE.substring(0, 10) : extensionRule.RULE_EFF_DATE;
        }
        if (extensionRule.RULE_INEFF_DATE && extensionRule.RULE_INEFF_DATE.length > 0) {
            tmpRuleIneffDate = extensionRule.RULE_INEFF_DATE.length > 10 ? extensionRule.RULE_INEFF_DATE.substring(0, 10) : extensionRule.RULE_INEFF_DATE;
        }
        return {
            exrId: extensionRule.EXR_ID,
            supNetworkId: extensionRule.SUP_NETWORK_ID,
            description: extensionRule.DESCRIPTION,
            requestByParId: extensionRule.REQUEST_BY_PAR_ID,
            approveByParId: extensionRule.APPROVE_BY_PAR_ID,
            noDaysVOR: extensionRule.NO_DAYS_VOR,
            noDaysRetailer: extensionRule.NO_DAYS_RETAILER,
            retailerCharge: extensionRule.RETAILER_CHARGE,
            retailerChargeFreeDays: extensionRule.RETAILER_CHARGE_FREE_DAYS,
            retailerChargeCost: extensionRule.RETAILER_CHARGE_COST,
            extraFields: extensionRule.EXTRA_FIELDS,
            startDate: tmpStartDate,
            endDate: tmpEndDate,
            whentoUse: extensionRule.WHEN_TO_USE,
            maxExtensionsVOR: extensionRule.MAX_EXTENSIONS_VOR,
            maxExtensionsRetailer: extensionRule.MAX_EXTENSIONS_RETAILER,
            displayOrder: extensionRule.DISPLAY_ORDER,
            custGroupCode: extensionRule.CUST_GROUP_CODE,
            showToRetailer: extensionRule.SHOW_TO_RETAILER,
            ruleEffDate: tmpRuleEffDate,
            ruleIneffDate: tmpRuleIneffDate
        };
    };

    const fetchExtensionRuleData = useCallback((selectedOptions) => {
        const selectedCustomerGroup = selectedOptions.length > 0 ? selectedOptions.map((customerGroup) => customerGroup.value).join(',') : '';
        setLoading(true);
        ExtentionRuleService.fetchExtensionRulesByCustomerGroup(selectedCustomerGroup)
            .then((resp) => {
                // console.log('Extension Rules Data:', resp.data);
                const listViewData = resp.data.map<ExtensionRuleListView>((extensionRule) => mapExtensionRuleToExtensionRuleListView(extensionRule));
                //console.log('Extension Rules Data:', listViewData);
                setExtensionRulesData(listViewData);
            })
            .catch((error) => console.error(error))
            .finally(() => setLoading(false));
    }, []);

    // Columns Memoization to prevent re-renders
    const columns = useMemo(() => {
        const updateCallback = () => {
            fetchExtensionRuleData(selectedCustomerGroups);
        };

        return createColumns(updateCallback);
    }, [fetchExtensionRuleData, selectedCustomerGroups]);

    // Table Instance for Extension Rules
    const tableInstance = useTable(
        {
            columns,
            data: extensionRulesData,
            initialState: { pageIndex: 0, pageSize: 10 }
        },
        useFilters,
        useGlobalFilter,
        useSortBy,
        usePagination
    );

    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        prepareRow,
        setGlobalFilter,
        page,
        canPreviousPage,
        canNextPage,
        pageOptions,
        pageCount,
        gotoPage,
        nextPage,
        previousPage,
        setPageSize,
        state: { pageIndex, pageSize }
    } = tableInstance;

    useEffect(() => {
        SupplierService.fetchCustomerGroups()
            .then((resp) => {
                const allCustomerGroups = resp.map((customerGroup) => ({
                    label: customerGroup.custGrpName,
                    value: customerGroup.custGrpCode
                }));
                setCustomerGroupOptions(allCustomerGroups);
            })
            .catch((error) => console.error(error));
    }, []);

    useEffect(() => {
        fetchExtensionRuleData([]);
    }, [fetchExtensionRuleData]);

    return (
        <div className="space-y-8 p-4">
            {/* Extension Rules Table */}
            <div>
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold">Extension Rules</h2>
                    <Select
                        isMulti
                        name="customerGroup"
                        options={customerGroupOptions}
                        value={selectedCustomerGroups}
                        onChange={handleCustomerGroupChange}
                        placeholder="Select customer groups"
                        className="react-select-container"
                        classNamePrefix="react-select"
                        styles={{
                            control: (base) => ({
                                ...base,
                                width: 600
                            })
                        }}
                    />
                    <Input
                        placeholder="Search..."
                        value={searchValue}
                        onChange={(e) => {
                            setGlobalFilter(e.target.value);
                            setSearchValue(e.target.value);
                        }}
                        className="max-w-xs ml-2"
                    />
                </div>

                <div className="overflow-x-auto bg-white shadow-lg rounded-lg">
                    {loading ? (
                        <div className="text-center">
                            <br />
                            <br />
                            Loading Extension Rules..
                            <br />
                            <br />
                            Please wait...
                            <Spinner
                                className="mr-3"
                                style={{ display: 'inline' }}
                            />
                            <br />
                            <br />
                            <br />
                        </div>
                    ) : (
                        <>
                            <table
                                {...getTableProps()}
                                className="min-w-full table-auto"
                            >
                                <thead>
                                    {headerGroups.map((headerGroup) => (
                                        <tr
                                            {...headerGroup.getHeaderGroupProps()}
                                            className="border-b"
                                        >
                                            {headerGroup.headers.map((column) => (
                                                <th
                                                    {...column.getHeaderProps(column.getSortByToggleProps())}
                                                    className="py-3 px-4 text-left text-sm font-semibold cursor-pointer text-muted"
                                                >
                                                    {column.render('Header')}
                                                    <span className="ml-1">
                                                        {column.isSorted ? column.isSortedDesc ? <ChevronDown className="inline w-4 h-4" /> : <ChevronUp className="inline w-4 h-4" /> : null}
                                                    </span>
                                                </th>
                                            ))}
                                        </tr>
                                    ))}
                                </thead>
                                <tbody {...getTableBodyProps()}>
                                    {page.map((row) => {
                                        prepareRow(row);
                                        return (
                                            <tr
                                                {...row.getRowProps()}
                                                className="border-b hover:bg-muted/50"
                                            >
                                                {row.cells.map((cell) => (
                                                    <td
                                                        {...cell.getCellProps()}
                                                        className="py-2 px-4 text-sm text-muted align-top"
                                                    >
                                                        {cell.render('Cell')}
                                                    </td>
                                                ))}
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                            {/* Pagination controls */}
                            <div className="flex items-center justify-between p-4">
                                <div>
                                    <Button
                                        onClick={() => gotoPage(0)}
                                        disabled={!canPreviousPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600"
                                    >
                                        {'<<'}
                                    </Button>
                                    <Button
                                        onClick={() => previousPage()}
                                        disabled={!canPreviousPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'<'}
                                    </Button>
                                    <Button
                                        onClick={() => nextPage()}
                                        disabled={!canNextPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'>'}
                                    </Button>
                                    <Button
                                        onClick={() => gotoPage(pageCount - 1)}
                                        disabled={!canNextPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'>>'}
                                    </Button>
                                </div>
                                <div>
                                    <span>
                                        Page&nbsp;
                                        <strong>
                                            {pageIndex + 1} of {pageOptions.length}&nbsp;
                                        </strong>
                                    </span>
                                    <span>
                                        | Go to page:&nbsp;
                                        <input
                                            type="number"
                                            value={pageIndex + 1}
                                            onChange={(e) => {
                                                const page = e.target.value ? Number(e.target.value) - 1 : 0;
                                                gotoPage(page);
                                            }}
                                            style={{ width: '50px' }}
                                            className="border border-input px-1 py-1 text-center"
                                        />
                                    </span>
                                </div>
                                <div>
                                    Rows per page:&nbsp;
                                    <select
                                        value={pageSize}
                                        onChange={(e) => {
                                            setPageSize(Number(e.target.value));
                                        }}
                                        className="border border-input px-3 py-1"
                                    >
                                        {[10, 20, 30, 40, 50].map((pageSize) => (
                                            <option
                                                key={pageSize}
                                                value={pageSize}
                                            >
                                                Show {pageSize}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ExtensionRulesViewModify;
