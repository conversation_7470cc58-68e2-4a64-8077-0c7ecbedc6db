import React, { useState, useEffect } from 'react';
import { Button } from '../../components/ui-component/ui/button';
import { Input } from '../../components/ui-component/ui/input';
import { Card } from '../../components/ui-component/ui/card';
import SimpleReactValidator from 'simple-react-validator';
import { cn } from '../../lib/utils';
import * as Dialog from '@radix-ui/react-dialog';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { useEnvironment } from '@aa/ui/hooks/use-environment';
import { useExtentionRuleService, useSupplierService } from '../../../service';
import { SelectInterface, SelectNumInterface, FormExtensionRuleAttributeInterface, ApproverGroupsInterface, RequesterGroupsInterface } from '../../../model';
import { CustomLoader } from '../../components/ui-component/custom-loader';
import { ExtensionRulesStep1 } from './components/ExtensionRulesStep1';
import { ExtensionRulesStep2 } from './components/ExtensionRulesStep2';
import { ExtensionRulesStep3 } from './components/ExtensionRulesStep3';
import { ExtensionRulesStep4 } from './components/ExtensionRulesStep4';
import { ExtensionRulesStep5, ExtraFieldTemplate } from './components/ExtensionRulesStep5';
import { initExtensionRulesAddObj } from './helper';

const ExtensionRulesAdd: React.FC = () => {
    const navigate = useNavigate();
    const environment = useEnvironment();
    const SupplierService = useSupplierService();
    const ExtentionRuleService = useExtentionRuleService();
    const [loading, setLoading] = useState<boolean>(false);
    const [currentStep, setCurrentStep] = useState<number>(1);
    const [formAttribute, setFormAttribute] = useState<FormExtensionRuleAttributeInterface>(initExtensionRulesAddObj);
    const [extraFieldTemplate, setExtraFieldTemplate] = useState<any[]>([]);
    const [validator] = useState(new SimpleReactValidator());
    const [forceRender, setForceRender] = useState<boolean>(false);
    const [customerGroups, setCustomerGroups] = useState<SelectInterface[]>([]);
    const [requesterGroups, setRequesterGroups] = useState<SelectNumInterface[]>([]);
    const [approverGroups, setApproverGroups] = useState<SelectNumInterface[]>([]);
    const [supplierNetworkOptions, setSupplierNetworkOptions] = useState<SelectNumInterface[]>([]);
    const today = new Date().toISOString().split('T')[0];
    const [lastStep, setLastStep] = useState<number>(0);
    const [isStep2Validated, setIsStep2Validated] = useState<boolean>(false);

    const addField = (newField: ExtraFieldTemplate) => {
        setExtraFieldTemplate((prevFields) => [...prevFields, newField]);
    };

    const fetchAllCustomerGroups = () => {
        SupplierService.fetchCustomerGroups()
            .then((resp) => {
                const allCustomerGroups = resp.map((group) => ({
                    label: group.custGrpName,
                    value: group.custGrpCode
                }));
                setCustomerGroups(allCustomerGroups);
            })
            .catch(console.error);
    };

    const fetchAllSupplierNetworks = () => {
        SupplierService.fetchSupplierNetworks()
            .then((resp) => {
                const allSupplierNetworks = resp.map((supplierNetwork) => ({
                    label: supplierNetwork.supNetworkName,
                    value: supplierNetwork.supNetworkId
                }));
                setSupplierNetworkOptions(allSupplierNetworks);
            })
            .catch(console.error);
    };

    const fetchAllRequesterGroups = () => {
        SupplierService.fetchRequesterGroups()
            .then((resp) => {
                const allRequesterGroups = resp.map((requesterGroups: RequesterGroupsInterface) => ({
                    label: requesterGroups.requestedByGroup,
                    value: requesterGroups.parId
                }));
                setRequesterGroups(allRequesterGroups);
            })
            .catch(console.error);
    };

    const fetchAllApproverGroups = () => {
        SupplierService.fetchApproverGroups()
            .then((resp) => {
                const allApproverGroups = resp.map((approverGroup: ApproverGroupsInterface) => ({
                    label: approverGroup.approvedByGroup,
                    value: approverGroup.parId
                }));
                setApproverGroups(allApproverGroups);
            })
            .catch(console.error);
    };

    useEffect(() => {
        fetchAllCustomerGroups();
        fetchAllSupplierNetworks();
        fetchAllRequesterGroups();
        fetchAllApproverGroups();
    }, []);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        if (type === 'checkbox') {
            setFormAttribute((prevState) => ({
                ...prevState,
                [name]: e.target.checked
            }));
        } else {
            const inputValue = type === 'number' ? parseFloat(value.replace(/e|-/gi, '')) : value;
            setFormAttribute((prevState) => ({
                ...prevState,
                [name]: inputValue
            }));
        }
    };

    const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setFormAttribute((prevState) => ({
            ...prevState,
            [e.target.name]: value
        }));
    };

    const nextStep = () => {
        if (!validateAtStep(currentStep)) {
            setForceRender(!forceRender);
            setFormAttribute((prev) => ({ ...prev })); // Trigger re-render to show validation messages
            setLastStep(currentStep); // Save the last step validated
            return;
        }
        setCurrentStep((prev) => Math.min(prev + 1, 5));
    };

    const validateAtStep = (step: number): boolean => {
        if (step === lastStep) {
            return true;
        }
        switch (step) {
            case 1:
                validator.showMessageFor('supplierNetwork');
                validator.showMessageFor('description');
                validator.showMessageFor('requestedBy');
                validator.showMessageFor('approvedBy');
                validator.showMessageFor('customerGroup');
                return (
                    validator.fieldValid('supplierNetwork') &&
                    validator.fieldValid('description') &&
                    validator.fieldValid('requestedBy') &&
                    validator.fieldValid('approvedBy') &&
                    validator.fieldValid('customerGroup')
                );
            case 2:
                validator.showMessageFor('noOfDaysVOR');
                validator.showMessageFor('noOfDaysRetailer');
                validator.showMessageFor('retailerCharge');
                validator.showMessageFor('retailerChargeFreeDays');
                validator.showMessageFor('retailerChargeCost');
                return (
                    validator.fieldValid('noOfDaysVOR') &&
                    validator.fieldValid('noOfDaysRetailer') &&
                    validator.fieldValid('retailerCharge') &&
                    validator.fieldValid('retailerChargeFreeDays') &&
                    validator.fieldValid('retailerChargeCost')
                );
            case 3:
                validator.showMessageFor('startDate');
                validator.showMessageFor('whenToUse');
                validator.showMessageFor('maxExtensionsVOR');
                validator.showMessageFor('maxExtensionsRetailer');
                return validator.fieldValid('startDate') && validator.fieldValid('whenToUse') && validator.fieldValid('maxExtensionsVOR') && validator.fieldValid('maxExtensionsRetailer');
            case 4:
                validator.showMessageFor('displayOrder');
                return validator.fieldValid('displayOrder');
            default:
                return false;
        }
    };

    const prevStep = () => {
        setCurrentStep((prev) => Math.max(prev - 1, 1));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (validator.allValid() && isStep2Validated) {
            const payload = {
                ...formAttribute,
                extraFields: extraFieldTemplate,
                requestByParId: formAttribute.requestByParId?.value,
                approveByParId: formAttribute.approveByParId?.value,
                supNetworkID: formAttribute.supNetworkID?.value,
                custGroupCode: formAttribute.custGroupCode?.value,
                showToRetailer: formAttribute.showToRetailer ? 1 : 0
            };
            ExtentionRuleService.insertExtentionRule(payload)
                .then(() => {
                    setLoading(false);
                    toast.success('New record inserted successfully!');
                    setTimeout(() => {
                        navigate(`${environment.basePath}/extension-rules/view-modify`, { replace: true });
                    }, 1000);
                })
                .catch(() => {
                    setLoading(false);
                    toast.error('Something went wrong!');
                });
        } else {
            validator.showMessages();
        }
    };

    const handleDropdownChange = (selectedOption: any, name: string) => {
        switch (name) {
            case 'supNetworkID':
                setFormAttribute((prev) => ({
                    ...prev,
                    supNetworkID: selectedOption
                }));
                break;
            case 'customerGroup':
                setFormAttribute((prev) => ({
                    ...prev,
                    custGroupCode: selectedOption
                }));
                break;
            case 'requestByParId':
            case 'approveByParId':
                setFormAttribute((prev) => ({
                    ...prev,
                    [name]: selectedOption
                }));
                break;

            default:
                break;
        }
    };

    const removeField = (index: number) => {
        setExtraFieldTemplate((prevFields) => prevFields.filter((_, i) => i !== index));
    };
    return (
        <>
            {/* Page Level Loader */}
            <CustomLoader show={loading} />

            <div className="p-6 space-y-6">
                <Card className="bg-white shadow-md rounded-lg p-6">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-semibold mb-4">Extension Rules</h2>
                    </div>

                    <form onSubmit={handleSubmit}>
                        <div className="flex justify-between items-center mb-6">
                            <Button
                                type="button"
                                onClick={prevStep}
                                disabled={currentStep === 1}
                                className={cn('px-4 py-2 rounded-md text-white', currentStep === 1 ? 'bg-gray-300 cursor-not-allowed' : 'bg-yellow-500 hover:bg-yellow-600')}
                            >
                                Previous
                            </Button>
                            <span className="text-gray-600">Step {currentStep} of 5</span>
                            <Button
                                type="button"
                                onClick={nextStep}
                                disabled={currentStep === 5}
                                className={cn('px-4 py-2 rounded-md text-white', currentStep === 5 ? 'bg-gray-300 cursor-not-allowed' : 'bg-yellow-500 hover:bg-yellow-600')}
                            >
                                Next
                            </Button>
                        </div>

                        {/* Step 1: Basic Information */}
                        {currentStep === 1 && (
                            <ExtensionRulesStep1
                                formAttribute={formAttribute}
                                handleDropdownChange={handleDropdownChange}
                                supplierNetworkOptions={supplierNetworkOptions}
                                validator={validator}
                                handleChange={handleChange}
                                customerGroups={customerGroups}
                                requesterGroupsOptions={requesterGroups}
                                approverGroupsOptions={approverGroups}
                            />
                        )}

                        {/* Step 2: Duration Information */}
                        {currentStep === 2 && (
                            <ExtensionRulesStep2
                                formAttribute={formAttribute}
                                validator={validator}
                                handleChange={handleChange}
                                isValidated={setIsStep2Validated}
                            />
                        )}

                        {/* Step 3: Dates */}
                        {currentStep === 3 && (
                            <ExtensionRulesStep3
                                formAttribute={formAttribute}
                                handleDateChange={handleDateChange}
                                validator={validator}
                                handleChange={handleChange}
                                today={today}
                            />
                        )}

                        {/* Step 4: Additional Information */}
                        {currentStep === 4 && (
                            <ExtensionRulesStep4
                                formAttribute={formAttribute}
                                handleDateChange={handleDateChange}
                                validator={validator}
                                handleChange={handleChange}
                                today={today}
                            />
                        )}

                        {/* Step 5: Dynamic Fields */}
                        {currentStep === 5 && (
                            <>
                                <ExtensionRulesStep5
                                    extraFieldTemplate={extraFieldTemplate}
                                    removeField={removeField}
                                    addField={addField}
                                    setExtraFieldTemplate={setExtraFieldTemplate}
                                />
                            </>
                        )}

                        {/* Submit Button on Final Step */}
                        {currentStep === 5 && (
                            <div className="mt-4">
                                <Button
                                    type="submit"
                                    disabled={!validator.allValid() && !isStep2Validated}
                                    className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
                                >
                                    Submit
                                </Button>
                            </div>
                        )}
                    </form>
                </Card>
            </div>
        </>
    );
};

export default ExtensionRulesAdd;
