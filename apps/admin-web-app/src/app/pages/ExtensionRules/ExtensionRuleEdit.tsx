'use client';

import React, { useState } from 'react';
import SimpleReactValidator from 'simple-react-validator';
import { toast } from 'sonner';

import { Input, Button, Textarea, Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../../components/ui-component/ui';
import { ExtensionRuleEditInterface, ExtensionRuleListView } from '../../../model';
import { useExtentionRuleService } from '../../../service';

export interface ExtensionRuleEditProps {
    record: ExtensionRuleListView;
    updateCallback: () => void;
}

export const ExtensionRuleEdit: React.FC<ExtensionRuleEditProps> = ({ record, updateCallback }) => {
    const [isOpen, setIsOpen] = React.useState(false);
    const ExtentionRuleService = useExtentionRuleService();

    const [formAttributes, setFormAttributes] = useState<ExtensionRuleEditInterface>({
        description: record.description,
        whentoUse: record.whentoUse,
        displayOrder: record.displayOrder,
        showToRetailer: record.showToRetailer,
        ruleEffDate: record.ruleEffDate,
        ruleIneffDate: record.ruleIneffDate ?? ''
    });
    const [validator] = useState(new SimpleReactValidator());

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormAttributes((prev) => ({
            ...prev,
            [name]: value
        }));
    };

    const handleShowRetailerChecked = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormAttributes((prev) => ({
            ...prev,
            showToRetailer: e.target.checked ? 1 : 0
        }));
    };

    const editClicked = (): void => {
        setIsOpen(true);
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        validator.showMessages();
        if (validator.allValid()) {
            // Call API to save the record
            ExtentionRuleService.updateExtensionRule(record.exrId, formAttributes)
                .then(() => {
                    updateCallback();
                    toast.success(`Extension rule updated successfully for ${formAttributes.description}.`);
                })
                .catch(() => {
                    toast.error(`Extension rule update failed!`);
                })
                .finally(() => {
                    setIsOpen(false);
                });
        } else {
            validator.showMessages();
            setFormAttributes({ ...formAttributes });
        }
    };

    const handleCancelClick = (): void => {
        setIsOpen(false);
    };

    return (
        <Dialog open={isOpen}>
            {/* <DialogTrigger asChild> */}
            <Button
                variant="outline"
                size="sm"
                className="bg-blue-600 text-white"
                onClick={editClicked}
            >
                Edit
            </Button>
            {/* </DialogTrigger> */}
            <DialogContent className="bg-white text-black">
                <form onSubmit={handleSubmit}>
                    <DialogHeader>
                        <DialogTitle>Edit Extension Rule</DialogTitle>
                    </DialogHeader>
                    <div className="p-4 space-y-4">
                        <div>
                            <label
                                htmlFor="description"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Description <span className="text-red-500">*</span>
                            </label>
                            <Textarea
                                id="description"
                                name="description"
                                value={formAttributes.description}
                                onChange={handleChange}
                                placeholder="Enter Description"
                                className="w-full bg-white border border-gray-300 text-gray-700 focus:ring-2 focus:ring-yellow-500"
                            />
                            {validator.message('description', formAttributes.description, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="whentoUse"
                                className="block text-sm font-medium text-gray-700"
                            >
                                When To Use <span className="text-red-500">*</span>
                            </label>
                            <Textarea
                                id="whentoUse"
                                name="whentoUse"
                                value={formAttributes.whentoUse}
                                onChange={handleChange}
                                placeholder="Enter when to use"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('whentoUse', formAttributes.whentoUse, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="display-order"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Display Order <span className="text-red-500">*</span>
                            </label>
                            <Input
                                type="number"
                                id="display-order"
                                name="displayOrder"
                                value={formAttributes.displayOrder}
                                onChange={handleChange}
                                placeholder="Enter Display Order"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('displayOrder', formAttributes.displayOrder, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="show-to-retailer"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Show to Retailer <span className="text-red-500">*</span>
                            </label>
                            <input
                                className="big-checkbox"
                                type="checkbox"
                                checked={formAttributes.showToRetailer === 1}
                                onChange={handleShowRetailerChecked}
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="effective-date"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Effective Date
                            </label>
                            <input
                                type="date"
                                id="effective-date"
                                name="ruleEffDate"
                                value={formAttributes.ruleEffDate as string}
                                onChange={handleChange}
                                min={formAttributes.ruleEffDate as string}
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="in-effective-date"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Ineffective Date
                            </label>
                            <input
                                type="date"
                                id="in-effective-date"
                                name="ruleIneffDate"
                                value={formAttributes.ruleIneffDate as string}
                                onChange={handleChange}
                                min={formAttributes.ruleEffDate as string}
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            type="submit"
                            className="bg-blue-500 text-white hover:bg-blue-600"
                        >
                            Save
                        </Button>
                        <Button
                            type="button"
                            className="bg-red-500 text-white hover:bg-red-600"
                            onClick={handleCancelClick}
                        >
                            Cancel
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
};
