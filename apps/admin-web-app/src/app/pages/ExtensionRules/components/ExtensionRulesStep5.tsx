import React, { useState } from 'react';
import { Input } from '../../../components/ui-component/ui/input';
import { Button } from '../../../components/ui-component/ui/button';
import { Textarea } from '../../../components/ui-component/ui/textarea';
import * as Dialog from '@radix-ui/react-dialog';
import { cn } from '../../../lib/utils';

export interface ExtraFieldTemplate {
    type: 'text' | 'input' | 'textarea' | 'datetimepicker' | 'select' | 'checkbox' | 'radio' | 'list';
    key: string;
    templateOptions: {
        label: string;
        placeholder: string;
        maxlength?: string;
        minlength?: string;
        required?: boolean;
        options?: { label: string; value: string }[];
        fields?: ExtraFieldTemplate[];
    };
}

interface ExtensionRulesStep5Props {
    extraFieldTemplate: ExtraFieldTemplate[];
    removeField: (index: number) => void;
    addField: (field: ExtraFieldTemplate) => void;
    setExtraFieldTemplate: (field: any) => void;
}

const FieldTypes = ['text', 'input', 'textarea', 'datetimepicker', 'select', 'checkbox', 'radio'];

export const ExtensionRulesStep5: React.FC<ExtensionRulesStep5Props> = ({ extraFieldTemplate, removeField, setExtraFieldTemplate }) => {
    const [fieldState, setFieldState] = useState({
        fieldType: 'text',
        fieldLabel: '',
        fieldKey: '',
        fieldPlaceholder: '',
        fieldMaxLength: '',
        fieldMinLength: '',
        isRequired: false,
        options: undefined,
        isList: false,
        listName: '',
        isAddingToSameList: false
    });

    const [fieldErrors, setFieldErrors] = useState({
        fieldLabelError: '',
        fieldKeyError: '',
        fieldListName: ''
    });

    const [showExtraFieldDialog, setShowExtraFieldDialog] = useState(false);
    const [listIndex, setListIndex] = useState<number | undefined>(undefined);

    const handleChange = (field: string, value: any) => {
        if (field === 'listName' && value.length > 0) {
            setFieldErrors((fieldErrors) => ({ ...fieldErrors, fieldListName: '' }));
        } else if (field === 'fieldLabel' && value.length > 0) {
            setFieldErrors((fieldErrors) => ({ ...fieldErrors, fieldLabelError: '' }));
        } else if (field === 'fieldKey' && value.length > 0) {
            setFieldErrors((fieldErrors) => ({ ...fieldErrors, fieldKeyError: '' }));
        }
        if (field === 'options') {
            const optionValues = value.split(',').map((item: any) => ({ label: item, value: item }));
            return setFieldState((prev) => ({ ...prev, [field]: optionValues }));
        }
        setFieldState((prev) => ({ ...prev, [field]: value }));
    };

    const handleFieldSubmit = () => {
        let isError = false;
        const { fieldLabel, fieldKey, fieldType, fieldPlaceholder, fieldMaxLength, fieldMinLength, isRequired, options, isList, listName } = fieldState;

        if (!fieldLabel.trim()) {
            setFieldErrors((fieldErrors) => ({ ...fieldErrors, fieldLabelError: 'Field label is required' }));
            isError = true;
        } else {
            isError = false;
        }

        if (!listName.trim()) {
            setFieldErrors((fieldErrors) => ({ ...fieldErrors, fieldListName: 'List Name is required' }));
            isError = true;
        } else {
            isError = false;
        }

        if (!fieldKey.trim()) {
            setFieldErrors((fieldErrors) => ({ ...fieldErrors, fieldKeyError: 'Field key is required' }));
            isError = true;
        } else {
            isError = false;
        }

        if (isError) {
            return;
        }

        setFieldErrors({ fieldLabelError: '', fieldKeyError: '', fieldListName: '' });

        const templateOptionsField = isList
            ? {
                  label: listName,
                  fields: [
                      {
                          key: fieldKey,
                          templateOptions: {
                              label: fieldLabel,
                              placeholder: fieldPlaceholder,
                              maxlength: fieldMaxLength,
                              minlength: fieldMinLength,
                              required: isRequired,
                              options: ['select', 'checkbox', 'radio'].includes(fieldType) ? options : []
                          },
                          type: fieldType
                      }
                  ],
                  type: 'list'
              }
            : {
                  label: fieldLabel,
                  placeholder: fieldPlaceholder,
                  maxlength: fieldMaxLength,
                  minlength: fieldMinLength,
                  required: isRequired,
                  options: ['select', 'checkbox', 'radio'].includes(fieldType) ? options : []
                  // type: fieldType,
              };
        let filteredTemplateOptions;
        if (isList) {
            const fields = templateOptionsField.fields[0].templateOptions;
            filteredTemplateOptions = {
                ...templateOptionsField,
                fields: [
                    {
                        ...templateOptionsField.fields[0],
                        templateOptions: Object.fromEntries(Object.entries(fields).filter(([_, value]) => value !== undefined && value !== '' && (!Array.isArray(value) || value.length > 0)))
                    }
                ]
            };
        } else {
            filteredTemplateOptions = Object.fromEntries(
                Object.entries(templateOptionsField).filter(([_, value]) => value !== undefined && value !== '' && (!Array.isArray(value) || value.length > 0))
            );
        }

        const newField = {
            key: isList ? listName : fieldKey,
            templateOptions: filteredTemplateOptions,
            type: isList ? 'list' : fieldType
        };

        const updateFields = (prevFields: any) => {
            if (isList && fieldState.isAddingToSameList) {
                const updatedFields = prevFields.map((field: any) => {
                    if (field.key === listName) {
                        return {
                            ...field,
                            templateOptions: {
                                ...field.templateOptions,
                                fields: [...field.templateOptions.fields.slice(0, listIndex), ...newField.templateOptions.fields, ...field.templateOptions.fields.slice(listIndex)]
                            }
                        };
                    }
                    return field;
                });

                if (!updatedFields.some((field: any) => field.key === listName)) {
                    updatedFields.push(newField);
                }

                return updatedFields;
            }

            return [...prevFields, newField];
        };

        setExtraFieldTemplate(updateFields);
        setFieldState({
            fieldType: 'text',
            fieldLabel: '',
            fieldKey: '',
            fieldPlaceholder: '',
            fieldMaxLength: '',
            fieldMinLength: '',
            isRequired: false,
            options: '',
            isList: false,
            listName: '',
            isAddingToSameList: false
        });
        setShowExtraFieldDialog(false);
    };

    const handleAddMoreFields = (fieldIndex: number, fieldKey: string) => {
        setListIndex(fieldIndex);
        setFieldState((prev) => ({ ...prev, isAddingToSameList: true, isList: true, listName: fieldKey }));
        setShowExtraFieldDialog(true);
    };

    const openDialog = () => setShowExtraFieldDialog(true);

    const closeDialog = () => {
        setFieldState((prev) => ({
            ...prev,
            isAddingToSameList: false,
            listName: ''
        }));
        setShowExtraFieldDialog(false);
    };

    return (
        <>
            <Dialog.Root>
                <Dialog.Trigger asChild>
                    <Button
                        type="button"
                        onClick={openDialog}
                        className={cn('bg-yellow-500 hover:bg-yellow-600')}
                    >
                        Add Extra Field
                    </Button>
                </Dialog.Trigger>

                {showExtraFieldDialog && (
                    <Dialog.Portal>
                        <Dialog.Overlay className="fixed inset-0 bg-black opacity-50" />
                        <Dialog.Content className="fixed inset-1/4 bg-white p-6 rounded-lg shadow-lg fit-content">
                            <Dialog.Title className="text-xl font-semibold mb-4">Add Extra Field</Dialog.Title>

                            {/* <div className="space-y-4">
                                {!fieldState.isAddingToSameList && !fieldState.isList && <Button
                                    onClick={() => handleChange('isList', true)}
                                    className={'mb-1 bg-blue-500 text-white rounded-m'}>
                                    Add List
                                </Button>}

                                {fieldState.isList && !fieldState.isAddingToSameList && (
                                    <div>
                                        <label
                                            htmlFor="list-name"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            List Name*
                                        </label>
                                        <Input
                                            type="text"
                                            id="list-name"
                                            value={fieldState.listName}
                                            onChange={(e) => handleChange('listName', e.target.value)}
                                            placeholder="Enter the name of the list"
                                            className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                        />
                                        {fieldErrors.fieldListName && <div className="text-red-600 text-sm mt-2">{fieldErrors.fieldListName}</div>}
                                    </div>
                                )}
                            </div> */}

                            <div className="space-y-4">
                                {!fieldState.isAddingToSameList && !fieldState.isList && (
                                    <Button
                                        onClick={() => handleChange('isList', true)}
                                        className={'mb-1 bg-blue-500 text-white rounded-m'}
                                    >
                                        Add List
                                    </Button>
                                )}

                                {fieldState.isList && !fieldState.isAddingToSameList && (
                                    <div>
                                        <label
                                            htmlFor="list-name"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            List Name*
                                        </label>
                                        <Input
                                            type="text"
                                            id="list-name"
                                            value={fieldState.listName}
                                            onChange={(e) => handleChange('listName', e.target.value)}
                                            placeholder="Enter the name of the list"
                                            className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                        />
                                        {fieldErrors.fieldListName && <div className="text-red-600 text-sm mt-2">{fieldErrors.fieldListName}</div>}
                                    </div>
                                )}
                                <div>
                                    <label
                                        htmlFor="field-type"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Field Type*
                                    </label>
                                    <select
                                        id="field-type"
                                        value={fieldState.fieldType}
                                        onChange={(e) => handleChange('fieldType', e.target.value)}
                                        className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                    >
                                        {FieldTypes.map((type) => (
                                            <option
                                                key={type}
                                                value={type}
                                            >
                                                {type.charAt(0).toUpperCase() + type.slice(1)}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label
                                        htmlFor="field-label"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Field Label*
                                    </label>
                                    <Input
                                        type="text"
                                        id="field-label"
                                        value={fieldState.fieldLabel}
                                        onChange={(e) => handleChange('fieldLabel', e.target.value)}
                                        placeholder="Enter Field Label e.g., User Name"
                                        className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                    />
                                    {fieldErrors.fieldLabelError && <div className="text-red-600 text-sm mt-2">{fieldErrors.fieldLabelError}</div>}
                                </div>
                                <div>
                                    <label
                                        htmlFor="field-key"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Field Key*
                                    </label>
                                    <Input
                                        type="text"
                                        id="field-key"
                                        value={fieldState.fieldKey}
                                        onChange={(e) => handleChange('fieldKey', e.target.value)}
                                        placeholder="Enter Field Key e.g., userName"
                                        className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                    />
                                    {fieldErrors.fieldKeyError && <div className="text-red-600 text-sm mt-2">{fieldErrors.fieldKeyError}</div>}
                                </div>
                                <div>
                                    <label
                                        htmlFor="field-placeholder"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Field Placeholder
                                    </label>
                                    <Input
                                        type="text"
                                        id="field-placeholder"
                                        value={fieldState.fieldPlaceholder}
                                        onChange={(e) => handleChange('fieldPlaceholder', e.target.value)}
                                        placeholder="Enter Field Placeholder"
                                        className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                    />
                                </div>

                                {['select', 'checkbox', 'radio'].includes(fieldState.fieldType) && (
                                    <div>
                                        <label
                                            htmlFor="field-options"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Field Options (comma-separated)
                                        </label>
                                        <Input
                                            type="text"
                                            id="field-options"
                                            value={fieldState.options}
                                            onChange={(e) => handleChange('options', e.target.value)}
                                            placeholder="Enter options like: Option1, Option2"
                                            className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                        />
                                    </div>
                                )}

                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Min Length</label>
                                    <Input
                                        type="number"
                                        value={fieldState.fieldMinLength}
                                        onChange={(e) => handleChange('fieldMinLength', e.target.value)}
                                        className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Max Length</label>
                                    <Input
                                        type="number"
                                        value={fieldState.fieldMaxLength}
                                        onChange={(e) => handleChange('fieldMaxLength', e.target.value)}
                                        className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                    />
                                </div>

                                <div>
                                    <label className="inline-flex items-center text-sm font-medium text-gray-700">
                                        <input
                                            type="checkbox"
                                            checked={fieldState.isRequired}
                                            onChange={() => handleChange('isRequired', !fieldState.isRequired)}
                                            className="form-checkbox"
                                        />
                                        &nbsp;Required
                                    </label>
                                </div>

                                <div className="mt-4 flex justify-end space-x-4">
                                    <Dialog.Close asChild>
                                        <Button
                                            type="button"
                                            onClick={closeDialog}
                                            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md"
                                        >
                                            Cancel
                                        </Button>
                                    </Dialog.Close>
                                    <Button
                                        type="button"
                                        className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md"
                                        onClick={handleFieldSubmit}
                                    >
                                        Add Field
                                    </Button>
                                </div>
                            </div>
                        </Dialog.Content>
                    </Dialog.Portal>
                )}
            </Dialog.Root>

            {extraFieldTemplate.map((field, fieldIndex) => {
                return field.type === 'list' ? (
                    <Button
                        key={`btn-more-field-${fieldIndex}`}
                        type="button"
                        onClick={() => handleAddMoreFields(fieldIndex, field.key)}
                        className="mt-4 ml-1 bg-blue-500 text-white px-4 py-2 rounded-md"
                    >
                        Add More Fields to the {field.key}
                    </Button>
                ) : null;
            })}
            {extraFieldTemplate.length > 0 && (
                <div className="mt-4">
                    {extraFieldTemplate.map((field, index) => (
                        <div
                            key={index}
                            className="flex flex-col space-y-4 mb-4"
                        >
                            <div className="mb-2 font-medium text-gray-700">{field.templateOptions.label}</div>

                            {field.type === 'list' ? (
                                <div className="space-y-4">
                                    {field.templateOptions.fields?.map((listField, listIndex) => (
                                        <div
                                            key={listIndex}
                                            className="flex items-center space-x-4"
                                        >
                                            <div className="mb-2 font-medium text-gray-700">{listField.templateOptions.label}</div>
                                            <div className="w-full">
                                                {listField.type === 'textarea' ? (
                                                    <Textarea
                                                        name={listField.key}
                                                        disabled
                                                        value=""
                                                        placeholder={listField.templateOptions.placeholder}
                                                        className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                                    />
                                                ) : listField.type === 'select' ? (
                                                    <select className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500">
                                                        {listField.templateOptions.options?.map((option, optionIndex) => (
                                                            <option
                                                                key={optionIndex}
                                                                value={option.value}
                                                            >
                                                                {option.label}
                                                            </option>
                                                        ))}
                                                    </select>
                                                ) : listField.type === 'checkbox' ? (
                                                    <div className="flex items-center">
                                                        {listField.templateOptions.options?.map((option, optionIndex) => (
                                                            <label
                                                                key={optionIndex}
                                                                className="flex items-center mr-4"
                                                            >
                                                                <input
                                                                    type="checkbox"
                                                                    className="mr-2"
                                                                    value={option.value}
                                                                    disabled
                                                                />
                                                                {option.label}
                                                            </label>
                                                        ))}
                                                    </div>
                                                ) : listField.type === 'radio' ? (
                                                    <div className="flex items-center">
                                                        {listField.templateOptions.options?.map((option, optionIndex) => (
                                                            <label
                                                                key={optionIndex}
                                                                className="flex items-center mr-4"
                                                            >
                                                                <input
                                                                    type="radio"
                                                                    name={listField.key}
                                                                    value={option.value}
                                                                    className="mr-2"
                                                                    disabled
                                                                />
                                                                {option.label}
                                                            </label>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <Input
                                                        type={listField.type == 'input' ? 'number' : listField.type}
                                                        disabled
                                                        value=""
                                                        placeholder={listField.templateOptions.placeholder}
                                                        className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                                    />
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="w-full">
                                    {field.type === 'textarea' ? (
                                        <Textarea
                                            name={field.key}
                                            disabled
                                            value=""
                                            placeholder={field.templateOptions.placeholder}
                                            className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                        />
                                    ) : field.type === 'select' ? (
                                        <select className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500">
                                            {field.templateOptions.options?.map((option, optionIndex) => (
                                                <option
                                                    key={optionIndex}
                                                    value={option.value}
                                                >
                                                    {option.label}
                                                </option>
                                            ))}
                                        </select>
                                    ) : field.type === 'checkbox' ? (
                                        <div className="flex items-center">
                                            {field.templateOptions.options?.map((option, optionIndex) => (
                                                <label
                                                    key={optionIndex}
                                                    className="flex items-center mr-4"
                                                >
                                                    <input
                                                        type="checkbox"
                                                        className="mr-2"
                                                        value={option.value}
                                                    />
                                                    {option.label}
                                                </label>
                                            ))}
                                        </div>
                                    ) : field.type === 'radio' ? (
                                        <div className="flex items-center">
                                            {field.templateOptions.options?.map((option, optionIndex) => (
                                                <label
                                                    key={optionIndex}
                                                    className="flex items-center mr-4"
                                                >
                                                    <input
                                                        type="radio"
                                                        name={field.key}
                                                        value={option.value}
                                                        className="mr-2"
                                                    />
                                                    {option.label}
                                                </label>
                                            ))}
                                        </div>
                                    ) : (
                                        <Input
                                            type={field.type}
                                            disabled
                                            value=""
                                            placeholder={field.templateOptions.placeholder}
                                            className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                        />
                                    )}
                                </div>
                            )}

                            <Button
                                type="button"
                                onClick={() => removeField(index)}
                                className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-md"
                            >
                                <span>-</span>
                            </Button>
                        </div>
                    ))}
                </div>
            )}
        </>
    );
};
