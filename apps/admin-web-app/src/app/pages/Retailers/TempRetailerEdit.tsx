'use client';

import React, { useState, useEffect } from 'react';
import { useRetailerService } from '../../../service/retailer.service';
import SimpleReactValidator from 'simple-react-validator';
import { format, parseISO } from 'date-fns';
import { toast } from 'sonner';

import { SupplierData } from '@aa/admin-helpers';
import { Button, Input } from '../../components/ui-component/ui';
import { cn } from '../../lib/utils'; // ShadCN utility for conditional classes
import { SupplierInsertInterface } from '../../../model';

interface TempRetailEditProps {
    editRecord: SupplierData;
    snapshotId: string;
    updateCallback: () => void;
}

export const TempRetailerEdit: React.FC<TempRetailEditProps> = (props) => {
    const [currentStep, setCurrentStep] = useState(1);
    const [lastStep, setLastStep] = useState<number>(0);
    const [retailerFormAttributes, setRetailerFormAttributes] = useState<SupplierInsertInterface>({
        snapshotId: props.snapshotId,
        SupResourceId: 0,
        CICode: '',
        Retailer_Name: '',
        Franchise: '',
        Group: '',
        SalesRegion: '',
        AftersalesRegion: '',
        RTMRegion: '',
        FSERegion: '',
        Phone: '',
        Address1: '',
        Address2: '',
        Town: '',
        County: '',
        PostCode: '',
        Latitude: 0,
        Longitude: 0,
        Lat_Long: '',
        ContactEmail: '',
        OpenMonday: '',
        OpenTuesday: '',
        OpenWednesday: '',
        OpenThursday: '',
        OpenFriday: '',
        OpenSaturday: '',
        OpenSunday: '',
        OpenMondayStart: '',
        OpenMondayEnd: '',
        OpenSaturdayStart: '',
        OpenSaturdayEnd: '',
        OpenSundayStart: '',
        OpenSundayEnd: ''
    });
    const [validator] = useState(new SimpleReactValidator());
    const RetailerService = useRetailerService();

    const validateAtStep = (step: number): boolean => {
        if (step === lastStep) {
            return true;
        }
        switch (step) {
            case 1:
                validator.showMessageFor('RetailerName');
                validator.showMessageFor('RetailerCustomerGroup');
                validator.showMessageFor('RetailerSalesRegion');
                validator.showMessageFor('RetailerAftersalesRegion');
                validator.showMessageFor('RetailerRtmRegion');
                validator.showMessageFor('RetailerFseRegion');
                return (
                    validator.fieldValid('RetailerName') &&
                    validator.fieldValid('RetailerCustomerGroup') &&
                    validator.fieldValid('RetailerSalesRegion') &&
                    validator.fieldValid('RetailerRtmRegion') &&
                    validator.fieldValid('RetailerFseRegion') &&
                    validator.fieldValid('RetailerAftersalesRegion')
                );
            case 2:
                validator.showMessageFor('RetailerPhone');
                validator.showMessageFor('FirstLineOfAddress');
                validator.showMessageFor('Town');
                validator.showMessageFor('County');
                validator.showMessageFor('PostCode');
                return (
                    validator.fieldValid('Town') &&
                    validator.fieldValid('County') &&
                    validator.fieldValid('PostCode') &&
                    validator.fieldValid('RetailerPhone') &&
                    validator.fieldValid('FirstLineOfAddress')
                );
            case 3:
                validator.showMessageFor('Latitude');
                validator.showMessageFor('Longitude');
                validator.showMessageFor('ContactEmail');
                return validator.fieldValid('Longitude') && validator.fieldValid('Latitude') && validator.fieldValid('ContactEmail');
            default:
                return false;
        }
    };

    const nextStep = () => {
        if (!validateAtStep(currentStep)) {
            setRetailerFormAttributes((prev) => ({ ...prev })); // Trigger re-render to show validation messages
            setLastStep(currentStep); // Save the last step validated
            return;
        }
        setCurrentStep((prev) => Math.min(prev + 1, 3));
    };

    const prevStep = () => {
        setCurrentStep((prev) => Math.max(prev - 1, 1)); // Minimum step: 1
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;

        setRetailerFormAttributes((prev) => ({
            ...prev,
            [name]: value
        }));
        validator.showMessageFor(name);
    };

    const formatDateForInput = (datetimeString: string) => {
        if (datetimeString) {
            const parsedDate = parseISO(datetimeString);
            return format(parsedDate, "yyyy-MM-dd'T'HH:mm");
        }
        return '';
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        validator.showMessages();
        if (!validator.allValid()) {
            setRetailerFormAttributes((prev) => ({ ...prev })); // Trigger re-render to show validation messages
            return;
        }
        const payload: SupplierInsertInterface = {
            snapshotId: retailerFormAttributes.snapshotId,
            CICode: retailerFormAttributes.CICode,
            Retailer_Name: retailerFormAttributes.Retailer_Name,
            Franchise: retailerFormAttributes.Franchise,
            Group: retailerFormAttributes.Group,
            SalesRegion: retailerFormAttributes.SalesRegion,
            AftersalesRegion: retailerFormAttributes.AftersalesRegion,
            RTMRegion: retailerFormAttributes.RTMRegion,
            FSERegion: retailerFormAttributes.FSERegion,
            Phone: retailerFormAttributes.Phone,
            Address1: retailerFormAttributes.Address1,
            Address2: retailerFormAttributes.Address2,
            Town: retailerFormAttributes.Town,
            County: retailerFormAttributes.County,
            PostCode: retailerFormAttributes.PostCode,
            Latitude: retailerFormAttributes.Latitude,
            Longitude: retailerFormAttributes.Longitude,
            Lat_Long: retailerFormAttributes.Latitude + '-' + retailerFormAttributes.Longitude,
            ContactEmail: retailerFormAttributes.ContactEmail,
            OpenMonday:
                new Date(retailerFormAttributes.OpenMondayStart).getHours().toString() +
                ':' +
                new Date(retailerFormAttributes.OpenMondayStart).getMinutes().toString() +
                '-' +
                new Date(retailerFormAttributes.OpenMondayEnd).getHours().toString() +
                ':' +
                new Date(retailerFormAttributes.OpenMondayEnd).getMinutes().toString(),
            OpenTuesday: retailerFormAttributes.OpenMonday,
            OpenWednesday: retailerFormAttributes.OpenMonday,
            OpenThursday: retailerFormAttributes.OpenMonday,
            OpenFriday: retailerFormAttributes.OpenMonday,
            OpenSaturday:
                new Date(retailerFormAttributes.OpenSaturdayStart).getHours().toString() +
                ':' +
                new Date(retailerFormAttributes.OpenSaturdayStart).getMinutes().toString() +
                '-' +
                new Date(retailerFormAttributes.OpenSaturdayEnd).getHours().toString() +
                ':' +
                new Date(retailerFormAttributes.OpenSaturdayEnd).getMinutes().toString(),
            OpenSunday:
                new Date(retailerFormAttributes.OpenSundayStart).getHours().toString() +
                ':' +
                new Date(retailerFormAttributes.OpenSundayStart).getMinutes().toString() +
                '-' +
                new Date(retailerFormAttributes.OpenSundayEnd).getHours().toString() +
                ':' +
                new Date(retailerFormAttributes.OpenSundayEnd).getMinutes().toString()
        };
        RetailerService.insertRetailer(payload)
            .then(() => {
                toast.success('Temp Retailer record updated successfully!');
                props.updateCallback();
            })
            .catch(() => {
                toast.error('Something went wrong :-( !');
            });
    };

    useEffect(() => {
        const setEditFields = () => {
            const rowData = props.editRecord;
            setRetailerFormAttributes({
                snapshotId: props.snapshotId,
                SupResourceId: rowData.SupResourceId,
                CICode: rowData?.CiCode,
                Retailer_Name: rowData?.supplier?.supplierName,
                Franchise: rowData?.Franchise,
                Group: rowData?.mbRetailer?.groupUc,
                SalesRegion: rowData?.mbRetailer?.region1,
                AftersalesRegion: rowData?.mbRetailer?.region2,
                RTMRegion: rowData?.mbRetailer?.region3,
                FSERegion: rowData?.mbRetailer?.region4,
                Phone: rowData?.supplierResourceTel?.resourceTelNo,
                Address1: rowData?.supplier?.supplierAddrLine1,
                Address2: rowData?.supplier?.supplierAddrLine2,
                Town: rowData?.supplier?.supplierAddrLine3,
                County: rowData?.supplier?.supplierAddrLine4,
                PostCode: rowData?.supplier?.supInwdPostCode + ' ' + rowData?.supplier?.supOutwdPostCode,
                Latitude: rowData?.supplierLatLong?.latitude,
                Longitude: rowData?.supplierLatLong?.longitude,
                Lat_Long: rowData?.supplierLatLong?.latitude + '/' + rowData?.supplierLatLong?.longitude,
                ContactEmail: rowData?.supplier?.email,
                OpenMonday: rowData?.supplier?.supWkOpenTime + '-' + rowData?.supplier?.supWkCloseTime,
                OpenTuesday: rowData?.supplier?.supWkOpenTime + '-' + rowData?.supplier?.supWkCloseTime,
                OpenWednesday: rowData?.supplier?.supWkOpenTime + '-' + rowData?.supplier?.supWkCloseTime,
                OpenThursday: rowData?.supplier?.supWkOpenTime + '-' + rowData?.supplier?.supWkCloseTime,
                OpenFriday: rowData?.supplier?.supWkOpenTime + '-' + rowData?.supplier?.supWkCloseTime,
                OpenSaturday: rowData?.supplier?.supSatOpenTime + '-' + rowData?.supplier?.supSatCloseTime,
                OpenSunday: rowData?.supplier?.supSunOpenTime + '-' + rowData?.supplier?.supSunCloseTime,
                OpenMondayStart: rowData?.supplier?.supWkOpenTime,
                OpenMondayEnd: rowData?.supplier?.supWkCloseTime,
                OpenSaturdayStart: rowData?.supplier?.supSatOpenTime,
                OpenSaturdayEnd: rowData?.supplier?.supSatCloseTime,
                OpenSundayStart: rowData?.supplier?.supSunOpenTime,
                OpenSundayEnd: rowData?.supplier?.supSunCloseTime
            });
        };

        setEditFields();
        //validator.showMessages();
    }, [props.editRecord, props.snapshotId]);

    return (
        <div>
            <form onSubmit={handleSubmit}>
                {/* Step Navigation */}
                <div className="flex justify-between items-center mb-6">
                    <Button
                        type="button"
                        onClick={prevStep}
                        disabled={currentStep === 1}
                        className={cn('px-4 py-2 rounded-md text-white', currentStep === 1 ? 'bg-gray-300 cursor-not-allowed' : 'bg-yellow-500 hover:bg-yellow-600')}
                    >
                        Previous
                    </Button>
                    <span className="text-gray-600">Step {currentStep} of 3</span>
                    <Button
                        type="button"
                        onClick={nextStep}
                        disabled={currentStep === 3}
                        className={cn('px-4 py-2 rounded-md text-white', currentStep === 3 ? 'bg-gray-300 cursor-not-allowed' : 'bg-yellow-500 hover:bg-yellow-600')}
                    >
                        Next
                    </Button>
                </div>
                {/* Step 1: Basic Info */}
                {currentStep === 1 && (
                    <div className="space-y-4">
                        <div>
                            <label
                                htmlFor="retailer-name"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Retailer Name
                            </label>
                            <Input
                                id="retailer-name"
                                name="Retailer_Name"
                                value={retailerFormAttributes.Retailer_Name}
                                onChange={handleChange}
                                placeholder="Enter retailer name"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('RetailerName', retailerFormAttributes.Retailer_Name, 'required')}
                        </div>
                        {/* <div>
                            <label
                                htmlFor="retailer-franchise"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Franchise
                            </label>
                            <Input
                                name="Franchise"
                                value={retailerFormAttributes.Franchise}
                                onChange={handleChange}
                                type="text"
                                id="retailer-franchise"
                                placeholder="Enter retailer Franchise"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                        </div> */}
                        <div>
                            <label
                                htmlFor="retailer-group"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Group
                            </label>
                            <Input
                                name="Group"
                                value={retailerFormAttributes.Group}
                                onChange={handleChange}
                                type="text"
                                id="retailer-group"
                                placeholder="Enter retailer group"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('RetailerCustomerGroup', retailerFormAttributes.Group, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-sales-region"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Sales Region
                            </label>
                            <Input
                                name="SalesRegion"
                                value={retailerFormAttributes.SalesRegion}
                                onChange={handleChange}
                                type="text"
                                id="retailer-sales-region"
                                placeholder="Enter retailer sales region"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('RetailerSalesRegion', retailerFormAttributes.SalesRegion, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-aftersales-region"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Aftersales Region
                            </label>
                            <Input
                                name="AftersalesRegion"
                                value={retailerFormAttributes.AftersalesRegion}
                                onChange={handleChange}
                                type="text"
                                id="retailer-aftersales-region"
                                placeholder="Enter retailer aftersales region"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('RetailerAftersalesRegion', retailerFormAttributes.AftersalesRegion, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-rtm-region"
                                className="block text-sm font-medium text-gray-700"
                            >
                                RTM Region
                            </label>
                            <Input
                                name="RTMRegion"
                                value={retailerFormAttributes.RTMRegion}
                                onChange={handleChange}
                                type="text"
                                id="retailer-rtm-region"
                                placeholder="Enter retailer rtm region"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('RetailerRtmRegion', retailerFormAttributes.RTMRegion, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-fse-region"
                                className="block text-sm font-medium text-gray-700"
                            >
                                FSE Region
                            </label>
                            <Input
                                name="FSERegion"
                                value={retailerFormAttributes.FSERegion}
                                onChange={handleChange}
                                type="text"
                                id="retailer-fse-region"
                                placeholder="Enter retailer fse region"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('RetailerFseRegion', retailerFormAttributes.FSERegion, 'required')}
                        </div>
                    </div>
                )}

                {/* Step 2: Address Info */}
                {currentStep === 2 && (
                    <div className="space-y-4">
                        <div>
                            <label
                                htmlFor="retailer-phone"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Phone
                            </label>
                            <Input
                                name="Phone"
                                value={retailerFormAttributes.Phone}
                                onChange={handleChange}
                                type="tel"
                                id="retailer-phone"
                                placeholder="Enter retailer phone"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('RetailerPhone', retailerFormAttributes.Phone, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-address1"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Address1
                            </label>
                            <Input
                                name="Address1"
                                value={retailerFormAttributes.Address1}
                                onChange={handleChange}
                                type="text"
                                id="retailer-address1"
                                placeholder="Enter address line1"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('FirstLineOfAddress', retailerFormAttributes.Address1, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-address2"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Address2
                            </label>
                            <Input
                                name="Address2"
                                value={retailerFormAttributes.Address2}
                                onChange={handleChange}
                                type="text"
                                id="retailer-address2"
                                placeholder="Enter address line2"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {/* {validator.message('SecondLineOfAddress', retailerFormAttributes.Address2, 'required')} */}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-town"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Town
                            </label>
                            <Input
                                name="Town"
                                value={retailerFormAttributes.Town}
                                onChange={handleChange}
                                type="text"
                                id="retailer-town"
                                placeholder="Enter retailer town"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('Town', retailerFormAttributes.Town, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-county"
                                className="block text-sm font-medium text-gray-700"
                            >
                                County
                            </label>
                            <Input
                                name="County"
                                value={retailerFormAttributes.County}
                                onChange={handleChange}
                                type="text"
                                id="retailer-county"
                                placeholder="Enter retailer county"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('County', retailerFormAttributes.County, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-postcode"
                                className="block text-sm font-medium text-gray-700"
                            >
                                PostCode
                            </label>
                            <Input
                                name="PostCode"
                                value={retailerFormAttributes.PostCode}
                                onChange={handleChange}
                                type="text"
                                id="retailer-postcode"
                                placeholder="Enter retailer postcode"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                pattern="^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A
Za-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9]?[A-Za-z])))) [0-9][A-Za-z]{2})$"
                                maxLength={9}
                            />
                            {validator.message('PostCode', retailerFormAttributes.PostCode, 'required')}
                        </div>
                    </div>
                )}

                {/* Step 3: Contact Info */}
                {currentStep === 3 && (
                    <div className="space-y-4">
                        <div>
                            <label
                                htmlFor="retailer-latlong"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Latitude/Longitude
                            </label>
                            <Input
                                name="Latitude"
                                value={retailerFormAttributes.Latitude}
                                onChange={handleChange}
                                type="text"
                                id="retailer-lat"
                                placeholder="Enter retailer latitude longitude"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                pattern="^-?[0-9]\d*(\.\d+)?$"
                            />
                            <Input
                                name="Longitude"
                                value={retailerFormAttributes.Longitude}
                                onChange={handleChange}
                                type="text"
                                id="retailer-long"
                                placeholder="Enter retailer latitude longitude"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                pattern="^-?[0-9]\d*(\.\d+)?$"
                            />
                            {validator.message('latitude', retailerFormAttributes.Latitude, 'required')}
                            {validator.message('longitude', retailerFormAttributes.Longitude, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-email"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Contact Email
                            </label>
                            <Input
                                name="ContactEmail"
                                value={retailerFormAttributes.ContactEmail}
                                onChange={handleChange}
                                type="email"
                                id="retailer-email"
                                placeholder="Enter retailer email"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('ContactEmail', retailerFormAttributes.ContactEmail, 'required|email')}
                        </div>
                        {/* <div>
                            <label
                                htmlFor="retailer-week-timings"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Open Weekdays
                            </label>
                            <Input
                                name="OpenMonday"
                                value={retailerFormAttributes.OpenMonday}
                                onChange={handleChange}
                                type="text"
                                id="retailer-week-timings"
                                placeholder="Enter retailer weekday timings"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-saturday-timings"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Open Saturday
                            </label>
                            <Input
                                name="OpenSaturday"
                                value={retailerFormAttributes.OpenSaturday}
                                onChange={handleChange}
                                type="text"
                                id="retailer-saturday-timings"
                                placeholder="Enter retailer saturday timings"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="retailer-sunday-timings"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Open Sunday
                            </label>
                            <Input
                                name="OpenSunday"
                                value={retailerFormAttributes.OpenSunday}
                                onChange={handleChange}
                                type="text"
                                id="retailer-sunday-timings"
                                placeholder="Enter retailer sunday timings"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                        </div> */}
                        {/* Open Weekday */}
                        <div>
                            <label
                                htmlFor="retailer-monday-timings"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Open Weekdays (Start - End)
                            </label>
                            <div className="flex space-x-4">
                                <input
                                    name="OpenMondayStart"
                                    value={formatDateForInput(retailerFormAttributes.OpenMondayStart)}
                                    onChange={handleChange}
                                    type="datetime-local"
                                    id="retailer-monday-start"
                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                />
                                <input
                                    name="OpenMondayEnd"
                                    value={formatDateForInput(retailerFormAttributes.OpenMondayEnd)}
                                    onChange={handleChange}
                                    type="datetime-local"
                                    id="retailer-monday-end"
                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                />
                            </div>
                        </div>

                        {/* Open Saturday */}
                        <div>
                            <label
                                htmlFor="retailer-saturday-timings"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Open Saturday (Start - End)
                            </label>
                            <div className="flex space-x-4">
                                <input
                                    name="OpenSaturdayStart"
                                    value={formatDateForInput(retailerFormAttributes.OpenSaturdayStart)}
                                    onChange={handleChange}
                                    type="datetime-local"
                                    id="retailer-saturday-start"
                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                />
                                <input
                                    name="OpenSaturdayEnd"
                                    value={formatDateForInput(retailerFormAttributes.OpenSaturdayEnd)}
                                    onChange={handleChange}
                                    type="datetime-local"
                                    id="retailer-saturday-end"
                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                />
                            </div>
                        </div>

                        {/* Open Sunday */}
                        <div>
                            <label
                                htmlFor="retailer-sunday-timings"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Open Sunday (Start - End)
                            </label>
                            <div className="flex space-x-4">
                                <input
                                    name="OpenSundayStart"
                                    value={formatDateForInput(retailerFormAttributes.OpenSundayStart)}
                                    onChange={handleChange}
                                    type="datetime-local"
                                    id="retailer-sunday-start"
                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                />
                                <input
                                    name="OpenSundayEnd"
                                    value={formatDateForInput(retailerFormAttributes.OpenSundayEnd)}
                                    onChange={handleChange}
                                    type="datetime-local"
                                    id="retailer-sunday-end"
                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                />
                            </div>
                        </div>
                    </div>
                )}

                {/* Submit Button on Final Step */}
                {currentStep === 3 && (
                    <div className="mt-4">
                        <Button
                            type="submit"
                            className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
                        >
                            Submit
                        </Button>
                    </div>
                )}
            </form>
        </div>
    );
};
