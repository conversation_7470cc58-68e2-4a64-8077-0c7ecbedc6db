'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useTable, useSortBy, useGlobalFilter, useFilters, usePagination } from 'react-table';
import { ChevronDown, ChevronUp, MapPinIcon } from 'lucide-react';
import Select from 'react-select';
import { toast } from 'sonner';

import { Spinner } from '@aa/ui/core/spinner';
import { SupplierData } from '@aa/admin-helpers';
import { Button, Input, YesNoDialog, Popover, PopoverTrigger, PopoverContent, Dialog, DialogContent, DialogHeader, DialogTitle } from '../../components/ui-component/ui';
import { useRetailerService } from '../../../service/retailer.service';
import { SelectInterface, TableRow } from '../../../model';
import CSHViewModify from './CSH/CSHViewModify';
import { cn, Utils } from '../../lib/utils';
import { CustomLoader } from '../../components/ui-component/custom-loader';
import { TempRetailerEdit } from './TempRetailerEdit';

const hiddenColumns = [
    'supplier.supplierAddrLine2',
    'supplier.supplierAddrLine3',
    'supplier.supplierAddrLine4',
    'supplier.supInwdPostCode',
    'supplier.supOutwdPostCode',
    'supplierLatLong.latitude',
    'supplierLatLong.longitude'
];
const createColumns = (selectedSnapshotId: string, refreshCallback: (selectedSnapshotId: string) => void) => {
    return [
        {
            Header: 'Info',
            accessor: 'errors',
            Cell: ({ value }) => {
                if (!value) {
                    return (
                        <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 bg-gray-500 rounded-full"></div>
                            {/* <div className="text-gray-500">Unverified</div> */}
                        </div>
                    );
                }
                return value.length ? (
                    <Popover>
                        <PopoverTrigger asChild>
                            <div
                                className="flex items-center space-x-2 cursor-pointer"
                                title="Errors: click to view"
                            >
                                <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                                {/* <div className="text-red-500">Error</div> */}
                            </div>
                        </PopoverTrigger>
                        <PopoverContent
                            align="start"
                            className="bg-white rounded-lg shadow-lg min-w-[400px]"
                        >
                            <ul className="list-disc list-inside text-sm">
                                {value.map((error: string, idx: number) => (
                                    <li key={idx}>{error}</li>
                                ))}
                            </ul>
                        </PopoverContent>
                    </Popover>
                ) : (
                    <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                        {/* <div className="text-green-500">Valid</div> */}
                    </div>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'CI Code',
            accessor: 'CiCode'
        },
        {
            Header: 'Name',
            accessor: 'supplier.supplierName'
        },
        {
            Header: 'Address',
            accessor: 'supplier.supplierAddrLine1',
            Cell: ({ row }) => (
                <div>
                    <div>{row.original.supplier.supplierAddrLine1}</div>
                    <div>{row.original.supplier.supplierAddrLine2}</div>
                    <div>{row.original.supplier.supplierAddrLine3}</div>
                    <div>{row.original.supplier.supplierAddrLine4}</div>
                    <div>
                        {row.original.supplier.supInwdPostCode} {row.original.supplier.supOutwdPostCode}
                    </div>
                </div>
            ),
            disableSortBy: true
        },
        {
            Header: 'Weekday Timings',
            accessor: 'supplier.supWkOpenTime', //+ '-' + 'supplier.supWkCloseTime',
            Cell: ({ row }) => {
                return Utils.getHHMM(row.original.supplier.supWkOpenTime) + '-' + Utils.getHHMM(row.original.supplier.supWkCloseTime);
            }
        },
        {
            Header: 'Saturday Timings',
            accessor: 'supplier.supSatOpenTime', // + '-' + 'supplier.supSatCloseTime',
            Cell: ({ row }) => {
                return Utils.getHHMM(row.original.supplier.supSatOpenTime) + '-' + Utils.getHHMM(row.original.supplier.supSatCloseTime);
            }
        },
        {
            Header: 'Sunday Timings',
            accessor: 'supplier.supSunOpenTime', // + '-' + 'supplier.supSunCloseTime',
            Cell: ({ row }) => {
                return Utils.getHHMM(row.original.supplier.supSunOpenTime) + '-' + Utils.getHHMM(row.original.supplier.supSunCloseTime);
            }
        },
        {
            Header: 'Account No',
            accessor: 'supplier.supplierAccountNo'
        },
        {
            Header: 'Email',
            accessor: 'supplier.email'
        },
        {
            Header: 'Network Code',
            accessor: 'mbRetailer.groupUc'
        },
        {
            Header: 'Sales Region',
            accessor: 'mbRetailer.region1'
        },
        {
            Header: 'After sales Region',
            accessor: 'mbRetailer.region2'
        },
        {
            Header: 'RTM Region',
            accessor: 'mbRetailer.region3'
        },
        {
            Header: 'FSE Region',
            accessor: 'mbRetailer.region4'
        },
        {
            Header: 'Phone',
            accessor: 'supplierResourceTel.resourceTelNo'
        },
        {
            Header: 'Latitude/Longitude',
            accessor: 'latLong',
            Cell: ({ row }) => (
                <div className="flex">
                    {row.original.supplierLatLong.latitude}&nbsp;/&nbsp;{row.original.supplierLatLong.longitude}&nbsp;
                    <a
                        className="flex-1"
                        href={'https://maps.google.com/?q=' + row.original.supplierLatLong.latitude + ',' + row.original.supplierLatLong.longitude}
                        target="_blank"
                        rel="noreferrer"
                        title="Go to Map"
                    >
                        <MapPinIcon />
                    </a>
                </div>
            ),
            disableSortBy: true
        },
        {
            Header: 'Actions',
            Cell: ({ row }) => {
                const [showEditDialog, setShowEditDialog] = useState(false);
                return (
                    <>
                        <Button
                            variant="outline"
                            size="sm"
                            className="bg-blue-600 text-white"
                            onClick={() => setShowEditDialog(true)}
                        >
                            Edit
                        </Button>
                        <Dialog
                            open={showEditDialog}
                            onOpenChange={setShowEditDialog}
                        >
                            <DialogContent
                                className="bg-muted p-6 rounded-lg shadow-lg bg-white"
                                aria-describedby={undefined}
                            >
                                <DialogHeader>
                                    <DialogTitle> Edit Record </DialogTitle>
                                </DialogHeader>
                                <div id="dialog-description">
                                    <TempRetailerEdit
                                        editRecord={row.original}
                                        snapshotId={selectedSnapshotId}
                                        updateCallback={() => {
                                            setShowEditDialog(false);
                                            refreshCallback(selectedSnapshotId);
                                        }}
                                    />
                                </div>
                            </DialogContent>
                        </Dialog>
                    </>
                );
            }
        },
        ...hiddenColumns.map((column) => ({
            Header: column,
            accessor: column
        }))
    ];
};

const RetailersView = () => {
    const [snapshotIdOptions, setSnapshotIdOptions] = useState<SelectInterface[]>([]);
    const [selectedSnapshot, setSelectedSnapshot] = useState<SelectInterface>();
    const [searchValue, setSearchValue] = useState('');
    const RetailerService = useRetailerService();

    const [tempRetailersData, setTempRetailersData] = useState<SupplierData[]>([]);
    const [loadingTempRetailers, setLoadingTempRetailers] = useState(true);

    const [showUploadToCSHPrompt, setShowUploadToCSHPrompt] = useState(false);

    const [pageLevelLoading, setPageLevelLoading] = useState(false);

    const fetchTempRetailerData = (selectSnapshotId: string) => {
        setLoadingTempRetailers(true);
        RetailerService.viewRetailers(selectSnapshotId)
            .then((resp) => setTempRetailersData(resp.data))
            .catch((error) => console.error(error))
            .finally(() => setLoadingTempRetailers(false));
    };

    const columns = useMemo(() => {
        if (!selectedSnapshot) {
            return [];
        }

        const refreshCallback = (selectedSnapshotId: string) => {
            fetchTempRetailerData(selectedSnapshotId);
        };

        return createColumns(selectedSnapshot?.value, refreshCallback);
    }, [selectedSnapshot]);

    const tableInstance = useTable(
        {
            columns,
            data: tempRetailersData,
            initialState: { pageIndex: 0, pageSize: 10, hiddenColumns }
        },
        useFilters,
        useGlobalFilter,
        useSortBy,
        usePagination
    );

    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        prepareRow,
        setGlobalFilter,
        page,
        canPreviousPage,
        canNextPage,
        pageOptions,
        pageCount,
        gotoPage,
        nextPage,
        previousPage,
        setPageSize,
        state: { pageIndex, pageSize }
    } = tableInstance;

    const handleSnapshotChange = (selectedOption: SelectInterface) => {
        setSelectedSnapshot(selectedOption);
        fetchTempRetailerData(selectedOption.value);
    };

    const validateTempRetailersData = () => {
        let isValid = true;
        const retailersData = [...tempRetailersData];
        retailersData.forEach((retailer: TableRow<SupplierData>) => {
            retailer.errors = [];
            // CiCode - required and should have 6 character length
            if (!retailer.CiCode || retailer.CiCode.length > 6) {
                retailer.errors.push('CI Code is required, should be 6 chars or less');
            }

            // Retailer Name - required
            if (!retailer.supplier.supplierName) {
                retailer.errors.push('Retailer Name is required');
            }

            // Group - required
            if (!retailer.mbRetailer.groupUc) {
                retailer.errors.push('Network Code is required');
            }

            // Phone - required
            if (!retailer.supplierResourceTel.resourceTelNo) {
                retailer.errors.push('Phone is required');
            }

            // Address1 - required
            if (!retailer.supplier.supplierAddrLine1 || (!retailer.supplier.supplierAddrLine2 && !retailer.supplier.supplierAddrLine3)) {
                retailer.errors.push('Address is required');
            }

            // // Town - required - seems this can be empty
            // if (!retailer.supplier.supplierAddrLine3) {
            //     retailer.errors.push('Town is required');
            // }

            // // County - required
            // if (!retailer.supplier.supplierAddrLine4) {
            //     retailer.errors.push('County is required');
            // }

            // PostCode - required
            if (!retailer.supplier.supOutwdPostCode) {
                retailer.errors.push('PostCode is required');
            }

            // Latitude - required
            if (!retailer.supplierLatLong.latitude) {
                retailer.errors.push('Latitude is required');
            }

            // Longitude - required
            if (!retailer.supplierLatLong.longitude) {
                retailer.errors.push('Longitude is required');
            }

            // ContactEmail - required
            if (!retailer.supplier.email) {
                retailer.errors.push('Email is required');
            }

            // supplierAccountNo - required and should have 6 character length
            if (!retailer.supplier.supplierAccountNo || retailer.supplier.supplierAccountNo.length > 6) {
                retailer.errors.push('Account No is required, should be 6 chars or less');
            }

            // mbRetailer.regionParId - required and should be greater than 0
            if (!retailer.mbRetailer.regionParId || retailer.mbRetailer.regionParId < 1) {
                retailer.errors.push('regionParId is required and should be greater than 0');
            }

            isValid = isValid && retailer.errors.length === 0;
        });
        setTempRetailersData(retailersData);
        return isValid;
    };

    const handleUploadToCSH = () => {
        if (selectedSnapshot) {
            setPageLevelLoading(true);
            RetailerService.insertRetailersToCSH(selectedSnapshot.value)
                .then(() => {
                    toast.success('Retailer record inserted successfully to CSH!');
                })
                .catch((error) => {
                    console.error(error);
                    toast.error('Something went wrong!');
                })
                .finally(() => {
                    setPageLevelLoading(false);
                });
        }
    };

    const attemptUploadToCSH = () => {
        if (!selectedSnapshot) {
            toast.error('Please choose a snapshot first!');
            return;
        }

        if (!tempRetailersData.length) {
            toast.error('No records to insert!');
            return;
        }

        if (!validateTempRetailersData()) {
            toast.error('Some rows have invalid data! Please correct the red ones and try again.');
            return;
        }

        setShowUploadToCSHPrompt(true);
    };

    useEffect(() => {
        RetailerService.listSnapshots()
            .then((resp) => {
                const allSnapshots = resp.map((snapshot) => ({
                    label: snapshot._id + ' Timestamp ' + snapshot.timestamp,
                    value: snapshot._id
                }));
                setSnapshotIdOptions(allSnapshots);
            })
            .catch((error) => console.error(error));
    }, []);

    return (
        <div className="space-y-8 p-4">
            {/* Page Level Loader */}
            <CustomLoader show={pageLevelLoading} />

            {/* Temporary Retailers Section */}
            <div>
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold">Temporary Retailers</h2>
                </div>
                <div className="flex items-center justify-between  mb-4">
                    <label
                        htmlFor="selectSnapshot"
                        className="block text-sm font-semibold text-muted"
                    >
                        Choose Snapshot
                    </label>
                    <Select
                        name="selectSnapshot"
                        options={snapshotIdOptions}
                        value={selectedSnapshot}
                        onChange={handleSnapshotChange}
                        placeholder="Select snapashot "
                        className="react-select-container"
                        classNamePrefix="react-select"
                        styles={{
                            control: (base) => ({
                                ...base,
                                width: '600px'
                            })
                        }}
                    />
                    <Input
                        placeholder="Search..."
                        onChange={(e) => {
                            setGlobalFilter(e.target.value);
                            setSearchValue(e.target.value);
                        }}
                        className="max-w-xs ml-2"
                        value={searchValue}
                    />
                </div>
                <div className="overflow-x-auto bg-white shadow-lg rounded-lg">
                    {selectedSnapshot === undefined ? (
                        <div className="text-center">
                            <br />
                            <br />
                            <br />
                            Please choose snapshot to view Temporary Retailers...
                            <br />
                            <br />
                            <br />
                            <br />
                        </div>
                    ) : loadingTempRetailers ? (
                        <div className="text-center">
                            <br />
                            <br />
                            Loading Temporary Retailers for {selectedSnapshot.label}..
                            <br />
                            <br />
                            Please wait...{' '}
                            <Spinner
                                className="mr-3"
                                style={{ display: 'inline' }}
                            />
                            <br />
                            <br />
                            <br />
                        </div>
                    ) : (
                        <>
                            {/* Temporary Retailers Table */}
                            <table
                                {...getTableProps()}
                                className="min-w-full table-auto"
                            >
                                <thead>
                                    {headerGroups.map((headerGroup) => (
                                        <tr
                                            {...headerGroup.getHeaderGroupProps()}
                                            className="border-b"
                                        >
                                            {headerGroup.headers.map((column) => (
                                                <th
                                                    {...column.getHeaderProps(column.getSortByToggleProps())}
                                                    className="py-3 px-4 text-left text-sm font-semibold cursor-pointer text-muted"
                                                >
                                                    {column.render('Header')}
                                                    <span className="ml-1">
                                                        {column.isSorted ? column.isSortedDesc ? <ChevronDown className="inline w-4 h-4" /> : <ChevronUp className="inline w-4 h-4" /> : null}
                                                    </span>
                                                </th>
                                            ))}
                                        </tr>
                                    ))}
                                </thead>

                                <tbody {...getTableBodyProps()}>
                                    {page.map((row) => {
                                        prepareRow(row);
                                        let extraRowClass = '';
                                        if (Array.isArray(row.original.errors)) {
                                            extraRowClass = row.original.errors.length > 0 ? 'bg-red-100' : 'bg-green-100';
                                        }
                                        return (
                                            <tr
                                                {...row.getRowProps()}
                                                className={cn('border-b hover:bg-muted/50', extraRowClass)}
                                            >
                                                {row.cells.map((cell) => (
                                                    <td
                                                        {...cell.getCellProps()}
                                                        className="py-2 px-4 text-sm text-muted"
                                                    >
                                                        {cell.render('Cell')}
                                                    </td>
                                                ))}
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </>
                    )}
                </div>
                {selectedSnapshot && !loadingTempRetailers && tempRetailersData.length > 0 && (
                    <>
                        {/* Pagination controls */}
                        <div className="flex items-center justify-between px-4 py-2 bg-white shadow-lg rounded-lg mt-2">
                            <div>
                                <Button
                                    onClick={() => gotoPage(0)}
                                    disabled={!canPreviousPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600"
                                >
                                    {'<<'}
                                </Button>
                                <Button
                                    onClick={() => previousPage()}
                                    disabled={!canPreviousPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                >
                                    {'<'}
                                </Button>
                                <Button
                                    onClick={() => nextPage()}
                                    disabled={!canNextPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                >
                                    {'>'}
                                </Button>
                                <Button
                                    onClick={() => gotoPage(pageCount - 1)}
                                    disabled={!canNextPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                >
                                    {'>>'}
                                </Button>
                            </div>
                            <div>
                                <span>
                                    Page&nbsp;
                                    <strong>
                                        {pageIndex + 1} of {pageOptions.length}&nbsp;
                                    </strong>
                                </span>
                                <span>
                                    | Go to page:&nbsp;
                                    <input
                                        type="number"
                                        value={pageIndex + 1}
                                        onChange={(e) => {
                                            const page = e.target.value ? Number(e.target.value) - 1 : 0;
                                            gotoPage(page);
                                        }}
                                        style={{ width: '50px' }}
                                        className="border border-input px-1 py-1 text-center"
                                    />
                                </span>
                            </div>
                            <div>
                                Rows per page:&nbsp;
                                <select
                                    value={pageSize}
                                    onChange={(e) => {
                                        setPageSize(Number(e.target.value));
                                    }}
                                    className="border border-input px-3 py-1"
                                >
                                    {[10, 20, 30, 40, 50].map((pageSize) => (
                                        <option
                                            key={pageSize}
                                            value={pageSize}
                                        >
                                            Show {pageSize}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </>
                )}
            </div>

            {/* Insert to CSH Button section */}
            <div>
                <YesNoDialog
                    title="Insert to CSH"
                    prompt="Do you want to insert the temporary retailers to CSH?"
                    yesClicked={() => {
                        handleUploadToCSH();
                        setShowUploadToCSHPrompt(false);
                    }}
                    noClicked={() => {
                        // Does Nothing
                        setShowUploadToCSHPrompt(false);
                    }}
                    open={showUploadToCSHPrompt}
                />
                <Button
                    variant="outline"
                    size="sm"
                    className="bg-blue-600 text-white"
                    onClick={() => attemptUploadToCSH()}
                >
                    Insert to CSH
                </Button>
            </div>

            {/* CSH Records Table */}
            {!pageLevelLoading && <CSHViewModify />}
        </div>
    );
};

export default RetailersView;
