'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '../../../components/ui-component/ui/button';
import { Input } from '../../../components/ui-component/ui/input';
import SimpleReactValidator from 'simple-react-validator';
import { cn } from '../../../lib/utils';
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle } from '../../../components/ui-component/ui/dialog';
import { useSupplierService } from '../../../../service';
import { format, parseISO } from 'date-fns';
import { toast } from 'sonner';

interface EditViewProps {
    cshData: any;
    refreshCallback: (selectedSupplierNetwork: string) => void;
    setIsLoading: (isLoading: boolean) => void;
}

const EditView: React.FC<EditViewProps> = ({ cshData, refreshCallback, setIsLoading }) => {
    const SupplierService = useSupplierService();
    const [isDialogOpen, setIsDialogOpen] = useState(true);
    const [loading, setLoading] = useState(true);
    const [validator] = useState(new SimpleReactValidator());
    const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
    const [currentStep, setCurrentStep] = useState(1);
    const [retailerFormAttributes, setRetailerFormAttributes] = useState<any>({
        SupResourceId: 0,
        CICode: '',
        Retailer_Name: '',
        Franchise: '',
        Group: '',
        SalesRegion: '',
        AftersalesRegion: '',
        RTMRegion: '',
        FSERegion: '',
        Phone: '',
        Address1: '',
        Address2: '',
        Town: '',
        County: '',
        PostCode: '',
        Latitude: '',
        Longitude: '',
        Lat_Long: '',
        ContactEmail: '',
        OpenMonday: '',
        OpenTuesday: '',
        OpenWednesday: '',
        OpenThursday: '',
        OpenFriday: '',
        OpenSaturday: '',
        OpenSunday: '',
        OpenMondayStart: '',
        OpenMondayEnd: '',
        OpenSaturdayStart: '',
        OpenSaturdayEnd: '',
        OpenSundayStart: '',
        OpenSundayEnd: ''
    });
    const [lastStep, setLastStep] = useState<number>(0);

    const nextStep = () => {
        if (!validateAtStep(currentStep)) {
            setRetailerFormAttributes((prev) => ({ ...prev })); // Trigger re-render to show validation messages
            setLastStep(currentStep); // Save the last step validated
            return;
        }
        setCurrentStep((prev) => Math.min(prev + 1, 3));
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;

        setRetailerFormAttributes((prev: any) => ({
            ...prev,
            [name]: value
        }));

        validator.showMessageFor(name);
    };

    const validateAtStep = (step: number): boolean => {
        if (step === lastStep) {
            return true;
        }
        switch (step) {
            case 1:
                validator.showMessageFor('RetailerName');
                validator.showMessageFor('RetailerRtmRegion');
                validator.showMessageFor('RetailerFseRegion');
                validator.showMessageFor('RetailerPhone');
                return validator.fieldValid('RetailerName') && validator.fieldValid('RetailerRtmRegion') && validator.fieldValid('RetailerFseRegion') && validator.fieldValid('RetailerPhone');
            case 2:
                validator.showMessageFor('FirstLineOfAddress');
                validator.showMessageFor('Town');
                validator.showMessageFor('County');
                validator.showMessageFor('PostCode');
                return validator.fieldValid('FirstLineOfAddress') && validator.fieldValid('Town') && validator.fieldValid('County') && validator.fieldValid('PostCode');
            case 3:
                validator.showMessageFor('Latitude');
                validator.showMessageFor('Longitude');
                validator.showMessageFor('ContactEmail');
                return validator.fieldValid('Longitude') && validator.fieldValid('Latitude') && validator.fieldValid('ContactEmail');
            default:
                return false;
        }
    };

    useEffect(() => {
        if (isDialogOpen && cshData) {
            setCurrentStep(1);
            setEditFields(cshData);
        }
        // validator.showMessages();
    }, [cshData, isDialogOpen]);

    const setEditFields = (rowData: any) => {
        setRetailerFormAttributes({
            SupResourceId: rowData.supResourceId,
            CICode: rowData?.CiCode,
            Retailer_Name: rowData?.supplier?.supplierName,
            Franchise: rowData?.Franchise,
            Group: rowData?.mbRetailer?.groupUc,
            SalesRegion: rowData?.mbRetailer?.region1,
            AftersalesRegion: rowData?.mbRetailer?.region2,
            RTMRegion: rowData?.mbRetailer?.region3,
            FSERegion: rowData?.mbRetailer?.region4,
            Phone: rowData?.supplierResourceTel?.resourceTelNo,
            Address1: rowData?.supplier?.supplierAddrLine1,
            Address2: rowData?.supplier?.supplierAddrLine2,
            Town: rowData?.supplier?.supplierAddrLine3,
            County: rowData?.supplier?.supplierAddrLine4,
            PostCode: rowData?.supplier?.supInwdPostCode + ' ' + rowData?.supplier?.supOutwdPostCode,
            Latitude: rowData?.supplierLatLong?.latitude,
            Longitude: rowData?.supplierLatLong?.longitude,
            Lat_Long: rowData?.supplierLatLong?.latitude + '/' + rowData?.supplierLatLong?.longitude,
            ContactEmail: rowData?.supplier?.email,
            OpenMondayStart: rowData?.supplier?.supWkOpenTime,
            OpenMondayEnd: rowData?.supplier?.supWkCloseTime,
            OpenSaturdayStart: rowData?.supplier?.supSatOpenTime,
            OpenSaturdayEnd: rowData?.supplier?.supSatCloseTime,
            OpenSundayStart: rowData?.supplier?.supSunOpenTime,
            OpenSundayEnd: rowData?.supplier?.supSunCloseTime,
            OpenMonday: rowData?.supplier?.supWkOpenTime + '-' + rowData?.supplier?.supWkCloseTime,
            OpenTuesday: rowData?.supplier?.supWkOpenTime + '-' + rowData?.supplier?.supWkCloseTime,
            OpenWednesday: rowData?.supplier?.supWkOpenTime + '-' + rowData?.supplier?.supWkCloseTime,
            OpenThursday: rowData?.supplier?.supWkOpenTime + '-' + rowData?.supplier?.supWkCloseTime,
            OpenFriday: rowData?.supplier?.supWkOpenTime + '-' + rowData?.supplier?.supWkCloseTime,
            OpenSaturday: rowData?.supplier?.supSatOpenTime + '-' + rowData?.supplier?.supSatCloseTime,
            OpenSunday: rowData?.supplier?.supSunOpenTime + '-' + rowData?.supplier?.supSunCloseTime
        });
    };

    const editClicked = (): void => {
        setIsDialogOpen(true);
    };

    const prevStep = () => {
        setCurrentStep((prev) => Math.max(prev - 1, 1)); // Minimum step: 1
    };

    const formatDateForInput = (datetimeString: string) => {
        if (datetimeString) {
            const parsedDate = parseISO(datetimeString);
            return format(parsedDate, "yyyy-MM-dd'T'HH:mm");
        }
        return '';
    };

    const finalPayload = () => {
        const [postCodeIn, postCodeOut] = retailerFormAttributes.PostCode.split(' ');
        return {
            ...cshData,
            supResourceId: retailerFormAttributes.SupResourceId,
            supplier: {
                ...cshData.supplier,
                supplierName: retailerFormAttributes.Retailer_Name,
                supplierAddrLine1: retailerFormAttributes.Address1,
                supplierAddrLine2: retailerFormAttributes.Address2,
                supplierAddrLine3: retailerFormAttributes.Town,
                supInwdPostCode: postCodeIn,
                supOutwdPostCode: postCodeOut,
                supWkOpenTime: retailerFormAttributes.OpenMondayStart ? format(parseISO(retailerFormAttributes.OpenMondayStart), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") : cshData.supplier.supWkOpenTime,
                supWkCloseTime: retailerFormAttributes.OpenMondayEnd ? format(parseISO(retailerFormAttributes.OpenMondayEnd), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") : cshData.supplier.supWkCloseTime,
                supSatOpenTime: retailerFormAttributes.OpenSaturdayStart ? format(parseISO(retailerFormAttributes.OpenSaturdayStart), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") : cshData.supplier.supSatOpenTime,
                supSatCloseTime: retailerFormAttributes.OpenSaturdayEnd ? format(parseISO(retailerFormAttributes.OpenSaturdayEnd), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") : cshData.supplier.supSatCloseTime,
                supSunOpenTime: retailerFormAttributes.OpenSundayStart ? format(parseISO(retailerFormAttributes.OpenSundayStart), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") : cshData.supplier.supSunOpenTime,
                supSunCloseTime: retailerFormAttributes.OpenMondayEnd ? format(parseISO(retailerFormAttributes.OpenMondayEnd), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") : cshData.supplier.supSunCloseTime,
                email: retailerFormAttributes.ContactEmail
            },
            mbRetailer: {
                ...cshData.mbRetailer,
                region3: retailerFormAttributes.RTMRegion,
                region4: retailerFormAttributes.FSERegion
            },
            supplierResourceTel: {
                ...cshData.supplierResourceTel,
                resourceTelNo: retailerFormAttributes.Phone
            },
            supplierLatLong: {
                ...cshData.supplierLatLong,
                latitude: retailerFormAttributes.Latitude,
                longitude: retailerFormAttributes.Longitude
            }
        };
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        setIsSubmitted(true);
        validator.showMessages();

        if (!retailerFormAttributes.Retailer_Name) {
            validator.showMessages();
            return;
        }

        setTimeout(() => {
            const isFormValid = validator.allValid();
            if (!isFormValid) {
                return;
            }
            setIsLoading(true);
            SupplierService.updateRetailerCSHRecords(finalPayload())
                .then(() => {
                    refreshCallback(cshData.mbRetailer.groupName);
                    toast.success(`Csh record updated successfully!`);
                })
                .catch(() => {
                    toast.error(`Something went wrong :-( !`);
                })
                .finally(() => {
                    setIsDialogOpen(false);
                    setIsLoading(false);
                });
        }, 1000);
    };

    return (
        <>
            <Dialog>
                <DialogTrigger asChild>
                    <Button
                        variant="outline"
                        size="sm"
                        className="bg-blue-600 text-white"
                        onClick={editClicked}
                    >
                        Edit
                    </Button>
                </DialogTrigger>
                {isDialogOpen && (
                    <DialogContent className="bg-white text-black">
                        <DialogHeader>
                            <DialogTitle>Edit Record</DialogTitle>
                        </DialogHeader>
                        <div>
                            <form onSubmit={handleSubmit}>
                                {/* Step Navigation */}
                                <div className="flex justify-between items-center mb-6">
                                    <Button
                                        type="button"
                                        onClick={prevStep}
                                        disabled={currentStep === 1}
                                        className={cn('px-4 py-2 rounded-md text-white', currentStep === 1 ? 'bg-gray-300 cursor-not-allowed' : 'bg-yellow-500 hover:bg-yellow-600')}
                                    >
                                        Previous
                                    </Button>
                                    <span className="text-gray-600">Step {currentStep} of 3</span>
                                    <Button
                                        type="button"
                                        onClick={nextStep}
                                        disabled={currentStep === 3}
                                        className={cn('px-4 py-2 rounded-md text-white', currentStep === 3 ? 'bg-gray-300 cursor-not-allowed' : 'bg-yellow-500 hover:bg-yellow-600')}
                                    >
                                        Next
                                    </Button>
                                </div>

                                {/* Step 1: Basic Info */}
                                {currentStep === 1 && (
                                    <div className="space-y-4">
                                        <div>
                                            <label
                                                htmlFor="retailer-name"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Retailer Name
                                            </label>
                                            <Input
                                                id="retailer-name"
                                                name="Retailer_Name"
                                                value={retailerFormAttributes.Retailer_Name}
                                                onChange={handleChange}
                                                placeholder="Enter retailer name"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('RetailerName', retailerFormAttributes.Retailer_Name, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="retailer-rtm-region"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                RTM Region
                                            </label>
                                            <Input
                                                name="RTMRegion"
                                                value={retailerFormAttributes.RTMRegion}
                                                onChange={handleChange}
                                                type="text"
                                                id="retailer-rtm-region"
                                                placeholder="Enter retailer rtm region"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('RetailerRtmRegion', retailerFormAttributes.RTMRegion, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="retailer-fse-region"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                FSE Region
                                            </label>
                                            <Input
                                                name="FSERegion"
                                                value={retailerFormAttributes.FSERegion}
                                                onChange={handleChange}
                                                type="text"
                                                id="retailer-fse-region"
                                                placeholder="Enter retailer fse region"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('RetailerFseRegion', retailerFormAttributes.FSERegion, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="retailer-phone"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Phone
                                            </label>
                                            <Input
                                                name="Phone"
                                                value={retailerFormAttributes.Phone}
                                                onChange={handleChange}
                                                type="tel"
                                                id="retailer-phone"
                                                placeholder="Enter retailer phone"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('RetailerPhone', retailerFormAttributes.Phone, 'required|phone')}
                                        </div>
                                    </div>
                                )}

                                {/* Step 2: Address Info */}
                                {currentStep === 2 && (
                                    <div className="space-y-4">
                                        <div>
                                            <label
                                                htmlFor="retailer-address1"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Address1
                                            </label>
                                            <Input
                                                name="Address1"
                                                value={retailerFormAttributes.Address1}
                                                onChange={handleChange}
                                                type="text"
                                                id="retailer-address1"
                                                placeholder="Enter address line1"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('FirstLineOfAddress', retailerFormAttributes.Address1, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="retailer-address2"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Address2
                                            </label>
                                            <Input
                                                name="Address2"
                                                value={retailerFormAttributes.Address2}
                                                onChange={handleChange}
                                                type="text"
                                                id="retailer-address2"
                                                placeholder="Enter address line2"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {/* {validator.message('Address2', retailerFormAttributes.Address2, 'required')} */}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="retailer-town"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Town
                                            </label>
                                            <Input
                                                name="Town"
                                                value={retailerFormAttributes.Town}
                                                onChange={handleChange}
                                                type="text"
                                                id="retailer-town"
                                                placeholder="Enter retailer town"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('Town', retailerFormAttributes.Town, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="retailer-postcode"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Post Code
                                            </label>
                                            <Input
                                                name="PostCode"
                                                value={retailerFormAttributes.PostCode}
                                                onChange={handleChange}
                                                type="text"
                                                id="retailer-postcode"
                                                placeholder="Enter retailer postcode"
                                                pattern="^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A
                                                    Za-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9]?[A-Za-z])))) [0-9][A-Za-z]{2})$"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('PostCode', retailerFormAttributes.PostCode, 'required')}
                                        </div>
                                    </div>
                                )}

                                {/* Step 3: Contact Info */}
                                {currentStep === 3 && (
                                    <div className="space-y-4">
                                        <div>
                                            <label
                                                htmlFor="retailer-latlong"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Latitude/Longitude
                                            </label>
                                            <div className="flex space-x-4">
                                                <Input
                                                    name="Latitude"
                                                    value={retailerFormAttributes.Latitude}
                                                    onChange={handleChange}
                                                    type="text"
                                                    id="retailer-lat"
                                                    pattern="^-?[0-9]\d*(\.\d+)?$"
                                                    placeholder="Enter retailer latitude"
                                                    className="w-auto border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                                />
                                                <Input
                                                    name="Longitude"
                                                    value={retailerFormAttributes.Longitude}
                                                    onChange={handleChange}
                                                    type="text"
                                                    id="retailer-long"
                                                    pattern="^-?[0-9]\d*(\.\d+)?$"
                                                    placeholder="Enter retailer longitude"
                                                    className="w-auto border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                                />
                                            </div>
                                            {validator.message('Latitude', retailerFormAttributes.Latitude, 'required')}
                                            {validator.message('Longitude', retailerFormAttributes.Longitude, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="retailer-email"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Contact Email
                                            </label>
                                            <Input
                                                name="ContactEmail"
                                                value={retailerFormAttributes.ContactEmail}
                                                onChange={handleChange}
                                                type="email"
                                                id="retailer-email"
                                                placeholder="Enter retailer email"
                                                className="w-auto border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('ContactEmail', retailerFormAttributes.ContactEmail, 'required|email')}
                                        </div>

                                        {/* Open Weekday */}
                                        <div>
                                            <label
                                                htmlFor="retailer-monday-timings"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Open Weekdays (Start - End)
                                            </label>
                                            <div className="flex space-x-4">
                                                <input
                                                    name="OpenMondayStart"
                                                    value={formatDateForInput(retailerFormAttributes.OpenMondayStart)}
                                                    onChange={handleChange}
                                                    type="datetime-local"
                                                    id="retailer-monday-start"
                                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                                />
                                                <input
                                                    name="OpenMondayEnd"
                                                    value={formatDateForInput(retailerFormAttributes.OpenMondayEnd)}
                                                    onChange={handleChange}
                                                    type="datetime-local"
                                                    id="retailer-monday-end"
                                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                                />
                                            </div>
                                        </div>

                                        {/* Open Saturday */}
                                        <div>
                                            <label
                                                htmlFor="retailer-saturday-timings"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Open Saturday (Start - End)
                                            </label>
                                            <div className="flex space-x-4">
                                                <input
                                                    name="OpenSaturdayStart"
                                                    value={formatDateForInput(retailerFormAttributes.OpenSaturdayStart)}
                                                    onChange={handleChange}
                                                    type="datetime-local"
                                                    id="retailer-saturday-start"
                                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                                />
                                                <input
                                                    name="OpenSaturdayEnd"
                                                    value={formatDateForInput(retailerFormAttributes.OpenSaturdayEnd)}
                                                    onChange={handleChange}
                                                    type="datetime-local"
                                                    id="retailer-saturday-end"
                                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                                />
                                            </div>
                                        </div>

                                        {/* Open Sunday */}
                                        <div>
                                            <label
                                                htmlFor="retailer-sunday-timings"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Open Sunday (Start - End)
                                            </label>
                                            <div className="flex space-x-4">
                                                <input
                                                    name="OpenSundayStart"
                                                    value={formatDateForInput(retailerFormAttributes.OpenSundayStart)}
                                                    onChange={handleChange}
                                                    type="datetime-local"
                                                    id="retailer-sunday-start"
                                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                                />
                                                <input
                                                    name="OpenSundayEnd"
                                                    value={formatDateForInput(retailerFormAttributes.OpenSundayEnd)}
                                                    onChange={handleChange}
                                                    type="datetime-local"
                                                    id="retailer-sunday-end"
                                                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                )}
                                {/* Submit Button on Final Step */}
                                {currentStep === 3 && (
                                    <div className="mt-4">
                                        <Button
                                            type="submit"
                                            // disabled={!validator.allValid()}
                                            className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
                                        >
                                            Submit
                                        </Button>
                                    </div>
                                )}
                            </form>
                        </div>
                    </DialogContent>
                )}
            </Dialog>
        </>
    );
};

export default EditView;
