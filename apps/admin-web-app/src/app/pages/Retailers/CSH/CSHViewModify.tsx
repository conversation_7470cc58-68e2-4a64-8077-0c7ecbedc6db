'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useTable, useSortBy, useGlobalFilter, useFilters, usePagination } from 'react-table';
import { Button } from '../../../components/ui-component/ui/button';
import { Input } from '../../../components/ui-component/ui/input';
import { ChevronDown, ChevronUp, MapPinIcon } from 'lucide-react';
import { SelectInterface, SelectNumInterface } from '../../../../model';
import Select from 'react-select';
import { getTimeString } from './helper';
import { useSupplierService } from '../../../../service';
import EditView from './EditCSH';
import { Spinner } from '@aa/ui/core/spinner';
import { Utils } from '../../../lib/utils';

const CSHViewModify = () => {
    const SupplierService = useSupplierService();
    const [cshRecords, setCSHRecords] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchValue, setSearchValue] = useState<string>('');
    const [selectedSupplierNetwork, setSelectedSupplierNetwork] = useState<SelectInterface | undefined>(undefined);
    const [supplierNetworkOptions, setSupplierNetworkOptions] = useState<SelectInterface[]>([]);

    const createColumns = () => [
        {
            Header: 'Name',
            accessor: 'supplier.supplierName'
        },
        {
            Header: 'Address',
            accessor: 'supplier.supplierAddrLine1',
            Cell: ({ row }: any) => (
                <div>
                    <div>{row.original.supplier.supplierAddrLine1}</div>
                    <div>{row.original.supplier.supplierAddrLine2}</div>
                    <div>{row.original.supplier.supplierAddrLine3}</div>
                    <div>{row.original.supplier.supplierAddrLine4}</div>
                    <div>
                        {row.original.supplier.supInwdPostCode} {row.original.supplier.supOutwdPostCode}
                    </div>
                </div>
            ),
            disableSortBy: true
        },
        {
            Header: 'Weekday Timings',
            accessor: 'supplier.supWkOpenTime', //+ '-' + 'supplier.supWkCloseTime',
            Cell: ({ row }: any) => {
                return Utils.getHHMM(row.original.supplier.supWkOpenTime) + '-' + Utils.getHHMM(row.original.supplier.supWkCloseTime);
            }
        },
        {
            Header: 'Saturday Timings',
            accessor: 'supplier.supSatOpenTime', // + '-' + 'supplier.supSatCloseTime',
            Cell: ({ row }: any) => {
                return Utils.getHHMM(row.original.supplier.supSatOpenTime) + '-' + Utils.getHHMM(row.original.supplier.supSatCloseTime);
            }
        },
        {
            Header: 'Sunday Timings',
            accessor: 'supplier.supSunOpenTime', // + '-' + 'supplier.supSunCloseTime',
            Cell: ({ row }: any) => {
                return Utils.getHHMM(row.original.supplier.supSunOpenTime) + '-' + Utils.getHHMM(row.original.supplier.supSunCloseTime);
            }
        },
        {
            Header: 'Account No',
            accessor: 'supplier.supplierAccountNo'
        },
        {
            Header: 'Email',
            accessor: 'supplier.email'
        },
        {
            Header: 'Network Code',
            accessor: 'mbRetailer.groupUc'
        },
        {
            Header: 'Sales Region',
            accessor: 'mbRetailer.region1'
        },
        {
            Header: 'After sales Region',
            accessor: 'mbRetailer.region2'
        },
        {
            Header: 'RTM Region',
            accessor: 'mbRetailer.region3'
        },
        {
            Header: 'FSE Region',
            accessor: 'mbRetailer.region4'
        },
        {
            Header: 'Phone',
            accessor: 'supplierResourceTel.resourceTelNo'
        },
        {
            Header: 'Latitude/Longitude',
            accessor: 'latlong',
            Cell: ({ row }: any) => (
                <div className="flex">
                    {row.original.supplierLatLong?.latitude && row.original.supplierLatLong?.longitude ? row.original.supplierLatLong?.latitude + '/' + row.original.supplierLatLong?.longitude : '--'}
                    {row.original.supplierLatLong?.latitude && row.original.supplierLatLong?.longitude && (
                        <a
                            className="flex-1"
                            href={'https://maps.google.com/?q=' + row.original.supplierLatLong?.latitude + ',' + row.original.supplierLatLong?.longitude}
                            target="_blank"
                            rel="noreferrer"
                            title="Go to Map"
                        >
                            <MapPinIcon />
                        </a>
                    )}
                </div>
            ),
            disableSortBy: true
        },
        {
            Header: 'Actions',
            Cell: ({ row }: any) => (
                <EditView
                    cshData={row.original}
                    refreshCallback={refreshCallback}
                    setIsLoading={setLoading}
                />
            )
        }
    ];

    const handleSupplierNetworkChange = (selectedOption: any) => {
        if (selectedOption) {
            setGlobalFilter('');
            setSearchValue('');
            setSelectedSupplierNetwork(selectedOption);
            fetchRetailerCSHRecord(selectedOption.value);
        }
    };

    const fetchRetailerCSHRecord = async (supplierNetworkId: string | undefined) => {
        setLoading(true);

        SupplierService.fetchRetailerCSHRecords(supplierNetworkId)
            .then((cshResponse: any) => {
                setCSHRecords(cshResponse);
            })
            .catch((error) => console.error(error))
            .finally(() => setLoading(false));
    };

    const fetchSupplierNetwork = () => {
        SupplierService.fetchSupplierNetworks()
            .then((resp) => {
                const allSupplierNetworks = resp.map((supplierNetwork) => ({
                    label: supplierNetwork.supNetworkName,
                    value: supplierNetwork.supNetworkCode
                }));
                setSupplierNetworkOptions(allSupplierNetworks);
            })
            .catch(console.error)
            .finally(() => setLoading(false));
    };

    useEffect(() => {
        fetchSupplierNetwork();
    }, []);

    const columns = useMemo(() => createColumns(), []);

    const cshTableInstance = useTable(
        {
            columns,
            data: cshRecords,
            initialState: { pageIndex: 0, pageSize: 10 }
        },
        useFilters,
        useGlobalFilter,
        useSortBy,
        usePagination
    );

    const {
        getTableProps: getCshTableProps,
        getTableBodyProps: getCshTableBodyProps,
        headerGroups: cshHeaderGroups,
        rows: cshRows,
        prepareRow: prepareCshRow,
        setGlobalFilter,
        page,
        canPreviousPage,
        canNextPage,
        pageOptions,
        pageCount,
        gotoPage,
        nextPage,
        previousPage,
        setPageSize,
        state: { pageIndex, pageSize }
    } = cshTableInstance;

    const refreshCallback = (selectedSupplierNetwork: string) => {
        fetchRetailerCSHRecord(selectedSupplierNetwork);
    };

    return (
        <div>
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">CSH Records</h2>
            </div>
            <div className="flex items-center justify-between  mb-4">
                <label
                    htmlFor="customerGroup"
                    className="block text-sm font-semibold text-muted"
                >
                    Choose Supplier Network
                </label>
                <Select
                    name="supNetworkID"
                    options={supplierNetworkOptions}
                    value={selectedSupplierNetwork} // Properly set value
                    onChange={handleSupplierNetworkChange}
                    placeholder="Select supplier network"
                    className="react-select-container"
                    classNamePrefix="react-select"
                />
                <Input
                    placeholder="Search..."
                    onChange={(e) => {
                        setGlobalFilter(e.target.value);
                        setSearchValue(e.target.value);
                    }}
                    className="max-w-xs ml-2"
                    value={searchValue}
                />
            </div>

            <div className="overflow-x-auto bg-white shadow-lg rounded-lg">
                {selectedSupplierNetwork === undefined ? (
                    <div className="text-center">
                        <br />
                        <br />
                        <br />
                        Please choose supplier network to view CSH...
                        <br />
                        <br />
                        <br />
                        <br />
                    </div>
                ) : loading ? (
                    <div className="text-center">
                        <br />
                        <br />
                        Loading CSH for {selectedSupplierNetwork.label}..
                        <br />
                        <br />
                        Please wait...{' '}
                        <Spinner
                            className="mr-3"
                            style={{ display: 'inline' }}
                        />
                        <br />
                        <br />
                        <br />
                    </div>
                ) : (
                    <>
                        <table
                            {...getCshTableProps()}
                            className="min-w-full table-auto"
                        >
                            <thead>
                                {cshHeaderGroups.map((headerGroup) => (
                                    <tr
                                        {...headerGroup.getHeaderGroupProps()}
                                        className="border-b"
                                    >
                                        {headerGroup.headers.map((column) => (
                                            <th
                                                {...column.getHeaderProps(column.getSortByToggleProps())}
                                                className="py-3 px-4 text-left text-sm font-semibold cursor-pointer text-muted"
                                            >
                                                {column.render('Header')}
                                                <span className="ml-1">
                                                    {column.isSorted ? column.isSortedDesc ? <ChevronDown className="inline w-4 h-4" /> : <ChevronUp className="inline w-4 h-4" /> : null}
                                                </span>
                                            </th>
                                        ))}
                                    </tr>
                                ))}
                            </thead>

                            <tbody {...getCshTableBodyProps()}>
                                {page.map((row: any) => {
                                    prepareCshRow(row);
                                    return (
                                        <tr
                                            {...row.getRowProps()}
                                            className="border-b hover:bg-muted/50"
                                        >
                                            {row.cells.map((cell: any) => (
                                                <td
                                                    {...cell.getCellProps()}
                                                    className="py-2 px-4 text-sm text-muted"
                                                >
                                                    {cell.render('Cell')}
                                                </td>
                                            ))}
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                        {/* Pagination controls */}
                        <div className="flex items-center justify-between p-4">
                            <div>
                                <Button
                                    onClick={() => gotoPage(0)}
                                    disabled={!canPreviousPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600"
                                >
                                    {'<<'}
                                </Button>
                                <Button
                                    onClick={() => previousPage()}
                                    disabled={!canPreviousPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                >
                                    {'<'}
                                </Button>
                                <Button
                                    onClick={() => nextPage()}
                                    disabled={!canNextPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                >
                                    {'>'}
                                </Button>
                                <Button
                                    onClick={() => gotoPage(pageCount - 1)}
                                    disabled={!canNextPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                >
                                    {'>>'}
                                </Button>
                            </div>
                            <div>
                                <span>
                                    Page&nbsp;
                                    <strong>
                                        {pageIndex + 1} of {pageOptions.length}&nbsp;
                                    </strong>
                                </span>
                                <span>
                                    | Go to page:&nbsp;
                                    <input
                                        type="number"
                                        defaultValue={pageIndex + 1}
                                        onChange={(e) => {
                                            const page = e.target.value ? Number(e.target.value) - 1 : 0;
                                            gotoPage(page);
                                        }}
                                        style={{ width: '50px' }}
                                        className="border border-input px-3 py-1 text-center"
                                    />
                                </span>
                            </div>
                            <div>
                                Rows per page:&nbsp;
                                <select
                                    value={pageSize}
                                    onChange={(e) => {
                                        setPageSize(Number(e.target.value));
                                    }}
                                    className="border border-input px-3 py-1"
                                >
                                    {[10, 20, 30, 40, 50].map((pageSize) => (
                                        <option
                                            key={pageSize}
                                            value={pageSize}
                                        >
                                            Show {pageSize}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

export default CSHViewModify;
