'use client';

import React, { useState } from 'react';
import SimpleReactValidator from 'simple-react-validator';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { CalendarIcon, TrashIcon } from 'lucide-react';

import { MBParamResponseInterface, MBParamUpdateInterface } from '../../../model';
import { useMbParamService } from '../../../service';
import { Button, Calendar, Input, Popover, PopoverContent, PopoverTrigger, Textarea } from '../../components/ui-component/ui';
import { cn } from '../../lib/utils';

interface MbParamEditProps {
    editRecord: MBParamResponseInterface;
    updateCallback: () => void;
}

export const MbParamEdit: React.FC<MbParamEditProps> = (props) => {
    const MbParamService = useMbParamService();
    const [calenderStartDate] = useState<Date>(props.editRecord.startDate ? new Date(props.editRecord.startDate) : new Date());
    const [mbParamFormAttributes, setMbParamFormAttributes] = useState<MBParamUpdateInterface>({
        parId: props.editRecord.parId,
        description: props.editRecord.description,
        startDate: props.editRecord.startDate ? new Date(props.editRecord.startDate) : '',
        endDate: props.editRecord.endDate ? new Date(props.editRecord.endDate) : null
    });
    const [validator] = useState(new SimpleReactValidator({ messages: { required: 'This field is required' } }));

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setMbParamFormAttributes((prev) => ({ ...prev, [name]: value }));
    };

    const handleDateChange = (field: 'startDate' | 'endDate', date: Date | null) => {
        if (date === undefined) return;
        setMbParamFormAttributes((prev) => ({ ...prev, [field]: date }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        validator.showMessages();
        if (!validator.allValid()) {
            return;
        }
        if (mbParamFormAttributes.startDate && mbParamFormAttributes.endDate && mbParamFormAttributes.startDate >= mbParamFormAttributes.endDate) {
            validator.showMessages();
            return;
        }

        const payload: MBParamUpdateInterface = {
            parId: mbParamFormAttributes.parId,
            description: mbParamFormAttributes.description,
            startDate: mbParamFormAttributes.startDate ? format(mbParamFormAttributes.startDate, 'yyyy-MM-dd') : '',
            endDate: mbParamFormAttributes.endDate ? format(mbParamFormAttributes.endDate, 'yyyy-MM-dd') : null
        };
        MbParamService.update(payload)
            .then(() => {
                toast.success('MB Param record updated successfully!');
                props.updateCallback();
            })
            .catch(() => {
                toast.error('Something went wrong :-( !');
            });
    };

    return (
        <form
            onSubmit={handleSubmit}
            className="space-y-6"
        >
            <div>
                <label
                    htmlFor="param-group"
                    className="block text-sm font-medium text-gray-700"
                >
                    Param Group (Disabled)
                </label>
                <Input
                    id="param-group"
                    name="paramGroup"
                    value={props.editRecord.paramGroup}
                    placeholder="Readonly param code"
                    className="w-full bg-white border border-gray-300 text-gray-700 focus:ring-2 focus:ring-yellow-500"
                    disabled
                />
                {/* No validation required for this field */}
            </div>

            <div>
                <label
                    htmlFor="param-code"
                    className="block text-sm font-medium text-gray-700"
                >
                    Param Code (Disabled)
                </label>
                <Input
                    id="param-code"
                    name="paramCode"
                    value={props.editRecord.paramCode}
                    placeholder="Readonly param code"
                    className="w-full bg-white border border-gray-300 text-gray-700 focus:ring-2 focus:ring-yellow-500"
                    disabled
                />
                {/* No validation required for this field */}
            </div>

            <div>
                <label
                    htmlFor="description"
                    className="block text-sm font-medium text-gray-700"
                >
                    Description
                </label>
                <Textarea
                    id="description"
                    name="description"
                    value={mbParamFormAttributes.description}
                    onChange={handleChange}
                    placeholder="Enter a brief description"
                    className="w-full bg-white border border-gray-300 text-gray-700 focus:ring-2 focus:ring-yellow-500"
                    rows={3}
                />
                {validator.message('description', mbParamFormAttributes.description, 'required|max:100')}
            </div>

            <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700">Start Date</label>
                <Popover>
                    <PopoverTrigger asChild>
                        <Button
                            variant="outline"
                            className={cn(
                                'w-[215px] justify-start text-left font-normal bg-white text-gray-700 border border-gray-300 focus:ring-2 focus:ring-yellow-500',
                                !mbParamFormAttributes.startDate && 'text-muted-foreground'
                            )}
                        >
                            <CalendarIcon className="mr-2" />
                            {mbParamFormAttributes.startDate ? <div>{format(mbParamFormAttributes.startDate, 'PPP')}</div> : <span>Pick a date</span>}
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto bg-white border border-gray-300 shadow-lg p-0 rounded-lg">
                        <Calendar
                            mode="single"
                            selected={mbParamFormAttributes.startDate ? new Date(mbParamFormAttributes.startDate) : undefined}
                            onSelect={(date) => handleDateChange('startDate', date as Date)}
                            initialFocus
                            disabledBeforeDate={calenderStartDate}
                            defaultMonth={mbParamFormAttributes.startDate ? new Date(mbParamFormAttributes.startDate) : undefined}
                            className="text-gray-700"
                        />
                    </PopoverContent>
                </Popover>
                {validator.message('startDate', mbParamFormAttributes.startDate, 'required')}
            </div>

            <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700">End Date</label>
                <Popover>
                    <PopoverTrigger asChild>
                        <Button
                            variant="outline"
                            className={cn(
                                'w-[215px] justify-start text-left font-normal bg-white text-gray-700 border border-gray-300 focus:ring-2 focus:ring-yellow-500',
                                !mbParamFormAttributes.endDate && 'text-muted-foreground'
                            )}
                        >
                            <CalendarIcon className="mr-2" />
                            {mbParamFormAttributes.endDate ? <div>{format(mbParamFormAttributes.endDate, 'PPP')}</div> : <span>Pick a date</span>}
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto bg-white border border-gray-300 shadow-lg p-0 rounded-lg">
                        <Calendar
                            mode="single"
                            selected={mbParamFormAttributes.endDate ? new Date(mbParamFormAttributes.endDate) : undefined}
                            onSelect={(date) => handleDateChange('endDate', date as Date)}
                            initialFocus
                            disabledBeforeDate={mbParamFormAttributes.startDate ? new Date(mbParamFormAttributes.startDate) : calenderStartDate}
                            defaultMonth={mbParamFormAttributes.endDate ? new Date(mbParamFormAttributes.endDate) : undefined}
                            className="text-gray-700"
                        />
                    </PopoverContent>
                    <Button
                        disabled={!mbParamFormAttributes.endDate}
                        type="button"
                        onClick={() => handleDateChange('endDate', null)}
                    >
                        <TrashIcon />
                    </Button>
                </Popover>
                {mbParamFormAttributes.startDate && mbParamFormAttributes.endDate && mbParamFormAttributes.startDate >= mbParamFormAttributes.endDate && (
                    <div className="text-danger mt-2">End date should be greater than start date</div>
                )}
            </div>

            <div className="mt-4 flex justify-end">
                <Button
                    disabled={!validator.allValid()}
                    type="submit"
                    className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
                >
                    Submit
                </Button>
            </div>
        </form>
    );
};
