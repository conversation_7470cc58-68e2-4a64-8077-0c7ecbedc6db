'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useTable, useSortBy, useGlobalFilter, useFilters, usePagination } from 'react-table';
import { ChevronDown, ChevronUp } from 'lucide-react';
import Select from 'react-select';
import { format } from 'date-fns';

import { Spinner } from '@aa/ui/core/spinner';
import { Button, Dialog, DialogContent, DialogHeader, DialogTitle, Input } from '../../components/ui-component/ui';
import { useSupplierService, useMbParamService } from '../../../service';
import { SelectInterface, MBParamResponseInterface } from '../../../model';
import { MbParamEdit } from './MbParamEdit';

const MbParamsViewModify = () => {
    const [customerGroupOptions, setCustomerGroupOptions] = useState<SelectInterface[]>([]);
    const [selectedCustomerGroups, setSelectedCustomerGroups] = useState<SelectInterface[]>([]);
    const [searchValue, setSearchValue] = useState<string>('');
    const [mbParamsData, setMbParamsData] = useState<MBParamResponseInterface[]>([]);
    const [loading, setLoading] = useState(true);
    const SupplierService = useSupplierService();
    const MbParamService = useMbParamService();

    useEffect(() => {
        SupplierService.fetchCustomerGroups()
            .then((resp) => {
                const allCustomerGroups = resp.map((customerGroup) => ({
                    label: customerGroup.custGrpName,
                    value: customerGroup.custGrpCode
                }));
                setCustomerGroupOptions(allCustomerGroups);
            })
            .catch((error) => console.error(error));
    }, []);

    const columns = useMemo(() => {
        return [
            {
                Header: 'Param Code',
                accessor: 'paramCode'
            },
            {
                Header: 'Param Group',
                accessor: 'paramGroup'
            },
            {
                Header: 'Description',
                accessor: 'description'
            },
            {
                Header: 'Customer Group',
                accessor: 'cgArray',
                Cell: ({ row }) => {
                    let valDisplay = '--';
                    if (row.original.cgArray) {
                        let cgValue: string[] = [];
                        try {
                            cgValue = JSON.parse(row.original.cgArray.replace(/'/g, '"'));
                        } catch (e) {
                            console.error('Error parsing cgArray:', e);
                        }

                        if (Array.isArray(cgValue) && cgValue.length > 0) {
                            if (customerGroupOptions.length > 0) {
                                const filteredCustomerGroups = customerGroupOptions.filter((group) => cgValue.includes(group.value));
                                if (filteredCustomerGroups.length > 0) {
                                    valDisplay = filteredCustomerGroups.map((group) => group.label).join(', ');
                                }
                            } else {
                                valDisplay = cgValue.join(', ');
                            }
                        }
                    }
                    return valDisplay;
                },
                disableSortBy: true
            },
            {
                Header: 'Start Date',
                accessor: 'startDate',
                Cell: ({ value }) => (value ? format(new Date(value), 'dd-MM-yyyy') : '--')
            },
            {
                Header: 'End Date',
                accessor: 'endDate',
                Cell: ({ value }) => (value ? format(new Date(value), 'dd-MM-yyyy') : '--')
            },
            {
                Header: 'Actions',
                Cell: ({ row }) => {
                    const [showEditDialog, setShowEditDialog] = useState(false);
                    return (
                        <>
                            <Button
                                variant="outline"
                                size="sm"
                                className="bg-blue-600 text-white"
                                onClick={() => setShowEditDialog(true)}
                            >
                                Edit
                            </Button>
                            <Dialog
                                open={showEditDialog}
                                onOpenChange={setShowEditDialog}
                            >
                                <DialogContent
                                    className="bg-muted p-6 rounded-lg shadow-lg bg-white"
                                    aria-describedby={undefined}
                                >
                                    <DialogHeader>
                                        <DialogTitle> Edit Record </DialogTitle>
                                    </DialogHeader>
                                    <MbParamEdit
                                        editRecord={row.original}
                                        updateCallback={() => {
                                            setShowEditDialog(false);
                                            fetchMbParamaData(selectedCustomerGroups);
                                        }}
                                    />
                                </DialogContent>
                            </Dialog>
                        </>
                    );
                }
            }
        ];
    }, [customerGroupOptions, selectedCustomerGroups]);

    const handleCustomerGroupChange = (selectedOptions: SelectInterface[]) => {
        setSearchValue('');
        setGlobalFilter('');
        setSelectedCustomerGroups(selectedOptions);
        fetchMbParamaData(selectedOptions);
    };

    const fetchMbParamaData = (selectedOptions: SelectInterface[]) => {
        const selectedCustomerGroup = selectedOptions.length > 0 ? selectedOptions.map((customerGroup) => customerGroup.value).join(',') : '';
        setLoading(true);
        MbParamService.fetchAll(selectedCustomerGroup)
            .then((resp) => {
                setMbParamsData(resp.data);
            })
            .catch((error) => console.error(error))
            .finally(() => setLoading(false));
    };

    useEffect(() => {
        fetchMbParamaData([]);
    }, []);

    const tableInstance = useTable(
        {
            columns,
            data: mbParamsData,
            initialState: { pageIndex: 0, pageSize: 10 }
        },
        useFilters,
        useGlobalFilter,
        useSortBy,
        usePagination
    );

    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        prepareRow,
        setGlobalFilter,
        page,
        canPreviousPage,
        canNextPage,
        pageOptions,
        pageCount,
        gotoPage,
        nextPage,
        previousPage,
        setPageSize,
        state: { pageIndex, pageSize }
    } = tableInstance;

    return (
        <div className="space-y-8 p-4">
            <div>
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold">MB Params</h2>
                    <Select
                        isMulti
                        name="customerGroup"
                        options={customerGroupOptions}
                        value={selectedCustomerGroups}
                        onChange={handleCustomerGroupChange}
                        placeholder="Select customer groups"
                        className="react-select-container"
                        classNamePrefix="react-select"
                        styles={{
                            control: (base) => ({
                                ...base,
                                width: 800
                            })
                        }}
                    />
                    <Input
                        placeholder="Search..."
                        onChange={(e) => {
                            setGlobalFilter(e.target.value);
                            setSearchValue(e.target.value);
                        }}
                        className="max-w-xs ml-2"
                        value={searchValue}
                    />
                </div>

                <div className="overflow-x-auto bg-white shadow-lg rounded-lg">
                    {loading ? (
                        <div className="text-center">
                            <br />
                            <br />
                            Loading MB Params..
                            <br />
                            <br />
                            Please wait...
                            <Spinner
                                className="mr-3"
                                style={{ display: 'inline' }}
                            />
                            <br />
                            <br />
                            <br />
                        </div>
                    ) : (
                        <>
                            <table
                                {...getTableProps()}
                                className="min-w-full table-auto"
                            >
                                <thead>
                                    {headerGroups.map((headerGroup) => (
                                        <tr
                                            {...headerGroup.getHeaderGroupProps()}
                                            className="border-b"
                                        >
                                            {headerGroup.headers.map((column) => (
                                                <th
                                                    {...column.getHeaderProps(column.getSortByToggleProps())}
                                                    className="py-3 px-4 text-left text-sm font-semibold cursor-pointer text-muted"
                                                >
                                                    {column.render('Header')}
                                                    <span className="ml-1">
                                                        {column.isSorted ? column.isSortedDesc ? <ChevronDown className="inline w-4 h-4" /> : <ChevronUp className="inline w-4 h-4" /> : null}
                                                    </span>
                                                </th>
                                            ))}
                                        </tr>
                                    ))}
                                </thead>
                                <tbody {...getTableBodyProps()}>
                                    {page.map((row) => {
                                        prepareRow(row);
                                        return (
                                            <tr
                                                {...row.getRowProps()}
                                                className="border-b hover:bg-muted/50"
                                            >
                                                {row.cells.map((cell) => (
                                                    <td
                                                        {...cell.getCellProps()}
                                                        className="py-2 px-4 text-sm text-muted"
                                                    >
                                                        {cell.render('Cell')}
                                                    </td>
                                                ))}
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                            {/* Pagination controls */}
                            <div className="flex items-center justify-between p-4">
                                <div>
                                    <Button
                                        onClick={() => gotoPage(0)}
                                        disabled={!canPreviousPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600"
                                    >
                                        {'<<'}
                                    </Button>
                                    <Button
                                        onClick={() => previousPage()}
                                        disabled={!canPreviousPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'<'}
                                    </Button>
                                    <Button
                                        onClick={() => nextPage()}
                                        disabled={!canNextPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'>'}
                                    </Button>
                                    <Button
                                        onClick={() => gotoPage(pageCount - 1)}
                                        disabled={!canNextPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'>>'}
                                    </Button>
                                </div>
                                <div>
                                    <span>
                                        Page&nbsp;
                                        <strong>
                                            {pageIndex + 1} of {pageOptions.length}&nbsp;
                                        </strong>
                                    </span>
                                    <span>
                                        | Go to page:&nbsp;
                                        <input
                                            type="number"
                                            value={pageIndex + 1}
                                            onChange={(e) => {
                                                const page = e.target.value ? Number(e.target.value) - 1 : 0;
                                                gotoPage(page);
                                            }}
                                            style={{ width: '50px' }}
                                            className="border border-input px-1 py-1 text-center"
                                        />
                                    </span>
                                </div>
                                <div>
                                    Rows per page:&nbsp;
                                    <select
                                        value={pageSize}
                                        onChange={(e) => {
                                            setPageSize(Number(e.target.value));
                                        }}
                                        className="border border-input px-3 py-1"
                                    >
                                        {[10, 20, 30, 40, 50].map((pageSize) => (
                                            <option
                                                key={pageSize}
                                                value={pageSize}
                                            >
                                                Show {pageSize}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default MbParamsViewModify;
