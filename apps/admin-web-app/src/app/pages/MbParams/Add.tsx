'use client';

import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '../../components/ui-component/ui/popover';
import { Button } from '../../components/ui-component/ui/button';
import { Input } from '../../components/ui-component/ui/input';
import { Textarea } from '../../components/ui-component/ui/textarea';
import { Card } from '../../components/ui-component/ui/card';
import { CustomLoader } from '../../components/ui-component/custom-loader';
import { cn } from '../../lib/utils';
import { Calendar } from '../../components/ui-component/ui/calendar';
import SimpleReactValidator from 'simple-react-validator';
import { MBParamInsertInterface } from '../../../model/mbParam.model';
import { useSupplierService, useMbParamService } from '../../../service';
import Select from 'react-select';
import { toast } from 'sonner';
import { SelectInterface } from '../../../model';
import { useNavigate } from 'react-router-dom';
import { useEnvironment } from '@aa/ui/hooks/use-environment';

const MbParamsAdd: React.FC = () => {
    const navigate = useNavigate();
    const SupplierService = useSupplierService();
    const MbParamService = useMbParamService();
    const environment = useEnvironment();
    const [loading, setLoading] = useState<boolean>(false);

    const [calenderStartDate] = useState<Date>(new Date());
    const [formAttributes, setFormAttributes] = useState<MBParamInsertInterface>({
        startDate: new Date(),
        endDate: null,
        customerGroup: [],
        paramGroup: undefined,
        paramCode: '',
        description: ''
    });
    const [customerGroups, setCustomerGroups] = useState<SelectInterface[]>([]);
    const [mbParamGroups, setMbParamGroups] = useState<SelectInterface[]>([]);
    const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
    const [validator] = useState(
        new SimpleReactValidator({
            messages: {
                required: 'This field is required'
            }
        })
    );

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormAttributes((prev) => ({
            ...prev,
            [name]: value
        }));
    };

    const handleDateChange = (field: 'startDate' | 'endDate', date: Date) => {
        const today = new Date();
        if (date === undefined) return;
        if (format(date, 'yyyy-MM-dd') >= format(today, 'yyyy-MM-dd')) {
            setFormAttributes((prev) => ({
                ...prev,
                [field]: date
            }));
        }
    };

    const handleDropdownChange = (selectedOption: SelectInterface) => {
        setFormAttributes((prev) => ({
            ...prev,
            paramGroup: selectedOption
        }));
    };

    const handleCustomerGroupChange = (selectedOptions: SelectInterface[]) => {
        setFormAttributes((prev) => ({
            ...prev,
            customerGroup: selectedOptions ? selectedOptions.map((option) => option.value) : []
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        setIsSubmitted(true);
        validator.showMessages();

        if (formAttributes.startDate && formAttributes.endDate && formAttributes.startDate >= formAttributes.endDate) {
            validator.showMessages();
            return;
        }

        setTimeout(() => {
            const isFormValid = validator.allValid();
            if (!isFormValid) {
                return;
            }
            setLoading(true);
            const payload = {
                paramGroup: formAttributes.paramGroup?.value,
                paramCode: formAttributes.paramCode,
                description: formAttributes.description,
                startDate: formAttributes.startDate ? format(formAttributes.startDate, 'yyyy-MM-dd') : '',
                endDate: formAttributes.endDate ? format(formAttributes.endDate, 'yyyy-MM-dd') : '',
                cgArray: JSON.stringify(formAttributes.customerGroup)
            };
            MbParamService.insert(payload)
                .then(() => {
                    setLoading(false);
                    toast.success('New record inserted successfully!');
                    setTimeout(() => {
                        navigate(`${environment.basePath}/mb-params/view`, { replace: true });
                    }, 1000);
                })
                .catch(() => {
                    setLoading(false);
                    toast.error('Something went wrong!');
                });
        }, 1000);
    };

    useEffect(() => {
        SupplierService.fetchCustomerGroups()
            .then((resp) => {
                const allCustomerGroups = resp.map((group) => ({
                    label: group.custGrpName,
                    value: group.custGrpCode
                }));
                setCustomerGroups(allCustomerGroups);
            })
            .catch(console.error);
        SupplierService.fetchMbParamGroups()
            .then((resp) => {
                const allMbParamGroups = resp.map((mbParam: string) => ({
                    label: mbParam,
                    value: mbParam
                }));
                setMbParamGroups(allMbParamGroups);
            })
            .catch(console.error);
    }, []);

    const customerGroupOptions = customerGroups.map((group) => ({ value: group.value, label: group.label }));

    return (
        <>
            {/* Page Level Loader */}
            <CustomLoader show={loading} />

            <div className="p-6">
                <Card className="max-w-3xl mx-auto p-6 space-y-6 bg-white">
                    <h2 className="text-xl font-semibold mb-4">MB Parameters</h2>
                    <form
                        onSubmit={handleSubmit}
                        className="space-y-6"
                    >
                        <div>
                            <label
                                htmlFor="param-group"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Param Group
                            </label>
                            <Select
                                name="paramGroup"
                                options={mbParamGroups}
                                value={formAttributes.paramGroup}
                                onChange={handleDropdownChange}
                                placeholder="Select mb param group"
                                className="react-select-container"
                                classNamePrefix="react-select"
                            />
                            {isSubmitted && validator.message('paramGroup', formAttributes.paramGroup, 'required')}
                        </div>

                        <div>
                            <label
                                htmlFor="param-code"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Param Code
                            </label>
                            <Input
                                id="param-code"
                                name="paramCode"
                                value={formAttributes.paramCode}
                                onChange={handleChange}
                                placeholder="Enter param code"
                                className="w-full bg-white border border-gray-300 text-gray-700 focus:ring-2 focus:ring-yellow-500"
                            />
                            {isSubmitted && validator.message('paramCode', formAttributes.paramCode, 'required|max:4')}
                        </div>

                        <div>
                            <label
                                htmlFor="description"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Description
                            </label>
                            <Textarea
                                id="description"
                                name="description"
                                value={formAttributes.description}
                                onChange={handleChange}
                                placeholder="Enter a brief description"
                                className="w-full bg-white border border-gray-300 text-gray-700 focus:ring-2 focus:ring-yellow-500"
                                rows={3}
                            />
                            {isSubmitted && validator.message('description', formAttributes.description, 'required|max:100')}
                        </div>

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700">Start Date</label>
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button
                                        variant="outline"
                                        className={cn(
                                            'w-[215px] justify-start text-left font-normal bg-white text-gray-700 border border-gray-300 focus:ring-2 focus:ring-yellow-500',
                                            !formAttributes.startDate && 'text-muted-foreground'
                                        )}
                                    >
                                        <CalendarIcon className="mr-2" />
                                        {formAttributes.startDate ? <div>{format(formAttributes.startDate, 'PPP')}</div> : <span>Pick a date</span>}
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto bg-white border border-gray-300 shadow-lg p-0 rounded-lg">
                                    <Calendar
                                        mode="single"
                                        selected={formAttributes.startDate ? new Date(formAttributes.startDate) : undefined}
                                        onSelect={(date) => handleDateChange('startDate', date as Date)}
                                        initialFocus
                                        disabledBeforeDate={calenderStartDate}
                                        className="text-gray-700"
                                    />
                                </PopoverContent>
                            </Popover>
                            {isSubmitted && formAttributes.startDate && formAttributes.endDate && formAttributes.startDate >= formAttributes.endDate && (
                                <div className="text-danger mt-2">Start date should be less than end date</div>
                            )}
                        </div>

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700">End Date</label>
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button
                                        variant="outline"
                                        className={cn(
                                            'w-[215px] justify-start text-left font-normal bg-white text-gray-700 border border-gray-300 focus:ring-2 focus:ring-yellow-500',
                                            !formAttributes.endDate && 'text-muted-foreground'
                                        )}
                                    >
                                        <CalendarIcon className="mr-2" />
                                        {formAttributes.endDate ? <div>{format(formAttributes.endDate, 'PPP')}</div> : <span>Pick a date</span>}
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto bg-white border border-gray-300 shadow-lg p-0 rounded-lg">
                                    <Calendar
                                        mode="single"
                                        selected={formAttributes.endDate ? new Date(formAttributes.endDate) : undefined}
                                        onSelect={(date) => handleDateChange('endDate', date as Date)}
                                        initialFocus
                                        disabledBeforeDate={calenderStartDate}
                                        className="text-gray-700"
                                    />
                                </PopoverContent>
                            </Popover>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Customer Group</label>
                            <Select
                                isMulti
                                name="customerGroup"
                                options={customerGroupOptions}
                                value={customerGroupOptions.filter((option) => formAttributes.customerGroup?.includes(option.value))}
                                onChange={handleCustomerGroupChange}
                                placeholder="Select customer groups"
                                className="react-select-container"
                                classNamePrefix="react-select"
                            />
                            {isSubmitted && validator.message('customerGroup', formAttributes.customerGroup.join(','), 'required')}
                        </div>

                        <div>
                            <Button
                                type="submit"
                                className="bg-yellow-500 text-white hover:bg-yellow-600 w-full"
                            >
                                Submit
                            </Button>
                        </div>
                    </form>
                </Card>
            </div>
        </>
    );
};

export default MbParamsAdd;
