'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useTable, useSortBy, useGlobalFilter, useFilters, usePagination } from 'react-table';
import { ChevronDown, ChevronUp } from 'lucide-react';
import Select from 'react-select';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { isString } from 'lodash';

import { Spinner } from '@aa/ui/core/spinner';
import { VehicleData } from '@aa/admin-helpers';
import { Input, Button, Dialog, DialogContent, DialogHeader, DialogTitle, Popover, PopoverTrigger, PopoverContent, YesNoDialog } from '../../components/ui-component/ui';
import { useVehicleService } from '../../../service/vehicle.service';
import { MbVehicleDataDetails, SelectInterface, TableRow } from '../../../model';
import { TempVehicleEdit } from './TempVehicleEdit';
import CSHViewModify from './CSH/CSHViewModify';
import { cn, Utils } from '../../lib/utils';
import { CustomLoader } from '../../components/ui-component/custom-loader';

const hiddenColumns = [
    'vehicleData.vehicleRegNo',
    'mbVehicleData.vin',
    'mbVehicleData.infleetDate',
    'mbVehicleData.defleetDate',
    'mbVehicleData.vehValue',
    'mbVehicleData.details',
    'mbVehicleData.details.BrandName',
    'mbVehicleData.details.ModelName',
    'mbVehicleData.details.Derivative',
    'mbVehicleData.details.BasicColour',
    'mbVehicleData.details.Colour',
    'mbVehicleData.details.RegDate',
    'mbVehicleData.details.EstimatedRRP',
    'mbVehicleData.details.Retailer',
    'mbVehicleData.details.Fuel',
    'mbVehicleData.details.Status',
    'mbVehicleData.details.Transmission'
];
const createColumns = (selectedSnapshotId: string, refreshCallback: (selectedSnapshotId: string) => void) => {
    return [
        {
            Header: 'Info',
            accessor: 'errors',
            Cell: ({ value }) => {
                if (!value) {
                    return (
                        <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 bg-gray-500 rounded-full"></div>
                            {/* <div className="text-gray-500">Unverified</div> */}
                        </div>
                    );
                }
                return value.length ? (
                    <Popover>
                        <PopoverTrigger asChild>
                            <div
                                className="flex items-center space-x-2 cursor-pointer"
                                title="Errors: click to view"
                            >
                                <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                                {/* <div className="text-red-500">Error</div> */}
                            </div>
                        </PopoverTrigger>
                        <PopoverContent
                            align="start"
                            className="bg-white rounded-lg shadow-lg min-w-[400px]"
                        >
                            <ul className="list-disc list-inside text-sm">
                                {value.map((error: string, idx: number) => (
                                    <li key={idx}>{error}</li>
                                ))}
                            </ul>
                        </PopoverContent>
                    </Popover>
                ) : (
                    <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                        {/* <div className="text-green-500">Valid</div> */}
                    </div>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Basic Info',
            accessor: 'basicInfo',
            Cell: ({ row }) => {
                const detailsObj: MbVehicleDataDetails = row.original.mbVehicleData.details ? JSON.parse(row.original.mbVehicleData.details) : {};
                const fields = [
                    { key: 'Reg No', value: row.original.vehicleData.vehicleRegNo },
                    { key: 'VIN', value: row.original.mbVehicleData.vin },
                    { key: 'Brand', value: detailsObj.BrandName },
                    { key: 'Model', value: detailsObj.ModelName }
                ];
                return (
                    <>
                        {fields.map((field, idx) => (
                            <div
                                key={field.key}
                                className="flex items-center whitespace-nowrap"
                            >
                                <i>{field.key}</i>: <span className="ml-1 border-b px-1">{field.value}</span>
                            </div>
                        ))}
                    </>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Dates',
            accessor: 'dates',
            Cell: ({ row }) => {
                const fields = [
                    { key: 'In-Fleet', value: row.original.mbVehicleData.infleetDate ? format(new Date(row.original.mbVehicleData.infleetDate), 'dd-MM-yyyy') : 'Not Set' },
                    { key: 'De-Fleet', value: row.original.mbVehicleData.defleetDate ? format(new Date(row.original.mbVehicleData.defleetDate), 'dd-MM-yyyy') : 'Not Set' }
                ];
                return (
                    <>
                        {fields.map((field, idx) => (
                            <div
                                key={field.key}
                                className={idx > 0 ? 'mt-3' : ''}
                            >
                                <i>{field.key}</i>:<br /> <span className="border-b whitespace-nowrap">{field.value}</span>
                            </div>
                        ))}
                    </>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Detail Info 1',
            accessor: 'detailsInfo1',
            Cell: ({ row }) => {
                const detailsObj: MbVehicleDataDetails = row.original.mbVehicleData.details ? JSON.parse(row.original.mbVehicleData.details) : {};
                //console.log('detailsObj', detailsObj);
                const fields = [
                    { key: 'Derivative', value: detailsObj.Derivative ? detailsObj.Derivative : 'Not Set' },
                    { key: 'Basic Colour', value: detailsObj.BasicColour ? detailsObj.BasicColour : 'Not Set' },
                    { key: 'Colour Name', value: detailsObj.Colour ? detailsObj.Colour : 'Not Set' },
                    { key: 'Reg. Date', value: detailsObj.RegDate ? detailsObj.RegDate : 'Not Set' }
                ];
                return (
                    <>
                        {fields.map((field, idx) => (
                            <div
                                key={field.key}
                                className="flex items-center whitespace-nowrap"
                            >
                                <i>{field.key}</i>: <span className="ml-1 border-b px-1">{field.value}</span>
                            </div>
                        ))}
                    </>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Detail Info 2',
            accessor: 'detailsInfo2',
            Cell: ({ row }) => {
                const detailsObj: MbVehicleDataDetails = row.original.mbVehicleData.details ? JSON.parse(row.original.mbVehicleData.details) : {};
                const fields = [
                    { key: 'Estimated RRP', value: row.original.mbVehicleData.vehValue ? row.original.mbVehicleData.vehValue : 'Not Set' },
                    { key: 'Retailer', value: detailsObj.Retailer ? detailsObj.Retailer : 'Not Set' },
                    { key: 'Fuel', value: detailsObj.Fuel ? detailsObj.Fuel : 'Not Set' }
                ];
                return (
                    <>
                        {fields.map((field, idx) => (
                            <div
                                key={field.key}
                                className="flex items-center whitespace-nowrap"
                            >
                                <i>{field.key}</i>: <span className="ml-1 border-b px-1">{field.value}</span>
                            </div>
                        ))}
                    </>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Detail Info 3',
            accessor: 'detailsInfo3',
            Cell: ({ row }) => {
                const detailsObj: MbVehicleDataDetails = row.original.mbVehicleData.details ? JSON.parse(row.original.mbVehicleData.details) : {};
                const fields = [
                    { key: 'Status', value: detailsObj.Status ? detailsObj.Status : 'Not Set' },
                    { key: 'Transmission', value: detailsObj.Transmission ? detailsObj.Transmission : 'Not Set' },
                    { key: 'Tow Bar', value: detailsObj.Towbar ? 'Yes' : 'No' },
                    { key: 'Wheel Nuts', value: detailsObj.LockingWheelNut ? 'Yes' : 'No' }
                ];
                return (
                    <>
                        {fields.map((field, idx) => (
                            <div
                                key={field.key}
                                className="flex items-center whitespace-nowrap"
                            >
                                <i>{field.key}</i>: <span className="ml-1 border-b px-1">{field.value}</span>
                            </div>
                        ))}
                    </>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Actions',
            Cell: ({ row }) => {
                const [showEditDialog, setShowEditDialog] = useState(false);
                return (
                    <>
                        <Button
                            variant="outline"
                            size="sm"
                            className="bg-blue-600 text-white"
                            onClick={() => setShowEditDialog(true)}
                        >
                            Edit
                        </Button>
                        <Dialog
                            open={showEditDialog}
                            onOpenChange={setShowEditDialog}
                        >
                            <DialogContent
                                className="bg-muted p-6 rounded-lg shadow-lg bg-white"
                                aria-describedby={undefined}
                            >
                                <DialogHeader>
                                    <DialogTitle> Edit Record </DialogTitle>
                                </DialogHeader>
                                <TempVehicleEdit
                                    editRecord={row.original}
                                    snapshotId={selectedSnapshotId}
                                    updateCallback={() => {
                                        setShowEditDialog(false);
                                        refreshCallback(selectedSnapshotId);
                                    }}
                                />
                            </DialogContent>
                        </Dialog>
                    </>
                );
            }
        },
        ...hiddenColumns.map((columnName) => ({
            Header: columnName,
            accessor: columnName
        }))
    ];
};

const VehiclesView = () => {
    const [snapshotIdOptions, setSnapshotIdOptions] = useState<SelectInterface[]>([]);
    const [selectedSnapshot, setSelectedSnapshot] = useState<SelectInterface | undefined>(undefined);
    const [searchValue, setSearchValue] = useState('');
    const VehicleService = useVehicleService();

    const [tempVehiclesData, setTempVehiclesData] = useState<TableRow<VehicleData>[]>([]);
    const [loadingTempVehicles, setLoadingTempVehicles] = useState(true);

    const [showUploadToCSHPrompt, setShowUploadToCSHPrompt] = useState(false);

    const [pageLevelLoading, setPageLevelLoading] = useState(false);

    const fetchTempVehiclesData = (snapshotId: string) => {
        setLoadingTempVehicles(true);
        VehicleService.getTempVehicles(snapshotId)
            .then((resp) => setTempVehiclesData(resp.data))
            .catch((error) => console.error(error))
            .finally(() => setLoadingTempVehicles(false));
    };

    const columns = useMemo(() => {
        if (!selectedSnapshot) {
            return [];
        }

        const refreshCallback = (selectedSnapshotId: string) => {
            fetchTempVehiclesData(selectedSnapshotId);
        };

        return createColumns(selectedSnapshot?.value, refreshCallback);
    }, [selectedSnapshot]);

    const tableInstance = useTable(
        {
            columns,
            data: tempVehiclesData,
            initialState: { pageIndex: 0, pageSize: 5, hiddenColumns }
        },
        useFilters,
        useGlobalFilter,
        useSortBy,
        usePagination
    );

    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        prepareRow,
        setGlobalFilter,
        page,
        canPreviousPage,
        canNextPage,
        pageOptions,
        pageCount,
        gotoPage,
        nextPage,
        previousPage,
        setPageSize,
        state: { pageIndex, pageSize }
    } = tableInstance;

    const handleSnapshotChange = (selectedOption: SelectInterface) => {
        setSelectedSnapshot(selectedOption);
        fetchTempVehiclesData(selectedOption.value);
    };

    const validateTempVehiclesData = () => {
        let isValid = true;
        const vehiclesData = [...tempVehiclesData];
        vehiclesData.forEach((vehicle: TableRow<VehicleData>) => {
            vehicle.errors = [];
            //console.log('vehicle', vehicle);
            const detailsObj: MbVehicleDataDetails = isString(vehicle.mbVehicleData.details) ? JSON.parse(vehicle.mbVehicleData.details) : vehicle.mbVehicleData.details;

            // vehicleData.vehicleRegNo?: string;
            if (!vehicle.vehicleData.vehicleRegNo) {
                vehicle.errors.push('Vehicle Reg No is required');
            }

            // mbVehicleData.vin?: string;
            if (!vehicle.mbVehicleData.vin) {
                vehicle.errors.push('VIN is required');
            }

            // vehicleData.variantId?: number;
            if (!Utils.isNumber(vehicle.vehicleData.variantId) || vehicle.vehicleData.variantId < 1) {
                vehicle.errors.push('Variant Id is required and should be greater than 0');
            }

            // mbVehicleData.vehModelId?: number;
            if (!Utils.isNumber(vehicle.mbVehicleData.vehModelId) || vehicle.mbVehicleData.vehModelId < 1) {
                vehicle.errors.push('Vehicle Model Id is required and should be greater than 0');
            }

            // detailsObj.BrandName?: string;
            if (!detailsObj.BrandName) {
                vehicle.errors.push('Brand Name is required');
            }

            // detailsObj.ModelName?: string;
            if (!detailsObj.ModelName) {
                vehicle.errors.push('Model Name is required');
            }

            // detailsObj.Derivative?: string;
            if (!detailsObj.Derivative) {
                vehicle.errors.push('Derivative is required');
            }

            // detailsObj.BasicColour?: string;
            if (!detailsObj.BasicColour) {
                vehicle.errors.push('Basic Colour is required');
            }

            // detailsObj.Colour?: string;
            if (!detailsObj.Colour) {
                vehicle.errors.push('Colour Name is required');
            }

            // detailsObj.RegDate?: string;
            if (!detailsObj.RegDate) {
                vehicle.errors.push('Reg. Date is required');
            }

            // mbVehicleData.infleetDate?: string;
            if (!vehicle.mbVehicleData.infleetDate) {
                vehicle.errors.push('In-Fleet Date is required');
            }

            // mbVehicleData.defleetDate?: string;
            // if (!vehicle.mbVehicleData.defleetDate) {
            //     vehicle.errors.push('De-Fleet Date is required');
            // }

            // detailsObj.Retailer?: string;
            if (!detailsObj.Retailer) {
                vehicle.errors.push('Retailer is required');
            }

            // detailsObj.Fuel?: string;
            if (!detailsObj.Fuel) {
                vehicle.errors.push('Fuel is required');
            }

            // detailsObj.Transmission?: string;
            if (!detailsObj.Transmission) {
                vehicle.errors.push('Transmission is required');
            }

            // detailsObj.Towbar?: boolean;
            /* No validation required for detailsObj.Towbar */

            // detailsObj.LockingWheelNut?: boolean;
            /* No validation required for detailsObj.LockingWheelNut */

            // detailsObj.Status?: string;
            /* No validation required for detailsObj.Status */

            // mbVehicleData.regionParId?: number;
            if (!Utils.isNumber(vehicle.mbVehicleData.regionParId) || vehicle.mbVehicleData.regionParId < 1) {
                vehicle.errors.push('Region Par Id is required and should be greater than 0');
            }
            // mbVehicleData.vehValue?: number;
            if (!Utils.isNumber(vehicle.mbVehicleData.vehValue) || vehicle.mbVehicleData.vehValue < 1) {
                vehicle.errors.push('Vehicle Value is required and should be greater than 0');
            }
            // mbVehicleData.vehParId?: number;
            if (!Utils.isNumber(vehicle.mbVehicleData.vehParId) || vehicle.mbVehicleData.vehParId < 1) {
                vehicle.errors.push('Vehicle Par Id is required and should be greater than 0');
            }

            // resourceData.aahSiteId?: number;
            if (vehicle.resourceData.aahSiteId && !Utils.isNumber(vehicle.resourceData.aahSiteId)) {
                vehicle.errors.push('AAH Site Id should be a number');
            }
            // resourceData.ccSiteId?: number;
            if (vehicle.resourceData.ccSiteId && !Utils.isNumber(vehicle.resourceData.ccSiteId)) {
                vehicle.errors.push('CC Site Id should be a number');
            }
            // resourceData.resourceRemarkId?: number;
            if (vehicle.resourceData.resourceRemarkId && !Utils.isNumber(vehicle.resourceData.resourceRemarkId)) {
                vehicle.errors.push('Resource Remark Id should be a number');
            }
            // resourceData.curTxnId?: number | null;
            if (vehicle.resourceData.curTxnId && !Utils.isNumber(vehicle.resourceData.curTxnId)) {
                vehicle.errors.push('Cur Txn Id should be a number');
            }
            // resourceData.updateSequence?: number;
            if (vehicle.resourceData.updateSequence && !Utils.isNumber(vehicle.resourceData.updateSequence)) {
                vehicle.errors.push('Update Sequence should be a number');
            }
            // resourceData.owningMgtUnitId?: number;
            if (!Utils.isNumber(vehicle.resourceData.owningMgtUnitId) || vehicle.resourceData.owningMgtUnitId < 1) {
                vehicle.errors.push('Owning Mgt Unit Id is required and should be greater than 0');
            }
            // resourceData.resourceTypeId?: number;
            if (vehicle.resourceData.resourceTypeId && !Utils.isNumber(vehicle.resourceData.resourceTypeId)) {
                vehicle.errors.push('Resource Type Id should be a number');
            }
            // resourceData.resourceStatusId?: number;
            if (vehicle.resourceData.resourceStatusId && !Utils.isNumber(vehicle.resourceData.resourceStatusId)) {
                vehicle.errors.push('Resource Status Id should be a number');
            }

            isValid = isValid && vehicle.errors.length === 0;
        });
        setTempVehiclesData(vehiclesData);
        return isValid;
    };

    const handleUploadToCSH = () => {
        if (selectedSnapshot) {
            setPageLevelLoading(true);
            VehicleService.insertVehiclesToCSH(selectedSnapshot.value)
                .then(() => {
                    toast.success('Vehicle record inserted successfully to CSH!');
                })
                .catch((error) => {
                    console.error(error);
                    toast.error('Something went wrong!');
                })
                .finally(() => {
                    setPageLevelLoading(false);
                });
        }
    };

    const attemptUploadToCSH = () => {
        if (!selectedSnapshot) {
            toast.error('Please choose a snapshot first!');
            return;
        }

        if (!tempVehiclesData.length) {
            toast.error('No records to insert!');
            return;
        }

        if (!validateTempVehiclesData()) {
            toast.error('Some rows have invalid data! Please correct the red ones and try again.');
            return;
        }

        setShowUploadToCSHPrompt(true);
    };

    useEffect(() => {
        VehicleService.listVehicleSnapshots()
            .then((snapshotsResponse) => {
                const allSnapshots = snapshotsResponse.map((snapshot) => {
                    let snapshotLabel = `${snapshot._id} | ${Utils.getShortDateTime(snapshot.timestamp)}`;
                    if (snapshot.custGroup) {
                        snapshotLabel += ` | ${snapshot.custGroup}`;
                    }
                    return {
                        label: snapshotLabel,
                        value: snapshot._id
                    };
                });
                setSnapshotIdOptions(allSnapshots);
            })
            .catch((error) => console.error(error));
    }, []);

    return (
        <div className="space-y-8 p-4">
            {/* Page Level Loader */}
            <CustomLoader show={pageLevelLoading} />

            {/* Temporary Vehicles Section */}
            <div>
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold">Temporary Vehicles</h2>
                </div>
                <div className="flex items-center justify-between  mb-4">
                    <label
                        htmlFor="selectSnapshot"
                        className="block text-sm font-semibold text-muted"
                    >
                        Choose Snapshot
                    </label>
                    <Select
                        name="selectSnapshot"
                        options={snapshotIdOptions}
                        value={selectedSnapshot}
                        onChange={handleSnapshotChange}
                        placeholder="Select snapshot"
                        className="react-select-container"
                        classNamePrefix="react-select"
                        styles={{
                            control: (styles) => ({
                                ...styles,
                                width: '600px'
                            })
                        }}
                    />
                    <Input
                        placeholder="Search..."
                        onChange={(e) => {
                            setGlobalFilter(e.target.value);
                            setSearchValue(e.target.value);
                        }}
                        className="max-w-xs ml-2"
                        value={searchValue}
                    />
                </div>
                <div className="overflow-x-auto bg-white shadow-lg rounded-lg">
                    {selectedSnapshot === undefined ? (
                        <div className="text-center">
                            <br />
                            <br />
                            <br />
                            Please choose snapshot to view Temporary Vehicles...
                            <br />
                            <br />
                            <br />
                            <br />
                        </div>
                    ) : loadingTempVehicles ? (
                        <div className="text-center">
                            <br />
                            <br />
                            Loading Temporary Vehicles for {selectedSnapshot.label}..
                            <br />
                            <br />
                            Please wait...{' '}
                            <Spinner
                                className="mr-3"
                                style={{ display: 'inline' }}
                            />
                            <br />
                            <br />
                            <br />
                        </div>
                    ) : (
                        <>
                            {/* Temporary Vehicles Table */}
                            <table
                                {...getTableProps()}
                                className="min-w-full table-auto"
                                style={{ minHeight: '550px' }}
                            >
                                <thead>
                                    {headerGroups.map((headerGroup) => (
                                        <tr
                                            {...headerGroup.getHeaderGroupProps()}
                                            className="border-b"
                                        >
                                            {headerGroup.headers.map((column) => (
                                                <th
                                                    {...column.getHeaderProps(column.getSortByToggleProps())}
                                                    className="py-3 px-4 text-left text-sm font-semibold cursor-pointer text-muted"
                                                >
                                                    {column.render('Header')}
                                                    <span className="ml-1">
                                                        {column.isSorted ? column.isSortedDesc ? <ChevronDown className="inline w-4 h-4" /> : <ChevronUp className="inline w-4 h-4" /> : null}
                                                    </span>
                                                </th>
                                            ))}
                                        </tr>
                                    ))}
                                </thead>

                                <tbody {...getTableBodyProps()}>
                                    {page.map((row) => {
                                        prepareRow(row);
                                        let extraRowClass = '';
                                        if (Array.isArray(row.original.errors)) {
                                            extraRowClass = row.original.errors.length > 0 ? 'bg-red-100' : 'bg-green-100';
                                        }
                                        return (
                                            <tr
                                                {...row.getRowProps()}
                                                className={cn('border-b hover:bg-muted/50', extraRowClass)}
                                            >
                                                {row.cells.map((cell) => (
                                                    <td
                                                        {...cell.getCellProps()}
                                                        className="py-2 px-4 text-sm text-muted align-top"
                                                    >
                                                        {cell.render('Cell')}
                                                    </td>
                                                ))}
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </>
                    )}
                </div>
                {selectedSnapshot && !loadingTempVehicles && tempVehiclesData.length > 0 && (
                    <>
                        {/* Pagination controls */}
                        <div className="flex items-center justify-between px-4 py-2 bg-white shadow-lg rounded-lg mt-2">
                            <div>
                                <Button
                                    onClick={() => gotoPage(0)}
                                    disabled={!canPreviousPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600"
                                >
                                    {'<<'}
                                </Button>
                                <Button
                                    onClick={() => previousPage()}
                                    disabled={!canPreviousPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                >
                                    {'<'}
                                </Button>
                                <Button
                                    onClick={() => nextPage()}
                                    disabled={!canNextPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                >
                                    {'>'}
                                </Button>
                                <Button
                                    onClick={() => gotoPage(pageCount - 1)}
                                    disabled={!canNextPage}
                                    className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                >
                                    {'>>'}
                                </Button>
                            </div>
                            <div>
                                <span>
                                    Page&nbsp;
                                    <strong>
                                        {pageIndex + 1} of {pageOptions.length}&nbsp;
                                    </strong>
                                </span>
                                <span>
                                    | Go to page:&nbsp;
                                    <input
                                        type="number"
                                        value={pageIndex + 1}
                                        onChange={(e) => {
                                            const page = e.target.value ? Number(e.target.value) - 1 : 0;
                                            gotoPage(page);
                                        }}
                                        style={{ width: '50px' }}
                                        className="border border-input px-1 py-1 text-center"
                                    />
                                </span>
                            </div>
                            <div>
                                Rows per page:&nbsp;
                                <select
                                    value={pageSize}
                                    onChange={(e) => {
                                        setPageSize(Number(e.target.value));
                                    }}
                                    className="border border-input px-3 py-1"
                                >
                                    {[5, 10, 20, 30, 40, 50].map((pageSize) => (
                                        <option
                                            key={pageSize}
                                            value={pageSize}
                                        >
                                            Show {pageSize}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </>
                )}
            </div>

            {/* Insert to CSH Button section */}
            <div>
                <YesNoDialog
                    title="Insert to CSH"
                    prompt="Do you want to insert the temporary vehicles to CSH?"
                    yesClicked={() => {
                        handleUploadToCSH();
                        setShowUploadToCSHPrompt(false);
                    }}
                    noClicked={() => {
                        // Does Nothing
                        setShowUploadToCSHPrompt(false);
                    }}
                    open={showUploadToCSHPrompt}
                />
                <Button
                    variant="outline"
                    size="sm"
                    className="bg-blue-600 text-white"
                    onClick={() => attemptUploadToCSH()}
                >
                    Insert to CSH
                </Button>
            </div>

            {/* CSH Records Table */}
            {!pageLevelLoading && <CSHViewModify />}
        </div>
    );
};

export default VehiclesView;
