'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '../../../components/ui-component/ui/button';
import { Input } from '../../../components/ui-component/ui/input';
import { useVehicleService } from '../../../../service';
import SimpleReactValidator from 'simple-react-validator';

import { toast } from 'sonner';
import { format } from 'date-fns';
import { cn } from '../../../lib/utils';
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle } from '../../../components/ui-component/ui/dialog';
import { CshRecordInterface, VehicleCshUpdateInterface, MbVehicleDataDetails } from '../../../../model';
import Select from 'react-select';
import { StaticLists } from '../../../lib/constants';
import { isString } from 'lodash';

interface EditViewProps {
    cshData: CshRecordInterface;
    refreshCallback: () => void;
}

const EditView: React.FC<EditViewProps> = ({ cshData, refreshCallback }) => {
    const [isDialogOpen, setIsDialogOpen] = useState(true);
    const VehicleService = useVehicleService();
    const [validator] = useState(
        new SimpleReactValidator({
            messages: {
                required: 'This field is required'
            }
        })
    );

    const [transmissionOptions] = useState(StaticLists.allTransmissionOptions);
    const [currentStep, setCurrentStep] = useState(1);
    const [vehicleFormAttributes, setVehicleFormAttributes] = useState<VehicleCshUpdateInterface>({
        regNo: '',
        vin: '',
        brand: '',
        model: '',
        derivative: '',
        options: '',
        basicColour: '',
        colourName: '',
        registrationDate: '',
        inFleetDate: '',
        deFleetDate: '',
        estimatedRRP: '',
        ciCode: '',
        retailer: '',
        fuel: '',
        transmission: undefined,
        towBar: false,
        wheelNuts: false,
        status: '',
        resourceEffectiveDate: '',
        resourceInEffectiveDate: '',
        comments: '',
        euroPak: false,
        awd: false,
        seats: 0
    });

    const nextStep = () => {
        setCurrentStep((prev) => Math.min(prev + 1, 3)); // Maximum steps: 3
    };

    const prevStep = () => {
        setCurrentStep((prev) => Math.max(prev - 1, 1)); // Minimum step: 1
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;

        if (name === 'vin') {
            if (value.length > 17) return;
        }
        if (type === 'checkbox') {
            setVehicleFormAttributes((prev) => ({
                ...prev,
                [name]: e.target.checked
            }));
        } else {
            setVehicleFormAttributes((prev) => ({
                ...prev,
                [name]: value
            }));
        }

        validator.showMessageFor(name);
    };

    useEffect(() => {
        if (isDialogOpen && cshData) {
            setCurrentStep(1);
            setEditFields(cshData);
        }
        validator.showMessages();
    }, [cshData, isDialogOpen]);

    const setEditFields = (rowData: any) => {
        const detailsObj: MbVehicleDataDetails = isString(rowData.mbVehicleData.details) ? JSON.parse(rowData.mbVehicleData.details) : rowData.mbVehicleData.details;
        setVehicleFormAttributes({
            regNo: rowData?.vehicleData?.vehicleRegNo,
            vin: rowData?.mbVehicleData?.vin,
            brand: detailsObj.BrandName,
            model: detailsObj.ModelName,
            derivative: detailsObj.Derivative,
            options: '',
            basicColour: detailsObj.BasicColour,
            colourName: detailsObj.Colour,
            registrationDate: detailsObj.RegDate,
            inFleetDate: format(rowData?.mbVehicleData?.infleetDate, 'yyyy-MM-dd'),
            deFleetDate: format(rowData?.mbVehicleData?.defleetDate, 'yyyy-MM-dd'),
            estimatedRRP: '',
            ciCode: '',
            retailer: '',
            fuel: detailsObj.Fuel,
            transmission: detailsObj.Transmission,
            towBar: detailsObj.Towbar,
            wheelNuts: detailsObj.LockingWheelNut,
            status: '',
            resourceEffectiveDate: format(rowData?.resourceData?.resourceEffDate, 'yyyy-MM-dd'),
            resourceInEffectiveDate: format(rowData?.resourceData?.resourceIneffDate, 'yyyy-MM-dd'),
            comments: detailsObj.Comments,
            euroPak: detailsObj.EuroPak,
            awd: detailsObj.AWD,
            seats: detailsObj.Seats
        });
    };

    const finalPayloadFormat = () => {
        const cshRecord = cshData;
        const detailsObj: MbVehicleDataDetails = isString(cshRecord.mbVehicleData.details) ? JSON.parse(cshRecord.mbVehicleData.details) : cshRecord.mbVehicleData.details;
        const finalPayload = {
            resource_id: cshRecord.resource_id,
            resourceData: {
                ...cshRecord.resourceData,
                resourceEffDate: format(vehicleFormAttributes.resourceEffectiveDate, 'yyyy-MM-dd'),
                resourceIneffDate: format(vehicleFormAttributes.resourceInEffectiveDate, 'yyyy-MM-dd')
            },
            vehicleData: { ...cshRecord.vehicleData },
            mbVehicleData: {
                regionParId: cshRecord.mbVehicleData.regionParId,
                vin: vehicleFormAttributes.vin,
                details: {
                    ...detailsObj,
                    Colour: vehicleFormAttributes.colourName,
                    Transmission: vehicleFormAttributes.transmission,
                    Towbar: vehicleFormAttributes.towBar,
                    Derivative: vehicleFormAttributes.derivative,
                    RegDate: vehicleFormAttributes?.registrationDate,
                    LockingWheelNut: vehicleFormAttributes.wheelNuts,
                    Fuel: vehicleFormAttributes.fuel,
                    BasicColour: vehicleFormAttributes.basicColour,
                    Comments: vehicleFormAttributes.comments,
                    EuroPak: vehicleFormAttributes.euroPak,
                    AWD: vehicleFormAttributes.awd,
                    Seats: vehicleFormAttributes.seats
                },
                vehModelId: cshRecord.mbVehicleData.vehModelId,
                infleetDate: format(vehicleFormAttributes.inFleetDate, 'yyyy-MM-dd'),
                defleetDate: format(vehicleFormAttributes.deFleetDate, 'yyyy-MM-dd'),
                vehValue: cshRecord.mbVehicleData.vehValue,
                vehParId: cshRecord.mbVehicleData.vehParId,
                replTime: cshRecord.mbVehicleData.replTime
            }
        };
        return finalPayload;
    };

    const editClicked = (): void => {
        setIsDialogOpen(true);
    };

    const handleEdit = (e: React.FormEvent) => {
        e.preventDefault();
        validator.showMessages();
        if (!vehicleFormAttributes.regNo) {
            validator.showMessages();
            return;
        }

        const isFormValid = validator.allValid();
        if (!isFormValid) {
            return;
        }

        VehicleService.updateCshVehicle(finalPayloadFormat())
            .then(() => {
                refreshCallback();
                toast.success(`Csh record updated successfully!`);
            })
            .catch(() => {
                toast.error(`Something went wrong :-( !`);
            })
            .finally(() => {
                setIsDialogOpen(false);
            });
    };

    const handleDropdownChange = (selectedOption: any) => {
        setVehicleFormAttributes((prev) => ({
            ...prev,
            transmission: selectedOption
        }));
    };

    return (
        <>
            <Dialog>
                <DialogTrigger asChild>
                    <Button
                        variant="outline"
                        size="sm"
                        className="bg-blue-600 text-white"
                        onClick={editClicked}
                    >
                        Edit
                    </Button>
                </DialogTrigger>
                {isDialogOpen && (
                    <DialogContent className="bg-white text-black">
                        <DialogHeader>
                            <DialogTitle>Edit Record</DialogTitle>
                        </DialogHeader>
                        <div>
                            <form onSubmit={handleEdit}>
                                {/* Step Navigation */}
                                <div className="flex justify-between items-center mb-6">
                                    <Button
                                        type="button"
                                        onClick={prevStep}
                                        disabled={currentStep === 1}
                                        className={cn('px-4 py-2 rounded-md text-white', currentStep === 1 ? 'bg-gray-300 cursor-not-allowed' : 'bg-yellow-500 hover:bg-yellow-600')}
                                    >
                                        Previous
                                    </Button>
                                    <span className="text-gray-600">Step {currentStep} of 3</span>
                                    <Button
                                        type="button"
                                        onClick={nextStep}
                                        disabled={currentStep === 3}
                                        className={cn('px-4 py-2 rounded-md text-white', currentStep === 3 ? 'bg-gray-300 cursor-not-allowed' : 'bg-yellow-500 hover:bg-yellow-600')}
                                    >
                                        Next
                                    </Button>
                                </div>

                                {/* Step Content */}
                                {/* Step 1: Basic Info */}
                                {currentStep === 1 && (
                                    <div className="space-y-4">
                                        <div>
                                            <label
                                                htmlFor="vehicle-vin"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                VIN
                                            </label>
                                            <Input
                                                type="text"
                                                id="vehicle-vin"
                                                name="vin"
                                                value={vehicleFormAttributes.vin}
                                                onChange={handleChange}
                                                placeholder="Enter vehicle VIN"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('vin', vehicleFormAttributes.vin, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="model-derivative"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Derivative
                                            </label>
                                            <Input
                                                type="text"
                                                id="model-derivative"
                                                name="derivative"
                                                value={vehicleFormAttributes.derivative}
                                                onChange={handleChange}
                                                placeholder="Enter vehicle Derivative"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('derivative', vehicleFormAttributes.derivative, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="resource-effective-date"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Effective Date
                                            </label>
                                            <Input
                                                type="date"
                                                id="resource-effective-date"
                                                name="resourceEffectiveDate"
                                                value={vehicleFormAttributes.resourceEffectiveDate}
                                                onChange={handleChange}
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('resourceEffectiveDate', vehicleFormAttributes.resourceEffectiveDate, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="resource-ineffective-date"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Ineffective Date
                                            </label>
                                            <Input
                                                type="date"
                                                id="resource-ineffective-date"
                                                name="resourceInEffectiveDate"
                                                value={vehicleFormAttributes.resourceInEffectiveDate}
                                                onChange={handleChange}
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('resourceInEffectiveDate', vehicleFormAttributes.resourceInEffectiveDate, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="registration-date"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Registration Date
                                            </label>
                                            <Input
                                                type="date"
                                                id="registration-date"
                                                name="registrationDate"
                                                value={vehicleFormAttributes.registrationDate}
                                                onChange={handleChange}
                                                placeholder="Enter vehicle registration date"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('registrationDate', vehicleFormAttributes.registrationDate, 'required')}
                                        </div>
                                    </div>
                                )}

                                {/* Step 2: Address Info (Vehicle Registration Details) */}
                                {currentStep === 2 && (
                                    <div className="space-y-4">
                                        <div>
                                            <label
                                                htmlFor="infleet-date"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                InFleet Date
                                            </label>
                                            <Input
                                                type="date"
                                                id="infleet-date"
                                                name="inFleetDate"
                                                value={vehicleFormAttributes.inFleetDate}
                                                onChange={handleChange}
                                                placeholder="Enter vehicle InFleet Date"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('infleetDate', vehicleFormAttributes.inFleetDate, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="defleet-date"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                DeFleet Date
                                            </label>
                                            <Input
                                                type="date"
                                                id="defleet-date"
                                                name="deFleetDate"
                                                value={vehicleFormAttributes.deFleetDate}
                                                onChange={handleChange}
                                                placeholder="Enter vehicle InFleet Date"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('defleetDate', vehicleFormAttributes.deFleetDate, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="basic-colour"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Basic Colour
                                            </label>
                                            <Input
                                                type="text"
                                                name="basicColour"
                                                value={vehicleFormAttributes.basicColour}
                                                onChange={handleChange}
                                                id="basic-colour"
                                                placeholder="Enter Basic Colour"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('basicColour', vehicleFormAttributes.basicColour, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="colour-name"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Colour Name
                                            </label>
                                            <Input
                                                type="text"
                                                name="colourName"
                                                value={vehicleFormAttributes.colourName}
                                                onChange={handleChange}
                                                id="colour-name"
                                                placeholder="Enter Colour Name"
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                            />
                                            {validator.message('colourName', vehicleFormAttributes.colourName, 'required')}
                                        </div>
                                        <div className="flex items-center">
                                            <label
                                                htmlFor="euro-pak"
                                                className="w-full block text-sm font-medium text-gray-700"
                                            >
                                                Euro Pak
                                            </label>
                                            <Input
                                                type="checkbox"
                                                id="euro-pak"
                                                name="euroPak"
                                                checked={vehicleFormAttributes.euroPak}
                                                onChange={handleChange}
                                                className={`w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500`}
                                            />
                                        </div>
                                        <div className="flex items-center">
                                            <label
                                                htmlFor="awd"
                                                className="w-full block text-sm font-medium text-gray-700"
                                            >
                                                AWD
                                            </label>
                                            <Input
                                                type="checkbox"
                                                id="awd"
                                                name="awd"
                                                checked={vehicleFormAttributes.awd}
                                                onChange={handleChange}
                                                className={`w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500`}
                                            />
                                        </div>
                                    </div>
                                )}

                                {/* Step 3: Contact Info (Owner Details) */}
                                {currentStep === 3 && (
                                    <div className="space-y-4">
                                        {/* Transmission */}
                                        <div>
                                            <label
                                                htmlFor="transmission"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Transmission
                                            </label>
                                            <Select
                                                name="transmission"
                                                options={transmissionOptions}
                                                value={transmissionOptions.find((option) => option.value === vehicleFormAttributes.transmission)}
                                                onChange={handleDropdownChange}
                                                placeholder="Select transmission"
                                                className="react-select-container"
                                                classNamePrefix="react-select"
                                            />
                                            {validator.message('transmission', vehicleFormAttributes.transmission, 'required')}
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="comments"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Comments
                                            </label>
                                            <Input
                                                type="text"
                                                id="Comment"
                                                name="comments"
                                                value={vehicleFormAttributes.comments}
                                                onChange={handleChange}
                                                placeholder="Enter vehicle comments"
                                                className={`w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500`}
                                            />
                                            {/* No validation for comments */}
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Seats</label>
                                            <Input
                                                type="number"
                                                name="seats"
                                                value={vehicleFormAttributes.seats}
                                                onChange={handleChange}
                                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500 text-black"
                                            />
                                        </div>
                                        {/* Tow Bar */}
                                        <div className="flex items-center">
                                            <label
                                                htmlFor="tow-bar"
                                                className="w-full block text-sm font-medium text-gray-700"
                                            >
                                                Tow Bar
                                            </label>
                                            <Input
                                                type="checkbox"
                                                id="tow-bar"
                                                name="towBar"
                                                checked={vehicleFormAttributes.towBar}
                                                onChange={handleChange}
                                                placeholder="Enter vehicle Tow Bar"
                                                className={`w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500`}
                                            />
                                        </div>
                                        {/* Wheel Nuts */}
                                        <div className="flex items-center">
                                            <label
                                                htmlFor="wheel-nuts"
                                                className="w-full block text-sm font-medium text-gray-700"
                                            >
                                                Wheel Nuts
                                            </label>
                                            <Input
                                                type="checkbox"
                                                id="wheel-nuts"
                                                name="wheelNuts"
                                                checked={vehicleFormAttributes.wheelNuts}
                                                onChange={handleChange}
                                                placeholder="Enter vehicle Wheel Nuts"
                                                className={`w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500`}
                                            />
                                        </div>
                                    </div>
                                )}

                                {/* Submit Button on Final Step */}
                                {currentStep === 3 && (
                                    <div className="mt-4 flex justify-end">
                                        <Button
                                            disabled={!validator.allValid()}
                                            type="submit"
                                            className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
                                        >
                                            Submit
                                        </Button>
                                    </div>
                                )}
                            </form>
                        </div>
                    </DialogContent>
                )}
            </Dialog>
        </>
    );
};

export default EditView;
