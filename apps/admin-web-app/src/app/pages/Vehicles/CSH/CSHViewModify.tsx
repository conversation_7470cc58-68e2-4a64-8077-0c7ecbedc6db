'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useTable, useSortBy, useGlobalFilter, useFilters, usePagination } from 'react-table';
import { ChevronDown, ChevronUp } from 'lucide-react';
import Select from 'react-select';
import { format } from 'date-fns';
import { Spinner } from '@aa/ui/core/spinner';
import { Button, Input } from '../../../components/ui-component/ui';
import { CshRecordInterface, SelectNumInterface, MbVehicleDataDetails } from '../../../../model';
import { useVehicleService, useSupplierService } from '../../../../service';
import EditView from './EditCSH';
import { isString } from 'lodash';

const CSHViewModify = () => {
    const [cshRecords, setCSHRecords] = useState<CshRecordInterface[]>([]);
    const [loadingCSHRecords, setLoadingCSHRecords] = useState(true);
    const [searchValue, setSearchValue] = useState<string>('');
    const VehicleService = useVehicleService();
    const SupplierService = useSupplierService();
    const [selectedSupplierNetwork, setSelectedSupplierNetwork] = useState<SelectNumInterface | undefined>(undefined);
    const [supplierNetworkOptions, setSupplierNetworkOptions] = useState<SelectNumInterface[]>([]);

    const hiddenColumns = [
        'vehicleData.vehicleRegNo',
        'mbVehicleData.vin',
        'mbVehicleData.infleetDate',
        'mbVehicleData.defleetDate',
        'mbVehicleData.vehValue',
        'mbVehicleData.details',
        'mbVehicleData.details.BrandName',
        'mbVehicleData.details.ModelName',
        'mbVehicleData.details.Derivative',
        'mbVehicleData.details.BasicColour',
        'mbVehicleData.details.Colour',
        'mbVehicleData.details.RegDate',
        'mbVehicleData.details.EstimatedRRP',
        'mbVehicleData.details.Retailer',
        'mbVehicleData.details.Fuel',
        'mbVehicleData.details.Status',
        'mbVehicleData.details.Transmission'
    ];
    const createColumns = () => [
        {
            Header: 'Basic Info',
            accessor: 'basicInfo',
            Cell: ({ row }) => {
                const detailsObj: MbVehicleDataDetails = isString(row.original.mbVehicleData.details) ? JSON.parse(row.original.mbVehicleData.details) : row.original.mbVehicleData.details;
                const fields = [
                    { key: 'Reg No', value: row.original.vehicleData.vehicleRegNo },
                    { key: 'VIN', value: row.original.mbVehicleData.vin },
                    { key: 'Brand', value: detailsObj.BrandName ? detailsObj.BrandName : 'Not Set' },
                    { key: 'Model', value: detailsObj.ModelName ? detailsObj.ModelName : 'Not Set' }
                ];
                return (
                    <>
                        {fields.map((field, idx) => (
                            <div
                                key={field.key}
                                className="flex items-center whitespace-nowrap"
                            >
                                <i>{field.key}</i>: <span className="ml-1 border-b px-1">{field.value}</span>
                            </div>
                        ))}
                    </>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Dates',
            accessor: 'dates',
            Cell: ({ row }) => {
                const fields = [
                    { key: 'In-Fleet', value: row.original.mbVehicleData.infleetDate ? format(new Date(row.original.mbVehicleData.infleetDate), 'dd-MM-yyyy') : 'Not Set' },
                    { key: 'De-Fleet', value: row.original.mbVehicleData.defleetDate ? format(new Date(row.original.mbVehicleData.defleetDate), 'dd-MM-yyyy') : 'Not Set' }
                ];
                return (
                    <>
                        {fields.map((field, idx) => (
                            <div
                                key={field.key}
                                className={idx > 0 ? 'mt-3' : ''}
                            >
                                <i>{field.key}</i>:<br /> <span className="border-b whitespace-nowrap">{field.value}</span>
                            </div>
                        ))}
                    </>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Detail Info 1',
            accessor: 'detailsInfo1',
            Cell: ({ row }) => {
                const detailsObj: MbVehicleDataDetails = isString(row.original.mbVehicleData.details) ? JSON.parse(row.original.mbVehicleData.details) : row.original.mbVehicleData.details;
                const fields = [
                    { key: 'Derivative', value: detailsObj.Derivative ? detailsObj.Derivative : 'Not Set' },
                    { key: 'Basic Colour', value: detailsObj.BasicColour ? detailsObj.BasicColour : 'Not Set' },
                    { key: 'Colour Name', value: detailsObj.Colour ? detailsObj.Colour : 'Not Set' },
                    { key: 'Reg. Date', value: detailsObj.RegDate ? detailsObj.RegDate : 'Not Set' }
                ];
                return (
                    <>
                        {fields.map((field, idx) => (
                            <div
                                key={field.key}
                                className="flex items-center whitespace-nowrap"
                            >
                                <i>{field.key}</i>: <span className="ml-1 border-b px-1">{field.value}</span>
                            </div>
                        ))}
                    </>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Detail Info 2',
            accessor: 'detailsInfo2',
            Cell: ({ row }) => {
                const detailsObj: MbVehicleDataDetails = isString(row.original.mbVehicleData.details) ? JSON.parse(row.original.mbVehicleData.details) : row.original.mbVehicleData.details;
                const fields = [
                    { key: 'Estimated RRP', value: detailsObj.EstimatedRRP ? detailsObj.EstimatedRRP : 'Not Set' },
                    { key: 'Retailer', value: detailsObj.Retailer ? detailsObj.Retailer : 'Not Set' },
                    { key: 'Fuel', value: detailsObj.Fuel ? detailsObj.Fuel : 'Not Set' }
                ];
                return (
                    <>
                        {fields.map((field, idx) => (
                            <div
                                key={field.key}
                                className="flex items-center whitespace-nowrap"
                            >
                                <i>{field.key}</i>: <span className="ml-1 border-b px-1">{field.value}</span>
                            </div>
                        ))}
                    </>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Detail Info 3',
            accessor: 'detailsInfo3',
            Cell: ({ row }) => {
                const detailsObj: MbVehicleDataDetails = isString(row.original.mbVehicleData.details) ? JSON.parse(row.original.mbVehicleData.details) : row.original.mbVehicleData.details;
                const fields = [
                    { key: 'Status', value: detailsObj.Status ? detailsObj.Status : 'Not Set' },
                    { key: 'Transmission', value: detailsObj.Transmission ? detailsObj.Transmission : 'Not Set' },
                    { key: 'Tow Bar', value: detailsObj.Towbar ? 'Yes' : 'No' },
                    { key: 'Wheel Nuts', value: detailsObj.LockingWheelNut ? 'Yes' : 'No' }
                ];
                return (
                    <>
                        {fields.map((field, idx) => (
                            <div
                                key={field.key}
                                className="flex items-center whitespace-nowrap"
                            >
                                <i>{field.key}</i>: <span className="ml-1 border-b px-1">{field.value}</span>
                            </div>
                        ))}
                    </>
                );
            },
            disableSortBy: true
        },
        {
            Header: 'Actions',
            Cell: ({ row }) => (
                <EditView
                    cshData={row.original}
                    refreshCallback={refreshCallback}
                />
            )
        },
        ...hiddenColumns.map((columnName) => ({
            Header: columnName,
            accessor: columnName
        }))
    ];

    const refreshCallback = () => fetchVehicleCSHRecords(selectedSupplierNetwork?.value);

    const handleSupplierNetworkChange = (selectedOption: SelectNumInterface) => {
        if (selectedOption) {
            setGlobalFilter('');
            setSearchValue('');
            setSelectedSupplierNetwork(selectedOption);
            fetchVehicleCSHRecords(selectedOption.value);
        }
    };

    const fetchVehicleCSHRecords = async (supplierNetworkId: number) => {
        setLoadingCSHRecords(true);
        VehicleService.getCshVehicles(supplierNetworkId)
            .then((cshResponse) => {
                setCSHRecords(cshResponse);
            })
            .catch((error) => console.error(error))
            .finally(() => setLoadingCSHRecords(false));
    };

    const fetchSupplierNetwork = () => {
        SupplierService.fetchSupplierNetworks()
            .then((resp) => {
                const allSupplierNetworks = resp.map((supplierNetwork) => ({
                    label: supplierNetwork.supNetworkName,
                    value: supplierNetwork.supNetworkId
                }));
                setSupplierNetworkOptions(allSupplierNetworks);
            })
            .catch(console.error);
    };

    useEffect(() => {
        fetchVehicleCSHRecords('');
        fetchSupplierNetwork();
    }, []);

    const columns = useMemo(() => createColumns(), []);

    const cshTableInstance = useTable(
        {
            columns,
            data: cshRecords,
            initialState: { pageIndex: 0, pageSize: 5, hiddenColumns }
        },
        useFilters,
        useGlobalFilter,
        useSortBy,
        usePagination
    );

    const {
        getTableProps: getCshTableProps,
        getTableBodyProps: getCshTableBodyProps,
        headerGroups: cshHeaderGroups,
        prepareRow: prepareCshRow,
        setGlobalFilter,
        page,
        canPreviousPage,
        canNextPage,
        pageOptions,
        pageCount,
        gotoPage,
        nextPage,
        previousPage,
        setPageSize,
        state: { pageIndex, pageSize }
    } = cshTableInstance;

    return (
        <div>
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">CSH Records</h2>
            </div>
            <div className="flex items-center justify-between  mb-4">
                <label
                    htmlFor="customerGroup"
                    className="block text-sm font-semibold text-muted"
                >
                    Choose Supplier Network
                </label>
                <Select
                    name="supNetworkID"
                    options={supplierNetworkOptions}
                    value={selectedSupplierNetwork}
                    onChange={handleSupplierNetworkChange}
                    placeholder="Select supplier network"
                    className="react-select-container"
                    classNamePrefix="react-select"
                />
                <Input
                    placeholder="Search..."
                    onChange={(e) => {
                        setGlobalFilter(e.target.value);
                        setSearchValue(e.target.value);
                    }}
                    className="max-w-xs ml-2"
                    value={searchValue}
                />
            </div>
            <div className="overflow-x-auto bg-white shadow-lg rounded-lg">
                {loadingCSHRecords ? (
                    <div className="text-center">
                        <br />
                        <br />
                        Loading CSH Vehicles..
                        <br />
                        <br />
                        Please wait...{' '}
                        <Spinner
                            className="mr-3"
                            style={{ display: 'inline' }}
                        />
                        <br />
                        <br />
                        <br />
                    </div>
                ) : (
                    <>
                        {/* CSH Vehicles Table */}
                        <table
                            {...getCshTableProps()}
                            className="min-w-full table-auto"
                            style={{ minHeight: '550px' }}
                        >
                            <thead>
                                {cshHeaderGroups.map((headerGroup) => (
                                    <tr
                                        {...headerGroup.getHeaderGroupProps()}
                                        className="border-b"
                                    >
                                        {headerGroup.headers.map((column) => (
                                            <th
                                                {...column.getHeaderProps(column.getSortByToggleProps())}
                                                className="py-3 px-4 text-left text-sm font-semibold cursor-pointer text-muted"
                                            >
                                                {column.render('Header')}
                                                <span className="ml-1">
                                                    {column.isSorted ? column.isSortedDesc ? <ChevronDown className="inline w-4 h-4" /> : <ChevronUp className="inline w-4 h-4" /> : null}
                                                </span>
                                            </th>
                                        ))}
                                    </tr>
                                ))}
                            </thead>

                            <tbody {...getCshTableBodyProps()}>
                                {page.map((row) => {
                                    prepareCshRow(row);
                                    return (
                                        <tr
                                            {...row.getRowProps()}
                                            className="border-b hover:bg-muted/50"
                                        >
                                            {row.cells.map((cell) => (
                                                <td
                                                    {...cell.getCellProps()}
                                                    className="py-2 px-4 text-sm text-muted align-top"
                                                >
                                                    {cell.render('Cell')}
                                                </td>
                                            ))}
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </>
                )}
            </div>
            {!loadingCSHRecords && cshRecords.length > 0 && (
                <>
                    {/* Pagination controls */}
                    <div className="flex items-center justify-between px-4 py-2 bg-white shadow-lg rounded-lg mt-2">
                        <div>
                            <Button
                                onClick={() => gotoPage(0)}
                                disabled={!canPreviousPage}
                                className="bg-yellow-500 text-white hover:bg-yellow-600"
                            >
                                {'<<'}
                            </Button>
                            <Button
                                onClick={() => previousPage()}
                                disabled={!canPreviousPage}
                                className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                            >
                                {'<'}
                            </Button>
                            <Button
                                onClick={() => nextPage()}
                                disabled={!canNextPage}
                                className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                            >
                                {'>'}
                            </Button>
                            <Button
                                onClick={() => gotoPage(pageCount - 1)}
                                disabled={!canNextPage}
                                className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                            >
                                {'>>'}
                            </Button>
                        </div>
                        <div>
                            <span>
                                Page&nbsp;
                                <strong>
                                    {pageIndex + 1} of {pageOptions.length}&nbsp;
                                </strong>
                            </span>
                            <span>
                                | Go to page:&nbsp;
                                <input
                                    type="number"
                                    value={pageIndex + 1}
                                    onChange={(e) => {
                                        const page = e.target.value ? Number(e.target.value) - 1 : 0;
                                        gotoPage(page);
                                    }}
                                    style={{ width: '50px' }}
                                    className="border border-input px-1 py-1 text-center"
                                />
                            </span>
                        </div>
                        <div>
                            Rows per page:&nbsp;
                            <select
                                value={pageSize}
                                onChange={(e) => {
                                    setPageSize(Number(e.target.value));
                                }}
                                className="border border-input px-3 py-1"
                            >
                                {[5, 10, 20, 30, 40, 50].map((pageSize) => (
                                    <option
                                        key={pageSize}
                                        value={pageSize}
                                    >
                                        Show {pageSize}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

export default CSHViewModify;
