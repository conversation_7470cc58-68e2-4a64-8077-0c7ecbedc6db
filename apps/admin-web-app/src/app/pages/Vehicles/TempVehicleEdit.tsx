'use client';

import React, { useState, useEffect } from 'react';
import SimpleReactValidator from 'simple-react-validator';
import Select from 'react-select';
import { toast } from 'sonner';

import { VehicleData, VehicleMongoInsertInterface } from '@aa/admin-helpers';
import { cn } from '../../lib/utils'; // ShadCN utility for conditional classes
import { Button, Input } from '../../components/ui-component/ui';
import { useVehicleService } from '../../../service';
import { MbVehicleDataDetails, SelectInterface } from '../../../model';
import { StaticLists } from '../../lib/constants';

interface TempVehicleEditProps {
    editRecord: VehicleData;
    snapshotId: string;
    updateCallback: () => void;
}

export const TempVehicleEdit: React.FC<TempVehicleEditProps> = (props) => {
    const [fuelOptions] = useState<SelectInterface[]>(StaticLists.allFuelOptions);
    const [transmissionOptions] = useState<SelectInterface[]>(StaticLists.allTransmissionOptions);
    const [currentStep, setCurrentStep] = useState(1);
    const [lastStep, setLastStep] = useState<number>(0);
    const VehicleService = useVehicleService();
    const [vehicleFormAttributes, setVehicleFormAttributes] = useState<VehicleMongoInsertInterface>({
        snapshotId: '',
        custGroup: '',
        regNo: '',
        vin: '',
        brand: '',
        model: '',
        derivative: '',
        options: '',
        basicColour: '',
        colourName: '',
        registrationDate: '',
        inFleetDate: '',
        deFleetDate: '',
        estimatedRRP: '',
        ciCode: '',
        retailer: '',
        fuel: '',
        transmission: '',
        towBar: 0,
        lockingWheelNut: 0,
        status: ''
    });
    const [validator] = useState(
        new SimpleReactValidator({
            messages: {
                required: 'This field is required',
                numeric: 'This field must be a number'
            }
        })
    );

    const validateAtStep = (step: number): boolean => {
        if (step === lastStep) {
            // If the step is already validated, let user see next step
            // Anyways, the user can't submit the form without validation
            return true;
        }
        switch (step) {
            case 1:
                validator.showMessageFor('vin');
                validator.showMessageFor('brand');
                validator.showMessageFor('model');
                validator.showMessageFor('derivative');
                return validator.fieldValid('vin') && validator.fieldValid('brand') && validator.fieldValid('model') && validator.fieldValid('derivative');
            case 2:
                validator.showMessageFor('basicColour');
                validator.showMessageFor('colourName');
                validator.showMessageFor('registrationDate');
                validator.showMessageFor('inFleetDate');
                validator.showMessageFor('estimatedRRP');
                return (
                    validator.fieldValid('basicColour') &&
                    validator.fieldValid('colourName') &&
                    validator.fieldValid('registrationDate') &&
                    validator.fieldValid('inFleetDate') &&
                    validator.fieldValid('estimatedRRP')
                );
            case 3:
                validator.showMessageFor('ciCode');
                validator.showMessageFor('retailer');
                validator.showMessageFor('fuel');
                validator.showMessageFor('transmission');
                return validator.fieldValid('ciCode') && validator.fieldValid('retailer') && validator.fieldValid('fuel') && validator.fieldValid('transmission');
            default:
                return false;
        }
    };

    const nextStep = () => {
        if (!validateAtStep(currentStep)) {
            setVehicleFormAttributes((prev) => ({ ...prev })); // Trigger re-render to show validation messages
            setLastStep(currentStep); // Save the last step validated
            return;
        }
        setCurrentStep((prev) => Math.min(prev + 1, 3)); // Maximum steps: 3
    };

    const prevStep = () => {
        setCurrentStep((prev) => Math.max(prev - 1, 1)); // Minimum step: 1
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setVehicleFormAttributes((prev) => ({ ...prev, [name]: value }));
    };

    const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = e.target;
        setVehicleFormAttributes((prev) => ({ ...prev, [name]: checked ? 1 : 0 }));
    };

    const handleFuelTypeChange = (selectedFuel: SelectInterface) => {
        setVehicleFormAttributes((prev) => ({ ...prev, fuel: selectedFuel.value }));
    };

    const handleTransmissionChange = (selectedTranmission: SelectInterface) => {
        setVehicleFormAttributes((prev) => ({ ...prev, transmission: selectedTranmission.value }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        validator.showMessages();
        if (!validator.allValid()) {
            setVehicleFormAttributes((prev) => ({ ...prev })); // Trigger re-render to show validation messages
            return;
        }

        const payload = { ...vehicleFormAttributes };
        // console.log('Form Payload:', payload);
        VehicleService.upsertTempVehicle(payload)
            .then(() => {
                toast.success('Temp Vehicle record updated successfully!');
                props.updateCallback();
            })
            .catch(() => {
                toast.error('Something went wrong :-( !');
            });
    };

    useEffect(() => {
        const setFormValues = () => {
            const vehicleRow = props.editRecord;
            const detailsObj: MbVehicleDataDetails = props.editRecord.mbVehicleData.details ? JSON.parse(props.editRecord.mbVehicleData.details) : {};
            // console.log('vehicleRow', vehicleRow);
            // console.log('detailsObj', detailsObj);
            setVehicleFormAttributes((prev: VehicleMongoInsertInterface) => ({
                ...prev,
                snapshotId: props.snapshotId,
                custGroup: vehicleRow.custGroup,
                regNo: vehicleRow.vehicleData.vehicleRegNo,
                vin: vehicleRow.mbVehicleData.vin,
                brand: detailsObj.BrandName,
                model: detailsObj.ModelName,
                derivative: detailsObj.Derivative,
                basicColour: detailsObj.BasicColour ? detailsObj.BasicColour : '',
                colourName: detailsObj.Colour ? detailsObj.Colour : '',
                registrationDate: detailsObj.RegDate ? detailsObj.RegDate.substring(0, 10) : '',
                inFleetDate: vehicleRow.mbVehicleData.infleetDate ? vehicleRow.mbVehicleData.infleetDate.substring(0, 10) : '',
                deFleetDate: vehicleRow.mbVehicleData.defleetDate ? vehicleRow.mbVehicleData.defleetDate.substring(0, 10) : '',
                estimatedRRP: vehicleRow.mbVehicleData.vehValue ?? '',
                ciCode: vehicleRow.ciCode,
                retailer: detailsObj.Retailer,
                fuel: detailsObj.Fuel,
                transmission: detailsObj.Transmission ?? '',
                towBar: detailsObj.Towbar ? 1 : 0,
                lockingWheelNut: detailsObj.LockingWheelNut ? 1 : 0,
                status: detailsObj.Status
            }));
        };

        setFormValues();
    }, [props.editRecord, props.snapshotId]);

    return (
        <div>
            <form onSubmit={handleSubmit}>
                {/* Step Navigation */}
                <div className="flex justify-between items-center mb-6">
                    <Button
                        type="button"
                        onClick={prevStep}
                        disabled={currentStep === 1}
                        className={cn('px-4 py-2 rounded-md text-white', currentStep === 1 ? 'bg-gray-300 cursor-not-allowed' : 'bg-yellow-500 hover:bg-yellow-600')}
                    >
                        Previous
                    </Button>
                    <span className="text-gray-600">Step {currentStep} of 3</span>
                    <Button
                        type="button"
                        onClick={nextStep}
                        disabled={currentStep === 3}
                        className={cn('px-4 py-2 rounded-md text-white', currentStep === 3 ? 'bg-gray-300 cursor-not-allowed' : 'bg-yellow-500 hover:bg-yellow-600')}
                    >
                        Next
                    </Button>
                </div>

                {/* Step Content */}
                {/* Step 1 */}
                {currentStep === 1 && (
                    <div className="space-y-4">
                        <div>
                            <label
                                htmlFor="reg-no"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Reg No (Disabled)
                            </label>
                            <Input
                                type="text"
                                id="reg-no"
                                name="regNo"
                                value={vehicleFormAttributes.regNo}
                                onChange={handleChange}
                                placeholder="Enter vehicle Reg No"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                disabled={true}
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="vehicle-vin"
                                className="block text-sm font-medium text-gray-700"
                            >
                                VIN
                            </label>
                            <Input
                                type="text"
                                id="vehicle-vin"
                                name="vin"
                                value={vehicleFormAttributes.vin}
                                onChange={handleChange}
                                placeholder="Enter vehicle VIN"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('vin', vehicleFormAttributes.vin, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="vehicle-brand"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Brand
                            </label>
                            <Input
                                type="text"
                                id="vehicle-brand"
                                name="brand"
                                value={vehicleFormAttributes.brand}
                                onChange={handleChange}
                                placeholder="Enter vehicle make"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('brand', vehicleFormAttributes.brand, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="vehicle-model"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Model
                            </label>
                            <Input
                                type="text"
                                id="vehicle-model"
                                name="model"
                                value={vehicleFormAttributes.model}
                                onChange={handleChange}
                                placeholder="Enter vehicle model"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('model', vehicleFormAttributes.model, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="model-derivative"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Derivative
                            </label>
                            <Input
                                type="text"
                                id="model-derivative"
                                name="derivative"
                                value={vehicleFormAttributes.derivative}
                                onChange={handleChange}
                                placeholder="Enter vehicle Derivative"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('derivative', vehicleFormAttributes.derivative, 'required')}
                        </div>
                    </div>
                )}

                {/* Step 2 */}
                {currentStep === 2 && (
                    <div className="space-y-4">
                        <div>
                            <label
                                htmlFor="basic-colour"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Basic Colour
                            </label>
                            <Input
                                type="text"
                                name="basicColour"
                                value={vehicleFormAttributes.basicColour}
                                onChange={handleChange}
                                id="basic-colour"
                                placeholder="Enter Basic Colour"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('basicColour', vehicleFormAttributes.basicColour, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="colour-name"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Colour Name
                            </label>
                            <Input
                                type="text"
                                name="colourName"
                                value={vehicleFormAttributes.colourName}
                                onChange={handleChange}
                                id="colour-name"
                                placeholder="Enter Colour Name"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('colourName', vehicleFormAttributes.colourName, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="registration-date"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Registration Date
                            </label>
                            <Input
                                type="date"
                                id="registration-date"
                                name="registrationDate"
                                value={vehicleFormAttributes.registrationDate}
                                onChange={handleChange}
                                placeholder="Enter vehicle registration date"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('registrationDate', vehicleFormAttributes.registrationDate, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="infleet-date"
                                className="block text-sm font-medium text-gray-700"
                            >
                                In-Fleet Date
                            </label>
                            <Input
                                type="date"
                                id="infleet-date"
                                name="inFleetDate"
                                value={vehicleFormAttributes.inFleetDate}
                                onChange={handleChange}
                                placeholder="Enter vehicle InFleet Date"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('inFleetDate', vehicleFormAttributes.inFleetDate, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="defleet-date"
                                className="block text-sm font-medium text-gray-700"
                            >
                                De-Fleet Date
                            </label>
                            <Input
                                type="date"
                                id="defleet-date"
                                name="deFleetDate"
                                value={vehicleFormAttributes.deFleetDate}
                                onChange={handleChange}
                                placeholder="Enter vehicle DeFleet Date"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {/* No validation for DeFleet date  */}
                        </div>
                        <div>
                            <label
                                htmlFor="estimated-rrp"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Estimated RRP
                            </label>
                            <Input
                                type="text"
                                id="estimated-rrp"
                                name="estimatedRRP"
                                value={vehicleFormAttributes.estimatedRRP}
                                onChange={handleChange}
                                placeholder="Enter vehicle Estimated RRP"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('estimatedRRP', vehicleFormAttributes.estimatedRRP, 'required|numeric')}
                        </div>
                    </div>
                )}

                {/* Step 3 */}
                {currentStep === 3 && (
                    <div className="space-y-4">
                        <div>
                            <label
                                htmlFor="ci-code"
                                className="block text-sm font-medium text-gray-700"
                            >
                                CI Code
                            </label>
                            <Input
                                type="text"
                                id="ci-code"
                                name="ciCode"
                                value={vehicleFormAttributes.ciCode}
                                onChange={handleChange}
                                placeholder="Enter vehicle CI Code"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                                maxLength={50}
                            />
                            {validator.message('ciCode', vehicleFormAttributes.ciCode, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="retailer"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Retailer
                            </label>
                            <Input
                                type="text"
                                id="retailer"
                                name="retailer"
                                value={vehicleFormAttributes.retailer}
                                onChange={handleChange}
                                placeholder="Enter vehicle Retailer"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {validator.message('retailer', vehicleFormAttributes.retailer, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="fuel"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Fuel
                            </label>
                            <Select
                                id="fuel"
                                name="fuel"
                                options={fuelOptions}
                                value={fuelOptions.find((opt) => opt.value === vehicleFormAttributes.fuel)}
                                onChange={handleFuelTypeChange}
                                className="react-select-container"
                                classNamePrefix="react-select"
                            />
                            {validator.message('fuel', vehicleFormAttributes.fuel, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="transmission"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Transmission
                            </label>
                            <Select
                                name="transmission"
                                options={transmissionOptions}
                                value={transmissionOptions.find((trans) => trans.value === vehicleFormAttributes.transmission)}
                                onChange={handleTransmissionChange}
                                placeholder="Select transmission"
                                className="react-select-container"
                                classNamePrefix="react-select"
                            />
                            {validator.message('transmission', vehicleFormAttributes.transmission, 'required')}
                        </div>
                        <div>
                            <label
                                htmlFor="tow-bar"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Tow Bar
                            </label>
                            <Input
                                className="big-checkbox"
                                type="checkbox"
                                id="tow-bar"
                                name="towBar"
                                checked={vehicleFormAttributes.towBar === 1}
                                onChange={handleCheckboxChange}
                            />
                            {/* No validation for towBar */}
                        </div>
                        <div>
                            <label
                                htmlFor="wheel-nuts"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Wheel Nuts
                            </label>
                            <Input
                                className="big-checkbox"
                                type="checkbox"
                                id="wheel-nuts"
                                name="lockingWheelNut"
                                checked={vehicleFormAttributes.lockingWheelNut === 1}
                                onChange={handleCheckboxChange}
                            />
                            {/* No validation for lockingWheelNut */}
                        </div>
                        <div>
                            <label
                                htmlFor="status"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Status
                            </label>
                            <Input
                                type="text"
                                id="status"
                                name="status"
                                value={vehicleFormAttributes.status}
                                onChange={handleChange}
                                placeholder="Enter vehicle Status"
                                className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring focus:ring-yellow-500"
                            />
                            {/* No validation for status */}
                        </div>
                    </div>
                )}

                {/* Submit Button on Final Step */}
                {currentStep === 3 && (
                    <div className="mt-4">
                        <Button
                            type="submit"
                            className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
                        >
                            Submit
                        </Button>
                    </div>
                )}
            </form>
        </div>
    );
};
