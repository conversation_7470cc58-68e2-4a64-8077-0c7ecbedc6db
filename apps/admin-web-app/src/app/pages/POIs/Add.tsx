'use client';

import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Select from 'react-select';
import SimpleReactValidator from 'simple-react-validator';
import { toast } from 'sonner';

import { useEnvironment } from '@aa/ui/hooks/use-environment';
import { SelectInterface } from '../../../model';
import { <PERSON><PERSON>, Card } from '../../components/ui-component/ui';
import { useCategoryService, useSupplierService, usePoiService } from '../../../service';

const POIsAdd: React.FC = () => {
    const navigate = useNavigate();
    const environment = useEnvironment();
    const CategoryService = useCategoryService();
    const SupplierService = useSupplierService();
    const PoiService = usePoiService();

    const [categories, setCategories] = useState<SelectInterface[]>([]);
    const [supplierNetworkOptions, setSupplierNetworkOptions] = useState<SelectInterface[]>([]);

    const [formAttributes, setFormAttributes] = useState<{
        category: SelectInterface | undefined;
        supNetwork: SelectInterface | undefined;
    }>({
        category: undefined,
        supNetwork: undefined
    });
    const [validator] = useState(new SimpleReactValidator());

    const handleCategoryChange = (selectedOption: SelectInterface) => {
        setFormAttributes((prev) => ({
            ...prev,
            category: selectedOption
        }));
    };

    const handleSupplierNetworkChange = (selectedOption: SelectInterface) => {
        setFormAttributes((prev) => ({
            ...prev,
            supNetwork: selectedOption
        }));
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        validator.showMessages();
        if (validator.allValid() && formAttributes.category && formAttributes.supNetwork) {
            PoiService.insertBulkPois(parseInt(formAttributes.category.value), formAttributes.supNetwork.value)
                .then((response) => {
                    toast.success(`Bulk POI insert done successfully! Total ${response.insertedCount} POIs inserted.`);
                    setTimeout(() => {
                        navigate(`${environment.basePath}/pois/view-modify`, { replace: true });
                    }, 1000);
                })
                .catch(() => {
                    toast.error(`Bulk POI insert failed!`);
                });
        } else {
            validator.showMessages();
            setFormAttributes({ ...formAttributes });
        }
    };

    useEffect(() => {
        CategoryService.fetchAllSelectedCategories()
            .then((resp) => {
                const allCategories = resp.map((cat) => ({ label: cat.name, value: `${cat.categoryId}` }));
                setCategories(allCategories);
            })
            .catch((error) => console.error(error));

        SupplierService.fetchSupplierNetworks()
            .then((resp) => {
                const allSupplierNetworks = resp.map((supplierNetwork) => ({
                    label: supplierNetwork.supNetworkName,
                    value: supplierNetwork.supNetworkCode
                }));
                setSupplierNetworkOptions(allSupplierNetworks);
            })
            .catch(console.error);
    }, []);

    return (
        <div className="p-6 space-y-6">
            <Card className="bg-white shadow-md rounded-lg p-6">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold mb-4">Add/Modify POI (Bulk)</h2>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="space-y-4">
                        <div>
                            <label
                                htmlFor="category-id"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Category
                            </label>
                            <Select
                                isMulti={false}
                                name="category"
                                options={categories}
                                value={formAttributes.category}
                                onChange={handleCategoryChange}
                                placeholder="Select category"
                                className="react-select-container"
                                classNamePrefix="react-select"
                            />
                            {validator.message('category', formAttributes.category, 'required')}
                        </div>

                        <div>
                            <label
                                htmlFor="supplier-network-id"
                                className="block text-sm font-medium text-gray-700"
                            >
                                Supplier Network
                            </label>
                            <Select
                                isMulti={false}
                                name="supNetwork"
                                options={supplierNetworkOptions}
                                value={formAttributes.supNetwork}
                                onChange={handleSupplierNetworkChange}
                                placeholder="Select supplier network"
                                className="react-select-container"
                                classNamePrefix="react-select"
                            />
                            {validator.message('supNetwork', formAttributes.supNetwork, 'required')}
                        </div>

                        <div>
                            <Button
                                type="submit"
                                className="bg-yellow-500 text-white hover:bg-yellow-600 w-full"
                            >
                                Submit
                            </Button>
                        </div>
                    </div>
                </form>
            </Card>
        </div>
    );
};

export default POIsAdd;
