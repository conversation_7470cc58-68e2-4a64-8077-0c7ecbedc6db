'use client';

import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTable, useSortBy, useGlobalFilter, useFilters, usePagination } from 'react-table';
import { ChevronDown, ChevronUp, MapPinIcon } from 'lucide-react';
import Select from 'react-select';
import { toast } from 'sonner';

import { Spinner } from '@aa/ui/core/spinner';
import { Button, Input, YesNoDialog } from '../../components/ui-component/ui';
import { usePoiService, useCategoryService } from '../../../service';
import { Poi, PoiListView, PoiUpdateResponseInterface, SelectInterface } from '../../../model';

// Columns for POIs table
const hiddenColumns = ['latitude', 'longitude'];
const createColumns = (handlePoiUpdate: (record: PoiListView) => void) => [
    {
        Header: 'Name',
        accessor: 'name'
    },
    {
        Header: 'Address',
        accessor: 'address'
    },
    {
        Header: 'Contact',
        accessor: 'contact'
    },
    {
        Header: 'Latitude/Longitude',
        accessor: 'latLong',
        Cell: ({ row }) => (
            <div className="flex items-end">
                <div>
                    <div className="whitespace-nowrap">lat: {row.original.latitude}</div>
                    <div className="whitespace-nowrap">long: {row.original.longitude}</div>
                </div>
                <div>
                    <a
                        href={'https://maps.google.com/?q=' + row.original.latitude + ',' + row.original.longitude}
                        target="_blank"
                        rel="noreferrer"
                        title="Go to Map"
                    >
                        <MapPinIcon />
                    </a>
                </div>
            </div>
        ),
        disableSortBy: true
    },
    {
        Header: 'Active',
        className: 'text-center',
        Cell: ({ row }) => (row.original.isActive ? 'Yes' : 'No')
    },
    {
        Header: 'Actions',
        Cell: ({ row }) => {
            const [showUpdateDialog, setShowUpdateDialog] = useState(false);
            return (
                <>
                    <Button
                        variant="outline"
                        size="sm"
                        className="bg-blue-600 text-white"
                        onClick={() => setShowUpdateDialog(true)}
                    >
                        Update
                    </Button>
                    {showUpdateDialog && (
                        <YesNoDialog
                            open={showUpdateDialog}
                            title="Update single POI"
                            prompt={`Do you want to update the POI \`${row.original.name}\`?`}
                            yesClicked={() => {
                                handlePoiUpdate(row.original);
                                setShowUpdateDialog(false);
                            }}
                            noClicked={() => {
                                // Does nothing
                                setShowUpdateDialog(false);
                            }}
                        />
                    )}
                </>
            );
        }
    },
    ...hiddenColumns.map((columnName) => ({
        Header: columnName,
        accessor: columnName
    }))
];

const PoisView = () => {
    const [categories, setCategories] = useState<SelectInterface[]>([]);
    const [selectedCategory, setSelectedCategory] = useState<SelectInterface | undefined>(undefined);
    const [searchValue, setSearchValue] = useState<string>('');

    const [pois, setPois] = useState<PoiListView[]>([]); // pois
    const [loading, setLoading] = useState<boolean>(true);
    const CategoryService = useCategoryService();
    const PoiService = usePoiService();

    const fetchPoisDataForCategory = useCallback(async (categoryId: string) => {
        setLoading(true);
        PoiService.fetchPoisByCatgeory(categoryId)
            .then((poisResponse) => {
                const poisListView: PoiListView[] = [];
                poisResponse.map((poi) => poisListView.push(mapPoiToPoiListView(poi)));

                setPois(poisListView);
            })
            .catch((error) => console.error(error))
            .finally(() => setLoading(false));
    }, []);

    // Columns Memoization to prevent re-renders
    const columns = useMemo(() => {
        const handlePoiUpdate = (poiRecord: PoiListView) => {
            PoiService.updateSinglePoi(poiRecord.categoryId, poiRecord.id)
                .then((poiUpdateResponse: PoiUpdateResponseInterface) => {
                    if (poiUpdateResponse.mongoResult.modifiedCount !== 1) {
                        toast.error(`POI update failed for ${poiRecord.name}!`);
                        return;
                    }

                    toast.success(`POI update done successfully for ${poiRecord.name}!`);
                    fetchPoisDataForCategory(poiRecord.categoryId.toString());
                    setSearchValue('');
                })
                .catch(() => {
                    toast.error(`POI update failed!`);
                });
        };

        return createColumns(handlePoiUpdate);
    }, [fetchPoisDataForCategory]);

    // Table Instance for POIs
    const tableInstance = useTable(
        {
            columns,
            data: pois,
            initialState: { pageIndex: 0, pageSize: 10, hiddenColumns }
        },
        useFilters,
        useGlobalFilter,
        useSortBy,
        usePagination
    );

    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        //rows,
        prepareRow,
        setGlobalFilter,
        page,
        canPreviousPage,
        canNextPage,
        pageOptions,
        pageCount,
        gotoPage,
        nextPage,
        previousPage,
        setPageSize,
        state: { pageIndex, pageSize }
    } = tableInstance;

    const handleCategoryChange = (selectedOption: SelectInterface) => {
        if (selectedOption) {
            setSearchValue('');
            setGlobalFilter('');
            setSelectedCategory(selectedOption);
            fetchPoisDataForCategory(selectedOption.value);
        }
    };

    const mapPoiToPoiListView = (poi: Poi): PoiListView => {
        let contact = '';
        if (poi.properties.telephone && poi.properties.telephone.trim().length > 2) {
            contact = 'Tel: ' + poi.properties.telephone.trim() + ';';
        }
        if (poi.properties.extNumber && poi.properties.extNumber.trim().length > 2) {
            contact += 'Ext: ' + poi.properties.extNumber;
        }
        return {
            id: poi.properties.id,
            categoryId: poi.properties.categoryId,
            name: poi.properties.name,
            address: `${poi.properties.street}, ${poi.properties.area}, ${poi.properties.postcode}`,
            telephone: poi.properties.telephone,
            extNumber: poi.properties.extNumber,
            contact: contact,
            latitude: poi.geometry.coordinates[1].toString(),
            longitude: poi.geometry.coordinates[0].toString(),
            isActive: poi.properties.isActive
        };
    };

    useEffect(() => {
        CategoryService.fetchAllSelectedCategories()
            .then((resp) => {
                const allCategories = resp.map((cat) => ({ label: cat.name, value: cat.categoryId }));
                setCategories(allCategories);
            })
            .catch((error) => console.error(error));
    }, []);

    return (
        <div className="space-y-8 p-4">
            <div>
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold">Points of Interest (POIs)</h2>
                    <Select
                        isMulti={false}
                        name="filterByCategory"
                        options={categories}
                        value={selectedCategory}
                        onChange={handleCategoryChange}
                        placeholder="Select category"
                        className="react-select-container"
                        classNamePrefix="react-select"
                        styles={{
                            control: (base) => ({
                                ...base,
                                minWidth: 350
                            })
                        }}
                    />
                    <Input
                        placeholder="Search..."
                        onChange={(e) => {
                            setSearchValue(e.target.value);
                            setGlobalFilter(e.target.value);
                        }}
                        className="max-w-xs"
                        disabled={selectedCategory === undefined}
                        value={searchValue}
                    />
                </div>

                <div className="overflow-x-auto bg-white shadow-lg rounded-lg">
                    {selectedCategory === undefined ? (
                        <div className="text-center">
                            <br />
                            <br />
                            <br />
                            Please choose catgory to view POIs...
                            <br />
                            <br />
                            <br />
                            <br />
                        </div>
                    ) : loading ? (
                        <div className="text-center">
                            <br />
                            <br />
                            Loading POIs for {selectedCategory.label}..
                            <br />
                            <br />
                            Please wait...{' '}
                            <Spinner
                                className="mr-3"
                                style={{ display: 'inline' }}
                            />
                            <br />
                            <br />
                            <br />
                        </div>
                    ) : (
                        <>
                            <table
                                {...getTableProps()}
                                className="min-w-full table-auto"
                            >
                                <thead>
                                    {headerGroups.map((headerGroup) => (
                                        <tr
                                            {...headerGroup.getHeaderGroupProps()}
                                            className="border-b"
                                        >
                                            {headerGroup.headers.map((column) => (
                                                <th
                                                    {...column.getHeaderProps(column.getSortByToggleProps())}
                                                    className="py-3 px-4 text-left text-sm font-semibold cursor-pointer text-muted"
                                                >
                                                    {column.render('Header')}
                                                    <span className="ml-1">
                                                        {column.isSorted ? column.isSortedDesc ? <ChevronDown className="inline w-4 h-4" /> : <ChevronUp className="inline w-4 h-4" /> : null}
                                                    </span>
                                                </th>
                                            ))}
                                        </tr>
                                    ))}
                                </thead>
                                <tbody {...getTableBodyProps()}>
                                    {page.map((row) => {
                                        prepareRow(row);
                                        return (
                                            <tr
                                                {...row.getRowProps()}
                                                className="border-b hover:bg-muted/50"
                                            >
                                                {row.cells.map((cell) => (
                                                    <td
                                                        {...cell.getCellProps()}
                                                        className="py-2 px-4 text-sm text-muted"
                                                    >
                                                        {cell.render('Cell')}
                                                    </td>
                                                ))}
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                            {/* Pagination controls */}
                            <div className="flex items-center justify-between p-4">
                                <div>
                                    <Button
                                        onClick={() => gotoPage(0)}
                                        disabled={!canPreviousPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600"
                                    >
                                        {'<<'}
                                    </Button>
                                    <Button
                                        onClick={() => previousPage()}
                                        disabled={!canPreviousPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'<'}
                                    </Button>
                                    <Button
                                        onClick={() => nextPage()}
                                        disabled={!canNextPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'>'}
                                    </Button>
                                    <Button
                                        onClick={() => gotoPage(pageCount - 1)}
                                        disabled={!canNextPage}
                                        className="bg-yellow-500 text-white hover:bg-yellow-600 ml-1"
                                    >
                                        {'>>'}
                                    </Button>
                                </div>
                                <div>
                                    <span>
                                        Page&nbsp;
                                        <strong>
                                            {pageIndex + 1} of {pageOptions.length}&nbsp;
                                        </strong>
                                    </span>
                                    <span>
                                        | Go to page:&nbsp;
                                        <input
                                            type="number"
                                            value={pageIndex + 1}
                                            onChange={(e) => {
                                                const page = e.target.value ? Number(e.target.value) - 1 : 0;
                                                gotoPage(page);
                                            }}
                                            style={{ width: '50px' }}
                                            className="border border-input px-1 py-1 text-center"
                                        />
                                    </span>
                                </div>
                                <div>
                                    Rows per page:&nbsp;
                                    <select
                                        value={pageSize}
                                        onChange={(e) => {
                                            setPageSize(Number(e.target.value));
                                        }}
                                        className="border border-input px-3 py-1"
                                    >
                                        {[10, 20, 30, 40, 50].map((pageSize) => (
                                            <option
                                                key={pageSize}
                                                value={pageSize}
                                            >
                                                Show {pageSize}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default PoisView;
