import { Action, Currency, WithDataId } from '@aa/data-models/common';

export const getInitials = (name: string | number | undefined): string => {
    if (typeof name === 'number') {
        return name.toString();
    }
    if (!name) {
        return '';
    }
    const nameParts = name.split(' ');
    const initials = nameParts.map((part) => part.charAt(0)).join('');
    return initials.toUpperCase();
};

export const releaseAction = (action: WithDataId<Action>) => {
    // const noteClient = new NoteClient({
    //     httpClient: new HttpClient({
    //         authClient: this.authClient,
    //     }),
    //     connector: this.connector,
    // });
    // const actionClient = new ActionClient({
    //     httpClient: new HttpClient(),
    //     connector: this.connector,
    // });
    // }
};
/**
 * Capitalizes the first letter of a given string
 * @param {string | undefined} str
 * @return {string}
 */
export const capitalizeFirstLetter = (str: string | undefined): string => {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
};

export const formatCurrency = (amount: number | string, currency: string): string => {
    if (currency === Currency.USD) {
        return `${amount}$`;
    } else if (currency === Currency.EURO) {
        return `${amount}€`;
    } else if (currency === Currency.GBP) {
        return `£${amount}`;
    } else {
        return `£${amount}`;
    }
};
