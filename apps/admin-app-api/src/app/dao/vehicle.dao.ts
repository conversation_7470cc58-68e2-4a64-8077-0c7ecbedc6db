import { DataStoreProviderType, MongodbDataProvider, OracleDataProvider } from '@aa/data-store';
import singleton from '../services/single.service';
import { Endpoints } from '../constants/endpoints';
import * as constants from '../enums/constants';
import { dbAdmin, vehicleSnapshot } from '../constants/mongo';
import { RestApiService } from '../services/rest-api.service';
import { Filter, ObjectId } from 'mongodb';
import { VehicleData, VehicleMongoInsertInterface } from '@aa/admin-helpers';
import { ExperianDetailResponse } from '../models';
import { makeWiseDefaultPrefixes, vehicleDefaultValues } from '../constants/vehicle';

const aah2Proxy = process.env.loadbalancer === 'localhost' ? 'http://localhost:8080' : 'https://' + process.env.loadbalancer;

class VehicleDao {
    unknownModelsData: { [brand: string]: { modelId: number; variantId: number } } = {};

    constructor() {}

    async readVehicleConstants() {
        await Promise.all(
            vehicleDefaultValues.DEFAULT_MAKES.map(async (make) => {
                this.unknownModelsData[make] = { modelId: 0, variantId: 0 };
                const tempModelId = await this.dbModelId(vehicleDefaultValues.DEFAULT_VEHICLE_TYPE, make, vehicleDefaultValues.UNKNOWN_CAR_MODEL);
                if (tempModelId && tempModelId > 0) {
                    this.unknownModelsData[make].modelId = tempModelId;
                    const tempVariantId = await this.dbVariantId(tempModelId, makeWiseDefaultPrefixes[make]);
                    if (tempVariantId && tempVariantId > 0) {
                        this.unknownModelsData[make].variantId = tempVariantId;
                    }
                }
            })
        );
    }

    oracleProvider(): OracleDataProvider {
        return singleton.dataStore.getProvider(DataStoreProviderType.ORACLE);
    }

    mongoProvider(): MongodbDataProvider {
        return singleton.dataStore.getProvider(DataStoreProviderType.MONGODB);
    }

    async dbTypeId(typeName: string): Promise<number> {
        const sql = `SELECT VEHICLE_TYPE_ID FROM VEHICLE_TYPE WHERE VEHICLE_TYPE_NAME = :typeName`;
        const result = await this.oracleProvider().execute<{ VEHICLE_TYPE_ID: number }>(sql, {
            typeName: typeName.toUpperCase()
        });
        return result[0].VEHICLE_TYPE_ID;
    }

    async dbMakeId(makeName: string): Promise<number> {
        const sql = `SELECT VEHICLE_MAKE_ID FROM VEHICLE_MAKE WHERE VEHICLE_MAKE_NAME = :makeName`;
        const result = await this.oracleProvider().execute<{ VEHICLE_MAKE_ID: number }>(sql, {
            makeName: makeName.toUpperCase()
        });
        return result[0].VEHICLE_MAKE_ID;
    }

    async dbModelId(typeName: string, makeName: string, modelName: string): Promise<number | null> {
        try {
            const typeId: number = await this.dbTypeId(typeName);
            const makeId: number = await this.dbMakeId(makeName);

            const sql = `SELECT VEHICLE_MODEL_ID FROM VEHICLE_MODEL WHERE VEHICLE_TYPE_ID = :typeId AND VEHICLE_MAKE_ID = :makeId AND VEHICLE_MODEL_NAME = :modelName`;
            const result = await this.oracleProvider().execute<{ VEHICLE_MODEL_ID: number }>(sql, {
                typeId,
                makeId,
                modelName: modelName.toUpperCase()
            });
            return result[0].VEHICLE_MODEL_ID;
        } catch (err) {
            return null;
        }
    }

    async dbVariantId(modelId: number, typePrefix: string): Promise<number | null> {
        try {
            const sql = `SELECT VARIANT_ID FROM VEHICLE_MODEL_VAR WHERE VEHICLE_TYPE_PREFIX = '${typePrefix}' AND VEHICLE_MODEL_ID = ${modelId}`;
            const result = await this.oracleProvider().execute<{ VARIANT_ID: number }>(sql);
            return result[0].VARIANT_ID;
        } catch (err) {
            return null;
        }
    }

    async dbParId(CICode: string): Promise<number> {
        const sql = `SELECT REGION_PAR_ID FROM MB_RETAILER R
                    INNER JOIN SUPPLIER S ON S.SUP_RESOURCE_ID = R.SUP_RESOURCE_ID
                    WHERE S.SUPPLIER_ACCOUNT_NO = '${CICode}'`;
        const result = await this.oracleProvider().execute<{ REGION_PAR_ID: number }>(sql);
        if (result.length) {
            return result[0].REGION_PAR_ID;
        } else {
            return 0;
        }
    }

    async getVehicleParId(custGroup: string): Promise<number> {
        const sql = `SELECT PAR_ID FROM MB_PARAM WHERE PARAM_GROUP =  'VEH' and CG_ARRAY like '%${custGroup}%'`;
        const result = await this.oracleProvider().execute<{ PAR_ID: number }>(sql);
        if (result.length) {
            return result[0].PAR_ID;
        } else {
            return 0;
        }
    }

    async dbOwningMgtUnitId(CICode: string): Promise<number> {
        const sql = `SELECT S.SUP_RESOURCE_ID FROM SUPPLIER S INNER JOIN RESOURCE_TBL R ON S.SUP_RESOURCE_ID = R.RESOURCE_ID WHERE S.SUPPLIER_ACCOUNT_NO = '${CICode}' AND R.RESOURCE_INEFF_DATE IS NULL`;
        const result = await this.oracleProvider().execute<{ SUP_RESOURCE_ID: number }>(sql);
        if (result.length) {
            return result[0].SUP_RESOURCE_ID;
        } else {
            return 0;
        }
    }

    async mongoInsertSnapshot(mongoSnapshot: any): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, vehicleSnapshot);
        await collection.insertOne({
            userId: '12345',
            timestamp: new Date(),
            status: 'pending',
            data: mongoSnapshot
        });
    }

    async mongoUpdateSnapshot(snapshotId: string, mongoSnapshot: any): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, vehicleSnapshot);
        await collection.updateOne(
            {
                _id: new ObjectId(snapshotId),
                'data.vehicleData.vehicleRegNo': mongoSnapshot[0].vehicleData.vehicleRegNo
            },
            {
                $set: {
                    'data.$': mongoSnapshot[0]
                }
            }
        );
    }

    async mongoDisableSnapshot(mongoSnapshotId: ObjectId): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, vehicleSnapshot);
        await collection.findOneAndUpdate(
            { _id: mongoSnapshotId },
            {
                $set: {
                    status: 'complete'
                }
            }
        );
    }

    async getSnapshotMultiple(mongoSnapshotId: any): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, vehicleSnapshot);
        const mongoSnapshotIds = mongoSnapshotId.split(',');
        const snapshotIdList: ObjectId[] = [new ObjectId(mongoSnapshotIds[0])];
        if (mongoSnapshotIds.length) {
            mongoSnapshotIds.map((snapshotId: string) => {
                snapshotIdList.push(new ObjectId(snapshotId));
            });
        } else {
            snapshotIdList.push(new ObjectId(mongoSnapshotId));
        }
        return await collection.find({
            _id: { $in: [snapshotIdList] }
        });
    }

    async getSnapshot(mongoSnapshotId: any): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, vehicleSnapshot);
        return await collection.findOne({
            _id: new ObjectId(mongoSnapshotId)
        });
    }

    async getExperianDetails(vehicleReg: string): Promise<ExperianDetailResponse> {
        const uri = aah2Proxy + Endpoints.VEHICLE_SERVICE_ENDPOINT + vehicleReg;
        return await RestApiService.execute(uri, constants.RequestType.GET);
    }

    async getVehicleProperties(params: {
        regNoInput: string;
        makeInput: string;
        modelInput: string;
        colourInput: string;
        seatsInput: number | null;
    }): Promise<{ make: string; model: string; colour: string; seats: number | null; modelId: number; variantId: number }> {
        // 1. get inputs - excel file or individual form on Admin App
        // 2. If above data missing, replace from Experian
        // 3. Try to get modelId and variantId from DB
        // 4. Consolodate and return

        let makeFromExperian = '',
            modelFromExperian = '',
            colourFromExperian = '',
            seatsFromExperian: number | null = null;
        try {
            const data = await this.getExperianDetails(params.regNoInput);
            if (data && data.experianDetails) {
                makeFromExperian = data.experianDetails.make;
                modelFromExperian = data.experianDetails.model;
                colourFromExperian = data.experianDetails.colour;
                seatsFromExperian = data.experianDetails.seatNumber;
            }
        } catch (error) {
            // Experian details not found
        }

        const finalMake = params.makeInput ? params.makeInput : makeFromExperian;
        const finalModel = params.modelInput ? params.modelInput : modelFromExperian;
        const finalColour = params.colourInput ? params.colourInput : colourFromExperian;
        const finalSeats = params.seatsInput ? params.seatsInput : seatsFromExperian;

        let finalModelId = await this.dbModelId(vehicleDefaultValues.DEFAULT_VEHICLE_TYPE, finalMake, finalModel);
        if (finalModelId == null) {
            finalModelId = this.unknownModelsData[finalMake].modelId;
        }

        let finalVariantId = await this.dbVariantId(finalModelId, makeWiseDefaultPrefixes[finalMake]);
        if (finalVariantId == null) {
            finalVariantId = this.unknownModelsData[finalMake].variantId;
        }

        return { make: finalMake, model: finalModel, colour: finalColour, seats: finalSeats, modelId: finalModelId, variantId: finalVariantId };
    }

    async getVehiclesCSH(supNetworkId: number, pageNumber: number, pageSize: number): Promise<VehicleData[]> {
        const sql = `SELECT R.*,
                        V.VEHICLE_REG_NO, V.VOR_FAULT, V.VOR_AUTHORITY, V.VARIANT_ID,
                        MBV.VEH_ID, MBV.REGION_PAR_ID, MBV.VIN, MBV.TRAKM8, MBV.VEH_MODEL_ID,
                        MBV.INFLEET_DATE, MBV.DEFLEET_DATE, MBV.VEH_VALUE, MBV.VEH_PAR_ID,
                        dbms_lob.substr( MBV.DETAILS, dbms_lob.getlength(MBV.DETAILS), 1) MBV_DETAILS,
                        S.SUP_NETWORK_ID
                    FROM VEHICLE V
                    INNER JOIN RESOURCE_TBL R ON V.VEHICLE_RESOURCE_ID = R.RESOURCE_ID
                    INNER JOIN MB_VEHICLE MBV ON V.VEHICLE_RESOURCE_ID = MBV.RES_ID
                    INNER JOIN SUPPLIER S ON S.SUP_RESOURCE_ID = R.OWNING_MGT_UNIT_ID
                    WHERE S.SUP_NETWORK_ID = ${supNetworkId}`;

        const result = await this.oracleProvider().execute<any>(this.pageSQL(sql, pageNumber, pageSize));
        return result.map((i) => {
            return {
                resource_id: i.RESOURCE_ID,
                resourceData: {
                    resourceId: i.RESOURCE_ID,
                    resourceEffDate: new Date(i.RESOURCE_EFF_DATE),
                    resourceIneffDate: new Date(i.RESOURCE_INEFF_DATE),
                    owningMgtUnitId: i.OWNING_MGT_UNIT_ID,
                    supNetworkId: i.SUP_NETWORK_ID,
                    resourceTypeId: i.RESOURCE_TYPE_ID,
                    resourceStatusId: i.RESOURCE_STATUS_ID,
                    resourceStatusTime: new Date(i.RESOURCE_STATUS_TIME),
                    aahSiteId: i.AAH_SITE_ID,
                    ccSiteId: i.CC_SITE_ID,
                    resourceRemarkId: i.RESOURCE_REMARK_ID,
                    curTxnId: i.CUR_TXN_ID,
                    updateSequence: i.UPDATE_SEQUENCE,
                    replTime: new Date()
                },
                vehicleData: {
                    vehicleRegNo: i.VEHICLE_REG_NO,
                    vorFault: i.VOR_FAULT,
                    vorAuthority: i.VOR_AUTHORITY,
                    variantId: i.VARIANT_ID,
                    replTime: new Date()
                },
                mbVehicleData: {
                    regionParId: i.REGION_PAR_ID,
                    vin: i.VIN,
                    trakm8: i.TRAKM8,
                    details: i.MBV_DETAILS ? JSON.parse(i.MBV_DETAILS) : null,
                    vehModelId: i.VEH_MODEL_ID,
                    infleetDate: new Date(i.INFLEET_DATE),
                    defleetDate: new Date(i.DEFLEET_DATE),
                    vehValue: i.VEH_VALUE,
                    vehParId: i.VEH_PAR_ID,
                    replTime: new Date()
                }
            };
        });
    }

    async listSnapshots(): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, vehicleSnapshot);
        return await collection
            .aggregate([
                {
                    $match: { status: 'pending' }
                },
                {
                    $project: {
                        _id: 1,
                        timestamp: 1,
                        custGroup: { $arrayElemAt: ['$data.custGroup', 0] }
                    }
                }
            ])
            .toArray();
    }

    async setVehicleDetailsFromFileContract(csvData: any, finalValues: any) {
        const details = {
            'Home-Depot-id': finalValues.owningMgtUnitId,
            Comments: '',
            Colour: finalValues.colour,
            BasicColour: csvData.BASIC_COLOUR,
            RegDate: this.formatDate(this.convertStringToDate(csvData.REG_DATE)),
            EuroPak: !!csvData.EUROPAK,
            LockingWheelNut: !!csvData.LOCKING_WHEEL_NUT,
            Towbar: !!csvData.TOWBAR,
            AWD: !!csvData.AWD,
            Seats: finalValues.seats,
            Derivative: csvData.DERIVATIVE,
            Transmission: this.getTransmission(csvData.TRANSMISSION_DESCRIPTION),
            Fuel: this.getFuel(csvData.FUEL),
            BrandName: finalValues.make,
            ModelName: finalValues.model,
            Retailer: csvData.SITE
        };
        return details;
    }

    async setVehicleDetailsFromUiContract(formData: VehicleMongoInsertInterface, finalValues: any) {
        const details = {
            'Home-Depot-id': finalValues.owningMgtUnitId,
            Comments: '',
            Colour: finalValues.colour,
            BasicColour: formData.basicColour,
            RegDate: formData.registrationDate,
            EuroPak: !!formData.europak,
            LockingWheelNut: !!formData.lockingWheelNut,
            Towbar: !!formData.towBar,
            AWD: !!formData.awd,
            Seats: finalValues.seats,
            Derivative: formData.derivative,
            Transmission: formData.transmission,
            Fuel: vehicleDao.getFuel(formData.fuel),
            EstimatedRRP: formData.estimatedRRP,
            Retailer: formData.retailer,
            Status: formData.status,
            BrandName: finalValues.make,
            ModelName: finalValues.model
        };
        return details;
    }

    getTransmission(transText: string) {
        if (transText == 'Auto') {
            return 'Automatic';
        }
        return transText;
    }

    formatDate(date: Date | null): string | null {
        if (date == null) {
            return null;
        }

        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    convertStringToDate(dateString: string): Date | null {
        const [day, month, year] = dateString.split('/').map(Number);

        if (!day || !month || !year) {
            return null;
        }

        return new Date(year, month - 1, day);
    }

    getFuel(fule: string) {
        if (fule?.includes('Petrol')) return 'Petrol';
        if (fule?.includes('Diesel')) return 'Diesel';
        if (fule?.includes('Electric')) return 'Electric';
        if (fule?.includes('Hybrid')) return 'Hybrid';
        if (fule?.includes('PHEV')) return 'PHEV';
        if (fule?.includes('BEV')) return 'BEV';
        if (fule?.includes('Hybrid')) return 'Hybrid';
        return 'NA';
    }

    pageSQL(sql: string, pageNumber: number, pageSize: number) {
        return `SELECT * FROM(
            SELECT a.*, rownum r__ FROM (
                ${sql}
            ) a
            WHERE rownum < ${pageNumber * pageSize + 1}
        )
        WHERE r__ >= ${(pageNumber - 1) * pageSize + 1}`;
    }
}

const vehicleDao = new VehicleDao();
export default vehicleDao;
