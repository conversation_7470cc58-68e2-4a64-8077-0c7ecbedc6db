import { DataStoreProviderType, MongodbDataProvider, OracleDataProvider } from '@aa/data-store';
import singleton from '../services/single.service';
import { dbAdmin, retailerSnapshot } from '../constants/mongo';
import { Filter, ObjectId } from 'mongodb';

class RetailerDao {
    constants: any = {};

    constructor() {}

    oracleProvider(): OracleDataProvider {
        return singleton.dataStore.getProvider(DataStoreProviderType.ORACLE);
    }

    mongoProvider(): MongodbDataProvider {
        return singleton.dataStore.getProvider(DataStoreProviderType.MONGODB);
    }

    async dbRegionParId(AnyRegionId: string, CustomerGroup: string): Promise<number> {
        const sql = `SELECT PAR_ID FROM MB_PARAM P
                    WHERE param_group = 'RGN' and param_code='${AnyRegionId}' or cg_array like '%${CustomerGroup}%'`;
        const result = await this.oracleProvider().execute<{ PAR_ID: number }>(sql);
        return result.length ? result[0].PAR_ID : 0;
    }

    async dbOwningMgtUnitId(CICode: string): Promise<number> {
        //find the supplier_network_id from the supplier text in the excel and put in the following condition
        const sql = `SELECT S.SUP_RESOURCE_ID FROM SUPPLIER S INNER JOIN RESOURCE_TBL R ON S.SUP_RESOURCE_ID = R.RESOURCE_ID WHERE S.SUPPLIER_ACCOUNT_NO = '${CICode}' AND R.RESOURCE_INEFF_DATE IS NULL`;
        const result = await this.oracleProvider().execute<{ SUP_RESOURCE_ID: number }>(sql);
        return result.length ? result[0].SUP_RESOURCE_ID : 0;
    }

    async dbGetSupplierNetworkId(SupNetCode: string): Promise<number> {
        const sql = `SELECT S.SUP_NETWORK_ID FROM SUPPLIER_NETWORK S WHERE S.SUP_NETWORK_CODE = '${SupNetCode}' AND S.INEFFECTIVE_DATE IS NULL`;
        const result = await this.oracleProvider().execute<{ SUP_NETWORK_ID: number }>(sql);
        if (result.length) {
            return result[0]?.SUP_NETWORK_ID;
        } else {
            return 0;
        }
    }

    async mongoInsertSnapshot(mongoSnapshot: any): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, retailerSnapshot);
        await collection.insertOne({
            userId: '12345',
            timestamp: new Date(),
            status: 'pending',
            data: mongoSnapshot
        });
    }

    async mongoUpdateSnapshot(snapshotId: string, mongoSnapshot: any): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, retailerSnapshot);
        await collection.updateOne(
            {
                _id: new ObjectId(snapshotId),
                'data.CiCode': mongoSnapshot[0].CiCode
            },
            {
                $set: {
                    'data.$': mongoSnapshot[0]
                }
            }
        );
    }

    async getSnapshotMultiple(mongoSnapshotId: any): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, retailerSnapshot);
        const mongoSnapshotIds = mongoSnapshotId.split(',');
        const snapshotIdList: ObjectId[] = [new ObjectId(mongoSnapshotIds[0])];
        if (mongoSnapshotIds.length) {
            mongoSnapshotIds.map((snapshotId: string) => {
                snapshotIdList.push(new ObjectId(snapshotId));
            });
        } else {
            snapshotIdList.push(new ObjectId(mongoSnapshotId));
        }
        return await collection.find({
            _id: { $in: [snapshotIdList] }
        });
    }

    async getSnapshot(mongoSnapshotId: any): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, retailerSnapshot);
        return await collection.findOne({
            _id: new ObjectId(mongoSnapshotId)
        });
    }

    async listSnapshots(): Promise<any> {
        const collection = await this.mongoProvider().collection<any>(dbAdmin, retailerSnapshot);
        return await collection
            .find(
                { status: 'pending' },
                {
                    projection: {
                        _id: 1.0,
                        timestamp: 1.0
                    }
                }
            )
            .toArray();
    }

    async convertToOpenAndCloseDate(openCloseTimeString: string): Promise<{ openTime: Date; closeTime: Date }> {
        try {
            const openHH: number | string = openCloseTimeString.split('-')[0].split(':')[0];
            const openMM: number | string = openCloseTimeString.split('-')[0].split(':')[1];
            const closeHH: number | string = openCloseTimeString.split('-')[1].split(':')[0];
            const closeMM: number | string = openCloseTimeString.split('-')[1].split(':')[1];

            return {
                openTime: new Date(new Date(new Date().setHours(parseInt(openHH))).setMinutes(parseInt(openMM))),
                closeTime: new Date(new Date(new Date().setHours(parseInt(closeHH))).setMinutes(parseInt(closeMM)))
            };
        } catch (error) {
            return {
                openTime: new Date(new Date().setHours(9, 0)),
                closeTime: new Date(new Date().setHours(17, 0))
            };
        }
    }

    setRetailerDetails(csvData: any, optional: any) {
        const details = {
            'Home-Depot-id': optional.owningMgtUnitId,
            Comments: '',
            Colour: csvData.COLOUR,
            BasicColour: csvData.BASIC_COLOUR,
            RegDate: csvData.REG_DATE,
            EuroPak: !!csvData.EUROPAK,
            LockingWheelNut: !!csvData.LOCKING_WHEEL_NUT,
            Towbar: !!csvData.TOWBAR,
            AWD: !!csvData.AWD,
            Seats: !!csvData.SEATS,
            Derivative: !!csvData.DERIVATIVE,
            Transmission: !!csvData.TRANSMISSION,
            Fuel: this.getFuel(csvData.FUEL)
        };
        return details;
    }

    getFuel(fule: string) {
        if (fule.includes('Petrol')) return 'Petrol';
        if (fule.includes('Diesel')) return 'Diesel';
        if (fule.includes('Electric')) return 'Electric';
        if (fule.includes('Hybrid')) return 'Hybrid';
        if (fule.includes('PHEV')) return 'PHEV';
        if (fule.includes('BEV')) return 'BEV';
        if (fule.includes('Hybrid')) return 'Hybrid';
        return 'NA';
    }
}

const retailerDao = new RetailerDao();
export default retailerDao;
