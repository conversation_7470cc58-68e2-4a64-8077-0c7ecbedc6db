import { getResponse } from '@aa/server-utils';
import { ServerResponseCode } from '@aa/http-client';
import { Request, Response } from 'express';
import { VehicleData, SupplierData, VehicleMongoInsertInterface } from '@aa/admin-helpers';
import vehicleDao from '../dao/vehicle.dao';
import retailerDao from '../dao/retailer.dao';
import { getAAHSiteId, vehicleDefaultValues, processVehicleValue } from '../constants/vehicle';
import { retailerDefaultValues, getRetailerAAHSiteId } from '../constants/retailer';
import { Server } from '@aa/server';
import { logger } from '@azure/storage-blob';
import { RestApiService } from '../services/rest-api.service';
import { Endpoints } from '../constants/endpoints';
import * as constants from '../enums/constants';

const aah2Proxy = process.env.loadbalancer === 'localhost' ? 'http://localhost:8080' : 'https://' + process.env.loadbalancer;

const uri = aah2Proxy + Endpoints.MAPPING_API_ADDRESS_LOOKUP;

export const upsertRetailer = async (req: Request, res: Response) => {
    try {
        const retailerRecord: SupplierData[] = await mapSupplierfields(req.body);
        if (req.body.snapshotId?.length > 0) {
            await retailerDao.mongoUpdateSnapshot(req.body.snapshotId, retailerRecord);
        } else {
            await retailerDao.mongoInsertSnapshot(retailerRecord);
        }
        getResponse(res, ServerResponseCode.OK, {
            status: 'ok',
            data: { retailerRecord }
        });
    } catch (err) {
        console.log(err);
    }
};

export const upsertVehicle = async (req: Request, res: Response) => {
    try {
        const vehicleRecord: VehicleData[] = await mapVehiclefields(req.body as VehicleMongoInsertInterface);
        if (req.body.snapshotId?.length > 0) {
            await vehicleDao.mongoUpdateSnapshot(req.body.snapshotId, vehicleRecord);
        } else {
            await vehicleDao.mongoInsertSnapshot(vehicleRecord);
        }
        getResponse(res, ServerResponseCode.OK, {
            status: 'ok',
            data: { vehicleRecord }
        });
    } catch (err) {
        console.log(err);
    }
};

const mapSupplierfields = async (upsertData: any) => {
    const [supInwdPostCode, supOutwdPostCode] = upsertData.PostCode.split(' ');
    const postData = {
        firstLineOfAddress: upsertData.Address1,
        postCode: upsertData.PostCode
    };
    let supplierLatReferenced = 0;
    let supplierLongReferenced = 0;
    if (upsertData.Latitude === 0 && upsertData.Longitude === 0) {
        const supplierAddress = await RestApiService.execute(uri, constants.RequestType.POST, postData);
        if (supplierAddress[0]?.length !== 0 || supplierAddress[0]?.length != undefined) {
            supplierLatReferenced = supplierAddress[0].location.navigation_points[0].location?.latitude;
            supplierLongReferenced = supplierAddress[0].location.navigation_points[0].location?.longitude;
        }
    }

    const supplierPayload: SupplierData[] = [
        {
            CiCode: upsertData.CICode,
            supResourceId: 0,
            supplier: {
                supplierName: upsertData.Retailer_Name,
                supTypeCode: retailerDefaultValues.SUP_TYPE_CODE,
                supplierAddrLine1: upsertData.Address1,
                supplierAddrLine2: upsertData.Address2,
                supplierAddrLine3: upsertData.Town,
                supplierAddrLine4: upsertData.County,
                supOutwdPostCode: supOutwdPostCode,
                supInwdPostCode: supInwdPostCode,
                cheqsAcceptedInd: retailerDefaultValues.CHEQS_ACCEPTED_IND,
                credCrdsAccptdInd: retailerDefaultValues.CRED_CRDS_ACCPTD_IND,
                relPlusVoucherInd: retailerDefaultValues.REL_PLUS_VOUCHER_IND,
                issA120FormsInd: retailerDefaultValues.ISS_A120_FORMS_IND,
                supWkOpenTime: (await retailerDao.convertToOpenAndCloseDate(upsertData.OpenMonday)).openTime,
                supWkCloseTime: (await retailerDao.convertToOpenAndCloseDate(upsertData.OpenMonday)).closeTime,
                supSatOpenTime: (await retailerDao.convertToOpenAndCloseDate(upsertData.OpenSaturday)).openTime,
                supSatCloseTime: (await retailerDao.convertToOpenAndCloseDate(upsertData.OpenSaturday)).closeTime,
                supSunOpenTime: (await retailerDao.convertToOpenAndCloseDate(upsertData.OpenSunday)).openTime,
                supSunCloseTime: (await retailerDao.convertToOpenAndCloseDate(upsertData.OpenSunday)).closeTime,
                supplierAccountNo: upsertData.CICode,
                turnOutTime: retailerDefaultValues.TURN_OUT_TIME,
                drivingSpeedFactor: retailerDefaultValues.DRIVING_SPEED_FACTOR,
                temporaryOpenTime: retailerDefaultValues.TEMPORARY_OPEN_TIME,
                temporaryClosedTime: retailerDefaultValues.TEMPORARY_CLOSED_TIME,
                dplymntMthdCode: retailerDefaultValues.DPLYMNT_MTHD_CODE,
                bkdnOnlyInd: retailerDefaultValues.BKDN_ONLY_IND,
                paymentRateId: retailerDefaultValues.PAYMENT_RATE_ID,
                dateSupAppointed: retailerDefaultValues.DATE_SUP_APPOINTED,
                totNumSupStaff: retailerDefaultValues.TOT_NUM_STAFF,
                turnOutCost: retailerDefaultValues.TURN_OUT_COST,
                kmCost: retailerDefaultValues.KM_COST,
                primeAgentInd: '',
                supNetworkId: await retailerDao.dbGetSupplierNetworkId(upsertData.Group),
                faxLanguage: '',
                incidentManageInd: '',
                minsUntilPrompt: retailerDefaultValues.MINS_UNTIL_PROMPT,
                imgtPromptId: '',
                imgtGroupName: '',
                imgtPriority: '',
                supExtRef1: '',
                supExtRef2: '',
                supExtRef3: '',
                supExtRef4: '',
                dplyZoneShortName: '',
                supplierGrade: '',
                contactName: '',
                selfBillingInd: '',
                email: upsertData.ContactEmail,
                supNetworkKey: ''
            },
            mbRetailer: {
                regionParId: await retailerDao.dbRegionParId(upsertData.SalesRegion, upsertData.Group),
                groupUc: upsertData.Group,
                region1: upsertData.SalesRegion,
                region2: upsertData.AftersalesRegion,
                region3: upsertData.RTMRegion,
                region4: upsertData.FSERegion
            },
            resource: {
                resourceId: 0,
                resourceEffDate: new Date(),
                resourceIneffDate: '',
                owningMgtUnitId: 0,
                resourceTypeId: retailerDefaultValues.RESOURCE_TYPE_ID,
                resourceStatusId: retailerDefaultValues.RESOURCE_STATUS_ID,
                resourceStatusTime: new Date(),
                aahSiteId: getAAHSiteId(),
                ccSiteId: retailerDefaultValues.CC_SITE_ID,
                resourceRemarkId: retailerDefaultValues.RESOURCE_REMARK_ID,
                curTxnId: retailerDefaultValues.CUR_TXN_ID,
                updateSequence: retailerDefaultValues.UPDATE_SEQUENCE
            },
            supplierCoverArea: {
                supAreaName: '',
                supGridSysCode: '',
                supGridPstnNth: 0,
                supGridPstnEast: 0,
                supHomeAreaInd: ''
            },
            supplierResourceTel: {
                resourceTelNo: upsertData.Phone,
                telTypeId: retailerDefaultValues.TEL_TYPE_ID
            },
            supplierLatLong: {
                latitude: upsertData.Latitude == 0 ? supplierLatReferenced : upsertData.Latitude,
                longitude: upsertData.Longitude == 0 ? supplierLongReferenced : upsertData.Longitude
            }
        }
    ];
    return supplierPayload;
};

const mapVehiclefields = async (upsertData: VehicleMongoInsertInterface) => {
    const vehicleProperties = await vehicleDao.getVehicleProperties({
        regNoInput: upsertData.regNo,
        makeInput: upsertData.brand,
        modelInput: upsertData.model,
        colourInput: upsertData.colourName,
        seatsInput: upsertData.seats ? upsertData.seats : null
    });
    const owningMgtUnitId = upsertData.ciCode ? await vehicleDao.dbOwningMgtUnitId(upsertData.ciCode) : 0;

    const vehiclePayload: VehicleData[] = await Promise.all([
        {
            resource_id: 0,
            custGroup: upsertData.custGroup,
            ciCode: upsertData.ciCode,
            resourceData: {
                resourceId: 0,
                resourceEffDate: new Date(),
                resourceIneffDate: upsertData.deFleetDate ? new Date(upsertData.deFleetDate) : undefined,
                owningMgtUnitId: owningMgtUnitId,
                resourceTypeId: vehicleDefaultValues.RESOURCE_TYPE_ID,
                resourceStatusId: vehicleDefaultValues.RESOURCE_STATUS_ID,
                resourceStatusTime: new Date(),
                aahSiteId: getAAHSiteId(),
                ccSiteId: vehicleDefaultValues.CC_SITE_ID,
                resourceRemarkId: vehicleDefaultValues.RESOURCE_REMARK_ID,
                curTxnId: vehicleDefaultValues.CUR_TXN_ID,
                updateSequence: vehicleDefaultValues.UPDATE_SEQUENCE,
                replTime: new Date()
            },
            vehicleData: {
                // resourceId: null,
                vehicleRegNo: upsertData.regNo,
                vorFault: vehicleDefaultValues.VOR_FAULT,
                vorAuthority: vehicleDefaultValues.VOR_AUTHORITY,
                variantId: vehicleProperties.variantId,
                replTime: new Date()
            },
            mbVehicleData: {
                // vehId: null,
                // resourceId: null,
                regionParId: await vehicleDao.dbParId(upsertData.ciCode),
                vin: upsertData.vin,
                trakm8: vehicleDefaultValues.TRAKM8,
                details: JSON.stringify(await vehicleDao.setVehicleDetailsFromUiContract(upsertData, { owningMgtUnitId, ...vehicleProperties })),
                vehModelId: vehicleProperties.modelId,
                infleetDate: upsertData.inFleetDate ? new Date(upsertData.inFleetDate) : undefined,
                defleetDate: upsertData.deFleetDate ? new Date(upsertData.deFleetDate) : undefined,
                vehValue: upsertData.estimatedRRP ? processVehicleValue(upsertData.estimatedRRP, true) : 0,
                vehParId: upsertData.custGroup ? await vehicleDao.getVehicleParId(upsertData.custGroup) : 0,
                replTime: new Date()
            }
        }
    ]);
    return vehiclePayload;
};
