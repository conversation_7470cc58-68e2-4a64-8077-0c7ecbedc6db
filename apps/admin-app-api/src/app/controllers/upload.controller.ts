import { getResponse } from '@aa/server-utils';
import { ServerResponseCode } from '@aa/http-client';
import { Request, Response } from 'express';
import { VehicleData, SupplierData } from '@aa/admin-helpers';
import vehicleDao from '../dao/vehicle.dao';
import retailerDao from '../dao/retailer.dao';
import { excelVehicleMap, getAAHSiteId, vehicleDefaultValues, processVehicleValue } from '../constants/vehicle';
import { excelRetailerMap, retailerDefaultValues, getRetailerAAHSiteId } from '../constants/retailer';
import { mapData, getUploadFileData } from '../services/utils.service';
import { logger } from '@azure/storage-blob';
import { RestApiService } from '../services/rest-api.service';
import { Endpoints } from '../constants/endpoints';
import * as constants from '../enums/constants';

const aah2Proxy = process.env.loadbalancer === 'localhost' ? 'http://localhost:8080' : 'https://' + process.env.loadbalancer;
const uri = aah2Proxy + Endpoints.MAPPING_API_ADDRESS_LOOKUP;

export const uploadVehicles = async (req: Request, res: Response) => {
    try {
        let csvData: any[] = await getUploadFileData(req, res);
        csvData = mapData(csvData, excelVehicleMap);
        const custGroup = req.query['custGroup'] ? (req.query['custGroup'] as string) : '';
        const vehParId = custGroup ? await vehicleDao.getVehicleParId(custGroup) : 0;
        csvData = csvData.filter((i) => i.VEH_REG);
        const mongoSnapshot: VehicleData[] = await Promise.all(
            csvData.map(async (item) => {
                const vehicleProperties = await vehicleDao.getVehicleProperties({
                    regNoInput: item.VEH_REG,
                    makeInput: item.MAKE,
                    modelInput: item.MODEL,
                    colourInput: item.COLOUR,
                    seatsInput: item.SEATS ? item.SEATS : null
                });
                const owningMgtUnitId = await vehicleDao.dbOwningMgtUnitId(item.CICODE);
                return {
                    resource_id: 0,
                    custGroup: custGroup,
                    ciCode: item.CICODE,
                    resourceData: {
                        resourceId: 0,
                        resourceEffDate: new Date(),
                        resourceIneffDate: item.DEFLEET_DATE,
                        owningMgtUnitId: owningMgtUnitId,
                        resourceTypeId: vehicleDefaultValues.RESOURCE_TYPE_ID,
                        resourceStatusId: vehicleDefaultValues.RESOURCE_STATUS_ID,
                        resourceStatusTime: new Date(),
                        aahSiteId: getAAHSiteId(),
                        ccSiteId: vehicleDefaultValues.CC_SITE_ID,
                        resourceRemarkId: vehicleDefaultValues.RESOURCE_REMARK_ID,
                        curTxnId: vehicleDefaultValues.CUR_TXN_ID,
                        updateSequence: vehicleDefaultValues.UPDATE_SEQUENCE,
                        replTime: new Date()
                    },
                    vehicleData: {
                        // resourceId: null,
                        vehicleRegNo: item.VEH_REG,
                        vorFault: vehicleDefaultValues.VOR_FAULT,
                        vorAuthority: vehicleDefaultValues.VOR_AUTHORITY,
                        variantId: vehicleProperties.variantId,
                        replTime: new Date()
                    },
                    mbVehicleData: {
                        // vehId: null,
                        // resourceId: null,
                        regionParId: await vehicleDao.dbParId(item.CICODE),
                        vin: item.VEH_VIN,
                        trakm8: vehicleDefaultValues.TRAKM8,
                        details: JSON.stringify(await vehicleDao.setVehicleDetailsFromFileContract(item, { owningMgtUnitId, ...vehicleProperties })),
                        vehModelId: vehicleProperties.modelId,
                        infleetDate: item.INFLEET_DATE ? new Date(item.INFLEET_DATE) : new Date(),
                        defleetDate: item.DEFLEET_DATE ? new Date(item.DEFLEET_DATE) : new Date(),
                        vehValue: item.VEH_VALUE ? processVehicleValue(item.VEH_VALUE) : 0,
                        vehParId: vehParId,
                        replTime: new Date()
                    }
                };
            })
        );
        await vehicleDao.mongoInsertSnapshot(mongoSnapshot);
        getResponse(res, ServerResponseCode.OK, {
            status: 'ok',
            data: { mongoSnapshot }
        });
    } catch (err) {
        console.log(err);
    }
};

export const uploadRetailers = async (req: Request, res: Response) => {
    try {
        let csvData: any[] = await getUploadFileData(req, res);
        csvData = mapData(csvData, excelRetailerMap);
        csvData = csvData.filter((i) => i.CICode);

        const mongoSnapshot: SupplierData[] = await Promise.all(
            csvData.map(async (item) => {
                const postData = {
                    firstLineOfAddress: item.supplierAddrLine1,
                    postCode: item.supInwdPostCode
                };
                let supplierLatReferenced = 0;
                let supplierLongReferenced = 0;
                if (item.latitude === '0' && item.longitude === '0') {
                    const supplierAddress = await RestApiService.execute(uri, constants.RequestType.POST, postData);
                    if (supplierAddress[0]?.length !== 0 || supplierAddress[0]?.length != undefined) {
                        supplierLatReferenced = supplierAddress[0].location.navigation_points[0].location?.latitude;
                        supplierLongReferenced = supplierAddress[0].location.navigation_points[0].location?.longitude;
                    }
                }
                const owningMgtUnitId = await retailerDao.dbOwningMgtUnitId(item.CICode);
                const [supInwdPostCode, supOutwdPostCode] = item.supInwdPostCode.split(' ');

                return {
                    CiCode: item.CICode,
                    supResourceId: 0,
                    supplier: {
                        sup_network_id: 0,
                        supplierName: item.supplierName,
                        supTypeCode: retailerDefaultValues.SUP_TYPE_CODE,
                        supplierAddrLine1: item.supplierAddrLine1,
                        supplierAddrLine2: item.supplierAddrLine2,
                        supplierAddrLine3: item.supplierAddrLine3,
                        supplierAddrLine4: item.supplierAddrLine4,
                        supOutwdPostCode: supOutwdPostCode,
                        supInwdPostCode: supInwdPostCode,
                        cheqsAcceptedInd: retailerDefaultValues.CHEQS_ACCEPTED_IND,
                        credCrdsAccptdInd: retailerDefaultValues.CRED_CRDS_ACCPTD_IND,
                        relPlusVoucherInd: retailerDefaultValues.REL_PLUS_VOUCHER_IND,
                        issA120FormsInd: retailerDefaultValues.ISS_A120_FORMS_IND,
                        supWkOpenTime: (await retailerDao.convertToOpenAndCloseDate(item.OpenMonday)).openTime,
                        supWkCloseTime: (await retailerDao.convertToOpenAndCloseDate(item.OpenMonday)).closeTime,
                        supSatOpenTime: (await retailerDao.convertToOpenAndCloseDate(item.OpenSaturday)).openTime,
                        supSatCloseTime: (await retailerDao.convertToOpenAndCloseDate(item.OpenSaturday)).closeTime,
                        supSunOpenTime: (await retailerDao.convertToOpenAndCloseDate(item.OpenSunday)).openTime,
                        supSunCloseTime: (await retailerDao.convertToOpenAndCloseDate(item.OpenSunday)).closeTime,
                        supplierAccountNo: item.CICode,
                        turnOutTime: retailerDefaultValues.TURN_OUT_TIME,
                        drivingSpeedFactor: retailerDefaultValues.DRIVING_SPEED_FACTOR,
                        temporaryOpenTime: retailerDefaultValues.TEMPORARY_OPEN_TIME,
                        temporaryClosedTime: retailerDefaultValues.TEMPORARY_CLOSED_TIME,
                        dplymntMthdCode: retailerDefaultValues.DPLYMNT_MTHD_CODE,
                        bkdnOnlyInd: retailerDefaultValues.BKDN_ONLY_IND,
                        paymentRateId: retailerDefaultValues.PAYMENT_RATE_ID,
                        dateSupAppointed: retailerDefaultValues.DATE_SUP_APPOINTED,
                        totNumSupStaff: retailerDefaultValues.TOT_NUM_STAFF,
                        turnOutCost: retailerDefaultValues.TURN_OUT_COST,
                        kmCost: retailerDefaultValues.KM_COST,
                        primeAgentInd: retailerDefaultValues.PRIME_AGENT_IND,
                        supNetworkId: await retailerDao.dbGetSupplierNetworkId(item.groupUc),
                        faxLanguage: '',
                        incidentManageInd: '',
                        minsUntilPrompt: retailerDefaultValues.MINS_UNTIL_PROMPT,
                        imgtPromptId: '',
                        imgtGroupName: '',
                        imgtPriority: '',
                        supExtRef1: '',
                        supExtRef2: '',
                        supExtRef3: '',
                        supExtRef4: '',
                        dplyZoneShortName: '',
                        supplierGrade: '',
                        contactName: '',
                        selfBillingInd: '',
                        email: item.email,
                        supNetworkKey: '',
                        supplier_network_code: item.groupUc
                    },
                    mbRetailer: {
                        regionParId: await retailerDao.dbRegionParId(item.region1, item.groupUc),
                        groupUc: item.groupUc,
                        region1: item.region1,
                        region2: item.region2,
                        region3: item.region3,
                        region4: item.region4
                    },
                    resource: {
                        resourceId: 0,
                        resourceEffDate: new Date(),
                        resourceIneffDate: '',
                        owningMgtUnitId: owningMgtUnitId,
                        resourceTypeId: retailerDefaultValues.RESOURCE_TYPE_ID,
                        resourceStatusId: retailerDefaultValues.RESOURCE_STATUS_ID,
                        resourceStatusTime: new Date(),
                        aahSiteId: getAAHSiteId(),
                        ccSiteId: retailerDefaultValues.CC_SITE_ID,
                        resourceRemarkId: retailerDefaultValues.RESOURCE_REMARK_ID,
                        curTxnId: retailerDefaultValues.CUR_TXN_ID,
                        updateSequence: retailerDefaultValues.UPDATE_SEQUENCE
                    },
                    supplierCoverArea: {
                        supAreaName: '',
                        supGridSysCode: '',
                        supGridPstnNth: 0,
                        supGridPstnEast: 0,
                        supHomeAreaInd: ''
                    },
                    supplierResourceTel: {
                        resourceTelNo: item.resourceTelNo,
                        telTypeId: retailerDefaultValues.TEL_TYPE_ID
                    },
                    supplierLatLong: {
                        latitude: item.latitude == 0 ? supplierLatReferenced : item.latitude,
                        longitude: item.longitude == 0 ? supplierLongReferenced : item.longitude
                    },
                    Franchise: item?.Franchise
                };
            })
        );

        await retailerDao.mongoInsertSnapshot(mongoSnapshot); //mongoSnapshot
        getResponse(res, ServerResponseCode.OK, {
            status: 'ok',
            data: { mongoSnapshot }
        });
    } catch (err) {
        console.log(err);
    }
};
