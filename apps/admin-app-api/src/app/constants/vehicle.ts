export const excelVehicleMap: any = {
    '﻿CICode': 'CICode',
    'CIC ode': 'CICODE',
    '﻿Reg No': 'VEH_REG',
    'Reg No': 'VEH_REG',
    VIN: 'VEH_VIN',
    Brand: 'MAKE',
    Model: 'MOD<PERSON>',
    Derivative: 'DERIVATIVE',
    Options: 'IGNORE',
    'Basic Colour': 'BASIC_COLOUR',
    'Colour Name': 'COLOUR',
    RegistrationDate: 'REG_DATE',
    'InFleet Date': 'INFLEET_DATE',
    'De-Fleet Date': 'DEFLEET_DATE',
    EstimatedRRP: 'VEH_VALUE',
    CICode: 'CICODE',
    Retailer: 'SITE',
    Fuel: 'FUEL',
    'Transmission Description': 'TRANSMISSION_DESCRIPTION', // AWD and TRANSMISSION SET FROM THIS
    'Tow Bar': 'TOWBAR',
    WheelNuts: 'LOCKING_WHEEL_NUT',
    Status: 'IGNORE',
    'Spare Key': 'SPARE_KEY_FOB',
    Europack: 'EUROPAK'
};

// export const vehicleSeat = {
//     Jaguar: {
//         'E-Pace': {
//             Seats: 5,
//         },
//         'F-Pace': {
//             Seats: 5,
//         },
//         'F-Type': {
//             Seats: 2,
//         },
//         'I-Pace': {
//             Seats: 5,
//         },
//         XE: {
//             Seats: 4,
//         },
//         XF: {
//             Seats: 5,
//         },
//         XJ: {
//             Seats: 5,
//         },
//         'F-Type Coupe': {
//             Seats: 2,
//         },
//         'F-Type Convertible': {
//             Seats: 2,
//         },
//         'XF Sportbrake': {
//             Seats: 5,
//         },
//     },
//     'Land Rover': {
//         Defender: {
//             Seats: 5,
//         },
//         'Defender 90': {
//             Seats: 5,
//         },
//         'Defender 110': {
//             Seats: 5,
//         },
//         'Defender 110 XS': {
//             Seats: 7,
//         },
//         'Defender 130': {
//             Seats: 8,
//         },
//         Discovery: {
//             Seats: 7,
//         },
//         'New Discovery': {
//             Seats: 7,
//         },
//         'Discovery Sport': {
//             Seats: 7,
//         },
//         Evoque: {
//             Seats: 5,
//         },
//         'Evoque Convertible': {
//             Seats: 5,
//         },
//         'Range Rover': {
//             Seats: 7,
//         },
//         'Range Rover Sport': {
//             Seats: 7,
//         },
//         'RANGE ROVER VELAR': {
//             Seats: 5,
//         },
//     },
//     Porsche: {
//         Macan: {
//             Seats: 5,
//         },
//         'Macan T': {
//             Seats: 5,
//         },
//         Panamera: {
//             Seats: 5,
//         },
//     },
//     Hyundai: {
//         i30: {
//             Seats: 5,
//         },
//     },
// };

export const vehicleDefaultValues = {
    RESOURCE_TYPE_ID: 3, // Vehicle
    RESOURCE_STATUS_ID: 1,
    RESOURCE_STATUS_TIME: 'sysdate',
    RESOURCE_EFF_DATE: 'sysdate',
    RESOURCE_INEFF_DATE: null,
    CC_SITE_ID: 0,
    RESOURCE_REMARK_ID: 0,
    CUR_TXN_ID: null,
    UPDATE_SEQUENCE: 1,
    TRAKM8: null,
    REPL_TIME: null,
    VOR_FAULT: null,
    VOR_AUTHORITY: null,
    VARAINT_DESCRIPTION: 'Hire Vehicle',
    VEHICLE_TYPE_PREFIX: "'1'",
    ICON_ID: 0,
    DEFAULT_MAKES: ['Jaguar', 'Land Rover', 'Porsche', 'Hyundai'],
    UNKNOWN_CAR_MODEL: 'CAR UNKNOWN',
    DEFAULT_VEHICLE_TYPE: 'CAR'
};

export const makeWiseDefaultPrefixes: { [brand: string]: string } = {
    Jaguar: '1',
    'Land Rover': '2',
    Porsche: '3',
    Hyundai: '4'
};

export const getAAHSiteId = () => {
    const environment = process.env.AAH_ENVIRONMENT ? process.env.AAH_ENVIRONMENT.toUpperCase() : '';
    if (environment == 'LIVE') {
        return 3;
    } else if (environment == 'INTEGRATION') {
        return 55;
    } else if (environment == 'ACCEPTANCE' || environment == 'UAT2') {
        return 19;
    } else if (environment == 'VERIFICATION') {
        //kept separate for now though it is same as integration
        return 55;
    } else {
        return 11;
    }
};

export const processVehicleValue = (EstimatedRRP: string, SkipPound = false) => {
    let vehicleValueStr;
    if (SkipPound) {
        vehicleValueStr = EstimatedRRP.toString().replace(/,/g, '').trimStart();
    } else {
        vehicleValueStr = EstimatedRRP.toString().substring(1).replace(/,/g, '').trimStart(); //remove currency symbol at beggining
    }

    const vehicleValueNumeric = parseFloat(vehicleValueStr);
    return Math.round(vehicleValueNumeric);
};
