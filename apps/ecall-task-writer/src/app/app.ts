import { CustomerGroupCode, EcallTaskData } from '@aa/data-models/common';
import { CustomerRequest } from '@aa/data-models/aux/customer-request';
import { BreakdownTask } from '@aa/data-models/entities/breakdown-task';
import { AuthorisableTask } from '@aa/data-models/entities/task';
import { EcallStore } from '@aa/data-store';
import { EventCode, Exception } from '@aa/exception';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubCheckpoint, EventHubReceiver, QueueEventHandler } from '@aa/queue';
import { BackendEnvironment, PartialBy } from '@aa/utils';

enum EcallTaskEventTypes {
    CREATE_ECALL_TASK = 'create/task/ecall'
}

export class App extends Microservice {
    public name = 'Ecall task writer';
    public application = BackendApplication.ECALL_TASK_WRITER;

    protected static getSeLocator({ customerDDINumber }: EcallTaskData): number {
        // SE locator is a number, lets make sure we can parse customerDDINumber
        customerDDINumber = customerDDINumber.replace(/\D/g, '0');
        // remove 0 from the front so we parse it correctly
        customerDDINumber = customerDDINumber.replace(/^0+/g, '');
        return Number.parseInt(customerDDINumber);
    }

    protected auxStreamReceiver!: EventHubReceiver<
        EcallTaskEventTypes,
        EcallTaskData & {
            customerGroup: CustomerGroupCode;
        },
        EcallWriteResult
    >;
    protected store: EcallStore;

    constructor(environment: BackendEnvironment) {
        super({ environment, appName: 'ecall-task-writer' });

        this.store = new EcallStore({
            logger: this.logger,
            dataStore: this.dataStore
        });

        const checkpoint = new EventHubCheckpoint({
            accountUrl: environment.queue.storageAccountUrl || '',
            accountName: environment.queue.storageAccountName || '',
            accountKey: environment.queue.storageAccountKey || '',
            containerName: 'ecall-task-writer',
            logger: this.logger
        });

        this.auxStreamReceiver = new EventHubReceiver({
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            eventHubName: 'aux-stream',
            consumerGroup: 'ecall-task-writer',
            checkpoint,
            maxBatchSize: 1, // we want to process even 1
            maxDelayPerBatch: 3 // 1s but for 1 ignored
        });

        this.auxStreamReceiver.on(EcallTaskEventTypes.CREATE_ECALL_TASK, this.ecallTaskHandler);
    }

    /**
     * Get bcasp codes for env
     * @return {{[key in CustomerGroupCode]?: string}}
     * @protected
     */
    protected getBcaspCodes(): { [key in CustomerGroupCode]?: string } {
        // for non-prod return diff set of codes
        if (!this.system.isProduction()) {
            return {
                [CustomerGroupCode.JGEC]: 'BCASP532081',
                // [CustomerGroupCode.LREC]: 'BCASP532082',
                [CustomerGroupCode.XBEC]: 'BCASP533318',
                [CustomerGroupCode.LTEC]: 'BCASP533317',
                [CustomerGroupCode.SMEC]: 'BCASP533315'
            };
        }

        // set production bcasp codes as default
        return {
            [CustomerGroupCode.JGEC]: 'BCASP714193',
            // [CustomerGroupCode.LREC]: 'BCASP714194',
            [CustomerGroupCode.XBEC]: 'BCASP714201',
            [CustomerGroupCode.LTEC]: 'BCASP714217',
            [CustomerGroupCode.SMEC]: 'BCASP714209'
        };
    }

    private ecallTaskHandler: QueueEventHandler<
        EcallTaskEventTypes,
        EcallTaskData & {
            customerGroup: CustomerGroupCode;
        },
        EcallWriteResult
    > = async ({ action, entry }) => {
        this.logger.log(`Processor of event for action ${action}`);

        // let's separate extra property from frontend - cust group code,
        // as it's not a part of ecall data we need to preserve
        const ecallTask = entry.data;
        const { customerGroup } = ecallTask;
        const bcaspCodes = this.getBcaspCodes();
        const bcaspCode = bcaspCodes[customerGroup];

        this.logger.log({
            sourceName: this.name,
            message: `Action details`,
            data: { ecallTask, customerGroup, bcaspCode }
        });

        if (!bcaspCode) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                data: { ecallTask, customerGroup },
                message: 'Unsupported customer group'
            });
        }

        if (!ecallTask.customerDDINumber) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                data: { ecallTask, customerGroup },
                message: 'Missing customer DDI number'
            });
        }

        // find entitlement
        const entitlements = await this.connector.entitlement.find.byBCASP(customerGroup, bcaspCode);
        this.logger.log({
            sourceName: this.name,
            message: `Entitlement search result for BCASP ${bcaspCode}`,
            data: { entitlements }
        });

        // get first entitlement
        const entitlement = entitlements.results.shift();
        if (!entitlement) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.MOD_EXEC_WARN,
                data: { ecallTask, entitlements },
                message: 'No valid entitlement for ecall task'
            });
        }

        // TODO: should we supply customerKey here?
        // set seLocator to a numeric representation of a DDI number of customer car
        entitlement.policy.customerKey = App.getSeLocator(ecallTask).toString();

        // get task with all ids, ready to authorise
        const createResponse = await this.connector.task.create(ecallTask, entitlement, 'INFO');
        const ecallCustomerRequest = createResponse.customerRequestHistory[createResponse.customerRequestHistory.length - 1];

        // set created id
        ecallTask.id = createResponse.taskId;
        ecallTask.customerRequestId = ecallCustomerRequest.id;

        this.logger.log({
            sourceName: this.name,
            message: 'Authorising ecall task',
            data: { ecallTask, ecallCustomerRequest }
        });
        await this.connector.task.authorise(ecallTask, ecallCustomerRequest);

        // if breakdown assistance required
        if (!ecallTask.breakdownRequired) {
            return { ecallTask, customerRequest: ecallCustomerRequest };
        }

        // create & authorise RSS
        const { taskId, customerRequest } = await this.connector.task.createForCustomerRequest(ecallCustomerRequest, 'RSS');

        customerRequest.seLocatorId = App.getSeLocator(ecallTask);
        const breakdownTask: AuthorisableTask = {
            operatorId: ecallTask.operatorId,
            customerRequestId: customerRequest.id,
            taskType: { code: 'RSS', name: 'Breakdown' },
            id: taskId
        };

        this.logger.log({
            sourceName: this.name,
            message: 'Authorising breakdown task',
            data: { breakdownTask, customerRequest }
        });
        await this.connector.task.authorise(breakdownTask, customerRequest);
        return { breakdownTask, ecallTask, customerRequest };
    };
}

interface EcallWriteResult {
    ecallTask: EcallTaskData;
    breakdownTask?: AuthorisableTask;
    customerRequest: CustomerRequest;
}
