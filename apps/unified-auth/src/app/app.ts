import { DataStoreProviderType } from '@aa/data-store';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';
import { appendFile } from 'fs';
import { EOL } from 'os';
import { promisify } from 'util';

export class App extends Microservice {
    public name = 'Unified Auth';
    public application = BackendApplication.UNITY_PROCESSOR;

    constructor(environment: BackendEnvironment) {
        super({
            appName: 'unified-auth',
            environment,
            dataStoreProviders: [DataStoreProviderType.REDIS, DataStoreProviderType.ORACLE, DataStoreProviderType.LDAP],
            isAuthServer: true
        });

        this.server.post('/sso-failure/report', this.failureReport, {
            protect: false
        });
    }

    protected failureReport = async (req: Request, res: Response) => {
        const appendFileAsync = promisify(appendFile);
        try {
            const { body } = req;
            await appendFileAsync('auth-failures.log', JSON.stringify(body) + EOL);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Auth failure report write failed'
            });
            this.logger.error(exception);
        }

        // we do not reject a call as this can cause issues
        return getResponse(res, ServerResponseCode.OK);
    };
}
