import { ErrorResponse } from '@aa/connector'; //weird place.
import { AuditClient } from '@aa/audit-client';
import {
    Action,
    ActionOwnership,
    ActionScheduleSLA,
    ActionStats,
    ExtendedPaginationQueryResult,
    Namespace,
    Note,
    NoteReason,
    NoteTripEntity,
    NoteType,
    QueryAction,
    QueryActionStats,
    QueueStatus,
    StatsNoteType,
    Team,
    WithDataId,
    WithQueueMeta
} from '@aa/data-models/common';
import { DataStoreProviderType, MongodbDataProvider } from '@aa/data-store';
import { MongodbUtils } from '@aa/data-store-utils';
import { HttpClient, ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { NoteClient } from '@aa/note-client';
import { RemoteQueue } from '@aa/remote-queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment, RelativeTime, Utils } from '@aa/utils';
import { Request, Response } from 'express';
import { Filter, ObjectId, WithId } from 'mongodb';
import { Exception } from '@aa/exception';

const appName = 'action-api';

/**
 * Check if invalid operator id
 * @param {number} operatorId
 * @return {operatorId is number}
 */
function isValidOperatorId(operatorId?: number): operatorId is number {
    return typeof operatorId === 'number';
}

/**
 * Handle invalid operator id
 * @param {e.Response} res
 */
function handleInvalidOperatorId(res: Response) {
    const response: ErrorResponse = {
        status: 'INVALID_REQ',
        error: { code: 'ERROR', msg: 'Action allowed only for operator' }
    };
    return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
}

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.ACTION_API;
    protected mongoProvider: MongodbDataProvider;
    protected actionQueue: RemoteQueue<'ACTION', Omit<Action, 'expiresAt' | 'scheduledAt'>>;
    protected auditClient: AuditClient;

    /**
     * constructor
     *
     * @param environment
     */
    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName
        });

        this.mongoProvider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);

        this.actionQueue = new RemoteQueue({
            provider: this.mongoProvider,
            logger: this.logger,
            system: this.system,
            collectionName: 'actions',
            dbName: 'queue',
            totalSlots: 0
        });

        //this is async so we must move this to the init method
        this.actionQueue.start();

        this.auditClient = new AuditClient({
            application: BackendApplication.ACTION_API,
            connector: this.connector,
            operatorId: -1
        });

        //this.actionQueue.on('ACTION', this.processAction);

        this.server.get('/action/:id', this.get);
        this.server.post('/action', this.insert);
        this.server.get('/action/:id/complete', this.complete);
        this.server.get('/action/:id/claim', this.claim);
        this.server.post('/action/:id/assign', this.assign); //TODO: finish this
        this.server.get('/action/:id/release', this.release);
        this.server.get('/action/:id/reject', this.reject);
        this.server.post('/action/:id/reschedule', this.reschedule); //TODO: finish this
        this.server.post('/actions', this.list);
        this.server.post('/action/:id/link', this.link);
        this.server.get('/action/:id/unlink', this.unlink);
        this.server.post('/stats', this.stats);
    }

    protected stats = async (req: Request<unknown, unknown, QueryActionStats, unknown>, res: Response<ActionStats>): Promise<void> => {
        try {
            const { namespace } = req.body;
            this.logger.log(`Getting stats actions`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Action>('queue', 'actions');

            const result: ActionStats = {
                namespace: namespace || [],
                counters: {
                    [StatsNoteType.EMAIL]: { expired: 0, total: 0 },
                    [StatsNoteType.MESSAGE]: { expired: 0, total: 0 },
                    [StatsNoteType.EVENT]: { expired: 0, total: 0 },
                    [StatsNoteType.ALERT]: { expired: 0, total: 0 },
                    [StatsNoteType.REMINDER]: { expired: 0, total: 0 }
                }
            };

            const noteTypes = Object.values(StatsNoteType) as StatsNoteType[];

            // if this will be lagging will need to change how actions are counted
            for (const noteType of noteTypes) {
                const expiredActionsCounted = await collection.countDocuments({
                    type: noteType,
                    '_meta.status': QueueStatus.EXPIRED,
                    ...(namespace && { namespace: { $in: namespace } })
                });

                const allActionsCounted = await collection.countDocuments({
                    type: noteType,
                    ...(namespace && { namespace: { $in: namespace } })
                });

                result.counters[noteType] = { expired: expiredActionsCounted, total: allActionsCounted };
            }

            return getResponse(res, ServerResponseCode.OK, result);
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting stats actions`
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Unlink the action from the parent entity and remove all notes associated with the action
     * @param req Id of the action to unlink
     * @param res 200 OK
     */
    protected unlink = async (req: Request<{ id: string }, unknown, unknown>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'unlink',
                data: { id: req.params.id }
            });

            const id = req.params.id;

            const collection = await this.mongoProvider.collection<Action>('queue', 'actions');
            const notesCollection = await this.mongoProvider.collection<Note>('entities', 'notes');

            const document = await collection.findOne({ _id: new ObjectId(id) });
            const parentEntityId = document?.parentEntity?.id;

            //This will remove parent entity from notes
            await notesCollection.updateOne(
                {
                    'parentEntity.id': parentEntityId
                },
                {
                    $set: {
                        'parentEntity.id': ''
                    }
                }
            );

            //This will remove parent entity from Actions
            const result = await collection.updateOne(
                {
                    _id: new ObjectId(id)
                },
                {
                    $set: {
                        'parentEntity.id': ''
                    }
                }
            );

            if (document?.parentEntity && document.parentEntity.id) {
                const parentId = String(document.parentEntity.id);
                const trace = this.auditClient.getTrace(Namespace.EUOPS, parentId);
                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `ActionAPI: Action Unlinked for TripCode: ${req.params.id}`,
                        data: { result }
                    },
                    this.context.store.operatorId as number
                );
            }
            return getResponse(res, ServerResponseCode.OK);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'unlink failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    protected list = async (req: Request<unknown, unknown, QueryAction, unknown>, res: Response<ExtendedPaginationQueryResult<Action>>): Promise<void> => {
        try {
            const { limit, skip, query, filter, sorting, type, status, tripCode, ...restOfQuery } = req.body;
            this.logger.log(`Querying actions`);

            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const collection = await provider.collection<Action>('queue', 'actions');

            const queryObject: Filter<Action> = { ...restOfQuery };

            if (type) {
                queryObject.type = { $in: type };
            }

            if (status && status.length > 0) {
                queryObject['_meta.status'] = { $in: status };
            }

            if (filter) {
                if (filter === 'unassigned') {
                    queryObject.$or = [{ owner: { $exists: false } }];
                } else if (!isNaN(Number(filter))) {
                    queryObject.owner = Number(filter);
                }
            }

            // This looks kind of disgusting, but I do not have better idea for multiple field search in Mongo
            // Any ideas, let me know. Matt
            if (query) {
                queryObject.$or = [
                    { 'parentEntity.id': { $regex: new RegExp(query.replace(/([.?*+^$[\]\\(){}|-])/g, '\\$1'), 'i') } },
                    { content: { $regex: new RegExp(query.replace(/([.?*+^$[\]\\(){}|-])/g, '\\$1'), 'i') } },
                    { 'content.name': { $regex: new RegExp(query.replace(/([.?*+^$[\]\\(){}|-])/g, '\\$1'), 'i') } },
                    { 'content.subject': { $regex: new RegExp(query.replace(/([.?*+^$[\]\\(){}|-])/g, '\\$1'), 'i') } },
                    { 'content.from': { $regex: new RegExp(query.replace(/([.?*+^$[\]\\(){}|-])/g, '\\$1'), 'i') } }
                ];
            }

            if (tripCode) {
                queryObject['parentEntity.id'] = tripCode;
            }

            const cursor = collection.find<Action>(queryObject).sort({ created: -1 });
            const countResult = await MongodbUtils.count(cursor, limit);

            // if none found
            if (!countResult.total) {
                return getResponse(res, ServerResponseCode.OK, {
                    results: [],
                    more: false,
                    ...countResult
                });
            }
            const paginationResult = await MongodbUtils.paginate(cursor, { limit, skip });
            const reshapedResult = paginationResult.results.map((item: Action) => {
                const itemWithMeta = item as WithQueueMeta<Action, 'ACTION'>;
                const { _meta, ...rest } = itemWithMeta;
                return { ...rest, ..._meta };
            });

            return getResponse(res, ServerResponseCode.OK, {
                results: reshapedResult,
                more: paginationResult.more,
                ...countResult
            });
        } catch (error) {
            const exception = new Exception({
                error,
                message: `Failed getting actions`
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**************************************************************************
     *
     * @param req
     * @param res
     * @returns
     *
     *************************************************************************/
    protected reschedule = async (req: Request<{ id: string }, unknown, { scheduleDate: string; expireAt: string; reason: string }>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'reschedule',
                data: { body: req.body }
            });

            const id = req.params.id;
            const { scheduleDate, expireAt, reason } = req.body;
            const scheduledAt = scheduleDate ? new Date(scheduleDate) : new Date();
            const expireDate = expireAt ? new Date(expireAt) : undefined;

            await this.actionQueue.reschedule(id, {
                scheduledAt,
                expiresAt: expireDate
            });
            const collection = await this.mongoProvider.collection<Action>('queue', 'actions');
            const document = await collection.findOne({ _id: new ObjectId(id) });
            if (document?.parentEntity && document.parentEntity.id) {
                const parentId = String(document.parentEntity.id);
                const trace = this.auditClient.getTrace(Namespace.EUOPS, parentId);

                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `ActionAPI: Action rescheduled for TripCode: ${req.params.id} with reason: "${reason}"`,
                        data: { scheduledAt }
                    },
                    this.context.store.operatorId as number
                );
            }

            return getResponse(res, ServerResponseCode.OK);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'reschedule failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /**************************************************************************
     *
     * set ownership to specific owner or team
     *
     * set owner on entry data
     * check how UAC api is getting users operator id
     * update data directly in mongo
     * if only team specified and current owner not in team, set owner value to none
     *
     *  @param {Request<unknown, unknown, ActionOwnership>} req - The request object containing the ownership
     *     information.
     *  @param {Response} res - The response object used to send back the HTTP response.
     *  @returns {Promise<Response>} A promise that resolves to the response object.
     *
     *************************************************************************/
    protected assign = async (req: Request<{ id: string }, unknown, ActionOwnership>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'assign',
                data: { body: req.body }
            });

            const _id = req.params.id; //action's id
            const ownership = req.body as WithId<ActionOwnership>;

            const actionCollection = await this.mongoProvider.collection<Action>('queue', 'actions');

            const teamCollection = await this.mongoProvider.collection<Team>('system-config', 'teams');

            const action = await actionCollection.findOne({
                _id: new ObjectId(_id)
            });
            if (action) {
                //we have an action
                if (ownership.team) {
                    //we were given a team.
                    action.team = ownership.team;
                    const team = (await teamCollection.findOne({
                        _id: new ObjectId(ownership._id)
                    })) as Team;

                    if (ownership.owner) {
                        //we were given an owner. we assume it's on the team.
                        action.owner = ownership.owner;
                    } else if (action.owner) {
                        //we were not given an owner. But does the action have a owner already?
                        if (!team.members.find((member) => member.id === action.owner)) {
                            //is the owner in the team?
                            action.owner = 0; //action owner is not in the new team, set owner to none
                        }
                    }
                } else if (ownership.owner) {
                    //we were not given a team but we were given an owner
                    if (action.team) {
                        //action has a team
                        const team = (await teamCollection.findOne({
                            _id: new ObjectId(action.team)
                        })) as Team;
                        if (action.owner && !team.members.find((member) => member.id === action.owner)) {
                            action.owner = 0; //action owner is not in the team, set owner to none
                        }
                    } else if (ownership.owner) {
                        //action does not have a team bit we were sent a new owner. We go ahead and set the owner
                        action.owner = ownership.owner;
                    }
                }
                await actionCollection.updateOne({ _id: new ObjectId(_id) }, { $set: action });
            }
            if (action?.parentEntity && action.parentEntity.id) {
                const parentId = String(action.parentEntity.id);
                const trace = this.auditClient.getTrace(Namespace.EUOPS, parentId);
                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `ActionAPI: Action claimed for TripCode: ${parentId}`,
                        data: { ...action }
                    },
                    this.context.store.operatorId as number
                );
            }

            return getResponse(res, ServerResponseCode.OK);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'assign failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /**************************************************************************
     *
     * @param req
     * @param res
     * @returns
     *
     *************************************************************************/
    protected release = async (req: Request<{ id: string }, unknown, unknown>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'release',
                data: { id: req.params.id }
            });

            const id = req.params.id;

            const collection = await this.mongoProvider.collection<Action>('queue', 'actions');

            const result = await collection.updateOne(
                {
                    _id: new ObjectId(id)
                },
                {
                    $unset: { owner: '' }
                }
            );
            const document = await collection.findOne({ _id: new ObjectId(id) });
            if (document?.parentEntity && document.parentEntity.id) {
                const parentId = String(document.parentEntity.id);
                const trace = this.auditClient.getTrace(Namespace.EUOPS, parentId);
                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `ActionAPI: Action unclaimed for TripCode: ${parentId}`,
                        data: { ...result }
                    },
                    this.context.store.operatorId as number
                );
            }

            return getResponse(res, ServerResponseCode.OK);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'release failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /**************************************************************************
     *
     * @param req
     * @param res
     * @returns
     *
     *************************************************************************/
    protected claim = async (req: Request<{ id: string }, unknown, unknown>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'claim',
                data: { id: req.params.id }
            });

            const id = req.params.id;
            const operatorId = this.context.store.operatorId;

            if (!isValidOperatorId(operatorId)) {
                return handleInvalidOperatorId(res);
            }

            const collection = await this.mongoProvider.collection<Action>('queue', 'actions');

            const result = await collection.updateOne(
                {
                    _id: new ObjectId(id)
                },
                {
                    $set: { owner: operatorId }
                }
            );

            const document = await collection.findOne({ _id: new ObjectId(id) });

            if (document?.parentEntity && document.parentEntity.id) {
                const parentId = String(document.parentEntity.id);
                const trace = this.auditClient.getTrace(Namespace.EUOPS, parentId);
                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `ActionAPI: Action claimed for TripCode: ${parentId}`,
                        data: { ...result }
                    },
                    this.context.store.operatorId as number
                );
            }

            return getResponse(res, ServerResponseCode.OK);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'claim failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /**************************************************************************
     * get action with metadata from queue
     *
     * do a query for id in the queues.actions
     * get all data including queue meta
     * get from the _meta data like 'expiresAt' | 'scheduledAt'
     * merge them with rest of data from the object
     * remove _meta from the object
     *
     * @param req
     * @param res
     * @returns
     *
     *************************************************************************/
    protected get = async (req: Request<{ id: string }, unknown, unknown>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'get',
                data: { id: req.params.id }
            });

            const _id = req.params.id;

            const collection = await this.mongoProvider.collection<WithQueueMeta<Action, 'ACTION'>>('queue', 'actions');

            const action = await collection.findOne({ _id: new ObjectId(_id) });

            if (action) {
                const { _meta, ...actionWithoutMeta } = action;
                const { expiresAt, scheduledAt, status } = _meta;

                //don't like this. adding expiresAt and scheduledAt to the action object seems weird and defeats the
                // purpose of _meta
                const updatedAction: Omit<Action, '_meta'> & {
                    expiresAt?: Date | string | number;
                    scheduledAt?: Date | string | number;
                    status?: string;
                } = {
                    ...actionWithoutMeta,
                    expiresAt,
                    scheduledAt,
                    status
                };

                return getResponse(res, ServerResponseCode.OK, updatedAction);
            }
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'get failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /**************************************************************************
     *
     * @param req
     * @param res
     * @returns
     *
     *************************************************************************/
    protected insert = async (req: Request<unknown, unknown, Action>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'insert',
                data: { body: req.body }
            });

            const actionData: Action = req.body as Action;
            actionData.created = new Date();
            actionData.updated = new Date();
            actionData.owner = actionData.owner ? actionData.owner : '';

            const { ...action } = actionData;
            let { expiresAt, scheduledAt } = actionData;

            if (!expiresAt) {
                let expireAtSLA;
                switch (actionData.type) {
                    case NoteType.ALERT:
                        expireAtSLA = ActionScheduleSLA.EUOPS?.ALERT;
                        break;
                    case NoteType.EMAIL:
                        expireAtSLA = ActionScheduleSLA.EUOPS?.EMAIL;
                        break;
                    case NoteType.EVENT:
                        expireAtSLA = ActionScheduleSLA.EUOPS?.EVENT;
                        break;
                    case NoteType.MESSAGE:
                        expireAtSLA = ActionScheduleSLA.EUOPS?.MESSAGE;
                        if (actionData.context?.reason === NoteReason.SPARX_MESSAGE) expireAtSLA = `45m` as RelativeTime;
                        break;
                    case NoteType.TEXT:
                        expireAtSLA = ActionScheduleSLA.EUOPS?.TEXT;
                        break;
                    case NoteType.SYSTEM:
                        expireAtSLA = ActionScheduleSLA.EUOPS?.SYSTEM;
                        break;
                }

                expiresAt = expireAtSLA && Utils.generateFutureDate(expireAtSLA);
            }

            if (!scheduledAt) {
                scheduledAt = new Date();
            }

            const insertedId = await this.actionQueue.schedule('ACTION', action, { scheduledAt, expiresAt });

            const insertedAction: WithDataId<Action> = {
                ...action,
                _id: insertedId.toString()
            };

            return getResponse(res, ServerResponseCode.OK, insertedAction);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'insert failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /**************************************************************************
     *
     * @param req
     * @param res
     * @returns
     *
     *************************************************************************/
    protected complete = async (req: Request<{ id: string }, unknown, unknown>, res: Response) => {
        try {
            // Extract the reason from the query parameters
            const reason = req.query.reason as string;

            this.logger.info({
                sourceName: this.name,
                message: 'complete',
                data: { id: req.params.id, reason }
            });

            const _id = req.params.id;
            await this.actionQueue.complete(_id);

            const collection = await this.mongoProvider.collection<WithId<Action>>('queue', 'actions');
            const action = await collection.findOne({ _id: new ObjectId(_id) });

            // Check if there is a parent entity and log/report with the reason if necessary
            if (action?.parentEntity && action.parentEntity.id) {
                const parentId = String(action.parentEntity.id);
                const trace = this.auditClient.getTrace(Namespace.EUOPS, parentId);
                await this.auditClient.reportAction(
                    trace,
                    {
                        message: `ActionAPI: Action archived for TripCode: ${parentId} with reason: "${reason}"`,
                        data: { ...action, reason } // Include reason in the report if needed
                    },
                    action.owner as number
                );
            }

            return getResponse(res, ServerResponseCode.OK, action);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'complete failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /**************************************************************************
     *
     * @param req
     * @param res
     * @returns
     *
     *************************************************************************/
    protected reject = async (req: Request<{ id: string }, unknown, unknown>, res: Response) => {
        try {
            this.logger.info({
                sourceName: this.name,
                message: 'reject',
                data: { id: req.params.id }
            });

            const _id = req.params.id;
            await this.actionQueue.fail(_id);

            const collection = await this.mongoProvider.collection<WithId<Action>>('queue', 'actions');
            const action = await collection.findOne({ _id: new ObjectId(_id) });

            return getResponse(res, ServerResponseCode.OK, action);
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'reject failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };

    /**************************************************************************

     *  @param {Request<unknown, unknown, NoteTripEntity>} req -
     *  @param {Response} res - The response object used to send back the HTTP response.

     /**************************************************************************/

    protected link = async (req: Request<{ id: string }, unknown, NoteTripEntity>, res: Response) => {
        try {
            // Action _id
            const { id } = req.params;
            // Parent entity id
            const { type, id: parentEntityId } = req.body;

            if (!type || !parentEntityId) {
                this.logger.warn({
                    sourceName: this.name,
                    message: 'Invalid Actionlink data',
                    data: { id, type, parentEntityId }
                });
                return getResponse(res, ServerResponseCode.BAD_REQUEST, 'Invalid Actionlink data');
            }

            if (!ObjectId.isValid(id)) {
                this.logger.warn({
                    sourceName: this.name,
                    message: 'Invalid ObjectId',
                    data: { id }
                });
                return getResponse(res, ServerResponseCode.BAD_REQUEST, 'Invalid ID format');
            }

            const objectId = new ObjectId(id);
            const collection = await this.mongoProvider.collection<Action>('queue', 'actions');
            const result = await collection.updateOne(
                { _id: objectId },
                {
                    $set: {
                        // set parententity
                        'parentEntity.type': type,
                        'parentEntity.id': parentEntityId
                    }
                }
            );
            // Find the updated action document
            const action = await collection.findOne({ _id: objectId });

            if (!action) {
                return getResponse(res, ServerResponseCode.SERVER_ERROR, 'Action not found');
            }

            // Extract the note details from the action
            const { team, assignedBy, owner, ownerSince, scheduledAt, _id, ...note } = action;
            // Initialize NoteClient with necessary configurations
            const client = new NoteClient({
                httpClient: new HttpClient({ authClient: this.authClient }),
                connector: this.connector
            });
            // Insert the note using NoteClient
            try {
                const noteInsertionResult = await client.insertNote(note);
                const collection = await this.mongoProvider.collection<Action>('queue', 'actions');
                const document = await collection.findOne({ _id: new ObjectId(id) });
                if (document?.parentEntity && document.parentEntity.id) {
                    const parentId = String(document.parentEntity.id);
                    const trace = this.auditClient.getTrace(Namespace.EUOPS, parentId);
                    await this.auditClient.reportAction(
                        trace,
                        {
                            message: `ActionAPI: Action Linked for TripCode: ${req.params.id}`,
                            data: { noteInsertionResult }
                        },
                        action.owner as number
                    );
                }

                return getResponse(res, ServerResponseCode.OK);
            } catch (insertError) {
                console.error('Exception during note insertion:', insertError);
                return getResponse(res, ServerResponseCode.SERVER_ERROR, 'Failed to insert note');
            }
        } catch (ex) {
            this.logger.error({
                sourceName: this.name,
                message: 'Link failed with error:',
                data: { ex }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, ex);
        }
    };
}
