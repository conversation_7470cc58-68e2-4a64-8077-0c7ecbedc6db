@port = 8395

# Filter all actions by type and query
POST {{host}}:{{port}}/api/action-api-service/actions
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "type": ["MESSAGE"],
    "query": "123"
}

###

# Complete action
GET {{host}}:{{port}}/api/action-api-service/action/66ed300b26e7b1ec064e8438/complete
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

//{
//    "type": ["MESSAGE"],
//    "query": "123"
//}

###

# Reschedule action
POST {{host}}:{{port}}/api/action-api-service/action/66ed300b26e7b1ec064e8438/reschedule
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "scheduleDate": "2025-01-01T00:00:00Z"
}

###

# Filter actions
POST {{host}}:{{port}}/api/action-api-service/actions
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "type": ["MESSAGE", "EMAIL"],
    "query": "",
    "filter": "9140066"
}

###

# Get stats
POST {{host}}:{{port}}/api/action-api-service/stats
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "namespace": ["EUOPS"]
}



###

# Get stats
POST http://0.0.0.0:5722/api/task-service/task/add
Content-Type: application/json
x-auth-apikey: Bearer {{apiKey}}

{
    "task": {
        "id": -1,
        "fleetReferral": false,
        "customerRequestId": 36301221,
        "createReason": {
            "id": 13,
            "name": "Hotel",
            "serviceType": "ADMIN",
            "createBehaviours": [
                "CB_REOPEN",
                "CB_DIAG",
                "CB_RPLUS",
                "CB_RSS",
                "CB_YES"
            ],
            "generalBehaviours": [
                "GB_COMP",
                "GB_NO_DEST"
            ],
            "trackBehaviours": [],
            "isPublicTransport": false
        },
        "status": "UNAC",
        "jobsheet": null,
        "contact": {
            "name": "Eventtest",
            "telephone": "07283746272",
            "extension": null,
            "eveningTelephone": null,
            "eveningExtension": null,
            "email": null,
            "extraChannels": {
                "pushNotification": null,
                "trackingLinkUrl": null
            }
        },
        "altContact": {
            "name": "EventTest",
            "telephone": "07283746272",
            "extension": null,
            "eveningTelephone": null,
            "eveningExtension": null,
            "email": null,
            "extraChannels": {
                "pushNotification": null,
                "trackingLinkUrl": null
            }
        },
        "entitlement": {
            "customerGroup": {
                "id": 414,
                "code": "JAG",
                "name": "JAGUAR ASSISTANCE",
                "seSystemId": 20,
                "resSystemId": 20,
                "warrantyPrompt": false,
                "allowForceRecy": true,
                "captureMileage": true,
                "displayInd": true,
                "driverDetsReqInd": true,
                "hirerNetworkId": 295,
                "supNetworkId": 1,
                "secondLevelSrchAllowed": false,
                "firstLevelValidationLabel": null,
                "recoveryCheckRequired": false,
                "msgHandling": true,
                "B2BGroupId": 4
            },
            "memberDetails": [
                "JAGUAR LAND ROVER LIMITED",
                "Banbury Road",
                "Gaydon",
                "WARWICK",
                "WARWICK",
                "CV35 0XJ",
                "",
                "10163267",
                "G01"
            ],
            "productPackage": {
                "code": null,
                "name": null
            },
            "packageName": null,
            "benefits": [
                {
                    "id": 1,
                    "code": "B",
                    "name": "ROADSIDE"
                },
                {
                    "id": 2,
                    "code": "H",
                    "name": "Home Start"
                },
                {
                    "id": 3,
                    "code": "R",
                    "name": "Relay"
                },
                {
                    "id": 4,
                    "code": "P",
                    "name": "Relay Plus"
                },
                {
                    "id": 16,
                    "code": "FE",
                    "name": "Fleet Europe"
                }
            ],
            "riskCode": null,
            "textTopics": [
                {
                    "flexFieldData": {
                        "fieldType": "MANDATORY",
                        "label": "Patrol Instructions",
                        "value": "DO NOT follow your ASC process apart from the Satisfaction element <br/><br/>For Relay / Relief Vehicle / Parts contact 03701422777 for instructions<br/><br/>Contact - TSC or Prestige Centre on 03701422777 - For Technical Assistance"
                    },
                    "flexFieldFormat": {
                        "dataSize": 19,
                        "dataType": "TEXT_TOPIC",
                        "faultCode": [],
                        "position": "J",
                        "select": []
                    },
                    "format": " Jm19B"
                },
                {
                    "flexFieldData": {
                        "fieldType": "MANDATORY",
                        "label": "Relay Plus",
                        "value": "Car Hire Duration: 2 days booked via JLR Mobility <br/><br/>Driving Licence Required:     Yes<br/>Credit Card Required:          No<br/>Contact Number: 0800521786 Opt 2<br/><br/>Out of Hours times: <br/>Monday to Thursday OOHs:  19:30  07:30<br/>Friday OOHs:                     19:30  08:00 Sat <br/>Saturday OOH:                  17:00  09:00 Sun<br/>Sunday OOH:                     17:00  07:30 Mon<br/><br/>***Please call all OOH bookings through to JLR Mobility via 0800521786 Opt 2***"
                    },
                    "flexFieldFormat": {
                        "dataSize": 10,
                        "dataType": "TEXT_TOPIC",
                        "faultCode": [],
                        "position": "G",
                        "select": []
                    },
                    "format": " Gm10B"
                },
                {
                    "flexFieldData": {
                        "fieldType": "MANDATORY",
                        "label": "Relay And Recovery",
                        "value": "Recover vehicle to: Driver's choice of destination <br/><br/>Recommend Jaguar dealership nearest to driver's home address"
                    },
                    "flexFieldFormat": {
                        "dataSize": 18,
                        "dataType": "TEXT_TOPIC",
                        "faultCode": [],
                        "position": "G",
                        "select": []
                    },
                    "format": " Gm18B"
                },
                {
                    "flexFieldData": {
                        "fieldType": "MANDATORY",
                        "label": "Parts Instructions",
                        "value": "DO NOT charge driver for Parts at the roadside"
                    },
                    "flexFieldFormat": {
                        "dataSize": 18,
                        "dataType": "TEXT_TOPIC",
                        "faultCode": [],
                        "position": "B",
                        "select": []
                    },
                    "format": " Bm18B"
                },
                {
                    "flexFieldData": {
                        "fieldType": "MANDATORY",
                        "label": "Accident Procedures",
                        "value": "24 HOURS: TRANSFER Driver to Jaguar Accident Management on 0207 206 0584 - Following an RTC"
                    },
                    "flexFieldFormat": {
                        "dataSize": 19,
                        "dataType": "TEXT_TOPIC",
                        "faultCode": [],
                        "position": "B",
                        "select": []
                    },
                    "format": " Bm19B"
                }
            ],
            "variableData": [
                {
                    "flexFieldData": {
                        "fieldType": "MANDATORY",
                        "value": "3125"
                    },
                    "flexFieldFormat": {
                        "dataSize": 10,
                        "dataType": "INTEGER",
                        "faultCode": [],
                        "fieldNumber": 333,
                        "label": "FDDS Code",
                        "position": "B",
                        "select": []
                    },
                    "format": "333Bm10I"
                },
                {
                    "flexFieldData": {
                        "fieldType": "MANDATORY_ON_COMPLETION_MDT",
                        "value": ""
                    },
                    "flexFieldFormat": {
                        "dataSize": 0,
                        "dataType": "INTEGER",
                        "faultCode": [],
                        "fieldNumber": 730,
                        "label": "Mileage",
                        "position": "X",
                        "select": []
                    },
                    "format": "730XC0I"
                },
                {
                    "flexFieldData": {
                        "fieldType": "READ_ONLY",
                        "value": ""
                    },
                    "flexFieldFormat": {
                        "dataSize": 0,
                        "dataType": "TEXT",
                        "faultCode": [],
                        "fieldNumber": 830,
                        "label": "",
                        "position": "X",
                        "select": []
                    },
                    "format": "830Xr0C"
                },
                {
                    "flexFieldData": {
                        "fieldType": "READ_ONLY",
                        "value": "AAH2"
                    },
                    "flexFieldFormat": {
                        "dataSize": 4,
                        "dataType": "TEXT",
                        "faultCode": [],
                        "fieldNumber": 847,
                        "label": "",
                        "position": "X",
                        "select": []
                    },
                    "format": "847Xr4C"
                }
            ],
            "fairPlay": null
        },
        "location": {
            "text": "THE AUTOMOBILE ASSOCIATION, WOLVERHAMPTON ROAD, SWALLOWFIELD ONE",
            "area": "DAC OLDBURY, WEST MIDLANDS, ENGLAND, B69 2AG",
            "remarks": null,
            "coordinates": {
                "latitude": 52.49418,
                "longitude": -2.02367
            },
            "remarksToAppend": "",
            "lane": null,
            "slipRoad": null,
            "dot": "",
            "lastJunctionPassed": null,
            "travellingTo": null,
            "isHardShoulder": false,
            "isActiveLane": false,
            "country": {
                "code": "GB",
                "name": "United Kingdom"
            }
        },
        "sequence": 10,
        "jobNoToday": 4,
        "operatorId": 9140066,
        "parentTaskId": -1,
        "changeOfNote": false,
        "partsStatus": null,
        "fault": {
            "capabilities": [
                {
                    "id": 145,
                    "name": "RELAY PLUS HIRE"
                }
            ],
            "outcome": {
                "completionCode": null,
                "componentCode": null,
                "completionFaultCode": null,
                "component": {
                    "code": null,
                    "name": null
                },
                "completionFault": {
                    "code": null,
                    "name": null
                },
                "completion": {
                    "code": null,
                    "name": null
                }
            },
            "repairProbability": 0,
            "id": 75,
            "name": "FLAT BATTERY",
            "code": {
                "code": "FB",
                "name": "FLAT BATTERY"
            },
            "categoryCode": "ELE",
            "amFaultChk": false,
            "motorwayChk": "",
            "rtaChk": false,
            "userPrompt": "",
            "mwayRecovVehFaultId": 0,
            "nextQuestionId": -1,
            "nextQuestionInstance": -1,
            "repairId": -1,
            "initialFault": {
                "id": 75,
                "name": "FLAT BATTERY"
            },
            "diagnosticsQAList": [
                {
                    "answers": [],
                    "name": "What is the vehicle fault?",
                    "selectedAnswer": {
                        "id": 0,
                        "instance": 0,
                        "patrolMsg": "",
                        "name": "FLAT BATTERY",
                        "nextQuestionId": 0,
                        "nextQuestionInstance": 0,
                        "repairId": 0,
                        "estimatedTimeToComplete": 0,
                        "recoveryJobInd": false,
                        "isUnknown": false
                    }
                }
            ],
            "quickCode": null
        },
        "_telematicsDiagnostics": null,
        "eligibilityQAList": [],
        "appointment": {
            "earliest": "2025-01-29T14:46:29.906Z",
            "latest": "2025-01-29T16:46:29.906Z",
            "appointmentChanged": true,
            "excludeResourceId": null,
            "exclusiveResourceId": null
        },
        "schedule": {
            "create": "2025-01-29T14:46:29.906Z",
            "dispatch": "2025-01-29T14:46:05.000Z",
            "arrive": "2025-01-29T16:46:05.000Z",
            "complete": "2025-01-29T17:06:05.000Z",
            "callsign": null,
            "ringOnApproach": 0,
            "resource": {
                "id": 0,
                "physical": false,
                "location": {
                    "text": null,
                    "area": null,
                    "remarks": null,
                    "coordinates": {
                        "latitude": null,
                        "longitude": null
                    },
                    "remarksToAppend": "",
                    "lane": null,
                    "slipRoad": null,
                    "dot": "",
                    "lastJunctionPassed": null,
                    "travellingTo": null,
                    "isHardShoulder": false,
                    "isActiveLane": false,
                    "country": {
                        "code": "GB",
                        "name": "United Kingdom"
                    }
                },
                "endLocation": {
                    "text": null,
                    "area": null,
                    "remarks": null,
                    "coordinates": {
                        "latitude": null,
                        "longitude": null
                    },
                    "remarksToAppend": "",
                    "lane": null,
                    "slipRoad": null,
                    "dot": "",
                    "lastJunctionPassed": null,
                    "travellingTo": null,
                    "isHardShoulder": false,
                    "isActiveLane": false,
                    "country": {
                        "code": "GB",
                        "name": "United Kingdom"
                    }
                },
                "startTime": null,
                "plannedEndTime": null,
                "capabilities": [],
                "updateUI": false,
                "break1": null,
                "break2": null,
                "aaVehicle": null,
                "type": {
                    "id": -1,
                    "name": null
                },
                "unproductive1": {
                    "startTime": null,
                    "endTime": null,
                    "status": null
                },
                "unproductive2": {
                    "startTime": null,
                    "endTime": null,
                    "status": null
                }
            }
        },
        "glassModelId": -1,
        "vehicle": {
            "registration": "RO71LXW",
            "modelId": 14941,
            "colour": "RED",
            "makeId": 171,
            "typeId": 1,
            "model": {
                "id": 14941,
                "name": "NIRO",
                "weight": 1400,
                "typeId": 1,
                "makeId": 171,
                "frontLiftWeight": 800,
                "rearLiftWeight": 600
            },
            "experianDetails": {
                "make": "KIA",
                "model": "NIRO 4 HEV S-A",
                "colour": "RED",
                "driveType": "4X2",
                "height": "1560",
                "width": "1805",
                "seatNumber": null,
                "transmission": "SEMI AUTO 6 GEARS",
                "grossWeight": "1930",
                "ukDateFirstRegistered": "20211015",
                "dateFirstRegistered": "20211015",
                "bodyStyle": "ESTATE",
                "modelVariant": "4",
                "smmtRange": "NIRO",
                "engineNo": "G4LEMS804628",
                "co2Emissions": "120",
                "mvrisCode": "Q7 BTS",
                "vin": "KNACC81CVN5493969",
                "vinConfirmFlag": false,
                "engineSize": "1580",
                "yrOfManufacture": "2021",
                "fuel": "HYB PETROL",
                "wheelPlan": "2 AXLE RIGID BODY",
                "imported": false,
                "series": "DE",
                "abiBrokernetCode": "25737468",
                "gears": "6",
                "importNonEu": false,
                "kerbWeight": "1490",
                "length": "4375",
                "wrongDetailsFlag": false,
                "flag": true,
                "doorPlan": "ESTATE",
                "doorPlanCode": "06"
            },
            "trailerDetails": {
                "make": null,
                "model": null,
                "colour": null,
                "type": null,
                "axles": null,
                "twinned": null,
                "hookUp": null,
                "curSymbol": null,
                "value": null,
                "abandonedFlag": null,
                "notes": null,
                "diagnosis": null,
                "fault": null,
                "yearConstructed": null,
                "weight": null,
                "length": null,
                "height": null,
                "width": null
            },
            "vehicleOwnerDetails": {
                "title": null,
                "firstName": null,
                "surName": null,
                "contactNumber": null,
                "email": null,
                "address": null,
                "postCode": null
            },
            "roadworthy": {
                "registrationNumber": "RO71LXW",
                "yearOfManufacture": "2021",
                "monthOfFirstRegistration": "2021-10-01T00:00:00.000Z",
                "motExpiryDate": "2025-10-14T00:00:00.000Z",
                "motStatus": "Valid",
                "taxDueDate": "2025-10-01T00:00:00.000Z",
                "taxStatus": "Taxed"
            },
            "twinnedWheels": null
        },
        "recovery": {
            "destination": {
                "text": null,
                "area": null,
                "remarks": null,
                "coordinates": {
                    "latitude": null,
                    "longitude": null
                },
                "remarksToAppend": "",
                "lane": null,
                "slipRoad": null,
                "dot": "",
                "lastJunctionPassed": null,
                "travellingTo": null,
                "isHardShoulder": false,
                "isActiveLane": false,
                "country": {
                    "code": "GB",
                    "name": "United Kingdom"
                }
            },
            "fault": {
                "capabilities": [],
                "outcome": {
                    "completionCode": null,
                    "componentCode": null,
                    "completionFaultCode": null,
                    "component": {
                        "code": null,
                        "name": null
                    },
                    "completionFault": {
                        "code": null,
                        "name": null
                    },
                    "completion": {
                        "code": null,
                        "name": null
                    }
                },
                "repairMinutes": -1,
                "repairProbability": -1,
                "id": -1,
                "name": null,
                "code": {
                    "code": null,
                    "name": null
                },
                "categoryCode": null,
                "additionalInfo": null,
                "amFaultChk": false,
                "motorwayChk": "",
                "rtaChk": false,
                "userPrompt": "",
                "mwayRecovVehFaultId": 0,
                "nextQuestionId": -1,
                "nextQuestionInstance": -1,
                "repairId": -1,
                "initialFault": {
                    "id": -1,
                    "name": null
                },
                "diagnosticsQAList": [],
                "quickCode": null
            },
            "follow": false,
            "motorway": false,
            "passengerRun": false,
            "relay": false,
            "adults": null,
            "children": 0,
            "dogs": 0,
            "unaccompaniedAfterLoading": false,
            "keysLocation": null,
            "forceRecyFault": false,
            "reasonForce": false,
            "reasonExpert": false,
            "reasonRoadOps": false,
            "email": null,
            "franchiseEmail": null,
            "destResourceId": null,
            "isTowing": false
        },
        "epyxGarageReference": null,
        "supJobTypeCode": null,
        "membImpressed": false,
        "canReattend": false,
        "canReopen": false,
        "split": true,
        "uiStatus": {
            "appointment": false,
            "contact": false,
            "fault": false,
            "location": false,
            "relay": true,
            "supplier": false,
            "vehicle": false
        },
        "parentTask": {
            "id": 51360792,
            "fleetReferral": false,
            "createReason": {
                "id": -1,
                "name": null,
                "serviceType": null,
                "instructions": null,
                "createBehaviours": [],
                "generalBehaviours": [],
                "trackBehaviours": [],
                "isPublicTransport": false
            },
            "status": "UNAC",
            "jobsheet": null,
            "contact": {
                "name": null,
                "telephone": null,
                "extension": null,
                "eveningTelephone": null,
                "eveningExtension": null,
                "email": null,
                "extraChannels": {
                    "pushNotification": null,
                    "trackingLinkUrl": null
                }
            },
            "altContact": {
                "name": null,
                "telephone": null,
                "extension": null,
                "eveningTelephone": null,
                "eveningExtension": null,
                "email": null,
                "extraChannels": {
                    "pushNotification": null,
                    "trackingLinkUrl": null
                }
            },
            "entitlement": {
                "customerGroup": {
                    "warrantyPrompt": false,
                    "allowForceRecy": false,
                    "captureMileage": false,
                    "displayInd": false,
                    "driverDetsReqInd": false,
                    "hirerNetworkId": -1,
                    "supNetworkId": -1,
                    "secondLevelSrchAllowed": false,
                    "firstLevelValidationLabel": null,
                    "recoveryCheckRequired": false,
                    "msgHandling": false,
                    "B2BGroupId": 0
                },
                "memberDetails": [],
                "productPackage": {
                    "code": null,
                    "name": null
                },
                "packageName": null,
                "benefits": [],
                "riskCode": null,
                "textTopics": [],
                "variableData": [],
                "fairPlay": null
            },
            "location": {
                "text": null,
                "area": null,
                "remarks": null,
                "coordinates": {
                    "latitude": null,
                    "longitude": null
                },
                "remarksToAppend": "",
                "lane": null,
                "slipRoad": null,
                "dot": "",
                "lastJunctionPassed": null,
                "travellingTo": null,
                "isHardShoulder": false,
                "isActiveLane": false,
                "country": {
                    "code": "GB",
                    "name": "United Kingdom"
                }
            },
            "sequence": 6,
            "jobNoToday": -1,
            "operatorId": -1,
            "parentTaskId": -1,
            "changeOfNote": false,
            "partsStatus": null,
            "fault": {
                "capabilities": [],
                "outcome": {
                    "completionCode": null,
                    "componentCode": null,
                    "completionFaultCode": null,
                    "component": {
                        "code": null,
                        "name": null
                    },
                    "completionFault": {
                        "code": null,
                        "name": null
                    },
                    "completion": {
                        "code": null,
                        "name": null
                    }
                },
                "repairMinutes": -1,
                "repairProbability": -1,
                "id": -1,
                "name": null,
                "code": {
                    "code": null,
                    "name": null
                },
                "categoryCode": null,
                "additionalInfo": null,
                "amFaultChk": false,
                "motorwayChk": "",
                "rtaChk": false,
                "userPrompt": "",
                "mwayRecovVehFaultId": 0,
                "nextQuestionId": -1,
                "nextQuestionInstance": -1,
                "repairId": -1,
                "initialFault": {
                    "id": -1,
                    "name": null
                },
                "diagnosticsQAList": [],
                "quickCode": null
            },
            "_telematicsDiagnostics": null,
            "eligibilityQAList": [],
            "appointment": {
                "earliest": null,
                "latest": null,
                "appointmentChanged": false,
                "excludeResourceId": null,
                "exclusiveResourceId": null
            },
            "schedule": {
                "create": "2025-01-28T16:07:52.000Z",
                "dispatch": "2025-01-29T14:46:05.000Z",
                "arrive": "2025-01-29T16:46:05.000Z",
                "complete": "2025-01-29T17:06:05.000Z",
                "callsign": null,
                "ringOnApproach": 0,
                "resource": {
                    "id": 0,
                    "physical": false,
                    "location": {
                        "text": null,
                        "area": null,
                        "remarks": null,
                        "coordinates": {
                            "latitude": null,
                            "longitude": null
                        },
                        "remarksToAppend": "",
                        "lane": null,
                        "slipRoad": null,
                        "dot": "",
                        "lastJunctionPassed": null,
                        "travellingTo": null,
                        "isHardShoulder": false,
                        "isActiveLane": false,
                        "country": {
                            "code": "GB",
                            "name": "United Kingdom"
                        }
                    },
                    "endLocation": {
                        "text": null,
                        "area": null,
                        "remarks": null,
                        "coordinates": {
                            "latitude": null,
                            "longitude": null
                        },
                        "remarksToAppend": "",
                        "lane": null,
                        "slipRoad": null,
                        "dot": "",
                        "lastJunctionPassed": null,
                        "travellingTo": null,
                        "isHardShoulder": false,
                        "isActiveLane": false,
                        "country": {
                            "code": "GB",
                            "name": "United Kingdom"
                        }
                    },
                    "startTime": null,
                    "plannedEndTime": null,
                    "capabilities": [],
                    "updateUI": false,
                    "break1": null,
                    "break2": null,
                    "aaVehicle": null,
                    "type": {
                        "id": -1,
                        "name": null
                    },
                    "unproductive1": {
                        "startTime": null,
                        "endTime": null,
                        "status": null
                    },
                    "unproductive2": {
                        "startTime": null,
                        "endTime": null,
                        "status": null
                    }
                }
            },
            "glassModelId": -1,
            "vehicle": {
                "registration": null,
                "modelId": -1,
                "colour": null,
                "hasTrailer": false,
                "hasOwner": false,
                "makeId": -1,
                "typeId": -1,
                "model": {
                    "id": -1,
                    "name": null,
                    "weight": -1,
                    "typeId": -1,
                    "makeId": -1,
                    "frontLiftWeight": -1,
                    "rearLiftWeight": -1,
                    "overPrsnWeight": -1,
                    "fuel": null,
                    "transmission": null
                },
                "experianDetails": {
                    "make": null,
                    "model": null,
                    "colour": null,
                    "driveType": null,
                    "height": null,
                    "width": null,
                    "seatNumber": null,
                    "transmission": null,
                    "grossWeight": null,
                    "ukDateFirstRegistered": null,
                    "dateFirstRegistered": null,
                    "bodyStyle": null,
                    "modelVariant": null,
                    "smmtRange": null,
                    "engineNo": null,
                    "co2Emissions": null,
                    "rbCodes": null,
                    "mvrisCode": null,
                    "vin": null,
                    "vinConfirmFlag": null,
                    "engineSize": null,
                    "yrOfManufacture": null,
                    "fuel": null,
                    "wheelPlan": null,
                    "imported": null,
                    "series": null,
                    "abiBrokernetCode": null,
                    "glassModelId": null,
                    "gears": null,
                    "importNonEu": null,
                    "kerbWeight": null,
                    "length": null,
                    "wrongDetailsFlag": false,
                    "flag": true,
                    "makeModel": null,
                    "doorPlan": null,
                    "doorPlanCode": null
                },
                "trailerDetails": {
                    "make": null,
                    "model": null,
                    "colour": null,
                    "type": null,
                    "axles": null,
                    "twinned": null,
                    "hookUp": null,
                    "curSymbol": null,
                    "value": null,
                    "abandonedFlag": null,
                    "notes": null,
                    "diagnosis": null,
                    "fault": null,
                    "yearConstructed": null,
                    "weight": null,
                    "length": null,
                    "height": null,
                    "width": null
                },
                "vehicleOwnerDetails": {
                    "title": null,
                    "firstName": null,
                    "surName": null,
                    "contactNumber": null,
                    "email": null,
                    "address": null,
                    "postCode": null
                },
                "roadworthy": {
                    "registrationNumber": null,
                    "yearOfManufacture": null,
                    "monthOfFirstRegistration": null,
                    "motExpiryDate": null,
                    "motStatus": null,
                    "taxDueDate": null,
                    "taxStatus": null
                },
                "twinnedWheels": null
            },
            "recovery": {
                "destination": {
                    "text": null,
                    "area": null,
                    "remarks": null,
                    "coordinates": {
                        "latitude": null,
                        "longitude": null
                    },
                    "remarksToAppend": "",
                    "lane": null,
                    "slipRoad": null,
                    "dot": "",
                    "lastJunctionPassed": null,
                    "travellingTo": null,
                    "isHardShoulder": false,
                    "isActiveLane": false,
                    "country": {
                        "code": "GB",
                        "name": "United Kingdom"
                    }
                },
                "fault": {
                    "capabilities": [],
                    "outcome": {
                        "completionCode": null,
                        "componentCode": null,
                        "completionFaultCode": null,
                        "component": {
                            "code": null,
                            "name": null
                        },
                        "completionFault": {
                            "code": null,
                            "name": null
                        },
                        "completion": {
                            "code": null,
                            "name": null
                        }
                    },
                    "repairMinutes": -1,
                    "repairProbability": -1,
                    "id": -1,
                    "name": null,
                    "code": {
                        "code": null,
                        "name": null
                    },
                    "categoryCode": null,
                    "additionalInfo": null,
                    "amFaultChk": false,
                    "motorwayChk": "",
                    "rtaChk": false,
                    "userPrompt": "",
                    "mwayRecovVehFaultId": 0,
                    "nextQuestionId": -1,
                    "nextQuestionInstance": -1,
                    "repairId": -1,
                    "initialFault": {
                        "id": -1,
                        "name": null
                    },
                    "diagnosticsQAList": [],
                    "quickCode": null
                },
                "follow": false,
                "motorway": false,
                "passengerRun": false,
                "relay": false,
                "distance": -1,
                "adults": null,
                "children": 0,
                "dogs": 0,
                "unaccompaniedAfterLoading": false,
                "keysLocation": null,
                "forceRecyFault": true,
                "reasonForce": true,
                "reasonExpert": false,
                "reasonRoadOps": false,
                "email": null,
                "franchiseEmail": null,
                "destResourceId": null,
                "isTowing": false
            },
            "epyxGarageReference": null,
            "supJobTypeCode": null,
            "membImpressed": false,
            "canReattend": false,
            "canReopen": false,
            "split": false,
            "uiStatus": {
                "appointment": false,
                "contact": false,
                "fault": false,
                "location": false,
                "relay": false,
                "supplier": false,
                "vehicle": false
            },
            "parentTask": null,
            "indicators": {
                "authorised": false,
                "changeOfNote": false,
                "dangerousLocation": false,
                "dissatisfied": false,
                "edi": false,
                "ediRefused": false,
                "ediNightManaged": false,
                "emailTracking": false,
                "exclusiveRes": false,
                "fax": false,
                "hasCallInfo": false,
                "headLocked": false,
                "homeStart": false,
                "incidentManaged": false,
                "locked": false,
                "manualDispatch": false,
                "motorway": false,
                "needsAIProcess": false,
                "noFreeService": false,
                "primaryLiveryPreferred": false,
                "remoteDiagnostics": false,
                "relayPlusAbuse": false,
                "relayPlusSource": false,
                "safetyAdviceGiven": false,
                "sdmLocked": false,
                "selfService": false,
                "smsTracking": false,
                "standby": false,
                "suppressIM": false,
                "taskTracking": false,
                "useSlotWindow": false,
                "qualified": false,
                "deliveryTimes": false,
                "inWindowOnly": false,
                "tel": false,
                "memberPayForUse": false,
                "payment": false
            },
            "miscFields": {
                "relayPlus": null,
                "seat": null,
                "vauxhall": null,
                "originatingPatrol": null,
                "pdqNo": null,
                "holdingResource": null,
                "holdingResourceRequired": null,
                "garageRefNo": null,
                "vbm": null,
                "cardType": null,
                "membershipType": null,
                "entitlements": null,
                "inceptionDate": null,
                "sif": null,
                "serviceDealerName": null,
                "serviceDealerLocation": null,
                "serviceDealerDateOfService": null,
                "ANK": null,
                "ANKactionTaken": null,
                "bcallValidationErrors": [],
                "reattend": false
            },
            "priorities": [],
            "taskType": {
                "code": "BRK",
                "name": "Breakdown task"
            },
            "isDirty": false,
            "systemStatus": null,
            "supplierJobType": {
                "code": null,
                "name": null
            },
            "deferQualification": false,
            "coveringSiteId": null,
            "contractValidation": {
                "roadworthiness": {
                    "override": "UNABLE_TO_RETRIEVE_DETAILS",
                    "passed": false,
                    "motStatus": null,
                    "taxStatus": null,
                    "noData": true
                },
                "outcome": {
                    "serviceRefused": null,
                    "pfuRequired": null,
                    "pfuQuoted": 0,
                    "pfuQuoteAccepted": null,
                    "pfuQuotedMileage": 0,
                    "override": null,
                    "coverUpgraded": null,
                    "paymentReference": null
                },
                "coolingOff": {
                    "passed": null,
                    "product": null,
                    "startDate": null,
                    "surchargeAccepted": null,
                    "coolingOffOverride": null
                },
                "excessiveUse": {
                    "passed": true,
                    "callouts": null,
                    "surchargeAccepted": null,
                    "calloutOverride": null
                },
                "relay": {
                    "surchargeAccepted": null,
                    "relayOverride": null
                },
                "secRecovery": {
                    "passed": true,
                    "surchargeAccepted": null,
                    "override": null,
                    "assocTaskId": -1
                },
                "homestart": {
                    "surchargeAccepted": null,
                    "homestartOverride": null
                },
                "repeatFault": {
                    "passed": true,
                    "surchargeAccepted": null,
                    "override": null,
                    "assocTaskId": -1,
                    "daysChecked": -1,
                    "faultCode": {
                        "code": null,
                        "name": null
                    }
                },
                "cuv": {
                    "passed": true,
                    "surchargeAccepted": null,
                    "override": null,
                    "assocTaskId": -1
                },
                "vrn": {
                    "passed": false,
                    "override": null,
                    "noVRN": false
                },
                "lastUpdatedBy": "C"
            },
            "eventHooks": null,
            "buzbyTelfix": null,
            "eurohelp": {
                "recovery": {
                    "distance": -1
                }
            },
            "demandDeflection": {
                "calls": null,
                "maxCallouts": null,
                "relayOverride": null,
                "relaySurchargeAccepted": null,
                "homestartOverride": null,
                "homestartSurchargeAccepted": null,
                "localRecoveryPassed": null,
                "repeatFaultPassed": null,
                "isCUV": null
            },
            "additionalExperianDetails": {
                "seatNumber": null,
                "lwb": null
            }
        },
        "indicators": {
            "authorised": true,
            "changeOfNote": false,
            "dangerousLocation": false,
            "dissatisfied": false,
            "edi": false,
            "ediRefused": false,
            "ediNightManaged": false,
            "emailTracking": false,
            "exclusiveRes": false,
            "fax": false,
            "hasCallInfo": true,
            "headLocked": false,
            "homeStart": false,
            "incidentManaged": false,
            "locked": false,
            "manualDispatch": false,
            "motorway": false,
            "needsAIProcess": false,
            "noFreeService": false,
            "primaryLiveryPreferred": false,
            "remoteDiagnostics": false,
            "relayPlusAbuse": false,
            "relayPlusSource": false,
            "safetyAdviceGiven": false,
            "sdmLocked": false,
            "selfService": false,
            "smsTracking": false,
            "standby": false,
            "suppressIM": false,
            "taskTracking": true,
            "useSlotWindow": false,
            "qualified": false,
            "deliveryTimes": false,
            "inWindowOnly": false,
            "tel": false,
            "memberPayForUse": false,
            "payment": false
        },
        "miscFields": {
            "relayPlus": {
                "licenceOK": false,
                "occupants": null,
                "gbType": null,
                "category": null,
                "status": null,
                "payerType": null,
                "crdCard": false,
                "authId": null,
                "authCode": null,
                "authAmount": null,
                "daysHire": null,
                "coverCode": null,
                "deliveryAmount": null,
                "outOfHours": null,
                "exceptAmount": null,
                "exceptDesc": null,
                "rentalNum": null,
                "caseNum": null,
                "reservationNum": null,
                "vehType": null,
                "vehMake": null,
                "vehModel": null,
                "deliveryReq": null,
                "upgradeAgreed": null,
                "hireGroup": null,
                "prePaid": null,
                "claim": null,
                "supplierAcct": null,
                "supplierName": null,
                "supplierAddress": [],
                "supplierPostCodeOut": null,
                "supplierPostCodeIn": null,
                "patSite": null,
                "patName": null,
                "patCallsign": null,
                "patMdtNo": null,
                "patTelNo": null,
                "patResId": null,
                "oneLinkRef": null
            },
            "seat": null,
            "vauxhall": null,
            "originatingPatrol": {
                "site": null,
                "name": null,
                "callsign": null,
                "mdtNo": null,
                "telNo": null,
                "resId": null
            },
            "pdqNo": null,
            "holdingResource": null,
            "holdingResourceRequired": null,
            "garageRefNo": null,
            "vbm": null,
            "cardType": null,
            "membershipType": null,
            "entitlements": "Fleet Europe:Home Start:Relay:Relay Plus:ROADSIDE",
            "inceptionDate": null,
            "serviceDealerName": null,
            "serviceDealerLocation": null,
            "serviceDealerDateOfService": null,
            "ANK": null,
            "ANKactionTaken": null,
            "bcallValidationErrors": [],
            "reattend": false
        },
        "priorities": [],
        "taskType": {
            "code": "BRK",
            "name": "Breakdown task"
        },
        "isDirty": false,
        "systemStatus": "UNAC",
        "supplierJobType": {
            "code": null,
            "name": null
        },
        "deferQualification": false,
        "coveringSiteId": null,
        "contractValidation": {
            "roadworthiness": {
                "override": null,
                "passed": true,
                "motStatus": 1,
                "taxStatus": 1,
                "noData": false
            },
            "outcome": {
                "serviceRefused": null,
                "pfuRequired": null,
                "pfuQuoted": 0,
                "pfuQuoteAccepted": null,
                "pfuQuotedMileage": 0,
                "override": null,
                "coverUpgraded": null,
                "paymentReference": null
            },
            "coolingOff": {
                "passed": null,
                "product": null,
                "startDate": null,
                "surchargeAccepted": null,
                "coolingOffOverride": null
            },
            "excessiveUse": {
                "passed": true,
                "callouts": -1,
                "surchargeAccepted": null,
                "calloutOverride": null
            },
            "relay": {
                "surchargeAccepted": null,
                "relayOverride": true
            },
            "secRecovery": {
                "passed": true,
                "surchargeAccepted": null,
                "override": null,
                "assocTaskId": -1
            },
            "homestart": {
                "surchargeAccepted": null,
                "homestartOverride": true
            },
            "repeatFault": {
                "passed": true,
                "surchargeAccepted": null,
                "override": null,
                "assocTaskId": -1,
                "daysChecked": 10,
                "faultCode": {
                    "code": "FB",
                    "name": "FLAT BATTERY"
                }
            },
            "cuv": {
                "passed": true,
                "surchargeAccepted": null,
                "override": null,
                "assocTaskId": -1
            },
            "vrn": {
                "passed": true,
                "override": null,
                "noVRN": false
            },
            "lastUpdatedBy": "C"
        },
        "eventHooks": {
            "roam": {
                "active": false
            },
            "sms": {
                "active": true
            }
        },
        "buzbyTelfix": null,
        "eurohelp": {
            "recovery": {}
        },
        "demandDeflection": {
            "calls": -1,
            "maxCallouts": 1000,
            "relayOverride": true,
            "relaySurchargeAccepted": null,
            "homestartOverride": true,
            "homestartSurchargeAccepted": null,
            "localRecoveryPassed": null,
            "repeatFaultPassed": true,
            "cuvPassed": true,
            "isCUV": null
        },
        "additionalExperianDetails": {
            "seatNumber": null,
            "lwb": null
        },
        "hotel": {
            "hotelName": "1213",
            "reservationId": "234234344",
            "checkInDate": "2025-01-30T14:46:29.906Z",
            "checkOutDate": 1738252410748,
            "hotelLocation": {
                "text": "Holiday Inn Edinburgh, an IHG Hotel, 132 Corstorphine Road",
                "area": "Edinburgh, Corstorphine, Scotland, EH12 6UA",
                "remarks": null,
                "coordinates": {
                    "latitude": 55.9428579,
                    "longitude": -3.2672782
                },
                "remarksToAppend": "",
                "lane": null,
                "slipRoad": null,
                "dot": null,
                "lastJunctionPassed": null,
                "travellingTo": null,
                "isHardShoulder": false,
                "isActiveLane": false,
                "country": {
                    "code": "GB",
                    "name": "United Kingdom"
                }
            },
            "hotelAddress": {
                "addressLines": [],
                "postcode": null,
                "houseNoName": null
            },
            "noOfRooms": 1,
            "phone": {
                "extension": null,
                "phoneNumber": "123333",
                "type": null
            },
            "price": {
                "currency": "GBP",
                "value": 123
            },
            "noOfAdults": 1,
            "noOfChildren": 0,
            "payAndClaim": false,
            "message": null,
            "totalnights": null,
            "completeAddress": null,
            "noOfOccupants": 1
        },
        "transport": {
            "dropOffLocation": {
                "text": null,
                "area": null,
                "remarks": null,
                "coordinates": {
                    "latitude": null,
                    "longitude": null
                },
                "remarksToAppend": "",
                "lane": null,
                "slipRoad": null,
                "dot": "",
                "lastJunctionPassed": null,
                "travellingTo": null,
                "isHardShoulder": false,
                "isActiveLane": false,
                "country": {
                    "code": "GB",
                    "name": "United Kingdom"
                }
            },
            "pickUpLocation": {
                "text": null,
                "area": null,
                "remarks": null,
                "coordinates": {
                    "latitude": null,
                    "longitude": null
                },
                "remarksToAppend": "",
                "lane": null,
                "slipRoad": null,
                "dot": "",
                "lastJunctionPassed": null,
                "travellingTo": null,
                "isHardShoulder": false,
                "isActiveLane": false,
                "country": {
                    "code": "GB",
                    "name": "United Kingdom"
                }
            },
            "dropOffAddress": {
                "addressLines": [],
                "postcode": null,
                "houseNoName": null
            },
            "pickUpAddress": {
                "addressLines": [],
                "postcode": null,
                "houseNoName": null
            },
            "type": null,
            "price": {
                "currency": null,
                "value": null
            },
            "noOfAdults": 0,
            "noOfChildren": 0,
            "operatorName": null,
            "reason": null,
            "phoneNumber": null,
            "message": null,
            "arrivalTime": null,
            "startDate": null,
            "class": null,
            "reservationId": null,
            "payAndClaim": false,
            "numOfPassengers": 0
        }
    }
}
