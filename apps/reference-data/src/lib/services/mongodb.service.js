const { MongoClient } = require('mongodb');
const svc = {};
svc.connectDB = connectDB.bind(svc);
svc.getCollection = getCollection.bind(svc);
svc.getCollectionData = getCollectionData.bind(svc);
svc.storeCollectionData = storeCollectionData.bind(svc);
svc.findFaultData = findFaultData.bind(svc);
svc.updateFaultData = updateFaultData.bind(svc);
module.exports = svc;
let client = null;
let logger = require('winston');
async function connectDB({ mongoUrl, dbName }) {
    try {
        mongoUrl = mongoUrl || process.env.mongodbUrl || 'mongodb+srv://aah2intAtlas:<EMAIL>';
        dbName = process.env.sysdbName || 'system-config';

        client =
            client ||
            (await MongoClient.connect(mongoUrl, {
                useNewUrlParser: true
            }));
        return client.db(dbName);
    } catch (err) {
        logger.error(`Mongodb client connection failed with error : ${err}`);
    }
}

async function getCollection({ mongoUrl, dbName, collectionName }) {
    const db = await connectDB({ mongoUrl, dbName });
    return db.collection(collectionName);
}

async function findFaultData({ collectionName, data }) {
    try {
        const collection = await getCollection({ collectionName });
        return collection.find({ 'data.code': data.code }).toArray();
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}

async function updateFaultData({ collectionName, data }) {
    try {
        const collection = await getCollection({ collectionName });
        return collection.updateOne({ 'data.code': data.code }, { $set: { 'data.custGroupCodes': data.custGroupCodes } });
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}

async function getCollectionData({ collectionName }) {
    try {
        const collection = await getCollection({ collectionName });
        return collection.find({}).toArray();
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}

async function storeCollectionData({ collectionName, data }) {
    try {
        const collection = await getCollection({ collectionName });
        return await collection.insertOne(data);
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}
