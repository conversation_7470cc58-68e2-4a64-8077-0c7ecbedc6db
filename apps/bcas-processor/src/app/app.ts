import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>s, CUVStatus } from '@aa/data-models/common';
import { BcasPayload, DataStoreProviderType } from '@aa/data-store';
import { Exception } from '@aa/exception';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { QueueEventHandler, ServiceBusReceiver, ServiceBusReceiverConfig, ServiceBusSender, ServiceBusSenderConfig } from '@aa/queue';
import { BackendEnvironment } from '@aa/utils';

const appName = 'bcas-processor';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.BCAS_PROCESSOR;
    protected taskCompletionSender: ServiceBusSender<CUVEvents.UPDATE_DOCUMENT, CUVData>;
    protected taskUpdateReceiver: ServiceBusReceiver<CUVEvents.TASK_UPDATE, CUVData, void>;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [
                //   DataStoreProviderType.REDIS,
                DataStoreProviderType.MONGODB,
                DataStoreProviderType.BCAS
                //    DataStoreProviderType.ORACLE,
            ]
        });

        const sbqBaseConfigSender: Omit<ServiceBusSenderConfig, 'queueName'> = {
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            connectionString: environment.queue.legacySBQConnectionString
        };

        const sbqBaseConfigReceiver: Omit<ServiceBusReceiverConfig, 'queueName'> = {
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            connectionString: environment.queue.SBQConnectionString,
            hasDeadletter: true
        };

        //todo: set proper config
        // from cuv-processor
        this.taskUpdateReceiver = new ServiceBusReceiver({
            ...sbqBaseConfigReceiver,
            queueName: 'bcas-queue'
        });
        this.taskUpdateReceiver.on(CUVEvents.TASK_UPDATE, this.onTaskUpdateReceived);

        // to aa-edocs-service
        this.taskCompletionSender = new ServiceBusSender({
            ...sbqBaseConfigSender,
            queueName: 'document-upload'
        });
        //this is for dev test only and will be removed
        this.server.post('/update-policy', async (req, res) => {
            try {
                const bcasProvider = this.dataStore.getProvider(DataStoreProviderType.BCAS);
                const result = await bcasProvider.updatePolicy(req.body);
                res.send(result);
            } catch (error) {
                res.send({ error });
            }
        });
    }
    /*
    bcas-processor
        - listens on bcas-queue svcbus: onTaskUpdateReceived from cuv-processor
            - calls bcasProvider.updatePolicy
            - publishes to the document-upload svcbus UPDATE_DOCUMENT event downstream to aa-edocs-service


*/

    protected onTaskUpdateReceived: QueueEventHandler<CUVEvents.TASK_UPDATE, CUVData, void> = async (context) => {
        try {
            const bcasProvider = this.dataStore.getProvider(DataStoreProviderType.BCAS);

            const {
                entry: { data }
            } = context;
            this.logger.info({
                sourceName: this.name,
                message: 'On complete event received',
                data: { data }
            });
            const cuvStatus = data.status === CUVStatus.REVOKED ? false : true;
            const payload: BcasPayload = {
                membershipId: data.membership,
                vreg: data.vrn,
                status: cuvStatus,
                cardType: data.status
            };

            this.logger.info({
                sourceName: this.name,
                message: 'Bcas updatePolicy payload',
                data: { payload }
            });
            const result = await bcasProvider.updatePolicy(payload);
            this.logger.info({
                sourceName: this.name,
                message: 'BCAS updatePolicy result received',
                data: { result }
            });

            //removed documnet update code as bcas service is returining true not the document id
        } catch (error) {
            throw new Exception({
                message: 'Error while receiveng event',
                data: { context },
                error
            });
        }
    };
}
