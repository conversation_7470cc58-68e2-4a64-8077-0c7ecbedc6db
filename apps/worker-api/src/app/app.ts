import { DataStoreProviderType } from '@aa/data-store';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';
import { readModuleAsString } from './read-module';

const appName = 'worker-api';

/**
 * The App class extends the Microservice class and sets up routes for worker communications and notifications.
 */
export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.WORKER_API;

    /**
     * Constructs an instance of the App class.
     * @param {BackendEnvironment} environment - The backend environment configuration.
     */
    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.MONGODB, DataStoreProviderType.REDIS]
        });

        this.server.get('/worker/comms', this.getCommsWorker, {
            protect: false
        });
        this.server.get('/worker/notifications', this.getNotificationsWorker, {
            protect: false
        });
    }

    /**
     * Retrieve worker responsible for communications.
     * @param {Request<unknown, unknown, unknown>} req - The request object.
     * @param {Response<unknown>} res - The response object.
     * @return {Promise<void>} - A promise that resolves when the worker script is returned.
     */
    protected getCommsWorker = async (req: Request<unknown, unknown, unknown>, res: Response<unknown>): Promise<void> => {
        try {
            // Return JS file for worker + headers to allow access to workers on any scope
            const content = await readModuleAsString('@aa/workers/comms-worker');
            const headers = {
                'service-worker-allowed': '/',
                'service-worker': 'script',
                'content-type': 'text/javascript'
            };
            return getResponse(res, ServerResponseCode.OK, content, headers);
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                message: 'Failed while returning web worker script',
                data: { error }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, error);
        }
    };

    /**
     * Retrieve worker responsible for notifications.
     * @param {Request<unknown, unknown, unknown>} req - The request object.
     * @param {Response<unknown>} res - The response object.
     * @return {Promise<void>} - A promise that resolves when the worker script is returned.
     */
    protected getNotificationsWorker = async (req: Request<unknown, unknown, unknown>, res: Response<unknown>): Promise<void> => {
        try {
            // Return JS file for worker + headers to allow access to workers on any scope
            const content = await readModuleAsString('@aa/workers/notification-worker');
            const headers = {
                'service-worker-allowed': '/',
                'service-worker': 'script',
                'content-type': 'text/javascript'
            };
            return getResponse(res, ServerResponseCode.OK, content, headers);
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                message: 'Failed while returning web worker script',
                data: { error }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, error);
        }
    };
}
