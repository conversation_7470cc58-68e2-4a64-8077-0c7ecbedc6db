'use strict';

const aaOracle = require('@aa/oracle-utilities'),
    oracledb = require('oracledb');

const user = process.env.cshUser || 'vanda',
    password = process.env.cshPassword || 'va.77.av',
    connectStrings = process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : ['sun54:1521/VACSH'],
    appName = 'mobility-redis-cleanup';

oracledb.fetchAsString = [oracledb.CLOB];

module.exports = {
    init: () => {
        return aaOracle.init({ user, password, connectStrings, appName });
    },
    connect: () => {
        return aaOracle.connect();
    },
    release: (dbConn) => {
        aaOracle.release(dbConn);
    }
};
