import { ThemeProvider } from '@aa/ui/core/theme-provider';
import { StrictMode } from 'react';
import * as ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';

import App from './app/app';

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
    <StrictMode>
        <BrowserRouter basename="/eurohelp">
            <ThemeProvider defaultTheme="light">
                <App />
            </ThemeProvider>
        </BrowserRouter>
    </StrictMode>
);
