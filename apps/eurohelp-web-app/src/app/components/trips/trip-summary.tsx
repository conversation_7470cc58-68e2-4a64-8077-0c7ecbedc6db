import { Entitlement, Trip, WithDataId } from '@aa/data-models/common';
import { BreakdownTask } from '@aa/data-models/entities/breakdown-task';
import { BreakdownTaskPreview } from '@aa/data-models/entities/task-preview';
import { Card, CardContent, CardDescription } from '@aa/ui/core/card';
import { Separator } from '@aa/ui/core/separator';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useEntitlementClient } from '@aa/ui/hooks/use-entitlement-client';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { commsChannels } from '../action-display/link-action';
import { TooltipButton } from '../shared/tooltip-button';
import { TripCallButton } from './trip-call-button';
import { TripCopyToClipboardButton } from './trip-copy-to-clipboard-button';
import { format } from 'date-fns/format';

interface TripSummaryProps {
    tripCode: string;
    tripSummaryData: WithDataId<Trip>;
    taskSummaryData: BreakdownTaskPreview;
}

export const TripSummary = ({ tripCode, tripSummaryData, taskSummaryData }: TripSummaryProps) => {
    const [isLoading, setIsLoading] = useState(false);
    const [entitlementClient] = useEntitlementClient();

    useEffect(() => {
        setIsLoading(true);
        fetch(tripCode).catch();
        setIsLoading(false);
    }, [tripCode]);

    useEffect(() => {
        if (tripSummaryData.partyContactCode) {
            fetchContact(tripSummaryData.partyContactCode).catch();
        }
    }, [tripSummaryData.partyContactCode]);

    const [tripClient] = useTripClient();
    const [data, fetch, isPending, error] = useCallback(tripClient.getOverdueActions);
    const { isInitialPending } = useInitialPending(isPending);
    const expiredCount = data?.overdueCount.toString() || '0';

    const [contactData, fetchContact, isPendingContact, errorContact] = useCallback(tripClient.getTripContactByCode);
    useInitialLoad(async () => fetchContact(tripSummaryData.partyContactCode || '').catch());
    const contactSummaryData = contactData;

    const [policyData, fetchPolicyData, isPendingPolicyData, policyDataError] = useCallback(entitlementClient.getProductsByCustomerGroupCode);
    const productSummaryData = policyData ? policyData : ({} as Entitlement);

    if (isLoading) {
        return <p>Loading...</p>;
    }

    if (!tripSummaryData) {
        return <p>No data found</p>;
    }

    const isEmptyObject = (obj: WithDataId<BreakdownTask>) => {
        return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
    };

    const commsChannel = commsChannels.find((channel) => {
        return channel.code === tripSummaryData.commsRouteOverride?.route;
    })?.name;

    return (
        <Card className="flex flex-col">
            {/* {taskSummaryData && taskSummaryData?.location?.coordinates?.latitude != null && taskSummaryData?.location?.coordinates?.longitude != null ? (
                <div className="btn w-full h-24 object-cover">
                    <MapWithMarker
                        mapId={'incidentTaskMap'}
                        position={taskSummaryData.location.coordinates}
                    />
                </div>
            ) : null} */}

            <Separator />
            <CardContent className="flex p-0 w-full">
                <Separator
                    className="h-30"
                    orientation="vertical"
                />

                <div className=" flex-col w-1/2">
                    <TripCopyToClipboardButton
                        valueToDisplay={'Status'}
                        value={tripSummaryData.status}
                    />
                    <Separator />
                    <TripCopyToClipboardButton
                        valueToDisplay={'Created'}
                        value={tripSummaryData.created instanceof Date ? tripSummaryData.created.toISOString() : format(new Date(tripSummaryData.created), 'dd-MM-yyyy, HH:mm aaaa')}
                    />
                    <Separator />
                    <TripCopyToClipboardButton
                        valueToDisplay={'Trip Ref'}
                        value={tripSummaryData.code}
                    />
                    <Separator />

                    <TripCopyToClipboardButton
                        valueToDisplay={'Overdue Actions'}
                        value={expiredCount}
                    />
                    <Separator />
                    <TripCopyToClipboardButton
                        valueToDisplay={'Policy'}
                        value={tripSummaryData.productCode || 'Not given'}
                    />
                    <Separator />
                    {/* <TripCopyToClipboardButton
                        valueToDisplay={'Policy number:'}
                        value={productSummaryData?.policy?.policyNumber || 'Not Available'}
                    />
                    <Separator /> */}
                    <TripCopyToClipboardButton
                        valueToDisplay={'External ref:'}
                        value={tripSummaryData.externalRef || 'Not given'}
                    />
                    <Separator />
                </div>

                <Separator
                    className="h-30"
                    orientation="vertical"
                />
                <div className=" flex-col w-1/2">
                    {tripSummaryData && tripSummaryData.vrn && (
                        <div>
                            <TripCopyToClipboardButton
                                valueToDisplay={'VRN'}
                                value={tripSummaryData.vrn}
                            />
                            <Separator />
                        </div>
                    )}
                    <TripCopyToClipboardButton
                        valueToDisplay={'Contact Number'}
                        value={contactSummaryData?.telephone ? contactSummaryData.telephone : 'Not given'}
                    />
                    <Separator />
                    {/* <TripCopyToClipboardButton
                        valueToDisplay={'Payment Guarantee'}
                        value={tripSummaryData.paymentGuarantee || 'Not given'}
                    /> */}
                    <Separator />
                    {tripSummaryData && tripSummaryData.partyComposition && (
                        <div>
                            <div className="p-2">
                                <CardDescription className="flex text-xs items-center">
                                    Party: {tripSummaryData.partyComposition.adultCount > 0 ? `${tripSummaryData.partyComposition.adultCount} adults,` : '0 adults'}{' '}
                                    {tripSummaryData.partyComposition.childrenCount > 0 ? `${tripSummaryData.partyComposition.childrenCount} children` : ''}
                                </CardDescription>
                            </div>
                            <Separator />
                        </div>
                    )}
                    {tripSummaryData.partyComposition && (
                        <div>
                            <div className="p-2">
                                <CardDescription className="flex text-xs items-center">
                                    Pets: {tripSummaryData.partyComposition.petCount}
                                    <TooltipButton
                                        valueToDisplay={'Additional details'}
                                        hoverValue={tripSummaryData.partyComposition.petNote}
                                    />
                                </CardDescription>
                            </div>
                            <Separator />
                        </div>
                    )}
                    <TripCopyToClipboardButton
                        valueToDisplay={'Customer Group'}
                        value={tripSummaryData.customerGroup || 'Not given'}
                    />
                    <Separator />
                    {tripSummaryData.commsRouteOverride && (
                        <div>
                            <div className="p-2">
                                <CardDescription className="flex text-xs items-center">
                                    Communication channel: <strong className="pl-1">{commsChannel}</strong>
                                </CardDescription>
                            </div>
                            <Separator />
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
};
