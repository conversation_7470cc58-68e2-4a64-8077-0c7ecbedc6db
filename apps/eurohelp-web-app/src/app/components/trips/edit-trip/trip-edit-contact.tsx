import { BusinessPartnerContact, ContactType, ContactTypeLabels, AddressData, LatLong, Trip, TripBusinessPartnerCategory, TripContact, WithDataId } from '@aa/data-models/common';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@aa/ui/core/form';
import { Input } from '@aa/ui/core/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@aa/ui/core/select';
import { Separator } from '@aa/ui/core/separator';
import { Textarea } from '@aa/ui/core/textarea';
import * as React from 'react';
import { useState } from 'react';
import { EditAddress } from '../../shared/edit-address';
import { CardDescription, CardTitle } from '@aa/ui/core/card';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { categories } from './trip-edit-contact-column-defination';

interface TripEditContactProps {
    tripData?: WithDataId<Trip>;
    contactData?: TripContact | BusinessPartnerContact;
    mode: string;
}

export const TripEditContact = ({ tripData, contactData, mode }: TripEditContactProps) => {
    const [formValues, setFormValues] = useState({ tripCode: tripData?.code } as TripContact | BusinessPartnerContact | any);
    const [isSendButtonDisabled, setIsSendButtonDisabled] = useState(false);
    const [tripClient] = useTripClient();

    const [, createContactHandler] = useCallback(tripClient.createTripContact);
    const [, editContactHandler] = useCallback(tripClient.editTripContact);

    const emptyAdressValue = {
        addressLines: ['', '', ''],
        postcode: '',
        houseNoName: ''
    };

    const contactTypes = mode === 'CONTACT' ? [ContactType.BUSINESS_PARTNER] : [ContactType.BUSINESS, ContactType.PERSON];

    const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        setIsSendButtonDisabled(true);
        if (['TRIPCONTACT', 'CONTACT'].includes(mode)) {
            const { ...updatedFormValuesWithId } = formValues as TripContact | BusinessPartnerContact;
            await createContactHandler(updatedFormValuesWithId);
        }

        if (mode === 'EDIT') {
            const { _id, ...updatedFormValuesWithoutId } = formValues as WithDataId<TripContact | BusinessPartnerContact>;
            if (!contactData?.code) return;
            await editContactHandler(contactData.code, updatedFormValuesWithoutId);
        }
        setIsSendButtonDisabled(false);
    };

    const handleAddressUpdate = (updatedData: AddressData) => {
        setFormValues({
            ...formValues,
            address: updatedData
        });
    };

    const handleFormChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setFormValues({
            ...formValues,
            [event.target.name]: event.target.value
        });
    };

    const handleLocationChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setFormValues({
            ...formValues,
            location: {
                ...formValues.location,
                [event.target.name]: parseFloat(event.target.value)
            } as LatLong
        });
    };

    const handleContactTypeChange = (type: any) => {
        setFormValues({
            ...formValues,
            type
        });
    };

    const handleCategoryChange = (category: TripBusinessPartnerCategory) => {
        setFormValues({
            ...formValues,
            categories: [category]
        });
    };

    return (
        <Form onSubmit={onSubmit}>
            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormLabel
                    className={'text-xs'}
                    htmlFor="type"
                >
                    Contact type
                </FormLabel>
                <FormField name="type">
                    <FormControl>
                        <Select
                            value={formValues.type}
                            onValueChange={handleContactTypeChange}
                        >
                            <SelectTrigger className="h-8 w-[400px]">
                                <SelectValue placeholder={formValues.type} />
                            </SelectTrigger>
                            <SelectContent side="bottom">
                                {contactTypes.map((cType) => (
                                    <SelectItem
                                        key={cType}
                                        value={`${cType}`}
                                    >
                                        {ContactTypeLabels[cType]}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </FormControl>
                </FormField>
            </FormItem>
            {formValues.type === ContactType.BUSINESS_PARTNER && (
                <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                    <FormLabel
                        className={'text-xs'}
                        htmlFor="type"
                    >
                        Category
                    </FormLabel>
                    <FormField name="type">
                        <FormControl>
                            <Select onValueChange={handleCategoryChange}>
                                <SelectTrigger className="h-8 w-[400px]">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent side="bottom">
                                    {categories.map((category) => (
                                        <SelectItem
                                            key={category.label}
                                            value={`${category.value}`}
                                        >
                                            {category.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </FormControl>
                    </FormField>
                </FormItem>
            )}
            {mode === 'TRIPCONTACT' && (
                <div>
                    <Separator className={'my-4'} />
                    <div className={'flex w-full'}>
                        <div className={'flex flex-col text-xs gap-y-1 w-[40%]'}>
                            <div className={'flex flex-col gap-1'}>
                                <CardTitle className={'text-sm'}>Selected Trip code:</CardTitle>
                                <CardDescription className={'text-sm'}>{tripData?.code}</CardDescription>
                            </div>
                        </div>
                        <Separator
                            orientation={'vertical'}
                            className={'h-35 mx-6'}
                        />
                        <div className={'flex flex-col gap-2'}>
                            <div className={'flex gap-x-2'}>
                                <CardTitle className={'text-sm'}>Product:</CardTitle>
                                <CardDescription className={'text-sm'}>{tripData?.productCode}</CardDescription>
                            </div>
                            <div className={'flex gap-x-2'}>
                                <CardTitle className={'text-sm'}>Customer group:</CardTitle>
                                <CardDescription className={'text-sm'}>{tripData?.customerGroup}</CardDescription>
                            </div>
                        </div>
                    </div>
                    <Separator className={'my-4'} />
                </div>
            )}

            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormLabel
                    className={'text-xs'}
                    htmlFor="name"
                >
                    Name
                </FormLabel>
                <FormField name="name">
                    <FormControl>
                        <Input
                            className="w-[400px]"
                            type="text"
                            placeholder="Customer Name"
                            value={formValues.name}
                            onChange={handleFormChange}
                        />
                    </FormControl>
                </FormField>
            </FormItem>
            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormLabel
                    className={'text-xs'}
                    htmlFor="telephone"
                >
                    Telephone
                </FormLabel>
                <FormField name="telephone">
                    <FormControl>
                        <Input
                            className="w-[400px]"
                            type="text"
                            placeholder="Telephone number"
                            value={formValues.telephone}
                            onChange={handleFormChange}
                        />
                    </FormControl>
                </FormField>
            </FormItem>

            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormLabel
                    className={'text-xs'}
                    htmlFor="email"
                >
                    Email
                </FormLabel>
                <FormField name="email">
                    <FormControl>
                        <Input
                            className="w-[400px]"
                            type="text"
                            placeholder="Email address"
                            value={formValues.email}
                            onChange={handleFormChange}
                        />
                    </FormControl>
                </FormField>
            </FormItem>

            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormLabel
                    className={'text-xs'}
                    htmlFor="latitude"
                >
                    Latitude
                </FormLabel>
                <FormField name="latitude">
                    <FormControl>
                        <Input
                            className="w-[400px]"
                            type="number"
                            value={formValues.location?.latitude}
                            onChange={handleLocationChange}
                        />
                    </FormControl>
                </FormField>
            </FormItem>

            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormLabel
                    className={'text-xs'}
                    htmlFor="longitude"
                >
                    Longitude
                </FormLabel>
                <FormField name="longitude">
                    <FormControl>
                        <Input
                            className="w-[400px]"
                            type="number"
                            value={formValues.location?.longitude}
                            onChange={handleLocationChange}
                        />
                    </FormControl>
                </FormField>
            </FormItem>

            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormLabel
                    className={'text-xs'}
                    htmlFor="note"
                >
                    Note
                </FormLabel>
                <FormField name="note">
                    <FormControl>
                        <Textarea
                            id="note"
                            placeholder="Note"
                            className="min-h-[50px] w-[400px]"
                            value={formValues.note}
                            onChange={handleFormChange}
                        />
                    </FormControl>
                </FormField>
            </FormItem>

            <Separator className={'my-4'} />

            <EditAddress
                data={formValues.address || (emptyAdressValue as AddressData)}
                onUpdate={(updatedAddress) => handleAddressUpdate(updatedAddress)}
                header={''}
                description={''}
                isSendButtonDisabled={isSendButtonDisabled}
            />
        </Form>
    );
};
