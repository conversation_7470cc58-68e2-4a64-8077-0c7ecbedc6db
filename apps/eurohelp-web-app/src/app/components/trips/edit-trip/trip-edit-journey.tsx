import { AddressData, Trip, WithDataId } from '@aa/data-models/common';
import { ScrollArea } from '@aa/ui/core/scroll-area';
import { EditAddress } from '../../shared/edit-address';
import { Card, CardContent } from '@aa/ui/core/card';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';

interface TripEditJourneyProps {
    tripData: WithDataId<Trip>;
    onSuccessEdit: () => void;
}

export const TripEditJourney = ({ tripData, onSuccessEdit }: TripEditJourneyProps) => {
    const [tripClient] = useTripClient();
    const [, updateTripHandler] = useCallback(tripClient.editTrip, { onComplete: onSuccessEdit });

    const handleSubmit = async (updatedAddress: AddressData, isOutwardJourney: boolean) => {
        let updatedTripData;
        if (isOutwardJourney) {
            updatedTripData = {
                ...tripData,
                outwardJourney: {
                    ...tripData.outwardJourney,
                    location: updatedAddress
                }
            };
        } else {
            updatedTripData = {
                ...tripData,
                returnJourney: {
                    ...tripData.returnJourney,
                    location: updatedAddress
                }
            };
        }

        const { _id, ...updatedTripDataWithoutId } = updatedTripData;

        await updateTripHandler(tripData.code, updatedTripDataWithoutId as Trip);
    };

    if (!tripData.outwardJourney && !tripData.returnJourney) {
        return (
            <Card>
                <CardContent className="pt-6">
                    <div className="flow-root">
                        <label className="float-left">Journey record not found</label>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <ScrollArea className={'gap-2 min-h-[400px]'}>
            {tripData.outwardJourney?.location && (
                <EditAddress
                    data={tripData.outwardJourney?.location}
                    onUpdate={(updatedAddress) => handleSubmit(updatedAddress, true)}
                    header={'Outward journey'}
                    description={'Below you can find details for outward journey'}
                />
            )}
            {tripData.returnJourney?.location && (
                <div className={'mt-4'}>
                    <EditAddress
                        data={tripData.returnJourney?.location}
                        onUpdate={(updatedAddress) => handleSubmit(updatedAddress, false)}
                        header={'Return journey'}
                        description={'Below you can find details for return journey'}
                    />
                </div>
            )}
        </ScrollArea>
    );
};
