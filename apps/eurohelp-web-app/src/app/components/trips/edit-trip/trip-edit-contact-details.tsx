import { Address, ContactType, ContactTypeLabels, ExtendedPaginationQueryResult, LatLong, PaginationQuery, QueryTripContact, Trip, TripContact, WithDataId } from '@aa/data-models/common';
import { Button } from '@aa/ui/core/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@aa/ui/core/card';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@aa/ui/core/form';
import { Input } from '@aa/ui/core/input';
import { Separator } from '@aa/ui/core/separator';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { EditAddress } from '../../shared/edit-address';
import { Textarea } from '@aa/ui/core/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@aa/ui/core/select';
import { DataTable } from '@aa/ui/hoc/data-table/data-table';
import { DataTableBody } from '@aa/ui/hoc/data-table/data-table-body';
import { DataTableColumnHeader } from '@aa/ui/hoc/data-table/data-table-column-header';
import { DataTablePagination } from '@aa/ui/hoc/data-table/data-table-pagination';
import { DataTableToolbar } from '@aa/ui/hoc/data-table/data-table-toolbar';
import { DataColumnFiltersState } from '@aa/ui/providers/data-table-provider';
import type { ColumnDef } from '@tanstack/react-table';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { useDataColumns } from '@aa/ui/hooks/use-data-columns';
import { LinkContact } from '../../shared/search-and-link-contact/link-contact';
import { tripContactListColumns, textFiltersSort, facetedFiltersSort } from './trip-edit-contact-column-defination';
import { useDebounce } from '@aa/ui/hooks/use-debounce';
interface TripEditContactDetailsProps {
    tripData: WithDataId<Trip>;
}

export const TripEditContactDetails = ({ tripData }: TripEditContactDetailsProps) => {
    const [isEditing, setIsEditing] = useState(false);
    const [isLinking, setIsLinking] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [formValues, setFormValues] = useState({} as TripContact);
    const [disableSendButton, setDisableSendButton] = useState(false);
    const [tripClient] = useTripClient();
    const [type, setType] = useState<Array<ContactType>>([]);
    const [showOnlyTripContacts, setShowOnlyTripContacts] = useState(false);
    const [tripCode, setTripCode] = useState<string>(tripData.code);
    const [name, setName] = useState<string | undefined>();
    const [query, setQuery] = useState<QueryTripContact>({ limit: 10, skip: 0, code: '', tripCode: tripData.code, showOnlyTripContacts, type, name });
    const debouncedQuery = useDebounce({ value: query, delay: 100 });

    const [tripContactData, fetchTripContact, isPendingTripContact, tripContactError] = useCallback(tripClient.getTripContactByCode);
    const { isInitialPending } = useInitialPending(isPendingTripContact);
    useInitialLoad(async () => fetchTripContact(tripData.partyContactCode as string));

    const [contactData, fetchContact, isPending, error] = useCallback(tripClient.getTripContacts);
    useInitialPending(isPending);
    useInitialLoad(async () => fetchContact(query));
    const { results: tripContacts, ...pagination }: ExtendedPaginationQueryResult<TripContact> = (contactData as ExtendedPaginationQueryResult<TripContact>) || {
        results: [],
        more: false,
        totalPages: 0,
        skip: 0,
        limit: 10,
        total: 0
    };
    useEffect(() => {
        fetchContact(query).catch();
    }, [debouncedQuery]);

    useEffect(() => {
        if (tripContactData) {
            setFormValues(tripContactData as TripContact);
        }
    }, [tripContactData]);

    const [, editContactHandler] = useCallback(tripClient.editTripContact);

    const [allContactsQuery, setAllContactsQuery] = useState<QueryTripContact>({ limit: 10, skip: 0, code: '', showOnlyTripContacts, type, name });
    const [allContactsData, fetchAllContacts, isPendingAllContacts, allContactsError] = useCallback(tripClient.getTripContacts);
    useInitialLoad(async () => fetchAllContacts(allContactsQuery));
    const { results: contacts, ...paginationAllContacts }: ExtendedPaginationQueryResult<TripContact> = (allContactsData as ExtendedPaginationQueryResult<TripContact>) || {
        results: [],
        more: false,
        totalPages: 0,
        skip: 0,
        limit: 10,
        total: 0
    };
    const debouncedQueryAllContacts = useDebounce({ value: allContactsQuery, delay: 100 });

    useEffect(() => {
        if (allContactsQuery) {
            fetchAllContacts(allContactsQuery).catch();
        }
    }, [debouncedQueryAllContacts]);

    const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        setDisableSendButton(true);
        const { _id, ...updatedFormValuesWithoutId } = formValues as WithDataId<TripContact>;
        if (tripData.partyContactCode) {
            await editContactHandler(tripData.partyContactCode, updatedFormValuesWithoutId);
            setDisableSendButton(false);
        }
    };

    const handleAddressUpdate = (updatedData: Address) => {
        setFormValues({
            ...formValues,
            address: updatedData
        });
    };

    const handleFormChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setFormValues({
            ...formValues,
            [event.target.name]: event.target.value
        });
    };

    const handleLocationChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setFormValues({
            ...formValues,
            location: {
                ...formValues.location,
                [event.target.name]: parseFloat(event.target.value || '')
            } as LatLong
        });
    };

    const handleContactTypeChange = (type: ContactType.BUSINESS | ContactType.PERSON) => {
        setFormValues({
            ...formValues,
            type
        });
    };

    const onLink = async (row: any) => {
        console.log('Linking contact', row.original);
        const { _id, ...contact } = row.original;
        contact.tripCode = tripData.code;
        setPartyContactCode(row.original.code);
        setIsEditing(false);
        setIsLinking(false);
        await editContactHandler(row.original.code, contact as TripContact);
    };

    // As this is child component, need set partyContactCode inside trip object. Otherwise close child component and re-render
    const setPartyContactCode = (contactCode: string) => {
        tripData.partyContactCode = contactCode;
    };

    //contact table
    const onPaginationChange = (state: PaginationQuery, paginationFor: string) => {
        if (paginationFor === 'ALL_CONTACTS') {
            setAllContactsQuery((prevState) => ({ ...prevState, ...state }));
        } else if (paginationFor === 'TRIP_CONTACTS') {
            setQuery((prevState) => ({ ...prevState, ...state }));
        }
    };

    const onColumnFiltersChange = (state: DataColumnFiltersState<TripContact>, filterFor: string) => {
        let mappedState = state.reduce((result: any, obj: any) => {
            result[obj.id] = obj.value;
            return result;
        }, {});

        if (Object.keys(mappedState).length === 0) {
            mappedState = {
                name,
                type,
                tripCode: filterFor === 'TRIP_CONTACTS' ? tripData.code : '',
                showOnlyTripContacts,
                categories: []
            };
        }

        // TODO: Fix sometime in the future
        // I hate it but, I don't have other idea or time for now
        if (filterFor === 'ALL_CONTACTS') {
            setAllContactsQuery((prevState) => ({
                ...prevState,
                ...mappedState,
                categories: mappedState.categories || [],
                type: mappedState.type || []
            }));
        } else if (filterFor === 'TRIP_CONTACTS') {
            setQuery((prevState) => ({
                ...prevState,
                ...mappedState,
                categories: mappedState.categories || [],
                type: mappedState.type || []
            }));
        }
    };

    const actionColumn: ColumnDef<TripContact> = {
        accessorKey: 'actions',
        header: ({ column }) => (
            <DataTableColumnHeader
                column={column}
                title="Actions"
            />
        ),
        cell: ({ row }) => (
            <div className="w-[80px]">
                <LinkContact
                    row={row}
                    onLink={(row) => onLink(row)}
                />
            </div>
        ),
        enableSorting: false,
        enableHiding: false
    };

    const [columns] = useDataColumns(tripContactListColumns, [actionColumn]);

    if (!tripData.partyContactCode && !isLinking && !isEditing) {
        return (
            <Card>
                <CardContent className="pt-6">
                    <div className="flow-root">
                        <label className="float-left">No contact selected for this trip</label>
                    </div>
                </CardContent>
                <CardContent className={'flex flex-col'}>
                    <CardHeader className={'p-0 gap-2 pb-2'}>
                        <CardTitle className="h-3 text-base">Trip contacts</CardTitle>
                        <CardDescription className="text-xs">Below you can find list of all contacts related to the trip.</CardDescription>
                    </CardHeader>

                    <div className="flex flex-col space-y-4 md:flex">
                        <DataTable
                            data={tripContacts}
                            pagination={pagination}
                            isDataPending={isInitialPending}
                            columns={columns}
                            onPagination={(query) => onPaginationChange(query, 'TRIP_CONTACTS')}
                            onColumnFilters={(state) => onColumnFiltersChange(state, 'TRIP_CONTACTS')}
                        >
                            <DataTableToolbar
                                className="w-[400px]"
                                textFilters={textFiltersSort}
                                facetedFilters={facetedFiltersSort}
                            ></DataTableToolbar>
                            <DataTableBody className="rounded-md border h-[calc(100%-260px)]" />

                            <DataTablePagination />
                        </DataTable>
                    </div>
                </CardContent>
            </Card>
        );
    }
    return (
        <Card>
            <CardContent className={'flex flex-col'}>
                {!isEditing && !isLinking && (
                    <div className={'pt-6'}>
                        <div className={'mb-2'}>
                            <h1>{formValues.name}</h1>
                        </div>
                        <Separator className={'my-2'} />
                        <div className={'flex w-full'}>
                            <div className={'flex flex-col text-xs gap-y-1 w-[40%]'}>
                                <div className={'flex gap-2'}>
                                    <CardTitle className={'text-sm'}>Postcode:</CardTitle>
                                    <CardDescription className={'text-sm'}>{formValues.address?.postcode}</CardDescription>
                                </div>
                                <div className={'flex gap-2'}>
                                    <CardTitle className={'text-sm'}>House no:</CardTitle>
                                    <CardDescription className={'text-sm'}>{formValues.address?.houseNoName}</CardDescription>
                                </div>
                                <div className={'flex flex-col gap-x-2'}>
                                    <CardTitle className={'text-sm'}>Address:</CardTitle>
                                    <CardDescription className={'text-sm'}>{formValues.address?.addressLines ? formValues.address?.addressLines.join() : ''}</CardDescription>
                                </div>
                            </div>
                            <Separator
                                orientation={'vertical'}
                                className={'h-35 mx-6'}
                            />
                            <div className={'flex flex-col gap-2'}>
                                <div className={'flex flex-col gap-x-2'}>
                                    <CardTitle className={'text-sm'}>Telephone:</CardTitle>
                                    <CardDescription className={'text-sm'}>{formValues.telephone}</CardDescription>
                                </div>
                                <div className={'flex flex-col gap-x-2'}>
                                    <CardTitle className={'text-sm'}>Email:</CardTitle>
                                    <CardDescription className={'text-sm'}>{formValues.email}</CardDescription>
                                </div>
                            </div>
                        </div>
                        <Separator className={'my-3'} />
                        <div className={'flex gap-2'}>
                            <Button
                                className={'w-40'}
                                type="submit"
                                variant={'default'}
                                size="default"
                                onClick={() => setIsEditing(true)}
                            >
                                Edit
                            </Button>
                        </div>
                    </div>
                )}
                {isEditing && (
                    <Form onSubmit={onSubmit}>
                        <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                            <FormLabel
                                className={'text-xs'}
                                htmlFor="type"
                            >
                                Contact type
                            </FormLabel>
                            <FormField name="type">
                                <FormControl>
                                    <Select
                                        value={formValues.type}
                                        onValueChange={handleContactTypeChange}
                                    >
                                        <SelectTrigger className="h-8 w-[400px]">
                                            <SelectValue placeholder={formValues.type} />
                                        </SelectTrigger>
                                        <SelectContent side="bottom">
                                            {[ContactType.BUSINESS, ContactType.PERSON].map((cType) => (
                                                <SelectItem
                                                    key={cType}
                                                    value={`${cType}`}
                                                >
                                                    {ContactTypeLabels[cType]}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </FormControl>
                            </FormField>
                        </FormItem>
                        <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                            <FormLabel
                                className={'text-xs'}
                                htmlFor="name"
                            >
                                Name
                            </FormLabel>
                            <FormField name="name">
                                <FormControl>
                                    <Input
                                        className="w-[400px]"
                                        type="text"
                                        required
                                        placeholder="Telephone number"
                                        value={formValues.name}
                                        onChange={handleFormChange}
                                    />
                                </FormControl>
                            </FormField>
                        </FormItem>
                        <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                            <FormLabel
                                className={'text-xs'}
                                htmlFor="telephone"
                            >
                                Telephone
                            </FormLabel>
                            <FormField name="telephone">
                                <FormControl>
                                    <Input
                                        className="w-[400px]"
                                        type="text"
                                        required
                                        placeholder="Telephone number"
                                        value={formValues.telephone}
                                        onChange={handleFormChange}
                                    />
                                </FormControl>
                            </FormField>
                        </FormItem>

                        <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                            <FormLabel
                                className={'text-xs'}
                                htmlFor="email"
                            >
                                Email
                            </FormLabel>
                            <FormField name="email">
                                <FormControl>
                                    <Input
                                        className="w-[400px]"
                                        type="text"
                                        required
                                        placeholder="Email address"
                                        value={formValues.email}
                                        onChange={handleFormChange}
                                    />
                                </FormControl>
                            </FormField>
                        </FormItem>

                        <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                            <FormLabel
                                className={'text-xs'}
                                htmlFor="latitude"
                            >
                                Latitude
                            </FormLabel>
                            <FormField name="latitude">
                                <FormControl>
                                    <Input
                                        className="w-[400px]"
                                        type="text"
                                        value={formValues.location?.latitude}
                                        onChange={handleLocationChange}
                                        required
                                    />
                                </FormControl>
                            </FormField>
                        </FormItem>

                        <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                            <FormLabel
                                className={'text-xs'}
                                htmlFor="longitude"
                            >
                                Longitude
                            </FormLabel>
                            <FormField name="longitude">
                                <FormControl>
                                    <Input
                                        className="w-[400px]"
                                        type="text"
                                        value={formValues.location?.longitude}
                                        onChange={handleLocationChange}
                                        required
                                    />
                                </FormControl>
                            </FormField>
                        </FormItem>

                        <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                            <FormLabel
                                className={'text-xs'}
                                htmlFor="note"
                            >
                                Note
                            </FormLabel>
                            <FormField name="note">
                                <FormControl>
                                    <Textarea
                                        id="note"
                                        placeholder="Note"
                                        className="min-h-[50px] w-[400px]"
                                        value={formValues.note}
                                        onChange={handleFormChange}
                                        required
                                    />
                                </FormControl>
                            </FormField>
                        </FormItem>

                        <Separator className={'my-4'} />

                        <EditAddress
                            data={formValues.address || ({} as Address)}
                            onUpdate={(updatedAddress) => handleAddressUpdate(updatedAddress)}
                            header={''}
                            description={''}
                            isSendButtonDisabled={disableSendButton}
                        />
                    </Form>
                )}
            </CardContent>
            {!isEditing && (
                <div>
                    <CardContent className={'flex flex-col'}>
                        <CardHeader className={'p-0 gap-2 pb-2'}>
                            <CardTitle className="h-3 text-base">Trip contacts</CardTitle>
                            <CardDescription className="text-xs">Below you can find list of all contacts related to the trip.</CardDescription>
                        </CardHeader>

                        <div className="flex flex-col space-y-4 md:flex">
                            <DataTable
                                data={tripContacts}
                                pagination={pagination}
                                isDataPending={isInitialPending}
                                columns={columns}
                                onPagination={(query) => onPaginationChange(query, 'TRIP_CONTACTS')}
                                onColumnFilters={(state) => onColumnFiltersChange(state, 'TRIP_CONTACTS')}
                            >
                                <DataTableToolbar
                                    className="w-[400px]"
                                    textFilters={textFiltersSort}
                                    facetedFilters={facetedFiltersSort}
                                ></DataTableToolbar>
                                <DataTableBody className="rounded-md border h-[calc(100%-260px)]" />

                                <DataTablePagination />
                            </DataTable>
                        </div>
                    </CardContent>
                </div>
            )}
        </Card>
    );
};
