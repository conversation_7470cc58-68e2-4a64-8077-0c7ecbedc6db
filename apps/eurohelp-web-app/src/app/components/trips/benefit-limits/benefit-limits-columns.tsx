import { BenefitSummary } from '@aa/data-models/common';
import { ColumnDef } from '@tanstack/react-table';
import { formatCurrency } from '../../../utils/layout/utils/utils';

export const benefitLimitsColumns: ColumnDef<BenefitSummary>[] = [
    {
        accessorKey: 'name',
        header: 'Benefit'
    },
    {
        accessorKey: 'forecast.amount',
        header: 'Forecast',
        cell: ({ row }) => {
            const benefit = row.original;

            return <span>{formatCurrency(benefit?.forecastNative?.amount, benefit?.forecastNative?.currency)}</span>;
        }
    },
    {
        accessorKey: 'amount',
        header: 'Limit',
        cell: ({ row }) => {
            const benefit = row.original;

            return <span>{formatCurrency(benefit.amount, benefit.currency)}</span>;
        }
    },
    {
        accessorKey: 'overspend.amount',
        header: 'Overspend',
        cell: ({ row }) => {
            const benefit = row.original;

            if (benefit?.forecastNative?.amount > benefit.amount) {
                return <span className="text-red-600">{formatCurrency(benefit?.forecastNative?.amount - benefit.amount, benefit?.forecastNative?.currency)}</span>;
            } else if (benefit.amount > benefit?.forecastNative?.amount) {
                return <span>{formatCurrency(0, benefit.currency)}</span>;
            } else {
                return <span>{formatCurrency(benefit?.forecastNative?.amount - benefit.amount, benefit?.forecastNative?.currency)}</span>;
            }
        }
    }
];
