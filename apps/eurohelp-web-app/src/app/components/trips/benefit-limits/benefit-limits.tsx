import { BenefitSummary, primaryBenefits, secondaryBenefits } from '@aa/data-models/common';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@aa/ui/core/card';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@aa/ui/core/chart';
import { Separator } from '@aa/ui/core/separator';
import { forEach } from 'lodash';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { Label, PolarRadiusAxis, RadialBar, RadialBarChart } from 'recharts';
import { DataTable } from '../../shared/data-table';
import { TaskCostView } from './task-cost-view';
import { useDataColumns } from '@aa/ui/hooks/use-data-columns';
import { benefitLimitsColumns } from './benefit-limits-columns';
import type { ColumnDef } from '@tanstack/react-table';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { formatCurrency } from '../../../utils/layout/utils/utils';

interface BenefitLimitsProps {
    tripCode: string;
    productCode: string;
}

export const BenefitLimits = ({ tripCode, productCode }: BenefitLimitsProps) => {
    const [isLoading, setIsLoading] = useState(false);
    const [tripClient] = useTripClient();
    const actionColumn: ColumnDef<BenefitSummary> = {
        id: 'actions',
        cell: ({ row }) => {
            const benefit = row.original;

            return (
                <TaskCostView
                    tripCode={benefit.tripCode}
                    benefitLimitId={benefit.code}
                    productCode={productCode}
                />
            );
        }
    };

    useEffect(() => {
        setIsLoading(true);
        fetch(tripCode).catch();
        setIsLoading(false);
    }, [tripCode]);

    const [benefitColumns] = useDataColumns(benefitLimitsColumns, [actionColumn]);

    const [data, fetch, isPending, error] = useCallback(tripClient.getBenefitsSummaryByTripCode);
    const { isInitialPending } = useInitialPending(isPending);
    useInitialLoad(async () => fetch(tripCode));
    const benefitLimits: BenefitSummary[] = (data as BenefitSummary[]) || [];

    if (!benefitLimits) {
        return <p>Data not found!</p>;
    }

    let primaryBenefitLimit = 0;
    let primaryForecast = 0;
    let secondaryBenefitLimit = 0;
    let secondaryForecast = 0;
    const nativeCurrency = benefitLimits.length ? benefitLimits[0].forecastNative.currency : '£';

    forEach(benefitLimits, (benefitLimit) => {
        if (primaryBenefits.includes(benefitLimit.type)) {
            primaryBenefitLimit += benefitLimit.amount;
            primaryForecast += benefitLimit.forecastNative.amount;
        } else if (secondaryBenefits.includes(benefitLimit.type)) {
            secondaryBenefitLimit += benefitLimit.amount;
            secondaryForecast += benefitLimit.forecastNative.amount;
        }
    });

    const primaryDifference = primaryBenefitLimit - primaryForecast;
    const secondaryDifference = secondaryBenefitLimit - secondaryForecast;
    const chartDataPrimaryCost = [
        {
            primaryCost: primaryForecast,
            limit: primaryDifference
        }
    ];
    const chartDataSecondaryCost = [
        {
            secondaryCost: secondaryForecast,
            limit: secondaryDifference
        }
    ];

    const primaryColour = primaryDifference > 0.5 * primaryBenefitLimit ? '#15803D' : primaryDifference < 0 ? '#EF4444' : '#EAB308';
    const secondaryColour = secondaryDifference > 0.5 * secondaryBenefitLimit ? '#15803D' : secondaryDifference < 0 ? '#EF4444' : '#EAB308';
    const chartConfigPrimary = {
        primaryCost: {
            label: 'Primary Cost',
            color: primaryColour
        },
        limit: {
            label: 'Limit Left',
            color: '#CBD5E1'
        }
    } satisfies ChartConfig;
    const chartConfigSecondary = {
        secondaryCost: {
            label: 'Secondary Cost',
            color: secondaryColour
        },
        limit: {
            label: 'Limit Left',
            color: '#CBD5E1'
        }
    } satisfies ChartConfig;

    return (
        <Card className={'flex flex-col min-w-[calc(100%-40px)]'}>
            <CardHeader className={'flex flex-col'}>
                <CardTitle className="text-base">Benefit limits</CardTitle>
                <CardDescription className={'text-sm'}>Below you can find a list of available limits and forecast of costs.</CardDescription>
            </CardHeader>
            <Separator />
            <CardContent className={'flex flex-col'}>
                <div className={'flex'}>
                    <ChartContainer
                        config={chartConfigPrimary}
                        className="mx-auto aspect-square w-full max-w-[250px]"
                    >
                        <RadialBarChart
                            data={chartDataPrimaryCost}
                            endAngle={180}
                            innerRadius={80}
                            outerRadius={130}
                        >
                            <ChartTooltip
                                cursor={false}
                                content={<ChartTooltipContent hideLabel />}
                            />
                            <PolarRadiusAxis
                                tick={false}
                                tickLine={false}
                                axisLine={false}
                            >
                                <Label
                                    content={({ viewBox }) => {
                                        if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                                            return (
                                                <text
                                                    x={viewBox.cx}
                                                    y={viewBox.cy}
                                                    textAnchor="middle"
                                                >
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={(viewBox.cy || 0) - 16}
                                                        className="fill-foreground text-2xl font-bold"
                                                    >
                                                        {formatCurrency(primaryForecast, nativeCurrency)}
                                                    </tspan>
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={(viewBox.cy || 0) + 4}
                                                        className="fill-muted-foreground"
                                                    >
                                                        Primary cost
                                                    </tspan>
                                                </text>
                                            );
                                        } else {
                                            return null;
                                        }
                                    }}
                                />
                            </PolarRadiusAxis>
                            <RadialBar
                                dataKey="limit"
                                fill="var(--color-limit)"
                                stackId="a"
                                cornerRadius={5}
                                className="stroke-transparent stroke-2"
                            />
                            <RadialBar
                                dataKey="primaryCost"
                                stackId="a"
                                cornerRadius={5}
                                fill="var(--color-primaryCost)"
                                className="stroke-transparent stroke-2"
                            />
                        </RadialBarChart>
                    </ChartContainer>

                    <ChartContainer
                        config={chartConfigSecondary}
                        className="mx-auto aspect-square w-full max-w-[250px]"
                    >
                        <RadialBarChart
                            data={chartDataSecondaryCost}
                            endAngle={180}
                            innerRadius={80}
                            outerRadius={130}
                        >
                            <ChartTooltip
                                cursor={false}
                                content={<ChartTooltipContent hideLabel />}
                            />
                            <PolarRadiusAxis
                                tick={false}
                                tickLine={false}
                                axisLine={false}
                            >
                                <Label
                                    content={({ viewBox }) => {
                                        if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                                            return (
                                                <text
                                                    x={viewBox.cx}
                                                    y={viewBox.cy}
                                                    textAnchor="middle"
                                                >
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={(viewBox.cy || 0) - 16}
                                                        className="fill-foreground text-2xl font-bold"
                                                    >
                                                        {formatCurrency(secondaryForecast, nativeCurrency)}
                                                    </tspan>
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={(viewBox.cy || 0) + 4}
                                                        className="fill-muted-foreground"
                                                    >
                                                        Secondary cost
                                                    </tspan>
                                                </text>
                                            );
                                        } else {
                                            return null;
                                        }
                                    }}
                                />
                            </PolarRadiusAxis>
                            <RadialBar
                                dataKey="limit"
                                fill="var(--color-limit)"
                                stackId="a"
                                cornerRadius={5}
                                className="stroke-transparent stroke-2"
                            />
                            <RadialBar
                                dataKey="secondaryCost"
                                stackId="a"
                                cornerRadius={5}
                                fill="var(--color-secondaryCost)"
                                className="stroke-transparent stroke-2"
                            />
                        </RadialBarChart>
                    </ChartContainer>
                </div>

                <div>
                    <DataTable
                        className="rounded-md border"
                        data={benefitLimits}
                        columns={benefitColumns}
                    />
                </div>
            </CardContent>
        </Card>
    );
};
