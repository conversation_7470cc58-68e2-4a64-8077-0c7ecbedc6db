import { Currency, TripCost } from '@aa/data-models/common';
import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@aa/ui/core/button';
import { ExternalLink } from 'lucide-react';
import { formatCurrency } from '../../../utils/layout/utils/utils';

export const taskCostColumns: ColumnDef<TripCost>[] = [
    {
        accessorKey: 'taskId',
        header: 'Task id'
    },
    {
        accessorKey: 'forecast.amount',
        header: 'Forecast',
        cell: ({ row }) => {
            const benefit = row.original;

            return <span>{formatCurrency(benefit?.forecastNative?.amount, benefit?.forecastNative?.currency)}</span>;
        }
    },
    {
        accessorKey: 'status',
        header: 'Status'
    }
    // {
    //     id: 'actions',
    //     cell: ({ row }) => {
    //         const benefit = row.original;

    //         return (
    //             <Button
    //                 variant="ghost"
    //                 className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
    //             >
    //                 <ExternalLink className="h-4 w-4" />
    //                 <span className={'sr-only'}>Details</span>
    //             </Button>
    //         );
    //     },
    // },
];
