import { BenefitLimit, ExtendedPaginationQueryResult, Invoice, TripCost } from '@aa/data-models/common';
import { Card, CardContent, CardDescription, CardTitle } from '@aa/ui/core/card';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@aa/ui/core/chart';
import { Separator } from '@aa/ui/core/separator';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@aa/ui/core/sheet';
import { forEach } from 'lodash';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { Label, PolarRadiusAxis, RadialBar, RadialBarChart } from 'recharts';
import { SearchAndLinkContactDataTable } from '../../shared/search-and-link-contact/data-table';
import { taskCostColumns } from './task-cost-columns';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { useProductBenefitClient } from '@aa/ui/hooks/use-product-benefit-client';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import { formatCurrency } from '../../../utils/layout/utils/utils';

interface TaskCostProps {
    tripCode: string;
    benefitLimitId: string;
    productCode: string;
}

export const TaskCostView = ({ tripCode, benefitLimitId, productCode }: TaskCostProps) => {
    const [isLoading, setIsLoading] = useState(false);
    const [productBenefitClient] = useProductBenefitClient();
    const [tripClient] = useTripClient();

    useEffect(() => {
        setIsLoading(true);
        fetchTripCost(tripCode).catch();
        setIsLoading(false);
    }, [tripCode]);

    const [tripCostData, fetchTripCost, isPendingTripCost, tripCostError] = useCallback(tripClient.getTripCostsByTripCode);
    const { isInitialPending } = useInitialPending(isPendingTripCost);
    useInitialLoad(async () => fetchTripCost(tripCode));
    let taskCosts = [] as TripCost[];
    if (tripCostData) {
        taskCosts = tripCostData as unknown as TripCost[];
    }
    taskCosts = taskCosts.filter((taskCost) => taskCost.benefitLimitId === benefitLimitId);
    // const { results: taskCosts, ...pagination }: ExtendedPaginationQueryResult<TripCost> = tripCostData || {
    //     results: [],
    //     more: false,
    //     totalPages: 0,
    //     skip: 0,
    //     limit: 10,
    //     total: 0,
    // };

    let totalForecast = 0;

    forEach(taskCosts, (taskCost: TripCost) => {
        totalForecast += taskCost.forecastNative.amount;
    });

    const [benefitLimitData, fetchBenefitLimit, isPendingBenefitLimit, benefitLimitError] = useCallback(productBenefitClient.getBenefitLimitByCode);
    useInitialLoad(async () => fetchBenefitLimit(benefitLimitId));
    const benefitLimit = benefitLimitData;

    if (!benefitLimit) {
        return <p>Benefit limit data not found!</p>;
    }

    const costDifference = benefitLimit.amount - totalForecast;

    const chartData = [
        {
            primaryCost: totalForecast,
            limit: costDifference
        }
    ];
    const nativeCurrency = taskCosts.length ? taskCosts[0].forecastNative.currency : '£';
    const primaryColour = costDifference > 0.5 * benefitLimit.amount ? '#15803D' : costDifference < 0 ? '#EF4444' : '#EAB308';
    const chartConfig = {
        primaryCost: {
            label: `${taskCosts && taskCosts.length > 0 ? benefitLimit.type : `Data was not enough`}`,
            color: primaryColour
        },
        limit: {
            label: 'Limit left',
            color: '#CBD5E1'
        }
    } satisfies ChartConfig;

    if (!taskCosts) {
        return <p>Data not found!</p>;
    }

    return (
        <Sheet>
            <SheetTrigger asChild>
                <button className="btn btn-primary mx-1">Details</button>
            </SheetTrigger>
            <SheetContent className="overflow-auto flex flex-col min-w-[700px]">
                <SheetHeader>
                    <SheetTitle>Benefit cost</SheetTitle>
                    <SheetDescription>Below you can find detail breakdown of tasks contributing to the selected benefit spend.</SheetDescription>
                </SheetHeader>
                <Card className={'flex flex-col'}>
                    <Separator />
                    <CardContent className={'flex flex-col'}>
                        <div className={'flex'}>
                            <div className={'min-w-[150px] py-8 w-full'}>
                                <div className={'flex flex-col gap-1 p-3'}>
                                    <div className={'flex flex-col gap-1'}>
                                        <CardTitle className={'text-sm'}>Trip code</CardTitle>
                                        <CardDescription className={'text-sm'}>{tripCode}</CardDescription>
                                    </div>
                                    <div className={'flex flex-col gap-1'}>
                                        <CardTitle className={'text-sm'}>Product</CardTitle>
                                        {taskCosts && taskCosts.length > 0 && <CardDescription className={'text-sm'}>{productCode}</CardDescription>}
                                    </div>
                                    <div className={'flex flex-col gap-1'}>
                                        <CardTitle className={'text-sm'}>Benefit name</CardTitle>
                                        {taskCosts && taskCosts.length > 0 && <CardDescription className={'text-sm'}>{benefitLimit.name}</CardDescription>}
                                    </div>
                                    <div className={'flex flex-col gap-1'}>
                                        <CardTitle className={'text-sm'}>Benefit type</CardTitle>
                                        {taskCosts && taskCosts.length > 0 && <CardDescription className={'text-sm'}>{benefitLimit.type}</CardDescription>}
                                    </div>
                                </div>
                            </div>

                            <Separator
                                className={'mx-2 my-2 h-22'}
                                orientation={'vertical'}
                            />

                            <ChartContainer
                                config={chartConfig}
                                className="mx-auto aspect-square w-full max-w-[250px]"
                            >
                                <RadialBarChart
                                    data={chartData}
                                    endAngle={180}
                                    innerRadius={80}
                                    outerRadius={130}
                                >
                                    <ChartTooltip
                                        cursor={false}
                                        content={<ChartTooltipContent hideLabel />}
                                    />
                                    <PolarRadiusAxis
                                        tick={false}
                                        tickLine={false}
                                        axisLine={false}
                                    >
                                        <Label
                                            content={({ viewBox }) => {
                                                if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                                                    return (
                                                        <text
                                                            x={viewBox.cx}
                                                            y={viewBox.cy}
                                                            textAnchor="middle"
                                                        >
                                                            <tspan
                                                                x={viewBox.cx}
                                                                y={(viewBox.cy || 0) - 16}
                                                                className="fill-foreground text-2xl font-bold"
                                                            >
                                                                {formatCurrency(totalForecast, nativeCurrency)}
                                                            </tspan>
                                                            <tspan
                                                                x={viewBox.cx}
                                                                y={(viewBox.cy || 0) + 4}
                                                                className="fill-muted-foreground"
                                                            >
                                                                {benefitLimit.type} cost
                                                            </tspan>
                                                        </text>
                                                    );
                                                } else {
                                                    return null;
                                                }
                                            }}
                                        />
                                    </PolarRadiusAxis>
                                    <RadialBar
                                        dataKey="limit"
                                        fill="var(--color-limit)"
                                        stackId="a"
                                        cornerRadius={5}
                                        className="stroke-transparent stroke-2"
                                    />
                                    <RadialBar
                                        dataKey="primaryCost"
                                        stackId="a"
                                        cornerRadius={5}
                                        fill="var(--color-primaryCost)"
                                        className="stroke-transparent stroke-2"
                                    />
                                </RadialBarChart>
                            </ChartContainer>
                        </div>

                        <Separator className={'mb-4'} />

                        <div>
                            <SearchAndLinkContactDataTable
                                data={taskCosts}
                                columns={taskCostColumns}
                                placeholder={'Search for task id'}
                                filters={[]}
                                mainFilter={'taskId'}
                                onLink={(row) => {}}
                            />
                        </div>
                    </CardContent>
                </Card>
            </SheetContent>
        </Sheet>
    );
};
