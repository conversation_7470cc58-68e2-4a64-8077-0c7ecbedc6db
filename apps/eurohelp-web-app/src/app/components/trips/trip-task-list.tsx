import { C<PERSON><PERSON><PERSON><PERSON>, Create<PERSON><PERSON>on<PERSON><PERSON>, ExtendedPaginationQueryResult, PaginationQuery, TaskStatus, Trip, WithDataId } from '@aa/data-models/common';
import { QueryBreakdownTask } from '@aa/data-models/entities/breakdown-task';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@aa/ui/core/card';
import { Separator } from '@aa/ui/core/separator';
import React, { useEffect, useState } from 'react';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { BreakdownTaskPreview } from '@aa/data-models/entities/task-preview';
import { DataTableToolbar } from '@aa/ui/hoc/data-table/data-table-toolbar';
import { DataTableBody } from '@aa/ui/hoc/data-table/data-table-body';
import { DataTablePagination } from '@aa/ui/hoc/data-table/data-table-pagination';
import { DataColumnFiltersState } from '@aa/ui/providers/data-table-provider';
import { useDataColumns } from '@aa/ui/hooks/use-data-columns';
import type { ColumnDef } from '@tanstack/react-table';
import { facetedFilters, textFilters, tripTasksListColumns } from './trip-task/trip-task-list-table-definition';
import { DataTable } from '@aa/ui/hoc/data-table/data-table';
import { TripTaskListView } from './trip-task/trip-task-list-view';
import { Label } from '@aa/ui/core/label';
import { Switch } from '@aa/ui/core/switch';
import { useDebounce } from '@aa/ui/hooks/use-debounce';
import { Preloader } from '@aa/ui/core/preloader';

interface TripTasksListProps {
    trip: WithDataId<Trip>;
}

export default function TripTasksList({ trip }: TripTasksListProps) {
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [tripClient] = useTripClient();
    const [showOnlyPrimary, setShowOnlyPrimary] = useState<boolean>(false);
    const [taskId, setTaskId] = useState<number | undefined>();
    const status: TaskStatus[] = [];
    const associatedStatus: TaskStatus[] = [];
    const [query, setQuery] = useState<QueryBreakdownTask>({ limit: 10, skip: 0, tripCode: trip.code, taskId, status, associatedStatus, showOnlyPrimary });
    const debouncedQuery = useDebounce({ value: query, delay: 500 });
    const [data, fetch, isPending, error] = useCallback(tripClient.queryTasks);
    const { isInitialPending } = useInitialPending(isPending);
    useInitialLoad(async () => fetch(debouncedQuery));
    const { results: tasks, ...pagination }: ExtendedPaginationQueryResult<BreakdownTaskPreview> = data || {
        results: [],
        more: false,
        totalPages: 0,
        skip: 0,
        limit: 10,
        total: 0
    };
    const sortedResult = tasks.sort((taskA: BreakdownTaskPreview, taskB: BreakdownTaskPreview) => {
        if (taskA && taskA.schedule && taskA.schedule.create && taskB && taskB.schedule && taskB.schedule.create) {
            const dateA = new Date(taskA.schedule.create).getTime();
            const dateB = new Date(taskB.schedule.create).getTime();
            return dateB - dateA;
        } else {
            return 0;
        }
    });
    const mappedTasks = sortedResult.map((item: BreakdownTaskPreview) => ({
        taskId: Number(item.id),
        customerGroup: trip.customerGroup,
        createReason: item.createReason as CreateReason,
        status: item.status as unknown as TaskStatus,
        isCompleted: item.status === 'CLSD' || item.status === 'COMP',
        vrn: item.vehicle?.registration as string,
        location: item.location,
        created: new Date(item.schedule?.create).toLocaleString() || 'No creation date',
        schedule: item.schedule,
        vehicle: item.vehicle,
        entitlement: item.entitlement,
        fault: item.fault,
        taskType: item.taskType,
        customerGroupName: item.customerGroupName,
        additionalExperianDetails: item.additionalExperianDetails
    }));
    const sortedTasks = mappedTasks as unknown as BreakdownTaskPreview[];

    useEffect(() => {
        setQuery((prevState) => ({ tripCode: trip.code }));
    }, [trip]);

    useEffect(() => {
        setIsLoading(true);
        fetch(query).catch();
        setIsLoading(false);
    }, [debouncedQuery]);

    const onPaginationChange = (state: PaginationQuery) => {
        setQuery((prevState) => ({ ...prevState, ...state }));
    };

    const onColumnFiltersChange = (state: DataColumnFiltersState<BreakdownTaskPreview>) => {
        let mappedState = state.reduce((result: any, obj) => {
            if (obj) {
                result[obj.id] = obj.value;
            }
            return result;
        }, {});

        if (mappedState && mappedState.status) {
            mappedState.associatedStatus = [...mappedState.status];
        }
        if (mappedState && mappedState.status && mappedState.status.includes(TaskStatus.GDET)) {
            mappedState.associatedStatus.push(
                TaskStatus.PLAN,
                TaskStatus.TIDY,
                TaskStatus.PACK,
                TaskStatus.HEAD,
                TaskStatus.ARVD,
                TaskStatus.WRAP,
                TaskStatus.LOAD,
                TaskStatus.UNLD,
                TaskStatus.RECY,
                TaskStatus.CHCK
            );
        }
        if (mappedState && mappedState.status && mappedState.status.includes(TaskStatus.COMP)) {
            mappedState.associatedStatus.push(TaskStatus.CLSD);
        }
        if (mappedState && mappedState.status && mappedState.status.includes(TaskStatus.GARR)) {
            mappedState.associatedStatus.push(TaskStatus.GDET);
        }

        if (Object.keys(mappedState).length === 0) {
            mappedState = {
                taskId,
                status,
                associatedStatus,
                showOnlyPrimary
            };
        }

        setQuery((prevState) => ({
            ...prevState,
            ...mappedState
        }));
    };

    const actionColumn: ColumnDef<BreakdownTaskPreview> = {
        id: 'actions',
        cell: ({ row }) => {
            const task = row.original;

            const breakdownRSSRecoveryCreateReasons: CreateReasonCode[] = [
                CreateReasonCode.INITIAL_TASK,
                CreateReasonCode.SUPPORT_TASK,
                CreateReasonCode.RELAY_LEG,
                CreateReasonCode.FINAL_LEG,
                CreateReasonCode.REATTEND,
                CreateReasonCode.RECOVERY,
                CreateReasonCode.KEY_ASSIST,
                CreateReasonCode.SUPPORT_TASK_AT_DESTINATION,
                CreateReasonCode.SELF_SERVICE_CAR,
                CreateReasonCode.SELF_SERVICE_APP
            ];

            // public transport, car hire etc. does not have enough details for detailed view
            if (task.createReason && breakdownRSSRecoveryCreateReasons.includes(task.createReason.id)) {
                return (
                    <TripTaskListView
                        task={task}
                        trip={trip}
                    />
                );
            }

            return <div></div>;
        }
    };

    const [columnsWithAction] = useDataColumns(tripTasksListColumns, [actionColumn]);

    return (
        <Card className={'min-w-[calc(100%-30px)]'}>
            <CardHeader>
                <CardTitle className={'text-base'}>Trip tasks</CardTitle>
                <CardDescription className={'text-sm'}>All tasks for trip {trip.code}</CardDescription>
            </CardHeader>
            <Separator />
            <CardContent className="p-0 flex flex-col gap-4">
                <DataTable
                    data={sortedTasks}
                    pagination={pagination}
                    // isDataPending={isInitialPending}
                    columns={columnsWithAction}
                    onPagination={onPaginationChange}
                    onColumnFilters={onColumnFiltersChange}
                >
                    <DataTableToolbar
                        facetedFilters={facetedFilters}
                        textFilters={textFilters}
                        className={'pt-4 pl-4 pb-0'}
                    >
                        <div className="flex space-x-5 pr-2">
                            <div className="flex items-center space-x-2">
                                <Switch
                                    checked={showOnlyPrimary}
                                    onCheckedChange={(value) => {
                                        setShowOnlyPrimary(value);
                                        setQuery((prevState) => ({ ...prevState, showOnlyPrimary: value }));
                                    }}
                                />
                                <Label htmlFor="create-task">Only primary task</Label>
                            </div>
                        </div>
                    </DataTableToolbar>
                    {isInitialPending ? <Preloader /> : <DataTableBody className="rounded-md border h-[calc(100%-150px)]" />}
                    <DataTablePagination className={'pb-3'} />
                </DataTable>
            </CardContent>
        </Card>
    );
}
