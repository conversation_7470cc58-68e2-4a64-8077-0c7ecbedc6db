'use client';

import { TaskStatus } from '@aa/data-models/common';
import { DataTableColumnHeader } from '@aa/ui/hoc/data-table/data-table-column-header';
import { DataTableFacetedFilter } from '@aa/ui/hoc/data-table/data-table-faceted-filter';
import { DataTableTextFilter } from '@aa/ui/hoc/data-table/data-table-text-filter';
import { ColumnDef } from '@tanstack/react-table';
import { BreakdownTaskPreview } from '@aa/data-models/entities/task-preview';
import { getStatusStyles } from '../task-details/task-status-button';
import { cn } from '@aa/ui/lib/utils';

export const statuses: { value: TaskStatus; label: string }[] = [
    {
        value: TaskStatus.INIT,
        label: 'Requested'
    },
    {
        value: TaskStatus.GDET,
        label: 'In progress'
    },
    {
        value: TaskStatus.GARR,
        label: 'Garage/Hire'
    },
    {
        value: TaskStatus.HOLD,
        label: 'On hold'
    },
    {
        value: TaskStatus.COMP,
        label: 'Completed'
    }
];

export const textFilters: DataTableTextFilter[] = [
    {
        column: 'taskId',
        placeholder: 'Task id...'
    }
];

export const facetedFilters: DataTableFacetedFilter[] = [
    {
        column: 'status',
        options: statuses,
        title: 'Status'
    }
];

export const tripTasksListColumns: ColumnDef<BreakdownTaskPreview>[] = [
    {
        accessorKey: 'taskId',
        header: ({ column }) => (
            <DataTableColumnHeader
                className="min-w-[80px] max-w-[120px] truncate"
                column={column}
                title="Task Id"
            />
        ),
        cell: ({ row }) => <div className="min-w-[60px] max-w-[120px] truncate">{row.getValue('taskId')}</div>,
        enableSorting: false,
        enableHiding: false
    },
    {
        accessorKey: 'createReason.name',
        header: 'Create Reason',
        cell: ({ row }) => {
            const task = row.original;
            const createReasonName = task.createReason?.name || (task.taskType?.code === 'FLP' || task.taskType?.code === 'FIN' ? task.taskType.name : 'N/A');

            return createReasonName;
        }
    },
    {
        accessorKey: 'customerGroup',
        header: 'Customer Group',
        cell: (row) => row.getValue()
    },
    {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => {
            const task = row.original;
            const status = getStatusStyles(task.status);
            return <span className={cn('rounded-full p-1 min-w-[120px] whitespace-nowrap')}>{status.text}</span>;
        },
        enableSorting: false,
        enableHiding: false
    },
    {
        accessorKey: 'vrn',
        header: 'VRN',
        cell: (row) => row.getValue() || 'N/A'
    },
    {
        accessorKey: 'location.text',
        header: 'Location',
        cell: (row) => row.getValue() || 'N/A'
    },
    {
        accessorKey: 'location.area',
        header: 'Area',
        cell: (row) => row.getValue() || 'N/A'
    },
    {
        accessorKey: 'schedule.create',
        header: 'Created',
        cell: (row) => new Date(row.getValue() as string).toLocaleString()
    }
];
