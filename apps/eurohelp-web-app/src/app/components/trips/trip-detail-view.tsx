import { <PERSON><PERSON>nti<PERSON>, NoteEntityType, Trip } from '@aa/data-models/common';
import { Button } from '@aa/ui/core/button';
import { Preloader } from '@aa/ui/core/preloader';
import { ScrollArea } from '@aa/ui/core/scroll-area';
import { Separator } from '@aa/ui/core/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@aa/ui/core/tooltip';
import { useState } from 'react';
import SendMessage from '../action-display/send-message';
import SetReminder from '../action-display/set-reminder';
import { AddTripContact } from './add-trip-contact';
import { BenefitLimits } from './benefit-limits/benefit-limits';
import { CreateRSSTask } from './create-rss-task';
import { TripEdit } from './edit-trip/trip-edit';
import TripLogBook from './notes-list/trip-log-book';
import TripListItem from './trip-list-item';
import { AddTripNote } from './trip-note';
import { TripSummary } from './trip-summary';
import TripTasksList from './trip-task-list';
import TripActionsList from './trip-action-list';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useCallback as useCallbackReact } from 'react';
import { BreakdownTaskPreview } from '@aa/data-models/entities/task-preview';
import { DataQuery } from '@aa/data-query';
import { useNotifications } from '@aa/ui/hooks/use-notifications';
import { Source } from '@aa/data-models/system/source';
import { useChange } from '@aa/ui/hooks/use-change';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';

interface TripDetailViewProps {
    tripCode: string;
}

export const TripDetailView = (props: TripDetailViewProps) => {
    const [updateTrigger, setUpdateTrigger] = useState(0);
    const [logBookUpdateTrigger, setLogBookUpdateTrigger] = useState(0);
    const [tripClient] = useTripClient();
    const [tripData, fetchTripData, isPendingTripData, tripDataError] = useCallback(tripClient.getTripById);

    const onChange = () => {
        if (props.tripCode) {
            getTripData().catch();
        }
    };

    const getTripData = async () => {
        if (props.tripCode) {
            await fetchTripData(props.tripCode).catch();
            await fetchTasksData(props.tripCode).catch();
        }
    };
    useInitialLoad(async () => await getTripData());
    const query = new DataQuery<Trip>({ code: props.tripCode });
    const { resubscribe } = useNotifications({ source: Source.TRIP_ENTITY, query }, { onChange });
    useChange(props.tripCode, resubscribe);

    const handleUpdate = () => {
        setUpdateTrigger((prev) => prev + 1);
    };

    const handleLogBookUpdate = useCallbackReact(() => {
        setLogBookUpdateTrigger((prev) => prev + 1);
    }, []);
    const { isInitialPending } = useInitialPending(isPendingTripData);

    let activeRSSTasks: BreakdownTaskPreview[] = [];
    let taskSummaryData: BreakdownTaskPreview = {} as BreakdownTaskPreview;
    const [tasksData, fetchTasksData, isPendingTasksData, tasksDataError] = useCallback(tripClient.getTasksByTripCode);
    if (tasksData) {
        const rssTasks = tasksData.filter((task: BreakdownTaskPreview) => task.createReason?.serviceType === 'RSS');
        if (rssTasks.length > 0) {
            const activeTasks = rssTasks.filter((task: BreakdownTaskPreview) => task.status !== 'COMP' && task.status !== 'CLSD');
            activeRSSTasks = activeTasks;
            if (activeTasks.length > 0) {
                const sortedTasks = activeTasks.sort(
                    (a: BreakdownTaskPreview, b: BreakdownTaskPreview) => new Date(b.schedule?.arrive as string).getTime() - new Date(a.schedule?.arrive as string).getTime()
                );
                const latestTask = sortedTasks[0];
                taskSummaryData = latestTask;
            } else {
                const sortedTasks = rssTasks.sort(
                    (a: BreakdownTaskPreview, b: BreakdownTaskPreview) => new Date(b.schedule?.arrive as string).getTime() - new Date(a.schedule?.arrive as string).getTime()
                );
                const latestTask = sortedTasks[0];
                taskSummaryData = latestTask;
            }
        }
    }

    //we dont need this callback, as we havenotification to update trip
    const updateTripSummary = () => {
        if (!tripData) {
            return;
        }
        //fetchTripData(tripData.code).catch();
        //fetchTasksData(tripData.code).catch();
    };

    if (!tripData) {
        return (
            <div className="mx-auto flex h-screen w-full items-center bg-gray-50 flex-col text-center justify-center space-y-6">
                <Preloader />
            </div>
        );
    }

    const parentEntity: NoteEntity = { type: NoteEntityType.TRIP, id: tripData.code };

    return (
        <TooltipProvider delayDuration={0}>
            <ScrollArea className="flex flex-col h-screen bg-gray-100">
                <div className="flex flex-col w-full bg-white">
                    <TripListItem tripData={tripData} />
                    <Separator />
                    <div className="flex items-center p-2">
                        <div className="flex items-center gap-2">
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        disabled={false}
                                    >
                                        <AddTripNote
                                            tripData={tripData}
                                            onUpdate={handleLogBookUpdate}
                                        />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Add note</TooltipContent>
                            </Tooltip>

                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        disabled={false}
                                    >
                                        <TripEdit
                                            tripData={tripData}
                                            onUpdateSummary={updateTripSummary}
                                        />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Edit trip</TooltipContent>
                            </Tooltip>

                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        disabled={false}
                                    >
                                        <AddTripContact
                                            tripData={tripData}
                                            isTripcontact={true}
                                        />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Add trip contact</TooltipContent>
                            </Tooltip>

                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        disabled={false}
                                    >
                                        <CreateRSSTask
                                            vrn={tripData.vrn}
                                            custGroup={tripData.customerGroup}
                                            bcasp={tripData.customerKey}
                                            allow={activeRSSTasks.length === 0}
                                        />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent>Create RSS in AAHelp2</TooltipContent>
                            </Tooltip>

                            <Separator
                                orientation="vertical"
                                className="mx-1 h-6"
                            />

                            <SendMessage
                                parentEntity={parentEntity}
                                hasTrip={true}
                                onUpdate={handleLogBookUpdate}
                            />
                            <SetReminder
                                parentEntity={parentEntity}
                                hasTrip={true}
                                onUpdate={handleLogBookUpdate}
                            />

                            {/*<Tooltip>*/}
                            {/*    <TooltipTrigger asChild>*/}
                            {/*        <Button*/}
                            {/*            variant="ghost"*/}
                            {/*            size="icon"*/}
                            {/*            disabled={false}*/}
                            {/*        >*/}
                            {/*            <AddSecureLocation*/}
                            {/*                tripCode={tripData.code}*/}
                            {/*                onUpdate={handleUpdate}*/}
                            {/*            />*/}
                            {/*        </Button>*/}
                            {/*    </TooltipTrigger>*/}
                            {/*    <TooltipContent>Add location</TooltipContent>*/}
                            {/*</Tooltip>*/}

                            {/*TODO: We need to come back to this later*/}
                            {/*<Tooltip>*/}
                            {/*    <TooltipTrigger asChild>*/}
                            {/*        <Button variant="ghost" size="icon" disabled={false}>*/}
                            {/*            <Add task*/}
                            {/*                tripData={tripData}*/}
                            {/*                onUpdate={handleLogBookUpdate}*/}
                            {/*            />*/}
                            {/*        </Button>*/}
                            {/*    </TooltipTrigger>*/}
                            {/*    <TooltipContent>Add task</TooltipContent>*/}
                            {/*</Tooltip>*/}

                            {/*TODO: We need to come back to this later*/}
                            {/*<Tooltip>*/}
                            {/*    <TooltipTrigger asChild>*/}
                            {/*        <Button variant="ghost" size="icon" disabled={false}>*/}
                            {/*            <Add action*/}
                            {/*                tripData={tripData}*/}
                            {/*                onUpdate={handleLogBookUpdate}*/}
                            {/*            />*/}
                            {/*        </Button>*/}
                            {/*    </TooltipTrigger>*/}
                            {/*    <TooltipContent>Add action</TooltipContent>*/}
                            {/*</Tooltip>*/}

                            {/*TODO: We need to come back to this later*/}
                            {/*<Tooltip>*/}
                            {/*    <TooltipTrigger asChild>*/}
                            {/*        <Button variant="ghost" size="icon" disabled={false}>*/}
                            {/*            <Other*/}
                            {/*                tripData={tripData}*/}
                            {/*                onUpdate={handleLogBookUpdate}*/}
                            {/*            />*/}
                            {/*        </Button>*/}
                            {/*    </TooltipTrigger>*/}
                            {/*    <TooltipContent>Other</TooltipContent>*/}
                            {/*</Tooltip>*/}
                        </div>
                    </div>
                    <Separator />
                </div>

                <div className="p-3 min-w-full">
                    <h2 className="mb-2">Trip summary</h2>
                    <div className="h-2/5 bg-gray-100 rounded-lg">
                        <div className="h-full">
                            <TripSummary
                                tripCode={tripData.code}
                                tripSummaryData={tripData}
                                taskSummaryData={taskSummaryData}
                            />
                        </div>
                    </div>
                </div>

                {/*<div className="p-3 flex gap-3">*/}
                {/*    // TODO: God knows what we will do with that*/}
                {/*    <div>*/}
                {/*        <h2 className="mb-2">Last secure location</h2>*/}
                {/*        <div className="h-2/5 flex bg-gray-100">*/}
                {/*            <div className="h-full">*/}
                {/*                <SecureLocations*/}
                {/*                    tripCode={tripData.code}*/}
                {/*                    updateTrigger={updateTrigger}*/}
                {/*                />*/}
                {/*            </div>*/}
                {/*        </div>*/}
                {/*    </div>*/}
                {/*    <div>*/}
                {/*        <h2 className="mb-2">Fault List</h2>*/}
                {/*        <div className="h-2/5 flex bg-gray-100">*/}
                {/*            <div className="h-full w-full">*/}
                {/*                <Faults tripCode={tripData.code} />*/}
                {/*            </div>*/}
                {/*        </div>*/}
                {/*    </div>*/}

                {/*    <div>*/}
                {/*        <h2 className="mb-2">Task Summary</h2>*/}
                {/*        <div className="flex bg-gray-100">*/}
                {/*            <div className="h-full">*/}
                {/*                <TaskSummaryCard*/}
                {/*                    tripCode={tripData.code}*/}
                {/*                    tripData={tripData}*/}
                {/*                />*/}
                {/*            </div>*/}
                {/*        </div>*/}
                {/*    </div>*/}
                {/*</div>*/}

                <div className={'p-3 flex gap-3'}>
                    <div className={'w-full'}>
                        {/*<h2 className="mb-2">Task List</h2>*/}
                        <div className="flex bg-gray-100">
                            <div className="h-full min-w-[calc(100%-15px)]">
                                <TripTasksList trip={tripData} />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="p-3 min-w-full">
                    <div className={'w-full'}>
                        <div className="flex bg-gray-100">
                            <div className="h-full min-w-[calc(100%-15px)]">
                                <TripActionsList tripCode={tripData.code} />
                            </div>
                        </div>
                    </div>
                </div>

                <div className={'p-3 flex gap-3'}>
                    <div className={'w-full'}>
                        {/*<h2 className="mb-2">Trip Logbook</h2>*/}
                        <div className="h-2/5 flex bg-gray-100">
                            <div className="h-full min-w-[calc(100%-15px)]">
                                <TripLogBook
                                    trip={tripData}
                                    updateTrigger={logBookUpdateTrigger}
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div className={'p-3 flex gap-3 min-w-full'}>
                    <div className={'min-w-full'}>
                        {/*<h2 className="mb-2">Benefits</h2>*/}
                        <div className="h-2/5 flex bg-gray-100 min-w-full">
                            <div className="h-full min-w-[calc(100%-30px)]">
                                <BenefitLimits
                                    tripCode={tripData.code}
                                    productCode={tripData.productCode}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </ScrollArea>
        </TooltipProvider>
    );
};
