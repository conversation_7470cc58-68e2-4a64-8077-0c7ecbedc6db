import { Action, WithDataId } from '@aa/data-models/common';
import { Button } from '@aa/ui/core/button';
import { Clock } from 'lucide-react';
import * as React from 'react';
import { Panel } from '@aa/ui/hoc/panel/panel';
import { Separator } from '@aa/ui/core/separator';
import { useState } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@aa/ui/core/tooltip';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useActionClient } from '@aa/ui/hooks/use-action-client';

interface SnoozeActionbarProps {
    action: WithDataId<Action> | null;
    disabled?: boolean;
}

export default function SnoozeAction({ action, disabled }: SnoozeActionbarProps) {
    const [state, setState] = useState({
        isPanelOpen: false,
        selectedDate: null as string | null,
        reason: '',
        sendButtonDisabled: false
    });
    const [actionClient] = useActionClient();

    const [, snoozeActionHandler] = useCallback(actionClient.reschedule);

    const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const dateStr = e.target.value;
        setState((prevState) => ({
            ...prevState,
            selectedDate: dateStr
        }));
    };

    const handleReasonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setState((prevState) => ({
            ...prevState,
            reason: e.target.value
        }));
    };

    const handleSubmit = () => {
        if (state.selectedDate && state.reason) {
            const date = new Date(state.selectedDate);
            if (!action) return;
            setState((prevState) => ({
                ...prevState,
                sendButtonDisabled: true
            }));
            snoozeActionHandler(action._id, { scheduleDate: date, reason: state.reason });
            setState((prevState) => ({
                ...prevState,
                isPanelOpen: false,
                sendButtonDisabled: false
            }));
        }
    };

    const handleCancel = () => {
        setState((prevState) => ({
            ...prevState,
            isPanelOpen: false
        }));
    };

    return (
        <>
            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        size="icon"
                        disabled={!action || disabled}
                        onClick={() => setState((prevState) => ({ ...prevState, isPanelOpen: true }))}
                    >
                        <Clock className="h-4 w-4" />
                    </Button>
                </TooltipTrigger>
                <TooltipContent>Reschedule</TooltipContent>
            </Tooltip>

            <Panel
                open={state.isPanelOpen}
                onClose={() => setState((prevState) => ({ ...prevState, isPanelOpen: false }))}
                title="Reschedule Action"
                description="Please provide justification for the action."
                scroll
            >
                <Separator className="my-4" />

                <div className="mt-4">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg mx-auto">
                        <h4 className="mb-2">Action active from</h4>
                        <input
                            type="datetime-local"
                            value={state.selectedDate || ''}
                            onChange={handleDateChange}
                            className="border p-2 rounded w-full"
                        />

                        <Separator className="my-8" />

                        <h4 className="mb-2">Reason for Rescheduling:</h4>
                        <textarea
                            value={state.reason}
                            onChange={handleReasonChange}
                            placeholder="Enter the reason here"
                            className="border p-2 rounded w-full h-24"
                        />

                        <div className="mt-4 flex space-x-4">
                            <Button
                                disabled={!state.selectedDate || !state.reason || state.sendButtonDisabled}
                                onClick={handleSubmit}
                            >
                                Reschedule Action
                            </Button>
                            <Button
                                variant="ghost"
                                onClick={handleCancel}
                            >
                                Cancel
                            </Button>
                        </div>
                    </div>
                </div>
            </Panel>
        </>
    );
}
