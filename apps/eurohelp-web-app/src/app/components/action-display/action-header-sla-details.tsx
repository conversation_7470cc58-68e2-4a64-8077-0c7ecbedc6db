import { Action, ActionSLA } from '@aa/data-models/common';
import { getSLAProgress, knowColour } from '@aa/ui/lib/utils';
import { format } from 'date-fns';
import { CalendarClock } from 'lucide-react';

interface ActionHeaderLockDetailsProps {
    action: Action;
}

export function ActionHeaderSlaDetails({ action }: ActionHeaderLockDetailsProps) {
    const { slaProgress, time } = getSLAProgress(action);
    const { className, backgroundColour } = knowColour(slaProgress);
    return (
        <>
            {slaProgress !== -1 && (
                <div className={backgroundColour}>
                    <div className="shrink flex flex-row gap-2">
                        {slaProgress === ActionSLA.EXPIRED && (
                            <>
                                <CalendarClock
                                    className={className}
                                    strokeWidth={1}
                                />
                                <span className={className}>
                                    This Action Expired {time}. Expiration time was set to {action.expiresAt && format(new Date(action.expiresAt), 'dd-MM-yyyy, HH:mm aaaa')}
                                </span>
                            </>
                        )}
                        {(slaProgress === ActionSLA.EXPIRING || slaProgress === ActionSLA.LATE || slaProgress === ActionSLA.ON_TIME) && (
                            <>
                                <span className={className}>
                                    This Action is due in {time}. Expiration time is set to {action.expiresAt && format(new Date(action.expiresAt), 'dd-MM-yyyy, HH:mm aaaa')}
                                </span>
                            </>
                        )}
                    </div>
                </div>
            )}
        </>
    );
}
