import { Action, Trip, WithDataId } from '@aa/data-models/common';
import { But<PERSON> } from '@aa/ui/core/button';
import { CardTitle, CardDescription, Card, CardFooter, CardHeader, CardContent } from '@aa/ui/core/card';
import { ScrollArea } from '@aa/ui/core/scroll-area';
import { Separator } from '@aa/ui/core/separator';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@aa/ui/core/sheet';
import { Tooltip, TooltipContent, TooltipTrigger } from '@aa/ui/core/tooltip';
import { CircleCheckIcon, LinkIcon, Unlink } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useActionClient } from '@aa/ui/hooks/use-action-client';

interface UnLinkSummaryProps {
    action: WithDataId<Action> | null;
}

export default function UnlinkSummary({ action }: UnLinkSummaryProps) {
    const parentId = action?.parentEntity?.id;
    const [state, setState] = useState({
        showUnlink: true,
        open: false,
        sendButtonDisabled: false
    });

    const [tripClient] = useTripClient();
    const [actionClient] = useActionClient();

    function onOpenChange(state: boolean) {
        setState((prevState) => ({
            ...prevState,
            open: state,
            showUnlink: true
        }));
    }

    const [data, fetch, isPending, error] = useCallback(tripClient.getTripById);
    const { isInitialPending } = useInitialPending(isPending);
    useInitialLoad(async () => fetch(parentId as string));
    const tripData = data;

    const onPending = () => {
        setState((prevState) => ({
            ...prevState,
            sendButtonDisabled: true
        }));
    };

    const onComplete = () => {
        setState((prevState) => ({
            ...prevState,
            showUnlink: false,
            sendButtonDisabled: false
        }));
    };

    const onClose = () => {
        setState((prevState) => ({
            ...prevState,
            open: false,
            showUnlink: true
        }));
    };

    const [, unlinkActionToTripHandler] = useCallback(actionClient.unlink, { onComplete: onComplete, onPending: onPending });

    return (
        <Sheet
            open={state.open}
            onOpenChange={(state) => onOpenChange(state)}
        >
            {parentId && (
                <Tooltip>
                    <SheetTrigger asChild>
                        <TooltipTrigger
                            asChild
                            onClick={() => setState((prevState) => ({ ...prevState, open: true }))}
                        >
                            <Button
                                variant="ghost"
                                size="icon"
                            >
                                <Unlink className="h-4 w-4" />
                                <span className="sr-only">Unlink</span>
                            </Button>
                        </TooltipTrigger>
                    </SheetTrigger>
                    <TooltipContent>Unlink trip</TooltipContent>
                </Tooltip>
            )}
            <SheetContent className="min-w-[600px] p-0">
                <SheetHeader className="p-6">
                    <SheetTitle>Trip Link</SheetTitle>
                    <SheetDescription>You can unlink this entity from current trip.</SheetDescription>
                </SheetHeader>
                <Card className="min-w-[300px] p-0">
                    {!state.showUnlink && (
                        <CardHeader className={'gap-3'}>
                            <CardTitle className="text-lg flex flex-col items-center">
                                <CircleCheckIcon
                                    strokeWidth={1}
                                    className="w-[50px] h-[50px]"
                                />
                                <h2 className="text-2xl pt-1">Trip Unlinked</h2>
                            </CardTitle>
                        </CardHeader>
                    )}

                    {state.showUnlink && (
                        <CardContent className={'flex flex-col'}>
                            <div>
                                <Separator className={'my-2'} />
                                <div className={'flex w-full'}>
                                    <div className={'flex flex-col text-xs gap-y-1 w-[40%]'}>
                                        <div className={'flex flex-col gap-x-2'}>
                                            <CardTitle className={'text-sm'}>Selected Trip code:</CardTitle>
                                            <CardDescription className={'text-sm'}>{action?.parentEntity?.id}</CardDescription>
                                        </div>
                                    </div>
                                    <Separator
                                        orientation={'vertical'}
                                        className={'h-35 mx-6'}
                                    />
                                    <div className={'flex flex-col gap-2'}>
                                        <div className={'flex flex-col gap-x-2'}>
                                            <CardTitle className={'text-sm'}>Entitlement:</CardTitle>
                                            <CardDescription className={'text-sm'}>{}</CardDescription>
                                        </div>
                                        <div className={'flex flex-col gap-x-2'}>
                                            <CardTitle className={'text-sm'}>Vrn:</CardTitle>
                                            <CardDescription className={'text-sm'}>{tripData?.vrn}</CardDescription>
                                        </div>
                                        <div className={'flex flex-col gap-x-2'}>
                                            <CardTitle className={'text-sm'}>Customer group:</CardTitle>
                                            <CardDescription className={'text-sm'}>{tripData?.customerGroup}</CardDescription>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    )}
                    <CardFooter className="pt-6">
                        <div className={'flex flex-col gap-3 w-full'}>
                            <div className="flex flex-row w-full justify-between">
                                {state.showUnlink && (
                                    <Button
                                        variant="default"
                                        size="default"
                                        className="w-40"
                                        onClick={() => unlinkActionToTripHandler(action?._id as string)}
                                        disabled={state.sendButtonDisabled}
                                    >
                                        Unlink
                                    </Button>
                                )}
                                <Button
                                    variant="outline"
                                    size="default"
                                    onClick={() => onClose()}
                                >
                                    Close
                                </Button>
                            </div>
                        </div>
                    </CardFooter>
                </Card>
            </SheetContent>
        </Sheet>
    );
}
