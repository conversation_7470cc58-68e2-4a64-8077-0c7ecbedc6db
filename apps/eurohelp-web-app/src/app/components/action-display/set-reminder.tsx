import { Namespace, NoteEntity, NoteType, ReminderAction, Team, UserProfile } from '@aa/data-models/common';
import { Badge } from '@aa/ui/core/badge';
import { Button } from '@aa/ui/core/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@aa/ui/core/card';
import { Combobox } from '@aa/ui/core/combobox';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from '@aa/ui/core/form';
import { Input } from '@aa/ui/core/input';
import { Label } from '@aa/ui/core/label';
import { ScrollArea } from '@aa/ui/core/scroll-area';
import { Separator } from '@aa/ui/core/separator';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@aa/ui/core/sheet';
import { Switch } from '@aa/ui/core/switch';
import { Textarea } from '@aa/ui/core/textarea';
import { Tooltip, TooltipContent, TooltipTrigger } from '@aa/ui/core/tooltip';
import { useActionClient } from '@aa/ui/hooks/use-action-client';
import { useUserProfile } from '@aa/ui/hooks/use-user-profile';
import { BellPlus, CircleCheckIcon, Clock } from 'lucide-react';
import * as React from 'react';
import { FormEvent, useEffect, useState } from 'react';
import SchedulePopover from '../various/schedule-popover';
import { useNoteClient } from '@aa/ui/hooks/use-note-client';
import { useTeamClient } from '@aa/ui/hooks/use-team-client';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { useUserClient } from '@aa/ui/hooks/use-user-client';

interface SetReminderProps {
    parentEntity?: NoteEntity;
    disabled?: boolean;
    hasTrip?: boolean;
    onUpdate?: () => void;
}

interface TeamAndRecipientsDisplay {
    value: string;
    label: string;
}

export default function SetReminder({ disabled, parentEntity, hasTrip, onUpdate }: SetReminderProps) {
    const [actionClient] = useActionClient();
    const [noteClient] = useNoteClient();
    const [teamClient] = useTeamClient();
    const [userClient] = useUserClient();
    const currentDateObj = new Date();
    const futureDateObj = new Date();
    futureDateObj.setTime(currentDateObj.getTime() + 30 * 60 * 1000);

    // Merged state
    const [state, setState] = useState({
        teamMessage: false,
        assignMe: false,
        isLoading: true,
        formValues: {
            body: '',
            subject: '',
            recipient: '',
            team: '',
            activeFrom: new Date(),
            expiresAt: futureDateObj // current date + 30 mins for expiry date
        },
        isCompleted: false,
        isPending: false,
        open: false,
        isButtonDisabled: false,
        operatorIds: [9096800]
    });

    const handleStateChange = (key: string, value: any) => {
        setState((prevState) => ({
            ...prevState,
            [key]: value
        }));
    };

    const handleFormValuesChange = (key: string, value: any) => {
        setState((prevState) => ({
            ...prevState,
            formValues: {
                ...prevState.formValues,
                [key]: value
            }
        }));
    };

    function onOpenChange(openState: boolean) {
        setState((prevState) => ({
            ...prevState,
            isPending: false,
            isCompleted: false,
            open: openState
        }));
    }

    useEffect(() => {
        setState((prevState) => ({ ...prevState, isLoading: true }));
        fetchTeams().catch();
        fetchUsers(state.operatorIds).catch();
        setState((prevState) => ({ ...prevState, isLoading: false }));
    }, []);

    const handleComboChange = (value: string, property: string) => {
        handleFormValuesChange(property, value);
    };

    const handleFormChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        handleFormValuesChange(event.target.name, event.target.value);
    };

    const handleDateChange = (date: Date, fieldNameToUpdate: string) => {
        handleFormValuesChange(fieldNameToUpdate, new Date(date));
    };

    const [teamsData, fetchTeams, isTeamsPending] = useCallback(teamClient.teams);
    const { isInitialPending } = useInitialPending(isTeamsPending);
    useInitialLoad(async () => fetchTeams());
    const teams: TeamAndRecipientsDisplay[] =
        teamsData?.map((team) => ({
            value: team.shortName,
            label: team.name
        })) || [];

    const userProfile = useUserProfile();

    const [usersData, fetchUsers] = useCallback(userClient.list);
    useInitialLoad(async () => fetchUsers(state.operatorIds));
    const recipients: TeamAndRecipientsDisplay[] =
        usersData?.map((user: UserProfile) => ({
            value: user.operatorId.toString(),
            label: `${user.forename} ${user.surname}`
        })) || [];

    const onSubmit = async (event: FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        handleStateChange('isButtonDisabled', true);
        const formData = new FormData(event.currentTarget);
        formData.set('team', state.formValues.team);
        formData.set('recipient', state.formValues.recipient);

        const team = formData.get('team') as string;
        const recipient = formData.get('recipient') as string | number;
        const subject = formData.get('subject') as string;
        const body = formData.get('body') as string;

        const date = new Date();
        const action: ReminderAction = {
            type: NoteType.REMINDER,
            namespace: Namespace.EUOPS,
            title: subject,
            created: date,
            updated: date,
            content: {
                subject: subject,
                body: body,
                from: userProfile.profile.operatorId
            },
            owner: recipient,
            scheduledAt: state.formValues.activeFrom,
            expiresAt: state.formValues.expiresAt,
            parentEntity,
            context: {},
            createdBy: userProfile.profile.operatorId
        };

        if (state.assignMe) {
            action.owner = userProfile.profile.operatorId;
        }

        if (state.teamMessage) {
            action.team = team;
        }

        if (hasTrip) {
            await noteClient.insertNote(action).then(() => {
                actionClient.insert(action);
                handleStateChange('isCompleted', true);
                if (onUpdate) {
                    onUpdate();
                }
            });
        } else {
            await actionClient.insert(action);
            handleStateChange('isCompleted', true);
        }
        handleStateChange('isButtonDisabled', false);
    };

    return (
        <Sheet
            open={state.open}
            onOpenChange={(openState) => onOpenChange(openState)}
        >
            <Tooltip>
                <SheetTrigger asChild>
                    <TooltipTrigger
                        asChild
                        onClick={() => handleStateChange('open', true)}
                    >
                        <Button
                            variant="ghost"
                            size="icon"
                            disabled={disabled}
                        >
                            <BellPlus className="h-4 w-4" />
                            <span className="sr-only">Schedule action</span>
                        </Button>
                    </TooltipTrigger>
                </SheetTrigger>
                <TooltipContent>Schedule action</TooltipContent>
            </Tooltip>
            <SheetContent className="min-w-[630px] p-0">
                <SheetHeader className="p-6">
                    <SheetTitle>Create an action</SheetTitle>
                    <SheetDescription>Create an action</SheetDescription>
                </SheetHeader>
                <Separator />
                <ScrollArea className="h-[calc(100%-140px)] w-full">
                    {state.isCompleted && (
                        <ReminderCompleted
                            subject={state.formValues.subject}
                            message={state.formValues.body}
                            activeFrom={state.formValues.activeFrom.toLocaleString()}
                            expiresAt={state.formValues.expiresAt.toLocaleString()}
                            team={state.teamMessage ? state.formValues.team : ''}
                            recipient={state.teamMessage ? '' : state.formValues.recipient}
                            onClose={() => handleStateChange('open', false)}
                        />
                    )}
                    {!state.isCompleted && (
                        <div className="p-6">
                            <Card>
                                <Form onSubmit={onSubmit}>
                                    <CardContent className="pt-6 flex flex-col gap-6">
                                        <FormItem className="w-full max-w-sm flex flex-col">
                                            <FormLabel htmlFor="subject">Subject</FormLabel>
                                            <FormField name="subject">
                                                <div className="flex gap-2">
                                                    <FormControl>
                                                        <Input
                                                            className="w-[300px]"
                                                            type="text"
                                                            required
                                                            placeholder="Subject content"
                                                            onChange={handleFormChange}
                                                        />
                                                    </FormControl>
                                                </div>
                                            </FormField>
                                        </FormItem>
                                        <FormItem className="w-full max-w-sm flex flex-col">
                                            <FormLabel htmlFor="body">Message</FormLabel>
                                            <FormField name="body">
                                                <div className="flex gap-2">
                                                    <FormControl>
                                                        <Textarea
                                                            id="body"
                                                            placeholder="Type your message here"
                                                            className="min-h-[150px]"
                                                            onChange={handleFormChange}
                                                            required
                                                        />
                                                    </FormControl>
                                                </div>
                                            </FormField>
                                            <FormDescription className="text-xs text-gray-400">Your message will be copied to the support team.</FormDescription>
                                        </FormItem>
                                        <Separator />
                                        <div className="flex flex-row place-content-between w-full">
                                            <FormItem>
                                                <FormLabel htmlFor="activeFrom">Active From</FormLabel>
                                                <div className="flex flex-row">
                                                    <FormField name="activeFrom">
                                                        <FormControl>
                                                            <Input
                                                                className="w-[225px]"
                                                                type="text"
                                                                value={state.formValues.activeFrom.toLocaleString()}
                                                                readOnly
                                                                required
                                                            />
                                                        </FormControl>
                                                    </FormField>
                                                    <SchedulePopover onChange={(date) => handleDateChange(date, 'activeFrom')}>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                        >
                                                            <Clock className="h-4 w-4" />
                                                            <span className="sr-only">Set activation date</span>
                                                        </Button>
                                                    </SchedulePopover>
                                                </div>
                                                <br />
                                                <FormLabel htmlFor="expiresAt">Due By</FormLabel>
                                                <div className="flex flex-row">
                                                    <FormField name="expiresAt">
                                                        <FormControl>
                                                            <Input
                                                                className="w-[225px]"
                                                                type="text"
                                                                value={state.formValues.expiresAt.toLocaleString()}
                                                                readOnly
                                                                required
                                                            />
                                                        </FormControl>
                                                    </FormField>
                                                    <SchedulePopover onChange={(date) => handleDateChange(date, 'expiresAt')}>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                        >
                                                            <Clock className="h-4 w-4" />
                                                            <span className="sr-only">Set expiration date</span>
                                                        </Button>
                                                    </SchedulePopover>
                                                </div>
                                            </FormItem>
                                        </div>
                                        <Separator />
                                    </CardContent>
                                    <CardFooter className="pt-6">
                                        <div className="flex flex-col gap-2">
                                            <div className="flex items-center space-x-2">
                                                <div className="flex flex-col gap-2">
                                                    <div className="flex items-center space-x-2">
                                                        <Switch
                                                            id="create-task"
                                                            onClick={() => handleStateChange('assignMe', !state.assignMe)}
                                                        />
                                                        <Label htmlFor="create-task">Assign action to me</Label>
                                                    </div>
                                                    {!state.assignMe && (
                                                        <div className="flex items-center space-x-2">
                                                            <Switch
                                                                id="create-task"
                                                                onClick={() => handleStateChange('teamMessage', !state.teamMessage)}
                                                            />
                                                            <Label htmlFor="create-task">Team message</Label>
                                                        </div>
                                                    )}
                                                    {state.teamMessage && !state.assignMe && (
                                                        <FormItem className="w-full max-w-sm flex flex-col">
                                                            <FormLabel htmlFor="team">Team</FormLabel>

                                                            <FormField name="team">
                                                                <div className="flex gap-2">
                                                                    <FormControl>
                                                                        <Combobox
                                                                            items={teams}
                                                                            notFoundLabel="No team found"
                                                                            noValueLabel="Select team"
                                                                            searchPlaceholder="Search for team..."
                                                                            onSelect={(val) => handleComboChange(val, 'team')}
                                                                        />
                                                                    </FormControl>
                                                                </div>
                                                            </FormField>
                                                        </FormItem>
                                                    )}
                                                    {!state.teamMessage && !state.assignMe && (
                                                        <FormItem className="w-full max-w-sm flex flex-col">
                                                            <FormLabel htmlFor="recipient">Recipient</FormLabel>

                                                            <FormField name="recipient">
                                                                <div className="flex gap-2">
                                                                    <FormControl>
                                                                        <Combobox
                                                                            items={recipients}
                                                                            notFoundLabel="No recipient found"
                                                                            noValueLabel="Select recipient"
                                                                            searchPlaceholder="Search for recipient..."
                                                                            onSelect={(val) => handleComboChange(val, 'recipient')}
                                                                        />
                                                                    </FormControl>
                                                                </div>
                                                            </FormField>
                                                        </FormItem>
                                                    )}
                                                </div>
                                            </div>
                                            <br />
                                            <div className="flex flex-row place-content-between w-full">
                                                <Button
                                                    type="submit"
                                                    variant="default"
                                                    size="default"
                                                    className="w-40"
                                                    disabled={state.isButtonDisabled}
                                                >
                                                    Create an Action
                                                </Button>
                                            </div>
                                        </div>
                                    </CardFooter>
                                </Form>
                            </Card>
                        </div>
                    )}
                </ScrollArea>
            </SheetContent>
        </Sheet>
    );
}

interface ReminderSetProps {
    subject: string;
    message: string;
    activeFrom: string;
    expiresAt: string;
    team: string;
    recipient: string;
    onClose: () => void;
}

function ReminderCompleted({ subject, message, activeFrom, expiresAt, team, recipient, onClose }: ReminderSetProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg flex flex-col items-center">
                    <CircleCheckIcon
                        strokeWidth={1}
                        className="w-[50px] h-[50px]"
                    />
                    <h2 className="text-2xl pt-1">Action Summary</h2>
                </CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6 flex flex-col gap-6">
                <ReminderSummary
                    subject={subject}
                    message={message}
                    activeFrom={activeFrom}
                    expiresAt={expiresAt}
                    team={team}
                    recipient={recipient}
                />
            </CardContent>
            <Separator />
            <CardFooter className="pt-6">
                <div className="flex flex-row justify-center w-full">
                    <Button
                        variant="outline"
                        size="default"
                        className="w-40"
                        onClick={() => onClose()}
                    >
                        Close
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
}

interface ReminderSummaryProps {
    subject: string;
    message: string;
    activeFrom: string;
    expiresAt: string;
    team: string;
    recipient: string;
}

function ReminderSummary({ subject, message, activeFrom, expiresAt, team, recipient }: ReminderSummaryProps) {
    return (
        <div className="flex items-stretch justify-center gap-10">
            <div className="flex flex-col gap-2">
                <div>
                    <span className="text-sm pr-2">Subject</span>
                    <Badge
                        className="w-fit"
                        variant="secondary"
                    >
                        {subject}
                    </Badge>
                </div>
                <div>
                    <span className="text-sm pr-2">Message</span>
                    <Badge
                        className="w-fit"
                        variant="secondary"
                    >
                        {message}
                    </Badge>
                </div>
                <div>
                    <span className="text-sm pr-2">Active From</span>
                    <Badge
                        className="w-fit"
                        variant="secondary"
                    >
                        {activeFrom}
                    </Badge>
                </div>
                <div>
                    <span className="text-sm pr-2">Due By</span>
                    <Badge
                        className="w-fit"
                        variant="secondary"
                    >
                        {expiresAt}
                    </Badge>
                </div>
                {team && (
                    <div>
                        <span className="text-sm pr-2">Sent to Team</span>
                        <Badge
                            className="w-fit"
                            variant="secondary"
                        >
                            {team}
                        </Badge>
                    </div>
                )}
                {recipient && (
                    <div>
                        <span className="text-sm pr-2">Sent to</span>
                        <Badge
                            className="w-fit"
                            variant="secondary"
                        >
                            {recipient}
                        </Badge>
                    </div>
                )}
            </div>
        </div>
    );
}
