import { Action, WithDataId } from '@aa/data-models/common';
import { Button } from '@aa/ui/core/button';
import { Separator } from '@aa/ui/core/separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@aa/ui/core/tooltip';
import { useUserProfile } from '@aa/ui/hooks/use-user-profile';
import { Forward, LockIcon, LockOpenIcon, Reply } from 'lucide-react';
import * as React from 'react';
import { useEffect, useState } from 'react';
import LinkAction from './link-action';
import SetReminder from './set-reminder';
import SendMessage from './send-message';
import SnoozeAction from './snooze-action';
import UnlinkSummary from './unlink-action';
import CloseActionAction from './close_action-action';
import { useMessageClient } from '@aa/ui/hooks/use-message-client';

interface ActionToolbarProps {
    action: WithDataId<Action>;
    onArchive: () => void;
    onClaim: () => void;
    onRelease: () => void;
    onEmailReply: () => void;
    onEmailForward: () => void;
}

export function ActionToolbar({ action, onArchive, onClaim, onRelease, onEmailReply, onEmailForward }: ActionToolbarProps) {
    const userProfile = useUserProfile();
    const [state, setState] = useState({
        claimedByMe: false,
        statusCompleted: true
    });

    useEffect(() => {
        if (action) {
            const status = (action as any).status;
            setState({
                claimedByMe: action.owner === userProfile.profile.operatorId,
                statusCompleted: status === 'COMPLETED'
            });
        }
    }, [action, userProfile.profile]);

    return (
        <div className="flex items-center p-2  bg-background">
            <div className="flex items-center gap-2">
                <LinkAction action={action} />
                <UnlinkSummary action={action} />
                <Separator
                    orientation="vertical"
                    className="mx-1 h-6"
                />
                {/* <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            variant="ghost"
                            size="icon"
                            disabled={!action.parentEntity?.id || !claimedByMe}
                            onClick={() => onArchive()}
                        >
                            <Archive className="h-4 w-4" />
                            <span className="sr-only">Archive</span>
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>Archive</TooltipContent>
                </Tooltip> */}

                <CloseActionAction
                    action={action}
                    disabled={!state.claimedByMe || state.statusCompleted}
                />

                {/*TODO: Dunno if we need this, to be reworked*/}
                {/*<Tooltip>*/}
                {/*    <TooltipTrigger asChild>*/}
                {/*        <Button variant="ghost" size="icon" disabled={!action || !isAssignedToMe}>*/}
                {/*            <Trash2 className="h-4 w-4" />*/}
                {/*            <span className="sr-only">Move to trash</span>*/}
                {/*        </Button>*/}
                {/*    </TooltipTrigger>*/}
                {/*    <TooltipContent>Move to trash</TooltipContent>*/}
                {/*</Tooltip>*/}
                <Separator
                    orientation="vertical"
                    className="mx-1 h-6"
                />
                {state.claimedByMe ? (
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                variant="ghost"
                                size="icon"
                                disabled={!action}
                                onClick={() => onRelease()}
                            >
                                <LockOpenIcon className="h-4 w-4" />
                                <span className="sr-only">Release</span>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Release</TooltipContent>
                    </Tooltip>
                ) : (
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                variant="ghost"
                                size="icon"
                                disabled={!action}
                                onClick={() => onClaim()}
                            >
                                <LockIcon className="h-4 w-4" />
                                <span className="sr-only">Claim</span>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Claim</TooltipContent>
                    </Tooltip>
                )}

                <SnoozeAction
                    action={action}
                    disabled={!state.claimedByMe}
                />
                <Separator
                    orientation="vertical"
                    className="mx-1 h-6"
                />
                <SendMessage
                    parentEntity={action.parentEntity}
                    replyTo={action}
                    disabled={!state.claimedByMe}
                    hasTrip={false}
                />
                <Separator
                    orientation="vertical"
                    className="mx-1 h-6"
                />
                <SetReminder
                    parentEntity={action.parentEntity}
                    disabled={!state.claimedByMe}
                    hasTrip={false}
                />
            </div>
            {/* commented as no action exists on button for Verification */}
            <div className="ml-auto flex items-center gap-2">
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            variant="ghost"
                            size="icon"
                            disabled={!action}
                            onClick={() => onEmailReply()}
                        >
                            <Reply className="h-4 w-4" />
                            <span className="sr-only">Reply</span>
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>Reply</TooltipContent>
                </Tooltip>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            variant="ghost"
                            size="icon"
                            disabled={!action}
                            onClick={() => onEmailForward()}
                        >
                            <Forward className="h-4 w-4" />
                            <span className="sr-only">Forward</span>
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>Forward</TooltipContent>
                </Tooltip>
            </div>
            <Separator
                orientation="vertical"
                className="mx-2 h-6"
            />
        </div>
    );
}
