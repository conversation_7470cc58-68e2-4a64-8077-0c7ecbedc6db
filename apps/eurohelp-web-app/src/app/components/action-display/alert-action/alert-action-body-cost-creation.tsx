import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>es<PERSON>, She<PERSON><PERSON>eader, Sheet<PERSON>itle, SheetTrigger } from '@aa/ui/core/sheet';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@aa/ui/core/card';
import { But<PERSON> } from '@aa/ui/core/button';
import { Separator } from '@aa/ui/core/separator';
import React, { useState } from 'react';
import { AlertAction, Currency, NoteEntity, NoteEntityType, Trip, TripCost, TripCostType, WithDataId } from '@aa/data-models/common';
import { SelectSupplierPanel } from '../../../routes/accounting/components/invoices/components/invoice-panel/components/invoice-details-tab/components/edit-invoice/components/select-supplier-panel';
import { ChooseCurrency } from '../../shared/choose-currency';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import { ChooseBenefit } from '../../shared/choose-benefit';
import { Input } from '@aa/ui/core/input';
import { useBillingClient } from '@aa/ui/hooks/use-billing-client';
import { CircleCheckIcon } from 'lucide-react';
import { format } from 'date-fns';
import { MonetaryValueSummary } from '../../../routes/accounting/components/invoices/components/invoice-panel/components/invoice-details-tab/components/invoice-details/components/monetary-value-summary';
import { formatDistanceToNow } from 'date-fns/formatDistanceToNow';
import { Preloader } from '@aa/ui/core/preloader';
import { useTaskClient } from '@aa/ui/hooks/use-task-client';

interface AlertActionBodyCostCreationProps {
    action: WithDataId<AlertAction> | null;
    onChange?: (trip: Trip) => void;
    taskId: number;
}

export const AlertActionBodyCostCreation = ({ action, onChange, taskId }: AlertActionBodyCostCreationProps) => {
    const [state, setState] = useState({
        open: false,
        isCompleted: false,
        tripCostData: {} as TripCost,
        isNewCostCreated: false,
        newTripCost: {} as WithDataId<TripCost>,
        isEditing: false,
        supplierId: null as number | null
    });

    // Find tripCode in action
    const tripCode = action?.context?.entities?.find((entity: NoteEntity) => entity.type === NoteEntityType.TRIP)?.id;
    const [tripClient] = useTripClient();
    const [billingClient] = useBillingClient();
    const [taskClient] = useTaskClient();
    const [data, fetch, isPending, error] = useCallback(tripClient.getTripById);
    const { isInitialPending } = useInitialPending(isPending);
    useInitialLoad(async () => fetch(tripCode ? tripCode : ''));
    const trip = data as Trip;

    const onUpsertComplete = (data: TripCost) => {
        setState((prevState) => ({
            ...prevState,
            newTripCost: data as WithDataId<TripCost>,
            isNewCostCreated: true,
            supplierId: data.supplierResourceId,
            tripCostData: data,
            isEditing: false
        }));
    };

    const [, createHandler] = useCallback(billingClient.createCost, { onComplete: onUpsertComplete });
    const [, updateHandler] = useCallback(billingClient.updateCost, { onComplete: onUpsertComplete });

    const [cr, fetchCrById] = useCallback(taskClient.getCustomerRequestIdByTaskId);
    useInitialLoad(async () => fetchCrById(taskId));

    const onOpenChange = (state: boolean) => {
        setState((prevState) => ({
            ...prevState,
            open: state
        }));
    };

    const onSubmit = async () => {
        if (state.isEditing) {
            const { _id, ...updatedTripCost } = state.newTripCost;
            const updatedTripCostData = {
                ...updatedTripCost,
                name: state.tripCostData.name,
                supplierId: state.supplierId,
                currency: state.tripCostData.currency,
                type: TripCostType.SERVICE_COST,
                benefitLimitId: state.tripCostData.benefitLimitId,
                tripCode,
                forecast: {
                    ...state.tripCostData.forecast,
                    currency: state.tripCostData.currency
                },
                taskId: taskId,
                reconciled: false,
                customerRequestId: cr
            };
            await updateHandler(state.tripCostData.code, updatedTripCostData as TripCost);
        } else {
            const newTripCost = {
                ...state.tripCostData,
                supplierId: state.supplierId,
                type: TripCostType.SERVICE_COST,
                tripCode,
                forecast: {
                    ...state.tripCostData.forecast,
                    currency: state.tripCostData.currency
                },
                taskId: taskId,
                reconciled: false,
                customerRequestId: cr
            };
            await createHandler(newTripCost as TripCost);
        }
    };

    const onSupplierChange = (supplierResourceId: number | null) => {
        setState((prevState) => ({
            ...prevState,
            tripCostData: {
                ...prevState.tripCostData,
                supplierResourceId: supplierResourceId || -1
            },
            supplierId: supplierResourceId
        }));
    };

    const onCurrencyChange = (currency: Currency) => {
        setState((prevState) => ({
            ...prevState,
            tripCostData: {
                ...prevState.tripCostData,
                currency: currency || Currency.GBP
            }
        }));
    };

    const onBenefitChange = (benefitLimitId: string) => {
        setState((prevState) => ({
            ...prevState,
            tripCostData: {
                ...prevState.tripCostData,
                benefitLimitId: benefitLimitId ? benefitLimitId : ''
            }
        }));
    };

    const handleFormChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setState((prevState) => ({
            ...prevState,
            tripCostData: {
                ...prevState.tripCostData,
                [event.target.name]: event.target.value
            }
        }));
    };

    const handleNetValueChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setState((prevState) => ({
            ...prevState,
            tripCostData: {
                ...prevState.tripCostData,
                forecast: {
                    ...prevState.tripCostData.forecast,
                    amount: Number(event.target.value)
                }
            }
        }));
    };

    if (isInitialPending) {
        return (
            <Card>
                <CardContent className="flex pt-6">
                    <Preloader />
                </CardContent>
            </Card>
        );
    }

    return (
        <Sheet
            open={state.open}
            onOpenChange={(state) => onOpenChange(state)}
        >
            <Card className="flex justify-between items-center min-w-[600px] mt-2 w-full">
                <CardHeader>
                    <CardTitle className="text-lg font-bold text-black">No cost created</CardTitle>
                    <CardDescription className="text-xs">Please create cost</CardDescription>
                </CardHeader>

                <SheetTrigger
                    asChild
                    className="mr-6"
                >
                    <Button variant="outline">{'Create cost'}</Button>
                </SheetTrigger>
            </Card>
            {(!state.isNewCostCreated || state.isEditing) && (
                <SheetContent className="min-w-[650px] bg-gray-100 overflow-y-scroll">
                    <SheetHeader>
                        <SheetTitle>Cost</SheetTitle>
                        <SheetDescription>Please provide detadils for cost</SheetDescription>
                    </SheetHeader>

                    <Separator className="my-5" />

                    <Card className="p-0">
                        <CardHeader className={'p-6'}>
                            <CardDescription className="text-sm">To create new cost we need few details.</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <p className={'text-sm py-1 font-medium'}>Supplier</p>
                            <SelectSupplierPanel
                                supplierResourceId={state.supplierId || null}
                                onChange={onSupplierChange}
                            />
                            <p className={'text-sm py-2 font-medium'}>Currency</p>
                            <ChooseCurrency onChange={(currency) => onCurrencyChange(currency)} />
                            <p className={'text-xs text-slate-500'}>Select main currency for cost.</p>
                            <p className={'text-sm py-2 font-medium'}>Benefit</p>
                            <ChooseBenefit
                                benefitLimits={trip?.benefitLimits || []}
                                onChange={onBenefitChange}
                            />
                            <p className={'text-xs text-slate-500'}>Select one of available trip benefits</p>
                            <p className={'text-sm py-2 font-medium'}>Cost reference</p>
                            <Input
                                className="w-full"
                                type="text"
                                name={'name'}
                                onChange={handleFormChange}
                                placeholder="Ref number"
                                value={state.tripCostData?.name}
                            />
                            <p className={'text-sm py-2 font-medium'}>Gross value</p>
                            <Input
                                className="w-full"
                                type="text"
                                name={'forecast.amount'}
                                onChange={handleNetValueChange}
                                placeholder="0"
                                value={state.tripCostData?.forecast?.amount || 0}
                            />
                            <Separator className="my-5" />
                            <Button
                                className="w-40"
                                type="submit"
                                variant={'default'}
                                size="default"
                                disabled={state.isCompleted}
                                onClick={onSubmit}
                            >
                                Create cost
                            </Button>
                        </CardContent>
                    </Card>
                </SheetContent>
            )}
            {state.isNewCostCreated && !state.isEditing && (
                <SheetContent className="min-w-[650px] bg-gray-100 overflow-y-scroll">
                    <SheetHeader>
                        <SheetTitle>Cost</SheetTitle>
                        <SheetDescription>Please provide details for cost</SheetDescription>
                    </SheetHeader>

                    <Separator className="my-5" />

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg flex flex-col items-center">
                                <CircleCheckIcon
                                    strokeWidth={1}
                                    className="w-[50px] h-[50px]"
                                />
                                <h2 className="text-2xl pt-1">Cost created</h2>
                            </CardTitle>
                        </CardHeader>
                        <Separator />
                        <CardContent className="pt-6 flex flex-col gap-6">
                            <AlertActionBodyCostCreated tripCost={state.newTripCost} />
                            <Separator className="my-1" />
                            <div className="flex justify-between w-full">
                                <div className="flex items-center space-x-2">
                                    <Button
                                        variant="outline"
                                        onClick={() => setState((prevState) => ({ ...prevState, isEditing: true }))}
                                    >
                                        Edit cost
                                    </Button>
                                    <span className="text-xs text-muted-foreground">
                                        Last edited {formatDistanceToNow(new Date(state.newTripCost.created))} {' ago'}
                                    </span>
                                </div>
                                <Button
                                    variant="outline"
                                    onClick={() => setState((prevState) => ({ ...prevState, open: false }))}
                                >
                                    Close
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </SheetContent>
            )}
        </Sheet>
    );
};

interface AlertActionBodyCostCreatedProps {
    tripCost: TripCost;
}

const AlertActionBodyCostCreated = ({ tripCost }: AlertActionBodyCostCreatedProps) => {
    return (
        <div className="flex items-stretch justify-center gap-10">
            <div className={'flex flex-col w-full'}>
                <div className="flex justify-between">
                    <span className={'text-lg font-bold'}>{tripCost.name}</span>
                    <span className="text-sm bg-gray-400 rounded-full border-1 m-0 p-1">{'SERVICE FEE'}</span>
                </div>
                <div className="flex justify-start text-sm text-slate-500">
                    <span>Created: {format(tripCost.created, 'dd-MM-yyyy HH:mm')}</span>
                </div>
                <Separator className={'my-6'} />
                <div className="flex flex-col w-full text-sm space-y-2">
                    <div className="flex justify-between">
                        <span>Invoice supplier:</span>
                        <span className="font-semibold">{tripCost.supplierResourceId}</span>
                    </div>
                    <div className="flex justify-between">
                        <span>Currency</span>
                        <span className="font-semibold">{tripCost.currency}</span>
                    </div>
                    <div className="flex justify-between">
                        <span>Benefit type</span>
                        <span className="font-semibold">{tripCost.benefitLimitId}</span>
                    </div>
                </div>
                <Separator className={'my-6'} />
                <div className="flex flex-col w-full text-sm space-y-2">
                    <MonetaryValueSummary
                        net={undefined}
                        vat={undefined}
                        gross={tripCost.forecast}
                        grossNative={tripCost.forecastNative}
                    />
                </div>
            </div>
        </div>
    );
};
