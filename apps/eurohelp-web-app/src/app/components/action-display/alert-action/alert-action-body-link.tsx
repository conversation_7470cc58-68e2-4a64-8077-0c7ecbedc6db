import { Sheet, Sheet<PERSON>ontent, SheetDes<PERSON>, Sheet<PERSON>eader, Sheet<PERSON>itle, SheetTrigger } from '@aa/ui/core/sheet';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@aa/ui/core/card';
import { But<PERSON> } from '@aa/ui/core/button';
import { Separator } from '@aa/ui/core/separator';
import { DataTable } from '@aa/ui/hoc/data-table/data-table';
import { DataTableToolbar } from '@aa/ui/hoc/data-table/data-table-toolbar';
import { DataTableBody } from '@aa/ui/hoc/data-table/data-table-body';
import { DataTablePagination } from '@aa/ui/hoc/data-table/data-table-pagination';
import React, { useEffect, useState } from 'react';
import { AlertAction, ExtendedPaginationQueryResult, NoteEntityType, PaginationQuery, QueryTrip, Trip, WithDataId } from '@aa/data-models/common';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import type { ColumnDef } from '@tanstack/react-table';
import { useDataColumns } from '@aa/ui/hooks/use-data-columns';
import { actionTripLinkListColumns, textFilters } from './alert-action-body-link-table-definitions';
import { DataColumnFiltersState } from '@aa/ui/providers/data-table-provider';
import { DataTableColumnHeader } from '@aa/ui/hoc/data-table/data-table-column-header';
import { SelectRowActions } from '../../shared/select-row-action';
import { useActionClient } from '@aa/ui/hooks/use-action-client';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@aa/ui/core/tabs';
import { cn } from '@aa/ui/lib/utils';
import { NewTrip } from '../link-action';

interface AlertActionBodyLinkProps {
    action: WithDataId<AlertAction> | null;
    onChange?: (trip: Trip) => void;
    taskId: number;
}

export const AlertActionBodyLink = ({ action, onChange, taskId }: AlertActionBodyLinkProps) => {
    const [state, setState] = useState({
        open: false,
        isCompleted: false,
        code: '',
        linkedTrip: null as Trip | null,
        query: { limit: 10, skip: 0, code: '' } as QueryTrip
    });

    const [tripClient] = useTripClient();
    const [actionClient] = useActionClient();
    const [data, fetch, isPending, error] = useCallback(tripClient.queryTrips);
    const { isInitialPending } = useInitialPending(isPending);
    useInitialLoad(async () => fetch(state.query));
    const { results: trips, ...pagination }: ExtendedPaginationQueryResult<Trip> = data || {
        results: [],
        more: false,
        totalPages: 0,
        skip: 0,
        limit: 10,
        total: 0
    };

    useEffect(() => {
        fetch(state.query).catch();
    }, [state.query]);

    const [, linkTaskToTripHandler] = useCallback(tripClient.linkTaskToTrip);
    const [, linkActionToTripHandler] = useCallback(actionClient.link);

    const onChangeTrip = (row: Trip) => {
        setState((prevState) => ({
            ...prevState,
            linkedTrip: row
        }));
        linkTaskToTripHandler(row.code, taskId).catch();
        linkActionToTripHandler(action?._id || '', {
            type: NoteEntityType.TRIP,
            id: row?.code || ''
        }).catch();
        setState((prevState) => ({
            ...prevState,
            open: false
        }));
    };

    const onPaginationChange = (state: PaginationQuery) => {
        setState((prevState) => ({
            ...prevState,
            query: { ...prevState.query, ...state }
        }));
    };

    const onColumnFiltersChange = (state: DataColumnFiltersState<Trip>) => {
        let mappedState = state.reduce((result: any, obj) => {
            if (obj) {
                result[obj.id] = obj.value;
            }
            return result;
        }, {});

        if (Object.keys(mappedState).length === 0) {
            mappedState = {
                code: state.find((filter) => filter?.id === 'code')?.value || ''
            };
        }

        setState((prevState) => ({
            ...prevState,
            query: { ...prevState.query, ...mappedState }
        }));
    };

    const onOpenChange = (state: boolean) => {
        setState((prevState) => ({
            ...prevState,
            open: state,
            isCompleted: false
        }));
    };

    const actionColumn: ColumnDef<Trip> = {
        accessorKey: 'actions',
        header: ({ column }) => (
            <DataTableColumnHeader
                column={column}
                title="Actions"
            />
        ),
        cell: ({ row }) => (
            <div className="w-[80px]">
                <SelectRowActions
                    row={row}
                    onClick={(row) => onChangeTrip(row.original)}
                />
            </div>
        ),
        enableSorting: false,
        enableHiding: false
    };

    const [columns] = useDataColumns(actionTripLinkListColumns, [actionColumn]);

    return (
        <Sheet
            open={state.open}
            onOpenChange={(state) => onOpenChange(state)}
        >
            {action?.parentEntity?.id ? (
                <Card className={'min-w-[100%] mt-2'}>
                    <CardHeader className={'flex flex-col'}>
                        <div>
                            <div className={'flex w-full'}>
                                <div className={'flex justify-between text-xs gap-y-1 w-full'}>
                                    <div className={'flex flex-col gap-x-2'}>
                                        <CardTitle className={'text-sm'}>Selected Trip code:</CardTitle>
                                        <CardDescription className={'text-sm'}>{action?.parentEntity?.id}</CardDescription>
                                    </div>
                                    <div className="flex">
                                        <Button
                                            variant="outline"
                                            onClick={() => onOpenChange(true)}
                                        >
                                            Change trip
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                </Card>
            ) : (
                <Card className="flex justify-between items-center min-w-[600px] mt-2">
                    <CardHeader>
                        <CardDescription className="text-xs">No trip selected</CardDescription>
                    </CardHeader>

                    <SheetTrigger
                        asChild
                        className="mr-6"
                    >
                        <Button variant="outline">{'Select trip'}</Button>
                    </SheetTrigger>
                </Card>
            )}
            <SheetContent className="min-w-[650px] bg-gray-100 overflow-y-scroll">
                <SheetHeader>
                    <SheetTitle>Select trip</SheetTitle>
                    <SheetDescription>Please select required trip from the list below.</SheetDescription>
                </SheetHeader>

                <Separator className="my-5" />

                <Card className="p-6">
                    <Tabs
                        defaultValue="new-trip"
                        className={cn('flex flex-col h-full')}
                    >
                        <div className="flex py-2">
                            <TabsList>
                                <TabsTrigger
                                    disabled={state.isCompleted || isPending}
                                    value="new-trip"
                                    className="text-zinc-600 dark:text-zinc-200"
                                >
                                    Link new trip
                                </TabsTrigger>
                                <TabsTrigger
                                    disabled={state.isCompleted || isPending}
                                    value="existing-trip"
                                    className="text-zinc-600 dark:text-zinc-200"
                                >
                                    Link existing trip
                                </TabsTrigger>
                            </TabsList>
                        </div>
                        <div className="flex w-full">
                            <TabsContent
                                value="new-trip"
                                className="m-0 w-full"
                            >
                                <NewTrip
                                    onComplete={(state: boolean) => setState((prevState) => ({ ...prevState, isCompleted: state }))}
                                    onClose={() => setState((prevState) => ({ ...prevState, open: false }))}
                                    action={action}
                                />
                            </TabsContent>
                            <TabsContent
                                value="existing-trip"
                                className="m-0 w-full"
                            >
                                <CardContent className="flex flex-col space-y-4 p-0">
                                    <DataTable
                                        data={trips}
                                        pagination={pagination}
                                        isDataPending={isInitialPending}
                                        columns={columns}
                                        onPagination={onPaginationChange}
                                        onColumnFilters={onColumnFiltersChange}
                                    >
                                        <DataTableToolbar textFilters={textFilters}></DataTableToolbar>
                                        <DataTableBody className="rounded-md border h-[calc(100%-150px)]" />
                                        <DataTablePagination className={'mt-2'} />
                                    </DataTable>
                                </CardContent>
                            </TabsContent>
                        </div>
                    </Tabs>
                </Card>
            </SheetContent>
        </Sheet>
    );
};
