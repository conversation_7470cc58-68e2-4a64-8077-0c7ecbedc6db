import { Action, NoteType, UserProfile } from '@aa/data-models/common';
import { ScrollArea } from '@aa/ui/core/scroll-area';
import { Separator } from '@aa/ui/core/separator';
import { TooltipProvider } from '@aa/ui/core/tooltip';
import { FileSearch2 } from 'lucide-react';
import { ReactElement, useEffect, useState } from 'react';
import NotFoundRoute from '../../routes/not-found/not-found-route';
import { ActionDetailsHeader } from './action-details-header';
import { ActionHeaderLockDetails } from './action-header-lock-details';
import { ActionToolbar } from './action-toolbar';
import AlertActionBody from './alert-action-body';
import EmailActionBody from './email-action-body';
import EventActionBody from './event-action-body';
import MessageActionBody from './message-action-body';
import ReminderActionBody from './reminder-action-body';
import { ActionHeaderSlaDetails } from './action-header-sla-details';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useActionClient } from '@aa/ui/hooks/use-action-client';
import { useNotifications } from '@aa/ui/hooks/use-notifications';
import { Source } from '@aa/data-models/system/source';
import { DataQuery } from '@aa/data-query';
import { useChange } from '@aa/ui/hooks/use-change';
import * as React from 'react';
import { Preloader } from '@aa/ui/core/preloader';

interface ActionDisplayProps {
    actionId: string | undefined;
    onArchive: () => void;
    onClaim: () => void;
    onRelease: () => void;
    users: UserProfile[] | null;
}

export const ActionDisplay = (props: ActionDisplayProps) => {
    const [state, setState] = useState({
        owner: null as UserProfile | null,
        assignedBy: null as UserProfile | null,
        isReplyPanelVisible: false,
        isForwardPanelVisible: false
    });

    const [actionClient] = useActionClient();
    const [action, fetchAction, isPending, error] = useCallback(actionClient.get);
    const onChange = () => {
        if (props.actionId) {
            getActionData();
        }
    };

    const getActionData = async () => {
        if (props.actionId) {
            await fetchAction(props.actionId).catch();
        }
    };

    const query = new DataQuery<Action>({ _id: props.actionId });
    const { resubscribe } = useNotifications({ source: Source.ACTION_QUEUE, query }, { onChange, onConnect: onChange });
    useChange(props.actionId, resubscribe);

    const [, archiveActionHandler] = useCallback(actionClient.complete);

    const handleArchive = async () => {
        if (action?._id) {
            try {
                await archiveActionHandler(action._id, undefined);
                props.onArchive();
            } catch (error) {
                console.error('Error archiving action:', error);
            }
        }
    };

    const handleEmailCancel = () => {
        setState((prevState) => ({
            ...prevState,
            isReplyPanelVisible: false,
            isForwardPanelVisible: false
        }));
    };

    const handleEmailReply = async () => {
        setState((prevState) => ({
            ...prevState,
            isReplyPanelVisible: true,
            isForwardPanelVisible: false
        }));
    };

    const handleEmailForward = async () => {
        setState((prevState) => ({
            ...prevState,
            isForwardPanelVisible: true,
            isReplyPanelVisible: false
        }));
    };

    useEffect(() => {
        if (action && props.users) {
            const assignedByUser = props.users.find((user) => user.operatorId === action?.owner);
            setState((prevState) => ({
                ...prevState,
                owner: assignedByUser || null
            }));
        }
    }, [action, props.users]);

    if (!action) {
        return (
            <div className="mx-auto flex h-full w-full items-center bg-muted flex-col text-center justify-center space-y-6">
                <Preloader />
            </div>
        );
    }

    let body: ReactElement;
    switch (action.type) {
        case NoteType.MESSAGE:
            body = <MessageActionBody action={action} />;
            break;
        case NoteType.EMAIL:
            body = (
                <EmailActionBody
                    action={action}
                    replyPanelVisible={state.isReplyPanelVisible}
                    forwardPanelVisible={state.isForwardPanelVisible}
                    onEmailCancel={handleEmailCancel}
                />
            );
            break;
        case NoteType.EVENT:
            body = <EventActionBody action={action} />;
            break;
        case NoteType.REMINDER:
            body = <ReminderActionBody action={action} />;
            break;
        case NoteType.ALERT:
            body = <AlertActionBody action={action} />;
            break;
        default:
            return (
                <TooltipProvider delayDuration={0}>
                    <div className="flex h-full flex-col">
                        <NotFoundRoute />
                    </div>
                </TooltipProvider>
            );
    }

    return (
        <TooltipProvider delayDuration={0}>
            <div className="flex h-full flex-col bg-muted">
                <ActionDetailsHeader action={action} />
                <Separator />
                {action.type !== 'EMAIL' && (
                    <>
                        <ActionHeaderSlaDetails action={action} />
                    </>
                )}
                {state.owner && (
                    <>
                        <ActionHeaderLockDetails
                            action={action}
                            owner={state.owner}
                            assignedBy={state.assignedBy}
                        />
                        <Separator />
                    </>
                )}
                <ActionToolbar
                    action={action}
                    onArchive={handleArchive}
                    onClaim={props.onClaim}
                    onRelease={props.onRelease}
                    onEmailReply={handleEmailReply}
                    onEmailForward={handleEmailForward}
                />
                <Separator />
                <ScrollArea>
                    <div className="p-4 text-sm bg-gray-100">{body}</div>
                </ScrollArea>
            </div>
        </TooltipProvider>
    );
};
