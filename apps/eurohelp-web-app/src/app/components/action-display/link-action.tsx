import { Action, CommsChannel, CommsRoute, EuopsCustomerGroup, isEmailAction, isSparxMessageAction, NoteContextData, NoteEntityType, ProductPolicy, Trip, WithDataId } from '@aa/data-models/common';

import { ActionEntityType, getWorkerActionCreateEvent, SendableWorkerCommsEvent } from '@aa/events/worker-events';
import { FrontendApplication } from '@aa/identifiers';
import { Alert, AlertDescription, AlertTitle } from '@aa/ui/core/alert';
import { Button } from '@aa/ui/core/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@aa/ui/core/card';
import { Combobox } from '@aa/ui/core/combobox';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@aa/ui/core/form';
import { Input } from '@aa/ui/core/input';
import { Label } from '@aa/ui/core/label';
import { Preloader } from '@aa/ui/core/preloader';
import { ScrollArea } from '@aa/ui/core/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@aa/ui/core/select';
import { Separator } from '@aa/ui/core/separator';
import { Sheet, SheetClose, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@aa/ui/core/sheet';
import { Switch } from '@aa/ui/core/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@aa/ui/core/tabs';
import { Tooltip, TooltipContent, TooltipTrigger } from '@aa/ui/core/tooltip';
import { useActionClient } from '@aa/ui/hooks/use-action-client';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useChange } from '@aa/ui/hooks/use-change';
import { useCommsWorkerClient } from '@aa/ui/hooks/use-comms-worker-client';
import { useEntitlementClient } from '@aa/ui/hooks/use-entitlement-client';
import { useEurohelpClient } from '@aa/ui/hooks/use-eurohelp-client';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useTripClient } from '@aa/ui/hooks/use-trip-client';
import { cn } from '@aa/ui/lib/utils';
import { CircleCheckIcon, InfoIcon, LinkIcon } from 'lucide-react';
import * as React from 'react';
import { ChangeEvent, FormEvent, useEffect, useState } from 'react';
import { useTrips } from '../../utils/hooks/use-trips';
import { CreateRSSTask } from '../trips/create-rss-task';
import { TripEdit } from '../trips/edit-trip/trip-edit';
import { LinkActionDataTable } from './link-action-table/data-table';
import { LinkActionWithTripColumns } from './link-action-with-trip-columns';

interface LinkActionbarProps {
    action: WithDataId<Action> | null;
}

interface LinkActionState {
    isCompleted: boolean;
    isPending: boolean;
    open: boolean;
    isParent: string | undefined | number;
}

export const commsChannels = [
    { code: CommsChannel.SPARX, name: 'ARC Sparx' },
    { code: CommsChannel.EMAIL, name: 'Eurohelp Email' }
];

export default function LinkAction({ action }: LinkActionbarProps) {
    const [state, setState] = useState<LinkActionState>({
        isCompleted: false,
        isPending: false,
        open: false,
        isParent: action?.parentEntity?.id
    });

    const onOpenChange = (state: boolean) => {
        setState((current) => ({
            ...current,
            open: state,
            isPending: false,
            isCompleted: false
        }));
    };

    const setOpen = (state: boolean) => {
        setState((current) => ({
            ...current,
            open: state
        }));
    };

    const setIsPending = (state: boolean) => {
        setState((current) => ({
            ...current,
            isPending: state
        }));
    };

    const setIsCompleted = (state: boolean) => {
        setState((current) => ({
            ...current,
            isCompleted: state
        }));
    };

    return (
        <Sheet
            open={state.open}
            onOpenChange={(state) => onOpenChange(state)}
        >
            {!state.isParent && (
                <Tooltip>
                    <SheetTrigger asChild>
                        <TooltipTrigger
                            asChild
                            onClick={() => setOpen(true)}
                        >
                            <Button
                                variant="ghost"
                                size="icon"
                                disabled={!action}
                            >
                                <LinkIcon className="h-4 w-4" />
                                <span className="sr-only">Link</span>
                            </Button>
                        </TooltipTrigger>
                    </SheetTrigger>
                    <TooltipContent>Link to trip</TooltipContent>
                </Tooltip>
            )}
            <SheetContent className="min-w-[650px] p-0">
                <SheetHeader className="p-6">
                    <SheetTitle>Select trip</SheetTitle>
                    <SheetDescription>Please select way you want to link selected entity to the trip.</SheetDescription>
                </SheetHeader>
                <Separator />
                <ScrollArea className="h-[calc(100%-96px)] w-full">
                    <div className="p-6">
                        <Tabs
                            defaultValue="new-trip"
                            className={cn('flex flex-col h-full')}
                        >
                            <div className="flex py-2">
                                <TabsList>
                                    <TabsTrigger
                                        disabled={state.isCompleted || state.isPending}
                                        value="new-trip"
                                        className="text-zinc-600 dark:text-zinc-200"
                                    >
                                        Link new trip
                                    </TabsTrigger>
                                    <TabsTrigger
                                        disabled={state.isCompleted || state.isPending}
                                        value="existing-trip"
                                        className="text-zinc-600 dark:text-zinc-200"
                                    >
                                        Link existing trip
                                    </TabsTrigger>
                                </TabsList>
                            </div>
                            <div className="flex w-full">
                                <TabsContent
                                    value="new-trip"
                                    className="m-0 w-full"
                                >
                                    <NewTrip
                                        onComplete={(state: boolean) => setIsCompleted(state)}
                                        onPending={(state: boolean) => setIsPending(state)}
                                        onClose={() => setOpen(false)}
                                        action={action}
                                    />
                                </TabsContent>
                                <TabsContent
                                    value="existing-trip"
                                    className="m-0 w-full"
                                >
                                    <ExistingTrip action={action} />
                                </TabsContent>
                            </div>
                        </Tabs>
                    </div>
                </ScrollArea>
            </SheetContent>
        </Sheet>
    );
}

interface NewTripProps {
    onPending?: (isPending: boolean) => void;
    onComplete: (isComplete: boolean) => void;
    onClose: () => void;
    action: WithDataId<Action> | null;
}

type TripTriggerContext = NoteContextData & {
    customerGroup?: string;
    policyNumber?: string;
    productCode?: string;
    vrn?: string;
};

interface ValueLabelDisplay {
    value: string;
    label: string;
}

type NewTripState = Pick<Trip, 'vrn' | 'commsRouteOverride' | 'externalRef' | 'productCode'> & {
    tripCode: string | null;
    customerGroup: string;
    policyNumber: string;
    entitlement: string;
    start: string;
    end: string;
    formSubmitPending: boolean;
    duplicateVRNMessage: string | null;
    duplicateCheckPending: boolean;
};

function getInitialCommsRouteOverride(action?: Action | null, custGroup?: string): CommsRoute | undefined {
    if (!action) {
        return;
    }

    if (isEmailAction(action)) {
        return {
            custGroup: custGroup || '',
            route: CommsChannel.EMAIL
        };
    }

    if (isSparxMessageAction(action)) {
        return {
            custGroup: custGroup || '',
            route: CommsChannel.SPARX
        };
    }

    return;
}

export function NewTrip({ onComplete, onPending, onClose, action }: NewTripProps) {
    const [actionClient] = useActionClient();
    const [tripClient] = useTripClient();
    const [entitlementClient] = useEntitlementClient();
    const [eurohelpClient] = useEurohelpClient();

    const context: TripTriggerContext = action?.context || {};
    const [state, setState] = useState<NewTripState>({
        tripCode: null,
        customerGroup: context?.customerGroup || '',
        policyNumber: context?.policyNumber || '',
        productCode: context?.productCode || '',
        vrn: context?.vrn || '',
        externalRef: '',
        entitlement: '',
        start: new Date().toISOString().slice(0, 10),
        end: '',
        // comms override can be sent at the start if we are linking specific
        commsRouteOverride: getInitialCommsRouteOverride(action, context.customerGroup),
        formSubmitPending: false,
        duplicateVRNMessage: null,
        duplicateCheckPending: false
    });

    const handleComboChange = (value: string, property: string) => {
        const updatedFormValues: Record<string, unknown> = {
            [property]: value
        };

        //we need both the value and the label of the policy. wonder if it wouldn't be better to set the value to a
        // (stringified) json object instead. apparently doing 2 consecutive setFormValues causes only the second call
        // to register so doing this in this convoluted way
        if (property === 'policyNumber') {
            const policy: any = policies.find((el: { value: string; label: string }) => el.value === value);
            if (policy) {
                updatedFormValues['productCode'] = policy.label;
            }
        }
        setState((current) => ({ ...current, ...updatedFormValues }));

        if (property === 'customerGroup') {
            setState((current) => {
                if (current.commsRouteOverride) {
                    return {
                        ...current,
                        commsRouteOverride: { ...current.commsRouteOverride, custGroup: value }
                    };
                }

                return current;
            });
        }
    };

    const handleFormChange = (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setState((current) => ({ ...current, [event.target.name]: event.target.value }));
    };

    useChange(state.vrn, async () => {
        try {
            setState((current) => ({
                ...current,
                duplicateVRNMessage: null,
                duplicateCheckPending: true
            }));
            const result = await tripClient.getTripByVrn(state.vrn);
            if (result._id && result.isActive) {
                setState((current) => ({
                    ...current,
                    duplicateVRNMessage: `${state.vrn} is already linked with trip: ${result._id}`,
                    duplicateCheckPending: false
                }));
            }
        } catch (error) {
            setState((current) => ({
                ...current,
                duplicateVRNMessage: null,
                duplicateCheckPending: false
            }));
        }
    });

    const handleVrn = async (event: ChangeEvent<HTMLInputElement>) => {
        const inputVrn = event.target.value;
        setState((current) => ({ ...current, vrn: inputVrn }));
    };

    const [customerGroupsData, fetchCustomerGroups] = useCallback(eurohelpClient.getCustomerGroups);
    useInitialLoad(async () => fetchCustomerGroups());

    let customerGroups: ValueLabelDisplay[] = [];
    if (customerGroupsData) {
        customerGroups = customerGroupsData.map((group: EuopsCustomerGroup) => {
            return {
                value: group.value,
                label: group.text
            };
        });
    }

    const [policiesData, fetchPolicies] = useCallback(entitlementClient.getPoliciesByCustomerGroupCode);

    let policies: ValueLabelDisplay[] = [
        {
            value: state.policyNumber,
            label: state.productCode
        }
    ];
    if (policiesData && policiesData.length > 0) {
        policies = policiesData.map((policy: ProductPolicy) => {
            return {
                value: policy.policyNumber,
                label: `${policy.policyNumber} - ${policy.productName}`
            };
        });
    }

    useChange(state.customerGroup, () => {
        // if (state.customerGroup) { going with this as other didnt work
        fetchPolicies(state.customerGroup).catch();
        //}
    });

    const onSubmitComplete = (response: Trip) => {
        if (response) {
            if (action?._id) {
                linkActionToTripHandler(action._id, {
                    type: NoteEntityType.TRIP,
                    id: response.code
                }).catch();
            }
            setState((current) => ({ ...current, tripCode: response.code }));
            onComplete(true);
        }

        setState((current) => ({ ...current, formSubmitPending: false }));
    };

    const [, linkActionToTripHandler] = useCallback(actionClient.link);
    const [, fetch] = useCallback(tripClient.createTrip, { onComplete: onSubmitComplete });

    /*************************************************************************************
     * Submit form
     * ************************************************************************************/
    const onSubmit = async (event: FormEvent<HTMLFormElement>) => {
        // don't forget: seLocator is:
        // entitlement.policy().contractKey() +
        // entitlement.policy().customerKey() +
        // vrn;

        event.preventDefault();
        if (state.duplicateVRNMessage) {
            return;
        }
        setState((current) => ({ ...current, formSubmitPending: true }));

        const newTrip: Partial<Trip> = {
            customerGroup: state.customerGroup,
            vrn: state.vrn,
            externalRef: state.externalRef,
            productCode: state.productCode,
            contractKey: state.policyNumber,
            customerKey: state.policyNumber,
            start: state.start,
            end: state.end,
            customerRequestIds: [],
            commsRouteOverride: state.commsRouteOverride
        };

        await fetch(newTrip as Trip);
    };

    const onCommsRouteOverrideChange = (checked: boolean) => {
        if (checked) {
            const commsRouteOverride = getInitialCommsRouteOverride(action, state.customerGroup || context.customerGroup) || {
                custGroup: state.customerGroup || context.customerGroup || '',
                route: CommsChannel.EMAIL
            };

            setState({ ...state, commsRouteOverride });
        } else {
            setState({ ...state, commsRouteOverride: undefined });
        }
    };
    const onCommsRouteChannelChange = (channel: CommsChannel) => {
        const commsRouteOverride = {
            custGroup: state.customerGroup,
            route: channel
        };

        setState({ ...state, commsRouteOverride });
    };

    if (state.tripCode) {
        return (
            <TripCreatedLinkCompleted
                tripCode={state.tripCode}
                vrn={state.vrn}
                custGroup={state.customerGroup}
                action={action}
                onClose={onClose}
                policyNumber={state.policyNumber}
                productCode={state.productCode}
                start={state.start}
                end={state.end}
                commsRouteOverride={state.commsRouteOverride}
            />
        );
    }

    return (
        <Card>
            <Form onSubmit={onSubmit}>
                <CardHeader>
                    <CardDescription>To create new trip we need few details.</CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col gap-3">
                    {/************************************************************************************************
                     customerGroup
                     *************************************************************************************************/}
                    <FormItem className="w-full max-w-sm flex flex-col">
                        <FormLabel htmlFor="customerGroup">Customer group</FormLabel>
                        <FormField name="customerGroup">
                            <FormMessage
                                match="valueMissing"
                                className="pb-2"
                            >
                                Please select customer group
                            </FormMessage>
                            <div className="flex gap-2">
                                <FormControl>
                                    <Combobox
                                        disabled={false}
                                        items={customerGroups}
                                        searchPlaceholder="Search for customer group..."
                                        onSelect={(val) => handleComboChange(val, 'customerGroup')}
                                        className="w-[420px]"
                                        value={context?.customerGroup}
                                    />
                                </FormControl>
                            </div>
                        </FormField>
                        <FormDescription className="text-xs text-gray-400">Based on all data available please select a correct group under which we should create any future entities.</FormDescription>
                    </FormItem>

                    {/************************************************************************************************
                     policy & product
                     *************************************************************************************************/}
                    <FormItem className="w-full max-w-sm flex flex-col">
                        <FormLabel htmlFor="policyNumber">Policy</FormLabel>
                        <FormField name="policyNumber">
                            <div className="flex gap-2">
                                <FormControl>
                                    <Combobox
                                        // disabled={isPending}
                                        items={policies}
                                        notFoundLabel="No policy found"
                                        noValueLabel="Select policy"
                                        searchPlaceholder="Select policy"
                                        onSelect={(val) => handleComboChange(val, 'policyNumber')}
                                        className="w-[420px]"
                                        value={context?.policyNumber}
                                    />
                                </FormControl>
                            </div>
                            <FormMessage
                                match="valueMissing"
                                className="pb-2"
                            >
                                Please select policy
                            </FormMessage>
                        </FormField>
                        <FormDescription className="text-xs text-gray-400">Select correct policy</FormDescription>
                    </FormItem>

                    {/************************************************************************************************
                     vrn
                     *************************************************************************************************/}
                    <FormItem className="w-full max-w-sm flex flex-col">
                        <FormLabel htmlFor="vrn">Vehicle registration</FormLabel>
                        <FormField name="vrn">
                            <FormMessage
                                match="valueMissing"
                                className="pb-2"
                            >
                                Please enter VRN
                            </FormMessage>
                            <FormMessage
                                match="typeMismatch"
                                className="pb-2"
                            >
                                Please provide a valid VRN
                            </FormMessage>
                            <div className="flex flex-col gap-2">
                                <div className="flex gap-2">
                                    <FormControl>
                                        <Input
                                            className={`w-[300px] ${state.duplicateVRNMessage ? 'border-red-500' : ''}`}
                                            type="text"
                                            required
                                            placeholder="Enter vehicle registration number"
                                            onChange={handleFormChange} // Update state on change
                                            onBlur={handleVrn} // Trigger API call on blur
                                            style={{
                                                border: state.duplicateVRNMessage ? '1px solid red' : '1px solid #ccc'
                                            }}
                                            value={context?.vrn}
                                        />
                                    </FormControl>
                                    {state.duplicateCheckPending && (
                                        <div className="w-6">
                                            <Preloader size="small" />
                                        </div>
                                    )}
                                </div>
                                {state.duplicateVRNMessage && <div className="text-red-600 text-sm">{state.duplicateVRNMessage}</div>}
                            </div>
                        </FormField>
                        <FormDescription className="text-xs text-gray-400">Please provide a valid customer VRN</FormDescription>
                    </FormItem>

                    {/************************************************************************************************
                     externalRef
                     *************************************************************************************************/}
                    <FormItem className="w-full max-w-sm flex flex-col">
                        <FormLabel htmlFor="externalRef">External reference</FormLabel>
                        <FormField name="externalRef">
                            <FormMessage
                                match="valueMissing"
                                className="pb-2"
                            >
                                Please enter external reference
                            </FormMessage>
                            <FormMessage
                                match="typeMismatch"
                                className="pb-2"
                            >
                                Please provide a valid external reference
                            </FormMessage>
                            <div className="flex gap-2">
                                <FormControl>
                                    <Input
                                        className="w-[300px]"
                                        type="text"
                                        // disabled={isPending}
                                        placeholder="Reference"
                                        onChange={handleFormChange}
                                    />
                                </FormControl>
                            </div>
                        </FormField>
                        <FormDescription className="text-xs text-gray-400">If we have any external reference please provide it here</FormDescription>
                    </FormItem>

                    {/************************************************************************************************
                     start and end date TODO: this might be timestamp instead of date
                     *************************************************************************************************/}
                    <div>
                        <FormLabel>Trip dates</FormLabel>
                        <FormDescription className="text-xs text-gray-400">To correctly track a trip we need its start and end date</FormDescription>
                        <div className="flex gap-4">
                            <FormItem className="flex flex-col mt-2 space-y-1">
                                <FormLabel
                                    className={'text-xs'}
                                    htmlFor="start"
                                >
                                    Start
                                </FormLabel>
                                <FormField name="start">
                                    <FormMessage
                                        match="valueMissing"
                                        className="pb-2"
                                    >
                                        Please enter start date
                                    </FormMessage>
                                    <FormMessage
                                        match="typeMismatch"
                                        className="pb-2"
                                    >
                                        Please provide a valid start date
                                    </FormMessage>
                                    <div className="flex gap-2">
                                        <FormControl>
                                            <Input
                                                className="w-[150px]"
                                                type="date"
                                                // disabled={isPending}
                                                placeholder="start"
                                                onChange={handleFormChange}
                                                value={state.start}
                                                required
                                            />
                                        </FormControl>
                                    </div>
                                </FormField>
                            </FormItem>
                            <FormItem className="flex flex-col mt-2 space-y-1">
                                <FormLabel
                                    className={'text-xs'}
                                    htmlFor="end"
                                >
                                    End
                                </FormLabel>
                                <FormField name="end">
                                    <FormMessage
                                        match="valueMissing"
                                        className="pb-2"
                                    >
                                        Please enter end date
                                    </FormMessage>
                                    <FormMessage
                                        match="typeMismatch"
                                        className="pb-2"
                                    >
                                        Please provide a valid end date
                                    </FormMessage>
                                    <div className="flex gap-2">
                                        <FormControl>
                                            <Input
                                                className="w-[150px]"
                                                type="date"
                                                // disabled={isPending}
                                                placeholder="end"
                                                onChange={handleFormChange}
                                            />
                                        </FormControl>
                                    </div>
                                </FormField>
                            </FormItem>
                        </div>
                    </div>

                    {/* Comms override */}
                    <FormItem className="w-full max-w-sm flex flex-col">
                        <FormLabel htmlFor="commsOverride">Communication channel</FormLabel>
                        <FormDescription className="text-xs text-gray-400">
                            In case trip requires different channel of communication that its customer group is configured at please set an override.
                        </FormDescription>
                        <FormField name="commsOverride">
                            <div className="flex gap-2">
                                <div className="flex items-center space-x-2 my-2">
                                    <FormControl>
                                        <Switch
                                            id="commsOverride"
                                            name={'commsOverrideSwitch'}
                                            checked={!!state.commsRouteOverride}
                                            onCheckedChange={onCommsRouteOverrideChange}
                                        />
                                    </FormControl>
                                    <Label htmlFor="commsOverrideSwitch">Trip requires communication channel override</Label>
                                </div>
                            </div>
                        </FormField>
                        {state.commsRouteOverride && (
                            <FormField name="commsOverride">
                                <div className="flex flex-col gap-2">
                                    <Label htmlFor="commsOverrideSwitch">Target channel</Label>
                                    <FormControl>
                                        <Select
                                            defaultValue={state.commsRouteOverride?.route}
                                            onValueChange={(value) => onCommsRouteChannelChange(value as CommsChannel)}
                                        >
                                            <SelectTrigger className="h-12 w-full">
                                                <SelectValue placeholder="Select communication channel" />
                                            </SelectTrigger>
                                            <SelectContent side="bottom">
                                                {commsChannels.map((channel) => (
                                                    <SelectItem
                                                        key={channel.name}
                                                        value={`${channel.code}`}
                                                    >
                                                        {channel.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                </div>
                            </FormField>
                        )}

                        {state.commsRouteOverride && (
                            <Alert className="mt-6">
                                <InfoIcon className="h-4 w-4" />
                                <AlertTitle>Communication channel override applied</AlertTitle>
                                <AlertDescription>
                                    All updates around this trip will be routed via a selected channel of communication. This option was enabled either due to the type of initial communication or
                                    manual operator intervention.
                                </AlertDescription>
                            </Alert>
                        )}
                    </FormItem>
                </CardContent>
                <CardFooter>
                    <div className="flex flex-col gap-2">
                        <Button
                            type="submit"
                            disabled={state.formSubmitPending || state.duplicateCheckPending}
                            variant="default"
                            size="default"
                        >
                            Create trip
                        </Button>
                    </div>
                </CardFooter>
            </Form>
        </Card>
    );
}

interface LinkSummaryProps {
    tripCode: string;
    productCode: string;
    vrn: string;
    custGroup: string;
    action: WithDataId<Action> | null;
    start: string;
    end: string;
    policyNumber: string;
    commsRouteOverride?: CommsRoute;
    onChangeTrip?: () => void;
}

function LinkSummary({ tripCode, productCode, custGroup, vrn, action, onChangeTrip, start, end, policyNumber, commsRouteOverride }: LinkSummaryProps) {
    const [actionClient] = useActionClient();

    const onComplete = () => {
        if (onChangeTrip) {
            onChangeTrip();
        }
    };

    const [, unlinkActionToTripHandler] = useCallback(actionClient.unlink, { onComplete: onComplete });

    const commsChannel = commsChannels.find((channel) => {
        return channel.code === commsRouteOverride?.route;
    })?.name;
    return (
        <div className="flex items-stretch justify-center gap-10">
            <div className={'flex w-full'}>
                <div className={'flex flex-col text-xs gap-y-1 w-[40%]'}>
                    <div className={'flex flex-col gap-1'}>
                        <CardTitle className={'text-sm'}>Trip code</CardTitle>
                        <CardDescription className={'text-sm'}>{tripCode}</CardDescription>
                    </div>
                    {onChangeTrip && (
                        <div className={'flex'}>
                            <Button
                                variant="outline"
                                size="default"
                                className="w-40"
                                onClick={() => unlinkActionToTripHandler}
                            >
                                Change trip
                            </Button>
                        </div>
                    )}
                </div>
                <Separator
                    orientation={'vertical'}
                    className={'h-35 mx-6'}
                />
                <div className={'flex flex-col gap-2'}>
                    <div className={'flex gap-x-2'}>
                        <CardTitle className={'text-sm'}>Policy:</CardTitle>
                        <CardDescription className={'text-sm'}>{productCode}</CardDescription>
                    </div>
                    <div className={'flex gap-x-2'}>
                        <CardTitle className={'text-sm'}>VRN:</CardTitle>
                        <CardDescription className={'text-sm'}>{vrn}</CardDescription>
                    </div>
                    <div className={'flex gap-x-2'}>
                        <CardTitle className={'text-sm'}>Customer group:</CardTitle>
                        <CardDescription className={'text-sm'}>{custGroup}</CardDescription>
                    </div>
                    <div className={'flex gap-x-2'}>
                        <CardTitle className={'text-sm'}>BCASP:</CardTitle>
                        <CardDescription className={'text-sm'}>{policyNumber}</CardDescription>
                    </div>
                    <div className={'flex gap-x-2'}>
                        <CardTitle className={'text-sm'}>Start date:</CardTitle>
                        <CardDescription className={'text-sm'}>{start}</CardDescription>
                    </div>
                    <div className={'flex gap-x-2'}>
                        <CardTitle className={'text-sm'}>End date:</CardTitle>
                        <CardDescription className={'text-sm'}>{end}</CardDescription>
                    </div>
                    {commsRouteOverride && (
                        <div className={'flex gap-x-2'}>
                            <CardTitle className={'text-sm'}>Communication channel:</CardTitle>
                            <CardDescription className={'text-sm'}>{commsChannel || 'unknown'}</CardDescription>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

interface TripCreatedLinkCompletedProps {
    tripCode: string;
    taskId?: number | null;
    vrn: string;
    custGroup: string;
    action: WithDataId<Action> | null;
    policyNumber: string;
    productCode: string;
    start: string;
    end: string;
    commsRouteOverride?: CommsRoute;
    onClose: () => void;
    onChangeTrip?: () => void;
}

function TripCreatedLinkCompleted({ tripCode, policyNumber, productCode, custGroup, vrn, taskId, action, start, end, onClose, onChangeTrip, commsRouteOverride }: TripCreatedLinkCompletedProps) {
    const [recipient] = useState<string>('');
    const [client, status, onlineClients] = useCommsWorkerClient(FrontendApplication.EUROHELP);

    const [tripClient] = useTripClient();
    const [tripResult, fetchTrip, isPendingTrip, errorTrip] = useCallback(tripClient.getTripById);
    const { isInitialPending } = useInitialPending(isPendingTrip);
    useInitialLoad(async () => fetchTrip(tripCode));

    useEffect(() => {
        fetchTrip(tripCode).catch();
    }, [tripCode]);

    const sendMessage = async () => {
        console.log('sendMessage:onlineClients', onlineClients);
        if (!client) {
            return;
        }

        const recipient = onlineClients.find((onlineClient) => {
            return onlineClient.application === FrontendApplication.AAHELP2;
        });

        if (!recipient) {
            console.error('No recipient found');
            return;
        }

        console.log('sendMessage data:', {
            type: 'RSS',
            vrn: vrn,
            BCASP: policyNumber,
            custGroupId: custGroup
        });

        const event: SendableWorkerCommsEvent = getWorkerActionCreateEvent({
            data: {
                type: ActionEntityType.RSS,
                vrn: vrn,
                BCASP: policyNumber,
                custGroupId: custGroup
            },
            source: FrontendApplication.EUROHELP,
            recipient: recipient.clientId
        });

        await client.send(event);
    };
    const isButtonDisabled = client?.connectionStatus !== 'ONLINE' && !recipient;

    const updateTripSummary = () => {
        fetchTrip(tripCode);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg flex flex-col items-center">
                    <CircleCheckIcon
                        strokeWidth={1}
                        className="w-[50px] h-[50px]"
                    />
                    <h2 className="text-2xl pt-1">Trip created</h2>
                </CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6 flex flex-col gap-6">
                <LinkSummary
                    tripCode={tripCode}
                    vrn={vrn}
                    custGroup={custGroup}
                    onChangeTrip={onChangeTrip}
                    productCode={productCode}
                    action={action}
                    policyNumber={policyNumber}
                    start={start}
                    end={end}
                    commsRouteOverride={commsRouteOverride}
                />
            </CardContent>
            <Separator />
            <CardFooter className="pt-6">
                <div className={'flex flex-col gap-3 w-full'}>
                    <div className="flex flex-row w-full justify-between">
                        <div className="flex space-x-4">
                            <Button
                                variant="default"
                                size="default"
                                className={`w-50 ${!isButtonDisabled ? 'bg-green-500' : 'bg-gray-500'}`}
                                onClick={sendMessage}
                                disabled={isButtonDisabled}
                                style={{
                                    cursor: !isButtonDisabled ? 'pointer' : 'not-allowed'
                                }}
                                title={'Please start the AAHelp2 application to create a task.'}
                            >
                                Create RSS in AAHelp2
                            </Button>
                            <Button
                                variant="outline"
                                size="icon"
                                className="w-40"
                                onClick={() => {}}
                            >
                                <TripEdit
                                    tripData={tripResult ? tripResult : ({} as WithDataId<Trip>)}
                                    onUpdateSummary={updateTripSummary}
                                    showDescription={true}
                                />
                            </Button>
                        </div>

                        <Button
                            variant="outline"
                            size="default"
                            className="w-40"
                            onClick={() => onClose()}
                        >
                            Close
                        </Button>
                    </div>
                    {isButtonDisabled && <div className="text-red-500 font-bold mb-2">Please run the AAHelp2 application.</div>}
                </div>
            </CardFooter>
        </Card>
    );
}

interface TripLinkedLinkCompletedProps {
    tripCode: string;
    taskId?: number | null;
    productCode: string;
    vrn: string;
    custGroup: string;
    action: WithDataId<Action> | null;
    start: string;
    end: string;
    policyNumber: string;
    onClose: () => void;
    onChangeTrip?: () => void;
}

function TripLinkedLinkExistingTrip({ tripCode, productCode, custGroup, vrn, taskId, start, end, policyNumber, onClose, onChangeTrip, action }: TripLinkedLinkCompletedProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg flex flex-col items-center">
                    <CircleCheckIcon
                        strokeWidth={1}
                        className="w-[50px] h-[50px]"
                    />
                    <h2 className="text-2xl pt-1">Trip linked</h2>
                </CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6 flex flex-col gap-6">
                <LinkSummary
                    tripCode={tripCode}
                    productCode={productCode}
                    vrn={vrn}
                    custGroup={custGroup}
                    onChangeTrip={onChangeTrip}
                    action={action}
                    start={start}
                    end={end}
                    policyNumber={policyNumber}
                />
            </CardContent>
            <Separator />
            <CardFooter className="pt-6">
                <div className={'flex flex-col gap-3 w-full'}>
                    <div className="flex flex-row w-full justify-between">
                        <Button
                            variant="default"
                            size="default"
                            className={'w-50 bg-green-500'}
                            title={'Please start the AAHelp2 application to create a task.'}
                        >
                            <CreateRSSTask
                                vrn={vrn}
                                custGroup={custGroup}
                                bcasp={policyNumber}
                                allow={true}
                            />
                            Create RSS in AAHelp2
                        </Button>
                        <SheetClose asChild>
                            <Button
                                variant="outline"
                                size="default"
                                className="w-40"
                                type={'submit'}
                            >
                                Close
                            </Button>
                        </SheetClose>
                    </div>
                </div>
            </CardFooter>
        </Card>
    );
}

interface ExistingTripProps {
    action: WithDataId<Action> | null;
}

function ExistingTrip({ action }: ExistingTripProps) {
    const [tripsData, more, isPending, error] = useTrips();
    const [selectedTrip, setSelectedTrip] = useState<Trip | null>(null);
    const [linkSuccessful, setLinkSuccessful] = useState(false);
    const [actionClient] = useActionClient();

    useEffect(() => {
        if (selectedTrip && action && action?._id) {
            linkActionToTripHandler(action?._id as string, {
                type: NoteEntityType.TRIP,
                id: selectedTrip.code
            }).catch();
        }
    }, [selectedTrip]);

    const [, linkActionToTripHandler] = useCallback(actionClient.link);

    function onSubmit(event: FormEvent<HTMLFormElement>) {
        // Do something with the form values.
        // ✅ This will be type-safe and validated.
        console.log(event);
        event.preventDefault();
        // callback(event);
    }

    const onClose = () => {
        console.log('Close');
    };

    // TODO: Rename and rewrite when connecting with backend
    const onTripLink = async (row: Trip) => {
        setLinkSuccessful(true);
        setSelectedTrip(row);
    };

    return (
        <Card>
            {!linkSuccessful && tripsData && (
                <Form onSubmit={onSubmit}>
                    <CardHeader>
                        <CardTitle className="text-lg">Find existing trip</CardTitle>
                        <CardDescription>To link existing trip please select one of the below results.</CardDescription>
                    </CardHeader>
                    <Separator />
                    <CardContent className="p-0 flex flex-col gap-6">
                        <LinkActionDataTable
                            data={tripsData}
                            columns={LinkActionWithTripColumns}
                            placeholder={'Search for trip code'}
                            filters={[
                                {
                                    filterBy: 'customerGroup',
                                    displayAs: 'Customer group'
                                },
                                { filterBy: 'vrn', displayAs: 'VRN' }
                            ]}
                            mainFilter="code"
                            onLink={(row) => onTripLink(row)}
                        />
                    </CardContent>
                    <Separator />
                    <CardFooter className="py-6 px-3"></CardFooter>
                </Form>
            )}
            {linkSuccessful && selectedTrip && (
                <TripLinkedLinkExistingTrip
                    tripCode={selectedTrip.code}
                    productCode={selectedTrip.productCode}
                    vrn={selectedTrip.vrn}
                    custGroup={selectedTrip.customerGroup}
                    onClose={onClose}
                    onChangeTrip={() => setLinkSuccessful(false)}
                    start={selectedTrip.start as string}
                    end={selectedTrip.end as string}
                    policyNumber={selectedTrip.contractKey as string}
                    action={action}
                />
            )}
        </Card>
    );
}
