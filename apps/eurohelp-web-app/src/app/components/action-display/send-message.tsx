import { Action, MessageNote, Namespace, NoteEntity, NoteType, UserProfile, WithDataId } from '@aa/data-models/common';
import { Badge } from '@aa/ui/core/badge';
import { Button } from '@aa/ui/core/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@aa/ui/core/card';
import { Combobox } from '@aa/ui/core/combobox';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from '@aa/ui/core/form';
import { Input } from '@aa/ui/core/input';
import { Label } from '@aa/ui/core/label';
import { ScrollArea } from '@aa/ui/core/scroll-area';
import { Separator } from '@aa/ui/core/separator';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@aa/ui/core/sheet';
import { Switch } from '@aa/ui/core/switch';
import { Textarea } from '@aa/ui/core/textarea';
import { Tooltip, TooltipContent, TooltipTrigger } from '@aa/ui/core/tooltip';
import { useActionClient } from '@aa/ui/hooks/use-action-client';
import { useNoteClient } from '@aa/ui/hooks/use-note-client';
import { CircleCheckIcon, MailIcon } from 'lucide-react';
import * as React from 'react';
import { FormEvent, useEffect, useState } from 'react';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { useTeamClient } from '@aa/ui/hooks/use-team-client';
import { useUserClient } from '@aa/ui/hooks/use-user-client';
import { useUserProfile } from '@aa/ui/hooks/use-user-profile';

interface TeamAndRecipientsDisplay {
    value: string;
    label: string;
}

interface MessageActionProps {
    parentEntity?: NoteEntity;
    replyTo?: WithDataId<Action> | null;
    disabled?: boolean;
    hasTrip?: boolean;
    onUpdate?: () => void;
}

export default function SendMessage({ replyTo, disabled, parentEntity, hasTrip, onUpdate }: MessageActionProps) {
    const [actionClient] = useActionClient();
    const [noteClient] = useNoteClient();
    const [teamClient] = useTeamClient();
    const [userClient] = useUserClient();
    const userProfile = useUserProfile();

    // Merged state
    const [state, setState] = useState({
        teamMessage: false,
        formValues: {
            body: '',
            subject: '',
            recipient: '',
            team: ''
        },
        isLoading: true,
        isButtonDisabled: false,
        isCompleted: false,
        open: false,
        operatorIds: [9096800]
    });

    const handleBooleanStateChange = (key: string, value: boolean) => {
        setState((prevState) => ({
            ...prevState,
            [key]: value
        }));
    };

    const handleFormValuesChange = (key: string, value: string) => {
        setState((prevState) => ({
            ...prevState,
            formValues: {
                ...prevState.formValues,
                [key]: value
            }
        }));
    };

    function onOpenChange(openState: boolean) {
        setState((prevState) => ({
            ...prevState,
            isCompleted: false,
            open: openState
        }));
    }

    useEffect(() => {
        setState((prevState) => ({
            ...prevState,
            isLoading: true
        }));
        fetchTeams().catch();
        fetchUsers(state.operatorIds).catch();
    }, []);

    const handleComboChange = (value: string, property: string) => {
        handleFormValuesChange(property, value);
    };

    const handleFormChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        handleFormValuesChange(event.target.name, event.target.value);
    };

    const [teamsData, fetchTeams, isTeamsPending] = useCallback(teamClient.teams);
    const { isInitialPending } = useInitialPending(isTeamsPending);
    useInitialLoad(async () => fetchTeams());
    const teams: TeamAndRecipientsDisplay[] =
        teamsData?.map((team) => ({
            value: team.shortName,
            label: team.name
        })) || [];

    const [usersData, fetchUsers] = useCallback(userClient.list);
    useInitialLoad(async () => fetchUsers(state.operatorIds));
    const recipients: TeamAndRecipientsDisplay[] =
        usersData?.map((user: UserProfile) => ({
            value: user.operatorId.toString(),
            label: `${user.forename} ${user.surname}`
        })) || [];

    const onSubmit = async (event: FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        setState((prevState) => ({
            ...prevState,
            isButtonDisabled: true
        }));
        const formData = new FormData(event.currentTarget);
        formData.set('team', state.formValues.team);
        formData.set('recipient', state.formValues.recipient);
        const team = formData.get('team') as string | number;
        const recipient = formData.get('recipient') as string | number;
        const subject = formData.get('subject') as string;
        const body = (formData.get('body') as string) || '';

        if (!team && !recipient) {
            return;
        }

        const date = new Date();
        const action: MessageNote = {
            type: NoteType.MESSAGE,
            title: subject,
            namespace: Namespace.EUOPS,
            created: date,
            updated: date,
            content: {
                from: recipient ? recipient : team,
                subject: subject,
                body: body,
                replyTo: replyTo?._id
            },
            parentEntity,
            context: {},
            createdBy: userProfile.profile.operatorId
        };

        if (hasTrip) {
            await noteClient.insertNote(action).then(() => {
                actionClient.insert(action);
                setState((prevState) => ({
                    ...prevState,
                    isCompleted: true
                }));
                if (onUpdate) {
                    onUpdate();
                }
            });
        } else {
            await actionClient.insert(action);
            setState((prevState) => ({
                ...prevState,
                isCompleted: true
            }));
        }
        setState((prevState) => ({
            ...prevState,
            isButtonDisabled: false
        }));
    };

    return (
        <Sheet
            open={state.open}
            onOpenChange={(openState) => onOpenChange(openState)}
        >
            <Tooltip>
                <SheetTrigger asChild>
                    <TooltipTrigger
                        asChild
                        onClick={() => handleBooleanStateChange('open', true)}
                    >
                        <Button
                            variant="ghost"
                            size="icon"
                            disabled={disabled}
                        >
                            <MailIcon className="h-4 w-4" />
                            <span className="sr-only">Email</span>
                        </Button>
                    </TooltipTrigger>
                </SheetTrigger>
                <TooltipContent>Send message</TooltipContent>
            </Tooltip>
            <SheetContent className="min-w-[600px] p-0">
                {!state.isCompleted && (
                    <SheetHeader className="p-6">
                        <SheetTitle>Send a message</SheetTitle>
                        <SheetDescription>Send a message.</SheetDescription>
                    </SheetHeader>
                )}
                <Separator />
                <ScrollArea className="h-[calc(100%-140px)] w-full">
                    <div className="p-6">
                        {state.isCompleted && (
                            <MessageSent
                                sentTo={state.formValues.recipient ? state.formValues.recipient : state.formValues.team}
                                subject={state.formValues.subject}
                                message={state.formValues.body}
                                onClose={() => handleBooleanStateChange('open', false)}
                            />
                        )}

                        {!state.isCompleted && (
                            <Card>
                                <Form onSubmit={onSubmit}>
                                    <CardContent className="pt-6 flex flex-col gap-6">
                                        <div className="flex flex-col gap-2">
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="create-task"
                                                    onClick={() => handleBooleanStateChange('teamMessage', !state.teamMessage)}
                                                />
                                                <Label htmlFor="create-task">Team message</Label>
                                            </div>
                                            {state.teamMessage && (
                                                <FormItem className="w-full max-w-sm flex flex-col">
                                                    <FormLabel htmlFor="team">Team</FormLabel>

                                                    <FormField name="team">
                                                        <div className="flex gap-2">
                                                            <FormControl>
                                                                <Combobox
                                                                    items={teams}
                                                                    notFoundLabel="No team found"
                                                                    noValueLabel="Select team"
                                                                    searchPlaceholder="Search for team..."
                                                                    onSelect={(val) => handleComboChange(val, 'team')}
                                                                />
                                                            </FormControl>
                                                        </div>
                                                    </FormField>
                                                </FormItem>
                                            )}
                                            {!state.teamMessage && (
                                                <FormItem className="w-full max-w-sm flex flex-col">
                                                    <FormLabel htmlFor="recipient">Recipient</FormLabel>

                                                    <FormField name="recipient">
                                                        <div className="flex gap-2">
                                                            <FormControl>
                                                                <Combobox
                                                                    items={recipients}
                                                                    notFoundLabel="No recipient found"
                                                                    noValueLabel="Select recipient"
                                                                    searchPlaceholder="Search for recipient..."
                                                                    onSelect={(val) => handleComboChange(val, 'recipient')}
                                                                />
                                                            </FormControl>
                                                        </div>
                                                    </FormField>
                                                </FormItem>
                                            )}
                                        </div>
                                        <FormItem className="w-full max-w-sm flex flex-col">
                                            <FormLabel htmlFor="subject">Subject</FormLabel>
                                            <FormField name="subject">
                                                <div className="flex gap-2">
                                                    <FormControl>
                                                        <Input
                                                            className="w-[300px]"
                                                            type="text"
                                                            placeholder="Subject content"
                                                            onChange={handleFormChange}
                                                            required
                                                        />
                                                    </FormControl>
                                                </div>
                                            </FormField>
                                        </FormItem>
                                        <FormItem className="w-full max-w-sm flex flex-col">
                                            <FormLabel htmlFor="body">Message</FormLabel>
                                            <FormField name="body">
                                                <div className="flex gap-2">
                                                    <FormControl>
                                                        <Textarea
                                                            id="feedback"
                                                            placeholder="Type your message here"
                                                            className="min-h-[150px]"
                                                            onChange={handleFormChange}
                                                            required
                                                        />
                                                    </FormControl>
                                                </div>
                                            </FormField>
                                            <FormDescription className="text-xs text-gray-400">Your message will be copied to the support team.</FormDescription>
                                        </FormItem>
                                    </CardContent>
                                    <Separator />
                                    <CardFooter className="pt-6">
                                        <div className="flex flex-row place-content-between w-full">
                                            <Button
                                                type="submit"
                                                variant="default"
                                                size="default"
                                                className="w-40"
                                                disabled={state.isButtonDisabled}
                                            >
                                                Send message
                                            </Button>
                                        </div>
                                    </CardFooter>
                                </Form>
                            </Card>
                        )}
                    </div>
                </ScrollArea>
            </SheetContent>
        </Sheet>
    );
}

interface MessageSentProps {
    sentTo: string;
    subject: string;
    message: string;
    onClose: () => void;
}

function MessageSent({ sentTo, subject, message, onClose }: MessageSentProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg flex flex-col items-center">
                    <CircleCheckIcon
                        strokeWidth={1}
                        className="w-[50px] h-[50px]"
                    />
                    <h2 className="text-2xl pt-1">Message Sent</h2>
                </CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6 flex flex-col gap-6">
                <MessageSummary
                    sentTo={sentTo}
                    subject={subject}
                    message={message}
                />
            </CardContent>
            <Separator />
            <CardFooter className="pt-6">
                <div className="flex flex-row justify-center w-full">
                    <Button
                        variant="outline"
                        size="default"
                        className="w-40"
                        onClick={() => onClose()}
                    >
                        Close
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
}

interface MessageSummaryProps {
    sentTo: string;
    subject: string;
    message: string;
}

function MessageSummary({ sentTo, subject, message }: MessageSummaryProps) {
    return (
        <div className="flex items-stretch justify-center gap-10">
            <div className="flex flex-col gap-2">
                <span className="text-xs">Message details</span>
                <Separator
                    orientation="horizontal"
                    className="w-auto"
                />
                <div>
                    <span className="text-sm pr-2">Sent To</span>
                    <Badge
                        className="w-fit"
                        variant="secondary"
                    >
                        {sentTo}
                    </Badge>
                </div>
                <div>
                    <span className="text-sm pr-2">Subject</span>
                    <Badge
                        className="w-fit"
                        variant="secondary"
                    >
                        {subject}
                    </Badge>
                </div>
                <div>
                    <span className="text-sm pr-2">Message</span>
                    <Badge
                        className="w-fit"
                        variant="secondary"
                    >
                        {message}
                    </Badge>
                </div>
            </div>
        </div>
    );
}
