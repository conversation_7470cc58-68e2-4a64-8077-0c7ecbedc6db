import { Action, actionOwnerToUserQuery, ActionSLA, NoteType, UserProfile, WithDataId } from '@aa/data-models/common';
import { Separator } from '@aa/ui/core/separator';
import { useChange } from '@aa/ui/hooks/use-change';
import { useUserClient } from '@aa/ui/hooks/use-user-client';
import { getSLAProgress, knowColour } from '@aa/ui/lib/utils';
import { formatDistanceToNow } from 'date-fns/formatDistanceToNow';
import { AlarmClock, BanIcon, CircleAlert, Clock2, FolderCheck, LucideProps, MailOpen, Megaphone, MessageSquareCode, TriangleAlert, UserRound } from 'lucide-react';
import * as react from 'react';
import { ReactElement, useEffect, useState } from 'react';

interface ActionDetailsHeaderProps {
    action: WithDataId<Action>;
}

export function ActionDetailsHeader(props: ActionDetailsHeaderProps) {
    const [state, setState] = useState({
        action: props.action,
        owner: null as UserProfile | null | string
    });
    const [userClient] = useUserClient();

    let from: string | number | undefined;
    let subject: string | undefined;

    useChange(props.action, (newAction) => {
        setState((prevState) => ({
            ...prevState,
            action: newAction
        }));
    });

    const { slaProgress, time } = getSLAProgress(state.action);
    const { className } = knowColour(slaProgress);

    let icon: ReactElement<Omit<LucideProps, 'ref'> & react.RefAttributes<SVGSVGElement>>;
    switch (state.action.type) {
        case NoteType.MESSAGE:
            icon = (
                <MessageSquareCode
                    className={className}
                    strokeWidth={1}
                />
            );
            from = state.action.content.from;
            subject = state.action.content.subject;
            break;
        case NoteType.EMAIL:
            icon = (
                <MailOpen
                    className="text-gray-500"
                    strokeWidth={1}
                />
            );
            from = state.action.content.from;
            subject = state.action.content.subject;
            break;
        case NoteType.EVENT:
            icon = (
                <Megaphone
                    className={className}
                    strokeWidth={1}
                />
            );
            from = state.action.content.from;
            break;
        case NoteType.REMINDER:
            icon = (
                <AlarmClock
                    className={className}
                    strokeWidth={1}
                />
            );
            from = state.action.content.from;
            subject = state.action.content.subject;
            break;
        case NoteType.ALERT:
            icon = (
                <TriangleAlert
                    className={className}
                    strokeWidth={1}
                />
            );
            from = state.action.content.from;
            break;
        default:
            icon = (
                <BanIcon
                    className="text-gray-500"
                    strokeWidth={1}
                />
            );
    }

    useEffect(() => {
        if (!from) return;

        if (isNaN(Number(from))) return setState((prevState) => ({ ...prevState, owner: from as string }));
        // Setting owner in Action header
        const ownerQuery = actionOwnerToUserQuery(Number(from));
        userClient.getUser(ownerQuery).then((user) => {
            setState((prevState) => ({
                ...prevState,
                owner: `${user?.forename} ${user?.surname}` || null
            }));
        });
    }, [from]);

    return (
        <div className="flex flex-col bg-background">
            <div className="flex w-full p-4 items-center justify-between flex-row gap-1">
                <div className="shrink relative">
                    {icon}
                    {state.action.type !== 'EMAIL' && slaProgress === ActionSLA.EXPIRED ? <CircleAlert className="w-4 h-4 absolute -bottom-2 left-3.5 bg-red-500 rounded-lg text-white" /> : null}
                </div>
                <Separator
                    className="mx-2 my-auto h-10"
                    orientation="vertical"
                />
                <div className="grow">
                    {subject && <p className="line-clamp-1 mb-1 text-xl break-all">{subject}</p>}
                    <div className="flex flex-row items-center gap-3 text-xs text-gray-400">
                        {state.action && state.action.created && (
                            <div className="flex flex-row items-center gap-1">
                                <Clock2
                                    className="w-4 h-4"
                                    strokeWidth={1}
                                />
                                <span className="truncate">
                                    {formatDistanceToNow(new Date(state.action.created))} {' ago'}
                                </span>
                            </div>
                        )}
                        {from && (
                            <div className="flex flex-row items-center gap-1">
                                <UserRound
                                    className="w-4 h-4"
                                    strokeWidth={1}
                                />
                                <span className="truncate">{state.owner as string}</span>
                            </div>
                        )}
                        {state.action.parentEntity && state.action.parentEntity.id && (
                            <div className="flex flex-row items-center gap-1">
                                <FolderCheck
                                    className="w-4 h-4"
                                    strokeWidth={1}
                                />
                                <span className={'max-w-[100px] truncate'}>{state.action.parentEntity.id}</span>
                            </div>
                        )}

                        {state.action.type !== 'EMAIL' && slaProgress === ActionSLA.EXPIRED && (
                            <div className="flex gap-1 items-center">
                                <Clock2
                                    className="w-3 h-3 text-red-500"
                                    strokeWidth={1}
                                />
                                <p className={className}>Expired</p>
                                <p className={className}>{time}</p>
                            </div>
                        )}

                        {state.action.type !== 'EMAIL' && (slaProgress === ActionSLA.EXPIRING || slaProgress === ActionSLA.LATE || slaProgress === ActionSLA.ON_TIME) && (
                            <div className="flex gap-1 items-center">
                                <Clock2
                                    className="w-3 h-3"
                                    strokeWidth={1}
                                />
                                <p> Due in</p>
                                <p className={className}>{time}</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
