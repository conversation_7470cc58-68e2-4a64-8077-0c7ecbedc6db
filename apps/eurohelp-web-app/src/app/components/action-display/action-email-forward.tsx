import { MessageForward } from '@aa/data-models/common';
import { Button } from '@aa/ui/core/button';
import { Input } from '@aa/ui/core/input';
import { Textarea } from '@aa/ui/core/textarea';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useMessageClient } from '@aa/ui/hooks/use-message-client';
import { Trash2Icon } from 'lucide-react';
import { ChangeEvent, useState } from 'react';

interface ActionEmailForwardProps {
    messageId: string;
    actionId: string;
    onCancel: () => void;
}

export default function ActionEmailForward({ messageId, actionId, onCancel }: ActionEmailForwardProps) {
    const [state, setState] = useState({
        emails: [' '],
        message: ''
    });
    const [messageClient] = useMessageClient();
    const [, forwardEmailHandler] = useCallback(messageClient.forwardEmail);

    const handleForward = async (messageId: string, messageForward: MessageForward) => {
        try {
            await forwardEmail<PERSON>andler(messageId, messageForward);
            onCancel();
        } catch (error) {
            console.error('Error forwarding email:', error);
        }
    };

    const addNewRecipient = () => {
        setState((prevState) => ({
            ...prevState,
            emails: [...prevState.emails, ' ']
        }));
    };

    const onChange = (e: ChangeEvent<HTMLInputElement>, index: number) => {
        setState((prevState) => ({
            ...prevState,
            emails: prevState.emails.map((email, i) => (i === index ? e.target.value : email))
        }));
    };

    const onRemove = (index: number) => {
        setState((prevState) => ({
            ...prevState,
            emails: prevState.emails.filter((_, i) => i !== index)
        }));
    };

    const handleTextareaChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
        setState((prevState) => ({
            ...prevState,
            message: event.target.value
        }));
    };

    return (
        <div className={'pt-3 gap-2'}>
            <p className={'text-lg font-bold pb-2'}>Forward</p>
            {state.emails.map((email, index) => (
                <div className={'pb-3 flex'}>
                    <Input
                        placeholder="Recipient email"
                        onChange={(e) => onChange(e, index)}
                    />
                    <Button
                        type="button"
                        variant="ghost"
                        size="default"
                        onClick={() => onRemove(index)}
                    >
                        <Trash2Icon className="w-4 h-4" />
                    </Button>
                </div>
            ))}
            <div className={'pb-3'}>
                <Button
                    type="submit"
                    variant="secondary"
                    size="default"
                    className="w-40"
                    onClick={addNewRecipient}
                >
                    Add new recipient
                </Button>
            </div>
            <Textarea
                id="reply"
                placeholder="Provide message content"
                className="min-h-[150px] w-full pt-3"
                onChange={handleTextareaChange}
            />
            <div className="flex place-content-between w-full pt-2">
                <Button
                    type="submit"
                    variant="default"
                    size="default"
                    className="w-40"
                    onClick={() => handleForward(messageId, { recipients: state.emails, message: state.message, actionId })}
                >
                    Send
                </Button>
                <Button
                    type="button"
                    variant="secondary"
                    size="default"
                    className="w-40"
                    onClick={onCancel}
                >
                    Cancel
                </Button>
            </div>
        </div>
    );
}
