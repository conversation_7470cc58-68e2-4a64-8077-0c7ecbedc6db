import { Address, AddressData } from '@aa/data-models/common';
import { Button } from '@aa/ui/core/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@aa/ui/core/card';
import { Input } from '@aa/ui/core/input';
import { Label } from '@aa/ui/core/label';
import { Separator } from '@aa/ui/core/separator';
import { SheetClose } from '@aa/ui/core/sheet';
import { Switch } from '@aa/ui/core/switch';
import * as React from 'react';
import { useState } from 'react';

interface EditAddressProps {
    data: AddressData;
    onUpdate: (updatedAddress: AddressData) => void;
    header: string;
    description: string;
    isSendButtonDisabled?: boolean;
}

export const EditAddress = ({ data, onUpdate, header, description, isSendButtonDisabled }: EditAddressProps) => {
    const [addressData, setAddressData] = useState<AddressData>(data);

    const onSubmitForm = () => {
        onUpdate(addressData);
    };

    // PS: Nesting forms is bad idea
    function updateAddressChange(event: React.ChangeEvent<HTMLInputElement>): void {
        const match = event.target.name.match(/\d+/);
        let updatedAddressData;
        if (match) {
            const index = Number(match[0]);
            const addressLines = addressData.addressLines;
            addressLines[index] = event.target.value;
            updatedAddressData = {
                ...addressData,
                addressLines: addressLines
            };
        } else {
            updatedAddressData = {
                ...addressData,
                [event.target.name]: event.target.value
            };
        }
        setAddressData(updatedAddressData);
    }

    const handleValidAddressChange = (checked: boolean) => {
        setAddressData({
            ...addressData,
            valid: checked
        });
    };

    if (!addressData) {
        return <div>Data is loading!</div>;
    }

    return (
        <div className={'min-h-[400px]'}>
            {header && (
                <Card className={'flex flex-col min-h-[400px]'}>
                    <CardHeader className={'gap-3'}>
                        <CardTitle className="h-3 text-base">{header}</CardTitle>
                        <CardDescription className="text-xs">{description}</CardDescription>
                    </CardHeader>
                    <CardContent className={'flex flex-col'}>
                        <div className={'flex gap-3'}>
                            <div className={'flex'}>
                                <div className={'flex flex-col space-y-1'}>
                                    <div className={'text-xs'}>Postcode</div>
                                    <Input
                                        className="w-[245px]"
                                        name={'postcode'}
                                        type="text"
                                        value={addressData?.postcode || ''}
                                        placeholder={'Postcode'}
                                        onChange={updateAddressChange}
                                    />
                                </div>
                            </div>
                            <div className={'flex'}>
                                <div className={'flex flex-col space-y-1'}>
                                    <div className={'text-xs'}>House number</div>
                                    <Input
                                        className="w-[245px]"
                                        name={'houseNoName'}
                                        type="text"
                                        value={addressData?.houseNoName as string}
                                        placeholder={'House number'}
                                        onChange={updateAddressChange}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className={'flex'}>
                            <div className={'flex flex-col space-y-1 mt-2 gap-1 w-full'}>
                                <div className={'text-xs'}>Address lines</div>
                                {addressData.addressLines && addressData.addressLines.length > 0 && (
                                    <div className={'flex flex-col gap-1'}>
                                        <Input
                                            name={'addressLines[0]'}
                                            className="w-full"
                                            type="text"
                                            value={addressData.addressLines[0] || ''}
                                            placeholder={'Address line 1'}
                                            onChange={updateAddressChange}
                                        />
                                        <Input
                                            className="w-full"
                                            name={'addressLines[1]'}
                                            type="text"
                                            value={addressData.addressLines[1] || ''}
                                            placeholder={'Address line 2'}
                                            onChange={updateAddressChange}
                                        />
                                        <Input
                                            className="w-full"
                                            name={'addressLines[2]'}
                                            type="text"
                                            value={addressData.addressLines[2] || ''}
                                            placeholder={'Address line 3'}
                                            onChange={updateAddressChange}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>

                        <Separator className={'my-4'} />

                        <div className="flex items-center space-x-2">
                            <Switch
                                id="valid"
                                name={'valid'}
                                checked={addressData.valid}
                                onCheckedChange={handleValidAddressChange}
                            />
                            <Label htmlFor="valid">Address has been validated</Label>
                        </div>

                        <Button
                            className={'mt-[20px] w-40 bg-black'}
                            type="submit"
                            variant={'default'}
                            size="default"
                            onClick={onSubmitForm}
                        >
                            Save details
                        </Button>
                    </CardContent>
                </Card>
            )}
            {!header && (
                <div>
                    <div className={'flex gap-3'}>
                        <div className={'flex'}>
                            <div className={'flex flex-col space-y-1'}>
                                <div className={'text-xs'}>Postcode</div>
                                <Input
                                    className="w-[245px]"
                                    name="postcode"
                                    type="text"
                                    value={addressData.postcode || ''}
                                    placeholder={'Postcode'}
                                    onChange={updateAddressChange}
                                />
                            </div>
                        </div>
                        <div className={'flex'}>
                            <div className={'flex flex-col space-y-1'}>
                                <div className={'text-xs'}>House number</div>
                                <Input
                                    className="w-[245px]"
                                    name="houseNoName"
                                    type="text"
                                    value={addressData.houseNoName as string}
                                    placeholder={'House number'}
                                    onChange={updateAddressChange}
                                />
                            </div>
                        </div>
                    </div>
                    <div className={'flex'}>
                        <div className={'flex flex-col space-y-1 mt-2 gap-1'}>
                            <div className={'text-xs'}>Address lines</div>
                            {addressData.addressLines && addressData.addressLines.length > 0 && (
                                <div className={'flex flex-col gap-1'}>
                                    <Input
                                        className="w-[500px]"
                                        name="addressLines[0]"
                                        type="text"
                                        value={addressData.addressLines[0] || ''}
                                        placeholder={'Address line 1'}
                                        onChange={updateAddressChange}
                                    />
                                    <Input
                                        className="w-[500px]"
                                        name="addressLines[1]"
                                        type="text"
                                        value={addressData.addressLines[1] || ''}
                                        placeholder={'Address line 2'}
                                        onChange={updateAddressChange}
                                    />
                                    <Input
                                        className="w-[500px]"
                                        name="addressLines[2]"
                                        type="text"
                                        value={addressData.addressLines[2] || ''}
                                        placeholder={'Address line 3'}
                                        onChange={updateAddressChange}
                                    />
                                </div>
                            )}
                        </div>
                    </div>

                    <Separator className={'my-4'} />

                    <div className="flex items-center space-x-2">
                        <Switch
                            id="valid"
                            name={'valid'}
                            checked={addressData.valid}
                            onCheckedChange={handleValidAddressChange}
                        />
                        <Label htmlFor="valid">Address has been validated</Label>
                    </div>

                    <SheetClose asChild>
                        <Button
                            className={'mt-[20px] w-40 bg-black'}
                            type="submit"
                            variant={'default'}
                            size="default"
                            onClick={onSubmitForm}
                            disabled={isSendButtonDisabled}
                        >
                            Save details
                        </Button>
                    </SheetClose>
                </div>
            )}
        </div>
    );
};
