import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@aa/ui/core/select';
import * as React from 'react';
import { Currency } from '@aa/data-models/common';

interface ChooseCurrencyProps {
    onChange: (currency: Currency) => void;
}

export const ChooseCurrency = ({ onChange }: ChooseCurrencyProps) => {
    const currencyList = [
        { code: Currency.USD, name: 'US Dollar' },
        { code: Currency.EURO, name: 'Euro' },
        { code: Currency.GBP, name: 'British Pound' }
    ];

    return (
        <Select onValueChange={(value) => onChange(value as Currency)}>
            <SelectTrigger className="h-12 w-full">
                <SelectValue placeholder="Select currency" />
            </SelectTrigger>
            <SelectContent side="bottom">
                {currencyList.map((currency) => (
                    <SelectItem
                        key={currency.name}
                        value={`${currency.code}`}
                    >
                        {currency.name}
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    );
};
