'use client';
import { ActionStats, Namespace, NoteType, StatsNoteType, TeamPreview, WithDataId } from '@aa/data-models/common';
import { Separator } from '@aa/ui/core/separator';
import { useEnvironment } from '@aa/ui/hooks/use-environment';
import { EnvironmentMode } from '@aa/ui/providers/environment-provider';
import { Bug, BugOff, BugPlay, CircleHelp, Coins, FileX, FlaskConical, Inbox, LayoutGrid, ListChecks, MessageCircleQuestion, NotebookTabs } from 'lucide-react';
import * as React from 'react';
import { useLocation } from 'react-router-dom';
import Logo from './logo';
import { NavigationSection, NavLink } from './navigation-section';
import UserNavigation from './user-navigation';
import { DataQuery } from '@aa/data-query';
import { useNotifications } from '@aa/ui/hooks/use-notifications';
import { Source } from '@aa/data-models/system/source';
import { useActionClient } from '@aa/ui/hooks/use-action-client';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';

interface NavigationProps {
    teams: WithDataId<TeamPreview>[];
    isCollapsed?: boolean;
}

export default function Navigation({ teams, isCollapsed = false }: NavigationProps) {
    const location = useLocation();
    const environment = useEnvironment();
    const [actionClient] = useActionClient();
    const [data, fetch, isPending, error] = useCallback(actionClient.stats);
    const { isInitialPending } = useInitialPending(isPending);
    useInitialLoad(async () => fetch({ namespace: [Namespace.EUOPS] }));

    const { counters } = data || {
        counters: {
            [StatsNoteType.EMAIL]: { expired: 0, total: 0 },
            [StatsNoteType.MESSAGE]: { expired: 0, total: 0 },
            [StatsNoteType.ALERT]: { expired: 0, total: 0 },
            [StatsNoteType.EVENT]: { expired: 0, total: 0 },
            [StatsNoteType.REMINDER]: { expired: 0, total: 0 }
        }
    };

    const onChange = () => {
        fetch({ namespace: [Namespace.EUOPS] }).catch();
    };

    const query = new DataQuery({});
    useNotifications({ source: Source.ACTION_QUEUE, query }, { onChange });
    const actionTypes = [StatsNoteType.MESSAGE, StatsNoteType.EVENT, StatsNoteType.ALERT, StatsNoteType.REMINDER];
    const expiredActionsCounter = actionTypes.reduce((acc, type) => acc + (counters[type]?.expired || 0), 0);
    const totalActionsCounter = actionTypes.reduce((acc, type) => acc + (counters[type]?.total || 0), 0);

    const mainNavLinks: NavLink[] = [
        {
            title: 'Actions',
            path: `/actions`,
            label: totalActionsCounter.toString(),
            icon: ListChecks,
            notifications: Number(expiredActionsCounter)
        },
        {
            title: 'Inbox',
            path: `/`,
            label: counters[NoteType.EMAIL].total.toString(),
            icon: Inbox,
            notifications: counters[NoteType.EMAIL].expired
        },
        {
            title: 'Trips',
            path: `/trips`,
            icon: LayoutGrid
        },
        // TODO: Uncomment when ready
        //     title: 'Ongoing tasks',
        //     path: `/tasks`,
        //     label: '128',
        //     icon: LayoutList,
        // },
        {
            title: 'Contacts',
            path: `/contacts`,
            icon: NotebookTabs
        }
    ];

    const adminNavLinks: NavLink[] = [
        {
            title: 'Accounting',
            path: `/accounting`,
            icon: Coins
        }
        // TODO: Uncomment when ready
        // {
        //     title: 'Team management',
        //     path: `/teams`,
        //     icon: UsersRound,
        // },
    ];

    const auxNavLinks: NavLink[] = [
        {
            title: 'Feedback',
            path: `/feedback`,
            icon: MessageCircleQuestion
        },
        {
            title: 'Support',
            path: `/support`,
            icon: CircleHelp
        }
    ];

    const devNavLinks: NavLink[] = [
        {
            title: 'Not Found',
            path: `/not-found`,
            icon: FileX
        },
        {
            title: 'Admin',
            path: `/developer/admin`,
            icon: FileX
        },
        {
            title: 'Error: crash app',
            path: `/developer/error-test/crash-app`,
            icon: Bug
        },
        {
            title: 'Error: crash route',
            path: `/developer/error-test/crash-route`,
            icon: BugPlay
        },
        {
            title: 'Error: no crash',
            path: `/developer/error-test/no-crash`,
            icon: BugOff
        },
        {
            title: 'App Error comp',
            path: `/developer/error-test/app-error-component`,
            icon: BugPlay
        },
        {
            title: 'Route Error comp',
            path: `/developer/error-test/route-error-component`,
            icon: BugPlay
        },
        {
            title: 'Playground',
            path: `/developer/playground`,
            icon: FlaskConical
        },
        {
            title: 'Components',
            path: `/developer/components`,
            icon: FlaskConical
        }
    ];

    const currentRoute = location.pathname;
    const isDevMode = environment.mode === EnvironmentMode.DEVELOPER;

    return (
        <div className="flex flex-col h-full">
            <Logo isCollapsed={isCollapsed} />
            <Separator />
            {/*<TeamSwitcher*/}
            {/*    isCollapsed={isCollapsed}*/}
            {/*    teams={teams}*/}
            {/*/>*/}
            {/*<Separator />*/}
            <NavigationSection
                isCollapsed={isCollapsed}
                links={mainNavLinks}
                currentRoute={currentRoute}
            />
            <Separator />
            <NavigationSection
                isCollapsed={isCollapsed}
                links={adminNavLinks}
                currentRoute={currentRoute}
            />
            <Separator />
            {/*// TODO: Uncomment when ready*/}
            {/*<NavigationSection*/}
            {/*    isCollapsed={isCollapsed}*/}
            {/*    links={auxNavLinks}*/}
            {/*    currentRoute={currentRoute}*/}
            {/*    className="grow"*/}
            {/*/>*/}
            {isDevMode && (
                <>
                    <Separator />
                    <NavigationSection
                        isCollapsed={isCollapsed}
                        links={devNavLinks}
                        currentRoute={currentRoute}
                    />
                </>
            )}
            <Separator className="mt-auto" />
            <UserNavigation
                className="py-4"
                isCollapsed={isCollapsed}
            />
        </div>
    );
}
