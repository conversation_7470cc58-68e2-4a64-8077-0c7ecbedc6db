'use client';

import { BrandLogos } from '@aa/ui/branding/brand-logos';
import { useEnvironment } from '@aa/ui/hooks/use-environment';
import { cn } from '@aa/ui/lib/utils';
import * as React from 'react';
import { Link } from 'react-router-dom';

interface LogoProps {
    isCollapsed: boolean;
}

export default function Logo({ isCollapsed }: LogoProps) {
    const environment = useEnvironment();
    return (
        <Link
            to="/"
            className={cn('flex h-[52px] items-center ', isCollapsed ? 'justify-center h-[52px]' : 'px-2')}
        >
            {isCollapsed ? (
                <BrandLogos.aa className="h-4 w-4" />
            ) : (
                <>
                    <BrandLogos.aa className="h-4 w-4 ml-2 mr-2" />
                    <span>Eurohelp</span>
                </>
            )}
        </Link>
    );
}
