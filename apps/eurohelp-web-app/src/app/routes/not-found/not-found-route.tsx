import { useEnvironment } from '@aa/ui/hooks/use-environment';
import * as React from 'react';
import { Link } from 'react-router-dom';

export default function NotFoundRoute() {
    const environment = useEnvironment();
    return (
        <main className="flex min-h-screen flex-col items-center justify-center bg-gray-100 px-4 py-12 dark:bg-gray-900">
            <div className="max-w-md space-y-4 text-center">
                <p className="text-base font-semibold text-gray-900">404</p>
                <h1 className="text-4xl font-bold tracking-tighter text-gray-900 dark:text-gray-50">Page Not Found</h1>
                <p className="text-gray-500 dark:text-gray-400">The page you're looking for doesn't exist or has been moved.</p>
                <Link
                    to="/"
                    className="inline-flex h-10 items-center justify-center rounded-md bg-gray-900 px-6 text-sm font-medium text-gray-50 shadow transition-colors hover:bg-gray-900/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:pointer-events-none disabled:opacity-50 dark:bg-gray-50 dark:text-gray-900 dark:hover:bg-gray-50/90 dark:focus-visible:ring-gray-300"
                >
                    Go to Homepage
                </Link>
            </div>
        </main>
    );
}
