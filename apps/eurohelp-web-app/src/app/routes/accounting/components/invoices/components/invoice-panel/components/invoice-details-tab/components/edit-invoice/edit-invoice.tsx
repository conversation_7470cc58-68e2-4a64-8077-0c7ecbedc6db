import { Currency, Invoice, NativeValue, CreateInvoice, CreateInvoiceSchema, Value, UpdateInvoiceSchema, UpdateInvoice } from '@aa/data-models/common';
import { Schema, SchemaErrors } from '@aa/schema';
import { Alert, AlertDescription, AlertTitle } from '@aa/ui/core/alert';
import { Button } from '@aa/ui/core/button';
import { Card, CardContent, CardDescription, CardHeader } from '@aa/ui/core/card';
import { Combobox } from '@aa/ui/core/combobox';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from '@aa/ui/core/form';
import { Input } from '@aa/ui/core/input';
import { Separator } from '@aa/ui/core/separator';
import { Switch } from '@aa/ui/core/switch';
import { Textarea } from '@aa/ui/core/textarea';
import { Hint } from '@aa/ui/hoc/hint/hint';
import { useBillingClient } from '@aa/ui/hooks/use-billing-client';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useChange } from '@aa/ui/hooks/use-change';
import { useInitialLoad } from '@aa/ui/hooks/use-initial-load';
import { useInitialPending } from '@aa/ui/hooks/use-initial-pending';
import { Utils } from '@aa/utils';
import { format } from 'date-fns/format';
import { CircleAlertIcon, Clock } from 'lucide-react';
import React, { ChangeEvent, useEffect, useState } from 'react';
import SchedulePopover from '../../../../../../../../../../components/various/schedule-popover';
import { useGrossCalculation } from '../../../../../../../../../../utils/hooks/use-gross-calculation';
import { MonetaryValueSummary } from '../invoice-details/components/monetary-value-summary';
import { SelectSupplierPanel } from './components/select-supplier-panel';

export interface EditInvoiceProps {
    invoice?: Invoice | null;
    onSubmit: (invoice: Invoice) => void;
    onCancel: () => void;
}

export const EditInvoice = ({ onCancel, onSubmit, ...props }: EditInvoiceProps) => {
    const [isNew] = useState(!props.invoice?.code);
    const [invoice, setInvoice] = useState<Partial<Invoice>>(
        props.invoice || {
            issued: Utils.generateDayDate(new Date()),
            currency: Currency.GBP,
            net: { currency: Currency.GBP, amount: 0 },
            vat: { currency: Currency.GBP, amount: 0 },
            gross: { currency: Currency.GBP, amount: 0 },
            grossNative: { currency: Currency.GBP, amount: 0 },
            ref: '',
            prePaid: false,
            comments: ''
        }
    );
    // TODO: handle display of errors
    const [errors, setErrors] = useState<SchemaErrors<typeof CreateInvoiceSchema>>({});

    const onUpsertComplete = (invoice: Invoice) => {
        onSubmit(invoice);
    };

    const [client] = useBillingClient();
    const [, create, isCreatePending, createError] = useCallback(client.createInvoice, { onComplete: onUpsertComplete });
    const [, update, isUpdatePending, updateError] = useCallback(client.updateInvoice, { onComplete: onUpsertComplete });

    const onSubmitForm = async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        event.stopPropagation();
        const code = invoice.code || null;

        if (code) {
            const parsed = UpdateInvoiceSchema.safeParse(invoice);
            if (parsed.success) {
                update(code, parsed.data).catch();
            } else {
                const dataErrors = Schema.errors(UpdateInvoiceSchema, invoice) || {};
                setErrors(dataErrors);
            }
        } else {
            const parsed = CreateInvoiceSchema.safeParse(invoice);
            if (parsed.success) {
                create(parsed.data).catch();
            } else {
                const dataErrors = Schema.errors(CreateInvoiceSchema, invoice) || {};
                setErrors(dataErrors);
            }
        }
    };

    const onCancelEdit = async () => {
        onCancel();
    };

    const onFormChange = (partialInvoice: Partial<Invoice>) => {
        setInvoice((prevState) => ({ ...prevState, ...partialInvoice }));
    };

    return (
        <Card className="flex flex-col">
            <CardHeader>
                <CardDescription className="text-xs">{isNew ? 'To create new invoice we need few details.' : 'Please modify and save updated invoice details.'}</CardDescription>
            </CardHeader>
            <Separator className="mb-6" />
            <Form onSubmit={onSubmitForm}>
                <CardContent className="flex flex-col">
                    <EditDetailsForm
                        invoice={invoice}
                        onChange={onFormChange}
                    />
                </CardContent>
                {!!(invoice.gross && invoice.grossNative) && (
                    <>
                        <Separator className="mb-6" />
                        <CardContent className="flex flex-col">
                            <MonetaryValueSummary
                                gross={invoice.gross}
                                grossNative={invoice.grossNative}
                            />
                        </CardContent>
                    </>
                )}
                <Separator className="mb-6" />
                {/* <CardContent className="flex flex-col">
                    <EditCommentsForm
                        invoice={invoice}
                        onChange={onFormChange}
                    />
                </CardContent> */}
                {/* <Separator className="mb-6" /> */}
                <CardContent>
                    <EditActions
                        isNew={isNew}
                        isAmountZero={invoice.net?.amount === 0 || invoice.net?.amount === undefined}
                        onCancel={onCancelEdit}
                    />
                </CardContent>
            </Form>
        </Card>
    );
};

interface EditDetailsFormProps {
    invoice?: Partial<Invoice> | null;
    onChange: (invoice: Partial<Invoice>) => void;
}

const EditDetailsForm = ({ invoice, onChange }: EditDetailsFormProps) => {
    const [billingClient] = useBillingClient();
    const [formData, setFormData] = useState<Partial<Invoice>>(invoice || {});
    const currencies: { value: string; label: string }[] = Object.keys(Currency).map((key) => {
        return { value: key, label: key };
    });
    useChange(formData, () => onChange(formData));

    const onGrossCalculation = (gross: Value, grossNative: NativeValue) => {
        setFormData((prevState) => ({
            ...prevState,
            gross,
            grossNative
        }));
        onChange({ gross, grossNative });
    };
    useGrossCalculation({ ...formData, onChange: onGrossCalculation });

    const onRefChange = (event: ChangeEvent<HTMLInputElement>) => {
        const ref = event.target.value;
        setFormData((prevState) => ({
            ...prevState,
            ref
        }));
    };

    const onPurchaseOrderRefChange = (event: ChangeEvent<HTMLInputElement>) => {
        const purchaseOrderRef = event.target.value;
        setFormData((prevState) => ({
            ...prevState,
            purchaseOrderRef
        }));
    };

    const onNetChange = (event: ChangeEvent<HTMLInputElement>) => {
        const netAmount = Number.parseFloat(event.target.value || '');
        setFormData((prevState) => ({
            ...prevState,
            net: { amount: netAmount, currency: prevState.currency || Currency.GBP }
        }));
    };

    const onVatChange = (event: ChangeEvent<HTMLInputElement>) => {
        const vatAmount = Number.parseFloat(event.target.value || '');
        setFormData((prevState) => ({
            ...prevState,
            vat: { amount: vatAmount, currency: prevState.currency || Currency.GBP }
        }));
    };

    const onPrePaidChange = (checked: boolean) => {
        setFormData((prevState) => ({
            ...prevState,
            prePaid: checked
        }));
    };

    const onSupplierChange = (supplierResourceId: number | null) => {
        setFormData((prevState) => ({
            ...prevState,
            supplierResourceId: supplierResourceId || undefined
        }));
    };

    const onCurrencyChange = (currency: string) => {
        const updatedCurrency = currency ? Currency[currency as Currency] : undefined;
        setFormData((prevState) => ({
            ...prevState,
            currency: updatedCurrency,
            net: { amount: prevState?.net?.amount || 0, currency: updatedCurrency || Currency.GBP },
            vat: { amount: prevState?.vat?.amount || 0, currency: updatedCurrency || Currency.GBP }
        }));
    };

    const onDateChange = (date: Date, fieldNameToUpdate: string) => {
        setFormData((prevState) => ({
            ...prevState,
            [fieldNameToUpdate]: Utils.generateDayDate(new Date(date))
        }));
    };

    const onFieldChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
        const data = { [event.target.name]: event.target.value };
        setFormData((prevState) => ({ ...prevState, ...data }));
        // onChange(data);
    };

    const [canUpdateCurrecyData, fetch, isPending, error] = useCallback(billingClient.canUpdateCurrencyInvoice);
    const { isInitialPending } = useInitialPending(isPending);
    // trigger load when open and we have creditNote
    useInitialLoad(() => fetch(invoice?.code || ''), !!invoice?.code);

    useEffect(() => {
        if (invoice?.code && !isInitialPending) {
            fetch(invoice.code).catch();
        }
    }, [invoice, isInitialPending]);

    const { valid: canUpdateCurrency, message: canUpdateCurrencyMessage } = canUpdateCurrecyData || { valid: !invoice?.code };

    const canEditCurrencyHint = (
        <Alert className="border-none">
            <CircleAlertIcon className="h-4 w-4" />
            <AlertTitle>Change of currency not allowed</AlertTitle>
            <AlertDescription>{canUpdateCurrencyMessage || 'Change not allowed due to unknown reason'}</AlertDescription>
        </Alert>
    );

    return (
        <div className="space-y-4">
            <SelectSupplierPanel
                supplierResourceId={formData.supplierResourceId || null}
                onChange={onSupplierChange}
            />

            <FormItem className="w-full max-w-sm flex flex-col">
                <FormLabel htmlFor="currency">Currency</FormLabel>
                <FormField name="currency">
                    <div className="flex gap-2">
                        <FormControl>
                            {canUpdateCurrency ? (
                                <Combobox
                                    // disabled={isPending}
                                    value={formData.currency}
                                    items={currencies}
                                    notFoundLabel="No currency found"
                                    noValueLabel="Select currency"
                                    searchPlaceholder="Select currency"
                                    onSelect={onCurrencyChange}
                                />
                            ) : (
                                <div className="flex flex-col space-y-4">
                                    <Hint
                                        content={canEditCurrencyHint}
                                        asChild
                                    >
                                        <Combobox
                                            disabled={!canUpdateCurrency}
                                            value={formData.currency}
                                            items={currencies}
                                            notFoundLabel="No currency found"
                                            noValueLabel="Select currency"
                                            searchPlaceholder="Select currency"
                                            onSelect={onCurrencyChange}
                                        />
                                    </Hint>
                                </div>
                            )}
                        </FormControl>
                    </div>
                </FormField>
                <FormDescription className="text-xs text-gray-400">Select main currency for the invoice.</FormDescription>
            </FormItem>

            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormLabel
                    className={'text-xs'}
                    htmlFor="ref"
                >
                    Invoice number
                </FormLabel>
                <FormField name="ref">
                    <FormControl>
                        <Input
                            type="text"
                            onChange={onRefChange}
                            placeholder="Reference name"
                            value={formData.ref}
                        />
                    </FormControl>
                </FormField>
            </FormItem>

            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormLabel
                    className={'text-xs'}
                    htmlFor="purchaseOrderRef"
                >
                    Purchase order
                </FormLabel>
                <FormField name="purchaseOrderRef">
                    <FormControl>
                        <Input
                            type="text"
                            onChange={onPurchaseOrderRefChange}
                            placeholder="Purchase order"
                            value={formData.purchaseOrderRef}
                        />
                    </FormControl>
                </FormField>
            </FormItem>

            <FormItem>
                <FormLabel htmlFor="issued">Issued date</FormLabel>
                <div className="flex flex-row">
                    <FormField name="issued">
                        <FormControl>
                            <Input
                                type="text"
                                value={format(formData.issued ? new Date(formData.issued) : Utils.generateDayDate(new Date()), 'dd.MM.yyyy')}
                                required
                            />
                        </FormControl>
                    </FormField>
                    <SchedulePopover
                        onChange={(date) => onDateChange(date, 'issued')}
                        allowFuture={true}
                        allowPast={true}
                        showExampleDates={false}
                    >
                        <Button
                            variant="ghost"
                            size="icon"
                        >
                            <Clock className="h-4 w-4" />
                            <span className="sr-only">Set issued date</span>
                        </Button>
                    </SchedulePopover>
                </div>
            </FormItem>

            <div className="flex gap-4">
                <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                    <FormLabel
                        className={'text-xs'}
                        htmlFor="net"
                    >
                        Net value
                    </FormLabel>
                    <FormField name="net">
                        <FormControl>
                            <Input
                                disabled={!formData.currency}
                                type="number"
                                onChange={onNetChange}
                                placeholder="amount"
                                value={formData.net?.amount}
                                min={1}
                            />
                        </FormControl>
                    </FormField>
                    {formData.net?.amount === 0 && (
                        <div className="flex gap-4">
                            <p className={'text-xs'}>NOTE: Net Value cannot be 0.</p>
                        </div>
                    )}
                </FormItem>

                <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                    <FormLabel
                        className={'text-xs'}
                        htmlFor="vat"
                    >
                        Vat value
                    </FormLabel>
                    <FormField name="vat">
                        <FormControl>
                            <Input
                                disabled={!formData.currency}
                                type="number"
                                onChange={onVatChange}
                                placeholder="amount"
                                value={formData.vat?.amount}
                            />
                        </FormControl>
                    </FormField>
                </FormItem>
            </div>

            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormField
                    name="prePaid"
                    className="flex gap-2 items-center"
                >
                    <FormControl>
                        <Switch
                            id="prePaid"
                            name={'prePaid'}
                            checked={formData.prePaid}
                            onCheckedChange={onPrePaidChange}
                        />
                    </FormControl>
                    <FormLabel
                        className={'text-xs'}
                        htmlFor="prePaid"
                    >
                        Pre-paid invoice
                    </FormLabel>
                </FormField>
            </FormItem>
            <FormItem className={'flex'}>
                <div className={'flex flex-col w-full space-y-1'}>
                    <FormLabel
                        className={'text-xs'}
                        htmlFor="comments"
                    >
                        Comments
                    </FormLabel>
                    <FormField name="comments">
                        <FormControl>
                            <Textarea
                                className="w-full"
                                placeholder="Type your comments here"
                                value={formData.comments}
                                onChange={onFieldChange}
                            />
                        </FormControl>
                    </FormField>
                </div>
            </FormItem>
        </div>
    );
};

interface EditCommentsFormProps {
    invoice?: Partial<Invoice> | null;
    onChange: (invoice: Partial<Invoice>) => void;
}

const EditCommentsForm = ({ invoice, onChange }: EditCommentsFormProps) => {
    const [formData, setFormData] = useState<Partial<Invoice>>(invoice || {});

    const onFieldChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
        const data = { [event.target.name]: event.target.value };
        setFormData((prevState) => ({ ...prevState, ...data }));
        onChange(formData);
    };

    return (
        <FormItem className={'flex'}>
            <div className={'flex flex-col w-full space-y-1'}>
                <FormLabel
                    className={'text-xs'}
                    htmlFor="comments"
                >
                    Comments
                </FormLabel>
                <FormField name="comments">
                    <FormControl>
                        <Textarea
                            className="w-full"
                            placeholder="Type your comments here"
                            value={formData.comments}
                            onChange={onFieldChange}
                        />
                    </FormControl>
                </FormField>
            </div>
        </FormItem>
    );
};

interface EditActionsProps {
    isNew: boolean;
    isAmountZero: boolean;
    onCancel: () => void;
}

const EditActions = ({ onCancel, isNew, isAmountZero }: EditActionsProps) => {
    return (
        <div className="flex justify-between w-full">
            <div className="flex items-center space-x-2">
                <Button
                    type="submit"
                    variant="outline"
                    disabled={isAmountZero}
                >
                    {isNew ? 'Create invoice' : 'Save changes'}
                </Button>
            </div>
            <Button
                variant="outline"
                type="button"
                onClick={() => onCancel()}
            >
                Cancel
            </Button>
        </div>
    );
};
