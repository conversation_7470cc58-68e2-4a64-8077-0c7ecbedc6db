import { Cur<PERSON>cy, Invoice, InvoiceLineItem, NativeValue, UpsertInvoiceLineItem, UpsertInvoiceLineItemSchema, Value } from '@aa/data-models/common';
import { Schema, SchemaErrors } from '@aa/schema';
import { But<PERSON> } from '@aa/ui/core/button';
import { Card, CardContent, CardDescription, CardHeader } from '@aa/ui/core/card';
import { Combobox } from '@aa/ui/core/combobox';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from '@aa/ui/core/form';
import { Input } from '@aa/ui/core/input';
import { Separator } from '@aa/ui/core/separator';
import { Switch } from '@aa/ui/core/switch';
import { useBillingClient } from '@aa/ui/hooks/use-billing-client';
import { useCallback } from '@aa/ui/hooks/use-callback';
import { useChange } from '@aa/ui/hooks/use-change';
import React, { ChangeEvent, useState } from 'react';
import { useGrossCalculation } from '../../../../../../../../../utils/hooks/use-gross-calculation';
import { MonetaryValueSummary } from '../../invoice-details-tab/components/invoice-details/components/monetary-value-summary';

export interface EditLineItemProps {
    invoice: Invoice;
    lineItem?: InvoiceLineItem | null;
    onSubmit: (lineItem: InvoiceLineItem) => void;
    onCancel: () => void;
}

export const EditLineItem = ({ onCancel, onSubmit, ...props }: EditLineItemProps) => {
    const [isNew] = useState(!props.lineItem?.code);
    const [invoice] = useState<Invoice>(props.invoice);
    const [lineItem, setLineItem] = useState<Partial<InvoiceLineItem>>(
        props.lineItem || {
            invoiceCode: invoice.code,
            currency: invoice.currency,
            net: { currency: invoice.currency, amount: 0 },
            vat: { currency: invoice.currency, amount: 0 },
            gross: { currency: invoice.currency, amount: 0 },
            grossNative: { currency: Currency.GBP, amount: 0 },
            ref: '',
            reverseCharge: false
        }
    );
    // TODO: handle display of errors
    const [errors, setErrors] = useState<SchemaErrors<typeof UpsertInvoiceLineItemSchema>>({});

    const onUpsertComplete = (lineItem: InvoiceLineItem) => {
        onSubmit(lineItem);
    };

    const [client] = useBillingClient();
    const [, createHandler] = useCallback(client.createInvoiceLineItem, { onComplete: onUpsertComplete });
    const [, updateHandler] = useCallback(client.updateInvoiceLineItem, { onComplete: onUpsertComplete });

    const upsert = async (code: string | null, data: UpsertInvoiceLineItem): Promise<void> => {
        if (code) {
            return updateHandler(code, data).catch();
        }

        return createHandler(data).catch().catch();
    };

    const onSubmitForm = async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        event.stopPropagation();
        const code = lineItem.code || null;
        const parsed = UpsertInvoiceLineItemSchema.safeParse(lineItem);
        if (parsed.success) {
            upsert(code, parsed.data).catch();
        } else {
            const dataErrors = Schema.errors(UpsertInvoiceLineItemSchema, lineItem) || {};
            setErrors(dataErrors);
        }
    };

    const onCancelEdit = async () => {
        onCancel();
    };

    const onFormChange = (data: Partial<InvoiceLineItem>) => {
        setLineItem((prevState) => ({ ...lineItem, ...data }));
    };

    return (
        <Card className="flex flex-col">
            <CardHeader>
                <CardDescription className="text-xs">{isNew ? 'To create new line item we need few details.' : 'Please modify and save updated line item details.'}</CardDescription>
            </CardHeader>
            <Separator className="mb-6" />
            <Form onSubmit={onSubmitForm}>
                <CardContent className="flex flex-col">
                    <EditDetailsForm
                        invoice={invoice}
                        lineItem={lineItem}
                        onChange={onFormChange}
                    />
                </CardContent>
                {!!(lineItem.gross && lineItem.grossNative) && (
                    <>
                        <Separator className="mb-6" />
                        <CardContent className="flex flex-col">
                            <MonetaryValueSummary
                                gross={lineItem.gross}
                                grossNative={lineItem.grossNative}
                            />
                        </CardContent>
                    </>
                )}
                <Separator className="mb-6" />
                <CardContent>
                    <EditActions
                        isNew={isNew}
                        isAmountZero={lineItem.net?.amount === 0 || lineItem.net?.amount === undefined}
                        onCancel={onCancelEdit}
                    />
                </CardContent>
            </Form>
        </Card>
    );
};

interface EditDetailsFormProps {
    invoice: Invoice;
    lineItem?: Partial<InvoiceLineItem> | null;
    onChange: (lineItem: Partial<InvoiceLineItem>) => void;
}

const EditDetailsForm = ({ onChange, invoice, ...props }: EditDetailsFormProps) => {
    const [formData, setFormData] = useState<Partial<InvoiceLineItem>>(props.lineItem || {});
    const currencies: { value: string; label: string }[] = Object.keys(Currency).map((key) => {
        return { value: key, label: key };
    });
    useChange(formData, () => onChange(formData));

    const onGrossCalculation = (gross: Value, grossNative: NativeValue) => {
        console.log({ gross, grossNative });
        setFormData((prevState) => ({
            ...prevState,
            gross,
            grossNative
        }));
    };
    useGrossCalculation({ ...formData, onChange: onGrossCalculation });

    const onRefChange = (event: ChangeEvent<HTMLInputElement>) => {
        const ref = event.target.value;
        setFormData((prevState) => ({
            ...prevState,
            ref
        }));
    };

    const onNetChange = (event: ChangeEvent<HTMLInputElement>) => {
        const netAmount = Number.parseFloat(event.target.value || '');
        setFormData((prevState) => ({
            ...prevState,
            net: { amount: netAmount, currency: prevState.currency || Currency.GBP }
        }));
    };

    const onVatChange = (event: ChangeEvent<HTMLInputElement>) => {
        const vatAmount = Number.parseFloat(event.target.value || '');
        setFormData((prevState) => ({
            ...prevState,
            vat: { amount: vatAmount, currency: prevState.currency || Currency.GBP }
        }));
    };

    const onReverseChargeChange = (checked: boolean) => {
        setFormData((prevState) => ({
            ...prevState,
            reverseCharge: checked
        }));
    };

    return (
        <div className="space-y-4">
            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormLabel
                    className={'text-xs'}
                    htmlFor="ref"
                >
                    Reference name
                </FormLabel>
                <FormField name="ref">
                    <FormControl>
                        <Input
                            type="text"
                            onChange={onRefChange}
                            placeholder="Reference name"
                            value={formData.ref}
                        />
                    </FormControl>
                </FormField>
            </FormItem>

            <FormItem className="w-full max-w-sm flex flex-col">
                <FormLabel htmlFor="currency">Currency</FormLabel>
                <FormField name="currency">
                    <div className="flex gap-2">
                        <FormControl>
                            <Combobox
                                disabled={true}
                                value={formData.currency}
                                items={currencies}
                                notFoundLabel="No currency found"
                                noValueLabel="Select currency"
                                searchPlaceholder="Select currency"
                            />
                        </FormControl>
                    </div>
                </FormField>
                <FormDescription className="text-xs text-gray-400">Your invoice currency.</FormDescription>
            </FormItem>

            <div className="flex gap-4">
                <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                    <FormLabel
                        className={'text-xs'}
                        htmlFor="net"
                    >
                        Net value
                    </FormLabel>
                    <FormField name="net">
                        <FormControl>
                            <Input
                                type="number"
                                onChange={onNetChange}
                                placeholder="amount"
                                value={formData.net?.amount}
                                min={1}
                            />
                        </FormControl>
                    </FormField>
                    {formData.net?.amount === 0 && (
                        <div className="flex gap-4">
                            <p className={'text-xs'}>NOTE: Net Value cannot be 0.</p>
                        </div>
                    )}
                </FormItem>

                <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                    <FormLabel
                        className={'text-xs'}
                        htmlFor="vat"
                    >
                        Vat value
                    </FormLabel>
                    <FormField name="vat">
                        <FormControl>
                            <Input
                                type="number"
                                onChange={onVatChange}
                                placeholder="amount"
                                value={formData.vat?.amount}
                            />
                        </FormControl>
                    </FormField>
                </FormItem>
            </div>

            <FormItem className="w-full max-w-sm flex flex-col mt-2 space-y-1">
                <FormField
                    name="reverseCharge"
                    className="flex gap-2 items-center"
                >
                    <FormControl title={invoice.prePaid ? 'Reverse charge not allowed for pre-paid invoices' : ''}>
                        <Switch
                            disabled={!!invoice.prePaid}
                            id="reverseCharge"
                            name={'reverseCharge'}
                            checked={formData.reverseCharge}
                            onCheckedChange={onReverseChargeChange}
                        />
                    </FormControl>
                    <FormLabel
                        className={'text-xs'}
                        htmlFor="reverseCharge"
                    >
                        Reverse charge
                    </FormLabel>
                </FormField>
            </FormItem>
        </div>
    );
};

interface EditActionsProps {
    isNew: boolean;
    isAmountZero: boolean;
    onCancel: () => void;
}

const EditActions = ({ onCancel, isNew, isAmountZero }: EditActionsProps) => {
    return (
        <div className="flex justify-between w-full">
            <div className="flex items-center space-x-2">
                <Button
                    type="submit"
                    variant="outline"
                    disabled={isAmountZero}
                >
                    {isNew ? 'Add to invoice' : 'Save changes'}
                </Button>
            </div>
            <Button
                variant="outline"
                type="button"
                onClick={() => onCancel()}
            >
                Cancel
            </Button>
        </div>
    );
};
