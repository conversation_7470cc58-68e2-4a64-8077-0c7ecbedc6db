'use client';

import { BenefitType, CurrencySymbol, TripCost, TripCostType, TripCostTypeLabels, Value } from '@aa/data-models/common';
import { DataTableColumnHeader } from '@aa/ui/hoc/data-table/data-table-column-header';
import { DataTableFacetedFilter } from '@aa/ui/hoc/data-table/data-table-faceted-filter';
import { DataTableTextFilter } from '@aa/ui/hoc/data-table/data-table-text-filter';
import { ColumnDef } from '@tanstack/react-table';

export const textFilters: DataTableTextFilter[] = [
    {
        column: 'query',
        placeholder: 'Filter...',
        className: 'min-w-[400px]'
    }
];
export const benefitType: { value: BenefitType; label: string }[] = [
    {
        value: BenefitType.RSS,
        label: 'RSS'
    },
    {
        value: BenefitType.RECOVERY,
        label: 'Recovery'
    },
    {
        value: BenefitType.HOTEL,
        label: 'Hotel'
    },
    {
        value: BenefitType.STORAGE,
        label: 'Storage'
    },
    {
        value: BenefitType.GARAGE_REPAIR,
        label: 'Garage repair'
    },
    {
        value: BenefitType.PUBLIC_TRANSPORT,
        label: 'Public transport'
    },
    {
        value: BenefitType.CAR_HIRE,
        label: 'Car hire'
    }
];

export const facetedFilters: DataTableFacetedFilter[] = [
    {
        column: 'benefitLimitId',
        options: benefitType,
        title: 'Benefit Type'
    }
];

export const selectCostPanelListColumns: ColumnDef<TripCost>[] = [
    {
        accessorKey: 'name',
        header: ({ column }) => (
            <DataTableColumnHeader
                className="w-[100px] truncate"
                column={column}
                title="Name"
            />
        ),
        cell: ({ row }) => <div className="w-[100px] truncate">{row.getValue('name')}</div>,
        enableSorting: false,
        enableHiding: false
    },
    {
        accessorKey: 'type',
        header: ({ column }) => (
            <DataTableColumnHeader
                className="w-[100px] truncate"
                column={column}
                title="Type"
            />
        ),
        cell: ({ row }) => <div className="w-[100px] truncate">{TripCostTypeLabels[row.getValue('type') as TripCostType]}</div>,
        enableSorting: false,
        enableHiding: false
    },
    {
        accessorKey: 'tripCode',
        header: ({ column }) => (
            <DataTableColumnHeader
                className="w-[100px] truncate"
                column={column}
                title="TripCode"
            />
        ),
        cell: ({ row }) => <div className="max-w-[100px] truncate">{row.getValue('tripCode')}</div>,
        enableSorting: false,
        enableHiding: false
    },
    {
        accessorKey: 'serviceRequesterRef',
        header: ({ column }) => (
            <DataTableColumnHeader
                className="w-[100px] truncate"
                column={column}
                title="Reference"
            />
        ),
        cell: ({ row }) => <div className="w-[100px] truncate">{row.getValue('serviceRequesterRef')}</div>,
        enableSorting: false,
        enableHiding: false
    },
    {
        accessorKey: 'forecast',
        header: ({ column }) => (
            <DataTableColumnHeader
                className="w-[70px] truncate"
                column={column}
                title="Gross"
            />
        ),
        cell: ({ row }) => (
            <div className="w-[70px] truncate">
                {CurrencySymbol[(row.getValue('forecast') as Value).currency]}
                {(row.getValue('forecast') as Value).amount}
            </div>
        ),
        enableSorting: false,
        enableHiding: false
    },
    {
        accessorKey: 'forecastNative',
        header: ({ column }) => (
            <DataTableColumnHeader
                className="w-[70px] truncate"
                column={column}
                title="Native Gross"
            />
        ),
        cell: ({ row }) => (
            <div className="w-[70px] truncate">
                {CurrencySymbol[(row.getValue('forecastNative') as Value).currency]}
                {(row.getValue('forecastNative') as Value).amount}
            </div>
        ),
        enableSorting: false,
        enableHiding: false
    },
    {
        accessorKey: 'query',
        header: undefined,
        enableHiding: true
    },
    {
        accessorKey: 'benefitLimitId',
        header: undefined,
        cell: undefined,
        enableHiding: true
    }
];
