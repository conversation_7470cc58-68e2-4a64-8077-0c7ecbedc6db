import { useEnvironment } from '@aa/ui/hooks/use-environment';
import * as React from 'react';
import { Link } from 'react-router-dom';
import { AuthForm } from './components/auth-form';

interface LoginRouteProps extends React.HTMLAttributes<HTMLDivElement> {}

export default function LoginRoute(props: LoginRouteProps) {
    const environment = useEnvironment();
    return (
        <div {...props}>
            <div className="flex flex-col space-y-2 text-center mb-6">
                <h1 className="text-2xl font-semibold tracking-tight">Authentication</h1>
                <p className="text-sm text-muted-foreground">To access this application please select from the following methods of authentication</p>
            </div>
            <AuthForm />
            <p className="mt-6 px-8 text-center text-sm text-muted-foreground">
                By authenticating, you agree to our{' '}
                <Link
                    to={`/terms`}
                    className="underline underline-offset-4 hover:text-primary"
                >
                    Terms of Service
                </Link>{' '}
                and{' '}
                <Link
                    to={`/privacy`}
                    className="underline underline-offset-4 hover:text-primary"
                >
                    Privacy Policy
                </Link>
                .
            </p>
        </div>
    );
}
