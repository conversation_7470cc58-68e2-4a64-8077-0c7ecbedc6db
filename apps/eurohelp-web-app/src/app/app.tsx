import { Namespace } from '@aa/data-models/common';
import { FrontendApplication } from '@aa/identifiers';
import { RequireAuth } from '@aa/ui/guards/require-auth';
import { ApplicationIdentityProvider } from '@aa/ui/providers/application-identity-provider';
import { AuthProvider } from '@aa/ui/providers/auth-provider';
import { ConfigProvider } from '@aa/ui/providers/config-provider';
import { EnvironmentProvider } from '@aa/ui/providers/environment-provider';
import { PublicConfigProvider } from '@aa/ui/providers/public-config-provider';
import { UserProfileProvider } from '@aa/ui/providers/user-profile-provider';
import * as React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Route, Routes } from 'react-router-dom';
import AccountSettingsRoute from './routes/account-settings/account-settings-route';
import ActionsRoute from './routes/actions/actions-route';
import AdminRoute from './routes/admin/admin-route';
import AppearanceSettingsRoute from './routes/appearance-settings/appearance-settings-route';
import AccountingRoute from './routes/accounting/accounting-route';
import ContactsRoute from './routes/contacts/contacts-route';
import DashboardRoute from './routes/dashboard/dashboard-route';
import ErrorTestRoute from './routes/error-test/error-test-route';
import FeedbackRoute from './routes/feedback/feedback-route';
import InboxRoute from './routes/inbox/inbox-route';
import LimboRoute from './routes/limbo/limbo-route';
import LoginRoute from './routes/login/login-route';
import NotFoundRoute from './routes/not-found/not-found-route';
import NotificationSettingsRoute from './routes/notification-settings/notification-settings-route';
import PlaygroundRoute from './routes/playground/playground-route';
import ProfileSettingsRoute from './routes/profile-settings/profile-settings-route';
import SupportRoute from './routes/support/support-route';
import TasksRoute from './routes/tasks/tasks-route';
import TeamsRoute from './routes/teams/teams-route';
import TripsRoute from './routes/trips/trips-route';
import AuthLayout from './utils/auth-layout/auth-layout';
import AppErrorBoundary from './utils/errors/app-error-boundary';
import Layout from './utils/layout/layout';
import { Toaster } from 'sonner';

export default function App() {
    const application = FrontendApplication.EUROHELP;
    const namespace = Namespace.EUOPS;

    return (
        <ApplicationIdentityProvider
            application={application}
            namespace={namespace}
        >
            <PublicConfigProvider>
                <EnvironmentProvider basePath="/">
                    <AuthProvider>
                        <Toaster />
                        <UserProfileProvider>
                            <ConfigProvider>
                                <ErrorBoundary fallbackRender={AppErrorBoundary}>
                                    <Routes>
                                        <Route element={<AuthLayout />}>
                                            <Route
                                                path="login"
                                                element={<LoginRoute />}
                                            />
                                        </Route>

                                        <Route element={<RequireAuth />}>
                                            <Route element={<Layout />}>
                                                <Route
                                                    index
                                                    element={<InboxRoute />}
                                                />
                                                <Route
                                                    path="actions"
                                                    element={<ActionsRoute />}
                                                />
                                                <Route
                                                    path="feedback"
                                                    element={<FeedbackRoute />}
                                                />
                                                <Route
                                                    path="accounting"
                                                    element={<AccountingRoute />}
                                                />
                                                <Route
                                                    path="trips"
                                                    element={<TripsRoute />}
                                                />
                                                <Route
                                                    path="contacts"
                                                    element={<ContactsRoute />}
                                                />
                                                <Route
                                                    path="support"
                                                    element={<SupportRoute />}
                                                />
                                                <Route
                                                    path="inbox"
                                                    element={<InboxRoute />}
                                                />
                                                <Route
                                                    path="login"
                                                    element={<LoginRoute />}
                                                />
                                                <Route
                                                    path="not-found"
                                                    element={<NotFoundRoute />}
                                                />
                                                <Route
                                                    path="tasks"
                                                    element={<TasksRoute />}
                                                />
                                                <Route path="settings">
                                                    <Route
                                                        index
                                                        path="profile"
                                                        element={<ProfileSettingsRoute />}
                                                    />
                                                    <Route
                                                        path="account"
                                                        element={<AccountSettingsRoute />}
                                                    />
                                                    <Route
                                                        path="appearance"
                                                        element={<AppearanceSettingsRoute />}
                                                    />
                                                    <Route
                                                        path="notifications"
                                                        element={<NotificationSettingsRoute />}
                                                    />
                                                </Route>
                                                <Route
                                                    path="teams"
                                                    element={<TeamsRoute />}
                                                />
                                                <Route path="developer">
                                                    <Route
                                                        path="admin"
                                                        element={<AdminRoute />}
                                                    />
                                                    <Route
                                                        path="error-test/:scenario"
                                                        element={<ErrorTestRoute />}
                                                    />
                                                    <Route
                                                        path="playground"
                                                        element={<PlaygroundRoute />}
                                                    />
                                                    <Route
                                                        path="components"
                                                        element={<LimboRoute />}
                                                    />
                                                </Route>
                                                <Route
                                                    path="*"
                                                    element={<NotFoundRoute />}
                                                />
                                            </Route>
                                        </Route>
                                    </Routes>
                                </ErrorBoundary>
                            </ConfigProvider>
                        </UserProfileProvider>
                    </AuthProvider>
                </EnvironmentProvider>
            </PublicConfigProvider>
        </ApplicationIdentityProvider>
    );
}
