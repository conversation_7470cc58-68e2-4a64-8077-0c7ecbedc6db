const { composePlugins, withNx } = require('@nx/webpack');
const { withReact } = require('@nx/react');

// Nx plugins for webpack.
module.exports = composePlugins(withNx(), withReact(), (config) => {
    // Update the webpack config as needed here.
    // e.g. `config.plugins.push(new MyPlugin())`
    config.output.publicPath = '/';
    if (config.devServer) {
        config.devServer.static.publicPath = '/';
    }

    config.stats = { ...config.stats, warnings: false };
    config.module.rules.push(
        {
            test: /\.(woff|woff2)$/,
            use: {
                loader: 'url-loader'
            }
        },
        {
            test: /\.(png|jpg)$/,
            type: 'asset/resource'
        }
    );

    return config;
});
