'use strict';

var express = require('express'),
    bodyParser = require('body-parser'),
    morgan = require('morgan'),
    bridge = require('@aa/bridge'),
    aaLogUtilities = require('@aa/utilities/aa-utilities').logging,
    aaServiceCheck = require('@aa/utilities/aa-utilities').serviceCheck,
    logger = require('winston'),
    nconf = require('nconf'),
    fs = require('fs'),
    ctrl = require('../lib/controller'),
    config = require('./config'),
    route,
    port,
    server;
const aaSecurityChecker = require('@aa/security-checker').tokenHandler;

// Setup server
port = process.env.aahPodHttpPort || process.env.primeProxyPort || 7530;

nconf.argv().env();
nconf.defaults(config);

server = express();

// create a write stream (in append mode)
var accessLogStream = fs.createWriteStream(__dirname + '/access.log', {
    flags: 'a'
});

server.use(
    morgan(aaLogUtilities.morganSettings(), {
        stream: accessLogStream
    })
);

logger.handleExceptions(new logger.transports.File(aaLogUtilities.logFileSettings('exceptions.log')));

logger.add(logger.transports.File, aaLogUtilities.logFileSettings('prime-proxy.log'));

server.use(bodyParser.json());

bridge
    .initPrime()
    .then(function () {
        // basic health check middleware ..
        server.use(nconf.get('apiEndPoint'), aaServiceCheck.health);
        server.use(nconf.get('apiEndPoint'), aaSecurityChecker.verifyCredentials);
        server.use(nconf.get('apiEndPoint'), ctrl.init(express.Router(), bridge));
        server.listen(parseInt(nconf.get('port')), nconf.get('ip'), function listenSuccess() {
            console.log(nconf.get('port'), nconf.get('ip'));
        });
    })
    .catch(function (err) {
        console.log(err);
        process.exit(1);
    });
