const logger = require('../services/logging.serivce');
const aaOracleUtility = require('@aa/oracle-utilities');
const appName = 'temp-repair';

module.exports = {
    init: () => {
        return aaOracleUtility.init({
            connectStrings: process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : ['sun54:1521/VACSH'],
            user: process.env.cshUser || 'Vanda',
            password: process.env.cshPassword || 'Va.77.av',
            appName
        });
    },
    connect: () => {
        logger.info('connecting');
        return aaOracleUtility.connect();
    },
    disconnect: (db) => {
        aaOracleUtility.release(db);
    }
};
