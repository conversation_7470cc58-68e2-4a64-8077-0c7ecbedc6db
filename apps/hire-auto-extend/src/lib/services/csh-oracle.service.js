'use strict';

const Q = require('q');

const aaOracle = require('@aa/oracle-utilities'),
    oracledb = require('oracledb');

/*process.env.cshUser='dando'
process.env.cshPassword='d3d4d0'
process.env.cshConnectStrings = 'sun55:1521/LACSH,sun52:1521/LBCSH';
*/
const user = process.env.cshUser || 'vanda',
    password = process.env.cshPassword || 'va.77.av',
    connectStrings = process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : ['sun54:1521/VACSH'],
    appName = 'hire-auto-extend';

oracledb.fetchAsString = [oracledb.CLOB];

module.exports = {
    init: () => {
        return aaOracle.init({
            user,
            password,
            connectStrings,
            appName
        });
    },
    connect: () => {
        return aaOracle.connect();
    },
    release: (dbConn) => {
        aaOracle.release(dbConn);
    }
};
