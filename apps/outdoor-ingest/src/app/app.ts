import { AuditClient } from '@aa/audit-client';
import { ErrorResponse } from '@aa/connector';
import {
    BatteryTestStatus,
    Case,
    CUVEvents,
    CUVOutdoorPayload,
    GarageBookingStatusType,
    isValidOutdoorB2Q,
    isValidOutdoorCUV,
    isValidOutdoorGarageBooking,
    isValidOutdoorGarageBookingStatusRequest,
    isValidOutdoorSMRComplete,
    isValidOutdoorTaskComplete,
    Namespace,
    OutdoorEvents,
    OutdoorGarageBooking,
    OutdoorGarageBookingStatusRequest,
    OutdoorSMRComplete,
    OutdoorTaskComplete,
    OutdoorTaskPayload
} from '@aa/data-models/common';
import { BreakdownTask } from '@aa/data-models/entities/breakdown-task';
import { Aah2UidFactory } from '@aa/endpoints';
import { CaseStore, GarageBookingStore } from '@aa/data-store';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubSender, EventHubSenderConfig } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';

const appName = 'outdoor-ingest';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.OUTDOOR_INGEST;
    protected auxStreamRGarageBookingSender: EventHubSender<OutdoorEvents.CREATE_GARAGE_BOOKING, OutdoorGarageBooking>;
    protected auxStreamRCompleteTaskSender: EventHubSender<OutdoorEvents.COMPLETE_BREAKDOWN_TASK, OutdoorTaskComplete>;
    protected auxStreamRCompleteSmrSender: EventHubSender<OutdoorEvents.COMPLETE_SMR, OutdoorSMRComplete>;
    protected auxStreamStartB2QSender: EventHubSender<OutdoorEvents.START_B2Q, OutdoorTaskPayload>;
    protected auxStreamCompleteB2QSender: EventHubSender<OutdoorEvents.COMPLETE_B2Q, OutdoorTaskPayload>;
    protected auxStreamReportCUVSender: EventHubSender<CUVEvents.CREATE_CUV_REPORTING, CUVOutdoorPayload>;
    protected auditClient: AuditClient;
    protected caseStore: CaseStore;
    private caseDabaseName = 'job';
    protected garageBookingStore: GarageBookingStore;
    private garageBookingDatabaseName = 'task';

    constructor(environment: BackendEnvironment) {
        super({ environment, appName });

        const configBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };

        this.auditClient = new AuditClient({
            application: BackendApplication.OUTDOOR_INGEST,
            connector: this.connector,
            operatorId: -1
        });
        this.caseStore = new CaseStore({
            databaseName: this.caseDabaseName,
            logger: this.logger,
            dataStore: this.dataStore
        });
        this.garageBookingStore = new GarageBookingStore({
            databaseName: this.garageBookingDatabaseName,
            logger: this.logger,
            dataStore: this.dataStore
        });

        this.auxStreamRGarageBookingSender = new EventHubSender({
            ...configBase,
            eventHubName: 'aux-stream'
        });

        this.auxStreamRCompleteTaskSender = new EventHubSender({
            ...configBase,
            eventHubName: 'aux-stream'
        });
        this.auxStreamRCompleteSmrSender = new EventHubSender({
            ...configBase,
            eventHubName: 'aux-stream'
        });

        this.auxStreamStartB2QSender = new EventHubSender({
            ...configBase,
            eventHubName: 'aux-stream'
        });

        this.auxStreamCompleteB2QSender = new EventHubSender({
            ...configBase,
            eventHubName: 'aux-stream'
        });

        this.auxStreamReportCUVSender = new EventHubSender({
            ...configBase,
            eventHubName: 'aux-stream'
        });

        this.server.post('/task/complete', this.taskComplete, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.post('/smr/complete', this.smrComplete, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.post('/booking', this.booking, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.post('/booking/v2', this.bookingV2, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.get('/booking/v2/status', this.bookingV2Status, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.post('/b2q/start', this.startB2Q, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.post('/b2q/complete', this.completeB2Q, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.post('/cuv/report', this.reportCUV, {
            allowedRoles: ['outdoor'],
            protect: false
        });
    }

    /**
     * Start B2Q event handler
     * @param { Request } req Request
     * @param { Response } res Response
     * @protected
     */
    protected startB2Q = async (req: Request, res: Response) => {
        let { body } = req;

        if (!isValidOutdoorB2Q(body)) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST);
        }

        try {
            body = {
                ...body,
                status: BatteryTestStatus.BT_START,
                startDate: new Date()
            };
            this.logger.info({
                sourceName: this.name,
                message: `On start B2Q event received for taskId: ${body.taskId}`,
                data: { body }
            });
            await this.auxStreamStartB2QSender.send(OutdoorEvents.START_B2Q, body, false);
            return getResponse(res, ServerResponseCode.ACCEPTED);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    /**
     * Complete B2Q event handler
     * @param { Request } req Request
     * @param { Response } res Response
     * @protected
     */
    protected completeB2Q = async (req: Request, res: Response) => {
        let { body } = req;

        if (!isValidOutdoorB2Q(body)) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST);
        }

        try {
            body = {
                ...body,
                status: BatteryTestStatus.BT_COMPLETE_EVA,
                completeDate: new Date()
            };
            this.logger.info({
                sourceName: this.name,
                message: `On complete B2Q event received for taskId: ${body.taskId}`,
                data: { body }
            });
            await this.auxStreamCompleteB2QSender.send(OutdoorEvents.COMPLETE_B2Q, body, false);

            return getResponse(res, ServerResponseCode.ACCEPTED);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    protected taskComplete = async (req: Request<unknown, unknown, Partial<OutdoorTaskComplete>>, res: Response) => {
        const { body: incomingBody } = req;
        const body: Partial<OutdoorTaskComplete> = {
            taskId: incomingBody.taskId,
            // TODO: outdoor sends us patrolId as operatorId, we need to ask them to rename it at some point
            staffNumber: (incomingBody as unknown as { operatorId: number }).operatorId
        };

        if (!isValidOutdoorTaskComplete(body)) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST);
        }

        try {
            this.logger.info({
                sourceName: this.name,
                message: 'On complete event received',
                data: { body }
            });
            await this.auxStreamRCompleteTaskSender.send(OutdoorEvents.COMPLETE_BREAKDOWN_TASK, body, false);

            return getResponse(res, ServerResponseCode.ACCEPTED);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };
    protected smrComplete = async (req: Request<unknown, unknown, Partial<OutdoorSMRComplete>>, res: Response) => {
        const { body } = req as { body: OutdoorSMRComplete };

        if (!isValidOutdoorSMRComplete(body)) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST);
        }

        try {
            this.logger.info({
                sourceName: this.name,
                message: 'On complete event received',
                data: { body }
            });
            await this.auxStreamRCompleteSmrSender.send(OutdoorEvents.COMPLETE_SMR, body, false);

            return getResponse(res, ServerResponseCode.ACCEPTED);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };
    protected booking = async (req: Request<unknown, unknown, Partial<OutdoorGarageBooking>>, res: Response) => {
        /*
        {
            "taskId": 51408568,
            "operatorId": 807518,
            "staffNumber": 807519, // staffNumber is not considered in payload as operatorId is considered
            "customer": {
                "email": "<EMAIL>",
                "mobileNumber": "+44 7442575005", // New field 1
                "postCode": "RG21 4EA",           // New field 2
                "address": null
            },
            "job": {
                "notes": null,
                "garageId": 1254
            }
        }
        */
        const { body: incomingBody } = req;
        const body: Partial<OutdoorGarageBooking> = {
            taskId: incomingBody.taskId,
            job: incomingBody.job,
            customer: incomingBody.customer,
            // TODO: outdoor sends us staffNumber as operatorId, we need to ask them to rename it at some point
            staffNumber: (incomingBody as unknown as { operatorId: number }).operatorId
        };

        if (!isValidOutdoorGarageBooking(body)) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST);
        }

        try {
            this.logger.info({
                sourceName: this.name,
                message: 'Booking event received',
                data: { body }
            });
            await this.auxStreamRGarageBookingSender.send(OutdoorEvents.CREATE_GARAGE_BOOKING, body, false);

            return getResponse(res, ServerResponseCode.ACCEPTED);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };
    protected bookingV2 = async (req: Request<unknown, unknown, Partial<OutdoorGarageBooking>>, res: Response) => {
        /*
        {
            "taskId": 51408568,
            "operatorId": 807518,
            "staffNumber": 807519, // staffNumber is not considered in payload as operatorId is considered
            "customer": {
                "email": "<EMAIL>",
                "mobileNumber": "+44 7442575005", // New field 1
                "postCode": "RG21 4EA",           // New field 2
                "address": null
            },
            "job": {
                "notes": null,
                "garageId": 1254
            }
        }
        */
        const { body: incomingBody } = req;
        const body: Partial<OutdoorGarageBooking> = {
            taskId: incomingBody.taskId,
            job: incomingBody.job,
            customer: incomingBody.customer,
            // TODO: outdoor sends us staffNumber as operatorId, we need to ask them to rename it at some point
            staffNumber: (incomingBody as unknown as { operatorId: number }).operatorId
            // staffNumber: incomingBody.staffNumber, // staffNumber is not considered in payload as operatorId is considered
        };
        const aah2BookingRequestId = Aah2UidFactory.get();

        if (!isValidOutdoorGarageBooking(body)) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST);
        }

        try {
            this.logger.info({ sourceName: this.name, message: 'V2 -> Booking event received', data: { body } });

            const bookingStatus = await this.garageBookingStore.getStatusByTaskId(body.taskId);
            if (bookingStatus && (bookingStatus.status === GarageBookingStatusType.PENDING || bookingStatus.status === GarageBookingStatusType.SUCCESS)) {
                this.logger.info({ sourceName: this.name, message: 'V2 -> Booking request already exists', data: { body } });
                const response = { bookingAlreadyExists: true, aah2BookingRequestId: bookingStatus.aah2BookingRequestId, unityBookingRefNo: bookingStatus.unityBookingRefNo };
                return getResponse(res, ServerResponseCode.ACCEPTED, response);
            }

            await this.garageBookingStore.upsertBookingStatus(aah2BookingRequestId, body.taskId, GarageBookingStatusType.PENDING);

            const bodyWithAAh2RequestId = { ...body, aah2BookingRequestId };
            await this.auxStreamRGarageBookingSender.send(OutdoorEvents.CREATE_GARAGE_BOOKING, bodyWithAAh2RequestId, false);

            const response = { bookingAlreadyExists: false, aah2BookingRequestId };
            return getResponse(res, ServerResponseCode.ACCEPTED, response);
        } catch (error) {
            const exception = new Exception({ error, message: 'V2 -> Booking request failure' });
            this.logger.error(exception);

            await this.garageBookingStore.upsertBookingStatus(aah2BookingRequestId, body.taskId, GarageBookingStatusType.FAILED, undefined, `V2 -> Booking request failure: ${exception.message}`);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };
    protected bookingV2Status = async (req: Request<unknown, unknown, unknown>, res: Response) => {
        const reqParams: Partial<OutdoorGarageBookingStatusRequest> = {
            taskId: parseInt(req.query.taskId as string),
            operatorId: parseInt(req.query.operatorId as string),
            reqId: req.query.reqId as string
        };

        if (!isValidOutdoorGarageBookingStatusRequest(reqParams)) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST);
        }

        try {
            const { taskId, operatorId, reqId } = reqParams;
            this.logger.info({
                sourceName: this.name,
                message: 'V2 -> Booking status request received',
                data: { taskId, operatorId, reqId }
            });

            let bookingStatus = await this.garageBookingStore.getStatusByAah2BookingRequestId(reqId);
            if (!bookingStatus) {
                bookingStatus = await this.garageBookingStore.getStatusByTaskId(taskId);
            }
            if (bookingStatus) {
                const response = { status: bookingStatus.status, unityBookingRefNo: bookingStatus.unityBookingRefNo };
                return getResponse(res, ServerResponseCode.OK, response);
            }

            return getResponse(res, ServerResponseCode.NOT_FOUND);
        } catch (error) {
            const exception = new Exception({ error, message: 'V2 -> Booking status request failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };
    protected reportCUV = async (req: Request, res: Response) => {
        const { body } = req;
        if (!isValidOutdoorCUV(body)) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST);
        }

        try {
            const auditId = body.membership + ':' + body.vrn;

            const trace = this.auditClient.getTrace(Namespace.CUV, auditId);
            await this.auditClient.reportAction(trace, {
                message: 'Received CUV report',
                data: body
            });

            this.logger.info({
                sourceName: this.name,
                message: 'CUV event received',
                data: { body }
            });

            const taskId = body.taskId;
            const task = await this.connector.task.find.byId(taskId);
            const customerRequestId = task.customerRequestId;
            const reportedBy = (task as BreakdownTask).schedule?.resource.dbId;
            const custGroup = (task as BreakdownTask).entitlement?.customerGroup.code;

            this.logger.info({
                sourceName: this.name,
                message: 'CUV event received - operatorId lookup : ',
                data: { reportedBy, custGroup }
            });
            const payload = { ...body, reportedBy, custGroup };
            //membership number will be masked hence membership number will be fetched from case store to update in CUV
            // report
            const caseData: Case | null = await this.caseStore.getCaseDetails(customerRequestId as number);
            if (caseData) {
                payload.membership = caseData.entitlement.policy.membershipNumber;
                this.logger.info({
                    sourceName: this.name,
                    message: 'retrieved customer details from case store',
                    data: { payload }
                });
            }
            await this.auxStreamReportCUVSender.send(CUVEvents.CREATE_CUV_REPORTING, payload, false);

            return getResponse(res, ServerResponseCode.ACCEPTED);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };
}
