const aaOracleUtility = require('@aa/oracle-utilities');
const oracledb = require('oracledb');
const q = require('q');
const logger = require('winston');
const appName = 'bolt-extension-cache';

// TODO: move this whole logic to the service
oracledb.fetchAsString = [oracledb.CLOB];

let cachedConnection = null;

module.exports = {
    init: () => {
        logger.info('oracle.database.init:: attemptig to connect to oracle');
        return aaOracleUtility
            .init({
                connectStrings: process.env.cshConnectStrings.split(','),
                user: process.env.cshUser,
                password: process.env.cshPassword,
                appName
            })
            .catch((err) => {
                logger.error('oracle.database.init:: failed to connect to oracle ', JSON.stringify(err));
                return q.reject();
            });
    },
    connect: () => {
        let def = q.defer();
        if (!cachedConnection) {
            aaOracleUtility.connect().then((connection) => {
                cachedConnection = connection;
                def.resolve(cachedConnection);
            });
        } else {
            def.resolve(cachedConnection);
        }
        return def.promise;
    },
    cursorRead: (dbConn, sql, bindvars) => {
        const defer = q.defer();
        dbConn.execute(
            sql,
            bindvars,
            {
                outFormat: oracledb.OBJECT
            },
            function (error, result) {
                const results = [];
                if (error) {
                    defer.reject(error.message);
                    return;
                }
                const resultStream = result.outBinds.cursor.toQueryStream();
                resultStream.on('error', function (error) {
                    defer.reject(error);
                });
                resultStream.on('data', function (data) {
                    results.push(data);
                });
                resultStream.on('end', function () {
                    defer.resolve(results);
                });
            }
        );
        return defer.promise;
    },
    releaseConnection: () => {
        return aaOracleUtility.release(cachedConnection);
    },
    release: async (db) => {
        return await aaOracleUtility.release(db);
    }
};
