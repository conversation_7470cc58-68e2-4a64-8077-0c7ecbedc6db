{"name": "aahelp2-web-app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/aahelp2-web-app/src", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"outputPath": "dist/apps/aahelp2-web-app", "compiler": "babel", "main": "apps/aahelp2-web-app/src/main.ts", "tsConfig": "apps/aahelp2-web-app/tsconfig.app.json", "webpackConfig": "apps/aahelp2-web-app/webpack.config.js", "assets": ["apps/aahelp2-web-app/src/isOK.html", "apps/aahelp2-web-app/src/failed-auth.html", "apps/aahelp2-web-app/src/package.json", "apps/aahelp2-web-app/src/package-lock.json", "apps/aahelp2-web-app/src/favicon.ico", "apps/aahelp2-web-app/src/assets", {"glob": "**/*.html", "input": "apps/aahelp2-web-app/src/app", "output": "partials"}, {"glob": "**/*.svg", "input": "node_modules/flag-icons/flags", "output": "images/flags"}, {"glob": "**/*.html", "input": "apps/aahelp2-web-app/src/assets/prompt-files", "output": "/"}, {"glob": "**/*.html", "input": "apps/aahelp2-web-app/src/static", "output": "/"}], "index": "apps/aahelp2-web-app/src/index.html", "baseHref": "/", "styles": ["apps/aahelp2-web-app/src/styles.scss"], "scripts": []}, "configurations": {"development": {}, "production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "fileReplacements": [{"replace": "apps/aahelp2-web-app/src/environments/environment.ts", "with": "apps/aahelp2-web-app/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "aahelp2-web-app:build"}, "configurations": {"development": {"buildTarget": "aahelp2-web-app:build:development"}, "production": {"buildTarget": "aahelp2-web-app:build:production"}}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/aahelp2-web-app/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/aahelp2-web-app/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}}