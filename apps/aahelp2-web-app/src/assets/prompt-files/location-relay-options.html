<!--
#
# Destination Location
#
-->
<div class="col-lg-12 col-sm-12 col-xs-12">
    <p>Please select one of the following options which describes the type of recovery location.</p>
    <div class="radio-wrap inline-radios">
        <input
            type="radio"
            id="misc-repairer"
            ng-model="prompt.data().task.recovery().destResourceId"
            ng-value="-1"
            ng-model-options="ctrl.modelOptions"
        />
        <label
            for="misc-repairer"
            class="radio-label"
            ><span class="dot-outer"><span class="dot"></span></span>Repairer</label
        >
    </div>
    <div class="radio-wrap inline-radios">
        <input
            type="radio"
            id="drivers-home"
            ng-model="prompt.data().task.recovery().destResourceId"
            ng-value="-2"
            ng-model-options="ctrl.modelOptions"
        />
        <label
            for="drivers-home"
            class="radio-label"
            ><span class="dot-outer"><span class="dot"></span></span>Driver's Home</label
        >
    </div>
    <div class="radio-wrap inline-radios">
        <input
            type="radio"
            id="harbour"
            ng-model="prompt.data().task.recovery().destResourceId"
            ng-value="-4"
            ng-model-options="ctrl.modelOptions"
        />
        <label
            for="harbour"
            class="radio-label"
            ><span class="dot-outer"><span class="dot"></span></span>Harbour</label
        >
    </div>
    <div class="radio-wrap inline-radios">
        <input
            type="radio"
            id="vehicles-base"
            ng-model="prompt.data().task.recovery().destResourceId"
            ng-value="-7"
            ng-model-options="ctrl.modelOptions"
        />
        <label
            for="vehicles-base"
            class="radio-label"
            ><span class="dot-outer"><span class="dot"></span></span>Vehicle's Base</label
        >
    </div>
    <div class="radio-wrap inline-radios">
        <input
            type="radio"
            id="storage"
            ng-model="prompt.data().task.recovery().destResourceId"
            ng-value="-5"
            ng-model-options="ctrl.modelOptions"
        />
        <label
            for="storage"
            class="radio-label"
            ><span class="dot-outer"><span class="dot"></span></span>Storage</label
        >
    </div>
    <div class="radio-wrap inline-radios">
        <input
            type="radio"
            id="safe-location"
            ng-model="prompt.data().task.recovery().destResourceId"
            ng-value="-8"
            ng-model-options="ctrl.modelOptions"
        />
        <label
            for="safe-location"
            class="radio-label"
            ><span class="dot-outer"><span class="dot"></span></span>Safe Location</label
        >
    </div>
</div>
