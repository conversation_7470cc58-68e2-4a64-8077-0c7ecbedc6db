<!DOCTYPE html>
<html>
    <head>
        <link
            rel="stylesheet"
            type="text/css"
            href="styles/bootstrap.css"
        />
        <link
            rel="icon"
            type="image/png"
            href="assets/favicon.png"
        />
        <title>AA Help 2</title>

        <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 12px;
                font-size: 1.2rem;
                line-height: 1.42857143;
                color: #333;
                background-color: #f1f1f1;
                margin: 0;
                padding: 0;
            }
            .container-fluid {
                margin: 0;
                padding: 0;
            }
            #header {
                background-color: #ffffff;
                border-color: #ffffff;
                color: #1d1d1d;
                margin-bottom: 50px;
                height: 40px;
            }
            #header .main-logo {
                display: block;
                width: 291px;
                height: 76px;
                background: url(../assets/aa-beamv2.png) no-repeat top left;
                background-size: cover;
                position: absolute;
                left: 0;
            }
            .content {
                width: 95%;
                background-color: #fff;
                overflow: hidden;
                margin: 0 auto;
                padding: 30px;
            }
            .shadow {
                -webkit-box-shadow: 0 0 5px 0px rgba(50, 50, 50, 0.2);
                -moz-box-shadow: 0 0 5px 0px rgba(50, 50, 50, 0.2);
                box-shadow: 0 0 5px 0px rgba(50, 50, 50, 0.2);
            }
            h1 {
                margin: 0 0 10px 0;
                padding: 0;
                font-size: 22px;
                font-size: 2.2rem;
                font-weight: bold;
                color: #1d1d1d;
            }
            p {
                font-size: 14px;
                font-size: 1.4rem;
                margin: 0;
            }
            p:last-child {
                margin-bottom: 30px;
            }
            .btn-primary {
                margin-top: 30px;
                font-weight: bold;
                width: 150px;
                border: 1px solid;
                background-color: #ffcc00;
                border-color: #ffc400;
                color: #1d1d1d;
                background: #ffe066;
                background: -moz-linear-gradient(top, #ffe066 30%, #ffc400 100%);
                background: -webkit-gradient(linear, left top, left bottom, color-stop(30%, #ffe066), color-stop(100%, #ffc400));
                background: -webkit-linear-gradient(top, #ffe066 30%, #ffc400 100%);
                background: -o-linear-gradient(top, #ffe066 30%, #ffc400 100%);
                background: -ms-linear-gradient(top, #ffe066 30%, #ffc400 100%);
                background: linear-gradient(to bottom, #ffe066 30%, #ffc400 100%);
                filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#000000',GradientType=0 );
                -moz-box-shadow: inset 0px 2px 2px #ffffff;
                -webkit-box-shadow: inset 0px 2px 2px #ffffff;
                outline: 0;
            }
            .btn-primary:hover,
            .btn-primary:focus {
                background-color: #ffe69a;
                border-color: #ffc400;
                color: #1d1d1d;
                background: #ffe69a;
                background: -moz-linear-gradient(top, #ffe69a 30%, #ffcc00 100%);
                background: -webkit-gradient(linear, left top, left bottom, color-stop(30%, #ffe69a), color-stop(100%, #ffcc00));
                background: -webkit-linear-gradient(top, #ffe69a 30%, #ffcc00 100%);
                background: -o-linear-gradient(top, #ffe69a 30%, #ffcc00 100%);
                background: -ms-linear-gradient(top, #ffe69a 30%, #ffcc00 100%);
                background: linear-gradient(to bottom, #ffe69a 30%, #ffcc00 100%);
                outline: 0;
            }
            .btn-primary:active,
            .btn-primary.active {
                color: #000;
                background-color: #e6b901;
                border: 1px solid #ffcc00;
                outline: 0;
            }
            .btn-primary.active:hover {
                color: #1d1d1d;
                background-color: #ffcc00;
                border-color: #ffcc00;
                outline: 0;
            }
            #footer {
                left: 0;
                width: 100%;
                height: 40px;
                bottom: 20px;
                position: fixed;
                background-color: #1d1d1d;
                color: #ffffff;
            }
        </style>
    </head>

    <body>
        <div class="container-fluid">
            <div
                id="header"
                class=""
            >
                <div class="col-lg-9 col-md-6 col-xs-1">
                    <div class="icon main-logo"></div>
                </div>
            </div>
            <div class="content shadow">
                <h1>You are not an authorised user!</h1>
                <p>Please request a user amendment form from your line manager.</p>
            </div>
            <div class="footer"></div>
        </div>
    </body>
</html>
