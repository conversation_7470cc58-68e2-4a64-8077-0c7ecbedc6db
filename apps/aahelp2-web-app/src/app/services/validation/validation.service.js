var _ = require('lodash');
require('angular');
var FlexFieldsHelper = require('@aa/malstrom-models/lib/factories/flexible-fields-helper.factory'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model'),
    Qualification = require('@aa/malstrom-models/lib/qualification.model'),
    HireScheduleRequest = require('@aa/mobility-models-common/lib/hire-schedule-request.model');

module.exports = angular
    .module('aah-validation-service-module', [
        require('../../services/prompt/prompt.service').name,
        require('../../services/alert/alert.service').name,
        require('../../services/mapping/google/google.service').name,
        require('../../constants/alert/alert-type.constants').name,
        require('../../constants/faults/faults.constants').name,
        require('../../services/qualification/qualification.service').name,
        require('../../services/ecall/ecall.service').name,
        require('../../services/contract-validation/contract-validation.service').name
    ])
    .service('aahValidationService', [
        '$injector',
        'aahPromptService',
        'aahGoogleService',
        'aahAlertService',
        'uibAlertTypes',
        'aahQualificationService',
        'aahEcallService',
        'aahContractValidationService',
        'aahFaultsConstants',
        function aahValidationService($injector, PromptService, GoogleService, AlertService, AlertTypes, QualificationService, EcallService, ContractValidationService, FaultsConstants) {
            var svc = this,
                TabService, // this gets initialised later to stop circular dependencies
                TaskService,
                VehicleService,
                EligibilityService,
                _adjustRecoveryDestination = false;

            function _isCapabilitiesValid(faultCapabilities, relayCapabilities) {
                return faultCapabilities.length || relayCapabilities.length;
            }

            function _isTaskValid(task) {
                return svc.areMandatoryFlexFieldsComplete(task.entitlement().variableData()) && svc._taskComponentsValid(task);
            }

            function _isJunctionValid(junction) {
                return !!junction && /^(JUNCTION|JUNC|JCT|J)(\s?)(t|T?)([0-9]+)(\s?)(A|B?)(\s?)*$/gi.test(junction);
            }

            _.extend(svc, {
                _taskComponentsValid: (task) => {
                    switch (task.createReason().id()) {
                        case CreateReason.HIRE_CAR:
                            return true;
                        case CreateReason.HOTEL:
                            if (task.createReason().isPublicTransport()) {
                                return svc.isValidTransportation(task) && svc.isValidTransportPickupLocation(task) && svc.isValidTransportDropoffLocation(task);
                            } else {
                                return svc.isValidAccommodation(task) && svc.isValidAccommodationLocation(task);
                            }
                            break;
                        case CreateReason.TRANSPORT:
                            return svc.isValidTransportation(task) && svc.isValidTransportPickupLocation(task) && svc.isValidTransportDropoffLocation(task);
                        //eurohelp
                        case CreateReason.SCRAPPED:
                        case CreateReason.RECOVERY:
                        case CreateReason.GARAGE_REPAIR:
                        case CreateReason.STORAGE:
                            return true;
                        //\eurohelp
                        default:
                            return (
                                _isCapabilitiesValid(task.fault().capabilities(), task.recovery().fault().capabilities()) &&
                                svc.isCustomerValid(task.contact()) &&
                                svc.areMandatoryFlexFieldsValid(task.entitlement().variableData()) &&
                                svc.isVehicleValid(task.vehicle()) &&
                                svc.isFaultValid(task.fault()) &&
                                svc.isLocationValid(task.location(), task.indicators(), task.sequence(), task.status()) &&
                                svc.isLocationInUK(task.location()) &&
                                (task.createReason().id() === CreateReason.PASSENGER_RUN
                                    ? svc.isPassengerRunValid(task.recovery())
                                    : svc.isRecoveryValid(task.recovery(), task.indicators().isRelayDiagnosticsRunning())) &&
                                svc.isRelayPlusValid(task)
                            );
                    }
                },
                isJunctionValid: _isJunctionValid,
                isCACValid: function (cac) {
                    return /^\d{1}-\d{11}$/.test(cac);
                },
                isTelephoneValid: function isTelephoneValid(telephone) {
                    if (QualificationService.inQualification() && QualificationService.activeTaskType() === Qualification.IM) {
                        return true;
                    } else {
                        return /^(\(?\+?[0-9]{1,4}\)?)?[0-9_\- \(\)]{4,11}$/.test(telephone);
                    }
                },
                telephoneWarnings: function telephoneWarnings(telephone) {
                    if (/^(0800|037|034|033|084|0300)$/.test(telephone) || /^(0800|037|034|033|084|0300)\d+$/.test(telephone)) {
                        return 'thirdPartyTelephoneErr';
                    } else if (/^(?:07)\d+$/.test(telephone) && !/^\d( ?\d){10}$/.test(telephone)) {
                        return 'telephoneLengthErr';
                    } else if (!/^07/.test(telephone)) {
                        return 'otherTelephoneErr';
                    }
                },
                isTelephoneOnlyZeros: function isTelephoneOnlyZeros(telephone) {
                    return !!telephone && telephone.length > 3 && /^0+$/.test(telephone);
                },
                isCustomerValid: function isCustomerValid(contact) {
                    var name = contact.name() ? contact.name().trim() : '',
                        telephone = contact.telephone() ? contact.telephone().trim() : '';

                    return !_.isEmpty(name) && svc.isTelephoneValid(telephone);
                },
                areMandatoryFlexFieldsValid: function areMandatoryFlexFieldsValid(flexibleFields) {
                    return FlexFieldsHelper.areMandatoryFlexFieldsComplete(flexibleFields);
                },
                isVehicleValid: function isVehicleValid(vehicle) {
                    if (!TaskService) {
                        TaskService = $injector.get('aahTaskService');
                    }
                    if (!VehicleService) {
                        VehicleService = $injector.get('aahVehicleService');
                    }

                    const vrn = vehicle.registration();
                    const task = TaskService.task();
                    const vrnValid = task.contractValidation().vrn().noVRN() || (vrn && VehicleService.validateVRN(vrn));

                    return (
                        !!vrnValid && vehicle.typeId() !== null && vehicle.typeId() > -1 && vehicle.makeId() !== null && vehicle.makeId() > -1 && vehicle.modelId() !== null && vehicle.modelId() > -1
                    );
                },
                isCUVComponentValid: function isCUVComponentValid(task) {
                    if (!EligibilityService) {
                        EligibilityService = $injector.get('aahEligibilityService');
                    }
                    return (
                        task.entitlement().customerGroup().isPersonal() && !task.entitlement().customerGroup().isBank() && !(EligibilityService.isWillJoin() || task.entitlement().riskCode() === 'WJ')
                    );
                },
                isFaultValid: function isFaultValid(fault) {
                    return fault.id() !== null && fault.id() >= 0 && !svc.isRestartDiagnostics(fault);
                },
                isRestartDiagnostics: function isRestartDiagnostics(fault) {
                    let restartDiagnostics = null;
                    restartDiagnostics = fault.diagnosticsQAList().some(function (fault) {
                        return fault.name().includes(FaultsConstants.RESTART_DIAGNOSTICS) && fault.selectedAnswer().nextQuestionId() === 0;
                    });
                    return restartDiagnostics;
                },
                isLocationValid: function isLocationValid(location, indicators, sequence, status) {
                    var motorwayInfoAvailable =
                        indicators && indicators.motorway() && (sequence === -1 || ['INIT', 'HOLD'].includes(status))
                            ? location.isMotorwayInfoValid() && _isJunctionValid(location.lastJunctionPassed())
                            : true;

                    return motorwayInfoAvailable && location.coordinates().latitude() !== null && location.coordinates().longitude() !== null && !!location.text() && !!location.area();
                },
                isLocationInUK: function isLocationInUK(location) {
                    let position = GoogleService.latLng(location.coordinates().latitude(), location.coordinates().longitude());
                    return GoogleService.locationInBounds(position);
                },
                isRecoveryValid: function isRecoveryValid(recovery, isRelayDiagnosticsRunning) {
                    if (!TabService) {
                        TabService = $injector.get('aahTabService');
                    }

                    if (recovery.destination().isSet() || _adjustRecoveryDestination) {
                        TabService.relayTouched(true);
                    }

                    if (!recovery.relay() && _adjustRecoveryDestination) {
                        _adjustRecoveryDestination = false;
                        recovery.relay(true);
                    }

                    const isRelay = recovery.relay();
                    const isRecoveryDestinationSet = recovery.destination().isSet();
                    const isRecoveryDestinationTextSet = recovery.destination().text();
                    const isRecoveryDestinationAreaSet = recovery.destination().area();
                    const isAdultsSet = !_.isNaN(parseInt(recovery.adults()));
                    const isDestResourceIdSet = true; //recovery.isDestResourceIdSet(); - hardcoded as part of RBAUAA-11763. remove completely if truly needed

                    return !isRelay || (isRecoveryDestinationSet && isRecoveryDestinationTextSet && isRecoveryDestinationAreaSet && isAdultsSet && !isRelayDiagnosticsRunning && isDestResourceIdSet);
                    // return (
                    //     !recovery.relay() ||
                    //     (recovery.destination().isSet() &&
                    //         recovery.destination().text() &&
                    //         recovery.destination().area() &&
                    //         !_.isNaN(parseInt(recovery.adults())) &&
                    //         parseInt(recovery.adults()) >= 0 &&
                    //         !isRelayDiagnosticsRunning &&
                    //         recovery.isDestResourceIdSet())
                    // );
                },
                isPassengerRunValid: function isPassengerRunValid(recovery) {
                    if (!TabService) {
                        TabService = $injector.get('aahTabService');
                    }

                    if (recovery.destination().isSet()) {
                        TabService.relayTouched(true);
                    }
                    return !recovery.relay() || (recovery.destination().isSet() && !_.isNaN(parseInt(recovery.adults())));
                },
                isRelayPlusValid: (task) => {
                    var relayPlus = task.miscFields().relayPlus();
                    return (
                        !task.createReason().isRelayPlus() ||
                        (relayPlus.crdCard() && relayPlus.licenceOK() && relayPlus.hireGroup() && relayPlus.occupants() && relayPlus.status() && relayPlus.gbType() && relayPlus.vehType())
                    );
                },
                /*
                 ** Dangerous location+motorway Tasks created through the mobileApp need to have lastJunctionPassed data
                 ** slightly different from the isLocationValid because we're not validating task status, sequence number or anything else
                 */
                isDangerousLocationJunctionDataValid: (task) => {
                    //we only care to validate mobileApp tasks
                    if (!task.createReason().isSelfServiceApp()) {
                        return true;
                    }

                    //if this is a dangerous location and a motorway we check if we have lastJunctionPassed
                    if (task.indicators().motorway() && task.indicators().dangerousLocation()) {
                        return task.location().lastJunctionPassed();
                    }

                    return true;
                },
                adjustRecoveryDest: function adjustRecoveryDest(adjustRecDest) {
                    _adjustRecoveryDestination = adjustRecDest;
                },
                areMandatoryFlexFieldsComplete: function areMandatoryFlexFieldsComplete(flexibleFields) {
                    if (FlexFieldsHelper.areMandatoryFlexFieldsComplete(flexibleFields)) {
                        return true;
                    }
                    AlertService.createAlert('There are missing customer details', AlertTypes.DANGER);
                    return false;
                },
                areMandatoryOnCompletionFlexFieldsComplete: function areMandatoryOnCompletionFlexFieldsComplete(flexibleFields, cancellingTask) {
                    var errList;
                    // if we are cancelling task then return true regardless of status
                    if (cancellingTask) {
                        return true;
                    }
                    if (FlexFieldsHelper.areMandatoryOnCompletionFlexFieldsComplete(flexibleFields)) {
                        return true;
                    }
                    errList = FlexFieldsHelper.getUnsetMandatoryOnCompletionFields(flexibleFields);

                    AlertService.createAlert('There are missing customer details ' + errList + ' required to complete this task', AlertTypes.DANGER);
                    return false;
                },
                isTaskValid: function isTaskValid(task) {
                    return task.sequence() > -1 && _isTaskValid(task);
                },
                isInsuranceValid: (rental) => {
                    let driver = rental.mainDriver(),
                        thirdPartyHireTypes = [HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_ENTERPRISE, HireScheduleRequest.SEARCH_TYPES.THIRD_PARTY_LCH];
                    return thirdPartyHireTypes.indexOf(rental.hireVehicle().supplierTypeCode().toLowerCase()) > -1 && driver && (!driver.contact().name() || !driver.contact().telephone());
                },
                canSendOrSaveTask: function canSendOrSaveTask(task) {
                    if (PromptService.hasOutstandingPrompts()) {
                        AlertService.createAlert('There are outstanding prompts, please deal with them before sending the task');
                        return Promise.resolve(false);
                    }
                    // Validate the task is not breaking terms and conditions (service abuse)
                    return svc.contractValidationSuccessful(task).then((result) => {
                        if (!result) {
                            return false;
                        }

                        if (!_isTaskValid(task)) {
                            return false;
                        }

                        return task.sequence() === -1 || task.sequence() >= 1;
                    });
                },
                sendButtonEnabled: function sendOrSaveButtonEnabled(task) {
                    TaskService = $injector.get('aahTaskService');
                    if (!EligibilityService) {
                        EligibilityService = $injector.get('aahEligibilityService');
                    }
                    if (
                        TaskService.task().entitlement().customerGroup().isPersonal() &&
                        !TaskService.task().entitlement().customerGroup().isBank() &&
                        !(EligibilityService.isWillJoin() || TaskService.task().entitlement().riskCode() === 'WJ') &&
                        (TaskService.task().demandDeflection().isCUV() === undefined || TaskService.task().demandDeflection().isCUV() === null) &&
                        !task.entitlement().customerGroup().isRoadsideAddOn()
                    ) {
                        return false;
                    } else {
                        return (
                            task.sequence() === -1 &&
                            svc._taskComponentsValid(task) &&
                            !ContractValidationService.contractValidationComponentsInvalid(task) &&
                            !ContractValidationService.sendOrSaveNotAllowed(task) &&
                            !ContractValidationService.skipCompletionState(task)
                        );
                    }
                },
                saveButtonEnabled: function sendOrSaveButtonEnabled(task) {
                    return svc._taskComponentsValid(task) && !ContractValidationService.contractValidationComponentsInvalid(task) && !ContractValidationService.sendOrSaveNotAllowed(task);
                },
                hasBenefit: function hasBenefit(benefits, benefit) {
                    var has = false;

                    _.forEach(benefits, function (item) {
                        has = item.code() === benefit;
                    });

                    return has;
                },
                combineDrivers: (rental) => {
                    const mergeAll = _.unionWith([rental.mainDriver()], rental.additionalDrivers(), 'id');
                    return _.uniqWith(mergeAll, _.isEqual);
                },

                areAllDriversValidated: function areAllDriversValidated(rental) {
                    if (rental && rental.mainDriver().id() > 0 && rental.hireVehicle().id() && !(!!rental.thirdPartyHire() && rental.thirdPartyHire().isSelfCheckout()) && !rental.isSelfInsured()) {
                        let unique = [...new Set(svc.combineDrivers(rental).map((driver) => driver.isEligible()))];
                        return unique.length === 1 && unique[0] === true;
                    }
                    if (!!rental.thirdPartyHire() && rental.thirdPartyHire().isSelfCheckout() && !rental.isSelfInsured()) {
                        return !svc.isInsuranceValid(rental);
                    }
                    return true;
                },
                moblityInsuranceAuthorised: (rental) => {
                    if (rental.thirdPartyHire() && rental.thirdPartyHire().isSelfCheckout()) {
                        return true;
                    }
                    return rental.isAuthorised();
                },
                mobilityOptionsSelected: (mobilityTask, businessRules) => {
                    let rental = mobilityTask.rental();
                    return (
                        (rental.dropOffLocation().id() > 0 || typeof rental.dropOffLocation().id() === 'string') &&
                        (rental.collectLocation().id() > 0 || typeof rental.collectLocation().id() === 'string') &&
                        (businessRules.agileEnabled ? rental.repairLocation().id() > 0 || typeof rental.repairLocation().id() === 'string' : true)
                    );
                },
                mobilityComponentsValid: (mobilityTask, businessRules) => {
                    let rental = mobilityTask.rental();
                    if (!rental.hireVehicle().id()) {
                        return true;
                    }
                    if (!svc.mobilityOptionsSelected(mobilityTask, businessRules)) {
                        AlertService.createAlert('Please select all mobility options');
                        return false;
                    }
                    if (!svc.areAllDriversValidated(rental)) {
                        AlertService.createAlert('Please add/validate drivers before saving the task');
                        return false;
                    }
                    return svc.moblityInsuranceAuthorised(rental);
                },
                contractValidationSuccessful: (task) => {
                    return ContractValidationService.isValid(task);
                },
                skipCompletionState: (task) => {
                    return ContractValidationService.skipCompletionState(task);
                },
                isValidAccommodation: (hotelTask) => {
                    const accommodationDetails = hotelTask.hotel();
                    if (!accommodationDetails.hotelName()) {
                        return false;
                    }
                    if (!svc.isTelephoneValid(accommodationDetails.phone().phoneNumber())) {
                        return false;
                    }
                    if (!accommodationDetails.checkInDate()) {
                        return false;
                    }
                    if (!accommodationDetails.checkOutDate()) {
                        return false;
                    }
                    if (accommodationDetails.checkOutDate() < accommodationDetails.checkInDate()) {
                        return false;
                    }
                    if ((!accommodationDetails.noOfRooms() && accommodationDetails.noOfRooms() !== 0) || accommodationDetails.noOfRooms() < 0) {
                        return false;
                    }
                    if ((!accommodationDetails.noOfAdults() && accommodationDetails.noOfAdults() !== 0) || accommodationDetails.noOfAdults() < 0) {
                        return false;
                    }
                    if ((!accommodationDetails.noOfChildren() && accommodationDetails.noOfChildren() !== 0) || accommodationDetails.noOfChildren() < 0) {
                        return false;
                    }
                    if ((!accommodationDetails.price().value() && accommodationDetails.price().value() !== 0) || accommodationDetails.price().value() < 1) {
                        return false;
                    }
                    if (!accommodationDetails.payAndClaim() && !accommodationDetails.reservationId()) {
                        return false;
                    }
                    return true;
                },
                isValidTransportation: (transportTask) => {
                    const transportationDetails = transportTask.transport && transportTask.transport();
                    if (transportationDetails) {
                        if (!transportationDetails.type()) {
                            return false;
                        }
                        if (!transportationDetails.operatorName()) {
                            return false;
                        }
                        if (!transportationDetails.startDate()) {
                            return false;
                        }
                        if ((!transportationDetails.noOfAdults() && transportationDetails.noOfAdults() !== 0) || transportationDetails.noOfAdults() < 0) {
                            return false;
                        }
                        if ((!transportationDetails.noOfChildren() && transportationDetails.noOfChildren() !== 0) || transportationDetails.noOfChildren() < 0) {
                            return false;
                        }
                        if ((!transportationDetails.price().value() && transportationDetails.price().value() !== 0) || transportationDetails.price().value() < 1) {
                            return false;
                        }
                        if (!transportationDetails.payAndClaim() && !transportationDetails.reservationId()) {
                            return false;
                        }
                    }
                    return true;
                },
                isValidAccommodationLocation: (hotelTask) => {
                    const accommodationLocationDetails = hotelTask.hotel().hotelLocation();
                    if (accommodationLocationDetails.text() && accommodationLocationDetails.area()) {
                        return true;
                    }
                    return false;
                },
                isValidTransportPickupLocation: (transportTask) => {
                    const accommodationLocationDetails = transportTask.transport().pickUpLocation();
                    if (accommodationLocationDetails.text() && accommodationLocationDetails.area()) {
                        return true;
                    }
                    return false;
                },
                isValidTransportDropoffLocation: (transportTask) => {
                    const accommodationLocationDetails = transportTask.transport().dropOffLocation();
                    if (accommodationLocationDetails.text() && accommodationLocationDetails.area()) {
                        return true;
                    }
                    return false;
                },
                ecallValid: () => EcallService.validateTaskForCompletion(),
                sendBtnStatus: () => EcallService.sendBtnStatus()
            });
        }
    ]);
