var _ = require('lodash');
require('angular');

var Completion = require('@aa/malstrom-models/lib/completion.model');
var Component = require('@aa/malstrom-models/lib/component.model');
var Prompt = require('@aa/malstrom-models/lib/prompt.model');

module.exports = angular
    .module('aah-completion-service-module', [
        //services
        require('../../services/task/task.service').name,
        require('../../services/mobility/mobility-retailer.service').name,
        require('../../services/csh/csh.service').name,
        require('../../services/ui/ui.service').name,
        require('../../services/service-type/service-type.service').name,
        require('../../services/customer-group/customer-group.service').name,
        require('../../services/mobility-task/mobility-task.service').name,
        require('../../services/create-reasons/create-reasons.service').name,
        require('../../services/centurion-task-lock-refresh/centurion-task-lock-refresh.service').name,
        require('../../services/alert/alert.service').name,
        require('../../services/eligibility/eligibility.service').name,
        // constants
        require('../../constants/completion/completion-urls.constants').name,
        require('../../constants/capabilities/capabilities.constants').name,
        require('../../constants/completion/cancellation-reason.constants').name
    ])
    .service('aahCompletionService', [
        '$state',
        '$http',
        'aahCompletionURLs',
        'aahTaskService',
        'aahMobilityRetailerService',
        'aahUIService',
        'aahCSHService',
        'aahServiceTypeService',
        'aahCustomerGroupService',
        'aahMobilityTaskService',
        'aahCreateReasonsService',
        'aahCenturionTaskLockRefreshService',
        'aahAlertService',
        'aahEligibilityService',
        'aahCancellationReasons',
        function CompletionService(
            $state,
            $http,
            URLs,
            TaskService,
            MobilityRetailerService,
            UIService,
            CSHService,
            ServiceTypeService,
            CustomerGroupService,
            MobilityTaskService,
            CreateReasonsService,
            CenturionTaskLockRefreshService,
            AlertService,
            EligibilityService,
            CancellationReasonsConstant
        ) {
            var svc = this,
                _reasons = [],
                _completionReasons = [],
                _cancellationReasons = [],
                _isAdmiralNonVal = false;

            /**
             * Filter array items based on search criteria (query)
             */
            const filterItems = (arr, query) => {
                return arr.filter((el) => el.name().toLowerCase().indexOf(query.toLowerCase()) !== -1);
            };

            _.extend(svc, {
                getCompletionReasons: function getCompletionReasons() {
                    return $http.get(URLs.COMPLETION_REASONS).then(function completionReasonSuccess(response) {
                        _reasons = [];

                        _.forEach(response.data, function convertCodes(item, idx) {
                            _reasons.push(new Completion(item));
                        });

                        return _reasons;
                    });
                },

                getEcallCodes: () => {
                    return filterItems(svc.cancellationReasons(), 'ecall');
                },
                completionReasons: function completionReasonsAccessor(val) {
                    return arguments.length ? (_reasons = val) : _reasons;
                },
                getCancellationReasons: function getCancellationReasons() {
                    return $http.get(URLs.CANCELLATION_REASONS).then(function cancellationReasonSuccess(response) {
                        _reasons = [];
                        _.forEach(response.data, function convertCodes(item, idx) {
                            _reasons.push(new Completion(item));
                        });
                        if (MobilityTaskService.isEnterprise() && _.isEmpty(_cancellationReasons)) {
                            _cancellationReasons = _reasons;
                        }
                        return _reasons;
                    });
                },
                cancellationReasons: function cancellationReasonsAccessor(val) {
                    return arguments.length ? (_reasons = val) : _reasons;
                },
                getComponentCodes: function getComponentCodes() {
                    return $http.get(URLs.COMPONENT_CODES).then(function componentCodeSuccess(response) {
                        _reasons = [];

                        _.forEach(response.data, function convertCodes(item, idx) {
                            _reasons.push(new Component(item));
                        });

                        return _reasons;
                    });
                },
                componentCodes: function componentCodesAccessor(val) {
                    return arguments.length ? (_reasons = val) : _reasons;
                },
                getFaultCodes: function getFaultCodes(componentCode) {
                    return $http.get(URLs.FAULT_CODES.replace(':componentCode', componentCode)).then(function faultCodeSuccess(response) {
                        _reasons = [];

                        _.forEach(response.data, function convertCodes(item, idx) {
                            _reasons.push(new Completion(item));
                        });

                        return _reasons;
                    });
                },
                faultCodes: function faultCodesAccessor(val) {
                    return arguments.length ? (_reasons = val) : _reasons;
                },

                reset: function reset() {
                    _reasons = [];
                },
                getTaskCompletionReasonCode: (taskCompletionReasonCode) => {
                    if (_.isEmpty(_completionReasons)) {
                        $http.get(URLs.ALL_COMPLETION_REASONS).then(({ data }) => {
                            _completionReasons = data;
                            svc.showTaskCompletionAlert(taskCompletionReasonCode);
                        });
                    } else {
                        svc.showTaskCompletionAlert(taskCompletionReasonCode);
                    }
                },
                showTaskCompletionAlert: (taskCompletionReasonCode) => {
                    let completionReason = _completionReasons.find((reason) => reason.code === taskCompletionReasonCode);
                    let taskCompletionReasonName = completionReason && completionReason.name;
                    AlertService.createAlert(`Task has been completed. Reason: ${taskCompletionReasonName}`);
                    _isAdmiralNonVal = false;
                },
                completeTask: function completeTask(cancellingTask, completeMode) {
                    let taskCompletionReasonCode = TaskService.task().fault().outcome().completionCode();
                    let cancellationReason;
                    let taskCompletionReasonName;
                    let completionPromise;

                    if (MobilityTaskService.isEnterprise()) {
                        cancellationReason = !_.isEmpty(_cancellationReasons) && _cancellationReasons.find((reason) => reason.code() === taskCompletionReasonCode);
                        taskCompletionReasonName = cancellationReason && cancellationReason.name();
                        completionPromise = TaskService.task().createReason().isHireCar()
                            ? MobilityTaskService.complete(taskCompletionReasonName)
                            : TaskService.complete(CSHService.csh(), cancellingTask);
                    } else {
                        completionPromise = TaskService.task().createReason().isHireCar() ? MobilityTaskService.complete() : TaskService.complete(CSHService.csh(), cancellingTask);
                    }

                    let task = TaskService.task();

                    completionPromise.then(
                        function completeSuccess() {
                            let stateTransition;
                            if (CSHService.csh().isCompleted()) {
                                UIService.updateCR(TaskService.task());

                                if (!_isAdmiralNonVal) {
                                    ServiceTypeService.reset();
                                    CustomerGroupService.reset();
                                }

                                task.isDirty(false);
                                if (completeMode === 'qualification') {
                                    CenturionTaskLockRefreshService.cancelRefreshLock();
                                    stateTransition = $state.go('summary');
                                } else if (_isAdmiralNonVal) {
                                    EligibilityService.customerGroup(TaskService.task().entitlement().customerGroup());
                                    stateTransition = $state.go('eligibility.questions');
                                } else if (
                                    !(
                                        (task.entitlement().customerGroup().isAllJLR() || task.entitlement().customerGroup().isPorsche() || task.entitlement().customerGroup().isHyundai()) &&
                                        (task.fault().outcome().completionCode() === '71' || task.fault().outcome().completionCode() === 'XD')
                                    )
                                ) {
                                    // Cancellation code 71 -> "WARRANTY DRIVE IN (JAGUAR/LAND ROVER)"
                                    stateTransition = $state.go('home');
                                } else {
                                    CreateReasonsService.reset();
                                    CreateReasonsService.getReopenReasons(task).then(function (dataSet) {
                                        CreateReasonsService.reopenReasons(dataSet);
                                    });

                                    CreateReasonsService.getReattendReasons(task).then(function (dataSet) {
                                        CreateReasonsService.reattendReasons(dataSet);
                                    });

                                    // To be discussed-- In which case there is no state transition on task completion.
                                    stateTransition = new Promise((resolve, reject) => resolve());
                                }
                            } else {
                                stateTransition = $state.go('summary');
                            }

                            if (stateTransition) {
                                stateTransition.then(() => svc.getTaskCompletionReasonCode(taskCompletionReasonCode));
                            }
                        },
                        function completeError(error) {
                            let msg = error && error.data && error.data.msg ? error.data.msg : 'Failed to complete task';
                            AlertService.createAlert(msg);
                        }
                    );
                },
                taskCompletionCode: function taskCompletionCode(completionCode, promptId) {
                    if (promptId && promptId === Prompt.ADMIRAL_PREVENT_HOMESTART_PROMPT) {
                        _isAdmiralNonVal = completionCode === CancellationReasonsConstant.codes.ADMIRAL_HOMESTART_NON_VALIDATION;
                        return _isAdmiralNonVal;
                    }
                }
            });
        }
    ]);
