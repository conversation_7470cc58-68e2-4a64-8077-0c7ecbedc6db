var _ = require('lodash');
require('angular');
//models
const { Address } = require('@aa/data-models/common');

module.exports = angular
    .module('aah-address-lookup-service-module', [
        //constants
        require('../../constants/address-lookup/address-lookup-urls.constants').name,
        require('../../services/alert/alert.service').name
    ])
    .service('aahAddressLookupService', [
        '$http',
        'aahAddressLookupURLs',
        'aahAlertService',
        function EligibilityService($http, addressLookUpUrls, AlertService) {
            var svc,
                _disableSelect = true,
                _addresses = [];

            svc = this;

            _.extend(svc, {
                search: function search(postcode, houseNumberName) {
                    var addressLookupUrl, promise;

                    _addresses = [];

                    addressLookupUrl = addressLookUpUrls.ADDRESS_LOOKUP;

                    if (!postcode) {
                        //throw('postcode must be provided');
                        AlertService.createAlert('postcode must be provided', 'error');
                    }

                    if (houseNumberName) {
                        addressLookupUrl = addressLookupUrl + houseNumberName + '/' + postcode;
                    } else {
                        addressLookupUrl = addressLookupUrl + postcode;
                    }

                    promise = $http.get(addressLookupUrl);

                    promise.then(function (httpResponse) {
                        _.forEach(httpResponse.data, function (rawAddress) {
                            _addresses.push(new Address(rawAddress));
                        });
                    });

                    return promise;
                },
                isDisableSelect: function (val) {
                    return arguments.length ? (_disableSelect = val) : _disableSelect;
                },
                addresses: function addressesAccessor() {
                    return _addresses;
                },
                postCodePatternService: function postCodePatternService() {
                    var POSTCODE_VALIDATION_REGEXT =
                        /^(([a-zA-Z]{1,2}[0-9][a-zA-Z0-9]?|ASCN|STHL|TDCU|BBND|[BFS]IQQ|PCRN|TKCA) ?[0-9][a-zA-Z]{2}|BFPO ?[0-9]{1,4}|(KY[0-9]|MSR|VG|AI)[ -]?[0-9]{4}|[a-zA-Z]{2} ?[0-9]{2}|GE ?CX|GIR ?0A{2}|SAN ?TA1)$/;
                    return {
                        test: function (postcode) {
                            return postcode ? Boolean(postcode.replace(' ', '').match(POSTCODE_VALIDATION_REGEXT)) : false;
                        }
                    };
                },
                resetLookupAddresses: () => {
                    _addresses = [];
                }
            });
        }
    ]);
