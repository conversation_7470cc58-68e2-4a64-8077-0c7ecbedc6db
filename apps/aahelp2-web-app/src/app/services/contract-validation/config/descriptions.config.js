var _ = require('lodash');
var RoadworthyDetails = require('@aa/malstrom-models/lib/roadworthy.model');
var Moment = require('moment/moment');

module.exports = getDescription;

function getDescription(task, descriptionName, taxDueDate, motExpiryDate, constants) {
    let vrn = () => {
        let descriptions = {
            NO_VRN: {
                header: 'Invalid Vehicle Registration',
                description: `
                    <p>
                      A valid vehicle registration must be provided to obtain service today. If we are unable to capture one today, service may be declined.
                    </p>
                    <p>
                       Collect a valid VRN.
                    </p>
                `,
                callInfoText: {
                    pfuServiceDeclined: `Service Declined`
                },
                remarksText: {
                    scenario: `No VRN`
                }
            }
        };

        return descriptions[descriptionName];
    };

    let excessiveUse = (calloutCount) => {
        let descriptions = {
            EXCESSIVE_USE: {
                header: 'EXCESSIVE MEMBERSHIP USE: SAFE LOCATION',
                description: `<p>The number of callouts in your current membership year are ${calloutCount} breakdowns.</p>
                <p>Within our terms and conditions, we reserve the right to limit service where usage is deemed excessive or unreasonable You will have recently received correspondence highlighting these concerns about your current usage.</p>
                <p>As we now consider the usage on this membership to be excessive, initial roadside assistance will only be available at a fee of &#163;150. Should a recovery be required, this would also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.EXCESSIVE_USE,
                callInfoText: {
                    serviceAccepted: `Service Accepted`,
                    pfuServiceDeclined: `Service Declined`,
                    pfuQuoteDeclined: `pfu quote declined`,
                    paymentFailed: `payment failed`
                },
                remarksText: {
                    scenario: `Excessive Use`,
                    paymentSuccessful: `remarksText.paymentSuccessful`
                },
                payment: {
                    reason: constants.PAYMENT_REASONS.EXCESSIVE_USE_PFU_SERVICE_ACCEPTED,
                    amount: constants.SURCHARGE_AMOUNT.EXCESSIVE_USE
                }
            },
            EXCESSIVE_USE_DANGEROUS_LOCATION: {
                header: 'EXCESSIVE MEMBERSHIP USE: DANGEROUS LOCATION',
                description: `<p>The number of callouts in your current membership year are ${calloutCount} breakdowns.</p>
                <p>Within our terms and conditions, we reserve the right to limit service where service use is deemed excessive or unreasonable. You will have recently received correspondence from us highlighting our concerns about current usage on this membership.</p>
                <p>Our records consider usage on this membership to be excessive but as you are in a dangerous location, we will recover you to a nearby place of safety. Any further services will be chargeable.</p>`,
                remarksText: {
                    scenario: `Excessive Use`
                }
            }
        };

        return descriptions[descriptionName];
    };

    let repeatFault = () => {
        let descriptions = {
            REPEAT_FAULT: {
                header: 'REPEAT FAULT: SAFE LOCATION',
                description: `
                    <p>Our records indicate that you have previously received assistance for this fault in the last 28 days. Under the terms and conditions of your membership any further attendance for a 'same or similar fault' within 28 days are not covered.</p>
                    <p>To receive service for this fault, a one off payment of &#163;75 is required before a patrol will attend. Should recovery be required, this will also be chargeable.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.REPEAT_FAULT,
                callInfoText: {
                    serviceAccepted: `Service Accepted`,
                    pfuServiceDeclined: `Service Declined`,
                    pfuQuoteDeclined: `pfu quote declined`,
                    paymentFailed: `payment failed`
                },
                remarksText: {
                    scenario: `Repeat Fault`,
                    paymentSuccessful: `remarksText.paymentSuccessful`
                },
                payment: {
                    reason: constants.PAYMENT_REASONS.REPEAT_FAULT_PFU,
                    amount: constants.SURCHARGE_AMOUNT.REPEAT_FAULT
                }
            },
            REPEAT_FAULT_DANGEROUS_LOCATION: {
                header: 'REPEAT FAULT: DANGEROUS LOCATION',
                description: `
                    <p>Our records indicate that you have previously received assistance for this fault in the last 28 days. Under the terms and conditions of your membership any further attendance for a 'same or similar fault' within 28 days are not covered.</p>
                    <p>As you are in a dangerous location we will recover you to a nearby place of safety. Following this any further service will be chargeable.</p>
                `,
                remarksText: {
                    scenario: `Repeat Fault`
                }
            }
        };

        return descriptions[descriptionName];
    };

    let secondRecovery = (data) => {
        const prevDate = Moment(data.secRecovery.prevDate).format('DD/MM/YYYY');
        let descriptions = {
            SECOND_RECOVERY: {
                header: '2<sup>nd</sup> Recovery Recognition',
                description: `
                    <p>
                        ${data.secRecovery.name}, our records indicate that you have previously had
                        assistance moving this vehicle (${data.secRecovery.vrn}) to a single destination in the last 28 days.
                    </p>

                    <p>
                        <strong>Previous Breakdown Location (${prevDate}):</strong>
                        <br>
                        ${data.secRecovery.prevFrom}
                    </p>

                    <p>
                        <strong>Previous Recovery Destination:</strong>
                        <br>
                        ${data.secRecovery.prevTo}
                    </p>

                    <p>
                        Under the terms and conditions of your membership, you are entitled to one recovery
                        to a destination within the UK per breakdown. Therefore, this recovery would
                        be at your own expense.
                    </p>
                `,
                callInfoText: {
                    serviceAccepted: `Service Accepted`,
                    pfuServiceDeclined: `Service Declined`,
                    pfuQuoteDeclined: `pfu quote declined`,
                    paymentFailed: `payment failed`
                },
                remarksText: {
                    scenario: `Second recovery`,
                    paymentSuccessful: `remarksText.paymentSuccessful`
                },
                payment: {
                    reason: constants.PAYMENT_REASONS.SECOND_RECOVERY_PFU
                }
            }
        };

        return descriptions[descriptionName];
    };

    let safeLocation = (task) => {
        let descriptions = {
            NO_TAX_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE: SAFE LOCATION',
                description: `<p>We have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                      <p>The terms and conditions of your membership state you must have a valid Road Fund Licence (Tax) [unless exempt].</p>
                      <p>As your vehicle has no Tax, we recommend you tax your vehicle and call back for assistance once DVLA records are updated to reflect this.</p>
                      <p>Service will be provided once DVLA records show that the vehicle is taxed.</p>
                      <p>You can check tax status online at <span>gov.uk/check-vehicle-tax</span></p>
                      <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>
                      `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: {
                    serviceAccepted: `Vehicle has no TAX (outside grace), member ringing back when tax has been purchased.`,
                    pfuServiceDeclined: `Vehicle has no Tax (outside grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no Tax (outside grace), PFU quote declined by member.`,
                    paymentFailed: `Vehicle has no TAX (outside grace), PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `NO TAX`,
                    paymentSuccessful: `[NO TAX - PFU FULL LIFT] `
                }
            },

            NO_TAX_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE: SAFE LOCATION',
                description: `<p>We have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid road fund licence (Tax) [unless exempt].</p>
                <p>As your vehicle has no Tax we recommend you tax your vehicle and call back for assistance at your earliest convenience with the purchase reference number.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: {
                    serviceAccepted: `Vehicle has no Tax (within grace), member ringing back when tax has been purchased.`,
                    courtesyDeclined: `Vehicle has no Tax (within grace), courtesy service declined by member.`
                },
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX - COURTESY FULL LIFT UP TO ENTITLEMENTS ONLY] `
                }
            },

            NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO MOT OUTSIDE GRACE: SAFE LOCATION',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt].</p>
                <p>As your vehicle has no MOT we can arrange a recovery for you, this will be at your own expense.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: {
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace), PFU quote declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (outside grace), PFU service declined by member.`,
                    paymentFailed: `Vehicle has no MOT (outside grace), PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    paymentSuccessful: `[NO MOT - PFU FULL LIFT] `
                }
            },
            NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO MOT WITHIN GRACE: SAFE LOCATION',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt].</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we can arrange a courtesy recovery for you.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace), courtesy service declined by member.`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT - COURTESY FULL LIFT UP TO ENTITLEMENTS ONLY] `
                }
            },

            SORN_SAFE_LOCATION: {
                header: 'SORN: SAFE LOCATION',
                description: `<p>We have checked the DVLA database and found your vehicle is declared off road (SORN).</p>`,
                prevContent: `<p>The terms and conditions of your membership state you must have a valid Road Fund Licence (Tax) [unless exempt] and a valid and current MOT test certificate [unless exempt].</p>
                <p>As your vehicle has been declared SORN we can arrange a recovery for you, this will be at your own expense.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                additionalContent: `<div><p>As you are on private land, we will send a patrol out to you today to assess and attempt to fix your vehicle.</p>
                <p>The patrol is unable to carry out any road tests on public highways due to the SORN status. If a recovery is required, then a full lift recovery will be arranged as all wheels need to be off the ground, this will be at your own expense. Is that ok?</p>
                <p>[If customer is not happy with this, we can only offer them a PFU recovery. Please override using FLP - SORN - Private Land]</p>
                </div>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.SORN,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle declared SORN, PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle declared SORN, PFU quote declined by member.`,
                    paymentFailed: `Vehicle declared SORN, PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `VEHICLE SORN`,
                    paymentSuccessful: `[VEHICLE SORN - PFU FULL LIFT] `
                }
            },

            NO_TAX_WITHIN_GRACE_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT WITHIN GRACE : SAFE LOCATION',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt] and valid Road Fund Licence(Tax) [unless exempt].</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we can arrange a courtesy recovery for you.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace) or Tax (within grace), courtesy service declined by member.`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - COURTESY FULL LIFT UP TO ENTITLEMENTS ONLY]`
                }
            },

            NO_TAX_OUTSIDE_GRACE_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT WITHIN GRACE : SAFE LOCATION',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt] and valid Road Fund Licence(Tax) [unless exempt].</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we can arrange a courtesy recovery for you.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace) or Tax (outside grace), courtesy service declined by member`,
                    pfuQuoteDeclined: `Vehicle has no MOT (within grace) or Tax (outside grace), PFU quote declined by member`,
                    paymentFailed: `Vehicle has no MOT (within grace) or Tax (outside grace), PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT] `,
                    courtesyAccepted: `[NO TAX/NO MOT - COURTESY FULL LIFT UP TO ENTITLEMENTS ONLY] `
                }
            },

            NO_TAX_OUTSIDE_GRACE_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT OUTSIDE GRACE : SAFE LOCATION',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt] and valid Road Fund Licence(Tax) [unless exempt].</p>
                <p>As your vehicle has no MOT or Tax we can arrange a recovery for you, this will be at your own expense.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle has no MOT (outside grace) or Tax (outside grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace) or Tax (outside grace), PFU quote declined by member.`,
                    paymentFailed: `Vehicle has no MOT (outside grace) or Tax (outside grace), PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT] `,
                    paymentSuccessNoTax: `[NO TAX - PFU FULL LIFT]`
                }
            },
            NO_TAX_WITHIN_GRACE_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT OUTSIDE GRACE : SAFE LOCATION',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt] and valid Road Fund Licence(Tax) [unless exempt].</p>
                <p>As your vehicle has no MOT or Tax we can arrange a recovery for you, this will be at your own expense.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle has no MOT (outside grace) or Tax (within grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace) or Tax (within grace), PFU quote declined by member.`,
                    paymentFailed: `Vehicle has no MOT (outside grace) or Tax (within grace), PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT] `,
                    courtesyAccepted: `[NO TAX - COURTESY FULL LIFT UP TO ENTITLEMENTS ONLY] `
                }
            },

            /** B2B_CUSTOMER_PAYS */
            B2B_CUSTOMER_PAYS_NO_TAX_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE: SAFE LOCATION - B2B DRIVER PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The DVLA records show your vehicle has no tax, you would need to tax your vehicle and call back for assistance once DVLA records are updated to reflect this.</p>
                <p>Service will be provided once DVLA records show that the vehicle is taxed.  </p>
                <p>If you have recently taxed your vehicle and can provide proof that the vehicle is now taxed, I can transfer you to the relevant team who will discuss your proof of purchase before service can be arranged.</p>
                <p>Alternatively, you can pay for a recovery to move your vehicle to a destination of your choice. This must be done with all four wheels lifted off the ground.</p>
                <p>The payment can be refunded if you can provide proof within 7 days of the recovery (<EMAIL>) that the vehicle was taxed at the time of your original breakdown call.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: {
                    serviceAccepted: `Vehicle has no TAX (outside grace), member ringing back when tax has been purchased.`,
                    pfuServiceDeclined: `Vehicle has no Tax (outside grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no Tax (outside grace), PFU quote declined by member.`,
                    paymentFailed: `Vehicle has no Tax (outside grace), PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `NO TAX`,
                    paymentSuccessful: `[NO TAX - PFU FULL LIFT] `
                }
            },

            B2B_CUSTOMER_PAYS_NO_TAX_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE: SAFE LOCATION - B2B DRIVER PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no Tax we recommend you tax your vehicle and call back for assistance at your earliest convenience with the purchase reference number.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: {
                    serviceAccepted: `Vehicle has no Tax (within grace), member ringing back when tax has been purchased.`,
                    courtesyDeclined: `Vehicle has no Tax (within grace), courtesy service declined by member.`
                },
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX - COURTESY FULL LIFT - WITHIN GRACE] `
                }
            },

            B2B_CUSTOMER_PAYS_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO MOT OUTSIDE GRACE: SAFE LOCATION - B2B DRIVER PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicle has no MOT we can arrange a recovery for you, this will be at your own expense.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                 <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: {
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace), PFU quote declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (outside grace), PFU service declined by member.`,
                    paymentFailed: `Vehicle has no MOT (outside grace), PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    paymentSuccessful: `[NO MOT - PFU FULL LIFT] `
                }
            },

            B2B_CUSTOMER_PAYS_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO MOT WITHIN GRACE: SAFE LOCATION - B2B DRIVER PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we can arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace), courtesy service declined by member.`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT - COURTESY FULL LIFT - WITHIN GRACE] `
                }
            },

            B2B_CUSTOMER_PAYS_SORN_SAFE_LOCATION: {
                header: 'SORN: SAFE LOCATION - B2B DRIVER PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle is declared off road (SORN).</p>`,
                prevContent: `<p>The terms and conditions of your membership state you must have a valid Road Fund Licence (Tax) [unless exempt] and a valid and current MOT test certificate [unless exempt].</p>
                <p>As your vehicle has been declared SORN we can arrange a recovery for you, this will be at your own expense.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                additionalContent: `<div>
                <p>As you are on private land we will send a patrol out to you today to assess
                and  attempt to fix your vehicle. </p>
                <p>The patrol is unable to carry out any road
                tests on public highways due to the SORN status. If a recovery is required,
                then a full lift recovery will be arranged as all wheels need to be off the
                ground. Is that ok?</p>
                <p>[If customer is not happy with this we can only offer them a PFU recovery.Please override using FLP - SORN -private Land]</p>
                </div>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.SORN,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle declared SORN, PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle declared SORN, PFU quote declined by member.`,
                    paymentFailed: `Vehicle declared SORN, PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `VEHICLE SORN`,
                    paymentSuccessful: `[VEHICLE SORN - PFU FULL LIFT]`
                }
            },

            B2B_CUSTOMER_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT WITHIN GRACE : SAFE LOCATION - B2B DRIVER PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and TAX expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we can arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace) or Tax (within grace), courtesy service declined by member.`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - COURTESY FULL LIFT UP TO ENTITLEMENTS ONLY]`
                }
            },

            B2B_CUSTOMER_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT WITHIN GRACE : SAFE LOCATION - B2B DRIVER PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and TAX expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we can arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                 <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace) or Tax (outside grace), courtesy service declined by member`,
                    pfuQuoteDeclined: `Vehicle has no MOT (within grace) or Tax (outside grace), PFU quote declined by member`,
                    paymentFailed: `Vehicle has no MOT (within grace) or Tax (outside grace), PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT] `,
                    courtesyAccepted: `[NO TAX/NO MOT - COURTESY FULL LIFT - WITHIN GRACE] `
                }
            },

            B2B_CUSTOMER_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT OUTSIDE GRACE : SAFE LOCATION - B2B DRIVER PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and TAX expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax we can arrange a recovery for you, this will be at your own expense.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                 <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle has no MOT (outside grace) or Tax (outside grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace) or Tax (outside grace), PFU quote declined by member.`,
                    paymentFailed: `Vehicle has no MOT (outside grace) or Tax (outside grace), PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT] `
                }
            },

            B2B_CUSTOMER_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT OUTSIDE GRACE : SAFE LOCATION - B2B DRIVER PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and TAX expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax we can arrange a recovery for you, this will be at your own expense.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                 <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle has no MOT (outside grace) or Tax (within grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace) or Tax (within grace), PFU quote declined by member.`,
                    paymentFailed: `Vehicle has no MOT (outside grace) or Tax (within grace), PFU payment unsuccessful`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT ONLY] `,
                    courtesyAccepted: `[NO TAX - COURTESY FULL LIFT UP TO ENTITLEMENTS ONLY] `
                }
            },

            /** B2B_COMPANY_PAYS */

            B2B_COMPANY_PAYS_NO_TAX_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE: SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no Tax, we will arrange a recovery for you. It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: {
                    serviceAccepted: `Vehicle has no Tax (outside grace), member ringing back when tax has been purchased.`,
                    pfuServiceDeclined: `Vehicle has no Tax (outside grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no Tax (outside grace), PFU quote declined by member.`,
                    serviceDeclined: `Vehicle has no Tax (outside grace), service declined by member.`
                },
                remarksText: {
                    scenario: `NO TAX`,
                    paymentSuccessful: `[NO TAX - PFU FULL LIFT ONLY] `,
                    serviceAccepted: `NO TAX - FULL LIFT ONLY`
                }
            },

            B2B_COMPANY_PAYS_NO_TAX_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE: SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicles Tax expired within the last ${RoadworthyDetails.GRACELENGTH} days, we will arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: {
                    serviceAccepted: `Vehicle has no Tax (within grace), member ringing back when tax has been purchased.`,
                    courtesyDeclined: `Vehicle has no Tax (within grace), courtesy service declined by member.`,
                    serviceDeclined: 'Vehicle has no Tax (within grace), service declined by member'
                },
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX - COURTESY FULL LIFT ONLY] `,
                    serviceAccepted: `[NO TAX - COURTESY FULL LIFT - WITHIN GRACE]`
                }
            },

            B2B_COMPANY_PAYS_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO MOT OUTSIDE GRACE: SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicle has no MOT, we will arrange a recovery for you. It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: {
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace), PFU quote declined by member.`,
                    pfuServiceDeclined: `Vehicle has no MOT (outside grace), PFU service declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (outside grace), service declined by member.`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    paymentSuccessful: `[NO MOT - PFU FULL LIFT ONLY] `,
                    serviceAccepted: `NO MOT - FULL LIFT ONLY`
                }
            },

            B2B_COMPANY_PAYS_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO MOT WITHIN GRACE: SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we will arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace), courtesy service declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (within grace), service declined by member.`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT - COURTESY FULL LIFT - WITHIN GRACE] `,
                    serviceAccepted: `[NO MOT - COURTESY FULL LIFT - WITHIN GRACE]`
                }
            },

            B2B_COMPANY_PAYS_SORN_SAFE_LOCATION: {
                header: 'SORN: SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle is declared off road (SORN).</p>`,
                prevContent: `<p>The terms and conditions of your membership state you must have a valid Road Fund Licence (Tax) [unless exempt] and a valid and current MOT test certificate [unless exempt].</p>
                <p>As your vehicle has been declared SORN we can arrange a recovery for you, this will be at your own expense.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                additionalContent: `<div>
                <p>As you are on private land,we will send a patrol out to you today to assess and attempt to fix your vehicle.</p>
                <p>The patrol is unable to carry out any  road
                tests on public highways due to the SORN status. If a recovery is required,
                then a full lift recovery will be arranged as all wheels need to be off the
                ground. Is that ok?</p>
                <p>[If customer is not happy with this we can only offer them a PFU recovery, Please override using FLP - SORN - Private Land]</p>
                </div>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.SORN,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle declared SORN, PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle declared SORN, PFU quote declined by member.`,
                    serviceDeclined: `Vehicle declared SORN, service declined by member.`
                },
                remarksText: {
                    scenario: `VEHICLE SORN`,
                    paymentSuccessful: `[VEHICLE SORN - PFU FULL LIFT ONLY] `,
                    serviceAccepted: `VEHICLE SORN - FULL LIFT ONLY`
                }
            },

            B2B_COMPANY_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT WITHIN GRACE : SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we will arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace) or Tax (within grace), courtesy service declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (within grace) or Tax (within grace), service declined by member`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    serviceAccepted: `[NO TAX/NO MOT - COURTESY FULL LIFT - WITHIN GRACE]`
                }
            },

            B2B_COMPANY_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT WITHIN GRACE : SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we will arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                 `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace) or Tax (outside grace), courtesy service declined by member`,
                    serviceDeclined: `Vehicle has no MOT (within grace) or Tax (outside grace), service declined by member`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT] `,
                    courtesyAccepted: `[NO TAX/NO MOT - COURTESY FULL LIFT - WITHIN GRACE] `,
                    serviceAccepted: `[NO TAX/NO MOT - COURTESY FULL LIFT - WITHIN GRACE]`
                }
            },

            B2B_COMPANY_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT OUTSIDE GRACE : SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, we will arrange a recovery for you. It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle has no MOT (outside grace) or Tax (outside grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace) or Tax (outside grace), PFU quote declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (outside grace) or Tax (outside grace), service declined by member.`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT ONLY] `,
                    serviceAccepted: `NO TAX/NO MOT - FULL LIFT ONLY`
                }
            },

            B2B_COMPANY_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT OUTSIDE GRACE : SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, we will arrange a recovery for you. It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle has no MOT (outside grace) or Tax (within grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace) or Tax (within grace), PFU quote declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (outside grace) or Tax (within grace), service declined by member.`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT ONLY] `,
                    courtesyAccepted: `[NO TAX - COURTESY FULL LIFT UP TO ENTITLEMENTS ONLY] `,
                    serviceAccepted: `NO TAX/NO MOT - FULL LIFT ONLY`
                }
            },

            /** B2B_COMPANY_MANUFACTURER_PAYS */

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE: SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no Tax, we will arrange a recovery for you. It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: {
                    serviceAccepted: `Vehicle has no Tax (outside grace), member ringing back when tax has been purchased.`,
                    pfuServiceDeclined: `Vehicle has no Tax (outside grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no Tax (outside grace), PFU quote declined by member.`,
                    serviceDeclined: `Vehicle has no Tax (outside grace), service declined by member.`
                },
                remarksText: {
                    scenario: `NO TAX`,
                    paymentSuccessful: `[NO TAX - PFU FULL LIFT ONLY] `,
                    serviceAccepted: `NO TAX - FULL LIFT ONLY`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE: SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicles Tax expired within the last ${RoadworthyDetails.GRACELENGTH} days, we will arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: {
                    serviceAccepted: `Vehicle has no Tax (within grace), member ringing back when tax has been purchased.`,
                    courtesyDeclined: `Vehicle has no Tax (within grace), courtesy service declined by member.`,
                    serviceDeclined: 'Vehicle has no Tax (within grace), service declined by member'
                },
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX - COURTESY FULL LIFT ONLY] `,
                    serviceAccepted: `[NO TAX - COURTESY FULL LIFT - WITHIN GRACE]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO MOT OUTSIDE GRACE: SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicle has no MOT, we will arrange a recovery for you. It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: {
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace), PFU quote declined by member.`,
                    pfuServiceDeclined: `Vehicle has no MOT (outside grace), PFU service declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (outside grace), service declined by member.`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    paymentSuccessful: `[NO MOT - PFU FULL LIFT ONLY] `,
                    serviceAccepted: `NO MOT - FULL LIFT ONLY`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO MOT WITHIN GRACE: SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we will arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace), courtesy service declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (within grace), service declined by member.`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT - COURTESY FULL LIFT - WITHIN GRACE] `,
                    serviceAccepted: `[NO MOT - COURTESY FULL LIFT - WITHIN GRACE]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_SORN_SAFE_LOCATION: {
                header: 'SORN: SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle is declared off road (SORN).</p>`,
                prevContent: `<p>The terms and conditions of your membership state you must have a valid Road Fund Licence (Tax) [unless exempt] and a valid and current MOT test certificate [unless exempt].</p>
                <p>As your vehicle has been declared SORN we can arrange a recovery for you, this will be at your own expense.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                additionalContent: `<div>
                <p>As you are on private land we will send a patrol out to you today to assess
                and  attempt to fix your vehicle. The patrol is unable to carry out any  road
                tests on public highways due to the SORN status. If a recovery is required,
                then a full lift recovery will be arranged as all wheels need to be off the
                ground. Is that ok?</p>
                <p>If customer is not happy with this we can only offer them a PFU recovery.</p>
                </div>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.SORN,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle declared SORN, PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle declared SORN, PFU quote declined by member.`,
                    serviceDeclined: `Vehicle declared SORN, service declined by member.`
                },
                remarksText: {
                    scenario: `VEHICLE SORN`,
                    paymentSuccessful: `[VEHICLE SORN - PFU FULL LIFT ONLY] `,
                    serviceAccepted: `VEHICLE SORN - FULL LIFT ONLY`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT WITHIN GRACE : SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we will arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace) or Tax (within grace), courtesy service declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (within grace) or Tax (within grace), service declined by member`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    serviceAccepted: `[NO TAX/NO MOT - COURTESY FULL LIFT - WITHIN GRACE]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_WITHIN_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT WITHIN GRACE : SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicles MOT expired within the last ${RoadworthyDetails.GRACELENGTH} days, we will arrange a courtesy recovery for you.
                 It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                 `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    courtesyDeclined: `Vehicle has no MOT (within grace) or Tax (outside grace), courtesy service declined by member`,
                    serviceDeclined: `Vehicle has no MOT (within grace) or Tax (outside grace), service declined by member`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT] `,
                    courtesyAccepted: `[NO TAX/NO MOT - COURTESY FULL LIFT - WITHIN GRACE] `,
                    serviceAccepted: `[NO TAX/NO MOT - COURTESY FULL LIFT - WITHIN GRACE]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT OUTSIDE GRACE : SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, we will arrange a recovery for you. It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle has no MOT (outside grace) or Tax (outside grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace) or Tax (outside grace), PFU quote declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (outside grace) or Tax (outside grace), service declined by member.`
                },
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT ONLY] `,
                    serviceAccepted: `NO TAX/NO MOT - FULL LIFT ONLY`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_OUTSIDE_GRACE_SAFE_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT OUTSIDE GRACE : SAFE LOCATION - B2B COMPANY PAYS',
                description: `<p>We have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, we will arrange a recovery for you. It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_SAFE_LOCATION,
                callInfoText: {
                    pfuServiceDeclined: `Vehicle has no MOT (outside grace) or Tax (within grace), PFU service declined by member.`,
                    pfuQuoteDeclined: `Vehicle has no MOT (outside grace) or Tax (within grace), PFU quote declined by member.`,
                    serviceDeclined: `Vehicle has no MOT (outside grace) or Tax (within grace), service declined by member.`
                },
                remarksText: {
                    scenario: `NO MOT`,
                    paymentSuccessful: `[NO TAX/NO MOT - PFU FULL LIFT ONLY] `,
                    courtesyAccepted: `[NO TAX - COURTESY FULL LIFT UP TO ENTITLEMENTS ONLY] `,
                    serviceAccepted: `NO TAX/NO MOT - FULL LIFT ONLY`
                }
            }
        };
        return descriptions[descriptionName];
    };

    let dangerousLocation = () => {
        let descriptions = {
            NO_TAX_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE: DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid Road Fund Licence(Tax) [unless exempt].</p>
                <p>As you are in a dangerous location we will recover you to a nearby place of safety.</p>
                <p>Once you and your vehicle are safe, we can only arrange an additional recovery for you, however this will be at your own expense. We will discuss this further when you are in a safe location.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: `Vehicle has no Tax (outside grace), courtesy recovery to safe location accepted`,
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX- DANGLOC - FULL LIFT - MBR TO RB AT SAFLOC] `
                }
            },

            NO_TAX_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE: DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid Road Fund Licence(Tax) [unless exempt].</p>
                <p>As your vehicle has no Tax, if you do not tax your vehicle upon arrival at a safe location we can only arrange a recovery for you, we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: `Vehicle has no Tax (within Grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX- DANGLOC - FULL LIFT - MBR TO RB AT SAFLOC] `
                }
            },

            NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO MOT WITHIN GRACE: DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt].</p>
                <p>As your vehicle has no MOT, upon arrival at a safe location we can only arrange a recovery for you, we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: `Vehicle has no MOT (within grace), courtesy recovery to safe location accepted`,
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT- DANGLOC - FULL LIFT - MBR TO RB AT SAFLOC] `
                }
            },
            NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO MOT OUTSIDE GRACE: DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt].</p>
                <p>As your vehicle has no MOT, upon arrival at a safe location we can only arrange a recovery for you, this will be at your own expense, we will discuss this further when you are in a safe location.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: `Vehicle has no MOT (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT- DANGLOC - FULL LIFT - MBR TO RB AT SAFLOC] `
                }
            },

            SORN_DANGEROUS_LOCATION: {
                header: 'SORN: DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle is declared off road (SORN).</p>
                <p>The terms and conditions of your membership state you must have a valid Road Fund Licence (Tax) [unless exempt] and a valid and current MOT test certificate [unless exempt].</p>
                <p>As your vehicle has been declared SORN we can only arrange a recovery for you, this will be at your own expense, we will discuss this further when you are in a safe location.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.SORN,
                callInfoText: `Vehicle declared SORN, courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `VEHICLE SORN`,
                    courtesyAccepted: `[NO MOT- DANGLOC - FULL LIFT - MBR TO RB AT SAFLOC] `
                }
            },

            // SORN_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
            //     header: 'NO TAX OUTSIDE GRACE: DANGEROUS LOCATION',
            //     description: `We will begin by moving your vehicle to a safe location however our system has identified your vehicle's TAX is expired on <strong>${taxDueDate}</strong>.              <br />The terms and conditions of your membership state you must have a valid Road Fund Licence(Tax).              As your vehicle has no TAX, if you do not tax your vehicle upon arrival at a safe location we can only arrange a recovery for you, this may be at your own expense.`,
            //     requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX
            // },
            // SORN_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
            //     header: 'NO TAX OUTSIDE GRACE: DANGEROUS LOCATION',
            //     description: `We will begin by moving your vehicle to a safe location however our system has identified your vehicle's TAX is expired on <strong>${taxDueDate}</strong>.              <br />The terms and conditions of your membership state you must have a valid Road Fund Licence(Tax).              As your vehicle has no TAX, if you do not tax your vehicle upon arrival at a safe location we can only arrange a recovery for you, this may be at your own expense.`,
            //     requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX
            // },

            NO_TAX_WITHIN_GRACE_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT WITHIN GRACE : DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt] and valid Road Fund Licence (Tax) [unless exempt]. </p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location we can only arrange a recovery for you, we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (within grace) or Tax (within grace), courtesy recovery to safe location accepted. `,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT - MBR TO RB AT SAFLOC] `
                }
            },

            NO_TAX_OUTSIDE_GRACE_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT WITHIN GRACE : DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt] and valid Road Fund Licence (Tax) [unless exempt]. </p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location we can only arrange a recovery for you, we will discuss this further when you are in a safe location.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (within grace) or Tax (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT - MBR TO RB AT SAFLOC] `
                }
            },

            NO_TAX_OUTSIDE_GRACE_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT OUTSIDE GRACE : DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt] and valid Road Fund Licence (Tax) [unless exempt]. </p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location we can only arrange a recovery for you, this will be at your own expense, we will discuss this further when you are in a safe location.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (outside grace) or Tax (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT - MBR TO RB AT SAFLOC] `
                }
            },
            NO_TAX_WITHIN_GRACE_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT OUTSIDE GRACE : DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>The terms and conditions of your membership state you must have a valid and current MOT test certificate [unless exempt] and valid Road Fund Licence (Tax) [unless exempt]. </p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location we can only arrange a recovery for you, this will be at your own expense, we will discuss this further when you are in a safe location.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (outside grace) or Tax (within grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT - MBR TO RB AT SAFLOC] `
                }
            },

            /** B2B_CUSTOMER_PAYS */
            B2B_CUSTOMER_PAYS_NO_TAX_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE: DANGEROUS LOCATION - B2B DRIVER PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no Tax, we can only arrange a recovery for you. This will be at your own expense, we will discuss this further when you are in a safe location.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: `Vehicle has no Tax (outside grace), courtesy recovery to safe location accepted`,
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_CUSTOMER_PAYS_NO_TAX_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE: DANGEROUS LOCATION - B2B DRIVER PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no Tax, if you do not tax your vehicle upon arrival at a safe location we can only arrange a recovery for you,
                this will be at your own expense, we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: `Vehicle has no Tax (within Grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_CUSTOMER_PAYS_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO MOT WITHIN GRACE: DANGEROUS LOCATION - B2B DRIVER PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicle has no MOT, upon arrival at a safe location we can only arrange a recovery for you,
                 we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: `Vehicle has no MOT (within grace), courtesy recovery to safe location accepted`,
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },
            B2B_CUSTOMER_PAYS_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO MOT OUTSIDE GRACE: DANGEROUS LOCATION - B2B DRIVER PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicle has no MOT, upon arrival at a safe location we can only arrange a recovery for you,
                 we will discuss this further when you are in a safe location.</p>
                 <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: `Vehicle has no MOT (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_CUSTOMER_PAYS_SORN_DANGEROUS_LOCATION: {
                header: 'SORN: DANGEROUS LOCATION - B2B DRIVER PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle is declared off road (SORN).</p>
                <p>As your vehicle has no MOT, upon arrival at a safe location we can only arrange a recovery for you,
                 we will discuss this further when you are in a safe location.</p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.SORN,
                callInfoText: `Vehicle declared SORN, courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `VEHICLE SORN`,
                    courtesyAccepted: `[VEHICLE SORN - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_CUSTOMER_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT WITHIN GRACE : DANGEROUS LOCATION - B2B DRIVER PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location we can only arrange a recovery for you,
                 we will discuss this further when you are in a safe location.</p>
                 <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (within grace) or Tax (within grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_CUSTOMER_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT WITHIN GRACE : DANGEROUS LOCATION - B2B DRIVER PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location we can only arrange a recovery for you,
                 we will discuss this further when you are in a safe location.</p>
                 <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (within grace) or Tax (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_CUSTOMER_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT OUTSIDE GRACE : DANGEROUS LOCATION - B2B DRIVER PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location we can only arrange a recovery for you,
                 we will discuss this further when you are in a safe location.</p>
                 <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (outside grace) or Tax (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_CUSTOMER_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT OUTSIDE GRACE : DANGEROUS LOCATION - B2B DRIVER PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location we can only arrange a recovery for you,
                 we will discuss this further when you are in a safe location.</p>
                 <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (outside grace) or Tax (within grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            /** B2B_COMPANY_PAYS */
            B2B_COMPANY_PAYS_NO_TAX_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE: DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no Tax, if you do not tax your vehicle upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: `Vehicle has no Tax (outside grace), courtesy recovery to safe location accepted`,
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_PAYS_NO_TAX_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE: DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no Tax, if you do not tax your vehicle upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: `Vehicle has no Tax (within Grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_PAYS_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO MOT WITHIN GRACE: DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicle has no Tax, if you do not tax your vehicle upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: `Vehicle has no MOT (within grace), courtesy recovery to safe location accepted`,
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },
            B2B_COMPANY_PAYS_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO MOT OUTSIDE GRACE: DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicle has no MOT, upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: `Vehicle has no MOT (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_PAYS_SORN_DANGEROUS_LOCATION: {
                header: 'SORN: DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle is declared off road (SORN).</p>
                <p>As your vehicle has been declared SORN, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.SORN,
                callInfoText: `Vehicle declared SORN, courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `VEHICLE SORN`,
                    courtesyAccepted: `[VEHICLE SORN - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT WITHIN GRACE : DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (within grace) or Tax (within grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT WITHIN GRACE : DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (within grace) or Tax (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT OUTSIDE GRACE : DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (outside grace) or Tax (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT OUTSIDE GRACE : DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (outside grace) or Tax (within grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            /** B2B_COMPANY_MANUFACTURER_PAYS */
            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE: DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no Tax, if you do not tax your vehicle upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: `Vehicle has no Tax (outside grace), courtesy recovery to safe location accepted`,
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE: DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no Tax, if you do not tax your vehicle upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX,
                callInfoText: `Vehicle has no Tax (within Grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX`,
                    courtesyAccepted: `[NO TAX - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO MOT WITHIN GRACE: DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicle has no Tax, if you do not tax your vehicle upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: `Vehicle has no MOT (within grace), courtesy recovery to safe location accepted`,
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO MOT OUTSIDE GRACE: DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong>.</p>
                <p>As your vehicle has no MOT, upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.MOT,
                callInfoText: `Vehicle has no MOT (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO MOT`,
                    courtesyAccepted: `[NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_SORN_DANGEROUS_LOCATION: {
                header: 'SORN: DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle is declared off road (SORN).</p>
                <p>As your vehicle has been declared SORN, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.SORN,
                callInfoText: `Vehicle declared SORN, courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `VEHICLE SORN`,
                    courtesyAccepted: `[VEHICLE SORN - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT WITHIN GRACE : DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (within grace) or Tax (within grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_WITHIN_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT WITHIN GRACE : DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (within grace) or Tax (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_OUTSIDE_GRACE_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX OUTSIDE GRACE, NO MOT OUTSIDE GRACE : DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (outside grace) or Tax (outside grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            B2B_COMPANY_MANUFACTURER_PAYS_NO_TAX_WITHIN_GRACE_NO_MOT_OUTSIDE_GRACE_DANGEROUS_LOCATION: {
                header: 'NO TAX WITHIN GRACE, NO MOT OUTSIDE GRACE : DANGEROUS LOCATION - B2B COMPANY PAYS',
                description: `<p>We will begin by moving your vehicle to a safe location however we have checked the DVLA database and found your vehicle's MOT expired on <strong>${motExpiryDate}</strong> and Tax expired on <strong>${taxDueDate}</strong>.</p>
                <p>As your vehicle has no MOT or Tax, upon arrival at a safe location, we will arrange a recovery for you. we will discuss this further when you are in a safe location.</p>
                `,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.TAX_AND_MOT_DANGEROUS_LOCATION,
                callInfoText: `Vehicle has no MOT (outside grace) or Tax (within grace), courtesy recovery to safe location accepted.`,
                remarksText: {
                    scenario: `NO TAX/NO MOT`,
                    courtesyAccepted: `[NO TAX/NO MOT - DANGLOC - FULL LIFT ONLY - MBR TO RB AT SAFLOC]`
                }
            },

            /** Within Cooling Off */
            WITHIN_COOLING_OFF_PERIOD_RELAY: {
                header: 'NEW MEMBERSHIP- RELAY: DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however the terms and conditions of your membership state National Recovery (Relay) cover is available only to those who have purchased Relay cover at least 24 hours before the relevant breakdown.</p>
                <p>For National Recovery (Relay) to be provided upon arrival at a safe location you will need to pay a surcharge of &#163;75.00</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                callInfoText: 'Relay entitlement purchased within 24 hours of breakdown, courtesy recovery to safe location.',
                remarksText: {
                    scenario: `24 Hour Exclusion(Relay-DANGEROUS LOCATION)`,
                    courtesyAccepted: `[NEW UPGRADE (RELAY) - DANGLOC - MBR TO RB AT SAFLOC]`
                }
            },
            WITHIN_COOLING_OFF_PERIOD_HOMESTART: {
                header: 'NEW MEMBERSHIP- HOMESTART: DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however the terms and conditions of your membership state At Home (Home Start) cover is available only to those who have purchased Home Start cover at least 24 hours before the relevant breakdown.</p>
                <p>For further assistance to be provided upon arrival at a safe location you will need to pay a surcharge of &#163;75.00</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                callInfoText: 'Homestart entitlement purchased within 24 hours of breakdown, courtesy recovery to safe location .',
                remarksText: {
                    scenario: `24 Hour Exclusion(Homestart-DANGEROUS LOCATION)`,
                    courtesyAccepted: `[NEW UPGRADE (HOMESTART) - DANGLOC - MBR TO RB AT SAFLOC]`
                }
            },
            WITHIN_COOLING_OFF_PERIOD_ROADSIDE: {
                header: 'NEW MEMBERSHIP: DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however the terms and conditions of your membership state Roadside Assistance is only available if you have purchased the cover at least 24 hours before the relevant breakdown.</p>
                <p>For further assistance to be provided upon arrival at a safe location you will need to pay a surcharge of &#163;150.00.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                callInfoText: 'Membership purchased within 24 hours of breakdown, courtesy recovery to safe location.',
                completionCode: constants.COMPLETION_CODES.WITHIN_24_HRS_NEW_MBR_S_CHRG_DECLINED,
                remarksText: {
                    scenario: `24 Hour Exclusion(New Member-DANGEROUS LOCATION)`,
                    courtesyAccepted: `[NEW MEMBER - DANGLOC - MBR TO RB AT SAFLOC]`
                }
            },
            WITHIN_COOLING_OFF_PERIOD_VEHICLE: {
                header: 'VEHICLE CHANGE - WITHIN 24 HOURS: DANGEROUS LOCATION',
                description: `<p>We will begin by moving your vehicle to a safe location however the terms and conditions of your membership state Roadside Assistance is only available if you have purchased the cover at least 24 hours before the relevant breakdown.</p>
                <p>For further assistance to be provided upon arrival at a safe location you will need to pay a supplementary premium of &#163;75.00.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                callInfoText: 'Vehicle change within 24 hours of breakdown, courtesy recovery to safe location.',
                remarksText: {
                    scenario: `24 Hour Exclusion (VEHICLE CHANGE - DANGEROUS LOCATION)`,
                    courtesyAccepted: `[NEW UPGARDE (VEHICLE CHANGE) - DANGLOC - MBR TO RB AT SAFLOC]`
                }
            },
            WITHIN_COOLING_OFF_PERIOD_COMMERCIAL_USE: {
                header: 'NEW MEMBERSHIP: DANGEROUS LOCATION (Commercially Used Vehicle)',
                description: `<p>We will begin by moving your vehicle to a safe location however the terms and conditions of your membership state Commercial Use is only available if you have purchased the cover at least 24 hours before the relevant breakdown.</p>
                      <p>For further assistance to be provided upon arrival at a safe location you will need to pay a surcharge of &#163;75.00.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                callInfoText: 'CUV Addon purchased within 24 hours of breakdown, courtesy recovery to safe location.',
                completionCode: constants.COMPLETION_CODES.WITHIN_24_HRS_NEW_MBR_S_CHRG_DECLINED,
                remarksText: {
                    scenario: `24 Hour Exclusion(New Member-DANGEROUS LOCATION, Commercially Used Vehicle)`,
                    courtesyAccepted: `[COMMERCIAL USE - DANGLOC - MBR TO RB AT SAFLOC]`
                }
            }
        };
        return descriptions[descriptionName];
    };

    let withinCoolingOff = () => {
        let isOnValidation = false;
        isOnValidation = descriptionName && descriptionName.includes(constants.CONSTANT_BUILDER.ON_VALIDATION);

        let descriptions = {
            WITHIN_COOLING_OFF_PERIOD_RELAY: {
                header: 'NEW MEMBERSHIP: RELAY',
                description: isOnValidation
                    ? `<p>Our system has identified you have broken down within 24 hours of purchasing the Relay entitlement.</p>
                <p>For Relay (National Recovery) to be provided you will need to pay a surcharge.</p>
                <p>We will be performing a roadworthiness check prior to taking payment.</p>`
                    : `<p>The terms and conditions of your membership state National Recovery (Relay) is available only to those who have purchased Relay cover at least 24 hours before the relevant breakdown.</p>
                <p>For National Recovery (Relay) to be provided you will need to pay a surcharge of £75.00.</p>
                <p>We will take you and up to 7 passengers to a destination up to 15 miles away. If you want to be taken any further today there will be an additional charge of £4.50 per mile and this is non-refundable, for any future breakdowns we would recover you to any destination in the UK with no additional charge for mileage.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                completionCode: constants.COMPLETION_CODES.WITHIN_24_HRS_MTA_RELAY_UPGRADE_S_CHRG_DECLINED,
                callInfoText: {
                    serviceDeclined: 'Relay entitlement purchased within 24 hours of breakdown, Surcharge declined by member.',
                    paymentFailed: 'Relay falls in 24-hour exclusion and payment failed.'
                },
                payment: {
                    reason: constants.PAYMENT_REASONS.RELAY_SURCHARGE_24_HOUR_EXCLUSION,
                    amount: constants.SURCHARGE_AMOUNT.RELAY,
                    additionalMileageText: `<p>Please take 24hr exclusion surcharge of &#163;75.</p>
                    <p>Then...</p>
                    <p>Using recovery mileage (Mileage), calculate the additional mileage cost if over 50 miles.</p>`
                },
                remarksText: {
                    scenario: `Immediate Surcharge`,
                    paymentSuccessful: `[NEW UPGRADE (RELAY) - SURCHARGE TAKEN £${constants.SURCHARGE_AMOUNT.RELAY} - ENTITLED TO NR]`
                }
            },
            WITHIN_COOLING_OFF_PERIOD_HOMESTART: {
                header: 'NEW MEMBERSHIP: HOMESTART',
                description: isOnValidation
                    ? `<p>Our system has identified you have broken down within 24 hours of purchasing the Homestart entitlement.</p>
                <p>For At Home Assistance to be provided you will need to pay a surcharge.</p>
                <p>We will be performing a roadworthiness check prior to taking payment.</p>`
                    : `<p>The terms and conditions of your membership state At Home (Home Start) cover is available only to those who have purchased Home Start cover at least 24 hours before the relevant breakdown.</p>
                <p>For Homestart to be provided you will need to pay a surcharge of £75.00</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                completionCode: constants.COMPLETION_CODES.WITHIN_24_HRS_MTA_HS_UPGRADE_S_CHRG_DECLINED,
                callInfoText: {
                    serviceDeclined: 'Homestart entitlement purchased within 24 hours of breakdown, Surcharge declined by member.',
                    paymentFailed: 'Home start falls in 24-hour exclusion and payment failed.'
                },
                payment: {
                    reason: constants.PAYMENT_REASONS.HOMESTART_SURCHARGE_24_HOUR_EXCLUSION,
                    amount: constants.SURCHARGE_AMOUNT.HOMESTART
                },
                remarksText: {
                    scenario: `Immediate Surcharge`,
                    paymentSuccessful: `[NEW UPGRADE (HOMESTART) - SURCHARGE TAKEN £${constants.SURCHARGE_AMOUNT.HOMESTART} - ENTITLED TO HS]`
                }
            },
            WITHIN_COOLING_OFF_PERIOD_CUV: {
                header: 'NEW MEMBERSHIP:Commercially Used Vehicle',
                description: isOnValidation
                    ? `<p>Our system has identified you have broken down within 24 hours of purchasing the Commercial Vehicle entitlement.</p>
                <p>For Commercial Vehicle Assistance to be provided you will need to pay a surcharge.</p>
                <p>We will be performing a roadworthiness check prior to taking payment.</p>`
                    : `<p>Do you wish to proceed.</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                completionCode: constants.COMPLETION_CODES.WITHIN_24_HRS_NEW_MBR_S_CHRG_DECLINED,
                callInfoText: {
                    serviceDeclined: 'Commercial Vehicle Entitlement purchased within 24 hours of breakdown, Surcharge declined by member.',
                    paymentFailed: 'Commercial Vehicle Entitlement falls in 24-hour exclusion and payment failed.'
                },
                payment: {
                    reason: constants.PAYMENT_REASONS.CUV,
                    amount: constants.SURCHARGE_AMOUNT.CUV
                },
                remarksText: {
                    scenario: `Immediate Surcharge`,
                    paymentSuccessful: `[NEW MEMBER - SURCHARGE TAKEN £${constants.SURCHARGE_AMOUNT.CUV} - ENTITLED TO SERVICE]`
                }
            },
            WITHIN_COOLING_OFF_PERIOD_COMMERCIAL_USE: {
                header: 'NEW MEMBERSHIP: COMMERCIAL USE',
                description: isOnValidation
                    ? `<p>The terms and conditions of your membership state Commercial Use cover is available only to those who have purchased at least 24 hours before the relevant breakdown.</p>
                    <p>Confirm if the vehicle broken down is one listed on the membership.</p>
                    <p>If the breakdown is not related to a vehicle on the addon override this prompt</p>`
                    : `<p>The terms and conditions of your membership state, Commercial Use cover is available only to those who have purchased at least 24 hours before the relevant breakdown.</p><p>[Confirm if the vehicle broken down is one listed on the membership. If the breakdown is not related to a vehicle on the addon, override this prompt.]</p><p>For Commercial Use to be to be provided you will need to pay a surcharge of £75.00</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                completionCode: constants.COMPLETION_CODES.WITHIN_24_HRS_MTA_HS_UPGRADE_S_CHRG_DECLINED,
                callInfoText: {
                    serviceDeclined: 'Commercial Use entitlement purchased within 24 hours of breakdown, Surcharge declined by member.',
                    paymentFailed: 'Commercial Use falls in 24-hour exclusion and payment failed.'
                },
                payment: {},
                remarksText: {
                    scenario: `Immediate Surcharge`,
                    paymentSuccessful: `[NEW UPGRADE (COMMERCIAL USE) - SURCHARGE TAKEN £${constants.SURCHARGE_AMOUNT.HOMESTART} - ENTITLED TO HS]`
                }
            },
            WITHIN_COOLING_OFF_PERIOD_ROADSIDE: {
                header: 'NEW MEMBERSHIP',
                description: isOnValidation
                    ? `
                    <p>Our system has identified you have broken down within 24 hours of purchasing membership. For roadside assistance to be provided, you will need to pay £150 non-refundable surcharge for immediate usage.</p>
                    <p>We will be performing a roadworthiness check prior to taking payment to check the vehicle holds valid tax and MOT.</p>
                    <p><b>Read if has National Recovery</b> - As you have joined with National Recovery on your policy, if you need to go further than 15 miles today you will be charged for additional mileage at a rate of £4.50 per mile.</p>
                    <p><b>Read if has no National Recovery</b> - In the event the vehicle is found non-repairable, you will be covered to be taken to the nearest repairer. If needing to go further we can offer an upgrade on your policy.</p>
                    <p><b>Read If has Onward Travel</b> - Unfortunately Onward Travel is not yet active, so you will be unable to utilise this product on this occasion.</p>
                    `
                    : `<p>The terms and conditions of your membership state Roadside Assistance is only available if you have purchased the cover at least 24 hours before the relevant breakdown.</p>
                <p>For Roadside Assistance to be provided you will need to pay a non-refundable surcharge of &#163;150.00</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                completionCode: constants.COMPLETION_CODES.WITHIN_24_HRS_NEW_MBR_S_CHRG_DECLINED,
                callInfoText: {
                    serviceDeclined: 'Membership purchased within 24 hours of breakdown, Surcharge declined by member.',
                    paymentFailed: 'Membership falls in 24-hour exclusion and payment failed.'
                },
                payment: {
                    reason: constants.PAYMENT_REASONS.ENTITLEMENT_SURCHARGE_24_HOUR_EXCLUSION,
                    amount: constants.SURCHARGE_AMOUNT.ROADSIDE
                },
                remarksText: {
                    scenario: `Immediate Surcharge`,
                    paymentSuccessful: `[NEW MEMBER - SURCHARGE TAKEN £${constants.SURCHARGE_AMOUNT.ROADSIDE} - ENTITLED TO SERVICE]`
                }
            },
            WITHIN_COOLING_OFF_PERIOD_VEHICLE: {
                header: 'VEHICLE CHANGE - WITHIN 24 HOURS',
                description: isOnValidation
                    ? `<p>Our system has identified you have broken down within 24 hours of changing your vehicle details.</p>
                <p>For assistance to be provided you will need to pay a non-refundable surcharge.</p>
                <p>We will be performing a roadworthiness check prior to taking payment.</p>`
                    : `<p>The terms and conditions of your membership state Service will not be available for the new vehicle until 24 hours after the AA receives notification of the vehicle change.</p>
                <p>For immediate assistance to be provided you will need to pay a surcharge of &#163;75.00</p>`,
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.COOLING_OFF,
                completionCode: constants.COMPLETION_CODES.WITHIN_24_HRS_MTA_VEH_CHANGE_S_CHRG_DECLINED,
                callInfoText: {
                    serviceDeclined: 'Vehicle change within 24 hours of breakdown, Surcharge declined by member.',
                    paymentFailed: 'Vehicle change within 24 hours of breakdown, payment failed.'
                },
                payment: {
                    reason: constants.PAYMENT_REASONS.VEHICLE_CHANGE_SURCHARGE_24_HOUR_EXCLUSION,
                    amount: constants.SURCHARGE_AMOUNT.VEHICLE
                },
                remarksText: {
                    scenario: `Immediate Surcharge`,
                    paymentSuccessful: `[VEHICLE CHANGED WITHIN 24 HOURS - PREMIUM TAKEN £${constants.SURCHARGE_AMOUNT.VEHICLE} - ENTITLED TO SERVICE]`
                }
            }
        };

        if (isOnValidation) {
            descriptions.WITHIN_COOLING_OFF_PERIOD_RELAY_ON_VALIDATION = angular.copy(descriptions.WITHIN_COOLING_OFF_PERIOD_RELAY);
            descriptions.WITHIN_COOLING_OFF_PERIOD_CUV_ON_VALIDATION = angular.copy(descriptions.WITHIN_COOLING_OFF_PERIOD_CUV);
            descriptions.WITHIN_COOLING_OFF_PERIOD_COMMERCIAL_USE_ON_VALIDATION = angular.copy(descriptions.WITHIN_COOLING_OFF_PERIOD_COMMERCIAL_USE);
            descriptions.WITHIN_COOLING_OFF_PERIOD_HOMESTART_ON_VALIDATION = angular.copy(descriptions.WITHIN_COOLING_OFF_PERIOD_HOMESTART);
            descriptions.WITHIN_COOLING_OFF_PERIOD_ROADSIDE_ON_VALIDATION = angular.copy(descriptions.WITHIN_COOLING_OFF_PERIOD_ROADSIDE);
            descriptions.WITHIN_COOLING_OFF_PERIOD_VEHICLE_ON_VALIDATION = angular.copy(descriptions.WITHIN_COOLING_OFF_PERIOD_VEHICLE);
        }
        return descriptions[descriptionName];
    };

    let willJoin = () => {
        let header = `WILL JOIN - ${_.pullAll(descriptionName.split('_'), ['WILL', 'JOIN', 'OUTSIDE', 'OUTSIDE', 'WITHIN', 'GRACE']).join(' ')}`;
        const isMOT = header.includes('MOT');
        const isTAX = header.includes('TAX');
        const isSORN = header.includes('SORN');
        const isMOTandTAX = isMOT && isTAX;
        const isDangerousLocation = header.includes('DANGEROUS LOCATION');

        let dvlaCheck = `<p>We have checked the DVLA database and found your vehicle${isSORN ? ' is declared SORN.' : "'s "}`;
        dvlaCheck += `${isMOT ? 'MOT expired on ' + motExpiryDate : ''} ${isMOTandTAX ? 'and ' : ''}
            ${isTAX ? 'Tax expired on <strong>' + taxDueDate + '</strong>' : ''}.</p>`;

        let termsAndCondition = `<p>
            The terms and conditions of an AA membership state you must have a
                ${isMOT ? 'valid and current MOT test certificate [unless exempt]' : ''} ${isMOTandTAX ? 'and ' : ' '}
                ${isTAX ? 'valid Road Fund Licence(Tax) [unless exempt]' : ''}
                ${isSORN ? 'valid Road Fund Licence(Tax) [unless exempt]' : ''}
            .</p>`;

        let buyTAX = `<p>As your vehicle has no Tax we recommend you tax your vehicle and call back for assistance at your earliest convenience with the purchase reference number.</p>`;

        //Overrite buyTAX for below scenarios
        switch (descriptionName) {
            case 'NO_TAX_OUTSIDE_GRACE_SAFE_LOCATION_WILL_JOIN':
            case 'B2B_CUSTOMER_PAYS_NO_TAX_OUTSIDE_GRACE_SAFE_LOCATION_WILL_JOIN':
                termsAndCondition = `<p>The terms and conditions of your membership state you must have a valid Road Fund Licence (Tax) [unless exempt].</p>`;
                buyTAX = `<p>As your vehicle has no Tax, we recommend you tax your vehicle and call back for assistance once DVLA records are updated to reflect this.</p>
                <p>Service will be provided once DVLA records show that the vehicle is taxed.</p>
                <p>You can check tax status online at <span>gov.uk/check-vehicle-tax</span></p>
                <p>I also have to make you aware that should we need to use specialist equipment to assist in moving your vehicle, this will also be at your own expense.</p>`;
                break;

            case 'NO_TAX_OUTSIDE_GRACE_DANGEROUS_LOCATION_WILL_JOIN':
                buyTAX = `<p>As you are in a dangerous location we will recover you to a nearby place of safety.</p>
                <p>Once you and your vehicle are safe, we can only arrange an additional recovery for you, however this will be at your own expense. We will discuss this further when you are in a safe location.</p>`;
                break;

            case 'B2B_CUSTOMER_PAYS_NO_TAX_OUTSIDE_GRACE_DANGEROUS_LOCATION_WILL_JOIN':
                buyTAX = `<p>As your vehicle has no Tax, we can only arrange a recovery for you. This will be at your own expense, we will discuss this further when you are in a safe location.</p>`;
                break;
        }

        let recoveryDescription = `<p>As your vehicle has ${isSORN ? 'been declared SORN' : 'no '}`;
        recoveryDescription += `${isMOT ? 'MOT' : ''} ${isMOTandTAX ? 'or ' : ''}${isTAX ? 'Tax ' : ''}
                 we can arrange a recovery ${isDangerousLocation ? 'to a destination of your choice' : 'for you'}, this will be at your own expense.
                It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.
            </p>`;
        let callInfo = `Vehicle ${isMOT ? 'Has no MOT ' : ''}${isMOTandTAX ? ' and ' : ''}${isTAX ? 'Has no Tax ' : ''}${isSORN ? 'is declared SORN' : ''}`;
        let dangerousLocationText = `<p>We will endeavour to be with you as quickly as possible,
            but due to the additional requirements of this recovery type unfortunately there could be a higher probability of delay.</p>`;

        let descriptions = {
            WILL_JOIN: {
                header,
                description: {
                    descriptionText: `${dvlaCheck}${termsAndCondition}${!isMOTandTAX && isTAX ? buyTAX : recoveryDescription}`,
                    dangerousLocationText: `${dvlaCheck}${termsAndCondition}${recoveryDescription}${dangerousLocationText}`,
                    taxRecoveryDescription: !isMOTandTAX && isTAX ? recoveryDescription : ''
                },
                requiredOverrideOptions: constants.REQUIRED_OVERRIDE_REASONS.WILL_JOIN,
                callInfoText: {
                    pfuServiceDeclined: `${callInfo}, PFU service declined by member.`,
                    pfuQuoteDeclined: `${callInfo}, PFU quote declined by member.`,
                    paymentFailed: `${callInfo}, PFU payment unsuccessful.`,
                    refuseSrvice: `${callInfo}, will join declined by AA.`
                },
                remarksText: {
                    scenario: header,
                    paymentSuccessful: `[${isMOT ? 'NO MOT' : ''}${isMOTandTAX ? '/' : ''}${isTAX ? 'NO TAX' : ''}${isSORN ? 'VEHICLE SORN' : ''} - PFU FULL LIFT]`
                }
            }
        };

        return descriptions.WILL_JOIN;
    };

    return {
        safeLocation,
        dangerousLocation,
        withinCoolingOff,
        willJoin,
        excessiveUse,
        secondRecovery,
        repeatFault,
        vrn
    };
}
