var _ = require('lodash');

module.exports = getConfig;

function getConfig(task, actionsService, constants, $filter, promptType) {
    const {
        actionDispatcher,
        prepopulateCallInfo,
        closePrompt,
        addRemarks,
        transitionToState,
        setCoolingOffProperties,
        setCompletionCodeWrapper,
        checkFailedCoolingOff,
        roadworthinessPassed,
        resolvePromise,
        resetCoolingOffOverrides,
        getServiceAbuseRemarkBeforeSend,
        isRoadworthyCheckPossible,
        setRecoveryFlags
    } = actionsService;

    const { header, description, requiredOverrideOptions, callInfoText, completionCode, payment, remarksText } = require(`./descriptions.config`)(
        task,
        promptType,
        '',
        '',
        constants
    ).withinCoolingOff();

    const getDynamicConfig = () => {
        let isOnValidation = !!(promptType && promptType.includes(constants.CONSTANT_BUILDER.ON_VALIDATION));
        if (checkFailedCoolingOff() && !isOnValidation) {
            return (roadworthinessPassed() || !isRoadworthyCheckPossible(task)) && !task.vehicle().roadworthy().isSorn() ? onSendSteps : roadworthyFailedSteps;
        }

        return initialSteps;
    };

    const getOverrideOptions = () => {
        const getOverrideOptions = require('./override-options.config');
        if (task.contractValidation().coolingOff().product() === 'Relay') {
            if (requiredOverrideOptions.indexOf('WITHIN_COOLING_OFF_NEAREST_LOCAL_GARAGE_OUTSIDE_10_MILE_RADIUS') === -1) {
                requiredOverrideOptions.push('WITHIN_COOLING_OFF_NEAREST_LOCAL_GARAGE_OUTSIDE_10_MILE_RADIUS');
            }
        } else {
            if (requiredOverrideOptions.indexOf('WITHIN_COOLING_OFF_NEAREST_LOCAL_GARAGE_OUTSIDE_10_MILE_RADIUS') !== -1) {
                _.pull(requiredOverrideOptions, 'WITHIN_COOLING_OFF_NEAREST_LOCAL_GARAGE_OUTSIDE_10_MILE_RADIUS');
            }
        }
        return getOverrideOptions && getOverrideOptions(task, actionsService, constants, requiredOverrideOptions, remarksText.scenario);
    };

    const coolOffStepReset = () => {
        return (
            task.contractValidation().coolingOff().product().toUpperCase() === 'COMMERCIAL USE' ||
            task.contractValidation().coolingOff().product().toUpperCase() === 'HOMESTART' ||
            task.contractValidation().coolingOff().product().toUpperCase() === 'ROADSIDE' ||
            task.contractValidation().coolingOff().product().toUpperCase() === 'RELAY'
        );
    };

    let { RELAY } = constants.CONSTANT_BUILDER;

    const initialSteps = [
        {
            index: constants.STEP_INDEXES.START_INDEX,
            header,
            description,
            decision: {
                text: 'Do you wish to proceed?',
                options: [
                    {
                        name: 'Yes',
                        execute: actionDispatcher,
                        args: {
                            actions: { setCoolingOffProperties, closePrompt, resolvePromise },
                            properties: {
                                passed: false
                            }
                        }
                    },
                    {
                        name: 'No',
                        execute: actionDispatcher,
                        args: {
                            actions: {
                                setCoolingOffProperties,
                                setCompletionCodeWrapper,
                                prepopulateCallInfo
                            },
                            properties: {
                                surchargeAccepted: false,
                                passed: false
                            },
                            completionCode,
                            text: callInfoText.serviceDeclined
                        },
                        nextStep: constants.STEP_INDEXES.SERVICE_DECLINED
                    }
                ]
            }
        }
    ];

    const onSendSteps = [
        {
            index: constants.STEP_INDEXES.START_INDEX,
            header,
            description,
            decision: {
                text: 'Do you wish to proceed?',
                options: [
                    {
                        name: 'Yes',
                        execute: actionDispatcher,
                        args: {
                            actions: { transitionToState },
                            nextState: 'payment.credit-card',
                            paymentReasonId: payment.reason,
                            paymentAmount: payment.amount
                        },
                        nextStep: constants.STEP_INDEXES.WITHIN_COOLING_OFF_PFU_TAKE_PAYMENT
                    },
                    {
                        name: 'No',
                        execute: actionDispatcher,
                        args: {
                            actions: {
                                setCoolingOffProperties,
                                setCompletionCodeWrapper,
                                prepopulateCallInfo
                            },
                            properties: {
                                surchargeAccepted: false,
                                passed: false
                            },
                            completionCode,
                            text: callInfoText.serviceDeclined
                        },
                        nextStep: constants.STEP_INDEXES.SERVICE_DECLINED
                    },
                    {
                        name: 'Override',
                        execute: actionDispatcher,
                        args: {
                            actions: {
                                resetCoolingOffOverrides
                            },
                            scenario: remarksText.scenario
                        },
                        nextStep: constants.STEP_INDEXES.OVERRIDE_REASONS
                    }
                ]
            }
        },
        {
            index: constants.STEP_INDEXES.OVERRIDE_REASONS,
            header: 'Override Reasons',
            description: ``,
            overrideOptions: getOverrideOptions()
        }
    ];

    const roadworthyFailedSteps = [
        {
            index: coolOffStepReset() ? constants.STEP_INDEXES.START_INDEX : constants.STEP_INDEXES.NON_ROADWORTHY_WITHIN_COOLING_OFF,
            header,
            description,
            decision: {
                text: 'Do you wish to proceed?',
                options: [
                    {
                        name: 'Yes',
                        execute: actionDispatcher,
                        args: {
                            actions: { transitionToState },
                            nextState: 'payment.credit-card',
                            paymentReasonId: payment.reason,
                            paymentAmount: payment.amount
                        },
                        nextStep: constants.STEP_INDEXES.WITHIN_COOLING_OFF_PFU_TAKE_PAYMENT
                    },
                    {
                        name: 'No',
                        execute: actionDispatcher,
                        args: {
                            actions: {
                                setCoolingOffProperties,
                                setCompletionCodeWrapper,
                                prepopulateCallInfo
                            },
                            properties: {
                                surchargeAccepted: false,
                                passed: false
                            },
                            completionCode,
                            text: callInfoText.serviceDeclined
                        },
                        nextStep: constants.STEP_INDEXES.SERVICE_DECLINED
                    },
                    {
                        name: 'Override',
                        execute: actionDispatcher,
                        args: {
                            actions: {
                                resetCoolingOffOverrides
                            },
                            scenario: remarksText.scenario
                        },
                        nextStep: constants.STEP_INDEXES.COOLING_OFF_OVERRIDE_REASONS
                    }
                ]
            }
        },
        {
            index: constants.STEP_INDEXES.COOLING_OFF_OVERRIDE_REASONS,
            header: 'Override Reasons',
            description: ``,
            overrideOptions: getOverrideOptions()
        }
    ];

    const commonSteps = [
        {
            index: constants.STEP_INDEXES.SERVICE_DECLINED,
            header: 'Service Declined',
            description: `<p>Surcharge Declined by Member.</p><p>Please:</p>`,
            bulletPoints: ['Review and save call info', 'Complete the task'],
            navigationActions: [
                {
                    name: 'Close',
                    execute: actionDispatcher,
                    args: {
                        actions: {
                            closePrompt
                        }
                    }
                }
            ]
        },
        {
            index: constants.STEP_INDEXES.WITHIN_COOLING_OFF_PFU_TAKE_PAYMENT,
            header: 'Payment',
            description:
                task.contractValidation().coolingOff().product() && task.contractValidation().coolingOff().product().toUpperCase() === RELAY
                    ? payment.additionalMileageText
                    : payment.amount
                    ? `Please take payment of £${payment.amount} for ${remarksText.scenario}`
                    : `Please take payment for ${remarksText.scenario}`,
            navigationActions: [
                {
                    name: 'Payment success',
                    execute: actionDispatcher,
                    args: {
                        actions: { setCoolingOffProperties, setRecoveryFlags },
                        properties: {
                            surchargeAccepted: true
                        }
                    },
                    nextStep: constants.STEP_INDEXES.WITHIN_COOLING_OFF_PFU_PAYMENT_SUCCESSFUL
                },
                {
                    name: 'Payment failed',
                    execute: actionDispatcher,
                    args: {
                        actions: { setCompletionCodeWrapper, setCoolingOffProperties, prepopulateCallInfo },
                        completionCode: completionCode,
                        properties: {
                            surchargeAccepted: false
                        },
                        text: callInfoText.paymentFailed
                    },
                    nextStep: constants.STEP_INDEXES.WITHIN_COOLING_OFF_PFU_PAYMENT_FAILED
                }
            ]
        },
        {
            index: constants.STEP_INDEXES.WITHIN_COOLING_OFF_PFU_PAYMENT_SUCCESSFUL,
            header: 'Payment successful',
            description: `Payment successful. Please send the task.`,
            navigationActions: [
                {
                    name: 'Close',
                    execute: actionDispatcher,
                    args: {
                        actions: { addRemarks, closePrompt },
                        remarksText: getServiceAbuseRemarkBeforeSend() ? getServiceAbuseRemarkBeforeSend() + remarksText.paymentSuccessful : remarksText.paymentSuccessful
                    }
                }
            ]
        },
        {
            index: constants.STEP_INDEXES.WITHIN_COOLING_OFF_PFU_PAYMENT_FAILED,
            header: 'Payment failed',
            description: `Payment failure. Please update the call info and complete the task.`,
            navigationActions: [
                {
                    name: 'Close',
                    execute: actionDispatcher,
                    args: {
                        actions: { closePrompt }
                    }
                }
            ]
        }
    ];

    return {
        steps: [
            ...getDynamicConfig(),
            ...commonSteps,
            // this is a big hack - looks like roadworthy steps are missing for some scenarios
            // but to many external changes to rewrite it all
            ...roadworthyFailedSteps
        ]
    };
}
