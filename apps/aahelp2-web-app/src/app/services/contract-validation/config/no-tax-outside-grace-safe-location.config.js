var getDescriptionConfig = require('./descriptions.config');
var getPayForUseConfig = require('./pay-for-use.config');

module.exports = getConfig;

function getConfig(task, actionsService, constants, $filter, promptType) {
    let taxDueDate = $filter('date')(task.vehicle().roadworthy().taxDueDate(), 'dd/MM/yyyy');
    let motExpiryDate = $filter('date')(task.vehicle().roadworthy().motExpiryDate(), 'dd/MM/yyyy');
    let { actionDispatcher, setOutcomeProperties, setCompletionCodeWrapper, transitionToState, prepopulateCallInfo, setFLPOverride, closePrompt, getRelayToDestination, isRelayToDestinationSet } =
        actionsService;

    const descriptionConfig = getDescriptionConfig(task, promptType, taxDueDate, motExpiryDate, constants);
    const descriptionConfigSafeLocation = descriptionConfig.safeLocation(task);
    const { header, description, additionalContent, callInfoText, remarksText } = descriptionConfigSafeLocation;

    const _isPersonalCustomer = () => {
        return task.entitlement().customerGroup().isPersonal();
    };

    let config = {
        steps: [
            {
                index: constants.STEP_INDEXES.START_INDEX,
                header,
                description,
                decision: {
                    text: 'Is this ok?',
                    options: [
                        {
                            name: 'Yes',
                            execute: actionDispatcher,
                            args: {
                                actions: {
                                    prepopulateCallInfo,
                                    setOutcomeProperties,
                                    setCompletionCodeWrapper
                                },
                                completionCode: constants.COMPLETION_CODES.NO_TAX_MBR_CALL_WHEN_PURCH_OUT_GRACE_PERIOD,
                                properties: {
                                    serviceRefused: true
                                },
                                text: callInfoText.serviceAccepted
                            },
                            nextStep: constants.STEP_INDEXES.MEMBER_CALLS_BACK
                        },
                        {
                            name: 'No',
                            nextStep: constants.STEP_INDEXES.OFFER_PFU
                        },
                        {
                            name: 'Claim Tax',
                            nextStep: constants.STEP_INDEXES.CLAIM_TAX
                        },
                        {
                            name: 'FLP',
                            execute: actionDispatcher,
                            args: {
                                actions: {
                                    transitionToState
                                },
                                nextState: 'flp',
                                stateParams: {
                                    flpType: 63
                                }
                            },
                            nextStep: constants.STEP_INDEXES.NO_TAX_FLP
                        }
                    ]
                }
            },
            {
                index: constants.STEP_INDEXES.CLAIM_TAX,
                header,
                paymentRef: {
                    input: {
                        showPaymentRef: {
                            execute: true
                        }
                    }
                }
            },
            {
                index: constants.STEP_INDEXES.NO_TAX_FLP,
                header: 'FLP request',
                description: `<p>Proceed with FLP request.</p><p>Please:</p>`,
                associatedTaskId: true,
                bulletPoints: ['Contact CEC if authorisation required', 'Send job if FLP added'],
                navigationActions: [
                    {
                        name: 'Close',
                        execute: actionDispatcher,
                        args: {
                            actions: {
                                setOutcomeProperties,
                                setFLPOverride,
                                closePrompt
                            },
                            properties: {
                                pfuRequired: true
                            },
                            flpOverrideConfig: {
                                flpType: 63,
                                target: {
                                    model: 'roadworthiness',
                                    property: 'override'
                                }
                            }
                        }
                    }
                ]
            },
            {
                index: constants.STEP_INDEXES.MEMBER_CALLS_BACK,
                header: 'Invalid Tax',
                description: `<p>Member has declined and making own arrangements.</p><p>Please:</p>`,
                bulletPoints: ['Review and save call info', 'Complete the task'],
                navigationActions: [
                    {
                        name: 'Close',
                        execute: actionDispatcher,
                        args: {
                            actions: {
                                closePrompt
                            }
                        }
                    }
                ]
            },
            {
                index: constants.STEP_INDEXES.OFFER_PFU,
                header: 'Pay for Use (PFU)',
                description: `
                    <p>As your vehicle has no Tax we can arrange a recovery for you, this will be at your own expense.
                    It is a legal requirement for us to recover the vehicle with all wheels lifted off the ground and to deliver the vehicle to a location off the highway.</p>
                `,
                decision: {
                    text: 'Would you be interested in a quote?',
                    options: [
                        {
                            name: 'Yes',
                            execute: actionDispatcher,
                            args: {
                                nextState: 'location.relay.to',
                                actions: {
                                    transitionToState: {
                                        setOutcomeProperties
                                    }
                                },
                                properties: {
                                    pfuRequired: true
                                }
                            },
                            nextStep: constants.STEP_INDEXES.PFU_GENERATE_QUOTE
                        },
                        {
                            name: 'No',
                            execute: actionDispatcher,
                            args: {
                                actions: {
                                    setCompletionCodeWrapper,
                                    setOutcomeProperties,
                                    prepopulateCallInfo
                                },
                                completionCode: constants.COMPLETION_CODES.NO_TAX_PFU_DECLINED_QUOTE_NOT_PROVIDED,
                                properties: {
                                    pfuRequired: true,
                                    serviceRefused: true
                                },
                                text: callInfoText.pfuServiceDeclined
                            },
                            nextStep: constants.STEP_INDEXES.PFU_DECLINED
                        }
                    ]
                }
            },
            {
                index: constants.STEP_INDEXES.PFU_DECLINED,
                header: 'PFU declined',
                description: `<p>PFU service declined by member.</p><p>Please:</p>`,
                bulletPoints: ['Review and save call info', 'Complete the task'],
                navigationActions: [
                    {
                        name: 'Close',
                        execute: actionDispatcher,
                        args: {
                            actions: {
                                closePrompt
                            }
                        }
                    }
                ]
            },
            {
                index: constants.STEP_INDEXES.OVERRIDE,
                header: 'Override and offer assistance',
                description: 'Please enter the relay destination and send the recovery task.',
                dynamicDetails: {
                    input: {
                        iconClass: 'icon-path',
                        getText: {
                            execute: getRelayToDestination
                        },
                        isDone: {
                            execute: isRelayToDestinationSet
                        }
                    }
                },
                navigationActions: [
                    {
                        name: 'Close',
                        preCondition: {
                            execute: isRelayToDestinationSet
                        },
                        execute: closePrompt
                    }
                ]
            }
        ]
    };

    //Add more configuration steps from other config files
    const PFUConfig = getPayForUseConfig(
        task,
        actionsService,
        constants,
        constants.COMPLETION_CODES.NO_TAX_PFU_DECLINED_QUOTE_PROVIDED,
        constants.PAYMENT_REASONS.NO_TAX_PFU_FULL_LIFT,
        callInfoText,
        remarksText.paymentSuccessful
    );
    const PFUSteps = PFUConfig.steps;
    config.steps.push(...PFUSteps);

    if (!_isPersonalCustomer()) {
        config.steps.forEach((step) => {
            if (step.decision && step.decision.options) {
                step.decision.options = step.decision.options.filter((option) => option.name !== 'Claim Tax');
            }
        });
    }

    return config;
}
