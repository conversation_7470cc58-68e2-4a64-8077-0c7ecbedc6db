var getDescriptionConfig = require('./descriptions.config');
var getCourtesyServiceConfig = require('./courtesy-service.config');

module.exports = getConfig;

function getConfig(task, actionsService, constants, $filter, promptType) {
    let taxDueDate = $filter('date')(task.vehicle().roadworthy().taxDueDate(), 'dd/MM/yyyy');
    let motExpiryDate = $filter('date')(task.vehicle().roadworthy().motExpiryDate(), 'dd/MM/yyyy');
    let description;

    let { updateWorkFlowConfig } = actionsService;

    const descriptionConfig = getDescriptionConfig(task, promptType, taxDueDate, motExpiryDate, constants);
    const descriptionConfigSafeLocation = descriptionConfig.safeLocation();

    description = descriptionConfigSafeLocation.description ? descriptionConfigSafeLocation.description : '';

    const remarkstext = descriptionConfigSafeLocation.remarkstext ? descriptionConfigSafeLocation.remarkstext : '';

    description = `<p><strong>[CHECK TEXT TOPICS FOR SPECIFIC MOT/TAX/SORN INSTRUCTIONS BEFORE PROCEEDING]</strong></p> ${description}`;

    let { B2B_CUSTOMER_PAYS } = constants.CONSTANT_BUILDER;
    const configFileName = promptType.replace(B2B_CUSTOMER_PAYS, '');
    const getConfig = constants.CONFIG_FILES[configFileName];
    const config = getConfig(task, actionsService, constants, $filter, promptType);

    if (!task.entitlement().customerGroup().isFleet()) {
        config.steps[0].description = `<p><strong>[CHECK TEXT TOPICS FOR SPECIFIC MOT/TAX/SORN INSTRUCTIONS BEFORE PROCEEDING]</strong></p> ${config.steps[0].description}`;

        return config;
    }

    const courtesyServiceSteps = [constants.STEP_INDEXES.COURTESY_ACCEPTED, constants.STEP_INDEXES.COURTESY_DECLINED, constants.STEP_INDEXES.COURTESY_FULL_LIFT_TO_SAFE_LOCATION];

    const args = {
        updateWorkFlowConfigPreCondition: true,
        propsToUpdate: [
            {
                stepIndex: constants.STEP_INDEXES.START_INDEX,
                properties: [
                    {
                        propName: 'description',
                        value: description
                    }
                ]
            }
        ]
    };

    // If config does not have courtesy service already, then change the header property.
    const hasCourtesyCarService = config.steps.some((step) => {
        return courtesyServiceSteps.includes(step.index);
    });

    if (!hasCourtesyCarService) {
        args.propsToUpdate.push({
            stepIndex: constants.STEP_INDEXES.COURTESY_ACCEPTED,
            properties: [
                {
                    propName: 'header',
                    value: 'Service Accepted'
                }
            ]
        });
    }

    const courtesyCarConfig = getCourtesyServiceConfig(task, actionsService, constants, remarkstext.scenario ? remarkstext.scenario : '');
    const courtesyCarSteps = courtesyCarConfig.steps;
    config.steps.push(...courtesyCarSteps);

    // Update workflow based on config
    updateWorkFlowConfig(args, config);

    return config;
}
