var _ = require('lodash');
require('angular');
var HireSite = require('@aa/mobility-models/lib/hire-site.model');
var RAFVehicle = require('@aa/mobility-models/lib/raf-vehicle.model');
const { Vehicle } = require('@aa/data-models/common');
var RefId = require('@aa/malstrom-models/lib/ref-id.model');
var HireScheduleRequest = require('@aa/mobility-models-common/lib/hire-schedule-request.model');
var LatLong = require('@aa/malstrom-models/lib/lat-long.model');
var { v4 } = require('uuid');

const Moment = require('moment');

module.exports = angular
    .module('aah-mobility-retailer-service-module', [
        require('../../constants/search/search-urls.constants').name,
        require('../task/task.service').name,
        require('../mobility-task/mobility-task.service').name,
        require('../mapping/mapping.service').name,
        require('../alert/alert.service').name,
        require('../csh/csh.service').name,
        require('../../constants/task/mobility-task-urls.constants').name,
        require('../../constants/capabilities/capabilities.constants').name,
        require('../mapping/location/location.service').name,
        require('../mapping/google/google.service').name,
        require('../../factories/create-retailer.factory').name,
        require('../../constants/alert/alert-type.constants').name,
        require('../../constants/enterprise/enterprise-hire.constants').name,
        require('../../constants/hire-types/hire-types.constants').name
    ])
    .service('aahMobilityRetailerService', [
        '$http',
        '$q',
        'aahTaskService',
        'aahMobilityTaskService',
        'aahCSHService',
        'aahLocationService',
        'aahSearchURLs',
        'aahMobilityTaskURLs',
        'aahRetailersFactory',
        'aahCapabilitiesConstants',
        'aahMappingService',
        'aahAlertService',
        'aahGoogleService',
        'uibAlertTypes',
        'aahEnterpriseHireCar',
        'aahHireTypes',

        function MobilityRetailerService(
            $http,
            $q,
            TaskService,
            MobilityTaskService,
            CSHService,
            LocationService,
            SearchURLs,
            MobilityURLs,
            RetailerFactory,
            Capabilities,
            MappingService,
            AlertService,
            GoogleService,
            AlertTypes,
            EnterpriseHireCar,
            HireTypes
        ) {
            const _arrived = ['ARVD', 'GARR', 'HIRE'];
            const AA_SUPPLIERS = [RAFVehicle.SUPPLIER_TYPE_CODES.AA, RAFVehicle.SUPPLIER_TYPE_CODES.OU, RAFVehicle.SUPPLIER_TYPE_CODES.L460];
            let svc = this,
                _hireSites = [],
                _hireTypes = [],
                _rafVehicles = [],
                _retailer = null,
                _rafVehicle = null,
                _isMatchingStock = true,
                _retailerMarkers = [],
                _nearestRetailers = [],
                _nearestRetailersFromPickUp = [],
                _vehicleTypeFilter,
                _highlightedMarker = null,
                _selectedRetailer = null,
                _vehicleReturnDate = null,
                _selectedLocationType = 'RETAILER',
                _selectedMapLocation = null,
                _hireVehicleClass = null,
                _enterpriseBusinessRules = null;

            Date.prototype.addHours = function (h) {
                this.setTime(this.getTime() + h * 60 * 60 * 1000);
                return this;
            };

            function _moreThanXDays(date, nDays) {
                return date.getTime() > TaskService.task().schedule().create().getTime() + nDays * 24 * 60 * 60 * 1000;
            }

            function _processBookingTimeLogistics(rafVehicle) {
                let retVal = false;

                if (svc.getTask().appointment().earliest().getTime() <= new Date().addHours(4)) {
                    retVal = rafVehicle.status() === 'FREE';
                } else if (svc.getTask().appointment().earliest().getTime() > new Date().addHours(4) && svc.getTask().appointment().earliest().getTime() <= new Date().addHours(72)) {
                    retVal = rafVehicle.status() === 'FREE' || rafVehicle.status() === 'VALET';
                } else if (svc.getTask().appointment().earliest().getTime() > new Date().addHours(72)) {
                    AlertService.alert('Warning you are attempting to book a vehicle for more than 72 in the future', AlertTypes.DANGER);
                    retVal = false;
                }

                return retVal;
            }

            function _mapResourceToRetailer(resource) {
                let deferred = $q.defer(),
                    resourceId = resource.physical ? resource.managerId() : resource.id();

                let matchedRetailer = _.find(_nearestRetailers, (retailer) => {
                    return retailer.id() === resourceId;
                });
                if (!matchedRetailer) {
                    svc.getRetailerById(resourceId, resource.physical ? resource.location().coordinates() : resource.location()).then((retailer) => {
                        return svc.fetchRAFVehiclesByRetailer(retailer).then((rafVehicles) => {
                            if (rafVehicles.has(retailer.id())) {
                                let ouResources, rafResources, l460Resources;

                                ouResources = _.filter(rafVehicles.get(retailer.id()), (rafVehicle) => {
                                    return !!_.find(rafVehicle.capabilities, (capability) => {
                                        return capability.id === Capabilities.CAP_MANUAL_ALLOCATION.id;
                                    });
                                });
                                retailer.ouAvailability = ouResources.map((resource) => {
                                    return new RAFVehicle(resource);
                                });

                                rafResources = _.filter(rafVehicles.get(retailer.id()), (rafVehicle) => {
                                    return !_.find(rafVehicle.capabilities, (capability) => {
                                        return capability.id === Capabilities.CAP_MANUAL_ALLOCATION.id;
                                    });
                                });
                                retailer.rafAvailability = rafResources.map((resource) => {
                                    return new RAFVehicle(resource);
                                });

                                l460Resources = _.filter(rafVehicles.get(retailer.id()), (rafVehicle) => {
                                    return !!_.find(rafVehicle.capabilities, (capability) => {
                                        return capability.id === Capabilities.CAP_L460.id;
                                    });
                                });
                                retailer.l460Availability = l460Resources.map((resource) => {
                                    return new RAFVehicle(resource);
                                });
                            }
                            deferred.resolve(retailer);
                        });
                    });
                } else {
                    deferred.resolve();
                }

                return deferred.promise;
            }

            function _mapConditionedStockToARetailer(retailer, rafVehicle) {
                let retVal = false;
                retVal = svc.getTask().rental().hireVehicle().transmissionType() === rafVehicle.transmissionType() && rafVehicle.model().name() === svc.getTask().vehicle().model().name();
                //this needs to be added as capabilities to RAF vehicle model
                //_arrayContainsArray(rafVehicle.availableFeatures(), _carHireRetailersQuery.requestedFeatures());

                return retVal;
            }

            function _processRetailerToResourceMapping(retailer) {
                let matchingStock = _.filter(retailer.availability, (vehicle) => {
                    return _mapConditionedStockToARetailer(retailer, vehicle) && _processBookingTimeLogistics(vehicle);
                });
                retailer.matchingAvailability = matchingStock;
            }

            /**
             * Returns TRUE if the first specified array contains all elements
             * from the second one. FALSE otherwise.
             *
             * @param {array} superset
             * @param {array} subset
             *
             * @returns {boolean}
             */
            function _arrayContainsArray(superset, subset) {
                if (0 === subset.length) {
                    return false;
                }
                return subset.every(function (value) {
                    return superset.indexOf(value) >= 0;
                });
            }

            function _prepareCapabilities() {
                let hireVehicleCapabilities = _.filter(Capabilities, (capability) => {
                    return capability.name === svc.getTask().rental().hireVehicle().transmissionType();
                });
                _.forEach(hireVehicleCapabilities, (capability) => {
                    svc.getTask().fault().capabilities().push(new RefId(capability));
                });
                svc.getTask()
                    .fault()
                    .capabilities(
                        _.uniqWith(svc.getTask().fault().capabilities(), function (arrVal, othVal) {
                            return arrVal.id() === othVal.id();
                        })
                    );
            }

            function _processRetailers(response) {
                let retailers = response.data ? response.data : response;
                _.forEach(retailers, (poi) => {
                    let foundIndex = _.findIndex(_nearestRetailers, (retailer) => retailer.id() === poi.retailer.id);
                    if (foundIndex === -1) {
                        _nearestRetailers.push(new HireSite(poi.retailer));
                    } else {
                        let retailer = _nearestRetailers[foundIndex];
                        retailer.distanceFromSrc(poi.retailer.distanceFromSrc);
                        retailer.driveTimeFromSrc(poi.retailer.driveTimeFromSrc);
                    }
                });

                MappingService.retailers(_nearestRetailers);
            }

            /**
             * To check whether supplier profiles details are available in thirdpartyHire object
             * Used for getlocation
             * IF details are not available then pass taskID through search criteria
             */
            function _getSupplierDetails(defaultGetSearchCriteria) {
                let thirdPartyHire = MobilityTaskService.mobilityTask().rental().thirdPartyHire() || svc.getTask().rental().thirdPartyHire();
                if (thirdPartyHire && thirdPartyHire.supplier()) {
                    if (thirdPartyHire.supplier().armsOfficeId() && thirdPartyHire.supplier().armsProfileId()) {
                        defaultGetSearchCriteria.supplier = {
                            transactionId: MobilityTaskService.mobilityTask().id(),
                            tradingPartnerId: thirdPartyHire.supplier().armsProfileId(),
                            officeId: thirdPartyHire.supplier().armsOfficeId()
                        };
                    } else {
                        defaultGetSearchCriteria.taskId = svc.getTask().rental().srcRssTaskId();
                    }
                }
            }

            function formatEntOperationHoursToString(entLocations) {
                let openingTimes = '';
                for (let [key, value] of Object.entries(entLocations)) {
                    if (['sunday', 'saturday'].includes(key.toLowerCase())) {
                        openingTimes += ` ${key} ${value},`;
                    } else if (['monday'].includes(key.toLowerCase())) {
                        //TODO: Need the change in UI to show all day timing until then considering monday to friday would be same
                        openingTimes += ` Monday-Friday ${value},`;
                    }
                }
                return openingTimes.replace(/(^,)|(,$)/g, '');
            }

            /**
             * To check if booking has already done or not for enteteprise car hire
             */
            function _hasEnterpriseBooking() {
                return (
                    svc.getTask().isWaitingOnResource() &&
                    svc.getTask().rental().isThirdPartyHireSet() &&
                    svc.getTask().rental().thirdPartyHire().isEnterprise() &&
                    svc.getTask().rental().thirdPartyHire().hireVehicleRef()
                );
            }

            function _businessRulesRequest() {
                return {
                    customerReqId: svc.getTask().customerRequestId(),
                    source: 'aah2'
                };
            }

            function _hireVehicleClassRequest(rental) {
                if ((rental || svc.getTask().rental) && svc.getTask().vehicle) {
                    return {
                        hireSupplierCode: rental ? rental.hireVehicle.supplierTypeCode : svc.getTask().rental().hireVehicle().supplierTypeCode(),
                        custGroupCode: svc.getTask().entitlement().customerGroup().code(),
                        vehicleMakeId: svc.getTask().vehicle().makeId(),
                        vehicleModelId: svc.getTask().vehicle().modelId(),
                        transmissionType: svc.getTask().vehicle().experianDetails().transmission()
                    };
                }
                return false;
            }

            function _setSupplierType() {
                if (MobilityTaskService.isEnterprise()) {
                    return svc.isNonJLR() ? EnterpriseHireCar.LOCATION_TYPES.ENTERPRISE : _selectedLocationType;
                }
                return EnterpriseHireCar.LOCATION_TYPES.RETAILER;
            }

            _.extend(svc, {
                init: () => {
                    _vehicleTypeFilter = svc.getTask().rental().hireVehicle().supplierTypeCode();
                    _selectedMapLocation = null;
                },
                addEnterpriseLocation: function _addEnterpriseLocation(result, address, id, postCode) {
                    let latLonValues = {},
                        tempLocation = new HireSite(result),
                        thirdPartyLocations = [];

                    latLonValues.latitude = result.geometry.location.lat();
                    latLonValues.longitude = result.geometry.location.lng();

                    tempLocation.id(id ? id : v4());
                    tempLocation.address(address ? address : result.formatted_address);
                    tempLocation.location(new LatLong(latLonValues));
                    tempLocation.regionCode(postCode);
                    thirdPartyLocations.push(tempLocation);
                    svc.addThirdPartyLocations(thirdPartyLocations);
                },
                getTask: () => TaskService.task(),
                appointmentPeriod: () => {
                    // return the appointment period .. 2 hours for all BOLT hires and 96 for all others
                    return MobilityTaskService.getBusinessRules().agileEnabled ? 2 : 96;
                },
                updatePickupTime: () => {
                    // for completed show when vehicle was completed
                    if (TaskService.task().isCompleted() || _arrived.includes(TaskService.task().status())) {
                        return $q.resolve(TaskService.task().schedule().complete());
                    }

                    return MobilityTaskService.updatePickupTime();
                },
                threeOClockRuleWithoutHolidays: (targetDate) => {
                    return targetDate.getHours() >= 15 ? 1 : 0;
                },
                getContinousDates: (dateStr, days, rule) => {
                    const useRule = rule === undefined ? true : rule;
                    let dayCounter = 0;
                    let targetDate = new Date(dateStr);
                    // cover three o'clock rule
                    if (useRule) {
                        days = days + svc.threeOClockRuleWithoutHolidays(targetDate);
                    }

                    while (dayCounter < days) {
                        targetDate.setUTCDate(targetDate.getUTCDate() + 1);
                        // we count working days ...

                        dayCounter++;
                    }
                    //console.log(dayCounter);
                    return targetDate;
                },
                updateReturnDate: () => {
                    // for completed show when vehicle was completed
                    if (TaskService.task().isCompleted()) {
                        return $q.resolve(TaskService.task().schedule().complete());
                    }

                    if (_moreThanXDays(TaskService.task().appointment().earliest(), 3)) {
                        AlertService.createAlert('Warning, You are attempting to reserve a vehicle more than 3 days in the future', AlertTypes.DANGER);
                    }

                    // make copy of earlest time ..
                    TaskService.task().appointment().latest(new Date(TaskService.task().appointment().earliest()).addHours(svc.appointmentPeriod()));

                    let hireDaysType = EnterpriseHireCar.HIRE_DAYS_TYPES.WORKING_DAYS;
                    hireDaysType = _enterpriseBusinessRules.initialHireDaysType ? _enterpriseBusinessRules.initialHireDaysType : hireDaysType;

                    if (hireDaysType === EnterpriseHireCar.HIRE_DAYS_TYPES.DAYS) {
                        let endDate = svc.getContinousDates(
                            TaskService.task().appointment().latest(),
                            _enterpriseBusinessRules && _enterpriseBusinessRules.initialHireDays ? _enterpriseBusinessRules.initialHireDays : 1,
                            false
                        );
                        let repairMinutes = (endDate.getTime() - TaskService.task().appointment().latest().getTime()) / (60 * 1000);
                        TaskService.task().fault().repairMinutes(repairMinutes);
                        _vehicleReturnDate = endDate;
                        return $q.resolve(_vehicleReturnDate);
                    } else {
                        return $http
                            .post('/api/extension-service/dates/addDays', {
                                date: TaskService.task().appointment().latest(),
                                days: _enterpriseBusinessRules && _enterpriseBusinessRules.initialHireDays ? _enterpriseBusinessRules.initialHireDays : 1 // todo this will not work for pers ..
                            })
                            .then((response) => {
                                const endDate = new Date(response.data),
                                    repairMinutes = (endDate.getTime() - TaskService.task().appointment().latest().getTime()) / (60 * 1000);
                                TaskService.task().fault().repairMinutes(repairMinutes);
                                _vehicleReturnDate = endDate;
                            })
                            .catch((err) => {
                                _vehicleReturnDate = new Date(TaskService.task().appointment().latest().getTime() + 48 * 60 * 60 * 1000);
                            });
                    }
                },
                vehicleReturnDate: (...args) => {
                    if (args.length) {
                        _vehicleReturnDate = args[0];
                    }
                    if (MobilityTaskService.mobilityTask().isCompleted() || _arrived.includes(MobilityTaskService.mobilityTask().status())) {
                        _vehicleReturnDate = MobilityTaskService.mobilityTask().schedule().complete();
                    }
                    return _vehicleReturnDate;
                },
                retailerMarkers: (...args) => {
                    return args.length ? _retailerMarkers.push(args[0]) : _retailerMarkers;
                },
                getRetailerById: (managerId, location) => {
                    return $http
                        .post(SearchURLs.RETAILER_BY_ID, {
                            managerId: managerId,
                            point: {
                                lat: location.latitude(),
                                lng: location.longitude()
                            }
                        })
                        .then((response) => {
                            let matchedPoi,
                                matchExists = response.data.length ? response.data[0] : null;

                            if (matchExists) {
                                matchedPoi = new HireSite(matchExists.retailer);
                            }
                            return matchedPoi;
                        });
                },
                fetchNearestRetailers: (location) => {
                    //this is to pick the retailers close to the pick up location
                    let _location = _.has(svc.getTask().location(), 'coordinates') ? svc.getTask().location().coordinates() : svc.getTask().location();
                    svc.resetRetailers();

                    if (svc.selectedLocationType() === EnterpriseHireCar.LOCATION_TYPES.RETAILER || svc.vehicleTypeFilter() !== HireTypes.HIRE_TYPES.ENTERPRISE) {
                        return $http
                            .post(
                                SearchURLs.DEALER_SEARCH_URL,
                                {
                                    point: {
                                        lat: location.latitude(),
                                        lng: location.longitude()
                                    },
                                    custGroup: TaskService.task().entitlement().customerGroup().code(),
                                    baseLocation: {
                                        lat: _location.latitude(),
                                        lng: _location.longitude()
                                    }
                                },
                                {
                                    notify: true,
                                    friendly: 'retailer search'
                                }
                            )
                            .then((response) => {
                                _processRetailers(response);
                            });
                    } else {
                        if (svc.selectedLocationType() === EnterpriseHireCar.LOCATION_TYPES.ENTERPRISE) {
                            // TODO: Need to be initialised in model
                            const defaultGetSearchCriteria = {
                                searchCriteria: {
                                    countryCode: 'GBR',
                                    geographicCoordinates: {
                                        latitude: location.latitude() || 0,
                                        longitude: location.longitude() || 0
                                    }
                                },
                                searchParameters: {
                                    maxSearchDistance: 40,
                                    maxReturnedLocations: 4,
                                    rentalBusinessType: 'Car'
                                }
                            };
                            _getSupplierDetails(defaultGetSearchCriteria);
                            return $http.post(SearchURLs.ENTERPRISE_SEARCH_URL, defaultGetSearchCriteria).then((response) => {
                                let enterpriseLocations = [];
                                _.forEach(response.data, (poi) => {
                                    if (
                                        _.findIndex(enterpriseLocations, (enteprise) => {
                                            return enteprise.id === poi.locationDetails.locationId;
                                        }) === -1
                                    ) {
                                        let poiLocation = poi;
                                        if (poiLocation) {
                                            let address = poiLocation.locationDetails ? Object.values(poiLocation.locationDetails.address).join(' ') : poiLocation.locationDetails;
                                            let locationObject = {
                                                retailer: {
                                                    id: poiLocation.locationDetails.locationId,
                                                    name: poiLocation.locationDetails.locationName,
                                                    address: address,
                                                    telephone: poiLocation.locationDetails.telephoneNumber.number,
                                                    location: {
                                                        longitude: poiLocation.locationDetails.geographicCoordinates ? poiLocation.locationDetails.geographicCoordinates.longitude : null,
                                                        latitude: poiLocation.locationDetails.geographicCoordinates ? poiLocation.locationDetails.geographicCoordinates.latitude : null
                                                    },
                                                    openingTimes: poiLocation.hoursOfOperation ? formatEntOperationHoursToString(poiLocation.hoursOfOperation) : null,
                                                    distanceFromSrc: poiLocation.distanceAway,
                                                    isEnterpriseBranch: true
                                                }
                                            };
                                            enterpriseLocations.push(locationObject);
                                        }
                                    }
                                });
                                _processRetailers(enterpriseLocations);
                            });
                        }
                    }
                },
                fetchRetailersAndResources: (location, type) => {
                    const locationType = type || _setSupplierType();
                    const filterType = MobilityTaskService.isEnterprise() ? HireTypes.HIRE_TYPES.ENTERPRISE : svc.getTask().rental().hireVehicle().supplierTypeCode();
                    svc.selectedLocationType(locationType);
                    svc.vehicleTypeFilter(filterType);

                    if (svc.selectedLocationType() === EnterpriseHireCar.LOCATION_TYPES.OTHER) {
                        svc.resetRetailers();
                        svc.selectedMapLocation(location);
                        GoogleService.geocode({
                            location: new google.maps.LatLng(svc.selectedMapLocation().latitude(), svc.selectedMapLocation().longitude())
                        }).then(function geoCodePromiseResolved(results) {
                            let result = results[0];
                            if (result) {
                                svc.addEnterpriseLocation(result);
                            }
                        });
                    } else if (MobilityTaskService.isEnterprise()) {
                        return svc.fetchNearestRetailers(location).then(() => {
                            svc.addMissingPickupDropOff();
                            MappingService.moveToBounds();
                        });
                    } else {
                        return svc.fetchNearestRetailers(location).then(() => {
                            return svc.fetchRAFVehicles().then((rafVehicles) => {
                                _.forEach(_nearestRetailers, (retailer) => {
                                    retailer.marker = LocationService.addDepotMarker(retailer);
                                    if (rafVehicles && rafVehicles.has(retailer.id())) {
                                        let ouResources, rafResources, l460Resources;

                                        ouResources = _.filter(rafVehicles.get(retailer.id()), (rafVehicle) => {
                                            return !!_.find(rafVehicle.capabilities, (capability) => {
                                                return capability.id === Capabilities.CAP_MANUAL_ALLOCATION.id;
                                            });
                                        });
                                        retailer.ouAvailability = ouResources.map((resource) => {
                                            return new RAFVehicle(resource);
                                        });

                                        rafResources = _.filter(rafVehicles.get(retailer.id()), (rafVehicle) => {
                                            return !_.find(rafVehicle.capabilities, (capability) => {
                                                return capability.id === Capabilities.CAP_MANUAL_ALLOCATION.id;
                                            });
                                        });
                                        retailer.rafAvailability = rafResources.map((resource) => {
                                            return new RAFVehicle(resource);
                                        });

                                        l460Resources = _.filter(rafVehicles.get(retailer.id()), (rafVehicle) => {
                                            return !!_.find(rafVehicle.capabilities, (capability) => {
                                                return capability.id === Capabilities.CAP_L460.id;
                                            });
                                        });
                                        retailer.l460Availability = l460Resources.map((resource) => {
                                            return new RAFVehicle(resource);
                                        });
                                    }
                                });
                                svc.addMissingPickupDropOff();
                                MappingService.moveToBounds();
                            });
                        });
                    }
                },
                entepriseHomeLocation: () => {
                    let address;
                    svc.resetRetailers();
                    try {
                        address = CSHService.entitlement().contact().address().addressAsString();
                    } catch (e) {
                        address = TaskService.task().entitlement().memberAddress().join(',');
                    }
                    GoogleService.geocode({
                        address: address
                    }).then(function geoCodePromiseResolved(results) {
                        let result = results[0];
                        if (result) {
                            svc.addEnterpriseLocation(result);
                        }
                    });
                },
                addThirdPartyLocations: (thirdPartyLocations) => {
                    _.forEach(thirdPartyLocations, function (thirdPartyLocation) {
                        MappingService.calculateDistance(svc.getTask().location().coordinates().toJSON(), thirdPartyLocation.location().toJSON()).then((data) => {
                            if (data) {
                                thirdPartyLocation.distanceFromSrc((data.value * 0.00062137119223733).toFixed(1));
                            }
                            thirdPartyLocation.marker = LocationService.addDepotMarker(thirdPartyLocation);
                            _nearestRetailers.unshift(thirdPartyLocation);
                        });
                    });
                },
                addMissingPickupDropOff: () => {
                    if (svc.getTask().rental().collectLocation().isSet() && !_nearestRetailers.map((ret) => ret.id()).includes(svc.getTask().rental().collectLocation().id())) {
                        let tempLocation = svc.getTask().rental().collectLocation();
                        tempLocation.marker = LocationService.addDepotMarker(tempLocation);
                        _nearestRetailers.unshift(tempLocation);
                    }
                    if (svc.getTask().rental().dropOffLocation().isSet() && !_nearestRetailers.map((ret) => ret.id()).includes(svc.getTask().rental().dropOffLocation().id())) {
                        let tempLocation = svc.getTask().rental().dropOffLocation();
                        tempLocation.marker = LocationService.addDepotMarker(tempLocation);
                        _nearestRetailers.unshift(tempLocation);
                    }
                },
                initSubHire: () => {
                    return MobilityTaskService.initSubHire().then((res) => {
                        if (_enterpriseBusinessRules && _enterpriseBusinessRules.allowedCarCategory === 'L4L' && !MobilityTaskService.isPnC()) {
                            return svc.hireVehicleClass(res);
                        }
                        return res;
                    });
                },
                scheduleThirdPartyBooking: () => {
                    if (MobilityTaskService.isEnterprise()) {
                        let searchType = svc.getTask().rental().hireVehicle().supplierTypeCode().toLowerCase();
                        let hiringDays = Math.round(svc.getTask().fault().repairMinutes() / (24 * 60));

                        return MobilityTaskService.schedule(searchType, hiringDays);
                    } else {
                        return MobilityTaskService.schedule();
                    }
                },
                getMobilityExperianDetails: (parentTaskId) => {
                    /**
                     * TODO:: This is hot fix patch to fetch experian deatils for mobility task and send it to enterprise
                     * as prime does not store experian detils for Mobility Tasks.
                     * This should be removed once required data is stored in Mobility Task.
                     */
                    let experianDetails = MobilityTaskService.mobilityTask().vehicle().experianDetails();
                    let isExperianDetailsAvalailble = experianDetails && experianDetails.vin() && experianDetails.make() && experianDetails.yrOfManufacture() && experianDetails.model();
                    return (
                        !isExperianDetailsAvalailble &&
                        parentTaskId > 0 &&
                        TaskService.fetchTaskById(parentTaskId).then(function (fetchedTask) {
                            experianDetails = fetchedTask ? fetchedTask.vehicle().experianDetails() : null;
                            MobilityTaskService.mobilityTask().vehicle().experianDetails(experianDetails);
                        })
                    );
                },
                findMatchingCar: () => {
                    _prepareCapabilities();
                    return $q
                        .all([MobilityTaskService.schedule(HireScheduleRequest.SEARCH_TYPES.LOCATION), svc.fetchRetailersAndResources(svc.getTask().location().coordinates())])
                        .then((response) => {
                            let mobilityTaskWriteResponse = response[0];

                            _mapResourceToRetailer(mobilityTaskWriteResponse.schedule().resource()).then((primePreferredResource) => {
                                //this becomes the recommended vehicle when available.
                                if (primePreferredResource) {
                                    _nearestRetailers.unshift(primePreferredResource);
                                    primePreferredResource.marker = LocationService.addDepotMarker(primePreferredResource);
                                }
                            });
                        });
                },
                fetchRAFVehiclesByRetailer: (retailer = null) => {
                    if (!retailer) {
                        return;
                    }
                    return $http
                        .post(MobilityURLs.RAF_VEHICLES, {
                            retailers: _.compact([retailer.id()])
                        })
                        .then((response) => {
                            return new Map(response.data);
                        });
                },
                fetchRAFVehicles: () => {
                    let postData = [];

                    _.forEach(_nearestRetailers, (retailer) => {
                        postData.push(retailer.id());
                    });
                    if (postData.length === 0) {
                        return $q.resolve();
                    }
                    return $http
                        .post(MobilityURLs.RAF_VEHICLES, {
                            retailers: _.compact(postData)
                        })
                        .then((response) => {
                            _rafVehicles = [];
                            _rafVehicles = new Map(response.data);
                            return _rafVehicles;
                        });
                },
                rafVehicles: () => _rafVehicles,
                hireTypes: () => {
                    let isCCP = TaskService.task().entitlement().customerGroup().isCCP();
                    if (isCCP) {
                        return _hireTypes.filter((i) => ['RAF', 'OUV', 'ENTERPRISE'].indexOf(i.name) > -1);
                    }
                    // let isPorsche = TaskService.task().entitlement().customerGroup().isPorsche();
                    // let isHyundai = MobilityTaskService.enableHyundaiHire() && TaskService.task().entitlement().customerGroup().isHyundai();
                    // if(isPorsche || isHyundai){
                    //     var hireTypeName = isPorsche? 'PMF':'HAF';
                    //     if(_hireTypes.filter(i=>['RAF'].indexOf(i.name)>-1).length > 0){
                    //     _hireTypes.filter(i=>['RAF'].indexOf(i.name)>-1)[0].name = hireTypeName;
                    //     }
                    //     return _hireTypes.filter(i=>['AA','LCH','ENTERPRISE','PNC'].indexOf(i.code)>-1);
                    // }
                    return MobilityTaskService.getBusinessRules().hireFleetTypes;
                },
                hireTypesList: () => {
                    let supplierCode = RAFVehicle.SUPPLIER_TYPE_CODES;
                    let mappedSuppiler = { AA: 'RAF', OU: 'OUV', PNC: 'PAY AND CLAIM' };
                    _hireTypes = _.chain(supplierCode)
                        .map((value, key) => ({
                            id: value,
                            code: key,
                            name: mappedSuppiler[key] || key
                        }))
                        .sortBy(['name'])
                        .value();
                    _hireTypes.push(_hireTypes.splice(_.findIndex(_hireTypes, ['code', 'PNC']), 1)[0]); // This is to move PnC to last
                },

                isMatchingStock: (...args) => (args.length ? (_isMatchingStock = args[0]) : _isMatchingStock),
                nearestRetailers: () => _nearestRetailers,
                nearestRetailersFromPickUp: () => _nearestRetailersFromPickUp,
                updateLocationLogistics: () => {
                    if (MobilityTaskService.mobilityTask().rental().collectLocation().id()) {
                        let matchingRetailer = _.find(_nearestRetailers, (retailer) => {
                            return MobilityTaskService.mobilityTask().rental().collectLocation().id() === retailer.id();
                        });
                        if (matchingRetailer) {
                            matchingRetailer.isPickupLocation(true);
                            svc.updatePickupLocation(matchingRetailer);
                        }
                    }

                    if (MobilityTaskService.mobilityTask().rental().repairLocation().id()) {
                        let matchingRetailer = _.find(_nearestRetailers, (retailer) => {
                            return MobilityTaskService.mobilityTask().rental().repairLocation().id() === retailer.id();
                        });
                        if (matchingRetailer) {
                            matchingRetailer.isRepairingLocation(true);
                            svc.updateRepairLocation(matchingRetailer);
                        }
                    }

                    if (MobilityTaskService.mobilityTask().rental().dropOffLocation().id() || MobilityTaskService.mobilityTask().recovery().isDestResourceIdSet()) {
                        let matchingRetailer = _.find(_nearestRetailers, (retailer) => {
                            return (
                                MobilityTaskService.mobilityTask().rental().dropOffLocation().id() === retailer.id() || MobilityTaskService.mobilityTask().recovery().destResourceId() === retailer.id()
                            );
                        });
                        if (matchingRetailer) {
                            matchingRetailer.isReturnLocation(true);
                            svc.updateReturnLocation(matchingRetailer);
                        }
                    }
                },
                updatePickupLocation: (markedRetailer) => {
                    _.forEach(_nearestRetailers, (retailer) => {
                        if (markedRetailer.id() !== retailer.id()) {
                            retailer.isPickupLocation(false);
                            if (
                                MobilityTaskService.mobilityTask().rental().repairLocation().id() !== retailer.id() &&
                                MobilityTaskService.mobilityTask().rental().dropOffLocation().id() !== retailer.id()
                            ) {
                                if (retailer.marker) {
                                    retailer.marker.setMap(null);
                                }
                            }
                        }
                    });
                },
                updateReturnLocation: (markedRetailer) => {
                    _.forEach(_nearestRetailers, (retailer) => {
                        if (markedRetailer.id() !== retailer.id()) {
                            retailer.isReturnLocation(false);
                            if (
                                MobilityTaskService.mobilityTask().rental().repairLocation().id() !== retailer.id() &&
                                MobilityTaskService.mobilityTask().rental().collectLocation().id() !== retailer.id()
                            ) {
                                if (retailer.marker) {
                                    retailer.marker.setMap(null);
                                }
                            }
                        }
                    });
                },
                updateRepairLocation: (markedRetailer) => {
                    _.forEach(_nearestRetailers, (retailer) => {
                        if (markedRetailer.id() !== retailer.id()) {
                            retailer.isRepairingLocation(false);
                            if (
                                (MobilityTaskService.mobilityTask().recovery().destResourceId() !== retailer.id() ||
                                    MobilityTaskService.mobilityTask().rental().dropOffLocation().id() !== retailer.id()) &&
                                MobilityTaskService.mobilityTask().rental().collectLocation().id() !== retailer.id()
                            ) {
                                if (retailer.marker) {
                                    retailer.marker.setMap(null);
                                }
                            }
                        }
                    });
                },
                requestedFeatures: [new RefId(Capabilities.CAP_MOBILITY_7SEAT), new RefId(Capabilities.CAP_MOBILITY_4x4), new RefId(Capabilities.CAP_MOBILITY_TOWBAR)],
                tramisssionFeatures: {
                    manual: new RefId(Capabilities.CAP_MOBILITY_MANUAL),
                    automatic: new RefId(Capabilities.CAP_MOBILITY_AUTOMATIC)
                },
                showAll: (...args) => {
                    return args.length ? svc.vehicleTypeFilter(args[0]) : svc.vehicleTypeFilter();
                },
                vehicleTypeFilter: (...args) => {
                    if (args.length) {
                        if (args[0] === 'AA') {
                            svc.getTask().rental().hireVehicle().supplierTypeCode(args[0]);
                        }
                        _vehicleTypeFilter = args[0];
                    }
                    return _vehicleTypeFilter;
                },
                highlightMarker: (retailer) => {
                    return retailer ? (_highlightedMarker = retailer.id()) : _highlightedMarker;
                },
                clearOverlay: (retailerMarker) => {
                    let overlayToRemove = _.find(MappingService.map().overlays, (overlay) => {
                        if (overlay && overlay.options) {
                            return retailerMarker.location().latitude() === overlay.options.lat && retailerMarker.location().longitude() === overlay.options.lng;
                        }
                    });
                    let overlayToRemoveIndex = _.findIndex(MappingService.map().overlays, (overlay) => {
                        if (overlay && overlay.options) {
                            return retailerMarker.location().latitude() === overlay.options.lat && retailerMarker.location().longitude() === overlay.options.lng;
                        }
                    });
                    if (overlayToRemove) {
                        overlayToRemove.setMap(null);
                    }
                    if (overlayToRemoveIndex > -1) {
                        delete MappingService.map().overlays[overlayToRemoveIndex];
                    }
                },
                resetAllMarkers: () => {
                    _.forEach(_nearestRetailers, (retailer) => {
                        if (retailer.marker) {
                            retailer.marker.setMap(null);
                        }
                        svc.clearOverlay(retailer);
                    });
                    _nearestRetailers = [];
                },
                resetRetailers: () => {
                    let retailersToBeRemoved = _.remove(_nearestRetailers, (nearestRetailer) => {
                        return (
                            [
                                MobilityTaskService.mobilityTask().rental().collectLocation().id(),
                                MobilityTaskService.mobilityTask().rental().dropOffLocation().id(),
                                MobilityTaskService.mobilityTask().recovery().destResourceId(),
                                MobilityTaskService.mobilityTask().rental().repairLocation().id()
                            ].indexOf(nearestRetailer.id()) < 0
                        );
                    });
                    _.forEach(retailersToBeRemoved, (retailer) => {
                        if (retailer.marker) {
                            retailer.marker.setMap(null);
                        }
                        svc.clearOverlay(retailer);
                    });
                },
                resetRetailerMarkers: () => {
                    _.forEach(svc.nearestRetailers(), (retailer) => {
                        if (
                            [
                                MobilityTaskService.mobilityTask().rental().collectLocation().id(),
                                MobilityTaskService.mobilityTask().recovery().destResourceId(),
                                MobilityTaskService.mobilityTask().rental().repairLocation().id()
                            ].indexOf(retailer.id()) < 0
                        ) {
                            if (retailer.marker) {
                                retailer.marker.setMap(null);
                            }
                            svc.clearOverlay(retailer);
                        }
                    });
                },
                disableOptions: () => MobilityTaskService.disableOptions(),
                getSelectedMobilityOptions: () => {
                    let deferred = $q.defer(),
                        selectedMobilityOptions = [];

                    if (MobilityTaskService.mobilityTask().rental().dropOffLocation().isSet()) {
                        selectedMobilityOptions.push(MobilityTaskService.mobilityTask().rental().dropOffLocation());
                    }

                    if (
                        MobilityTaskService.mobilityTask().rental().repairLocation().isSet() &&
                        MobilityTaskService.mobilityTask().rental().repairLocation().id() !== MobilityTaskService.mobilityTask().rental().dropOffLocation().id()
                    ) {
                        selectedMobilityOptions.push(MobilityTaskService.mobilityTask().rental().repairLocation());
                    }
                    if (selectedMobilityOptions.length) {
                        deferred.resolve(
                            _.forEach(selectedMobilityOptions, (mobilityOption) => {
                                return _mapResourceToRetailer(mobilityOption).then((retailer) => {
                                    if (retailer) {
                                        _nearestRetailers.unshift(retailer);
                                        retailer.marker = LocationService.addDepotMarker(retailer);
                                    }
                                });
                            })
                        );
                    }
                    deferred.resolve(true);
                    return deferred.promise;
                },
                getResourceDetails: (retailer) => {
                    let availableResources = _rafVehicles.get(retailer.id());
                    return $http
                        .post('/api/mobility-task-service/supplierVehicles', {
                            retailerId: retailer.id(),
                            availableResources: availableResources
                        })
                        .then((rafVehicleDetails) => {
                            return rafVehicleDetails.data.map((resource) => {
                                let matchingResourceFromPrime = _.find(availableResources, (availableResource) => {
                                    return availableResource.id === resource.id;
                                });
                                resource.status = matchingResourceFromPrime.status;
                                return new RAFVehicle(resource);
                            });
                        });
                },
                isAppointmentInThePast: () => {
                    // give user 15 minutes grace between setting time and pressing schedule ..
                    let inThePast = false,
                        deferred = $q.defer();

                    if (MobilityTaskService.mobilityTask().status() === 'ARVD' || MobilityTaskService.mobilityTask().isCompleted() || MobilityTaskService.mobilityTask().status() === 'HEAD') {
                        deferred.resolve(true);
                    }
                    inThePast = Date.now() - MobilityTaskService.mobilityTask().appointment().earliest().getTime() > 15 * 60 * 1000;
                    if (inThePast) {
                        AlertService.createAlert('Warning appointment in the past - please reset earliest appointment time', AlertTypes.DANGER);
                        TaskService.task().appointment().earliest(new Date());
                        return svc.updateReturnDate().then(() => {
                            deferred.resolve(true);
                        });
                    } else {
                        deferred.resolve(true);
                    }
                    return deferred.promise;
                },
                selectedRetailer: (...args) => {
                    return args.length ? (_selectedRetailer = args[0]) : _selectedRetailer;
                },
                selectedLocationType: (...args) => {
                    return args.length ? (_selectedLocationType = args[0]) : _selectedLocationType;
                },
                selectedMapLocation: (...args) => {
                    return args.length ? (_selectedMapLocation = args[0]) : _selectedMapLocation;
                },
                searchRetailerByVehicleReg: (reg) => {
                    return $http.get('/api/mobility-task-service/resource/reg/' + reg).then((response) => {
                        return response.data;
                    });
                },
                fetchRetailerByVehicleReg: (reg) => {
                    return $http
                        .get('/api/mobility-task-service/resource/reg/' + reg)
                        .then((response) => {
                            let { hireSite, rafVehicle, resource } = response.data,
                                matchedRetailer;
                            if (!rafVehicle.id || !hireSite.id || !resource.id) {
                                AlertService.createAlert('vehicle search did not return any information, please check the vehicle reg', AlertTypes.WARNING);
                                return;
                            }

                            let { OU, L460, AA } = HireTypes.HIRE_TYPES;

                            svc.resetAllMarkers();
                            rafVehicle.capabilities = resource.capabilities;
                            rafVehicle.status = resource.status;
                            matchedRetailer = new HireSite(hireSite);
                            matchedRetailer.marker = LocationService.addDepotMarker(matchedRetailer);

                            if (
                                _.find(resource.capabilities, (capability) => {
                                    return capability.id === Capabilities.CAP_L460.id;
                                })
                            ) {
                                rafVehicle.supplierTypeCode = L460;
                                matchedRetailer.l460Availability = [new RAFVehicle(rafVehicle)];
                                svc.getTask().rental().hireVehicle().supplierTypeCode(L460);
                                svc.vehicleTypeFilter(L460);
                            } else if (
                                _.find(resource.capabilities, (capability) => {
                                    return capability.id === Capabilities.CAP_MANUAL_ALLOCATION.id;
                                })
                            ) {
                                rafVehicle.supplierTypeCode = OU;
                                matchedRetailer.ouAvailability = [new RAFVehicle(rafVehicle)];
                                svc.getTask().rental().hireVehicle().supplierTypeCode(OU);
                                svc.vehicleTypeFilter(OU);
                            } else {
                                rafVehicle.supplierTypeCode = AA;
                                matchedRetailer.rafAvailability = [new RAFVehicle(rafVehicle)];
                                svc.vehicleTypeFilter(AA);
                            }
                            if (
                                !_.find(_nearestRetailers, (retailer) => {
                                    return retailer.id() === matchedRetailer.id();
                                })
                            ) {
                                _nearestRetailers.push(matchedRetailer);
                            }
                            MappingService.moveToBounds();
                        })
                        .catch((error) => {});
                },
                resetRetailersFlag: () => {
                    _.forEach(_nearestRetailers, (retailer) => {
                        retailer.isPickupLocation(false);
                        retailer.isReturnLocation(false);
                        retailer.isRepairingLocation(false);
                        MobilityTaskService.mobilityTask().recovery().destResourceId(null);
                        if (retailer.marker) {
                            retailer.marker.setMap(null);
                        }
                        svc.clearOverlay(retailer);
                    });
                },
                enterpriseBusinessRules: () => {
                    // Need to move this call into mobility-task.service.js
                    return $http.post(SearchURLs.ENTERPRISE_BUSINESS_RULES, _businessRulesRequest()).then((resp) => {
                        _enterpriseBusinessRules = resp.data;
                        MobilityTaskService.setBusinessRules(_enterpriseBusinessRules);
                        return _enterpriseBusinessRules;
                    });
                },
                getEnterpriseBusinessRules: () => {
                    return _.isEmpty(_enterpriseBusinessRules) ? false : _enterpriseBusinessRules;
                },
                isEligibleForCarHire: () => {
                    /*
                    Don't allow Car hire if that customer is eligible for car hire.
                    Eligibility is defined in business rules.
                */
                    if (svc.getEnterpriseBusinessRules()) {
                        if (_enterpriseBusinessRules.carHireEligibility && !_enterpriseBusinessRules.carHireEligibility.status) {
                            AlertService.createAlert(`${_enterpriseBusinessRules.carHireEligibility.message}`, AlertTypes.DANGER);
                        }
                    }
                    return true;
                },
                hireVehicleClass: (initSubHireRes) => {
                    const rental = initSubHireRes.rental;
                    if (!_hireVehicleClassRequest(rental)) {
                        console.error('Hire Vehicle Class request failed due to insufficient params, No L4L hireclass has been set');
                        // bailout let the user select the vehicle class
                        return $q.resolve(rental);
                    }

                    return $http
                        .get(SearchURLs.HIRE_VEHICLE_CLASS, { params: _hireVehicleClassRequest() })
                        .then((resp) => {
                            console.log('hirevehicle class resp: ', resp);
                            const { hireVehicleClass, sameMake } = resp.data;
                            if (hireVehicleClass) {
                                svc.getTask().rental().thirdPartyHire().vehicleGroup(hireVehicleClass);
                                svc.getTask().rental().thirdPartyHire().sameMake(sameMake);
                            } else {
                                AlertService.createAlert('No like for like vehicle class found. Select manually.', AlertTypes.WARNING);
                            }

                            return initSubHireRes;
                        })
                        .catch((error) => {
                            AlertService.createAlert('Failed to get like for like vehicle class. Select manually.', AlertTypes.WARNING);
                            return $q.reject(Utils.handleError(error, 'Failed to get hire vehicle class for L4L'));
                        });
                },
                getHireVehicleClass: () => {
                    return _.isEmpty(_hireVehicleClass) ? false : _hireVehicleClass;
                },
                getNextWorkingDay: (date, rule) => {
                    return $http
                        .post(`/api/extension-service/dates/nextWorkingDay`, { date, rule })
                        .then((res) => {
                            return res.data;
                        })
                        .catch((error) => {
                            return $q.reject(Utils.handleError(error, 'Cannot calculate next working day.'));
                        });
                },
                getDriverAge: (dob) => {
                    let today = new Date();
                    let birthDate = new Date(dob);
                    let age = today.getFullYear() - birthDate.getFullYear();
                    let month = today.getMonth() - birthDate.getMonth();
                    if (month < 0 || (month === 0 && today.getDate() < birthDate.getDate())) {
                        age--;
                    }
                    return age;
                },
                hireTypeChangeValidation: () => {
                    const dateNow = new Date();
                    const def = $q.defer();

                    //Agile Enterprise: Dont allow to change supplier type if booking is already done
                    if (_hasEnterpriseBooking()) {
                        def.reject({
                            msg: 'Enterprise rental reservation is confirmed.  Changing the hire supplier is not allowed for this task'
                        });
                    }
                    if (TaskService.task().status() === 'UNAC') {
                        // don't bother ...
                        def.resolve();
                    }
                    // VALIDATE TIME ..time ..
                    dateNow.setMinutes(dateNow.getMinutes() - 30);
                    if (dateNow.getTime() < TaskService.task().appointment().earliest().getTime()) {
                        def.resolve();
                    } else {
                        def.reject({
                            msg: 'Please update vehicle collection appointment as is more than 30 minutes in the past'
                        });
                    }

                    return def.promise;
                },
                /**
                 * supplier change validation
                 * @param {string} supplierCodeType supplier we are changing too
                 * @returns {Object} outcome of the test
                 * @returns {boolean} outcome.invalid true if change is invalid otherwise false
                 * @returns {string} outcome.msg contain message to show to use if change is invalid
                 */
                supplierChangeValidation: (supplierCodeType) => {
                    const def = $q.defer();
                    let msg = null;

                    // we can't move from OU allocation to RAF - this is no longer true as we keep chaning time ?
                    // if (RAFVehicle.SUPPLIER_TYPE_CODES[supplierCodeType] === RAFVehicle.SUPPLIER_TYPE_CODES.AA &&
                    //     MobilityTaskService.isHireAssignedToOU()) {
                    //     msg = `Can not move from OU hire to ${supplierCodeType}. Please cancel this task and create another one`;
                    // }

                    // if we are on enterprise we can't move to another supplier
                    if (!msg && MobilityTaskService.isHireAssignedToEnterprise() && RAFVehicle.SUPPLIER_TYPE_CODES[supplierCodeType] !== RAFVehicle.SUPPLIER_TYPE_CODES.ENTERPRISE) {
                        msg = `You can not move from Enterprise to ${supplierCodeType}. Please cancel this hire and create another one`;
                    }

                    // if msg is set we then reject the promise as there is an issue ..
                    if (msg) {
                        def.reject({ msg });
                    } else {
                        def.resolve();
                    }

                    return def.promise;
                },
                // return thrid party raf vehicle return emtpy object if no thridPartyHire is set
                thirdPartySupplierVehicle: () => {
                    return TaskService.task().rental().thirdPartyHire() ? TaskService.task().rental().thirdPartyHire().experianDetails() : new Vehicle();
                },
                setWorkingDays: () => {
                    let deferred = null;
                    if (_enterpriseBusinessRules) {
                        let hiringDuration = EnterpriseHireCar.HIRING_DURATION.DEFAULT;
                        let hireDaysType = EnterpriseHireCar.HIRE_DAYS_TYPES.DAYS;
                        const vehicleRequiredOn = svc.getTask().appointment().earliest();

                        hireDaysType = _enterpriseBusinessRules.initialHireDaysType ? _enterpriseBusinessRules.initialHireDaysType : hireDaysType;
                        hiringDuration = _enterpriseBusinessRules.initialHireDays ? _enterpriseBusinessRules.initialHireDays : hiringDuration;

                        // vehicle will wait customer for 4 days ...
                        svc.getTask().appointment().latest(Moment(vehicleRequiredOn).add(svc.appointmentPeriod(), 'h').toDate());

                        if (hireDaysType === EnterpriseHireCar.HIRE_DAYS_TYPES.WORKING_DAYS) {
                            return svc.getNextWorkingDay(vehicleRequiredOn, hiringDuration).then((resp) => {
                                const returnDate = new Date(resp);
                                hiringDuration = Math.round((returnDate.getTime() - vehicleRequiredOn.getTime()) / (60 * 1000));
                                svc.getTask().fault().repairMinutes(hiringDuration);
                            });
                        } else {
                            deferred = $q.defer();
                            hiringDuration = hiringDuration * 24 * 60; // hire duration is in mimutes
                            svc.getTask().fault().repairMinutes(hiringDuration);
                            deferred.resolve();

                            return deferred.promise;
                        }
                    }
                    deferred = $q.defer(); // so all the paths return a promise
                    deferred.resolve();
                    return deferred.promise;
                },
                fetchLocations: (locationType) => {
                    let deferred = $q.defer();
                    svc.selectedLocationType(locationType);
                    switch (locationType) {
                        case EnterpriseHireCar.LOCATION_TYPES.RETAILER:
                        case EnterpriseHireCar.LOCATION_TYPES.ENTERPRISE:
                            const location = svc.selectedMapLocation() ? svc.selectedMapLocation() : svc.getTask().location().coordinates();

                            return svc.fetchRetailersAndResources(location, locationType);

                        case EnterpriseHireCar.LOCATION_TYPES.HOME:
                            return svc.entepriseHomeLocation();

                        case EnterpriseHireCar.LOCATION_TYPES.OTHER:
                            svc.resetRetailers();
                            break;

                        default:
                            //task type unknown
                            break;
                    }
                    deferred.resolve(true);
                    return deferred.promise;
                },
                getSelectedAddress: (addrObject) => {
                    const address = addrObject.addressAsString();
                    const postCode = addrObject.postcode();

                    //Created manual id for selected location. If you use uuid lib then it will add same address multiple times.
                    const id = address ? address.split(' ').join('') : null;
                    svc.resetRetailers();
                    if (svc.isAddressAlreadySelected(id)) {
                        return;
                    }

                    GoogleService.geocode({
                        address
                    }).then(function geoCodePromiseResolved(results) {
                        let result = results[0];
                        if (result) {
                            svc.addEnterpriseLocation(result, address, id, postCode);
                        }
                    });
                },
                isAddressAlreadySelected: (id) => {
                    return [
                        MobilityTaskService.mobilityTask().rental().collectLocation().id(),
                        MobilityTaskService.mobilityTask().rental().dropOffLocation().id(),
                        MobilityTaskService.mobilityTask().recovery().destResourceId(),
                        MobilityTaskService.mobilityTask().rental().repairLocation().id()
                    ].includes(id);
                },
                addManualAddress: (data, postCode) => {
                    let locations = [];
                    if (svc.isAddressAlreadySelected(data)) {
                        return;
                    }
                    let tempLocation = new HireSite({
                        id: data.split(' ').join(''), //id is created based on address to avoid duplication of records tobe added
                        address: data,
                        regionCode: postCode
                    });
                    locations.push(tempLocation);
                    svc.addThirdPartyLocations(locations);
                },
                enterpriseDealers: () => {
                    return MobilityTaskService.isEnterprise() ? _.get(_enterpriseBusinessRules, 'dealers', []) : [];
                },
                entepriseProfiles: () => {
                    return MobilityTaskService.isEnterprise() ? _.get(_enterpriseBusinessRules, 'armsProfiles', []) : [];
                },
                isNonJLR: () => {
                    let entitlement = svc.getTask().entitlement();
                    let isHyundai = MobilityTaskService.enableHyundaiHire() && entitlement.customerGroup().isHyundai();
                    if (MobilityTaskService.enableCCPHire()) {
                        return !entitlement.customerGroup().isAllJLR() && !entitlement.customerGroup().isPorsche() && !isHyundai;
                    } else {
                        return !entitlement.customerGroup().isJLR() && !entitlement.customerGroup().isPorsche() && !isHyundai;
                    }
                },
                isThirdPartyVehicleInHire: (vehicleReg) => {
                    return $http
                        .get('/api/mobility-task-service/getOnHireThirdPartyVehicles/' + vehicleReg)
                        .then((response) => {
                            return response;
                        })
                        .catch((error) => {
                            return undefined;
                        });
                }
            });
        }
    ]);
