var _ = require('lodash'),
    Prompt = require('@aa/malstrom-models/lib/prompt.model'),
    PromptOption = require('@aa/malstrom-models/lib/prompt-option.model');
require('angular');

module.exports = angular
    .module('aah-static-prompt-service-module', [
        //constants
        require('../../constants/prompt/prompt-urls.constants').name,
        require('../../constants/contract-validation/contract-validation.constants').name,
        require('../../constants/completion/cancellation-reason.constants').name
    ])
    .service('aahStaticPromptService', [
        '$http',
        'aahPromptURLs',
        'aahContractValidationConstants',
        'aahCancellationReasons',
        function StaticPromptService($http, URLs, ContractValidationConstants, CancellationReasons) {
            const promptDefinitions = [
                {
                    id: Prompt.PERSONAL_NOT_ENTITLED_FOR_RELAY,
                    name: 'Not Entitled to National Recovery',
                    htmlFile: 'personal-no-relay.html',
                    ignoreOnce: true,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Continue anyway'
                        }
                    ]
                },
                {
                    id: Prompt.FLP_PROMPT,
                    name: 'Flp service',
                    htmlFile: 'flp-prompt.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes',
                            state: 'flp'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No',
                            linkPrompt: Prompt.ENTITLEMENTS_EXPIRED
                        }
                    ]
                },
                {
                    id: Prompt.ENTITLEMENTS_EXPIRED,
                    name: 'Service Adjustment',
                    htmlFile: 'service-adjustment-personal-membership-expired.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept service',
                            state: 'payment'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: '08'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            linkPrompt: Prompt.FLP_PROMPT
                        }
                    ]
                },
                {
                    id: Prompt.LOCAL_RECOVERY,
                    name: 'Local recovery distance',
                    htmlFile: 'local-recovery.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No',
                            state: 'flp'
                        }
                    ]
                },
                {
                    id: Prompt.LOCAL_RECOVERY_DISTANCE_EXCEEDED,
                    name: 'Local Recovery Distance Exceeded',
                    htmlFile: 'local-recovery-distance-exceeded.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'No',
                            linkPrompt: Prompt.SERVICE_ADJUSTMENT_RELAY
                        }
                    ]
                },
                {
                    id: Prompt.ROADSIDE_ADDON_LOCAL_RECOVERY_DISTANCE_EXCEEDED,
                    name: 'Roadside Add-on National Recovery Charge',
                    htmlFile: 'roadside-addon-local-recovery-distance-exceeded.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept Service',
                            state: 'payment.credit-card',
                            stateProps: {
                                paymentReasonId: ContractValidationConstants.PAYMENT_REASONS.ROADSIDE_ADDON_PFU
                            }
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: '13'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.TASK_COMPLETED_WITHIN_SEVEN_DAYS,
                    name: 'There is a task completed within seven days',
                    htmlFile: 'task-completed-within-seven-days.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.TASK_COMPLETED_WITHIN_SEVEN_DAYS_REJECTED_FLP,
                    name: 'Task completed within seven days had rejected FLP',
                    htmlFile: 'task-completed-within-seven-days-rejected-flp.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.LOCAL_RECOVERY_WARN,
                    name: 'Local recovery only',
                    htmlFile: 'local-recovery-warn.html',
                    ignoreOnce: true,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes',
                            state: 'location.relay'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No',
                            state: 'location.mapping'
                        }
                    ]
                },
                {
                    id: Prompt.UPGRADE_CUV,
                    name: 'UPGRADE CUV',
                    htmlFile: 'cuv-upgrade-prompt.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Upgrade',
                            linkPrompt: Prompt.UPGRADE_SUCCESSFUL
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp',
                            stateProps: {
                                flpType: 62
                            }
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: 'YI'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.NOT_ENTITLED_TO_HOMESTART,
                    name: 'Not Entitled to At Home Cover',
                    htmlFile: 'not-entitled-to-homestart.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Upgrade',
                            linkPrompt: Prompt.UPGRADE_SUCCESSFUL
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp',
                            stateProps: {
                                flpType: 55
                            }
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: '13'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.ROADSIDE_ADDON_NOT_ENTITLED_TO_HOMESTART,
                    name: 'Roadside Add-on No Home Start Entitlement',
                    htmlFile: 'roadside-addon-not-entitled-to-homestart.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept Service',
                            state: 'payment.credit-card',
                            stateProps: {
                                paymentReasonId: ContractValidationConstants.PAYMENT_REASONS.ROADSIDE_ADDON_PFU,
                                paymentAmount: 75
                            }
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: '13'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.ROADSIDE_ADDON_EXCESSIVE_USE_DANGEROUS_LOCATION,
                    name: 'RoadSide Insurance Add-on Call out limit reached: Dangerous location',
                    htmlFile: 'roadside-addon-excessive-use-dangerous-location.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.ROADSIDE_ADDON_EXCESSIVE_USE_NOTICE,
                    name: 'RoadSide Insurance Add-on Call out limit reached',
                    htmlFile: 'roadside-addon-excessive-use-notice.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No',
                            state: 'service-declined',
                            value: '13'
                        }
                    ]
                },
                {
                    id: Prompt.ROADSIDE_ADDON_EXCESSIVE_USE_SAFE_LOCATION,
                    name: 'RoadSide Insurance Add-on Call out Charge',
                    htmlFile: 'roadside-addon-excessive-use-safe-location.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept Service',
                            state: 'payment.credit-card',
                            stateProps: {
                                paymentReasonId: ContractValidationConstants.PAYMENT_REASONS.ROADSIDE_ADDON_PFU,
                                paymentAmount: 75
                            }
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: '13'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.SAGA_NOT_ENTITLED_TO_HOMESTART,
                    name: 'Not Entitled to Homestart',
                    htmlFile: 'saga-not-entitled-to-homestart.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Upgrade',
                            linkPrompt: Prompt.UPGRADE_SUCCESSFUL
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: '13'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.HOMESTART_UPGRADE_GOLD_MEMBER_OVER_10_YEARS,
                    name: 'Gold Member Discount',
                    htmlFile: 'homestart-upgrade-gold-member-over-10-years.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept Service',
                            linkPrompt: Prompt.UPGRADE_SUCCESSFUL
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: '13'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.HOMESTART_UPGRADE_GOLD_MEMBER_5_TO_10_YEARS,
                    name: 'Gold Member Discount',
                    htmlFile: 'homestart-upgrade-gold-member-5-10-years.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept Service',
                            linkPrompt: Prompt.UPGRADE_SUCCESSFUL
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: '13'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.SERVICE_ADJUSTMENT_RELAY,
                    name: 'Service Adjustment',
                    htmlFile: 'service-adjustment-relay.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept Service',
                            linkPrompt: Prompt.UPGRADE_SUCCESSFUL
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline Service',
                            state: 'completion',
                            stateProps: {
                                completionReasonCode: CancellationReasons.codes.RECOVERY_UPGRADE_DECLINED
                            }
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp',
                            stateProps: {
                                flpType: 56
                            }
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.REFUSE_RELAY_SERVICE,
                    name: 'Refuse Relay Service',
                    htmlFile: 'refuse-relay-service.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.VEHICLE_HAS_ADDITIONAL_POLICIES,
                    name: 'Vehicle has additional policies',
                    htmlFile: 'vehicle-has-additional-policies.html',
                    ignoreOnce: true,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.NO_ENTITLEMENTS_FOUND,
                    name: 'No entitlements found',
                    htmlFile: 'no-entitlements-found.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.VEHICLE_CHANGED_PROMPT,
                    name: 'Vehicle changed',
                    htmlFile: 'vehicle-changed.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.VEHICLE_CHANGED_CREATE_NEW_TASK,
                    name: 'Create a New Task with New Entitlement',
                    htmlFile: 'vehicle-changed-create-new-task.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.NEW_MEMBERSHIP,
                    name: 'New Membership',
                    htmlFile: 'new-membership.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.DATA_CHANGED,
                    name: 'Data changed',
                    htmlFile: 'data-changed.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK',
                            state: 'home'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.CASE_HAS_NEW_TASK,
                    name: 'Case has data not saved',
                    htmlFile: 'case-data-not-saved.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK',
                            state: 'home'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.INCIDENT_INDICATOR,
                    name: 'Incident indicator',
                    htmlFile: 'incident-indicator.html',
                    ignoreOnce: true,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Clear',
                            linkPrompt: Prompt.CLEAR_INCIDENT_INDICATOR
                        }
                    ]
                },
                {
                    id: Prompt.CLEAR_INCIDENT_INDICATOR,
                    name: 'Incident Indicator Cleared',
                    htmlFile: 'clear-incident-indicator.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.UPGRADE_SUCCESSFUL,
                    name: 'Upgrade',
                    htmlFile: 'upgrade-successful.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No',
                            state: PromptOption.PAYMENT_STATE
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.TASK_OUT_OF_SEQUENCE,
                    name: 'Task out of sequence',
                    htmlFile: 'task-out-of-sequence.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.MISSING_MEMBER_DETAILS,
                    name: 'Missing member details',
                    htmlFile: 'missing-member-details.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok',
                            linkPrompt: Prompt.FLP_PROMPT
                        }
                    ]
                },
                {
                    id: Prompt.PFU_SCRIPT,
                    name: 'Pay for use script',
                    htmlFile: 'pay-for-use-script.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.NO_RESULTS_FOUND,
                    name: 'No results found',
                    htmlFile: 'no-results-found.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.BATTERY_ASSIST_WARRANTY,
                    name: 'Risk code use',
                    htmlFile: 'battery-assist-warranty.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.INVALID_LOCATION,
                    name: 'Invalid location',
                    htmlFile: 'invalid-location.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.INACCURATE_LOCATION,
                    name: 'Inaccurate location',
                    htmlFile: 'inaccurate-location.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.SERVICE_ADJUSTMENT_RELAY_PLUS,
                    name: 'Service Adjustment',
                    htmlFile: 'service-adjustment-relay-plus.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept service',
                            state: 'payment'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'completion',
                            stateProps: {
                                completionReasonCode: CancellationReasons.codes.RECOVERY_UPGRADE_DECLINED
                            }
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            linkPrompt: Prompt.FLP_PROMPT
                        }
                    ]
                },
                {
                    id: Prompt.CLI_ENTITLEMENT,
                    name: 'Candidate Entitlements',
                    htmlFile: 'cli-entitlement.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Close'
                        }
                    ]
                },
                {
                    id: Prompt.AGENT_LOGGED_OFF,
                    name: 'Agent Logged Off',
                    htmlFile: 'agent-logged-off.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.NO_REFUSE_LBG_CUSTOMERS,
                    name: 'Customer Not With Vehicle',
                    htmlFile: 'no-customer-with-vehicle-refuse-lbg-customers.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.CUSTOMER_NOT_WITH_VEHICLE_CHAS,
                    name: 'Customer Not With Vehicle',
                    htmlFile: 'no-customer-with-vehicle-refuse-chas-customers.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.SCHEDULE_WINDOW_OPEN,
                    name: 'Open Schedule window for Repairer',
                    htmlFile: 'schedule-window-open.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes',
                            state: 'appointment'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.CHANGE_INSURANCE_OPTION,
                    name: 'Insurance option changed',
                    htmlFile: 'change-insurance-option.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.DUAL_COVER_CHECK,
                    name: 'Dual Cover checks',
                    htmlFile: 'dual-cover-check.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.SAVE_TASK_WITHOUT_CONTACT_NUMBER,
                    name: 'Save task without contact number',
                    htmlFile: 'save-task-without-contact-number.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.ADD_CASE_ON_A_LIVE_BREAKDOWN,
                    name: 'There is a live breakdown for this membership',
                    htmlFile: 'add-case-on-a-live-breakdown.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.CHECK_BANK_CUSTOMER_HAS_UPGRADE,
                    name: 'Check if customer has purchased an Upgrade',
                    htmlFile: 'check-if-bank-customer-purchased-an-upgrade.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.TASK_OVERRIDE,
                    name: 'Task Override',
                    htmlFile: 'task-override.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.VIP_INDICATOR,
                    name: 'VIP Customer',
                    htmlFile: 'vip-indicator.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.VEHICLE_GROSS_WEIGHT_OVER_LIMIT,
                    name: 'Vehicle Gross Weight is over 3.5T',
                    htmlFile: 'vehicle-gross-weight.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.ADMIRAL_VEHICLE_GROSS_WEIGHT_OVER_LIMIT,
                    name: 'Vehicle Gross Weight is over 4.2T',
                    htmlFile: 'vehicle-admiral-gross-weight.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.ROI,
                    name: 'ROI',
                    htmlFile: 'roi-location.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.CORRESPONDENCE_ADDRESS,
                    name: 'Correspondence Address Invalid',
                    htmlFile: 'correspondence-address-invalid.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'SMS'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Update TIA/CATHIE'
                        }
                    ]
                },
                {
                    id: Prompt.CORRESPONDENCE_ADDRESS_JOINT_MEMBER,
                    name: 'Correspondence Address Invalid',
                    htmlFile: 'correspondence-address-invalid-joint-member.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.WILL_JOIN_TASK,
                    name: 'Digital Job - Policy Not Located',
                    htmlFile: 'will-join.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Policy Found'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Will Join'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel',
                            state: 'completion'
                        }
                    ]
                },
                {
                    id: Prompt.HONDA_ASSISTANCE_PROMPT,
                    name: 'Honda Assistance Task',
                    htmlFile: 'honda-assistance-task.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.CORRESPONDENCE_ADDRESS_VEHICLE_BASED_MEMBER,
                    name: 'Correspondence Address Invalid',
                    htmlFile: 'correspondence-address-vehicle-based.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.CORRESPONDENCE_ADDRESS_JOINT_MEMBER, // ANk JOINT menmber prompt
                    name: 'Correspondence Address Invalid',
                    htmlFile: 'correspondence-address-invalid-joint-member.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.TASK_MERGE,
                    name: 'Task Merged',
                    htmlFile: 'task-merge.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.DANGEROUS_LOCATION_ENTRY_JUNCTION,
                    name: 'Dangerous location',
                    htmlFile: 'dangerous-location.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.ADMIRAL_PREVENT_HOMESTART_PROMPT,
                    name: 'Not Entitled to At Home Cover',
                    htmlFile: 'admiral-prevent-homestart-prompt.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'AA/AVA Policy',
                            state: 'service-declined',
                            value: 'ET'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Non Validation',
                            state: 'service-declined',
                            value: 'EV'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel'
                        }
                    ]
                },
                {
                    id: Prompt.ADMIRAL_PREVENT_RECOVERY_PROMPT,
                    name: 'Not entitled to Local Recovery',
                    htmlFile: 'admiral-prevent-recovery-prompt.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.CHECK_MILEAGE_EXISTS,
                    name: 'Mileage is required',
                    htmlFile: 'check-if-mileage-exists.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.LOCAL_DRIVER_PROMPT,
                    name: 'Local Driver Prompt',
                    htmlFile: 'local-driver-prompt.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept service',
                            state: 'payment'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: 'SF'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            linkPrompt: Prompt.FLP_PROMPT
                        }
                    ]
                },
                {
                    id: Prompt.STANDBY_EARLY_PROMPT,
                    name: 'Standby Prompt',
                    htmlFile: 'standby-early-prompt.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                },
                {
                    id: Prompt.CALLOUT_HISTORY_PROMPT,
                    name: 'Call out history Prompt',
                    htmlFile: 'callout-history-prompt.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: 'EU'
                        }
                    ]
                },
                {
                    id: Prompt.CALLOUT_HISTORY_LATTER_PROMPT,
                    name: 'Call out history Prompt',
                    htmlFile: 'callout-history-latter-prompt.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept service',
                            state: 'payment'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline service',
                            state: 'service-declined',
                            value: 'EU'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            linkPrompt: Prompt.FLP_PROMPT
                        }
                    ]
                },
                {
                    id: Prompt.NO_SLOT_ACCEPTABLE,
                    name: 'No Slot Acceptable',
                    htmlFile: 'no-slot-acceptable-prompt.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'No'
                        }
                    ]
                },
                {
                    id: Prompt.NO_REFUSE_NBS_CUSTOMERS,
                    name: 'Authorised Driver (Customer not with Vehicle) and **Accident Assist**',
                    htmlFile: 'no-customer-with-vehicle-refuse-nbs-customers.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.ELECTRIC_VEHICLE_DIAGNOSTIC,
                    name: 'This is an Electric Vehicle',
                    htmlFile: 'electic-vehicle-diagnostic.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.NBS_GROUP_VEHICLE_GROSS_WEIGHT_OVER_LIMIT,
                    name: 'NATIONWIDE - Vehicle overweight ',
                    htmlFile: 'nbs-group-vehicle-gross-weight.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.NO_REFUSE_NWG_CUSTOMERS,
                    name: 'Customer Not With Vehicle and **Accident Assist**',
                    htmlFile: 'no-customer-with-vehicle-refuse-nwg-customers.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.NO_REFUSE_RBS_CUSTOMERS,
                    name: 'Customer Not With Vehicle and **Accident Assist**',
                    htmlFile: 'no-customer-with-vehicle-refuse-rbs-customers.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.ADMIRAL_LOCAL_RECOVERY_DISTANCE_EXCEEDED,
                    name: 'Local Recovery Distance Exceeded',
                    htmlFile: 'local-recovery-distance-exceeded.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Yes'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'No',
                            linkPrompt: Prompt.ADMIRAL_SERVICE_ADJUSTMENT_RELAY
                        }
                    ]
                },
                {
                    id: Prompt.ADMIRAL_SERVICE_ADJUSTMENT_RELAY,
                    name: 'Service Adjustment',
                    htmlFile: 'service-adjustment-relay.html',
                    ignoreOnce: false,
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Accept Service',
                            state: 'payment'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Decline Service',
                            state: 'completion',
                            stateProps: {
                                completionReasonCode: CancellationReasons.codes.RECOVERY_UPGRADE_DECLINED
                            }
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Vulnerable Caller/FLP',
                            state: 'flp'
                        },
                        {
                            optionType: PromptOption.OPTION_TYPE_NO,
                            name: 'Cancel',
                            state: 'location.relay'
                        }
                    ]
                },
                {
                    id: Prompt.COMPASS_NO_AUTHORIZATION,
                    name: 'Permissions Error',
                    htmlFile: 'compass-no-authorization.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.SPECIALIST_VEH,
                    name: 'Specialist Vehicle',
                    htmlFile: 'specialistveh.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.MEMBERSHIP_SEARCH_NOTFOUND,
                    name: 'No Entitlements Found',
                    htmlFile: 'membership-search.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.CONFLICTING_RECORDS_FOUND,
                    name: 'Conflicting records found',
                    htmlFile: 'conflicting-records-found.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.INVALID_VEHICLE_VRN,
                    name: 'Invalid Vehicle Registration',
                    htmlFile: 'invalid-vrn.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'Ok'
                        }
                    ]
                },
                {
                    id: Prompt.SUSPENDED_POLICY,
                    name: 'Policy Suspended',
                    htmlFile: 'membership-suspended.html',
                    options: [
                        {
                            optionType: PromptOption.OPTION_TYPE_YES,
                            name: 'OK'
                        }
                    ]
                }
            ];
            var svc = this,
                _staticPrompts = {};

            // map static prompts by id
            for (const promptDefinition of Object.values(promptDefinitions)) {
                _staticPrompts[promptDefinition.id] = promptDefinition;
            }

            function getStaticPromptById(id) {
                var prompt = _staticPrompts ? _staticPrompts[id] : null;
                return prompt ? new Prompt(prompt) : null;
            }

            function getStaticPrompts(val) {
                return arguments.length ? (_staticPrompts = val) : _staticPrompts;
            }

            _.extend(svc, {
                getStaticPromptById: getStaticPromptById,
                getStaticPrompts: getStaticPrompts
            });
        }
    ]);
