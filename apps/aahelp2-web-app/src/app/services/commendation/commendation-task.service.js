const { Address } = require('@aa/data-models/common');
require('angular');
var _ = require('lodash'),
    CommendationTask = require('@aa/malstrom-models/lib/commendation-task.model');
module.exports = angular.module('aah-commendation-task-service-module', [require('../../constants/task/task-urls.constants').name]).service('aahCommendationTaskService', [
    '$http',
    'aahTaskURLs',
    function CommendationTaskService($http, TaskURLs) {
        var svc = this;

        _.extend(svc, {
            save: function save(commendationTask) {
                return $http.post(TaskURLs.COMMENDATION_TASK, { commendationTask: commendationTask.toJSON() });
            },
            newCommendationTask: function newCommendationTask(task) {
                var address = new Address(),
                    commendationTask = new CommendationTask();
                _.forEach(task.entitlement().memberAddress(), function forEachAddressLine(addressLine) {
                    address.addressLines().push(addressLine);
                });
                commendationTask.customerRequestId(task.customerRequestId());
                commendationTask.address(address);
                commendationTask.schedule().create(new Date());
                return commendationTask;
            }
        });
    }
]);
