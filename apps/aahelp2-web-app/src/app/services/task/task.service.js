/* jshint expr: true */
var _ = require('lodash');
require('angular');
var Prompt = require('@aa/malstrom-models/lib/prompt.model');
var RefId = require('@aa/malstrom-models/lib/ref-id.model');

// models
const { Entitlement } = require('@aa/data-models/aux/entitlement');
var Task = require('@aa/malstrom-models/lib/task.model');
var MobilityTask = require('@aa/mobility-models-common/lib/mobility-task.model');
var LatLong = require('@aa/malstrom-models/lib/lat-long.model');
var FinancialTask = require('@aa/malstrom-models/lib/financial-task.model');
var Fault = require('@aa/malstrom-models/lib/fault.model');
var TaskWriteResponse = require('@aa/malstrom-models/lib/task-write-response.model');
var CreateReason = require('@aa/malstrom-models/lib/create-reason.model');
var AlertStatus = require('@aa/malstrom-models/lib/alert-status.model');
var CustomerRequestQuery = require('@aa/malstrom-models/lib/customer-request-query.model');

var AAHelpTaskFactory = require('@aa/mobility-models-common/lib/factories/aahelp-task.factory');
var TransportTask = require('@aa/malstrom-models/lib/transport-task.model');
var HotelTask = require('@aa/malstrom-models/lib/hotel-task.model');
var FlpTask = require('@aa/malstrom-models/lib/flp-task.model');
var CommendationTask = require('@aa/malstrom-models/lib/commendation-task.model');
var UpdateTask = require('@aa/malstrom-models/lib/update-task.model');
var SplitTaskResponse = require('@aa/malstrom-models/lib/split-task-response.model');
var ArchivedTask = require('@aa/malstrom-models/lib/archived-task.model');
var SEUpdateTask = require('@aa/malstrom-models/lib/se-update-task.model');
var Contact = require('@aa/malstrom-models/lib/contact.model');

module.exports = angular
    .module('aah-task-service-module', [
        //constants
        require('../../constants/task/task-urls.constants').name,
        require('../../constants/task/task-type.constants').name,
        require('../../constants/callinfo/callinfo-types.constants').name,
        require('../../constants/bridge-response/bridge-response-messages.constants').name,
        require('../../constants/alert/alert-type.constants').name,
        require('../../constants/recovery/dest-resource-ids.constants').name,
        require('../../constants/flexible-fields/flexible-fields.constants').name,
        require('../../constants/capabilities/capabilities.constants').name,

        // services
        require('../validation/validation.service').name,
        require('../appointment/appointment.service').name,
        require('../recovery/recovery.service').name,
        require('../prompt/prompt.service').name,
        require('../payment/payment.service').name,
        require('../garage/garage.service').name,
        require('../partial-task-update/partial-task-update.service').name,
        require('../../services/text-topic/text-topic.service').name,
        require('../../services/flp/flp-task.service').name,
        require('../service-type/service-type.service').name,
        require('../sms-service/sms.service').name,
        require('../../factories/sms/sms.factory').name,
        require('../multa-ank-alerts-status/multa-ank-alert-status.service').name,
        require('../limited-membership/limited-membership.service').name,
        require('../csh/csh.service').name,

        //helpers
        require('../../services/tracking-link/tracking-link.service').name,
        require('../../services/qualification/qualification-workflow.service').name,
        require('../../services/csh-search-viewer/csh-search-viewer.service').name,
        require('../alert/alert.service').name,
        require('../../services/cti/cisco-cti-event.service').name
    ])
    .service('aahTaskService', [
        '$http',
        '$q',
        '$rootScope',
        '$injector',
        '$state',
        'aahTaskURLs',
        'aahBridgeResponseMessages',
        'aahValidationService',
        'aahAppointmentService',
        'aahRecoveryService',
        'aahPromptService',
        'aahPaymentService',
        'aahFlpTaskService',
        'aahTextTopicService',
        'aahGarageService',
        'aahPartialTaskUpdateService',
        'aahTaskTypeConstants',
        'aahCallinfoTypes',
        'aahTrackingLinkService',
        'aahQualificationWorkflowService',
        'aahCSHSearchViewerService',
        'aahServiceTypeService',
        'aahDestResourceIDsConstants',
        'aahFlexibleFieldsConstants',
        'aahAlertService',
        'uibAlertTypes',
        'aahSMSService',
        'aahSMSFactory',
        'aahMultaANKAlertStatusService',
        'aahLimitedMembershipService',
        'aahCSHService',
        'aahCiscoCtiEventService',
        'aahContractValidationService',
        'aahCapabilitiesConstants',
        'aahVehicleService',
        function TaskService(
            $http,
            $q,
            $rootScope,
            $injector,
            $state,
            URLs,
            BridgeResponseMessages,
            ValidationService,
            AppointmentService,
            RecoveryService,
            PromptService,
            PaymentService,
            FlpTaskService,
            TextTopicService,
            GarageService,
            PartialTaskUpdateService,
            TaskType,
            CallinfoType,
            TrackingLinkService,
            QualificationWorkflowService,
            CSHSearchViewerService,
            ServiceTypeService,
            DestResourceIDsConstants,
            flexibleFieldsConstants,
            AlertService,
            AlertTypes,
            SMSService,
            SMSFactory,
            MultaANKAlertStatusService,
            LimitedMembershipService,
            cshService,
            CiscoCtiEventService,
            ContractValidationService,
            Capabilities,
            VehicleService
        ) {
            let svc = this,
                _task = new Task(),
                _tasks = {},
                _assistanceData,
                _swapCarTasks = [],
                _drm = '',
                _drmVisible = false,
                _pendingAdhocTasks = [],
                _nearestEntryJunction = {}, // keep track of nearest entry and marker post ..
                _nearestMotorwayMarkerPost = {},
                _appTask,
                _attendedScheduleWindow = false,
                _overrideTaskDetail = null,
                _completionCodes = [],
                _driveIn = false,
                _storage = false,
                _repair = false,
                _abandonment = false,
                _goodWill = false,
                _isRepairerChanged = false,
                _saveTaskOptions = null,
                _serviceAbuseRemark = '',
                _isTaskMerged = false,
                _isNewlyCreated = false,
                _fairPlayLimit = '26';
            let hasPreExistingRecovery = false;
            let preExistingRecoveryTaskId = -1;

            const _reopenReattend = (url, reason, srcTask, entitlement) => {
                return $http
                    .post(url, {
                        entitlement: entitlement.toJSON(),
                        reason: reason.toJSON(),
                        task: srcTask.toJSON()
                    })
                    .then(function onSuccessReattend(resp) {
                        _drm = '';
                        const task = AAHelpTaskFactory.create(resp.data.task);
                        if (task.isNew() && task.rental && task.rental() && task.rental().srcRentalTaskId() > 0) {
                            //this is to reset all drivers for values in conditions like swap hire task
                            task.rental()
                                .additionalDrivers()
                                .forEach((driver) => {
                                    driver.softReset();
                                });
                        }
                        const vrn = task.vehicle().registration();
                        if (vrn && VehicleService.validateVRN(vrn)) {
                            task.contractValidation().vrn().passed(true);
                        }
                        return {
                            entitlement: new Entitlement(resp.data.entitlement),
                            task
                        };
                    });
            };

            const getLocationObjectByState = (stateName) => {
                let location = _task.location();
                switch (stateName) {
                    case 'location.accommodation':
                        location = _task.hotel().hotelLocation();
                        break;
                    case 'location.transportPickup':
                        location = _task.transport().pickUpLocation();
                        break;
                    case 'location.transportDropoff':
                        location = _task.transport().dropOffLocation();
                        break;
                    default:
                        location = _task.location();
                }
                return location;
            };

            $rootScope.$watch(
                () => _drm,
                (newVal, oldVal) => {
                    if (newVal === '') {
                        _drmVisible = false;
                    }
                }
            );

            RecoveryService.setTaskService(svc);

            function toDrmMessage(resp) {
                _drmVisible = true;
                if (typeof resp === 'string') {
                    _drm = resp;
                    return _drm;
                }

                if (!resp) {
                    _drm = 'failed transaction';
                    return _drm;
                }

                if (resp.primeMsg) {
                    _drm = resp.primeMsg;
                    return _drm;
                }

                if (resp.errMsg) {
                    _drm = resp.errMsg;
                    return _drm;
                }

                _drm = 'failed transaction';
                return _drm;
            }

            /**
             * process task write response. If prime update has failed this method resets the sequenceNo
             * @param  {TaskWriteResponse} responseModel [description]
             * @param  {Task} [task] - the saved task
             * @param {Object} options - extra info
             * @return {boolean} true if prime has processed successfully the request
             */
            function processTaskWriteResponse(responseModel, task, options) {
                //TODO - AH - we need to send initialFault as part of the response
                var taskToUpdate = task ? task : _task;
                switch (responseModel.result()) {
                    case 0:
                        taskToUpdate.sequence(responseModel.sequence());
                        taskToUpdate.status(responseModel.status());
                        taskToUpdate.schedule(responseModel.schedule());
                        taskToUpdate.appointment(responseModel.appointment());
                        AppointmentService.availability(responseModel.availability());
                        taskToUpdate.jobNoToday(responseModel.jobNoToday());
                        taskToUpdate.indicators(responseModel.indicators());
                        taskToUpdate.fault().capabilities(responseModel.capabilities());
                        taskToUpdate.priorities(responseModel.priorities());
                        if (responseModel.primeMsg()) {
                            _drm = responseModel.primeMsg();
                            _drmVisible = true;
                        }
                        taskToUpdate.isDirty(false);
                        break;
                    case -2:
                        PromptService.taskOutOfSequence().then(function outOfSequencePrompt(reloadTask) {
                            return reloadTask ? svc.resyncTask() : null;
                        });
                        break;
                    case -21:
                        svc.checkPrimeMsg(responseModel.primeMsg(), taskToUpdate);
                        _drm = responseModel.primeMsg();
                        _drmVisible = true;
                        break;

                    default: // for all others ... show the error
                        // for existing tasks
                        _drm = responseModel.primeMsg();
                        _drmVisible = true;
                        break;
                }

                if (options) {
                    _drmVisible = !options.drmVisible ? options.drmVisible : _drmVisible;
                }

                return responseModel.result() === 0;
            }

            /**
             * Clear location info on task and other location related indicators
             */
            function clearLocation() {
                _task.location().text(null);
                _task.location().area(null);
                _task.location().coordinates(new LatLong());
                _task.location().resetMotorwayInfo();

                _task.indicators().motorway(false);
                _task.indicators().dangerousLocation(false);
            }

            /**
             * remove the recovery from the task
             */
            function clearRecovery() {
                if (!_task.createReason().isLogistics() && !_task.createReason().isRelayLeg()) {
                    // this resets the recovery diagnostics q&a list that is now inside fault object
                    _task.recovery().reset();
                }
            }

            function _setRelayRecoveryDestination(lat, lng, distance, text, area, gazResult = null) {
                const relayLatLng = {
                    lat: gazResult && gazResult.properties.nextJunc && gazResult.properties.nextJunc.latitude ? gazResult.properties.nextJunc.latitude : lat,
                    lng: gazResult && gazResult.properties.nextJunc && gazResult.properties.nextJunc.longitude ? gazResult.properties.nextJunc.longitude : lng
                };

                _task.recovery().destination().coordinates().latitude(relayLatLng.lat);
                _task.recovery().destination().coordinates().longitude(relayLatLng.lng);
                _task.recovery().distance(distance);
                _task.eurohelp().recovery().distance(distance);
                _task
                    .recovery()
                    .destination()
                    .text(_.isArray(text) ? text.join(', ') : text);
                if (_.some(area, Boolean)) {
                    _task.recovery().destination().area(area.join(', '));
                }
                _task.recovery().relay(true);
                _task.uiStatus().relay(false);
                //if user changes Relay To destination after retrieving -3 destResourceId task then the fields Destination Location,
                // Area/Postcode and Destination options(Repairer, Home and Other) should be enabled
                // NOTE: removed by RBAUAA-11803 for release/166. Delete this code if it is not needed in future.
                //if (_task.recovery().destResourceId() === DestResourceIDsConstants.LOCAL) {
                //_task.recovery().destResourceId(null);
                //}
            }

            _.extend(svc, {
                appTask: function appTask(val) {
                    return arguments.length ? (_appTask = val) : _appTask;
                },
                drm: function drmAcessor(val) {
                    return _.isString(val) ? (_drm = val) : _drm;
                },
                showDRM: function showDRM(val) {
                    return arguments.length ? (_drmVisible = val) : _drmVisible;
                },
                /**
                 * Assert if task loaded to the aah2 had the sec recovery added via eg via legacy or EVA,
                 */
                checkPreExistingRecovery: () => {
                    // we only check valid recoveries
                    if (!_task || (_task && !_task.recovery().destination().isSet())) {
                        hasPreExistingRecovery = false;
                        preExistingRecoveryTaskId = -1;
                        return;
                    }

                    // if check for th same task, ignore
                    const id = _task.id();
                    if (id === preExistingRecoveryTaskId) {
                        return;
                    }

                    hasPreExistingRecovery = true;
                    preExistingRecoveryTaskId = id;
                },
                hasPreExistingRecovery: () => hasPreExistingRecovery,
                /**
                 * Accessor for task
                 */
                task: function taskAcessor(val) {
                    if (arguments.length) {
                        _task = val;
                        this.checkPreExistingRecovery();
                        _appTask = _.noop();
                        TextTopicService.loadTextTopics(_task.entitlement().textTopics, _task.status());
                    }
                    return _task;
                },
                isFairPlayValue: function isfairplayValue() {
                    return svc.task().entitlement().fairPlay() === _fairPlayLimit ? true : false;
                },
                entitlementExhaustedAlert: function entitlementExhaustedAlert() {
                    AlertService.createAlert('Onward Travel - Entitlement limit has been reached', AlertTypes.INFO);
                },
                isRepairer: function isRepairer(destResourceId) {
                    return destResourceId === DestResourceIDsConstants.REPAIRER || destResourceId > 0;
                },
                resetContact: function resetContact() {
                    svc.task().contact(new Contact());
                },
                validationForRepairer: function validationForRepairerAccessor() {
                    var deferred = $q.defer();
                    if (_task.recovery().isSet() && svc.isRepairer(_task.recovery().destResourceId())) {
                        //to check for Repairer is selected in Relay window
                        let outstandingPromptReferrer = PromptService.outstandingPromptReferrer();
                        if (!_task.isNew() && !svc.recoveryOptionOrRepairerChanged()) {
                            _attendedScheduleWindow = true;
                        }
                        if (svc.attendedScheduleWindow()) {
                            if (outstandingPromptReferrer && !outstandingPromptReferrer.isComplete()) {
                                //when Schedule window is opened from Open Schedule Window prompt
                                outstandingPromptReferrer.resolve(true);
                                deferred.resolve(true);
                            } else {
                                deferred.resolve(true); // when the user opens the schedule window manually
                            }
                        } else {
                            //when schedule window is not attended for Repairer destination
                            PromptService.showScheduleWindowOpen()
                                .then((optionNoSelected) => {
                                    optionNoSelected ? deferred.resolve(true) : null;
                                })
                                .catch(() => {
                                    deferred.reject();
                                });
                        }
                        svc.recoveryOptionOrRepairerChanged(false);
                    } else {
                        deferred.resolve(true);
                    }
                    return deferred.promise;
                },
                prepSend: function prepSend() {},
                /**
                 * Validate demand-deflection and membership
                 * @param ddCheckOptions
                 * @returns {Promise<never>}
                 */
                validate: function validate(ddCheckOptions = undefined) {
                    const entitlement = cshService.entitlement();

                    return PromptService.checkLocationType(_task)
                        .then(() => {
                            return PromptService.checkDemandDeflection(entitlement, _task, ddCheckOptions);
                        })
                        .then(() => {
                            return ValidationService.canSendOrSaveTask(_task);
                        })
                        .then((scope) => {
                            return scope ? LimitedMembershipService.check(_task) : Promise.reject();
                        })
                        .catch(() => {
                            return Promise.reject();
                        });
                },
                /**
                 *
                 * @param {Object} [brctaskOptions] - optional options
                 */
                send: function send(cr, brctaskOptions, checkOptions = undefined) {
                    return svc.validate(checkOptions).then(() => {
                        return svc.validationForRepairer().then(function () {
                            var _brctaskOptions = brctaskOptions ? brctaskOptions : null;
                            _isTaskMerged = false;
                            svc.prepSend();
                            //To remove remark text coming from digital app
                            svc.removeDigitalRemark(_task);
                            svc.addBuzbyTelFixDetails(_task);
                            PromptService.customerVulnerability();
                            AppointmentService.isKeyOnOrderSelected(true);
                            return $http
                                .post(URLs.TASK_AUTHORISE, {
                                    task: _task.toJSON(),
                                    cr: cr.toJSONExcludeTasks(),
                                    brctaskOptions: _brctaskOptions
                                })
                                .then(function authoriseSuccess(response) {
                                    const responseModel = new TaskWriteResponse(response.data);
                                    if (responseModel.taskId() && responseModel.taskId() >= 0 && responseModel.taskId() !== _task.id()) {
                                        const customerRequestQuery = new CustomerRequestQuery();
                                        // start 1 hour ago
                                        customerRequestQuery.param().startTime(new Date(new Date() - 60 * 60 * 1000));
                                        // end now
                                        customerRequestQuery.param().endTime(new Date());
                                        // pass taskID
                                        customerRequestQuery.param().taskId(responseModel.taskId());
                                        //return cshService.findByQuery(customerRequestQuery);
                                        return Promise.all([cshService.findByQuery(customerRequestQuery), responseModel, response]);
                                    } else {
                                        return Promise.all([Promise.resolve(-1), responseModel, response]);
                                    }
                                })
                                .then(([csh, responseModel, response]) => {
                                    if (csh && csh !== -1) {
                                        let mergedTask;
                                        const mergedCr = csh.customerRequestHistory().find((cr) => {
                                            return cr.tasks().find((task) => {
                                                if (task.id() === responseModel.taskId()) {
                                                    mergedTask = task;
                                                    return task;
                                                }
                                            });
                                        });

                                        if (mergedCr && mergedTask) {
                                            cr = mergedCr;
                                            _task = mergedTask;
                                            _isTaskMerged = true;
                                        }
                                    }
                                    if (processTaskWriteResponse(responseModel)) {
                                        const smsEventHook = _task.eventHooks() && _task.eventHooks().sms() && _task.eventHooks().sms().active();
                                        // don't send tacking-link for CUST_GROUP who don't want SMS from the AA
                                        if (smsEventHook) {
                                            TrackingLinkService.sendSMS(_task);
                                        }
                                        _serviceAbuseRemark = '';

                                        // this needs to go to a different method ..
                                        // each called service handles failure internally and returns success
                                        return Promise.all([GarageService.garageBooking(_task), PaymentService.send(cr), FlpTaskService.send(cr)])
                                            .finally(
                                                _.spread(function (garageResp, paymentResp, flpTaskResp) {
                                                    // if we have performed any of these transaction we need to force another task update on the server
                                                    // using a full task update is problematic as prime changes in sequence no will cause the write to fail
                                                    // and recovering from that using normal approach will cause all kind of issues
                                                    return PartialTaskUpdateService.save(_task, GarageService.bookingResult(), paymentResp, FlpTaskService.sendResults());
                                                })
                                            )
                                            .then(function onPartialTaskUpdate() {
                                                FlpTaskService.sendResults(null);
                                                GarageService.bookingResult(null);
                                                return responseModel;
                                            })
                                            .catch(function (err) {
                                                // don't know what to do if we have a failure on a partial update the task
                                                return responseModel;
                                            });
                                    } else {
                                        return Promise.reject(response.data);
                                    }
                                })
                                .catch(function saveError(resp) {
                                    const message = toDrmMessage(resp);
                                    return Promise.reject('TaskService.send :: failed to send task to prime :: ' + message);
                                });
                        });
                    });
                },
                getLastTasks: (cshData) => {
                    if (!cshData.customerRequestHistory) {
                        return;
                    }

                    const history = cshData.customerRequestHistory();

                    if (!history.length) {
                        return;
                    }

                    let current;
                    let lastCompleted;
                    let lastCompletedInSevenDays;

                    for (const entry of history) {
                        current = entry;
                        const status = entry.status().code();
                        if ([Task.COMP_STATUS, Task.CLSD_STATUS].includes(status)) {
                            if (!lastCompleted) {
                                lastCompleted = entry;
                            } else if (entry.statusUpdatedTime() > lastCompleted.statusUpdatedTime()) {
                                lastCompleted = entry;
                            }
                        }
                    }

                    if (lastCompleted && lastCompleted.isCompWithin7Days()) {
                        lastCompletedInSevenDays = lastCompleted;
                    }

                    return { current, lastCompleted, lastCompletedInSevenDays };
                },
                /*Function to check if task is created from a web-app and it is in INIT status*/
                isSelfServiceAppTask: (activeTask) => {
                    return !!(this.task().createReason().isSelfServiceApp() && activeTask && (activeTask.status() === 'INIT' || activeTask.status() === 'UNAC'));
                },
                isContactNumberEmpty: function () {
                    return !_task.contact().telephone();
                },
                /**
                 * To save the currently selected task or the supplied task.
                 * If task is supplied, task validation will not occur.
                 * @param {Task} [task] - optional task, if not supplied, it will save the current task being edited
                 * @param {Object} [options] - optional options, exatra info with save
                 */
                save: function save(task, options, brctaskOptions) {
                    let ddCheckOptions = {};
                    var taskToSave = task ? task : _task;

                    // don't make checks for any created tasks except if task in INIT state (e.g. comes from digital channel )
                    if (taskToSave.status() != Task.INIT_STATUS) {
                        ddCheckOptions = {
                            checkCalloutHistory: false,
                            // let's not make any checks for tasks modified outside of aah2
                            checkSecondRecovery: !this.hasPreExistingRecovery(),
                            checkRepeatFault: false,
                            forceCheck: true
                        };
                    }

                    return svc.validate(ddCheckOptions).then(() => {
                        var _saveTaskOptions = options ? options : _saveTaskOptions,
                            _brctaskOptions = brctaskOptions ? brctaskOptions : null,
                            taskOriginalStatus = taskToSave.status();

                        svc.prepSend();
                        //To remove remark text coming from digital app
                        svc.removeDigitalRemark(taskToSave);
                        svc.addBuzbyTelFixDetails(taskToSave);

                        return $http
                            .post(URLs.TASK_AUTO_SAVE, {
                                task: taskToSave.toJSON(),
                                brctaskOptions: _brctaskOptions
                            })
                            .then(function autoSaveSuccess(response) {
                                var responseModel = new TaskWriteResponse(response.data);
                                if (processTaskWriteResponse(responseModel, taskToSave, _saveTaskOptions)) {
                                    // If we were in an INIT status it means it was a partial task.
                                    // If we're now not in INIT status it means we have a full task.
                                    // Let's send the trackingLink SMS. fire and forget. We don't care about any errors
                                    // Also we need to stop tracking link SMS for tasks generated from ROAM
                                    const roamEventHook = taskToSave.eventHooks() && taskToSave.eventHooks().roam() && taskToSave.eventHooks().roam().active();

                                    if (responseModel.indicators().selfService() && taskOriginalStatus === 'INIT' && responseModel.status() !== 'INIT' && !roamEventHook) {
                                        TrackingLinkService.sendSMS(_task);
                                        // If correspondence address is invalid show prompt of update or send update sms with link options
                                        svc.checkCorrespondenceAddress();
                                    }
                                    _serviceAbuseRemark = '';

                                    return Promise.resolve(responseModel);
                                } else {
                                    if (responseModel.result() == -2) {
                                    }
                                    // save has failed anyway ..
                                    return Promise.reject(response.data);
                                }
                            })
                            .then((response) => {
                                return FlpTaskService.send(cshService.csh()).then(() => {
                                    FlpTaskService.sendResults(null);
                                    return response;
                                });
                            })
                            .catch(function saveError(resp) {
                                const message = toDrmMessage(resp);
                                return Promise.reject('TaskService.save :: failed to save task :: ' + message);
                            });
                    });
                },
                complete: function complete(cr, cancellingTask) {
                    var deferred = $q.defer();

                    if (!_task.isCompleted() && (_task.isNew() || ValidationService.areMandatoryOnCompletionFlexFieldsComplete(_task.entitlement().variableData(), cancellingTask))) {
                        svc.prepSend();
                        $http
                            .post(URLs.TASK_COMPLETE, {
                                task: _task.toJSON(),
                                cr: cr.toJSONExcludeTasks()
                            })
                            .then((response) => {
                                return FlpTaskService.send(cr).then(() => {
                                    FlpTaskService.sendResults(null);
                                    let responseModel = new TaskWriteResponse(response.data);
                                    if (processTaskWriteResponse(responseModel)) {
                                        deferred.resolve();
                                    } else {
                                        deferred.reject(response.data);
                                    }
                                });
                            })
                            .catch((resp) => {
                                toDrmMessage(resp);
                                deferred.reject();
                            });
                    } else {
                        deferred.reject();
                    }

                    return deferred.promise;
                },
                completeTask: function completeTask(task) {
                    var deferred = $q.defer();
                    $http
                        .post(URLs.TASK_COMPLETE, {
                            task: task
                        })
                        .then(function completeSuccess(response) {
                            let responseModel = new TaskWriteResponse(response.data);
                            if (processTaskWriteResponse(responseModel, task)) {
                                deferred.resolve();
                            } else {
                                deferred.reject(response.data);
                            }
                        })
                        .catch((resp) => {
                            toDrmMessage(resp);
                            deferred.reject();
                        });
                },
                close: function close() {
                    if ($state.paymentReference) {
                        $state.paymentReference = null;
                    }
                    _task = new Task();
                    _appTask = _.noop();
                    _task.status(null);
                    _drm = '';
                    _attendedScheduleWindow = false;
                    _driveIn = false;
                    _goodWill = false;
                    _storage = false;
                    _repair = false;
                    _abandonment = false;
                    _goodWill = false;
                    _serviceAbuseRemark = '';
                },
                resetTask: function resetTask() {
                    _task = new Task();
                },
                /**
                 * Function to fetch all tasks by CustomerRequest
                 */
                fetch: function fetchTasks(customerRequestId) {
                    return $http.get(URLs.TASKS_REQUEST + customerRequestId).then(function (response) {
                        _tasks = [];

                        _.forEach(response.data, function (item, idx) {
                            // TODO: should we use here function svc.createTask?
                            _tasks.push(new Task(item));
                        });

                        return _tasks;
                    });
                },
                getAssistanceCheckbox: function getAssistanceCheckbox() {
                    return $http
                        .get(
                            `api/common-faults-service/commonFaults/compassionateService/${svc.task().entitlement().customerGroup().code()}?taskType=${svc.task().taskType().code()}&status=${svc
                                .task()
                                .status()}&completionCode=${svc.task().fault().outcome().completionCode()}`
                        )
                        .then((res) => {
                            _assistanceData = res.data.data;
                            return _assistanceData;
                        })
                        .catch((error) => {
                            return $q.reject(Utils.handleError(error, 'Cannot retrieve Data.'));
                        });
                },
                /**
                 * Function to fetch task by task id
                 */
                fetchTaskById: function fetchTaskById(taskId) {
                    return $http.get(URLs.TASK_FETCH.replace(':id', taskId)).then(function (response) {
                        // TODO: should we use here function svc.createTask?
                        return new Task(response.data);
                    });
                },
                tasksByCustomerRequest: function tasksByCustomerRequest(customerRequestId) {
                    svc.carSwapCarTasksByCustomerRequest(customerRequestId);
                    return $http.get(URLs.TASKS_BY_CUSTOMER_REQUEST + customerRequestId).then(function tasksByCustomerRequestSuccess(response) {
                        _tasks = [];

                        _.forEach(response.data, function (item, idx) {
                            const task = svc.createTask(item);

                            if (task) {
                                _tasks.push(task);
                            }
                        });

                        return _tasks;
                    });
                },
                carSwapCarTasksByCustomerRequest: function carSwapCarTasksByCustomerRequest(customerRequestId) {
                    return $http.get(URLs.CAR_SWAP_TASKS_BY_CUSTOMER_REQUEST + customerRequestId).then(function carSwapCarTasksByCustomerRequestSuccess(response) {
                        _swapCarTasks = response.data || [];
                        return _tasks;
                    });
                },
                swapCarTasks: () => {
                    return _swapCarTasks;
                },
                /**
                 * Create task based on type or create reason
                 * @param {*} item
                 */
                createTask: function createTask(item) {
                    switch (item.taskType.code) {
                        case TaskType.FINANCIAL:
                            return new FinancialTask(item);
                        case TaskType.FRONT_LINE_POLICY:
                            return new FlpTask(item);
                        case TaskType.UPDATE:
                            return new UpdateTask(item);
                        case TaskType.COMMENDATION:
                            return new CommendationTask(item);
                        case TaskType.SE_UPDATE:
                            return new SEUpdateTask(item);
                        case TaskType.BREAKDOWN:
                            // depending on create reason create breakdown task
                            // or Secondary Benefits tasks
                            switch (item.createReason.id) {
                                case CreateReason.HOTEL:
                                    return new HotelTask(item);
                                case CreateReason.TRANSPORT:
                                    return new TransportTask(item);
                                default:
                                    // for all other create breakdown task
                                    return new Task(item);
                            }
                            break;
                        case TaskType.ARCHIVED:
                            return new ArchivedTask(item);
                        case TaskType.ADMINISTRATION:
                            return new MobilityTask(item);
                        default:
                            //ignore the task not one we are interested in
                            break;
                    }
                },
                /**
                 * Reschedule task to get new slots
                 * @return promises
                 */
                reschedule: function reschedule() {
                    var deferred = $q.defer();

                    if (ValidationService.isTaskValid(_task)) {
                        svc.prepSend();
                        return $http
                            .post(URLs.RESCHEDULE, {
                                task: _task
                            })
                            .then(function rescheduleSuccess(data) {
                                var responseModel = new TaskWriteResponse(data.data);
                                processTaskWriteResponse(responseModel);
                                deferred.resolve(responseModel);
                            })
                            .catch(function rescheduleError(resp) {
                                const message = toDrmMessage(resp.data);
                                deferred.reject('TaskService.reschedule :: failed to reschedule the task :: ' + message);
                            });
                    }

                    return deferred.promise;
                },
                saveLocation: function saveLocation(location, exitJunction = null, stateName = null) {
                    const locationObject = getLocationObjectByState(stateName);
                    locationObject.coordinates().latitude(location.lat());
                    locationObject.coordinates().longitude(location.lng());
                    locationObject.resetMotorwayInfo();
                    if (_task.isDiagnosedRecoveryJob()) {
                        if (exitJunction && exitJunction.properties && exitJunction.properties.nextJunc) {
                            _task.recovery().destination().coordinates().latitude(exitJunction.properties.nextJunc.latitude);
                            _task.recovery().destination().coordinates().longitude(exitJunction.properties.nextJunc.longitude);
                        } else {
                            _task.recovery().destination().coordinates().latitude(location.lat());
                            _task.recovery().destination().coordinates().longitude(location.lng());
                        }
                    }
                },
                saveLocationText: function saveLocationString(lines, stateName) {
                    const locationObject = getLocationObjectByState(stateName);
                    locationObject.text(_.isArray(lines) ? lines.join(', ') : [lines].join(', '));
                },
                saveLocationArea: function saveLocationString(lines, stateName) {
                    let area = _.isArray(lines) ? lines.join(', ') : [lines].join(', ');

                    area = area.replace(/, united kingdom/gim, '');
                    const locationObject = getLocationObjectByState(stateName);

                    locationObject.area(area);
                    if (_task.isDiagnosedRecoveryJob() || _task.indicators().dangerousLocation()) {
                        _task.recovery().destination().area(_task.location().area());
                    }
                },
                /**
                 * Sets relay destination on the task and will display member related prompts if necessary
                 * @param {number} lat
                 * @param {number} lng
                 * @param {Array.<string>} text
                 * @param {Array.<string>} area
                 * @param {boolean} [disablePrompt=false]
                 * @param {Object} [properties] poi property details
                 * @returns {Promise} the promise which resolves to true or false to represent apply and reject respectfully
                 */
                setRelayRecoveryDestination: function setRelayRecoveryDetails(lat, lng, text, area, disablePrompt, properties) {
                    let originLatLong = null;
                    let destinationLatLong = null;

                    if (_.isUndefined(disablePrompt)) {
                        disablePrompt = false; //default
                    }

                    // test if we are recoverying to a garage that can accept a booking
                    if (GarageService.isGarageAcceptingBookings(properties)) {
                        // store in the task details of the garage we recovering breakdown.
                        _task.recovery().garage(GarageService.garageDetails(properties, lat, lng));

                        // try to book garage ideally we should chain the outcome of thie with the rest ..
                        // this might be in the wrong place if distance is to be taken into account
                        GarageService.garageBooking(_task);
                    }

                    //calculate distance

                    if (_task.location().coordinates().latitude() && _task.location().coordinates().longitude()) {
                        originLatLong = `${_task.location().coordinates().latitude()},${_task.location().coordinates().longitude()}`;
                    }

                    if (lat && lng) {
                        destinationLatLong = `${lat},${lng}`;
                    }

                    return RecoveryService.calculateDistanceInMiles(originLatLong, destinationLatLong).then((distance) => {
                        if (disablePrompt === true) {
                            // by pass prompt i.e. if dangerous location is set this will add relay to the nearest safe location
                            // without showing relay related prompts
                            _setRelayRecoveryDestination(lat, lng, distance, text, area);
                            return true;
                        } else {
                            if (distance <= 10) {
                                /* clearing 24 hour exclusion product if product Relay is set to true
                                    when recovery location changed to a location less than 10 miles from breakdown location */

                                const destinationCoordinates = _task.recovery().destination().coordinates();
                                if (
                                    destinationCoordinates.latitude() &&
                                    destinationCoordinates.longitude() &&
                                    _task.contractValidation().coolingOff().product() &&
                                    _task.contractValidation().coolingOff().product().toUpperCase() === 'RELAY' &&
                                    _task.contractValidation().roadworthiness().passed()
                                ) {
                                    PromptService.removePromptById(1045); // Prompt.CONTRACT_VALIDATION = 1045;
                                    PromptService.clearCoolingOffOutcomes(_task, 'Relay');
                                }
                            }
                            if (ContractValidationService.isAdHockPromptCheckRequired(_task)) {
                                return PromptService.checkRecoveryDistance(_task, null, distance).then((success) => {
                                    if (success) {
                                        _setRelayRecoveryDestination(lat, lng, distance, text, area);
                                    }
                                    return success;
                                });
                            }

                            _setRelayRecoveryDestination(lat, lng, distance, text, area);
                            return Promise.resolve(true);
                        }
                    });
                },
                nearestEntryJunction: function nearestEntryJunctionAccessor(val) {
                    return arguments.length ? (_nearestEntryJunction = val) : _nearestEntryJunction;
                },
                nearestMotorwayMarkerPost: function nearestMotorwayMarkerPostAccessor(val) {
                    return arguments.length ? (_nearestMotorwayMarkerPost = val) : _nearestMotorwayMarkerPost;
                },
                resyncTask: function resyncTask(inputTaskId, resetQualification = true) {
                    let taskId = inputTaskId ? inputTaskId : _task.id();
                    return $http.get(URLs.TASK_RESYNC.replace(':id', taskId)).then(function onResyncSuccess(resp) {
                        const tmpTask = AAHelpTaskFactory.create(resp.data);
                        if (_task.createReason().isHireCar()) {
                            // rember rental object ..
                            tmpTask.rental(_task.rental());
                        }
                        _task = tmpTask;
                        CSHSearchViewerService.task(cshService.csh().findTask(tmpTask.id()));

                        //Reset qualification panel (in case it exists)
                        if (resetQualification) {
                            QualificationWorkflowService.restart();
                        }
                    });
                },
                /**
                 * This function get task id and some relevant information of previously saved task
                 * And assign these details to new UNAC task
                 * Then give a call to Save task insted of send task.
                 */
                overrideTask: function overrideTask() {
                    _task.id(_overrideTaskDetail.id);
                    _task.status(_overrideTaskDetail.status);
                    _task.sequence(_overrideTaskDetail.sequence);
                    _task.customerRequestId(_overrideTaskDetail.customerRequestId);
                    svc.save();
                },
                canOverrideTask: function canOverrideTask(responseModel) {
                    var EntitlementHelperService = $injector.get('aahEntitlementHelperService'),
                        selectedEntitlement = EntitlementHelperService.selectedEntitlement();

                    /**
                     *  Fetching history of customer using CSH Service
                     */
                    CSHService.findByEntitlement(selectedEntitlement, ServiceTypeService.serviceType()).then(
                        function membershipSearchSuccess(data) {
                            var customerRequestHistory = data.toJSON().customerRequestHistory,
                                customerRequestHistoryLen = customerRequestHistory.length,
                                currentTaskJSON = _task.toJSON(),
                                tasks,
                                tasksLen;

                            _overrideTaskDetail = null;

                            /**
                             * Looping through history of customer to match vehicle registration number
                             * If we get a match then we can override
                             * Else we have to show prompt of prime insert failed
                             */
                            for (let i = 0; i < customerRequestHistoryLen; i++) {
                                tasks = customerRequestHistory[i].tasks;
                                tasksLen = tasks.length;
                                for (let j = 0; j < tasksLen; j++) {
                                    if (currentTaskJSON.vehicle.registration === tasks[j].vehicle.registration) {
                                        /**
                                         * We find match and saving this task details in _overrideTaskDetail to override.
                                         */
                                        _overrideTaskDetail = tasks[j];
                                        // PromptService.taskOverride()
                                        //     .then(function outOfSequencePrompt(overrideTask) {
                                        //         return overrideTask ? svc.overrideTask() : null;
                                        //     });
                                    }
                                }
                            }

                            if (!_overrideTaskDetail) {
                                _drm = responseModel.primeMsg();
                                _drmVisible = true;
                            }
                        },
                        function membershipSearchFailure(response) {
                            var message = Errors.SYSTEM_ERROR_MESSAGE;
                            if (response.data && response.status === Errors.HTTP_INTERNAL_SERVER_ERROR_CODE) {
                                message = response.data.msg ? response.data.msg : response.data;
                            }
                            _drm = responseModel.primeMsg();
                            _drmVisible = true;
                            return AlertService.createAlert(message, AlertTypes.DANGER);
                        }
                    );
                },
                clone: function cloneTask(reason) {
                    return $http
                        .post(URLs.CLONE, {
                            reason: reason.toJSON(),
                            task: _task.toJSON()
                        })
                        .then(function onCloneSuccess(resp) {
                            let task;

                            switch (reason.id()) {
                                // TODO: should we use here function svc.createTask?
                                case CreateReason.HIRE_CAR:
                                    task = new MobilityTask(resp.data);
                                    break;
                                case CreateReason.HOTEL:
                                    task = new HotelTask(resp.data);
                                    break;
                                case CreateReason.TRANSPORT:
                                    task = new TransportTask(resp.data);
                                    break;
                                default:
                                    task = new Task(resp.data);
                                    break;
                            }

                            return task;
                        });
                },
                addTask: function addTask(reason) {
                    return svc.validate().then(() => {
                        var deferred = $q.defer();
                        svc.validationForRepairer()
                            .then(function () {
                                $http
                                    .post(URLs.TASK_ADD, {
                                        reason: reason,
                                        task: _task.toJSON()
                                    })
                                    .then(function onAddTask(resp) {
                                        var splitResp = new SplitTaskResponse(resp.data);
                                        // no idea if we need to book a garage ..
                                        // GarageService.book(splitResp.task());

                                        _drm = splitResp.response().primeMsg() ? splitResp.response().primeMsg() : '';
                                        _drmVisible = !_.isEmpty(_drm);

                                        deferred.resolve(splitResp.task());
                                    });
                            })
                            .catch(function () {
                                deferred.reject();
                            });

                        return deferred.promise;
                    });
                },
                _getTaskAuditService: () => {
                    return $injector.get('aahTaskAuditService');
                },
                reopen: function reopenTask(reason, srcTask, entitlement) {
                    return _reopenReattend(URLs.TASK_REOPEN, reason, srcTask, entitlement);
                },
                reattend: function reattendTask(reason, srcTask, entitlement) {
                    return _reopenReattend(URLs.TASK_REATTEND, reason, srcTask, entitlement);
                },
                setVulnerableCustomerRemarks: function setVulnerableCustomerRemarks() {
                    let entitlement = cshService.entitlement();
                    if (!entitlement) {
                        return;
                    }

                    if (!this.entitlementHelper().isVulnerableCustomer()) {
                        return;
                    }

                    // add remarks for vulnerable customer
                    if (entitlement.contact().options().vulnerability().isVulnerable()) {
                        const remarks = _task.location().remarks() || '';
                        // only mark with [VC] once per task
                        if (!remarks || !remarks.match(new RegExp(/\[VC\]/gm))) {
                            _task.location().remarks(`${remarks}${remarks.length ? ' ' : ''}[VC]`);
                        }
                    }

                    // add remarks for additional roadside assistance
                    if (entitlement.contact().options().additionalRoadsideSupport()) {
                        const remarks = _task.location().remarks() || '';
                        // only mark with [ARS] once per task
                        if (!remarks || !remarks.match(new RegExp(/\[ARS\]/gm))) {
                            _task.location().remarks(`${remarks}${remarks.length ? ' ' : ''}[ARS]`);
                        }
                    }
                },
                setVulnerableCustomerAudit: function setVulnerableCustomerAudit() {
                    let entitlement = cshService.entitlement();
                    if (!entitlement) {
                        return;
                    }

                    if (!this.entitlementHelper().isVulnerableCustomer()) {
                        return;
                    }

                    const id = _task.id();
                    if (!id) {
                        return;
                    }

                    let callinfoText = '';

                    // add callinfo for vulnerable customer
                    if (entitlement.contact().options().vulnerability().isVulnerable()) {
                        const vulnerabilities = entitlement.contact().options().vulnerability().vulnerabilities().join(', ');
                        const outcome = entitlement.contact().options().vulnerability().outcome();
                        callinfoText += `[VC] vulnerabilities: ${vulnerabilities} and outcome: ${outcome || 'n/a'}`;
                    }

                    // add callinfo for additional roadside assistance
                    if (entitlement.contact().options().additionalRoadsideSupport()) {
                        const arsDetails = entitlement.contact().options().additionalRoadsideSupportInformation();
                        callinfoText += callinfoText ? ' | ' : '';
                        callinfoText += `[ARS] ${arsDetails}`;
                    }

                    let isCallInfoExist = svc
                        ._getTaskAuditService()
                        .taskAuditList()
                        .some((i) => i.description() === callinfoText);
                    if (!isCallInfoExist) {
                        svc._getTaskAuditService()
                            .saveCallinfoRecord(_task, callinfoText)
                            .then(() => {
                                svc._getTaskAuditService().getTransactionHistory(_task.customerRequestId(), _task.id(), cshService.seLocator());
                            });

                        _task.isDirty(true);
                    }
                },
                setDirectRecovery: function setDirectRecovery(isDirect) {
                    //if direct recovery, clear the selected forced recovery fault
                    if (isDirect) {
                        _task.recovery().forceRecyFault(false);
                        _task.recovery().reasonForce(false);
                        RecoveryService.clearForcedRecoveryFault();

                        _task.indicators().authorised(_task.indicators().dangerousLocation() || !_task.fault().isFuelAssist()); // set to authorise if in a dangerous location or if fault is anything other than fuel assist
                    }

                    //make sure we set the FORCE_RECY indicators on the task
                    if (_task.fault().mwayRecovVehFaultId() === 0) {
                        _task.recovery().reasonForce(!isDirect);
                    } else {
                        _task.recovery().reasonForce(isDirect);
                    }

                    if (!isDirect) {
                        if (_task.recovery().fault().categoryCode() === 'FRE') {
                            _task.recovery().forceRecyFault(true);
                        } else {
                            _task.recovery().forceRecyFault(false);
                        }

                        _task.indicators().authorised(false);
                    }
                },
                getDirectRecovery: function getDirectRecovery() {
                    //check if the task has a forced recovery
                    return !_task.recovery().reasonForce();
                },
                //remove the recovery from the task
                clearRecovery: clearRecovery,
                clearLocation: clearLocation,
                /**
                 * Function to tidy up task when changing a vehicle
                 */
                updateVehicle: function updateVehicle(vehicle) {
                    _task.vehicle(vehicle);
                    _task.fault(new Fault()); // this resets the diansosticsQandAList
                    if (!_task.indicators().motorway() && !_task.indicators().dangerousLocation()) {
                        clearRecovery();
                    }
                },
                addAdhocTask: function AddAdhocTask(task) {
                    _pendingAdhocTasks.push(task);
                },
                willJoin: function willJoin(customerGroupCode, serviceTypeCode) {
                    return $http
                        .post(URLs.WILL_JOIN, {
                            customerGroupCode: customerGroupCode,
                            serviceTypeCode: serviceTypeCode
                        })
                        .then(function onWillJoin(resp) {
                            // TODO: should we use here function svc.createTask?
                            _task = new Task(resp.data);
                            return _task;
                        });
                },
                getTaskStatusText: function getTaskStatusText(taskId) {
                    var promise,
                        url = URLs.TASK_STATUS_TEXT.replace(':id', taskId);
                    if (arguments.length && taskId !== -1) {
                        promise = $http.get(url).then(function getTaskStatusSuccess(response) {
                            _drm = response.data.statusText;
                            return _drm;
                        });
                    }
                    return $q.when(promise || null);
                },
                updateFault: function updateFault(fault) {
                    var theFault = fault ? fault : new Fault(); //if null/falsy value is supplied then clear the task fault
                    if (_task.sequence() > -1) {
                        theFault.initialFault(_task.fault().initialFault()); //preserve initial fault on live jobs
                    }
                    _task.fault(theFault);
                },
                attendedScheduleWindow: function (val) {
                    return arguments.length ? (_attendedScheduleWindow = val) : _attendedScheduleWindow;
                },
                recoveryOptionOrRepairerChanged: function (val) {
                    return arguments.length ? (_isRepairerChanged = val) : _isRepairerChanged;
                },
                completionCodes: (val) => {
                    return arguments.length && val != undefined ? (_completionCodes = val) : _completionCodes;
                },
                driveIn: function (val) {
                    return arguments.length ? (_driveIn = val) : _driveIn;
                },
                goodWill: function (val) {
                    return arguments.length ? (_goodWill = val) : _goodWill;
                },
                storage: function (val) {
                    return arguments.length ? (_storage = val) : _storage;
                },
                repair: function (val) {
                    return arguments.length ? (_repair = val) : _repair;
                },
                abandonment: function (val) {
                    return arguments.length ? (_abandonment = val) : _abandonment;
                },
                goodWill: function (val) {
                    return arguments.length ? (_goodWill = val) : _goodWill;
                },
                uniqueCapabilities: () => {
                    return _.uniqBy(_task.fault().capabilities().concat(_task.recovery().fault().capabilities()), (obj) => obj.id());
                },
                checkPrimeMsg: (primeMsg, task) => {
                    switch (primeMsg) {
                        case BridgeResponseMessages.NON_ZERO_SEQUENCE_NUM_REQD_ON_UPDATE:
                        case BridgeResponseMessages.ZERO_SEQUENCE_NUM_REQD_ON_CREATE:
                            if (!task.isNew()) {
                                // only prompt for reload if task is
                                PromptService.taskOutOfSequence().then(function outOfSequencePrompt(reloadTask) {
                                    return reloadTask ? svc.resyncTask() : null;
                                });
                            }
                            break;
                        case BridgeResponseMessages.LATEST_TIME_LESS_THAN_EARILEST:
                            break;
                    }
                },
                serviceAbuseRemark: function (val) {
                    return arguments.length ? (_serviceAbuseRemark = val) : _serviceAbuseRemark;
                },
                removeDigitalRemark: function (task) {
                    let remarks = task.location().remarks();
                    if (remarks) {
                        remarks = remarks.replace(/\[Dig.*?\][\.\s]*/, '');
                        task.location().remarks(remarks);
                    }
                },
                addBuzbyTelFixDetails: (task) => {
                    let buzbyTelfix = CiscoCtiEventService.buzbyTelfixDetails();
                    if (!_.isEmpty(buzbyTelfix)) {
                        task.buzbyTelfix(buzbyTelfix);
                    }
                },
                entitlementHelper: function entitlementHelper() {
                    return $injector.get('aahEntitlementHelperService');
                },
                updateMultaANKAlertStatus: function updateMultaANKAlertStatus() {
                    return MultaANKAlertStatusService.upsertMultaANKStatus(
                        new AlertStatus({
                            customerReqId: svc.task().customerRequestId(),
                            status: true
                        })
                    );
                },
                vehicleTypeMembership: function vehicleTypeMembership() {
                    return svc.entitlementHelper().checkMembershipType('VEHICLE');
                },
                multaCommonCheck: function multaCommonCheck() {
                    return (
                        svc.entitlementHelper() &&
                        !MultaANKAlertStatusService.multaANKAlertStatus().status() &&
                        svc.entitlementHelper().selectedEntitlement() &&
                        !svc.entitlementHelper().selectedEntitlement().isCorrespondenceAddressInvalid() &&
                        svc.task().createReason().isBreakdown() &&
                        (svc.task().isNew() || svc.task().status())
                    );
                },
                checkIsSameDriver: function checkIsSameDriver() {
                    // To check is same driver calling for vehicle type meberships
                    return (
                        [
                            svc.entitlementHelper().selectedEntitlement().contact().surname(),
                            svc.entitlementHelper().selectedEntitlement().contact().title(),
                            svc.entitlementHelper().selectedEntitlement().contact().firstName()
                                ? svc.entitlementHelper().selectedEntitlement().contact().firstName().trim().charAt(0)
                                : svc.entitlementHelper().selectedEntitlement().contact().initials()
                        ]
                            .join(' ')
                            .trim() === svc.task().contact().name().trim()
                    );
                },
                checkCorrespondenceAddress: () => {
                    const entitlementSvc = svc.entitlementHelper();

                    if (svc.task().entitlement().customerGroup().isPersonal()) {
                        if (
                            (svc.multaCommonCheck() && entitlementSvc.isMainMember() && !svc.vehicleTypeMembership()) ||
                            (svc.multaCommonCheck() && svc.entitlementHelper().selectedEntitlement() && svc.checkIsSameDriver() && svc.vehicleTypeMembership())
                        ) {
                            svc.task().miscFields().ANK(1);

                            return PromptService.checkCorrespondenceAddressInvalid(svc.task()).then((option) => {
                                if (svc.task().miscFields().ANKactionTaken()) {
                                    svc.entitlementHelper().sendAuditInfo(
                                        svc.task().id(),
                                        CallinfoType.GENERAL_REMARK,
                                        svc.task().miscFields().ANKactionTaken(),
                                        svc.task().customerRequestId(),
                                        svc.task()
                                    );

                                    svc.save();
                                }

                                if (option) {
                                    return SMSService.send(SMSFactory.corresspondenceAddressSMS(entitlementSvc.selectedEntitlement(), svc.task())).then(() => {
                                        return svc.updateMultaANKAlertStatus();
                                    });
                                }

                                return svc.updateMultaANKAlertStatus();
                            });
                        }

                        if (svc.multaCommonCheck() && !entitlementSvc.isMainMember() && !svc.vehicleTypeMembership()) {
                            return PromptService.checkCorrespondenceAddressInvalidJointMember(svc.task()).then((option) => {
                                svc.updateMultaANKAlertStatus();
                                return option;
                            });
                        }
                        if (svc.multaCommonCheck() && !svc.checkIsSameDriver() && svc.vehicleTypeMembership()) {
                            return PromptService.vehicleBasedPolicyCorrespondenceAddressCheck(svc.task()).then((option) => {
                                svc.updateMultaANKAlertStatus();
                                return option;
                            });
                        }
                    }
                },
                isTaskMerged: function isTaskMerged() {
                    return _isTaskMerged;
                },
                isNewlyCreated: function isNewlyCreated(val) {
                    if (val !== 'null') {
                        _isNewlyCreated = val;
                    }
                    return _isNewlyCreated;
                },
                setKeyOnOrderCapability: function setKeyOnOrderCapability() {
                    //Set specialist capability on initial load to indicate Key on order
                    if (svc.task().createReason().id() === CreateReason.KEY_ASSIST && AppointmentService.isKeyOnOrderSelected()) {
                        const capabilities = svc.task().fault().capabilities();
                        const hasSpecialistCapability = capabilities.some((capability) => capability === Capabilities.CAP_SPECIALIST);
                        if (!hasSpecialistCapability) {
                            svc.task().fault().capabilities().push(new RefId(Capabilities.CAP_SPECIALIST));
                        }
                    }
                }
            });
        }
    ])
    .config([
        '$qProvider',
        function ($qProvider) {
            $qProvider.errorOnUnhandledRejections(false);
        }
    ]);
