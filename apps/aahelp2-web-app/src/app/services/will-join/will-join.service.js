var _ = require('lodash');
require('angular');

var Task = require('@aa/malstrom-models/lib/task.model');

module.exports = angular
    .module('aah-will-join-service-module', [
        //services
        require('../../services/prompt/prompt.service').name,
        require('../../services/task/task.service').name,
        require('../../services/csh/csh.service').name,
        require('../../services/mapping/context-menu/context-menu.service').name,
        require('../../helpers/task/task.helper').name,
        require('../../services/mapping/location/location.service').name
    ])
    .service('aahWillJoinService', [
        'aahTaskService',
        'aahCSHService',
        'aahContextMenuService',
        'aahPromptService',
        'aahTaskHelper',
        'aahLocationService',
        'aahEligibilityService',
        function TextTopicService(TaskService, CshService, ContextMenuService, PromptService, TaskHelper, LocationService, EligibilityService) {
            var svc = this;

            function validateVehicleChanged() {
                if (
                    CshService.entitlement().vehicle().registration() &&
                    TaskService.task().vehicle().registration() &&
                    CshService.entitlement().vehicle().registration().toUpperCase() != TaskService.task().vehicle().registration().toUpperCase()
                ) {
                    PromptService.showVehicleChanged();
                }
            }

            function validateHomestart() {
                let coordinates = TaskService.task().location().coordinates();
                let isValid = coordinates.longitude() && coordinates.latitude();
                if (!isValid) {
                    PromptService.showInvalidLocationPrompt('Breakdown');
                } else {
                    ContextMenuService.checkNearHomeStart(`${coordinates.latitude()},${coordinates.longitude()}`);
                }
            }

            function authoriseTask() {
                if (!(TaskService.task().fault().isFuelAssist() || TaskService.task().fault().isKeyAssist()) || TaskService.task().indicators().dangerousLocation()) {
                    TaskService.task().indicators().authorised(true);
                }
            }

            _.extend(svc, {
                checkWillJoinTask: function checkWillJoinTask(result) {
                    let task = result.tasks ? result.tasks()[0] : result;
                    if (task instanceof Task && task.createReason().isSelfServiceApp() && task.status() === 'INIT' && task.entitlement().riskCode() === 'WJ') {
                        PromptService.showWillJoinTaskPrompt(task).then((prompt) => {
                            let willJoinTask = prompt.data().task;
                            switch (prompt.selectedOption().name()) {
                                case 'Cancel':
                                    $state.go('completion');
                                    break;
                                case 'Will Join':
                                    authoriseTask();
                                    return EligibilityService.willJoin(true);
                                case 'Policy Found':
                                    let mergedTask = TaskHelper.mergeWillJoinTask(TaskService.task(), willJoinTask);
                                    validateHomestart();
                                    validateVehicleChanged();
                                    authoriseTask();
                                    if (mergedTask.success) {
                                        willJoinTask.fault().outcome().completionCode('09');
                                        TaskService.completeTask(willJoinTask);
                                    }
                                    break;
                            }
                        });
                    }
                }
            });
        }
    ]);
