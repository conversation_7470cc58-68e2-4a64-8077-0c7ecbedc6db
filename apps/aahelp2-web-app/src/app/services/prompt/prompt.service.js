var DateUtil = require('@aa/utilities/date');
require('angular');
var _ = require('lodash');
var ContractValidation = require('@aa/malstrom-models/lib/contract-validation.model');
var CustomerGroup = require('@aa/malstrom-models/lib/customer-group.model');
var PromptOption = require('@aa/malstrom-models/lib/prompt-option.model');
var Prompt = require('@aa/malstrom-models/lib/prompt.model');
var Task = require('@aa/malstrom-models/lib/task.model');
var { isAdditionalTask } = require('@aa/malstrom-models/lib/helpers/task.helper');
var PromptReferrer = require('@aa/malstrom-models/lib/prompt-referrer.model');
var Cuv = require('@aa/malstrom-models/lib/contract-validation-cuv.model');
var ContractValidationCoolingOff = require('@aa/malstrom-models/lib/contract-validation-cooling-off.model');

module.exports = angular
    .module('aah-prompt-service-module', [
        require('angular-ui-router'),
        //constants
        require('../../constants/prompt/prompt-urls.constants').name,
        require('../../constants/alert/alert-type.constants').name,
        require('../../constants/contract-validation/contract-validation.constants').name,
        require('../../constants/flexible-fields/flexible-fields.constants').name,
        require('../../constants/payment/limited-membership-payment-reasons.constants').name,
        //services
        require('../static-prompt/static-prompt.service').name,
        require('../package/package.service').name,
        require('../alert/alert.service').name,
        require('../cli-entitlements/cli-entitlements.service').name,
        require('../side-panel/side-panel.service').name,
        require('../vehicle/vehicle.service').name,
        require('../demand-deflection/demand-deflection.service').name,
        require('../../services/cuv/cuv.service').name,

        //helpers
        require('../../helpers/link-prompt/link-prompt.helper').name,
        require('../../helpers/deferred-with-update').name
    ])
    .service('aahPromptService', [
        '$injector',
        '$http',
        '$q',
        '$state',
        'aahPromptURLs',
        'aahStaticPromptService',
        'aahPackageService',
        'aahAlertService',
        'aahCliEntitlementsService',
        'aahSidePanelService',
        'uibAlertTypes',
        'aahLinkPromptHelper',
        'aahVehicleService',
        'aahContractValidationConstants',
        'aahFlexibleFieldsConstants',
        'aahLimitedMembershipPaymentReasonsConstants',
        'aahDemandDeflectionService',
        'aahUserInfoService',
        'aahCallinfoTypes',
        'aahCuvService',
        '$sce',
        function PromptService(
            $injector,
            $http,
            $q,
            $state,
            URLs,
            StaticPromptService,
            PackageService,
            AlertService,
            CliEntitlementsService,
            SidePanelService,
            AlertTypes,
            LinkPromptHelper,
            VehicleService,
            ContractValidationConstants,
            flexibleFieldsConstants,
            LimitedMembershipPaymentReasonsConstants,
            DemandDeflectionService,
            UserInfoService,
            CallinfoType,
            CuvService,
            $sce
        ) {
            let svc = this,
                _promptsToDisplay = [],
                _ignoredPrompts = [],
                _outstandingPromptReferrer = null,
                _isRTCFault,
                _resolvePromiseServiceAbuse;
            const _getContractValidationService = () => {
                return $injector.get('aahContractValidationService');
            };
            const _getCSHService = () => {
                return $injector.get('aahCSHService');
            };
            const _getUACService = () => {
                return $injector.get('aahUACService');
            };
            const _getContractValidationWorkflowService = () => {
                return $injector.get('aahContractValidationWorkflowService');
            };
            const _getContractValidationActionService = () => {
                return $injector.get('aahContractValidationActionsService');
            };
            const _getEntitlementHelperService = () => {
                return $injector.get('aahEntitlementHelperService');
            };
            const _getTaskService = () => {
                return $injector.get('aahTaskService');
            };
            const _getTaskWriteResponseService = () => {
                return $injector.get('aahTaskWriteResponseService');
            };
            let _getValidationService = () => {
                return $injector.get('aahValidationService');
            };
            let _getEligibilityService = () => {
                return $injector.get('aahEligibilityService');
            };
            let _getMobilityTaskService = () => {
                return $injector.get('aahMobilityTaskService');
            };

            function checkSpecialNeeds(entitlement) {
                if (entitlement.contact().options().specialNeeds()) {
                    AlertService.createAlert('Care Marker: Please see Tiara/Cathie for details', AlertTypes.INFO);
                }
                return null;
            }
            function extractContent(s) {
                var span = document.createElement('span');
                span.innerHTML = s;
                return span.textContent || span.innerText;
            }
            function checkMembershipExpiryDate(entitlement, task) {
                let promise = null,
                    prompt;

                if (task.isNew() && !task.isCompleted() && entitlement.policy().endDate()) {
                    if (entitlement.policy().endDate() < Date.now()) {
                        if (!entitlement.policy().customerGroup().isAddedValueAccount() && DateUtil.durationInDaysFromNow(entitlement.policy().endDate()) < 42) {
                            prompt = StaticPromptService.getStaticPromptById(Prompt.ENTITLEMENTS_EXPIRED);
                            prompt.data({
                                entitlement: task.entitlement(),
                                altContact: task.altContact()
                            });
                            promise = addPrompt(prompt).then((prompt) => (prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes()));
                        } else if (!entitlement.policy().customerGroup().isFleet() && !entitlement.policy().customerGroup().isPFU()) {
                            promise = getMembershipLapsedPrompt(entitlement).then((prompt) => {
                                prompt.data({
                                    entitlement: task.entitlement(),
                                    altContact: task.altContact()
                                });
                                return addPrompt(prompt).then((prompt) => (prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes()));
                            });
                        }
                    }
                }
                return $q.when(promise || true); //if expired, prompt promise is returned, otherwise a promise is returned which resolves to true i.e. valid (not expired)
            }

            function checkSuspendedMembership(entitlement, task) {
                let promise = null;
                let prompt;
                if (entitlement.policy().status()) {
                    if (entitlement.policy().status().toLowerCase() === 's') {
                        prompt = StaticPromptService.getStaticPromptById(1091);
                        prompt.data({
                            entitlement: task.entitlement(),
                            altContact: task.altContact()
                        });
                        promise = addPrompt(prompt).then((prompt) => (prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes()));
                    }
                }

                return $q.when(promise || true);
            }

            function checkNewMembership(entitlement) {
                if (!_getContractValidationService().isToggledOn()) {
                    let promise = null;
                    if (entitlement.policy().startDate() && DateUtil.durationInDaysFromNow(entitlement.policy().startDate()) <= 1) {
                        promise = addPrompt(StaticPromptService.getStaticPromptById(Prompt.NEW_MEMBERSHIP)).then((prompt) => {
                            return prompt.selectedOption().isYes();
                        });
                    }
                    return $q.when(promise || true);
                }
            }

            function checkMembershipWithinCUVCoolOff(task) {
                let promise = null;
                if (!task.contractValidation()) {
                    task.contractValidation(new ContractValidation());
                }
                promise = _getCoolingOffPeriodPrompt(task, 'Commercial Use');
                return $q.when(promise || true);
            }

            function checkMembershipWithinExclusion(task) {
                let promise = null;
                if (!task.contractValidation()) {
                    task.contractValidation(new ContractValidation());
                }
                promise = _getCoolingOffPeriodPrompt(task, 'Roadside');
                return $q.when(promise || true);
            }

            function checkEntitlementStandbyEarlyPrompt(entitlement) {
                if (entitlement.isStandby() && entitlement.policy().customerGroup().isPersonal()) {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.STANDBY_EARLY_PROMPT);
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                }
            }

            function checkEntitlementStandby(entitlement, task) {
                let promise = null,
                    url = URLs.SERVICE_PROMPT.replace(':custGroupCode', entitlement.policy().customerGroup().code());

                if (entitlement.isStandby() && entitlement.policy().customerGroup().isPersonal()) {
                    promise = $http
                        .get(url)
                        .then((response) => {
                            let prompt = new Prompt(response.data);
                            prompt.data({
                                entitlement: task.entitlement(),
                                altContact: task.altContact(),
                                limitedMembershipPaymentReasons: LimitedMembershipPaymentReasonsConstants.STAND_BY
                            });
                            return addPrompt(prompt).then((prompt) => (prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes()));
                        })
                        .catch((err) => {
                            AlertService.createAlert('There was a problem getting Standby information', AlertTypes.INFO);
                            return false;
                        });
                }
                return $q.when(promise || true);
            }

            /**
             * @param {Prompt} prompt
             * @param {boolean} first Force prompt to be displayed first
             * @returns {promise} resolves to; null (if prompt is not added), a prompt containing the selected option, or a boolean result
             */
            function addPrompt(prompt, first = false) {
                let deferred = $q.defer();

                _resolvePromiseServiceAbuse = {
                    promptObject: prompt,
                    promiseValue: deferred
                };

                SidePanelService.updateStateByPrompt(false);

                if (prompt && !isIgnored(prompt) && !_hasPrompt(prompt, _promptsToDisplay)) {
                    if (first) {
                        _promptsToDisplay.unshift(prompt);
                    } else {
                        _promptsToDisplay.push(prompt);
                    }
                    prompt.addOptionHandler((prompt) => {
                        let linkPromptId = LinkPromptHelper.getLinkPromptId(prompt);
                        if (linkPromptId) {
                            deferred.resolve(svc.showStaticPromptById(linkPromptId, prompt.data()));
                        } else if (prompt.selectedOption().state()) {
                            deferred.resolve(svc.showState(prompt));
                        } else {
                            deferred.resolve(prompt); //return prompt with the user selected option set
                        }
                    });
                } else {
                    deferred.reject(null);
                }

                return deferred.promise;
            }

            function getMembershipLapsedPrompt(entitlement) {
                var endDate = entitlement.policy().endDate(),
                    customerGroupCode = entitlement.policy().customerGroup().code(),
                    status = entitlement.policy().status();

                return $http
                    .post(URLs.POST_MEMBERSHIP_LAPSED_PROMPT, {
                        endDate: endDate,
                        customerGroupCode: customerGroupCode,
                        status: status
                    })
                    .then((data) => {
                        data.data.html = data.data.html.replace('[NAME]', entitlement.contact().title() + ' ' + entitlement.contact().surname()).replace('%exp', endDate.toDateString());
                        return data.data ? new Prompt(data.data) : null;
                    });
            }

            function isIgnored(prompt) {
                return _hasPrompt(prompt, _ignoredPrompts);
            }

            function getRefDataPrompt(id) {
                return $http.get(URLs.REF_DATA_PROMPT + id).then((response) => new Prompt(response.data));
            }

            function _hasPrompt(prompt, list) {
                return !!_.find(list, (_prompt) => _prompt.id() && prompt.id() && _prompt.id() === prompt.id());
            }

            function _hasPromptById(id, list) {
                return !!_.find(list, (_prompt) => _prompt.id() && id && _prompt.id() === id);
            }

            /**
             * Builds prompt options for the supplied prompt using the supplied option configs
             * @param prompt
             * @param yesConfig - optional behaviour for 'yes' prompt option
             * @param noConfig - optional behaviour for 'no' prompt option
             * @private
             */
            function _buildPromptOptions(prompt, yesConfig, noConfig) {
                if (!prompt) {
                    return;
                }

                if (yesConfig && noConfig) {
                    prompt.options().length = 0;
                    prompt.options().push(new PromptOption(yesConfig));
                    prompt.options().push(new PromptOption(noConfig));
                }
            }

            function _upgradeEntitlement(task, cr, entitlementPackage, amount, remarks) {
                let formattedAmount = amount ? parseFloat(amount).toFixed(2) : '0.00';
                return PackageService.upgradePackage(task, cr, entitlementPackage, formattedAmount, remarks)
                    .then((success) => success === true)
                    .catch((err) => false);
            }

            function _checkAnyProductFailedCoolingOff(task) {
                if (task.status().toUpperCase() === 'INIT') {
                    //reovke prompts for cases created with digital journey
                    task.contractValidation().coolingOff().passed(null);
                }
                return !!(task.contractValidation().coolingOff() && task.contractValidation().failedCoolingOff());
            }

            function _getCoolingOffPeriodPrompt(task, productName) {
                if (
                    _getContractValidationService().isToggledOn() &&
                    !task.entitlement().customerGroup().isB2BCustomer() &&
                    !task.entitlement().customerGroup().isRoadsideAddOn() &&
                    !_checkAnyProductFailedCoolingOff(task)
                ) {
                    const product = _getCSHService().entitlement().getProduct(productName);
                    if (!product) {
                        return true; // returning for expired memberships
                    }
                    const { productNameResp, isProductWithinCoolingOff } = _getEntitlementHelperService().withinCoolingOffPeriod(product);
                    // const isProductWithinCoolingOff = product ? _getEntitlementHelperService().withinCoolingOffPeriod(product) : false;
                    if (productNameResp && isProductWithinCoolingOff) {
                        let { WITHIN_COOLING_OFF_PERIOD, ON_VALIDATION } = ContractValidationConstants.CONSTANT_BUILDER;
                        let productNameConstant = ContractValidationConstants.CONSTANT_BUILDER[productNameResp.toUpperCase().replace(/ /g, '')];

                        if (_isCoolingOffRequired(task, productName)) {
                            task.contractValidation().coolingOff(
                                new ContractValidationCoolingOff({
                                    product: productNameResp,
                                    startDate: product.claimFromDateTime() || product.startDate()
                                })
                            );
                        }

                        if (svc.isInSvcAbusePages()) {
                            return task.contractValidation().coolingOff().passed(!isProductWithinCoolingOff);
                        }
                        task.contractValidation().coolingOff().passed(!isProductWithinCoolingOff);
                        return _getContractValidationWorkflowService().buildAndLaunchPrompt(task, `${WITHIN_COOLING_OFF_PERIOD}${productNameConstant}_${ON_VALIDATION}`);
                    }
                    task.contractValidation().coolingOff().passed(!isProductWithinCoolingOff);
                }
            }

            function _isCoolingOffRequired(task, productName) {
                //incase product has both cuv and roadside ,roadside takes precedence when created from bcall
                if (productName == 'Commercial Use' && task.status().toUpperCase() === 'INIT' && task.contractValidation().coolingOff().product() == 'Roadside') {
                    return false;
                }
                return true;
            }

            function _roadworthyAndPassedCoolingOff(task) {
                return (
                    _getContractValidationService().isRoadworthy(task.contractValidation()) && // isRoadworthy and
                    (!_getContractValidationActionService().checkFailedCoolingOff() || svc.isInSvcAbusePages() || _coolingOffValidated(task.contractValidation().coolingOff()))
                ); // notFailedCoolingOff or is showing ServiceAbusePrompts
            }

            function _coolingOffValidated(coolingOff) {
                return !_.isNil(coolingOff.coolingOffOverride()) || !_.isNil(coolingOff.surchargeAccepted());
            }

            function showCuvUpgradePrompt(task, cr) {
                let promise = null;
                let prompt = StaticPromptService.getStaticPromptById(Prompt.UPGRADE_CUV);
                promise = addPrompt(prompt).then((prompt) => {
                    let result = prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes();
                    if (result && prompt.data().package) {
                        // set surcharge flag for homestart
                        // task.contractValidation().homestart().surchargeAccepted(true);
                        task.contractValidation().cuv().override(true);
                        //task.demandDeflection().isCUV('no');

                        return _upgradeEntitlement(task, cr, prompt.data().package, prompt.data().amount, prompt.data().remarks).then((success) => success);
                    } else {
                        if (result) {
                            // treat all as override as no other identification has been built in
                            task.contractValidation().cuv().override(true);
                        }
                        return result;
                    }
                });
            }

            _.extend(svc, {
                addPrompt,
                checkEntitlementStandby,
                validateRelay: (entitlement) => {
                    let deferred = $q.defer();
                    //Check that we are not already going though the contract validation prompt to avoid interferences between different rules
                    // as the whole contract validation will supersede the normal rules
                    if (!_hasPromptById(Prompt.CONTRACT_VALIDATION, _promptsToDisplay) && !entitlement.hasRelay()) {
                        //add prompt
                        if (entitlement.customerGroup().isAddedValueAccount()) {
                            getRefDataPrompt(19).then((prompt) => {
                                _buildPromptOptions(
                                    prompt,
                                    {
                                        name: 'Yes',
                                        optionType: PromptOption.OPTION_TYPE_YES
                                    },
                                    {
                                        name: 'No',
                                        optionType: PromptOption.OPTION_TYPE_NO
                                    }
                                );
                                addPrompt(prompt).then((prompt) => deferred.resolve(prompt.selectedOption().isYes()));
                            });
                        } else if (entitlement.customerGroup().isPersonal()) {
                            let prompt = StaticPromptService.getStaticPromptById(Prompt.PERSONAL_NOT_ENTITLED_FOR_RELAY);
                            addPrompt(prompt)
                                .then((prompt) => deferred.resolve(prompt.selectedOption().isYes()))
                                .catch((error) => deferred.reject(error));
                        }
                    } else {
                        deferred.resolve(true);
                    }
                    return deferred.promise;
                },
                clearPrompts: () => (_promptsToDisplay = []),
                promptsToDisplay: () => _promptsToDisplay,
                remove: (prompt) => {
                    if (prompt) {
                        _promptsToDisplay.splice(_promptsToDisplay.indexOf(prompt), 1);
                        if (prompt.id() === Prompt.NO_ENTITLEMENTS_FOUND || svc.hasOutstandingPrompts()) {
                            SidePanelService.updateStateByPrompt(false);
                        } else {
                            SidePanelService.updateStateByPrompt(true);
                        }
                        prompt.removeAllOptionHandlers();

                        if (prompt.ignoreOnce()) {
                            _ignoredPrompts.push(prompt);
                        }
                    }
                },
                reset: () => {
                    _promptsToDisplay = [];
                    _ignoredPrompts = [];
                },
                resetAllPromptsExcept: (id) => {
                    _.forEach([..._promptsToDisplay], (prompt) => {
                        if (prompt.id() !== id) {
                            svc.remove(prompt);
                        }
                    });
                    _ignoredPrompts = [];
                },
                resetAllPromptsExceptIds: (ids) => {
                    _.forEach([..._promptsToDisplay], (prompt) => {
                        if (ids.indexOf(prompt.id()) == -1) {
                            svc.remove(prompt);
                        }
                    });
                    _ignoredPrompts = [];
                },
                removeIgnoredPrompts: (ids) => {
                    if (ids.length) {
                        _ignoredPrompts = _ignoredPrompts.filter((prompt) => {
                            if (ids.indexOf(prompt.id()) == -1) {
                                return true;
                            }
                            return false;
                        });
                    } else {
                        _ignoredPrompts = [];
                    }
                },
                ignoredPrompts: () => _ignoredPrompts,
                isHondaAssistance: (task) => {
                    if (task.entitlement().customerGroup().isHondaAssistance()) {
                        let prompt = StaticPromptService.getStaticPromptById(Prompt.HONDA_ASSISTANCE_PROMPT);

                        prompt.data().isHondaPromptRead = false;
                        prompt.options()[0].disabled(!prompt.data().isHondaPromptRead);
                        prompt.data().toggleHondaPrompt = () => {
                            prompt.data().isHondaPromptRead = !prompt.data().isHondaPromptRead;
                            prompt.options()[0].disabled(!prompt.data().isHondaPromptRead);
                            return prompt.data().isHondaPromptRead;
                        };

                        return addPrompt(prompt).then(() => {
                            if (prompt.selectedOption().name() === 'Ok' && prompt.data().isHondaPromptRead) {
                                svc.removePromptById(Prompt.HONDA_ASSISTANCE_PROMPT);
                            }
                        });
                    }
                },
                checkEntitlement: (entitlement, task) => {
                    checkSpecialNeeds(entitlement);
                    return $q
                        .all([
                            checkNewMembership(entitlement),
                            checkMembershipWithinExclusion(task),
                            checkMembershipWithinCUVCoolOff(task),
                            checkMembershipExpiryDate(entitlement, task),
                            checkSuspendedMembership(entitlement, task),
                            checkEntitlementStandbyEarlyPrompt(entitlement),
                            svc.checkDemandDeflection(entitlement, task, { checkRoadAddon: true }),
                            svc.checkVulnerableCustomer(entitlement)
                        ])
                        .then(([newMembershipResolvedAndOk, membershipOutsideExclusionAndOk, expiryResolvedAndOk, suspendedResolvedOk, standbyResolvedAndOk]) => {
                            /* below line to be changed to
                             *  return membershipOutsideExclusionAndOk && expiryResolvedAndOk && suspendedResolvedOk && standbyResolvedAndOk
                             *  once checkNewMembership(entitlement) is removed
                             */
                            return (newMembershipResolvedAndOk || membershipOutsideExclusionAndOk) && expiryResolvedAndOk && suspendedResolvedOk; // && standbyResolvedAndOk;
                        });
                },
                checkVulnerableCustomer: (entitlementVal) => {
                    const entitlement = arguments.length ? entitlementVal : _getCSHService().entitlement();
                    if (!entitlement) {
                        return;
                    }
                    if (!_getEntitlementHelperService().isVulnerableCustomerHack(entitlement)) {
                        return;
                    }
                    if (_getTaskService().task().isCompleted()) {
                        return;
                    }
                    const entitlementOptions = entitlement.contact().options();

                    // add prompt template partial for vulnerable customer
                    let promptName = '';
                    if (entitlementOptions.vulnerability().isVulnerable()) {
                        promptName += 'Vulnerabilities';
                    }

                    // add prompt template partial for additional roadside assistance
                    if (entitlementOptions.additionalRoadsideSupport()) {
                        const arsText = 'Additional Road Support';
                        promptName += promptName.length ? ` & ${arsText}` : arsText;
                    }

                    const prompt = new Prompt({
                        name: promptName,
                        htmlFile: 'vulnerable-customer.html',
                        id: Prompt.VULNERABLE_CUSTOMER,
                        ignoreOnce: false,
                        data: {
                            vulnerability: entitlementOptions.vulnerability(),
                            additionalRoadsideSupport: entitlementOptions.additionalRoadsideSupport(),
                            additionalRoadsideSupportInformation: entitlementOptions.additionalRoadsideSupportInformation()
                        },
                        options: [
                            {
                                name: 'OK'
                            }
                        ]
                    });

                    return addPrompt(prompt);
                },
                checkNoServiceAccount: () => {
                    if (!_getEntitlementHelperService().isNoServiceAccount()) {
                        return;
                    }
                    _getCSHService().newCaseBtnDisabled(true);
                    _getCSHService().reattendBtnDisabled(true);
                    const prompt = new Prompt({
                        id: Prompt.NO_SERVICE_ACCOUNT,
                        name: 'NO SERVICE ACCOUNT',
                        html: 'DO NOT PROVIDE FREE SERVICE ON ACCOUNT <br><br> Please check Text Topics for further instructions',
                        ignoreOnce: true,
                        options: [
                            {
                                name: 'OK'
                            }
                        ]
                    });
                    return addPrompt(prompt);
                },
                checkHomestartEntitlement: (task, cr, entitlement) => {
                    let promise = null;
                    if (task.entitlement().hasHomestart()) {
                        _getCoolingOffPeriodPrompt(task, 'Homestart');
                    }

                    task.validateContract();
                    /* show prompt if
                     *   roadworthy and (passed coolingoff or is in SvcAbuse prompts )
                     *   or
                     *   pfu not required
                     *  */
                    if (
                        !task.entitlement().hasHomestart() &&
                        !(task.contractValidation().homestart().homestartOverride() || task.demandDeflection().homestartOverride()) &&
                        !task.entitlement().hasPFU() &&
                        !_getContractValidationService().isExcessSurchargeAccepted(task) &&
                        !task.createReason().isBatteryAssist() &&
                        !task.createReason().isFuelAssist() &&
                        !task.createReason().isKeyAssist()
                    ) {
                        let prompt = StaticPromptService.getStaticPromptById(Prompt.NOT_ENTITLED_TO_HOMESTART);

                        if (task.entitlement().customerGroup().isRoadsideAddOn()) {
                            prompt = StaticPromptService.getStaticPromptById(Prompt.ROADSIDE_ADDON_NOT_ENTITLED_TO_HOMESTART);
                        }

                        if (task.entitlement().customerGroup().code() === 'SAGA') {
                            prompt = StaticPromptService.getStaticPromptById(Prompt.SAGA_NOT_ENTITLED_TO_HOMESTART);
                        }

                        if (task.entitlement().customerGroup().isAdmiral()) {
                            prompt = StaticPromptService.getStaticPromptById(Prompt.ADMIRAL_PREVENT_HOMESTART_PROMPT);
                        }

                        prompt.data({
                            policy: entitlement.policy(),
                            years: () => {
                                //Useing startDate, if inceptionDate is null
                                return entitlement.policy().inceptionDate() !== null
                                    ? Math.floor((new Date() - entitlement.policy().inceptionDate().getTime()) / (365 * 24 * 60 * 60 * 1000))
                                    : Math.floor((new Date() - entitlement.policy().startDate().getTime()) / (365 * 24 * 60 * 60 * 1000));
                            }, //no of years member has been with us,
                            coolingOffPromptShown: () => {
                                return svc.isInSvcAbusePages() && _getContractValidationActionService().checkFailedCoolingOff();
                            }
                        });

                        promise = addPrompt(prompt).then((prompt) => {
                            let result = prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes();
                            if (result && prompt.data().package) {
                                // set surcharge flag for homestart
                                task.contractValidation().homestart().surchargeAccepted(true);
                                task.demandDeflection().homestartSurchargeAccepted(true);

                                return _upgradeEntitlement(task, cr, prompt.data().package, prompt.data().amount, prompt.data().remarks).then((success) => success);
                            } else {
                                if (result) {
                                    // treat all as override as no other identification has been built in
                                    task.contractValidation().homestart().homestartOverride(true);
                                    task.demandDeflection().homestartOverride(true);
                                }
                                return result;
                            }
                        });
                    }

                    return $q.when(promise || true);
                },
                isInSvcAbusePages: () => {
                    return _hasPromptById(Prompt.CONTRACT_VALIDATION, _promptsToDisplay);
                },
                getFaultCode: (fault) => {
                    let faultCode;
                    if (fault.name() && fault.code().name()) {
                        faultCode = fault.code().name();
                    } else {
                        faultCode = VehicleService.getVehicleFault(fault.id()).then(() => {
                            return VehicleService.vehicleFault().code().name();
                        });
                    }
                    return $q.when(faultCode);
                },
                /**
                 * Displays prompt if necessary
                 * @param {Task} task
                 * @param {CustomerRequest} cr
                 * @param {number} distance
                 */
                checkRecoveryDistance: (task, cr, distance) => {
                    if (!task.recovery().destination()) {
                        return Promise.resolve(true);
                    }

                    // if we already override the check
                    if (task.contractValidation().relay().relayOverride()) {
                        return Promise.resolve(true);
                    }

                    // if user paid already
                    if (_getContractValidationService().isExcessSurchargeAccepted(task)) {
                        return Promise.resolve(true);
                    }

                    // if already resolved prompt, bail out
                    if (task.demandDeflection().localRecoveryPassed()) {
                        return Promise.resolve(true);
                    }

                    return svc
                        .getFaultCode(task.fault())
                        .then((faultCode) => {
                            let faultName = task.fault().name();
                            _isRTCFault = faultName && faultName.toUpperCase().match(/RTC/) && faultCode.toUpperCase().match(/RTC/);

                            if (task.entitlement().hasRelay() && !(distance <= 10 || _isRTCFault || _getContractValidationService().isOutcomePFURequired(task))) {
                                _getCoolingOffPeriodPrompt(task, 'Relay');
                            }

                            if (
                                task.entitlement().hasRelay() ||
                                !distance ||
                                distance <= 10 ||
                                _isRTCFault ||
                                (!_roadworthyAndPassedCoolingOff(task) && _getContractValidationService().isOutcomePFURequired(task) && !task.entitlement().customerGroup().isAdmiral()) ||
                                (task.indicators().motorway() && task.indicators().dangerousLocation())
                            ) {
                                return true;
                            }
                            let prompt = StaticPromptService.getStaticPromptById(Prompt.LOCAL_RECOVERY_DISTANCE_EXCEEDED);

                            if (task.entitlement().customerGroup().isAdmiral()) {
                                prompt = StaticPromptService.getStaticPromptById(Prompt.ADMIRAL_LOCAL_RECOVERY_DISTANCE_EXCEEDED);
                            }

                            if (task.entitlement().customerGroup().isRoadsideAddOn()) {
                                prompt = StaticPromptService.getStaticPromptById(Prompt.ROADSIDE_ADDON_LOCAL_RECOVERY_DISTANCE_EXCEEDED);
                            }

                            prompt.data().recoveryDistance = distance;
                            prompt.data().isAdmiral = task.entitlement().customerGroup().isAdmiral();
                            prompt.data().coolingOffPromptShown = () => {
                                return svc.isInSvcAbusePages() && _getContractValidationActionService().checkFailedCoolingOff();
                            };
                            return addPrompt(prompt).then((prompt) => {
                                const isValid = prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes();

                                if (isValid && prompt.data().package) {
                                    // set surcharge flag for relay
                                    task.contractValidation().relay().surchargeAccepted(true);
                                    task.demandDeflection().relaySurchargeAccepted(true);

                                    return _upgradeEntitlement(task, cr, prompt.data().package, prompt.data().amount, prompt.data().remarks).then(() => {
                                        // persist result of the prompt
                                        task.demandDeflection().localRecoveryPassed(isValid);
                                        return true;
                                    });
                                } else {
                                    const optionProps = prompt.selectedOption().stateProps();
                                    // if payment amount in selected option lets preserve info about that in the task
                                    if (optionProps && optionProps.paymentAmount) {
                                        task.contractValidation().outcome().pfuQuoted(`£${optionProps.paymentAmount}`);
                                        task.contractValidation().outcome().pfuRequired(true);
                                        if (isValid) {
                                            task.contractValidation().outcome().pfuQuoteAccepted(true);
                                        } else {
                                            task.contractValidation().outcome().pfuQuoteAccepted(false);
                                        }
                                    }

                                    if (isValid) {
                                        // persist result of the prompt
                                        task.contractValidation().relay().relayOverride(true);
                                        task.demandDeflection().relayOverride(true);
                                        task.demandDeflection().localRecoveryPassed(isValid);
                                    }

                                    return isValid;
                                }
                            });
                        })
                        .then((success) => {
                            if (!success) {
                                _getTaskService().clearRecovery();
                                _getValidationService().adjustRecoveryDest(true);
                            }

                            return success;
                        });
                },
                checkVehicleChanged: (registration, entitlement) => {
                    let promise;

                    if (entitlement.vehicle().registration() && registration.toUpperCase()) {
                        if (entitlement.vehicle().registration().toUpperCase() != registration.toUpperCase()) {
                            let prompt = StaticPromptService.getStaticPromptById(Prompt.VEHICLE_CHANGED_CREATE_NEW_TASK);
                            promise = addPrompt(prompt).then((prompt) => prompt);
                        }
                    }

                    return $q.when(promise || true);
                },
                showVehicleChanged: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.VEHICLE_CHANGED_PROMPT);
                    let promise = addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());

                    return $q.when(promise || true);
                },
                showLocalRecoveryWarning: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.LOCAL_RECOVERY_WARN);
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                },
                showVehicleHasAdditionalPolicies: (task, numberOfWarrantyEntitlements) => {
                    let promise = null;
                    if ((task.status() === 'UNAC' || task.status() === 'INIT') && numberOfWarrantyEntitlements > 0) {
                        let prompt = StaticPromptService.getStaticPromptById(Prompt.VEHICLE_HAS_ADDITIONAL_POLICIES);
                        promise = addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                    }

                    return $q.when(promise || false); //returns promise which will resolve to a boolean value (default is false i.e. no additional policies)
                },
                processPromptAnswer: (prompt, option) => {
                    prompt.selectedOption(option);
                    svc.remove(prompt);
                },
                showNoEntitlementsFound: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.NO_ENTITLEMENTS_FOUND);
                    return addPrompt(prompt).then(() => true);
                },
                checkServiceMessage: async () => {
                    const taskService = _getTaskService();
                    await taskService.getTaskStatusText(taskService.task().id());
                    const taskWriteResponseService = _getTaskWriteResponseService();
                    let drm = taskWriteResponseService.getDRM();
                    let qualificationData = JSON.parse(sessionStorage.getItem('QualificationDetails'));
                    if (
                        drm &&
                        taskService.task().status() !== 'INIT' &&
                        qualificationData === null &&
                        !taskService.task().createReason().isRelayPlus() &&
                        !taskService.task().createReason().isHireCar()
                    ) {
                        const customerRingBackPrompt = new Prompt({
                            name: 'Customer Ring Back',
                            htmlFile: 'customer-ring-back.html',
                            id: Prompt.CUSTOMER_RING_BACK,
                            ignoreOnce: false,
                            data: { drm },
                            options: [
                                {
                                    optionType: PromptOption.OPTION_TYPE_YES,
                                    name: 'ETA Related Call',
                                    value: 'etacall'
                                },
                                {
                                    optionType: PromptOption.OPTION_TYPE_YES,
                                    name: 'Non ETA Related Call',
                                    value: 'non_etacall'
                                },
                                {
                                    name: 'Non Ring Back',
                                    optionType: PromptOption.OPTION_TYPE_NO,
                                    value: 'Non Ring Back'
                                }
                            ]
                        });
                        return addPrompt(customerRingBackPrompt).then((prompt) => {
                            if (prompt.selectedOption().value() === 'etacall') {
                                let callInfoMessage = 'ETA Ring back : The following script has been read to the customer:';
                                callInfoMessage = callInfoMessage + extractContent(drm.toString());
                                _getEntitlementHelperService().sendAuditInfo(
                                    taskService.task().id(),
                                    CallinfoType.GENERAL_REMARK,
                                    callInfoMessage,
                                    taskService.task().customerRequestId(),
                                    taskService.task()
                                );
                                taskService.isNewlyCreated(true);
                            }

                            if (prompt.selectedOption().value() === 'non_etacall') {
                                const ringBackReason = new Prompt({
                                    name: 'Ring Back Reason',
                                    htmlFile: 'ring-back-reason.html',
                                    id: Prompt.RING_BACK_REASON,
                                    ignoreOnce: false
                                });
                                let newcallInfoMessage;
                                ringBackReason.data().isRadioSelected = false;
                                ringBackReason.data().ringReason = (val) => {
                                    newcallInfoMessage = 'Non-ETA Ring back. Reason: ' + val;
                                    ringBackReason.data().isRadioSelected = true;
                                };
                                ringBackReason.data().confirmReason = () => {
                                    _getEntitlementHelperService().sendAuditInfo(
                                        taskService.task().id(),
                                        CallinfoType.GENERAL_REMARK,
                                        newcallInfoMessage,
                                        taskService.task().customerRequestId(),
                                        taskService.task()
                                    );
                                    svc.remove(prompt);
                                    taskService.isNewlyCreated(true);
                                };
                                return addPrompt(ringBackReason);
                            }

                            if (prompt.selectedOption().value() === 'Non Ring Back') {
                                svc.removePromptById(Prompt.CUSTOMER_RING_BACK);
                            }
                            return prompt;
                        });
                    }
                    sessionStorage.removeItem('QualificationDetails');
                },
                checkTaskIscompletedWithinSevenDays: (cshData) => {
                    const taskService = _getTaskService();
                    const last = taskService.getLastTasks(cshData);

                    if (last && last.lastCompletedInSevenDays) {
                        svc.showStaticPromptById(Prompt.TASK_COMPLETED_WITHIN_SEVEN_DAYS);
                    }
                },
                checkTaskCompletedWithinSevenDaysNoFLPRejected: (cshData) => {
                    const taskService = _getTaskService();
                    const records = taskService.getLastTasks(cshData);

                    if (!records.lastCompletedInSevenDays) {
                        return Promise.resolve();
                    }

                    const tasks = records.lastCompletedInSevenDays.tasks();
                    let latestBreakdownTask;

                    for (const task of tasks) {
                        if (task.taskType().code() !== 'BRK') {
                            continue;
                        }

                        if (!latestBreakdownTask) {
                            latestBreakdownTask = task;
                        } else if (task.schedule().create() > latestBreakdownTask.schedule().create()) {
                            latestBreakdownTask = task;
                        }
                    }

                    if (!latestBreakdownTask) {
                        return Promise.resolve();
                    }

                    const UACService = _getUACService();

                    // find rejected UAC for FLP for this task
                    const query = {
                        namespace: UACService.namespaces.FLP,
                        taskId: latestBreakdownTask.id(),
                        statuses: [UACService.statuses.DENIED],
                        skip: 0,
                        limit: 10
                    };

                    // search for the rejected UAC on the last task id
                    return UACService.list(query).then(({ results }) => {
                        if (!results.length) {
                            return;
                        }

                        const prompt = StaticPromptService.getStaticPromptById(Prompt.TASK_COMPLETED_WITHIN_SEVEN_DAYS_REJECTED_FLP);

                        prompt.data({ results });
                        return addPrompt(prompt);
                    });
                },
                checkHasLiveTask: (customerRequestHistory) => {
                    let promise = null;
                    if (_.some(customerRequestHistory, (task) => task.isActive())) {
                        let prompt = StaticPromptService.getStaticPromptById(Prompt.ADD_CASE_ON_A_LIVE_BREAKDOWN);
                        promise = addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                    }
                    return $q.when(promise || true); //returns promise which will resolve to a boolean value
                    //(default is true i.e. adding NewCase when there is no active breakdown)
                },
                showIncidentIndicatorPrompt: (incidentIndicator) => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.INCIDENT_INDICATOR);
                    prompt.data({
                        incidentIndicatorText: incidentIndicator.description(),
                        incidentDate: incidentIndicator.updateTimeStamp()
                    });
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isNo()); //clear incident indicator if selected option is 'No'
                },
                /**
                 *
                 * @param {number} id - the prompt id
                 * @param {object} promptData - the data to be used in the prompt html. Note: this
                 * will be carried across the prompt chain and can be used to add more data down the chain.
                 * @returns {promise} - resolves when prompt chain is complete
                 */
                showStaticPromptById: (id, promptData) => {
                    let prompt = StaticPromptService.getStaticPromptById(id);
                    if (promptData) {
                        prompt.data(promptData);
                    }
                    return addPrompt(prompt).then((prompt) => prompt);
                },
                hasOutstandingPrompts: () => _promptsToDisplay.length > 0,
                showDataChangedWarning: () => {
                    let promise = null,
                        prompt = StaticPromptService.getStaticPromptById(Prompt.DATA_CHANGED);

                    promise = addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                    return $q.when(promise || true);
                },
                showMemberHasNewTaskWarning: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.CASE_HAS_NEW_TASK);
                    return addPrompt(prompt).then((prompt) => true);
                },
                showRefDataPrompt: (id) => {
                    return getRefDataPrompt(id).then((prompt) => addPrompt(prompt).then((prompt) => true));
                },
                showState: (prompt) => {
                    let deferred = $q.defer(),
                        promptReferrer = new PromptReferrer(prompt, deferred);
                    _outstandingPromptReferrer = promptReferrer;
                    const stateProps = Object.assign({ promptReferrer }, prompt.selectedOption().stateProps());

                    $state.go(prompt.selectedOption().state(), stateProps);

                    return deferred.promise;
                },
                outstandingPromptReferrer: () => _outstandingPromptReferrer,
                ignorePrompt: (prompt) => _ignoredPrompts.push(prompt),
                eCallEvent: (details) => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.ECALL);
                    prompt.data(details);
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                },
                taskOutOfSequence: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.TASK_OUT_OF_SEQUENCE);
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                },
                taskOverride: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.TASK_OVERRIDE);
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                },
                vipIndicator: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.VIP_INDICATOR);
                    return addPrompt(prompt).then((prompt) => true);
                },
                showScheduleWindowOpen: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.SCHEDULE_WINDOW_OPEN);
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isNo());
                },
                showPFUScriptPrompt: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.PFU_SCRIPT);
                    return addPrompt(prompt).then((prompt) => true);
                },
                showNoResultsFound: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.NO_RESULTS_FOUND);
                    return addPrompt(prompt).then((prompt) => true);
                },
                electricVehicleprompt: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.ELECTRIC_VEHICLE_DIAGNOSTIC);
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                },
                showInaccurateLocation: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.INACCURATE_LOCATION);
                    return addPrompt(prompt).then((prompt) => true);
                },
                taskAllreadyCompleted: () => {
                    // need to do something here ...
                },
                showAgentLoggedOffPrompt: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.AGENT_LOGGED_OFF);
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                },
                showInvalidLocationPrompt: (type) => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.INVALID_LOCATION);
                    prompt.data(type);
                    return addPrompt(prompt).then((prompt) => true);
                },
                validateAddTaskEntitlement: (task, cr, entitlement, selectedReason) => {
                    let promise = null;
                    const MobilityTaskService = _getMobilityTaskService();

                    // JLR service check
                    if (MobilityTaskService.getBusinessRules().agileEnabled) {
                        promise = $q.defer();

                        promise.resolve(true);

                        return promise.promise;
                    }

                    if (selectedReason.isRelayPlus() && !task.entitlement().hasRelayPlus() && !entitlement.policy().customerGroup().isPFU()) {
                        //member is not entitled to relay plus - show service adjustment
                        let prompt = StaticPromptService.getStaticPromptById(Prompt.SERVICE_ADJUSTMENT_RELAY_PLUS);
                        prompt.data({
                            entitlement: task.entitlement(),
                            altContact: task.altContact(),
                            package: PackageService.getPackageByCode('RP')
                        });
                        promise = addPrompt(prompt)
                            .then((prompt) => {
                                let result = prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes();
                                if (result && prompt.data().package) {
                                    return _upgradeEntitlement(task, cr, prompt.data().package, prompt.data().amount, prompt.data().remarks).then((success) => success);
                                } else {
                                    return result;
                                }
                            })
                            .catch((err) => console.error(err));
                    }

                    return $q.when(promise || true);
                },
                removePromptById: (id) => {
                    let prompt = _.find(_promptsToDisplay, (prompt) => prompt.id() === id);
                    svc.remove(prompt);
                },
                cliEntitlementSearch: (cli, ddi) => {
                    return CliEntitlementsService.entitlementCliSearh(cli, ddi).then((outcome) => {
                        const prompt = StaticPromptService.getStaticPromptById(Prompt.CLI_ENTITLEMENT);
                        if (outcome) {
                            prompt.data({
                                entitlements: CliEntitlementsService.entitlements(),
                                cli: cli
                            });
                            return addPrompt(prompt);
                        } else {
                            return outcome;
                        }
                    });
                },
                addMembershipEntitlementPrompt: (entitlements) => {
                    const prompt = StaticPromptService.getStaticPromptById(Prompt.CTI_MEMBERSHIP_ENTITLEMENT);
                    prompt.data({
                        entitlements: CliEntitlementsService.entitlements()
                    });
                    return addPrompt(prompt);
                },
                checkBankCustomerForVehiclePrompt: (entitlement) => {
                    if (entitlement.policy().customerGroup().isBank()) {
                        switch (entitlement.policy().customerGroup().code()) {
                            case CustomerGroup.NBS:
                                svc.showStaticPromptById(Prompt.NO_REFUSE_NBS_CUSTOMERS);
                                break;
                            case CustomerGroup.NWGB:
                            case CustomerGroup.NWGP:
                                svc.showStaticPromptById(Prompt.NO_REFUSE_NWG_CUSTOMERS);
                                break;
                            case CustomerGroup.RBSB:
                            case CustomerGroup.RBSP:
                                svc.showStaticPromptById(Prompt.NO_REFUSE_RBS_CUSTOMERS);
                                break;
                            case CustomerGroup.CHAS:
                                svc.showStaticPromptById(Prompt.CUSTOMER_NOT_WITH_VEHICLE_CHAS);
                                break;
                            default:
                                svc.showStaticPromptById(Prompt.NO_REFUSE_LBG_CUSTOMERS);
                                break;
                        }
                    }
                },
                SpecialistVeh: (entitlement) => {
                    if (entitlement.products()) {
                        for (let vehname in entitlement.products()) {
                            if (entitlement.products()[vehname].name().includes('Specialist Veh')) {
                                svc.showStaticPromptById(Prompt.SPECIALIST_VEH);
                            }
                        }
                    }
                },
                MembershipNotFound: () => {
                    svc.showStaticPromptById(Prompt.MEMBERSHIP_SEARCH_NOTFOUND);
                },
                showSaveTaskWithoutContactNumber: () => {
                    return svc.showStaticPromptById(Prompt.SAVE_TASK_WITHOUT_CONTACT_NUMBER);
                },
                showChangeInsuranceOptionPrompt: () => {
                    return svc.showStaticPromptById(Prompt.CHANGE_INSURANCE_OPTION).then((prompt) => {
                        return prompt.selectedOption().isYes();
                    });
                },
                checkBankCustomerHasUpgrade: (entitlement) => {
                    if (
                        entitlement.policy().customerGroup().code() === CustomerGroup.CHAS ||
                        (entitlement.policy().customerGroup().isLloydsUpgrade() &&
                            (entitlement.policy().customerGroup().code() === CustomerGroup.HALI ||
                                _.difference(
                                    ['R', 'H'],
                                    entitlement.benefits().map((val) => val.code())
                                ).length !== 0))
                    ) {
                        svc.showStaticPromptById(Prompt.CHECK_BANK_CUSTOMER_HAS_UPGRADE);
                    }
                },
                vehicleGrossWeightOverLimit: (customerGroupCode) => {
                    let prompt;
                    if (customerGroupCode === CustomerGroup.NBS) {
                        prompt = StaticPromptService.getStaticPromptById(Prompt.NBS_GROUP_VEHICLE_GROSS_WEIGHT_OVER_LIMIT);
                    } else {
                        prompt = StaticPromptService.getStaticPromptById(Prompt.VEHICLE_GROSS_WEIGHT_OVER_LIMIT);
                    }
                    return addPrompt(prompt).then((prompt) => true);
                },
                vehicleAdmiralGrossWeightOverLimit: () => {
                    prompt = StaticPromptService.getStaticPromptById(Prompt.ADMIRAL_VEHICLE_GROSS_WEIGHT_OVER_LIMIT);
                    return addPrompt(prompt).then((prompt) => true);
                },
                ROI: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.ROI);
                    return addPrompt(prompt).then((prompt) => true);
                },
                resetOutcomeObject: (task) => {
                    if (task.contractValidation()) {
                        const repeatFaultPassed = task.contractValidation().repeatFault().passed();
                        const repeatFaultPayment = task.contractValidation().repeatFault().surchargeAccepted();
                        const excessivePayment = task.contractValidation().excessiveUse().surchargeAccepted();
                        const coolOffPayment = task.contractValidation().coolingOff().surchargeAccepted();
                        const secRecoveryPayment = task.contractValidation().secRecovery().surchargeAccepted();
                        const repeatFaultOverride = task.contractValidation().repeatFault().override();
                        const cuvPayment = task.contractValidation().cuv().surchargeAccepted();
                        const cuvOverride = task.contractValidation().cuv().override();
                        const noVRN = task.contractValidation().vrn().noVRN();
                        const vrnPassed = task.contractValidation().vrn().passed();
                        const vrnOverride = task.contractValidation().vrn().override();
                        let cuvBackup;
                        // check if reset due to vrn changes - if not, preserve cuv results related to prev check
                        if (task.contractValidation().cuv().vrn() === task.vehicle().registration()) {
                            cuvBackup = task.contractValidation().cuv().toJSON();
                        }

                        task.contractValidation().resetOutcome();
                        task.contractValidation().repeatFault().passed(repeatFaultPassed);
                        task.contractValidation().repeatFault().surchargeAccepted(repeatFaultPayment);
                        task.contractValidation().excessiveUse().surchargeAccepted(excessivePayment);
                        task.contractValidation().coolingOff().surchargeAccepted(coolOffPayment);
                        task.contractValidation().secRecovery().surchargeAccepted(secRecoveryPayment);
                        task.contractValidation().repeatFault().override(repeatFaultOverride);
                        task.contractValidation().vrn().noVRN(noVRN);
                        task.contractValidation().vrn().passed(vrnPassed);
                        task.contractValidation().vrn().override(vrnOverride);

                        // if we got full cuv object backed up, restore it else restore payment info only
                        if (cuvBackup) {
                            task.contractValidation().cuv(new Cuv(cuvBackup));
                        } else {
                            task.contractValidation().cuv().surchargeAccepted(cuvPayment);
                            task.contractValidation().cuv().override(cuvOverride);
                        }
                    }
                    if (_hasPromptById(Prompt.CONTRACT_VALIDATION, _promptsToDisplay)) {
                        svc.removePromptById(Prompt.CONTRACT_VALIDATION);
                    }
                    if (Object.values(ContractValidationConstants.COMPLETION_CODES).includes(task.fault().outcome().completionCode())) {
                        task.fault().outcome().completionCode(null);
                    }
                    svc.clearPreviouslySelectedServiceBuseRemark(task);
                },
                resolvePromiseForServiceAbusePrompt: () => {
                    if (_resolvePromiseServiceAbuse) {
                        _resolvePromiseServiceAbuse.promiseValue.resolve(_resolvePromiseServiceAbuse.promptObject);
                    }
                },
                clearPreviouslySelectedServiceBuseRemark: (task) => {
                    //To clear previously added remark into task object.
                    if (_getContractValidationActionService().getServiceAbuseRemarkBeforeSend()) {
                        if (task.location().remarks() && task.location().remarks().indexOf(_getContractValidationActionService().getServiceAbuseRemarkBeforeSend()) !== -1) {
                            task.location().remarks(task.location().remarks().replace(_getContractValidationActionService().getServiceAbuseRemarkBeforeSend(), '').trim());
                            _getContractValidationActionService().getServiceAbuseRemarkBeforeSend('');
                        }
                    }
                },
                clearCoolingOffOutcomes: (task, productName) => {
                    if (_getContractValidationService().isToggledOn() && task.contractValidation()) {
                        task.contractValidation().coolingOff().coolingOffOverride(null);
                        if (task.contractValidation().coolingOff().product() && task.contractValidation().coolingOff().product().toUpperCase() === productName.toUpperCase()) {
                            task.contractValidation().coolingOff().passed(null);
                            task.contractValidation().coolingOff().product(null);
                        }
                    }
                },
                showWillJoinTaskPrompt: (task) => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.WILL_JOIN_TASK);
                    prompt.data({
                        memberName: task.entitlement().memberName(),
                        vrn: task.vehicle().registration(),
                        fault: task.fault().name(),
                        location: task.location().area(),
                        task: task
                    });
                    prompt.options()[0].disabled(true);
                    return addPrompt(prompt);
                },
                hasWillJoinPrompt() {
                    return _hasPromptById(Prompt.WILL_JOIN_TASK, _promptsToDisplay);
                },
                toggleMergeOptionWillJoin(enableMerge) {
                    if (svc.hasWillJoinPrompt()) {
                        _promptsToDisplay
                            .find((prompt) => prompt.name() === 'Digital Job - Policy Not Located')
                            .options()[0]
                            .disabled(enableMerge);
                    }
                },
                checkCorrespondenceAddressInvalid: (task) => {
                    return new Promise((resolve, reject) => {
                        let _prompt = StaticPromptService.getStaticPromptById(Prompt.CORRESPONDENCE_ADDRESS);
                        let ANK_ACTION_TAKEN;

                        if (task.indicators().dangerousLocation() || task.indicators().motorway()) {
                            _prompt.options()[0].disabled(false);
                            _prompt.options()[1].disabled(true);
                            ANK_ACTION_TAKEN = 'SMS SENT';
                        } else {
                            _prompt.options()[0].disabled(true);
                            _prompt.options()[1].disabled(false);
                            ANK_ACTION_TAKEN = 'Updated TIA/CATHIE';
                        }
                        addPrompt(_prompt)
                            .then((prompt) => {
                                if (ANK_ACTION_TAKEN) {
                                    task.miscFields().ANKactionTaken(ANK_ACTION_TAKEN);
                                }

                                resolve(prompt.selectedOption().isYes());
                            })
                            .catch((error) => {
                                reject(error);
                            });
                    });
                },
                checkCorrespondenceAddressInvalidJointMember: (task) => {
                    return new Promise((resolve, reject) => {
                        let _prompt = StaticPromptService.getStaticPromptById(Prompt.CORRESPONDENCE_ADDRESS_JOINT_MEMBER);

                        addPrompt(_prompt)
                            .then((prompt) => {
                                resolve(prompt.selectedOption().isYes());
                            })
                            .catch((error) => {
                                reject(error);
                            });
                    });
                },
                vehicleBasedPolicyCorrespondenceAddressCheck: (task) => {
                    return new Promise((resolve, reject) => {
                        let _prompt = StaticPromptService.getStaticPromptById(Prompt.CORRESPONDENCE_ADDRESS_VEHICLE_BASED_MEMBER);

                        addPrompt(_prompt)
                            .then((prompt) => {
                                resolve(prompt.selectedOption().isYes());
                            })
                            .catch((error) => {
                                reject(error);
                            });
                    });
                },
                taskMerged: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.TASK_MERGE);
                    return addPrompt(prompt).then((prompt) => true);
                },
                showDangerousLocationJunctionPrompt: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.DANGEROUS_LOCATION_ENTRY_JUNCTION);
                    return addPrompt(prompt);
                },
                dualCheckCoverPrompt: () => {
                    return svc.showStaticPromptById(Prompt.DUAL_COVER_CHECK).then((prompt) => {
                        return prompt.selectedOption().isYes();
                    });
                },
                showLocalDriverPrompt: (task) => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.LOCAL_DRIVER_PROMPT);
                    prompt.data({
                        entitlement: task.entitlement(),
                        paymentReasonId: LimitedMembershipPaymentReasonsConstants.LOCAL_DRIVER[0],
                        limitedMembershipPaymentReasons: LimitedMembershipPaymentReasonsConstants.LOCAL_DRIVER
                    });
                    return addPrompt(prompt).then((prompt) => {
                        return prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes();
                    });
                },
                showCallOutHistoryLatePromp: (task) => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.CALLOUT_HISTORY_LATTER_PROMPT);
                    prompt.data({
                        entitlement: task.entitlement(),
                        altContact: task.altContact()
                    });
                    return addPrompt(prompt).then((prompt) => {
                        return prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes();
                    });
                },
                showStanbyEarlyPrompt: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.STANDBY_EARLY_PROMPT);
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                },
                checkMileageExistsPrompt: () => {
                    svc.showStaticPromptById(Prompt.CHECK_MILEAGE_EXISTS);
                },
                showNoSlotAcceptableWarning: () => {
                    const prompt = StaticPromptService.getStaticPromptById(Prompt.NO_SLOT_ACCEPTABLE);
                    return addPrompt(prompt).then((prompt) => prompt.selectedOption().isYes());
                },
                checkDemandDeflection: (entitlement, task, options = {}) => {
                    const {
                        checkRoadAddon = false,
                        checkCalloutHistory = true,
                        checkSecondRecovery = true,
                        checkRepeatFault = true,
                        checkCuv = true,
                        forceCheck = false,
                        showUpgradeCUV = false
                    } = options;

                    // Bail out if:
                    // - if non-validation flow (there is no entitlement available)
                    // - if task completed
                    // - if will-join (we don't check DD)
                    if (!entitlement || task.isCompleted() || _getEligibilityService().isWillJoin()) {
                        return Promise.resolve();
                    }

                    if (
                        task.demandDeflection().isCUV() === 'yes' &&
                        !task.contractValidation().cuv().override() &&
                        !task.location().remarks().includes(ContractValidationConstants.CONSTANT_BUILDER.CUVPAYMENTCONFIRM)
                    ) {
                        let vehicleVrnListed = false;
                        let cuvEntitlementExists = false;
                        let vehicles = _getCSHService().getProducts(_getCSHService().entitlement().products());
                        vehicles.filter((vehicle) => {
                            if (vehicle.benefitCode() === 'CUV') {
                                cuvEntitlementExists = true;
                                let coveredVehicles = vehicle.coveredVehicle().map((v) => v.toUpperCase());
                                if (coveredVehicles.length && coveredVehicles.includes(task.vehicle().registration().toUpperCase())) {
                                    vehicleVrnListed = true;
                                }
                            }
                        });

                        if (cuvEntitlementExists) {
                            vehicleVrnListed ? '' : showCuvUpgradePrompt(_getTaskService().task(), _getCSHService().csh());
                        } else {
                            showUpgradeCUV ? showCuvUpgradePrompt(_getTaskService().task(), _getCSHService().csh()) : '';
                        }
                    }
                    //there are situation where we NEED to check. Like when task is created with some other fault and then changed here to RepeatBattery.
                    if (!forceCheck) {
                        //Bail out if:
                        // - status is not INIT, UNAC
                        // not sure if we shouldn't retrieve the count from the task_json and let the flow proceed instead. Let's see what the testers have to say
                        if (!['INIT', 'UNAC'].includes(task.status())) {
                            return Promise.resolve();
                        }
                    }
                    const checks = [];
                    if (checkCuv) {
                        checks.push(DemandDeflectionService.checkCuv(entitlement, task, task.vehicle().registration()));
                    } else {
                        checks.push(Promise.resolve(undefined));
                    }
                    if (checkCalloutHistory) {
                        checks.push(DemandDeflectionService.checkCalloutHistory(entitlement));
                    } else {
                        checks.push(Promise.resolve(undefined));
                    }

                    if (checkSecondRecovery && task.recovery().destination().isSet()) {
                        // if current task is a recovery, add second recovery check
                        checks.push(DemandDeflectionService.checkSecondRecovery(entitlement, task, task.vehicle().registration()));
                    } else {
                        checks.push(Promise.resolve(undefined));
                    }

                    if (checkRepeatFault) {
                        checks.push(DemandDeflectionService.checkRepeatFault(entitlement, task, task.vehicle().registration()));
                    } else {
                        checks.push(Promise.resolve(undefined));
                    }

                    return Promise.all(checks).then(([cuvResult, calloutHistoryResult, secRecoveryResult, repeatFaultResult]) => {
                        if (cuvResult) {
                            task.demandDeflection().cuvPassed(cuvResult.passed);
                            task.contractValidation().cuv().passed(cuvResult.passed);
                            if (!cuvResult.passed) {
                                const entitlement = _getCSHService().entitlement();
                                const membership = entitlement ? entitlement.policy().membershipNumber() : '';
                                const vrn = _getTaskService().task().vehicle().registration();

                                CuvService.getCUV(membership, vrn).then((entity) => {
                                    task.contractValidation().cuv().assocTaskId(entity.taskId);
                                    task.contractValidation().cuv().vrn(entity.vrn);
                                    task.contractValidation().cuv().created(entity.created);
                                });
                            }
                        }
                        if (calloutHistoryResult) {
                            task.demandDeflection().calls(calloutHistoryResult.deployedCases);
                            task.demandDeflection().maxCallouts(calloutHistoryResult.maxCallouts);
                        }

                        if (secRecoveryResult) {
                            // apply results for the second recovery check
                            task.contractValidation().secRecovery().passed(secRecoveryResult.passed);
                            task.contractValidation()
                                .secRecovery()
                                .assocTaskId(secRecoveryResult.taskId || -1);
                        } else {
                            task.contractValidation().secRecovery().passed(true);
                        }

                        if (repeatFaultResult) {
                            task.demandDeflection().repeatFaultPassed(repeatFaultResult.passed);
                            task.contractValidation().repeatFault().passed(repeatFaultResult.passed);
                            task.contractValidation().repeatFault().assocTaskId(repeatFaultResult.taskId);
                            task.contractValidation().repeatFault().daysChecked(repeatFaultResult.daysChecked);
                            task.contractValidation().repeatFault().faultCode(task.fault().code());
                        }

                        task.validateContract();

                        let prompt;

                        // if additional task in CR but not reattend
                        if (isAdditionalTask(task, _getCSHService().csh().tasks()) && !task.isReattend()) {
                            task.contractValidation().relay().relayOverride(true);
                            task.demandDeflection().relayOverride(true);
                            task.contractValidation().homestart().homestartOverride(true);
                            task.demandDeflection().homestartOverride(true);
                            task.demandDeflection().cuvPassed(true);
                            task.contractValidation().cuv().passed(true);
                            if (checkCalloutHistory) {
                                // if DD detected set an overwrite for additional task / reattend in the same case,
                                // first task already passed similar this checks
                                if (task.contractValidation().excessiveUse().passed() === false) {
                                    // set override to true as we don't want to mess with MI reports
                                    task.contractValidation().excessiveUse().calloutOverride(true);
                                }
                            }

                            if (checkRepeatFault) {
                                // if DD detected set an overwrite for additional task / reattend in the same case,
                                if (task.contractValidation().repeatFault().passed() === false) {
                                    task.contractValidation().repeatFault().override(true);
                                }
                            }
                        }

                        if (checkRoadAddon && !isAdditionalTask(task, _getCSHService().csh().tasks()) && !task.isReattend()) {
                            const validationResult = task.contractValidation();
                            // if excessive usage detected
                            if (!validationResult.passedExcessiveUse()) {
                                // check for excessive use only if task not deployed and should be road assistance
                                if (task.createReason().serviceType() === 'RSS' && [Task.UNAC_STATUS, Task.INIT_STATUS].includes(task.status())) {
                                    if (!(validationResult.excessiveUse().surchargeAccepted() || validationResult.excessiveUse().calloutOverride())) {
                                        if (task.entitlement().customerGroup().isRoadsideAddOn()) {
                                            prompt = StaticPromptService.getStaticPromptById(Prompt.ROADSIDE_ADDON_EXCESSIVE_USE_NOTICE);
                                            prompt.data(calloutHistoryResult);
                                        }
                                        if (task.entitlement().customerGroup().isCDLVRoadsideAddOn()) {
                                            prompt = StaticPromptService.getStaticPromptById(Prompt.ROADSIDE_ADDON_EXCESSIVE_USE_NOTICE);
                                            prompt.data(calloutHistoryResult);
                                        }
                                    }
                                }
                            }
                        }

                        if (!prompt && entitlement.policy().customerGroup().isUBER() && calloutHistoryResult && !calloutHistoryResult.allowed) {
                            prompt = StaticPromptService.getStaticPromptById(Prompt.CALLOUT_HISTORY_PROMPT);
                        }

                        if (prompt) {
                            return addPrompt(prompt).then((prompt) => {
                                return prompt.selectedOption().state() ? prompt.selectedOption().stateResolution() : prompt.selectedOption().isYes();
                            });
                        }
                    });
                },
                checkLocationType: (task) => {
                    const destResourceId = task.recovery().destResourceId();

                    if (!task.recovery().destination().isSet()) {
                        return Promise.resolve();
                    }

                    if (destResourceId !== null && typeof destResourceId !== 'undefined') {
                        return Promise.resolve();
                    }

                    //if ([-1, -2, -3, -8].includes(destResourceId)) {
                    //    return Promise.resolve();
                    //}

                    let prompt = new Prompt({
                        name: 'Destination Location Category',
                        htmlFile: 'location-relay-options.html',
                        id: 31415,
                        ignoreOnce: false,
                        data: {
                            task
                        },
                        options: [
                            {
                                optionType: PromptOption.OPTION_TYPE_NO,
                                name: 'Cancel'
                            },
                            {
                                optionType: PromptOption.OPTION_TYPE_YES,
                                name: 'Save'
                            }
                        ]
                    });
                    return addPrompt(prompt).then((prompt) => {
                        if (prompt.selectedOption().isYes()) {
                            return Promise.resolve();
                        }
                        task.recovery().destResourceId(destResourceId);
                        return Promise.reject();
                    });
                },
                showCompassNoAuthorizationPrompt: (tracking, restartCallBack) => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.COMPASS_NO_AUTHORIZATION);
                    let os = 'other';
                    if (tracking && tracking.deviceType && tracking.deviceType.os && ['mac', 'android', 'windows', 'ios'].includes(tracking.deviceType.os.toLowerCase())) {
                        os = tracking.deviceType.os.toLowerCase();
                    }

                    prompt.data({
                        os,
                        userName: UserInfoService.userInfo().userName()
                    });
                    return addPrompt(prompt).then((prompt) => {
                        restartCallBack();
                    });
                },
                showConflictingRecordsFound: () => {
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.CONFLICTING_RECORDS_FOUND);
                    return addPrompt(prompt).then((prompt) => true);
                },
                showInvalidVehicleVRNPrompt: () => {
                    let promise;
                    let prompt = StaticPromptService.getStaticPromptById(Prompt.INVALID_VEHICLE_VRN);

                    const taskService = _getTaskService();
                    //Eurohelp BRK vehicles are non-uk vehicles. So we wont get experian details. Skip this prompt for EuroHelp
                    if (!taskService.task().entitlement().customerGroup().isEuroHelp()) {
                        promise = addPrompt(prompt).then((prompt) => true);
                    }

                    return $q.when(promise || true);
                }
            });
        }
    ])
    .config([
        '$qProvider',
        function ($qProvider) {
            $qProvider.errorOnUnhandledRejections(false);
        }
    ]);
