var _ = require('lodash');
require('angular');

// models
var AdvancedMembershipQuery = require('@aa/malstrom-models/lib/advanced-membership-query.model');
var MembershipQuery = require('@aa/malstrom-models/lib/membership-query.model');
var VehicleQuery = require('@aa/malstrom-models/lib/vehicle-query.model');
var Entitlement = require('@aa/malstrom-models/lib/entitlement.model');
var Vehicle = require('@aa/malstrom-models/lib/vehicle.model');
var Company = require('@aa/malstrom-models/lib/company.model');
var AdvancedCustomerRequestQuery = require('@aa/malstrom-models/lib/advanced-customer-request-query.model');
var CustomerGroup = require('@aa/malstrom-models/lib/customer-group.model');

module.exports = angular
    .module('aah-search-service-module', [
        // constants
        require('../../constants/search/search-urls.constants').name,
        require('../../constants/specific-search-terms/specific-search-terms.constants').name,
        require('../../constants/alert/alert-type.constants').name,

        //models
        require('../../models/location-query.factory').name,

        // services
        require('../../services/mapping/mapping.service').name,
        require('../address-lookup/address-lookup.service').name,
        require('../../services/alert/alert.service').name,
        require('../../services/vehicle/vehicle.service').name,
        require('../../services/mapping/google/google.service').name,
        require('../../services/mapping/location/location.service').name,
        require('../service-type/service-type.service').name,
        require('../customer-group/customer-group.service').name,
        require('../csh/csh.service').name,
        require('../prompt/prompt.service').name,
        require('../create-reasons/create-reasons.service').name
    ])
    .service('aahSearchService', [
        // Angular
        '$http',
        '$q',
        '$injector',
        '$state',

        // Constants
        'aahSearchURLs',
        'aahSpecificSearchTermsConstants',

        //Models
        'aahLocationQueryFactory',

        //Services
        'aahMappingService',
        // 'aahSecondLevelValidationSearchService',
        'aahAddressLookupService',
        'aahAlertService',
        'aahVehicleService',
        'aahLocationService',
        'aahServiceTypeService',
        'aahCustomerGroupService',
        'aahCSHService',
        'aahPromptService',
        'aahCreateReasonsService',
        'uibAlertTypes',
        'aahGoogleService',
        function SearchService(
            $http,
            $q,
            $injector,
            $state,
            SearchURLs,
            SpecificSearchTermsConstants,
            LocationQuery,
            MappingService,
            AddressLookupService,
            AlertService,
            VehicleService,
            LocationService,
            ServiceTypeService,
            CustomerGroupService,
            CSHService,
            PromptService,
            CreateReasonService,
            AlertTypes,
            GoogleService
        ) {
            var svc = this,
                _membershipSearch = new MembershipQuery(),
                _entitlements = [],
                _companies = [],
                _warrantyEntitlements = [],
                _warrantyEntitlementsForDisplay = [],
                _vehicleSearch = new VehicleQuery(),
                _vehicles = [],
                _vehicleResults = [],
                _locationSearch = new LocationQuery(),
                _locationResults = {
                    compiled: []
                },
                _searchTaskByQueryParam = false,
                _isAdvancedMembershipSearchVisible = false,
                _w3wlocationFound = false,
                _isinvalidW3search = false,
                _hasConflictingVehicleData = false;

            let _getContextMenuService = () => {
                return $injector.get('aahContextMenuService');
            };

            let _getUIService = () => {
                return $injector.get('aahUIService');
            };

            function processAndPopulateEntitlements(entitlements) {
                // Step 1: Create main object for all results
                _.forEach(entitlements, function convertToEntitelment(item, idx) {
                    _entitlements.push(new Entitlement(item));
                });

                return _entitlements;
            }

            function processAndPopulateCompanies(companies) {
                _.forEach(companies, function convertToCompanies(item, idx) {
                    _companies.push(new Company(item));
                });

                return _companies;
            }

            function _resetLocationResults() {
                _.forEach(MappingService.googleMarkers(), function (marker) {
                    MappingService.map().removeMarker(marker);
                });

                _locationResults = {
                    compiled: []
                };
                _w3wlocationFound = false;
            }

            _.extend(svc, {
                reset: function reset() {
                    svc.resetMembershipSearchModel();
                    _entitlements = [];
                    _companies = [];
                    _vehicleSearch.search('');
                    _vehicles = [];
                    _vehicleResults = [];
                    _warrantyEntitlements = [];
                    _warrantyEntitlementsForDisplay = [];
                    _locationSearch.search('');
                    _resetLocationResults();
                },

                resetMembershipSearchModel: function resetMembershipSearchModel() {
                    _membershipSearch.search('');
                    _membershipSearch.param().inPostcode('');
                    _membershipSearch.param().outPostcode('');
                    _membershipSearch.param().membershipNumber('');
                    _membershipSearch.param().vehicleRegistrationNumber('');
                    _membershipSearch.param().policyId('');
                    _membershipSearch.param().vehicleIdentificationNumber('');
                    _membershipSearch.param().surname('');
                    _membershipSearch.param().initials('');
                    _membershipSearch.param().town('');
                    _membershipSearch.param().companyName('');
                    _membershipSearch.param().cardNumber('');
                    _membershipSearch.param().page(1);
                    _membershipSearch.param().pageSize(20);
                    _membershipSearch.param().customerGroupCode(null);
                },

                /**
                 * Performs request to load memberships - entitlements/companies.
                 * @param {boolean} [isInfiniteSearch=false] - whether to treat this as additional search or new search e.g. append or replace result
                 * @returns {*}
                 */
                membershipSearch: function membershipSearch(isInfiniteSearch) {
                    if (!isInfiniteSearch) {
                        _entitlements.length = 0;
                        _companies.length = 0;
                        _membershipSearch.param().page(0); //reset
                    } else {
                        //check if there are any more pages, if so ,continue with search otherwise avoid searching
                        if (!_membershipSearch.param().page() || _membershipSearch.param().page() === 0) {
                            return;
                        }
                    }

                    //make sure we clear the alert messages and prompts from previous membership search..
                    AlertService.removeAllAlerts();

                    // return svc.entitlementCliSearh('07879813445', 'PERS');

                    return $http
                        .post(
                            SearchURLs.MEMBERSHIP_SEARCH_URL,
                            {
                                membershipSearch: _membershipSearch.toJSON()
                            },
                            {
                                notify: true,
                                friendly: 'Entitlement search'
                            }
                        )
                        .then(function membershipSearchSuccess(response) {
                            var entitlementResults = response.data.results;

                            if (response.data.query) {
                                _membershipSearch.param(new AdvancedMembershipQuery(response.data.query));
                            }

                            if (entitlementResults) {
                                svc.isAdvancedMembershipSearchVisible(false);
                                if (entitlementResults.entitlements) {
                                    _membershipSearch.param().page(entitlementResults.nextPage);
                                    return processAndPopulateEntitlements(entitlementResults.entitlements);
                                }

                                if (entitlementResults.companies) {
                                    _membershipSearch.param().page(entitlementResults.nextPage);
                                    return processAndPopulateCompanies(entitlementResults.companies);
                                }
                            }
                        });
                },
                toggleAdvancedMembershipSearch: () => {
                    _membershipSearch.search('');
                    svc.isAdvancedMembershipSearchVisible(!svc.isAdvancedMembershipSearchVisible());
                    if (svc.isAdvancedMembershipSearchVisible()) {
                        _membershipSearch.param().customerGroupCode(CustomerGroupService.customerGroup().code());
                    } else {
                        _membershipSearch.param().customerGroupCode(null);
                    }
                },
                //TODO rename this to membershipQuery
                membershipSearchTerm: function membershipSearchTermAcessor(val) {
                    return arguments.length ? (_membershipSearch = val) : _membershipSearch;
                },

                getMembershipResults: function getMembershipResults() {
                    return _entitlements;
                },
                getCompanyResults: function getCompanyResults() {
                    return _companies;
                },
                warrantyEntitlement: function warrantyEntitlementAccessor(val) {
                    return arguments.length ? (_warrantyEntitlements = val) : _warrantyEntitlements;
                },
                getEntitlementByCR: function getEntitlementByCR(cr, timeout) {
                    var crEntitlements = {
                            unListedSlvEntitlement: null,
                            entitlements: []
                        },
                        vehicleRegNo = null;
                    if (cr.assistType().customerGroup().code() === CustomerGroup.UBE) {
                        const firstTaskWithVehicle = cr.tasks().find((task) => task.vehicle);
                        vehicleRegNo = (firstTaskWithVehicle && firstTaskWithVehicle.vehicle().registration()) || null;
                    }
                    if (cr.assistType().customerGroup().code() === CustomerGroup.ADM) {
                        const firstTaskWithVehicle = cr.tasks().find((task) => task.vehicle);
                        vehicleRegNo = (firstTaskWithVehicle && firstTaskWithVehicle.vehicle().registration()) || null;
                    }

                    return $http
                        .post(
                            SearchURLs.ENTITLEMENT_BY_CR,
                            {
                                cr: cr.toJSONExcludeTasks(),
                                vehicleReg: vehicleRegNo
                            },
                            {
                                notify: true,
                                friendly: 'Entitlement by customer request',
                                timeout: timeout || 10000
                            }
                        )
                        .then(
                            function getEntitlementByCRSuccess(response) {
                                crEntitlements.unListedSlvEntitlement = response.data.unListedSlvEntitlement ? new Entitlement(response.data.unlistedSlvEntitlement) : null;

                                _.forEach(response.data.entitlements, function (eItem) {
                                    crEntitlements.entitlements.push(new Entitlement(eItem));
                                });
                                _entitlements = crEntitlements.entitlements;
                                return crEntitlements;
                            },
                            function getEntitlementByCRError(errors) {
                                CSHService.newCaseBtnDisabled(false);
                                CSHService.reattendBtnDisabled(false);
                                AlertService.createAlert('Failed to find an entitlement', AlertTypes.DANGER);
                            }
                        );
                },

                /**
                 * vehicle warranty search
                 * @param  {string} vrn vehicle registration no
                 * @return {Array.Entitlement}     [description]
                 */
                vehicleWarrantySearch: function vehicleWarrantySearch(currentPolicyCustomerGroupCode) {
                    var regNo = _vehicleSearch.search() ? _vehicleSearch.search().toLowerCase() : _vehicleSearch.search();
                    var vehicleMembership = new MembershipQuery({
                        param: {
                            vehicleRegistrationNumber: regNo
                        }
                    });

                    return $http
                        .post(
                            SearchURLs.MEMBERSHIP_SEARCH_URL,
                            {
                                membershipSearch: vehicleMembership.toJSON()
                            },
                            {
                                notify: true,
                                friendly: 'Vehicle warranty search'
                            }
                        )
                        .then(function vehicleWarrantySearchSuccess(response) {
                            _warrantyEntitlements = [];

                            if (response.data.results) {
                                _.forEach(response.data.results.entitlements, function (eItem) {
                                    if (eItem.systemId === 'BCAS') {
                                        if (currentPolicyCustomerGroupCode !== eItem.policy.customerGroup.code && new Date() < new Date(eItem.policy.endDate)) {
                                            _warrantyEntitlements.push(new Entitlement(eItem));
                                        }
                                    }
                                });
                                return _warrantyEntitlements;
                            }
                        });
                },

                getWarrantyEntitlements: function getWarrantyEntitlements() {
                    return _warrantyEntitlements;
                },
                prepareWarrantyEntitlementsForDisplay: function prepareWarrantyEntitlementsForDisplay() {
                    _warrantyEntitlementsForDisplay = _warrantyEntitlements.slice();
                },
                getWarrantyEntitlementsForDisplay: function getWarrantyEntitlementsForDisplay() {
                    return _warrantyEntitlementsForDisplay;
                },
                /**
                 * Performs request to search for vehicle by search criteria
                 * @returns {object} containing the vehicle and the vehicle's make models
                 */
                vehicleSearch: function vehicleSearch(vrn) {
                    // Reset the flag at the start of each search
                    _hasConflictingVehicleData = false;

                    return $http
                        .get(SearchURLs.VEHICLE_SEARCH_URL + `${vrn ? vrn : _vehicleSearch.search()}`, {
                            notify: true,
                            friendly: 'Vehicle search'
                        })
                        .then(function (response) {
                            if (response.data.vehicle.registration.toLowerCase() !== _vehicleSearch.search().toLowerCase()) {
                                response.data.vehicle.registration = _vehicleSearch.search().toUpperCase();
                                _hasConflictingVehicleData = true;
                                AlertService.createAlert('The vehicle search returned conflicting data', AlertTypes.DANGER);
                            }
                            var data = response.data;

                            _vehicles = [];
                            _vehicleResults = [];
                            _vehicles.push(new Vehicle(data.vehicle));

                            VehicleService.setModelsFromRaw(data.refData.models);
                            return {
                                vehicle: _vehicles[0],
                                models: data.refData.models,
                                type: data.refData.type,
                                hasConflictingData: _hasConflictingVehicleData
                            };
                        });
                },
                vehicleSearchTerm: function vehicleSearchTermAcessor(val) {
                    return arguments.length ? (_vehicleSearch = val) : _vehicleSearch;
                },
                getVehicleResults: function getVehicleResults() {
                    return _vehicleResults;
                },
                getVehicle: function getVehicle() {
                    return _vehicles[0];
                },
                searchGoogle: (searchTerm) => {
                    let postCodeValidationFn = AddressLookupService.postCodePatternService();
                    if (!searchTerm.match(/^(\-?\d+(\.\d+)?)\s*,\s*(\-?\d+(\.\d+)?)$/)) {
                        if ((postCodeValidationFn.test(searchTerm) && searchTerm.substring(0, 2) === 'JE') || (postCodeValidationFn.test(searchTerm) && searchTerm.substring(0, 2) === 'GY')) {
                            searchTerm = searchTerm; // 'uk' not  added if postcode valid and postcode regions belongs to  guernsey or jersey
                        } else if (!searchTerm.match(/guernsey/i) && !searchTerm.match(/jersey/i) && !LocationService.europeanPoiChecked()) {
                            searchTerm = searchTerm + ', uk'; // 'uk' added if the search term is not latLng or guernsey or jersey
                        }
                        return MappingService.search(searchTerm).then(
                            function success(results) {
                                _locationResults.google = [];

                                if (results) {
                                    _.forEach(results, function (result) {
                                        _locationResults.google.push(result);
                                    });
                                }

                                return results;
                            },
                            function () {
                                console.log(6);
                            }
                        );
                    } else {
                        return MappingService.latLngSearch(searchTerm).then(function success(results) {
                            let latLng = searchTerm.split(',');
                            let lat, lng;

                            if (latLng && latLng.length) {
                                lat = parseFloat(latLng[0]) !== results[0].geometry.location.lat() ? parseFloat(latLng[0]) : results[0].geometry.location.lat();
                                lng = parseFloat(latLng[1]) !== results[0].geometry.location.lng() ? parseFloat(latLng[1]) : results[0].geometry.location.lng();
                            }
                            let position = GoogleService.latLng(lat, lng);
                            results[0].geometry.location = position;
                            _locationResults.google = [];
                            _locationResults.google.push(results[0]);
                            return results;
                        });
                    }
                },
                matchGenericSearch: (search) => {
                    const _genericSearchTerm = SpecificSearchTermsConstants[search.join('_')];
                    return _genericSearchTerm ? _genericSearchTerm : null;
                },
                searchLocation: function searchLocation() {
                    var resolve = [],
                        junctions,
                        mwayServices,
                        searchTerms = [],
                        isSpecificTerm = false,
                        matchedSpecificTerm = null,
                        specificPosts,
                        markerPosts,
                        sosBox,
                        searchValue = _locationSearch.search().toUpperCase(); // all searches in upper case ...

                    _resetLocationResults();

                    // sanitise result for marker post search
                    searchTerms = searchValue.replace(/([ ]){1,}/i, '%').split('%');
                    matchedSpecificTerm = svc.matchGenericSearch(searchTerms);

                    // Specific search
                    if (searchTerms[0] && searchTerms[1] && matchedSpecificTerm) {
                        isSpecificTerm = true;
                        // Search poi post
                        specificPosts = MappingService.getPoi([matchedSpecificTerm.categoryId]).then(
                            function (response) {
                                _locationResults.specificPost = [];

                                _.forEach(response.data || [], function (result) {
                                    _locationResults.specificPost.push(result);
                                });

                                return response.data;
                            },
                            function () {
                                console.log(1);
                            }
                        );

                        resolve.push(specificPosts);
                    }

                    // server side does more parsing of the properties
                    if (searchTerms[0] && searchTerms[1] && !isSpecificTerm) {
                        // Search marker post
                        markerPosts = $http
                            .post(
                                SearchURLs.MARKER_POST_SEARCH_URL,
                                {
                                    markerPost: searchTerms[0],
                                    route: searchTerms[1]
                                },
                                {
                                    notify: true,
                                    friendly: 'Marker post search'
                                }
                            )
                            .then(
                                function (response) {
                                    _locationResults.markerPost = [];

                                    _.forEach(response.data || [], function (result) {
                                        _locationResults.markerPost.push(result);
                                    });

                                    return response.data;
                                },
                                function () {
                                    console.log(1);
                                }
                            );

                        resolve.push(markerPosts);
                    }

                    if (searchTerms[0] && searchTerms[1] && !isSpecificTerm) {
                        sosBox = $http
                            .post(
                                SearchURLs.SOS_BOX_SEARCH_URL,
                                {
                                    sosBox: searchTerms[0],
                                    route: searchTerms[1]
                                },
                                {
                                    notify: true,
                                    friendly: 'SOS box search'
                                }
                            )
                            .then(
                                function (response) {
                                    _locationResults.sosBox = [];

                                    _.forEach(response.data || [], function (result) {
                                        _locationResults.sosBox.push(result);
                                    });

                                    return response.data;
                                },
                                function () {
                                    console.log(2);
                                }
                            );

                        resolve.push(sosBox);
                    }

                    if (searchTerms[0] && searchTerms[1] && !isSpecificTerm) {
                        if (/TOLL/gi.test(searchTerms[1]) || /TOLL/gi.test(searchTerms[0])) {
                            searchTerms[0] = /TOLL/gi.test(searchTerms[0]) ? searchTerms[0].replace(/TOLL(\s?)/gi, ' TOLL') : searchTerms[0] + ' TOLL';
                            searchTerms[1] = searchTerms[1].replace(/TOLL(\s?)/gi, '');
                        }
                        junctions = $http
                            .post(
                                SearchURLs.JUNCTION_SEARCH_URL,
                                {
                                    junction: searchTerms[1].replace(/(JUNCTION|JUNC|JCT|J)(\s?)/, ''),
                                    route: searchTerms[0]
                                },
                                {
                                    notify: true,
                                    friendly: 'Motorway junction search'
                                }
                            )
                            .then(
                                function (response) {
                                    _locationResults.junctions = [];

                                    _.forEach(response.data || [], function (result) {
                                        _locationResults.junctions.push(result);
                                    });

                                    return response.data;
                                },
                                function () {
                                    console.log(3);
                                }
                            );

                        resolve.push(junctions);
                    }
                    // motorway service search ...
                    if (searchTerms.length > 0 && !isSpecificTerm) {
                        //need to test for motorway name within the search string:
                        let mwayNameRegEx = new RegExp('(M6\\s?Toll|[A|M]\\s?\\d+\\s?(M\\b)?(\\(M\\))?)', 'gi');
                        let mwayName = mwayNameRegEx.exec(searchValue);
                        // sanitise result for motorway service search if possible
                        if (mwayName !== null && mwayName[0].length > 0) {
                            searchTerms[0] = _.trim(searchValue.replace(mwayName[0], ''));
                            searchTerms[1] = _.trim(mwayName[0]);
                        }

                        mwayServices = $http
                            .post(
                                SearchURLs.MWAY_SERVICES_SEARCH_URL,
                                {
                                    serviceName: searchTerms[0],
                                    route: searchTerms[1]
                                },
                                {
                                    notify: true,
                                    friendly: 'Motorway services search'
                                }
                            )
                            .then(
                                function (response) {
                                    _locationResults.mwayServices = [];

                                    _.forEach(response.data || [], function (result) {
                                        _locationResults.mwayServices.push(result);
                                    });

                                    return response.data;
                                },
                                function () {
                                    console.log(4);
                                }
                            );

                        resolve.push(mwayServices);
                    }

                    return $q.all(resolve).then(function (results) {
                        var pushToCompiled = function pushToCompiled(result) {
                            _locationResults.compiled.push(result);
                        };

                        _locationResults.compiled = [];

                        _.forEach(_locationResults.junctions, pushToCompiled);
                        _.forEach(_locationResults.markerPost, pushToCompiled);
                        _.forEach(_locationResults.specificPost, pushToCompiled);
                        _.forEach(_locationResults.sosBox, pushToCompiled);
                        _.forEach(_locationResults.mwayServices, pushToCompiled);
                        _.forEach(_locationResults.address, pushToCompiled);
                        //should only add google results when nothing comes up from rest of the search queries.
                        if (!_locationResults.compiled.length) {
                            let inputSearchTerm = _locationSearch.search();
                            svc.searchGoogle(inputSearchTerm).then((response) => {
                                _.forEach(_locationResults.google, pushToCompiled);
                                MappingService.addResultMarkers(_locationResults.google);
                            });
                        }
                        MappingService.addResultMarkers(_locationResults.compiled); //add markers for all bar address lookup result
                        return _locationResults;
                    });
                },
                getPoi: function getPoi(cats) {
                    return MappingService.getPoi(cats).then(function (response) {});
                },
                radiusSearch: (center, radius, catIDs) => {
                    return $http
                        .post('/api/mapping-service/search/radius', {
                            center: center,
                            radius: radius,
                            catIds: catIDs
                        })
                        .then((response) => {
                            return response.data;
                        });
                },
                locationSearch: function locationSearch(val) {
                    return arguments.length ? (_locationSearch = val) : _locationSearch;
                },
                removeCurrentDealers: () => {
                    //this is to remove the existing POIs
                    _.remove(_locationResults.compiled, (poi) => {
                        return poi.properties && (!!poi.properties.supResourceId || !!poi.properties.epyxId || !!poi.properties.automyzeId || poi.properties.categoryId === 10037);
                    });
                },
                locationResults: function locationResultsAcessor(val) {
                    if (val && val.garages.length) {
                        svc.removeCurrentDealers();
                        _.forEach(val.garages, (result) => {
                            _locationResults.compiled.unshift(result);
                        });
                    }
                    return _locationResults;
                },
                dealerSearch: function dealerSearch(breakdownLocation, custGroupCode) {
                    return $http
                        .post(
                            SearchURLs.DEALER_SEARCH_URL,
                            {
                                point: {
                                    lat: breakdownLocation.latitude(),
                                    lng: breakdownLocation.longitude()
                                },
                                custGroup: custGroupCode
                            },
                            {
                                notify: true,
                                friendly: 'Dealer search'
                            }
                        )
                        .then(function onGarageSearchSuccess(response) {
                            _locationResults.dealers = response.data;
                            _locationResults.compiled = response.data;
                            MappingService.addResultMarkers(_locationResults.compiled); //add markers for all bar address lookup result
                            return _locationResults.compiled;
                        });
                },
                /**
                 * find entitlement by cli - this method queries csh for past CRs against cli
                 * @param  {string} cli defines inbound phone no
                 * @param  {string} ddi defines number dialled by member
                 * @return {Promise}
                 */
                entitlementCliSearh: (cli, ddi) => {
                    return $http
                        .get(
                            SearchURLs.MEMBERSHIP_CLI_SEARCH_URL,
                            {
                                params: {
                                    cli: cli,
                                    ddi: ddi
                                }
                            },
                            {
                                notify: true,
                                friendly: 'Entitlement search'
                            }
                        )
                        .then((response) => {
                            var entitlementResults = response.data;
                            /*                        if (response.data.query) {
                                                    _membershipSearch.param(new AdvancedMembershipQuery(response.data.query));
                                                }
                        */
                            if (entitlementResults) {
                                if (entitlementResults.entitlements) {
                                    _membershipSearch.param().page(entitlementResults.nextPage);
                                    return processAndPopulateEntitlements(entitlementResults.entitlements);
                                }

                                if (entitlementResults.companies) {
                                    _membershipSearch.param().page(entitlementResults.nextPage);
                                    return processAndPopulateCompanies(entitlementResults.companies);
                                }
                            }
                        });
                },

                /**
                 * Serach tasks
                 * @param {object} customerRequestQuery
                 * @param {string} searchType
                 * @returns {Promise}
                 */
                search: (customerRequestQuery, searchType) => {
                    switch (searchType) {
                        case AdvancedCustomerRequestQuery.ASSISTANCE_TYPE_SEARCH_TYPE:
                            customerRequestQuery.param().serviceTypeId(ServiceTypeService.serviceType().id());
                            customerRequestQuery.param().customerGroupCode(CustomerGroupService.customerGroup().code());
                            break;
                        case AdvancedCustomerRequestQuery.CUSTOMER_SEARCH_TYPE:
                            customerRequestQuery.param().seSystemId(CustomerGroupService.customerGroup().systemId());
                            customerRequestQuery.param().customerGroupCode(CustomerGroupService.customerGroup().code());
                            break;
                    }

                    return CSHService.findByQuery(customerRequestQuery, searchType).then(function (result) {
                        CreateReasonService.reset();
                        if (result.customerRequestHistory().length) {
                            CSHService.isAdvancedCshSearchVisible(false);
                        } else {
                            // no results
                            PromptService.showNoResultsFound();
                        }
                        return result;
                    });
                },

                /**
                 * Flag to indicate task search by url param
                 * @param {boolean} val
                 * @returns {boolean}
                 */
                searchTaskByQueryParam: (...args) => (args.length ? (_searchTaskByQueryParam = args[0]) : _searchTaskByQueryParam),

                isAdvancedMembershipSearchVisible: (...args) => (args.length ? (_isAdvancedMembershipSearchVisible = args[0]) : _isAdvancedMembershipSearchVisible),

                isW3WSearchQuery: function isW3WSearchQuery(query) {
                    try {
                        return query().match(/^\/\/\/(\w+\.\w+\.\w+)/) && $state.current.name.includes('mapping');
                    } catch (e) {
                        return false;
                    }
                },
                isW3WSearch: function isW3WSearch(val) {
                    return arguments.length ? (_w3wlocationFound = val) : _w3wlocationFound;
                },
                isinvalidW3search: function isinvalidW3search(val) {
                    return _isinvalidW3search;
                },
                W3Wsearch: function search(searchQuery) {
                    let matches = searchQuery.match(/^\/\/\/(\w+\.\w+\.\w+)/);
                    if (searchQuery.includes('///pretty.needed.chill')) {
                        _isinvalidW3search = true;
                        return;
                    }
                    if (matches) {
                        const words = matches[1];
                        _isinvalidW3search = false;
                        $http
                            .get(SearchURLs.W3W_TO_LAT_LONG_URL + `/${words}`)
                            .then((result) => {
                                return result.data;
                            })
                            .then((result) => {
                                if (result.error) {
                                    throw new Error(result.error);
                                }
                                _w3wlocationFound = result;
                                const latLng = new google.maps.LatLng(result.coordinates.lat, result.coordinates.lng);
                                if (MappingService.map().getZoom() < 17) {
                                    MappingService.map().setZoom(17);
                                }
                                _getContextMenuService().setBreakdownLocation({ latLng });
                                _getUIService().showLocationInfo(true);
                            })
                            .catch((error) => {
                                AlertService.createAlert('Could not lookup what 3 Words: ' + error.message, AlertTypes.INFO);
                            });
                    }
                },
                getW3WForLatLong: function search(latitude, longitude) {
                    return $http
                        .get(SearchURLs.LAT_LONG_TO_W3W_URL + `?lat=${latitude}&lng=${longitude}`)
                        .then((result) => {
                            if (result.error) {
                                throw new Error(result.error);
                            }
                            return result.data && result.data.words;
                        })
                        .catch((error) => {
                            AlertService.createAlert('Could not lookup what 3 Words for given coords: ' + error.message, AlertTypes.INFO);
                        });
                },
                hasConflictingVehicleData: function hasConflictingVehicleData(val) {
                    return arguments.length ? (_hasConflictingVehicleData = val) : _hasConflictingVehicleData;
                }
            });
        }
    ]);
