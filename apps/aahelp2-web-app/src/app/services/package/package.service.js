const { Benefit } = require('@aa/data-models/aux/entitlement');
const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    Package = require('@aa/malstrom-models/lib/package.model'),
    SEUpdateTask = require('@aa/malstrom-models/lib/se-update-task.model');
require('angular');

module.exports = angular
    .module('aah-package-service-module', [require('../../constants/ref-data/ref-data-urls.constants').name, require('../../constants/task/task-urls.constants').name])
    .service('aahPackageService', [
        '$http',
        '$q',
        'aahRefDataURLs',
        'aahTaskURLs',
        function PackageService($http, $q, RefDataURLs, TaskURLs) {
            var svc = this,
                _packages = null;

            function _buildRemarks(newPackage, amount, additionalRemarks) {
                var result = ['Trade up to ' + newPackage.name(), 'Paid £' + amount, additionalRemarks].join('. ');
                return result.trim();
            }

            _.extend(svc, {
                loadPackages: function loadPackages() {
                    return $http.get(RefDataURLs.PACKAGES).then(function getPackageResult(data) {
                        _packages = {};
                        const allowedPackageCodes = ['H', 'R', 'D', 'AB', 'A2', 'CUV'];
                        _.forEach(data.data, function convertToRefCode(raw) {
                            if (allowedPackageCodes.includes(raw.code)) {
                                _packages[raw.code] = new Package(raw);
                            }
                        });
                        return _packages;
                    });
                },
                getPackageBenefits: function getPackageBenefits(id) {
                    var promise = null;
                    if (_.isNumber(id)) {
                        promise = $http.get(RefDataURLs.PACKAGE_BENEFITS.replace(':id', id)).then(function getPackageBenefitsResult(data) {
                            var benefits = [];
                            _.forEach(data.data, function convertToBenefit(raw) {
                                benefits.push(new Benefit(raw));
                            });

                            return benefits;
                        });
                    }

                    return $q.when(promise);
                },
                upgradePackage: function upgradePackage(task, cr, newPackage, paymentAmount, remarks) {
                    var existingBenefits = task.entitlement().benefits(); // retrieve existing benefits
                    var allBenefits = []; // define allBenefits here
                    return svc
                        .getPackageBenefits(newPackage.id())
                        .then(function getPackageBenefits(data) {
                            var newBenefits = data;
                            allBenefits = existingBenefits.concat(newBenefits); // update allBenefits
                            return svc.createSEUpdateTask(task, newPackage, paymentAmount);
                        })
                        .then(function (seUpdateTask) {
                            task.entitlement().benefits(allBenefits); // update task entitlement with concatenated benefits
                            task.location().addRemarks(_buildRemarks(newPackage, paymentAmount, remarks));
                            cr.addTask(seUpdateTask);
                            return true;
                        })
                        .catch(function getPackageBenefitsError(err) {
                            return false;
                        });
                },
                getPackageByCode: (code) => _.find(_packages, (pkg) => pkg.code() === code),
                createSEUpdateTask: function createSEUpdateTask(task, newPackage, paymentAmount) {
                    var seUpdateTask = new SEUpdateTask({
                        customerRequestId: task.customerRequestId(),
                        packageId: newPackage.id(),
                        amount: paymentAmount,
                        memberName: task.entitlement().memberName(),
                        contact: task.contact().toJSON()
                    });

                    seUpdateTask.address(
                        new Address({
                            addressLines: task.entitlement().memberAddress().slice(0, -1), //exclude postcode
                            postcode: task.entitlement().memberAddress()[task.entitlement().memberAddress().length - 1] //only postcode
                        })
                    );

                    return $http.post(TaskURLs.SE_UPDATE_TASK, seUpdateTask.toJSON()).then(function seUpdateTaskResult(data) {
                        seUpdateTask.id(data.data.taskId);
                        return seUpdateTask;
                    });
                },
                getPackages: function getPackages() {
                    return _packages;
                }
            });
        }
    ]);
