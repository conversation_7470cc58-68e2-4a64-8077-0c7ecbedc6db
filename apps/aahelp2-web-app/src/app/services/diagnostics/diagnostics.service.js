var _ = require('lodash'),
    QuestionRequest = require('@aa/malstrom-models/lib/diagnostics-question-request.model'),
    Task = require('@aa/malstrom-models/lib/task.model'),
    DiagnosticsQuestion = require('@aa/malstrom-models/lib/diagnostics-question.model'),
    RefId = require('@aa/malstrom-models/lib/ref-id.model'),
    Fault = require('@aa/malstrom-models/lib/fault.model'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model');

require('angular');

module.exports = angular
    .module('aah-diagnostics-service-module', [
        //constants
        require('../../constants/diagnostics/diagnostics-urls.constants').name,
        require('../../constants/recovery/recovery.constants').name,
        require('../../constants/faults/faults.constants').name,

        // service
        require('../vehicle/vehicle.service').name,
        require('../recovery/recovery.service').name,
        require('../task/task.service').name,
        require('../mapping/google/google.service').name,
        require('../../factories/flexible-fields/flexible-fields.helper').name,
        require('../tab/tab.service').name,
        require('../../helpers/experian-details/experian-details.helper.js').name,
        require('../priority/priority.service').name
    ])
    .service('aahDiagnosticsService', [
        '$http',
        '$q',
        '$injector',
        'aahDiagnosticsURLs',
        'aahRecoveryConstants',
        'aahRecoveryService',
        'aahTaskService',
        'aahExperianDetailsHelperService',
        'aahFlexibleFieldsHelper',
        'aahFaultsConstants',
        'aahTabService',
        'aahGoogleService',
        'aahPromptService',
        'aahCSHService',
        'aahPriorityService',
        'aahDestResourceIDsConstants',
        function DiagnosticsService(
            $http,
            $q,
            $injector,
            URLs,
            RecoveryConstants,
            RecoveryService,
            TaskService,
            ExperianDetailsHelper,
            FlexibleFieldsHelper,
            FaultsConstants,
            TabService,
            GoogleService,
            PromptService,
            CSHService,
            PriorityService,
            DestResourceIDsConstants
        ) {
            var svc = this,
                _commonFaults = [],
                _diagnosticsQAList = [],
                _selectedCommonFault = null,
                _relayDiagnosticsQAList = [],
                _nextQuestionId = 0,
                _nextInstanceId = 0,
                _forcedRecoveryFault = 0,
                _sifFault = false,
                _people1Capability = {},
                _repairMinutes,
                _selectedAnsweredQuestion = null;

            function setSIFFault(question) {
                if (!_sifFault && question.selectedAnswer().name().indexOf('#c') > -1) {
                    _sifFault = true;
                    setSifFlexField('Y');
                }
            }

            function setSifFlexField(value) {
                TaskService.task().miscFields().sif(value);
            }

            function getDiagnosticsFault(faultId, capabilityId) {
                if (_selectedCommonFault) {
                    return _selectedCommonFault.clone();
                }

                var url = URLs.DIAGNOSTICS_FAULT.replace(':faultId', faultId).replace(':capabilityId', capabilityId);
                return $http.get(url).then(function success(response) {
                    return new Fault(response.data);
                });
            }

            function getCapabilities(repairId) {
                var url = URLs.CAPABILITIES_QUERY.replace(':repairId', repairId),
                    promise = null;

                if (repairId) {
                    promise = $http.get(url).then(function success(data) {
                        var _capabilities = [];
                        _.forEach(data.data, function convertToRefId(item, idx) {
                            _capabilities.push(new RefId(item));
                        });
                        return _capabilities;
                    });
                }

                return $q.when(promise || []);
            }

            function getDiagnosticQuestion(instance, id, fault) {
                var request = new QuestionRequest();

                var formattedExperianDetails = ExperianDetailsHelper.formatExperianDetails(TaskService.task().vehicle().experianDetails());

                if (instance !== null) {
                    request.instance(instance);
                }

                if (id !== null) {
                    request.id(id);
                }

                if (fault && fault.id() > -1) {
                    request.faultId(fault.id());
                    request.faultName(fault.name());
                }

                request.makeId(TaskService.task().vehicle().makeId());
                request.modelId(TaskService.task().vehicle().modelId());
                request.year(TaskService.task().vehicle().experianDetails().yrOfManufacture());
                request.weight(TaskService.task().vehicle().experianDetails().grossWeight());
                request.customerGroupId(TaskService.task().entitlement().customerGroup().id());
                request.vehicleTypeId(TaskService.task().vehicle().typeId());
                request.people(TaskService.task().recovery().unaccompaniedAfterLoading() ? 0 : TaskService.task().recovery().adults());
                request.recoveryDistance(TaskService.task().recovery().distance());
                request.transmission(formattedExperianDetails.transmission());
                request.fuel(formattedExperianDetails.fuel());
                request.dateFirstRegistered(TaskService.task().vehicle().experianDetails().dateFirstRegistered());
                request.engineSize(TaskService.task().vehicle().experianDetails().engineSize());

                return $http.post(URLs.DIAGNOSTICS_QUERY, request.toJSON()).then(function success(response) {
                    var data = response.data;
                    return new DiagnosticsQuestion(data);
                });
            }

            function clearDiagnosticsQA() {
                _diagnosticsQAList.length = 0;
                _selectedCommonFault = null;
                _sifFault = false;
                setSifFlexField('N');
                TaskService.updateFault(new Fault()); //clear fault
            }

            function clearRelayDiagnosticsQA() {
                _relayDiagnosticsQAList.length = 0;
                TaskService.task().recovery().fault().diagnosticsQAList().length = 0; //clear list
                if (!TaskService.task().createReason().isLogistics()) {
                    TaskService.task().recovery().fault(new Fault());
                }
            }

            function initialiseDiagnosticsForVBMs() {
                _diagnosticsQAList.length = 0;

                //Non key assist Diagnostics questions should not be displayed for key assist faults (RBAUAA 1326)
                if (TaskService.task().createReason().id() === CreateReason.KEY_ASSIST) {
                    return true;
                }

                getDiagnosticQuestion(0, 0).then(function getDiagnosticQuestion(question) {
                    _diagnosticsQAList.push(question);
                }); //get initial question
            }

            function resetQuestionsAndAnswers() {
                clearDiagnosticsQA();

                //if we have already got a locn which is dangerous/mway, if we're not reseting locn,
                //we don't want to reset the recy - only clear the adults to ensure recy diagnostics correct!!
                if (TaskService.task().recovery().adults() > 0) {
                    TaskService.task().recovery().adults(null);
                }

                TaskService.setDirectRecovery(false);

                return getDiagnosticQuestion(0, 0).then(function getDiagnosticQuestion(question) {
                    _diagnosticsQAList.push(question);
                    return question;
                }); //get initial question
            }

            function resetRelayQuestionsAndAnswers() {
                if (TaskService.task().recovery().adults() === null) {
                    return;
                }

                // for these cases relayDiagnostics is not running ...
                if (TaskService.task().recovery().passengerRun() || TaskService.task().createReason().isRelayLeg() || TaskService.task().createReason().isSupportTask()) {
                    TaskService.task().indicators().isRelayDiagnosticsRunning(false);
                    return;
                }

                /**Reason for adding ESCAPE_FAULTS_CATEGORY_CODE check is we were not able to get diagnostic questions for RTC fault */
                if (
                    !RecoveryService.pausedNextQuestionId() &&
                    !svc.isForcedRecovery() &&
                    !TaskService.task().indicators().dangerousLocation() &&
                    !FaultsConstants.ESCAPE_FAULTS_CATEGORY_CODE.includes(TaskService.task().fault().categoryCode())
                ) {
                    TaskService.task().indicators().isRelayDiagnosticsRunning(false);
                    clearRelayDiagnosticsQA();
                    TaskService.task().recovery().fault(new Fault(TaskService.task().fault().toJSON()));
                    TaskService.task().recovery().fault().diagnosticsQAList([]);
                    return;
                }
                clearRelayDiagnosticsQA();

                //need to determine whether we are starting from an nextQuestionId [DIRECT] or forced recy method [INDIRECT]
                if (RecoveryService.selectedForcedRecoveryFault() || (!RecoveryService.pausedNextQuestionId() && TaskService.getDirectRecovery())) {
                    _nextQuestionId = 1;
                    _nextInstanceId = 1;
                    //because a DL/MW fault requires forceRecy fault to fire correct relay diag, if they change the fault, need to make sure we keep it
                    _forcedRecoveryFault =
                        RecoveryService.selectedForcedRecoveryFault() &&
                        (RecoveryService.selectedForcedRecoveryFault().categoryCode() === 'FRE' || RecoveryService.selectedForcedRecoveryFault().categoryCode() === 'FRM')
                            ? RecoveryService.selectedForcedRecoveryFault()
                            : RecoveryService.faults()[0];
                } else if (RecoveryService.pausedNextQuestionId()) {
                    _nextQuestionId = RecoveryService.pausedNextQuestionId();
                    _nextInstanceId = RecoveryService.pausedNextInstanceId();
                    _forcedRecoveryFault = null;
                } else {
                    TaskService.task().indicators().isRelayDiagnosticsRunning(false);
                }

                if (_nextQuestionId > 0) {
                    TaskService.task().indicators().isRelayDiagnosticsRunning(true);
                    getDiagnosticQuestion(_nextInstanceId, _nextQuestionId, _forcedRecoveryFault).then(function getDiagnosticQuestion(question) {
                        //_relayDiagnosticsQAList.push(question);
                        if (question.answers().length === 1 && question.answers()[0].repairId()) {
                            //no further questions
                            question.selectedAnswer(question.answers()[0]);
                            svc.processRelayQuestionAnswer(question);
                        } else {
                            //prompt user the 1st question
                            _relayDiagnosticsQAList.push(question);
                        }
                    }); //get initial question
                }
            }

            function _updateTaskFault(fault, qaList) {
                var diagnosticsQuestion,
                    tempQAList = [];

                TaskService.updateFault(fault);

                if (!TaskService.task().createReason().isLogistics()) {
                    TaskService.task().recovery().fault(new Fault());
                }

                _.forEach(qaList, function _forEachQA(qa) {
                    diagnosticsQuestion = new DiagnosticsQuestion();
                    diagnosticsQuestion.name(qa.name());
                    diagnosticsQuestion.selectedAnswer(qa.selectedAnswer());
                    tempQAList.push(diagnosticsQuestion);
                });

                fault.diagnosticsQAList(tempQAList);
                TaskService.task().isDirty(true);

                //set to authorise if either; in a dangerous location or fault is anything other than fuel assist
                if (!(fault.isFuelAssist() || fault.isKeyAssist()) || TaskService.task().indicators().dangerousLocation()) {
                    TaskService.task().indicators().authorised(true);
                }
            }

            function _isRelayQuestion(question) {
                return question.id() === RecoveryConstants.QT_DISTANCE || question.id() === RecoveryConstants.QT_PEOPLE;
            }

            function _finaliseDiagnosticsQA(faultId, repairId, selectedAnswer) {
                //RepaidId set to 317 if additional key assist
                repairId = !isAdditionalKeyAssist(faultId) ? repairId : 317;
                return $q.all([getDiagnosticsFault(faultId, repairId), getCapabilities(repairId)]).then(function (data) {
                    var fault = data[0],
                        capabilities = data[1];

                    if (!TaskService.task().isRecovery()) {
                        //In case of add task recovery, capabilities of parent task is cloned to child, prime fails because of the unsupported cabilities for recovery
                        fault.capabilities(capabilities);
                    }
                    fault.additionalInfo(selectedAnswer.patrolMsg());
                    fault.repairMinutes(!isAdditionalKeyAssist(fault.id()) ? selectedAnswer.estimatedTimeToComplete() : fault.repairMinutes());

                    //if unknown fault, take the fault description in free text form

                    if (_diagnosticsQAList.length && _diagnosticsQAList[0].selectedAnswer().isUnknown()) {
                        fault.name(_diagnosticsQAList[0].selectedAnswer().name());
                    }

                    _updateTaskFault(fault, _diagnosticsQAList);
                    _diagnosticsQAList.length = 0; //clear list

                    const forceCheckBecauseFaultIsFB = fault.code().code() === 'FB';
                    return PromptService.checkDemandDeflection(CSHService.entitlement(), TaskService.task(), { forceCheck: forceCheckBecauseFaultIsFB }).then(() => fault);
                });
            }

            function _finaliseRecoveryDiagnosticsQA(repairId) {
                return getCapabilities(repairId).then(function (data) {
                    var diagnosticsQuestion,
                        tempQAList = [];

                    //for DIRECT recoveries, we do not have a selectedForcedRecoveryFault - have to set one same as task fault
                    if (!RecoveryService.selectedForcedRecoveryFault()) {
                        RecoveryService.selectedForcedRecoveryFault(new Fault(TaskService.task().fault().toJSON()));
                    }

                    RecoveryService.selectedForcedRecoveryFault().capabilities(data);
                    //on a motorway or dangerous location, make sure we set PEOPLE capability..
                    if (TaskService.task().indicators().motorway() || TaskService.task().indicators().dangerousLocation()) {
                        if (TaskService.task().vehicle().experianDetails().grossWeight() < 3500) {
                            _people1Capability = new RefId({
                                id: 17,
                                name: 'PEOPLE 1'
                            });
                            RecoveryService.selectedForcedRecoveryFault().capabilities().push(_people1Capability);
                        }
                    }

                    TaskService.task().recovery().fault(RecoveryService.selectedForcedRecoveryFault());
                    _.forEach(_relayDiagnosticsQAList, function _forEachQA(qa) {
                        diagnosticsQuestion = new DiagnosticsQuestion();
                        diagnosticsQuestion.name(qa.name());
                        diagnosticsQuestion.selectedAnswer(qa.selectedAnswer());
                        tempQAList.push(diagnosticsQuestion);
                    });

                    TaskService.task().recovery().fault().diagnosticsQAList(tempQAList);
                    TaskService.task().isDirty(true);

                    _relayDiagnosticsQAList.length = 0; //clear list
                });
            }

            function _getFaultIconClass(fault) {
                //TODO - this a temporary hack until we get the correct fault ids from prime
                var result = 'other-fault-identified',
                    _faultName = TaskService.task().createReason().isFuelAssist() ? 'fuelassist' : fault.name();

                if (
                    fault &&
                    fault.name() &&
                    !!_.find(_commonFaults, function (commonFault) {
                        return commonFault.id() === fault.id();
                    })
                ) {
                    result = _faultName.toLowerCase().replace(/[\(\)/ .':-]/g, '');
                }
                return 'icon-' + result;
            }

            function _getCommonFaultsByTypeId(vehicleTypeId, serviceTypeCode, createReasonId, customerGroupCode) {
                return $http
                    .post(URLs.COMMON_FAULTS, {
                        type: vehicleTypeId,
                        serviceType: serviceTypeCode,
                        createReasonId: createReasonId,
                        customerGroupCode: customerGroupCode
                    })
                    .then(
                        function fetchSuccess(resp) {
                            _commonFaults.length = 0;
                            _.forEach(resp.data, function eachFault(item, idx) {
                                _commonFaults.push(new Fault(item));
                            });
                            return _commonFaults;
                        },
                        function fetchError(resp) {
                            //TODO need to do something with the error
                        }
                    );
            }

            /**
             * Process question and answer to determine whether there are more questions
             * to follow or whether we have reached the end of the QA process and thus ready
             * to save the fault on the currently selected task.
             *
             * @param {DiagnosticsQuestion} answeredQuestion
             * @returns {promise}
             */
            function _processQuestionAnswer(answeredQuestion) {
                var deferred = $q.defer(),
                    relayText,
                    diagnosticFaultId = _diagnosticsQAList[0] ? _diagnosticsQAList[0].selectedAnswer().id() : answeredQuestion.selectedAnswer().id();

                if (_.isArray(_diagnosticsQAList) && _diagnosticsQAList.indexOf(answeredQuestion) > -1) {
                    _diagnosticsQAList.splice(_diagnosticsQAList.indexOf(answeredQuestion) + 1); //discard superseded QAs, if any
                }

                svc.selectedAnsweredQuestion(answeredQuestion);
                setSIFFault(answeredQuestion);
                if (answeredQuestion.selectedAnswer().repairId() || answeredQuestion.selectedAnswer().nextQuestionId() === 0) {
                    //end of QA cycle, resolve with derived fault
                    _finaliseDiagnosticsQA(diagnosticFaultId, answeredQuestion.selectedAnswer().repairId(), answeredQuestion.selectedAnswer()).then(function _finaliseDiagnosticsQASuccess(fault) {
                        if (
                            answeredQuestion.selectedAnswer().recoveryJobInd() ||
                            (TaskService.task().location().isSet() && (TaskService.task().indicators().motorway() || TaskService.task().indicators().dangerousLocation()))
                        ) {
                            TabService.touch('relayTo');
                            if (answeredQuestion.selectedAnswer().recoveryJobInd()) {
                                TaskService.task().recovery().relay(true);
                                TaskService.task().recovery().reasonForce(true);
                            }
                            if (TaskService.task().indicators().motorway() || TaskService.task().indicators().dangerousLocation()) {
                                //if the diagnostics result in recovery we will need to wait for location to get set and system force the recy
                                relayText = FaultsConstants.NEAREST_SAFE_PLACE_WITH_FACILITIES;
                            } else if (fault.isFuelAssist()) {
                                relayText = FaultsConstants.FUEL_ASSIST_RECOVERY_REMARK;
                                TaskService.task().recovery().destResourceId(DestResourceIDsConstants.LOCAL);
                            } else {
                                // If fault is recovery and location is safe do not set safe location and also do not expand enabled relay info panel
                                const UIService = $injector.get('aahUIService');
                                UIService.showRelayInfo(false);
                                relayText = null;
                            }
                            TaskService.setRelayRecoveryDestination(
                                TaskService.task().location().coordinates().latitude(),
                                TaskService.task().location().coordinates().longitude(),
                                [relayText],
                                [TaskService.task().location().area()],
                                true
                            );

                            //we need to set that this is a direct recovery for other logic in the task
                            TaskService.setDirectRecovery(TaskService.task().fault().mwayRecovVehFaultId() === 0);
                        }
                        //Set priorities when fault is child lock
                        PriorityService.setPrioritiesOnChildLock(fault);
                        deferred.resolve({
                            fault: fault
                        });
                    });
                } else {
                    //get next question
                    getDiagnosticQuestion(answeredQuestion.selectedAnswer().nextQuestionInstance(), answeredQuestion.selectedAnswer().nextQuestionId()).then(function getDiagnosticQuestionHandler(
                        question
                    ) {
                        var repairId = answeredQuestion.selectedAnswer().repairId();

                        if (_isRelayQuestion(question)) {
                            //postpone relay questions and complete QA process with derived fault
                            TaskService.task().recovery().relay(true);
                            TaskService.task().uiStatus().relay(false);

                            //we need to set that this is a direct recovery for other logic in the task
                            TaskService.setDirectRecovery(TaskService.task().fault().mwayRecovVehFaultId() === 0);

                            RecoveryService.pausedNextQuestionId(question.id());
                            RecoveryService.pausedNextInstanceId(question.instance());
                            // if (question.answers()[0].nextQuestionId()) {
                            //     //we also need to hold the nextQuestionId & nextInstanceId as we're pausing diagnostics
                            //     RecoveryService.pausedNextQuestionId(question.answers()[0].nextQuestionId());
                            //     RecoveryService.pausedNextInstanceId(question.answers()[0].nextQuestionInstance());
                            // } else {
                            //     //assume relay question answers have repair id set (due to nextQuestionId being 0), thus get capabilities using this repairId
                            //     repairId = question.answers()[0].repairId();
                            // }

                            _finaliseDiagnosticsQA(diagnosticFaultId, repairId, answeredQuestion.selectedAnswer()).then(function _finaliseDiagnosticsQASuccess(fault) {
                                deferred.resolve({
                                    fault: fault
                                });
                            });
                        } else {
                            //resolve with new diagnostics question
                            _diagnosticsQAList.push(question);
                            deferred.resolve({
                                question: question
                            });
                        }
                    });
                }

                return deferred.promise;
            }

            /**
             * Process question and answer to determine whether there are more questions
             * to follow or whether we have reached the end of the QA process and thus ready
             * to save the fault on the currently selected task.
             *
             * @param {DiagnosticQuestion} answeredQuestion
             * @returns {promise}
             */
            function _processRelayQuestionAnswer(answeredQuestion) {
                var deferred = $q.defer();

                if (_relayDiagnosticsQAList.indexOf(answeredQuestion) > -1) {
                    _relayDiagnosticsQAList.splice(_relayDiagnosticsQAList.indexOf(answeredQuestion) + 1); //discard superseded QAs, if any
                }

                if (answeredQuestion.selectedAnswer().repairId()) {
                    _finaliseRecoveryDiagnosticsQA(answeredQuestion.selectedAnswer().repairId()).then(function finaliseDiagnosticsQASuccess() {
                        TaskService.task().indicators().isRelayDiagnosticsRunning(false);
                        deferred.resolve({});
                    });
                } else if (answeredQuestion.selectedAnswer().nextQuestionId() === 0) {
                    //reset QA
                    _relayDiagnosticsQAList.length = 0; //clear list
                    getDiagnosticQuestion(1, 1, RecoveryService.selectedForcedRecoveryFault()).then(function getDiagnosticQuestionHandler(question) {
                        _relayDiagnosticsQAList.push(question);
                        deferred.resolve({
                            question: question
                        });
                    });
                } else {
                    //get next question

                    getDiagnosticQuestion(answeredQuestion.selectedAnswer().nextQuestionInstance(), answeredQuestion.selectedAnswer().nextQuestionId()).then(function getDiagnosticQuestionHandler(
                        question
                    ) {
                        //no further relay questions
                        _relayDiagnosticsQAList.push(question);
                        deferred.resolve({
                            question: question
                        });
                    });
                }

                return deferred.promise;
            }

            function _processCommonFault(fault, answeredQuestion) {
                var deferred = $q.defer(),
                    repairId,
                    faultCopy = fault.clone();

                //if we have already got a locn which is dangerous/mway, if we're not reseting locn,
                //we don't want to reset the recy - only clear the adults to ensure recy diagnostics correct!!
                if (TaskService.task().recovery().adults() > 0) {
                    TaskService.task().recovery().adults(null);
                }

                if (faultCopy.repairId() > 0) {
                    repairId = faultCopy.repairId();
                } else if (answeredQuestion && answeredQuestion.selectedAnswer().repairId()) {
                    repairId = answeredQuestion.selectedAnswer().repairId();
                }

                if (repairId > 0 && faultCopy.capabilities().length === 0) {
                    getCapabilities(repairId).then(function getCapabilitiesSuccess(capabilities) {
                        faultCopy.capabilities(capabilities);
                        _updateTaskFault(faultCopy, _diagnosticsQAList);
                        _diagnosticsQAList.length = 0; //clear list
                        deferred.resolve({
                            fault: faultCopy
                        });
                    });
                } else if (faultCopy.capabilities().length > 0) {
                    // fault already has capabilities set so no need to trip back to the server
                    _updateTaskFault(faultCopy, _diagnosticsQAList);
                    _diagnosticsQAList.length = 0; //clear list
                    deferred.resolve({
                        fault: faultCopy
                    });
                } else {
                    const nextQuestionId = FaultsConstants.CHECK_NEXT_QUESTION_KEY_ASSIST.includes(faultCopy.id()) ? 11153 : 1;
                    //common fault so starting from the root of the tree
                    getDiagnosticQuestion(1, nextQuestionId, faultCopy).then(function getDiagnosticQuestionHandler(question) {
                        _diagnosticsQAList.push(question);
                        deferred.resolve({
                            question: question
                        });
                    });
                }
                return deferred.promise;
            }

            function isAdditionalKeyAssist(faultId) {
                //Check if fault id falls in additional key assist code
                return TaskService.task().createReason().isKeyAssist() && FaultsConstants.IS_ADDITIONAL_KEY_ASSISTS.includes(faultId);
            }

            //public functions
            _.extend(svc, {
                initialiseDiagnosticsForVBMs: initialiseDiagnosticsForVBMs,
                getDiagnosticsFault: getDiagnosticsFault,
                getDiagnosticQuestion: getDiagnosticQuestion,
                resetQuestionsAndAnswers: resetQuestionsAndAnswers,
                resetRelayQuestionsAndAnswers: resetRelayQuestionsAndAnswers,
                clearDiagnosticsQA: clearDiagnosticsQA,
                clearRelayDiagnosticsQA: clearRelayDiagnosticsQA,
                getCapabilities: getCapabilities,
                isRelayQuestion: _isRelayQuestion,
                getFaultIconClass: _getFaultIconClass,
                commonFaults: function getCommonFaults() {
                    return _commonFaults;
                },
                getCommonFaultsByTypeId: _getCommonFaultsByTypeId,
                processQuestionAnswer: _processQuestionAnswer,
                processRelayQuestionAnswer: _processRelayQuestionAnswer,
                processCommonFault: _processCommonFault,
                diagnosticsQAList: function diagnosticsQAListAccessor(val) {
                    return arguments.length ? (_diagnosticsQAList = val) : _diagnosticsQAList;
                },
                selectedCommonFault: function selectedCommonFaultAccessor(val) {
                    return arguments.length ? (_selectedCommonFault = val) : _selectedCommonFault;
                },
                relayDiagnosticsQAList: function relayDiagnosticsQAListAccessor(val) {
                    return arguments.length ? (_relayDiagnosticsQAList = val) : _relayDiagnosticsQAList;
                },
                isSIFFault: function isSIFFaultAccessor(val) {
                    return arguments.length ? (_sifFault = val) : _sifFault;
                },
                initialiseDiagnosticsQA: function initialiseDiagnosticsQA() {
                    // it should not ask QA for LOG task
                    if (TaskService.task().createReason().id() !== CreateReason.AA_LOGISTICS) {
                        svc.resetRelayQuestionsAndAnswers();
                        RecoveryService.showQA(true);
                    }
                },
                initialiseCommonFaults: (vehicleTypeId, serviceTypeCode, createReasonId, customerGroupCode) => {
                    return _getCommonFaultsByTypeId(vehicleTypeId, serviceTypeCode, createReasonId, customerGroupCode);
                },
                isForcedRecovery: () => {
                    if (RecoveryService.selectedForcedRecoveryFault()) {
                        return ['FRE', 'FRM'].includes(RecoveryService.selectedForcedRecoveryFault().categoryCode());
                    }
                    return ['FRE', 'FRM'].includes(TaskService.task().recovery().fault().categoryCode()) || TaskService.task().recovery().reasonForce();
                },
                handleForcedRecoveryChange: () => {
                    if (TaskService.task().recovery().relay()) {
                        TaskService.task().recovery().fault(RecoveryService.selectedForcedRecoveryFault());
                        //cannot be a direct as we're changing the force dropdown
                        TaskService.setDirectRecovery(false);

                        if (RecoveryService.selectedForcedRecoveryFault()) {
                            resetRelayQuestionsAndAnswers();
                            RecoveryService.showQA(true);
                        }
                    }
                },
                clearRelayOnDignosed: () => {
                    TabService.clearRelay();
                },
                selectedAnsweredQuestion: (val) => {
                    return val ? (_selectedAnsweredQuestion = val) : _selectedAnsweredQuestion;
                }
            });
        }
    ]);
