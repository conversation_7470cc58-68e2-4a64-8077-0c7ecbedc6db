var _ = require('lodash');

require('angular');

module.exports = angular.module('cti-service-module', [require('./unavailable.service').name]).service('cti-service', [
    '$rootScope',
    '$q',
    '$document',
    '$interval',
    'UnavailableService',
    function ($rootScope, $q, $document, $interval, UnavailableService) {
        var svc = this,
            _debug = true,
            _cli = '',
            _finesseEnv = null,
            _logging = false,
            _user = {},
            _events = {
                DEFAULT_EVENT: [],
                LOGON_FAILED: [
                    function () {
                        _cti.server.sendCommand('AGENT_LOGOFF', '', '');
                    }
                ],
                NU_TONE: [
                    function () {
                        _cti.server.sendCommand('CLEAR_CALL', '', '');
                    }
                ],
                CATCH_ALL: [
                    function (name, event, cli, ddi, ai) {
                        if (_debug) {
                            console.log('EVENT RECEIVED');
                            console.log(arguments);
                        }
                    }
                ],
                INCOMING_CALL: [
                    function (name, event, cli, ddi, di) {
                        cli = cli.replace('+44', '0');
                        _cli = cli;
                    }
                ],
                INTERNAL_CALL: [
                    function (name, event, cli, ddi, di) {
                        cli = cli.replace('+44', '0');
                        _cli = cli;
                    }
                ]
            },
            _cti,
            _hub,
            _init = false,
            _notReadyReasons = [];

        function _formatReason(reason) {
            switch (reason) {
                case 'One to One':
                    reason = 'oneToOne';
                    break;
                case 'Supervisor Initiated':
                    reason = 'unavailable';
                    break;
                default:
                    reason = reason.toLocaleLowerCase();
            }
            return reason;
        }

        function _eventHandler(eventData) {
            console.log('::Event Received ::', eventData);

            let name = eventData.event;
            let event = eventData.event;
            let cli = eventData.cli;
            let ddi = eventData.ddi;
            let serial = eventData.serial_number;
            let timeEvent = eventData.time_event;
            let timeArrived = eventData.time_arrived;
            let trunk = eventData.trunk;
            let td = eventData.tag_data;
            let ai = eventData.data;
            let reason = eventData.reason;

            let ciscoState = _.get(eventData, 'data.state');
            let mappedStates = {
                READY: 'AVAILABLE',
                WORK_READY: 'AGENT_BUSY_WU',
                WORK: 'AGENT_BUSY_WU',
                NOT_READY: 'AGENT_BUSY_NA',
                TALKING: 'EXT_CALL_CONNECTED',
                HOLD: 'EXT_CALL_CONNECTED'
            };
            let ciscoStateId = ciscoState && mappedStates[ciscoState];

            if (['CURRENT_STATUS'].includes(event)) {
                cli = ciscoStateId;
            }
            if (name === 'AGENT_BUSY_NA' || (['CURRENT_STATUS'].includes(event) && cli === 'AGENT_BUSY_NA')) {
                let reasons = svc.notReadyReasons();
                reason = _formatReason(reason);
                ciscoStateId = _.isEmpty(reason) ? 300 : reasons[reason].id;
            }
            if (['COMMAND_FAILED', 'CTI_DISCONNECTED', 'CTI_SHUTDOWN', 'AGENT_LOGON_FAILED'].includes(name)) {
                ai = reason;
            }

            if (Object.keys(_events).indexOf(event) >= 0) {
                _.forEach(_events[event], function (func) {
                    if (name === 'DEFAULT_EVENT') return;
                    if (_.isFunction(func)) {
                        func(name, event, cli, ddi, serial, timeEvent, timeArrived, trunk, td, ai);
                    }
                });
            }

            _.forEach(_events.CATCH_ALL, function (func) {
                if (_.isFunction(func)) {
                    func(name, event, cli, ddi, serial, timeEvent, timeArrived, trunk, td, ai);
                }
            });

            _.forEach(_events.DEFAULT_EVENT, function (func) {
                if (_.isFunction(func)) {
                    func(name, event, cli, ddi, serial, timeEvent, timeArrived, trunk, td, ai, ciscoStateId);
                }
            });

            if (!$rootScope.$$phase) $rootScope.$apply();
        }

        _.extend(svc, {
            init: function (ip, debug, logging) {
                var def = $q.defer();

                def.resolve('loaded');
                _cti = { server: null };

                if (debug === false) _debug = debug;

                return def.promise.then(function () {
                    _init = true;
                });
            },
            /**
             * Get CTI server instance
             * @param agentId {number}
             * @param finesseDomain {string}
             * @param handler {(eventData) => void}
             * @param options {object}
             * @return {{sendCommand: function(*, *, *): void, shutdown: function(): void}}
             */
            getCTIServer: (agentId, finesseDomain, handler, options) => {
                try {
                    return new AAFinesse(agentId, finesseDomain, handler, options);
                } catch (error) {
                    throw new Error('Failure while creating CTI server instance');
                }
            },
            initFinesse: (user) => {
                let { finesseHost, finesseDomain, secondaryFinesseHost, webSockets } = _finesseEnv;
                let options = {
                    ConnectionRetryInterval: 10,
                    MaxReconnectAttempts: 5,
                    FinesseHost: finesseHost.toString(),
                    SecondaryFinesseHost: secondaryFinesseHost ? secondaryFinesseHost.toString() : '',
                    debug: true,
                    unsecureAllowed: false,
                    webSockets: webSockets && parseInt(webSockets) ? true : false
                };

                _cti.server = svc.getCTIServer(user.agentId, finesseDomain.toString(), _eventHandler, options);

                UnavailableService.init(svc, svc.run, _cti);

                window.onbeforeunload = (evt) => {
                    /**
                     * Make agent unavailable in case of browser crashes or reloads.
                     */
                    let defaultUnavailableReason = _.find(_notReadyReasons, ['friendly', 'Unavailable']);
                    if (_cti && _cti.server) {
                        _cti.server.sendCommand('AGENT_NOT_AVAILABLE', defaultUnavailableReason.ciscoId);
                    }
                    evt.returnValue = '';
                    return null;
                };

                $rootScope.$watch(
                    function () {
                        return svc.status;
                    },
                    function () {
                        _.forEach(_events.STATUS, function (func) {
                            func(svc.status);
                        });
                    }
                );
            },
            isInit: function () {
                return _init;
            },
            run: function (callback) {
                var def = $q.defer();
                callback();
                def.resolve('function complete');
                return def.promise;
            },
            subscribe: function (event, callback) {
                if (!_events[event]) _events[event] = [];

                _events[event].push(callback);

                return _events[event].indexOf(callback);
            },
            unsubscribe: function (event, index) {
                _events.CTI_CONNECTED.splice(index, 1);
            },
            notReadyReasons: function (...args) {
                return args.length ? (_notReadyReasons = args[0]) : _notReadyReasons;
            },
            getCiscoNotReadyReason: function (reason) {
                let mappedReason = _.find(_notReadyReasons, (notReadyReason) => {
                    return notReadyReason.id === reason;
                });
                return mappedReason ? mappedReason.ciscoId : _.find(_notReadyReasons, ['friendly', 'Unavailable']); //return default unavailable reason
            },
            // CTI functions
            cti: function () {
                return _cti;
            },
            cli: function cliAcessor(...args) {
                return args.length ? (_cli = args[0]) : _cli;
            },
            connect: function (user) {
                var def = $q.defer();
                svc.ciscoCTIUser(user);
                let { extension, password } = user;

                if (!user || !user.agentId) {
                    return def.reject('agentId required');
                }

                svc.initFinesse(user);
                _cti.server.sendCommand('AGENT_LOGON', extension.toString(), password.toString());

                def.resolve(1);
                return def.promise.then(function () {});
            },
            disconnect: function () {
                var def = $q.defer();
                def.resolve(true);
                if (_cti && _cti.server) {
                    _cti.server.shutdown();
                    _cti.server = null;
                }

                return def.promise.catch(function () {
                    console.log('error caught');
                });
            },

            sendCommand: function (cmd, data, arg) {
                if (!_.isEmpty(cmd)) {
                    switch (cmd) {
                        case 'AGENT_LOGOFF':
                            data = _user.agentId;
                            break;
                        case 'AGENT_NOT_AVAILABLE':
                            data = _.toString(svc.getCiscoNotReadyReason(data));
                            break;
                    }
                    console.log('---- Sending Command ---->', cmd, data);
                    _cti.server.sendCommand(cmd, data, arg);
                }
            },

            subscribeOnce: function subscribeOnce(event, callback) {
                var funcToCall = function (name, event, cli, ddi, serial, timeEvent, timeArrived, trunk, td, ai) {
                        callback(name, event, cli, ddi, serial, timeEvent, timeArrived, trunk, td, ai);

                        _events[event].splice(index, 1);
                    },
                    index;

                if (!_events[event]) _events[event] = [];

                _events[event].push(funcToCall);

                index = _events[event].indexOf(funcToCall);
                return index;
            },
            status: 'UNAVAILABLE',
            unavailable: function () {
                return UnavailableService.menuItems();
            },
            finesseEnv: function (...args) {
                return args.length ? (_finesseEnv = args[0]) : _finesseEnv;
            },
            getRegisteredEvents: () => _events,
            ciscoCTIUser: function (...args) {
                return args.length ? (_user = args[0]) : _user;
            }
        });
    }
]);
