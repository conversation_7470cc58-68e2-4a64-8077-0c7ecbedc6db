var _ = require('lodash'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model'),
    CreateReasonQuery = require('@aa/malstrom-models/lib/create-reasons-query.model');
const Task = require('@aa/malstrom-models/lib/task.model');

require('angular');

module.exports = angular
    .module('aah-create-reasons-service-module', [
        //constants
        require('../../constants/create-reasons/create-reasons-urls.constants').name,

        //services
        require('../../services/task/task.service').name
    ])
    .service('aahCreateReasonsService', [
        '$http',
        'aahCreateReasonsURLs',
        'aahTaskService',
        'envService',
        function CreateReasonsService($http, createReasonsUrls, TaskService, envService) {
            var svc = this,
                _createReasons = [],
                _reopenReasons = [],
                _reattendReasons = [];

            function _getUiCompatibleCreateReasons(createReasons, task) {
                const uiCompatibleCreateReasons = [];
                if (Array.isArray(createReasons)) {
                    createReasons.forEach((createReason) => {
                        // We are splitting Hotel/Public Transport into Hotel and Public Transport for presentation purpose only
                        if (!CreateReason.INCOMPATIBLE_WITH_AAH2.includes(createReason.id)) {
                            if (createReason.id === CreateReason.HOTEL) {
                                // Make Hotel & Public Transport option open for live tasks
                                if (![Task.COMP_STATUS, Task.CLSD_STATUS].includes(task.status())) {
                                    // Split HOTEL/Public Transport createReason into HOTEL & Public Transport
                                    const hotelCreateReason = new CreateReason(createReason);
                                    const publicTransportCreateReason = new CreateReason(createReason);
                                    hotelCreateReason.name('Hotel');
                                    publicTransportCreateReason.name('Alternate Transport');
                                    publicTransportCreateReason.isPublicTransport(true);
                                    uiCompatibleCreateReasons.push(hotelCreateReason);
                                    uiCompatibleCreateReasons.push(publicTransportCreateReason);
                                }
                            } else {
                                uiCompatibleCreateReasons.push(new CreateReason(createReason));
                            }
                        }
                    });
                }
                return uiCompatibleCreateReasons;
            }

            function _enableAddTaskStatuses() {
                return [
                    Task.INIT_STATUS,
                    Task.CHCK_STATUS,
                    Task.PLAN_STATUS,
                    Task.PACK_STATUS,
                    Task.HEAD_STATUS,
                    Task.HIRE_STATUS,
                    Task.GDET_STATUS,
                    Task.GARR_STATUS,
                    Task.TIDY_STATUS,
                    Task.WRAP_STATUS,
                    Task.LOAD_STATUS,
                    Task.UNLD_STATUS
                ];
            }

            _.extend(svc, {
                getByTaskId: function getByTaskId(task) {
                    const taskId = task.id();
                    _createReasons = [];
                    return $http
                        .get(createReasonsUrls.TASK, {
                            params: {
                                taskId: taskId
                            }
                        })
                        .then(function (httpResponse) {
                            _createReasons = _getUiCompatibleCreateReasons(httpResponse.data, task);
                            return _createReasons;
                        });
                },
                getReattendReasons: function getReattendReasons(task) {
                    const createReason = task.createReason();
                    const outcome = task.fault().outcome();
                    const customerGroup = task.entitlement().customerGroup();
                    var param = new CreateReasonQuery();
                    param.createReason(createReason);
                    param.outcome(outcome);
                    param.customerGroup(customerGroup);
                    return $http
                        .post(createReasonsUrls.REATTEND, {
                            param: param.toJSON()
                        })
                        .then(function (httpResponse) {
                            return _getUiCompatibleCreateReasons(httpResponse.data, task);
                        });
                },
                getReopenReasons: function getReopenReasons(task) {
                    const createReason = task.createReason();
                    const outcome = task.fault().outcome();
                    const customerGroup = task.entitlement().customerGroup();
                    var param = new CreateReasonQuery();
                    param.createReason(createReason);
                    param.outcome(outcome);
                    param.customerGroup(customerGroup);
                    return $http
                        .post(createReasonsUrls.REOPEN, {
                            param: param.toJSON()
                        })
                        .then(function (httpResponse) {
                            return _getUiCompatibleCreateReasons(httpResponse.data, task);
                        });
                },
                createReasons: function createReasonsAccessor(val) {
                    return arguments.length ? (_createReasons = val) : _createReasons;
                },
                reset: function reset() {
                    _createReasons = [];
                    _reopenReasons = [];
                    _reattendReasons = [];
                },
                reopenReasons: function reopenReasonsAccessorAccessor(val) {
                    return arguments.length
                        ? (_reopenReasons = val)
                        : _reopenReasons.filter(function (dataItem) {
                              return ![CreateReason.STORAGE, CreateReason.SCRAPPED, CreateReason.GARAGE_REPAIR].includes(dataItem.id()); // RBAUAA-11421 : Removed Storage, Scrapped and Garage Repair from left panel add task
                          });
                },
                reattendReasons: function reattendReasons(val) {
                    return arguments.length ? (_reattendReasons = val) : _reattendReasons;
                },
                getReasonByParentTaskId: function getByTaskId() {
                    return new CreateReason({
                        id: CreateReason.HIRE_CAR,
                        name: 'Hire Car',
                        serviceType: 'ADMIN',
                        createBehaviours: ['CB_REOPEN', 'CB_DIAG', 'CB_RPLUS', 'CB_RSS', 'CB_YES'],
                        generalBehaviours: ['GB_NEW_ARR', 'GB_NO_DEST'],
                        trackBehaviours: [],
                        isPublicTransport: false
                    });
                },
                fetchReopenReattendReasons: function fetchReopenReattendReasons(task) {
                    let allowCLSDStatusForBolt =
                        _.includes([Task.CLSD_STATUS], task.status()) &&
                        (task.entitlement().customerGroup().isAllJLR() || task.entitlement().customerGroup().isPorsche() || task.entitlement().customerGroup().isHyundai());
                    svc.reset();
                    if (task.isCompletedWithin(7) && (allowCLSDStatusForBolt || _.includes([Task.COMP_STATUS], task.status()))) {
                        svc.getReopenReasons(task).then(function (reopenReasons) {
                            svc.reopenReasons(reopenReasons);
                        });
                        svc.getReattendReasons(task).then(function (reattendReasons) {
                            svc.reattendReasons(reattendReasons);
                        });
                    } else if (_enableAddTaskStatuses().indexOf(task.status()) > -1) {
                        svc.getByTaskId(task).then(function (reopenReasons) {
                            svc.reopenReasons(reopenReasons);
                        });
                    }
                }
            });
        }
    ]);
