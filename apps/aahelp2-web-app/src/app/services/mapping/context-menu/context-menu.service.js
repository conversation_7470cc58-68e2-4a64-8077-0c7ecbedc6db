var _ = require('lodash'),
    Prompt = require('@aa/malstrom-models/lib/prompt.model'),
    Location = require('@aa/malstrom-models/lib/location.model');

require('angular');

var LatLong = require('@aa/malstrom-models/lib/lat-long.model');

module.exports = angular
    .module('aah-mapping-context-menu-service-module', [
        // services
        require('../mapping.service').name,
        require('../google/google.service').name,
        require('../location/location.service').name,
        require('../../connected-car/connected-car.service').name,
        require('../../task/task.service').name,
        require('../../diagnostics/diagnostics.service').name,
        require('../../csh/csh.service').name,
        require('../../../constants/mapping/google-maps-place-types.constants').name,
        require('../../search/search.service').name,
        require('../../alert/alert.service').name,
        require('../../prompt/prompt.service').name,
        require('../../recovery/recovery.service').name,

        require('../../../constants/alert/alert-type.constants').name,

        require('../../../factories/apply.factory').name,
        require('../../../services/cti/line-management.service').name,
        require('../../mobility/mobility-retailer.service').name,
        require('../../../services/tab/tab.service').name,
        require('../../../services/task-audit/task-audit.service').name,
        require('../../../services/tab/tab.service').name,
        require('../../../services/address-lookup/address-lookup.service').name,

        require('../../../helpers/processLocation/accuracy-helper').name
    ])
    .service('aahContextMenuService', [
        '$rootScope',
        '$window',
        'aahApply',
        'aahGoogleService',
        'aahMappingService',
        'aahTaskService',
        'aahAddressLookupService',
        'aahConnectedCarService',
        'aahCSHService',
        'aahDiagnosticsService',
        'aahLocationService',
        'aahLocationTextTypes',
        'aahLocationAreaTypes',
        'aahAccuratePlaceTypes',
        'aahSearchService',
        'aahAlertService',
        'uibAlertTypes',
        'aahPromptService',
        'aahRecoveryService',
        '$state',
        'aahLineManagementService',
        'aahMobilityRetailerService',
        'aahTaskAuditService',
        '$timeout',
        'aahTabService',
        'aahContractValidationService',
        function ContextMenuService(
            $rootScope,
            $window,
            Apply,
            GoogleService,
            MappingService,
            TaskService,
            AddressLookupService,
            ConnectedCarService,
            CshService,
            DiagnosticsService,
            LocationService,
            LocationTextTypes,
            LocationAreaTypes,
            AccuratePlaceTypes,
            SearchService,
            AlertService,
            AlertTypes,
            PromptService,
            RecoveryService,
            $state,
            LineManagementService,
            MobilityRetailerService,
            TaskAuditService,
            $timeout,
            TabService,
            ContractValidationService
        ) {
            var svc = this,
                _locationMenus = {},
                _relayMenus = {},
                _mobilityOptionsMenu = {},
                _processAddressComponents = require('../../../factories/construct-address-array.factory'),
                _mainLine = LineManagementService.getMainLine();

            function constructLocationText(locationText, overRideLocationText, houseNoOrName) {
                var i;
                if (locationText.length) {
                    if (houseNoOrName) {
                        for (i = locationText.length - 1; i >= 0; i--) {
                            if (houseNoOrName.indexOf(locationText[i].toUpperCase()) > -1) {
                                locationText.splice(i, 1);
                            }
                        }
                        // condition to check if houseNoOrName has locationText
                        if (houseNoOrName.includes(locationText)) {
                            return houseNoOrName;
                        } else {
                            return locationText.length ? houseNoOrName + ', ' + locationText : houseNoOrName;
                        }
                    }
                    return locationText;
                }
                return overRideLocationText;
            }

            function menuBreakdownHandler(latLng, results, addMarker, overrideLocationText, houseNoOrName) {
                let result = results[0],
                    locationText;
                let mwayNameRegEx = new RegExp('(M6\\s?Toll|[A|M]\\s?\\d+\\s?(M\\b)?(\\(M\\))?)', 'gi'),
                    mwayName = mwayNameRegEx.test(SearchService.locationSearch().search());
                if (mwayName) {
                    let regex = /^[MmAaBb]\s*\d{1,4}/,
                        matchedLocation =
                            _.find(results, (googleResult) => {
                                return regex.test(googleResult.formatted_address);
                            }) || LocationService.getRouteTypeLocation(results);
                    if (matchedLocation) {
                        result = matchedLocation;
                    }
                }

                if (TaskService.task().isMoveable()) {
                    MappingService.map().removeOverlay(MappingService.assistanceMarker());

                    locationText = constructLocationText(_processAddressComponents(result.address_components, LocationTextTypes), overrideLocationText, houseNoOrName);

                    TaskService.saveLocationText(locationText);
                    TaskService.saveLocationArea(_processAddressComponents(result.address_components, LocationAreaTypes));

                    TaskService.saveLocation(latLng);

                    addMarker(latLng.lat(), latLng.lng());

                    TaskService.task().isDirty(true);

                    getNearby(latLng, result);

                    MappingService.centerOnBreakdown();
                    LocationService.isMotorway(latLng, result);
                    // .then(() => {
                    //     if(DiagnosticsService.selectedAnsweredQuestion() && !DiagnosticsService.selectedAnsweredQuestion().selectedAnswer().recoveryJobInd() && !TaskService.task().indicators().motorway() && TaskService.task().recovery().relay()) {
                    //         DiagnosticsService.clearRelayOnDignosed();
                    //         RecoveryService.resetRecovery();
                    //     }
                    // });
                }
            }

            function menuRelayHandler(latLng, results, addMarker) {
                var result = results[0];
                if (result && !GoogleService.locationInBounds(latLng)) {
                    return AlertService.createAlert('The location chosen is outside the UK , Please choose a valid location');
                }

                return TaskService.setRelayRecoveryDestination(
                    latLng.lat(),
                    latLng.lng(),
                    _processAddressComponents(result.address_components, LocationTextTypes),
                    _processAddressComponents(result.address_components, LocationAreaTypes)
                ).then(function setRelayRecoveryDestinationResult(success) {
                    if (success) {
                        addMarker(latLng.lat(), latLng.lng());
                        // Reset relay questions and answers when user change location
                        DiagnosticsService.initialiseDiagnosticsQA();
                        getNearby(latLng, result);
                        MappingService.centerOnRelay();
                        TaskService.task().recovery().destResourceId(null);
                    }
                });
            }

            function getNearby(latLng, result, limit) {
                SearchService.locationResults().compiled = [];
                MappingService.map().removeMarkers();

                //this is to keep the original address in tact in case we get the results by shortening it in the geocode call
                if (!result.formatted_address) {
                    result.formatted_address = getHomeAddress();
                }

                //if(!SearchService.isW3WSearch()){
                SearchService.locationResults().compiled.push(result);
                //}

                // force the first result to have same latLng as where the user clicked rather than googles
                if (!result.geometry) {
                    result.geometry = {
                        location: latLng
                    };
                } else {
                    result.geometry.location = latLng;
                }

                GoogleService.places().nearbySearch(
                    {
                        radius: 100,
                        location: latLng
                    },
                    function (nearbyResults, status) {
                        result.nearby = [];

                        if (limit) {
                            nearbyResults = nearbyResults.splice(0, limit);
                        }

                        result.nearby = nearbyResults;

                        if (SearchService.isW3WSearch()) {
                            MappingService.addResultMarkers(nearbyResults);
                        } else {
                            MappingService.addResultMarkers([result].concat(nearbyResults));
                        }

                        MappingService.selectedLocation(result);

                        Apply();
                    }
                );
            }

            function checkTypes(types) {
                var checks = ['country', 'political'],
                    magicNumber = 0;

                _.forEach(checks, function (type) {
                    magicNumber += types.indexOf(type);
                });

                return magicNumber !== -2;
            }

            function getHomeAddress() {
                var address;

                try {
                    address = CshService.entitlement().contact().address().addressAsString();
                } catch (e) {
                    address = TaskService.task().entitlement().memberAddress().join(',');
                }
                return address;
            }

            function hasTelematicsLocationData() {
                const telematicsData = ConnectedCarService.getCurrent();
                try {
                    return telematicsData.location().coordinates().latitude() && telematicsData.location().coordinates().longitude();
                } catch (err) {
                    return false;
                }
            }

            function serviceAbuseClosePromptAction() {
                //Service Abuse: reset and close service abuse prompt
                PromptService.resetOutcomeObject(TaskService.task());
                //hide audit panel (in case we pre-populated call info)
                TaskAuditService.hideAudit();
            }

            function resetSearchValues() {
                SearchService.locationSearch().search('');
                SearchService.locationSearch().what3WordsSearch('');
            }

            _.extend(svc, {
                constructMenu: function constructMenu(state, menu) {
                    switch (state) {
                        case 'location':
                            switch (menu) {
                                case 'map':
                                    _locationMenus[menu] = [
                                        {
                                            title: 'Centre here',
                                            name: 'center_here',
                                            action: svc.setCenter
                                        },
                                        {
                                            title: 'Mobile Lookup',
                                            name: 'SearchMobileLookup',
                                            action: svc.addManualCallerLocation
                                        }
                                    ];

                                    // relay to home is only shown if that task is RSS
                                    if (getHomeAddress() && !TaskService.task().createReason().isRelayLeg()) {
                                        _locationMenus[menu].unshift({
                                            title: 'At Home',
                                            name: 'Homestart',
                                            action: svc.setAsHomeStart
                                        });
                                    }

                                    if (hasTelematicsLocationData()) {
                                        _locationMenus[menu].unshift({
                                            title: 'Smart Breakdown',
                                            name: 'Smart Breakdown',
                                            action: svc.setAsConnectedCar
                                        });
                                    }

                                    if (MappingService.map().getZoom() >= 17) {
                                        _locationMenus[menu].unshift({
                                            title: 'Set location',
                                            name: 'add_marker',
                                            action: svc.setBreakdownLocation
                                        });
                                    }

                                    break;
                                case 'marker':
                                    _locationMenus[menu] = [
                                        {
                                            title: 'Set location',
                                            name: 'add_marker',
                                            action: svc.setBreakdownLocationOnMarker
                                        },
                                        {
                                            title: 'Center here',
                                            name: 'center_here',
                                            action: svc.setCenter
                                        }
                                    ];
                                    break;
                            }
                            break;
                        case 'relay':
                            switch (menu) {
                                case 'map':
                                    _relayMenus[menu] = [
                                        {
                                            title: 'Center here',
                                            name: 'center_here',
                                            action: svc.setCenter
                                        }
                                    ];

                                    // relay to home is not shown for isRelayLeg
                                    if (getHomeAddress() && !TaskService.task().createReason().isRelayLeg()) {
                                        _relayMenus[menu].unshift({
                                            title: 'Relay to home',
                                            name: 'Homestart',
                                            action: svc.relayToHome
                                        });
                                    }

                                    if (MappingService.map().getZoom() >= 17) {
                                        _relayMenus[menu].unshift({
                                            title: 'Set destination',
                                            name: 'add_marker',
                                            action: svc.setRelayDestination
                                        });
                                    }

                                    break;
                                case 'marker':
                                    _relayMenus[menu] = [
                                        {
                                            title: 'Set destination',
                                            name: 'add_marker',
                                            action: svc.setRelayDestination
                                        },
                                        {
                                            title: 'Center here',
                                            name: 'center_here',
                                            action: svc.setCenter
                                        }
                                    ];
                                    break;
                            }
                            break;
                        case 'moblityOptions':
                            switch (menu) {
                                case 'map':
                                    _mobilityOptionsMenu[menu] = [
                                        {
                                            title: 'Mobility options',
                                            name: 'add_marker',
                                            action: svc.getNearestRetailers
                                        }
                                    ];
                                    break;
                            }
                            break;
                    }
                },
                addManualCallerLocation: function addManualCallerLocation() {
                    /*return LocationService.addManualCallerLocation(_mainLine.cli)
                    .then(function () {
                        LocationService.addHomeStartFence();
                        MappingService.moveToBounds();
                    });*/
                    $window.open('https://aa-ap951az.theaa.local/CustomerLocation/Default.aspx', '_blank');
                },
                locationMenus: function locationMenusAcessor(menu) {
                    if (!LocationService.europeanPoiChecked()) {
                        svc.constructMenu('location', menu);
                    }
                    return _locationMenus[menu];
                },
                relayMenus: function relayMenusAcessor(menu) {
                    if (!LocationService.europeanPoiChecked()) {
                        svc.constructMenu('relay', menu);
                    }
                    return _relayMenus[menu];
                },
                mobilityOptionsMenus: (menu) => {
                    if (!LocationService.europeanPoiChecked()) {
                        svc.constructMenu('moblityOptions', menu);
                    }
                    return _mobilityOptionsMenu[menu];
                },
                relayToHome: function relayToHome(e) {
                    GoogleService.geocode({
                        address: getHomeAddress()
                    }).then(function geoCodePromiseResolved(results) {
                        if (!results || (results && !results.length)) {
                            AlertService.createAlert("Can't relay to home - home address is invalid  (set location manually).");
                            return;
                        }

                        menuRelayHandler(results[0].geometry.location, results, MappingService.addRelayMarker).then(() => {
                            TaskService.task().recovery().destResourceId(-2);
                            Apply();
                        });
                    });
                },
                setAsHomeStart: function contextMenuActionSetHomestart() {
                    const task = TaskService.task();
                    let isAtHomeStartAllowed = false;
                    if (!task.entitlement().hasHomestart() && task.createReason().isBatteryAssist()) {
                        isAtHomeStartAllowed = true;
                    }

                    let postcode, postCodeValidationFn;

                    postcode = CshService.entitlement().contact().address().postcode();
                    postcode = postcode ? postcode.toUpperCase() : postcode;
                    postCodeValidationFn = AddressLookupService.postCodePatternService();

                    if (!postCodeValidationFn.test(postcode)) {
                        return AlertService.createAlert('Post code Invalid : Please confirm and enter manually');
                    }

                    // Reset w3w search
                    resetSearchValues();
                    GoogleService.geocode({ address: getHomeAddress() }).then(function geoCodePromiseResolved(results) {
                        if (!results || (results && !results.length)) {
                            AlertService.createAlert("Can't set as homestart - home address is invalid  (set location manually).");
                            return;
                        }

                        if (results.length && !GoogleService.locationInBounds(results[0].geometry.location)) {
                            return AlertService.createAlert('The location chosen is outside the UK , Please choose a valid location');
                        }

                        serviceAbuseClosePromptAction();

                        const setHomestart = () => {
                            var houseNoOrName = CshService.entitlement().contact().address().addressLines()[0];
                            task.indicators().homeStart(true);
                            $state.go('location.mapping');
                            menuBreakdownHandler(results[0].geometry.location, results, MappingService.addAssistanceMarker, getHomeAddress(), houseNoOrName);
                            Apply();
                        };

                        if (ContractValidationService.isAdHockPromptCheckRequired(task) || isAtHomeStartAllowed) {
                            // timeout added so that the prompts/config is cleared before trying to load a new prompt
                            $timeout(() => {
                                PromptService.checkHomestartEntitlement(task, CshService.csh(), CshService.entitlement()).then(function checkHomestartEntitlementSuccess(success) {
                                    if (success) {
                                        setHomestart();
                                    }
                                });
                            });
                        } else {
                            setHomestart();
                        }
                    });
                },
                setAsConnectedCar: function contextMenuActionSetConnectedCar() {
                    let position,
                        latlong = GoogleService.latLng(ConnectedCarService.getCurrent().location().coordinates().latitude(), ConnectedCarService.getCurrent().location().coordinates().longitude());
                    PromptService.removePromptById(Prompt.INACCURATE_LOCATION);
                    GoogleService.geocode({
                        location: latlong
                    }).then(function handleGeocodeResult(results) {
                        if (results.length === 1 && checkTypes(results[0].types)) {
                            return;
                        }
                        serviceAbuseClosePromptAction();
                        //check if motorway
                        if (
                            LocationService.isMotorway(latlong, null).then((isMotorway) => {
                                if (isMotorway) {
                                    LocationService.getNearestJunction(latlong)
                                        .then((junction) => {
                                            position = GoogleService.latLng(junction.geometry.coordinates[0], junction.geometry.coordinates[1]);
                                            menuBreakdownHandler(position, results, MappingService.addAssistanceMarker);
                                            Apply();
                                        })
                                        .catch(function nearestJunctionError(error) {
                                            //TODO handle this error to alert the user
                                            return error;
                                        });
                                } else {
                                    menuBreakdownHandler(latlong, results, MappingService.addAssistanceMarker);
                                    Apply();
                                }
                            })
                        );
                    });
                },
                setBreakdownLocation: function setBreakdownLocation(e) {
                    let position,
                        originLatLong = `${e.latLng.lat()},${e.latLng.lng()}`; // Check for Homestart entitlement, if user is under 1/4mile of Home location

                    PromptService.removePromptById(Prompt.INACCURATE_LOCATION);
                    PromptService.removePromptById(Prompt.LOCAL_DRIVER_PROMPT);
                    if (MappingService.map().getZoom() >= 17) {
                        GoogleService.pinnedLocation({
                            location: e.latLng
                        }).then(function handleGeocodeResult(results) {
                            if (GoogleService.isIrelandAddress(results[0].address_components)) {
                                PromptService.ROI();
                            } else {
                                let resultTypes = results[0].types || results[0].address_components[0].types;

                                if (results.length === 1 && checkTypes(resultTypes)) {
                                    return;
                                }

                                serviceAbuseClosePromptAction();

                                //check if motorway
                                if (
                                    LocationService.isMotorway(e.latLng, results[0]).then((isMotorway) => {
                                        menuBreakdownHandler(e.latLng, results, MappingService.addAssistanceMarker);
                                        svc.checkNearHomeStart(originLatLong);
                                        Apply();
                                    })
                                );
                            }
                        });
                    } else {
                        AlertService.createAlert('Zoom in closer to set accurate location.', AlertTypes.DANGER);
                    }
                },
                checkNearHomeStart: (originLatLong, ignoreAdHockPromptCheck = false) => {
                    const task = TaskService.task();

                    return GoogleService.geocode({ address: getHomeAddress() })
                        .then((geoResult) => {
                            if (!geoResult || (geoResult && !geoResult.length)) {
                                return new Error("Can't set as homestart - home address is invalid (set location manually).");
                            }

                            let destinationLatLong = `${geoResult[0].geometry.location.lat()},${geoResult[0].geometry.location.lng()}`;

                            return RecoveryService.calculateDistanceInMiles(originLatLong, destinationLatLong);
                        })
                        .then((calculatedDistance) => {
                            const ONE_FORTH_OF_A_MILE = 0.25;
                            let remarks = task.location().remarks(),
                                remarksToAppend = task.location().remarksToAppend();

                            if (calculatedDistance <= ONE_FORTH_OF_A_MILE) {
                                const setHomestart = () => {
                                    task.indicators().homeStart(true);
                                    $state.go('location.mapping');
                                    Apply();
                                };

                                if (ignoreAdHockPromptCheck || ContractValidationService.isAdHockPromptCheckRequired(task)) {
                                    PromptService.checkHomestartEntitlement(task, CshService.csh(), CshService.entitlement()).then(function checkHomestartEntitlementSuccess(success) {
                                        if (success) {
                                            setHomestart();
                                        } else {
                                            task.location(new Location());
                                            task.location().remarks(remarks);
                                            task.location().remarksToAppend(remarksToAppend);
                                            TabService.touch('location');
                                        }
                                    });
                                } else {
                                    setHomestart();
                                }
                            } else if (_.isUndefined(calculatedDistance)) {
                                task.location(new Location());
                                task.location().remarks(remarks);
                                task.location().remarksToAppend(remarksToAppend);
                            } else if (task.indicators().homeStart()) {
                                // clearing 24 hour exclusion product if product homestart is set to true
                                // when breakdown location changed to a non-homestart location
                                PromptService.clearCoolingOffOutcomes(task, 'Homestart');
                                //task.indicators().homeStart(false);
                            }
                        })
                        .catch((error) => {
                            // if error, just proceed - we dont want homestart check block breakdown
                            return;
                        });
                },
                setBreakdownLocationOnMarker: function setBreakdownLocationOnMarker(e) {
                    var marker = e.marker;

                    if (marker.infoWindow) marker.infoWindow.close();

                    // Reset w3w search
                    resetSearchValues();
                    svc.setBreakdownLocation(e);
                },
                setCenter: function setCenter(e) {
                    MappingService.map().setCenter(e.latLng.lat(), e.latLng.lng());
                },
                setRelayDestination: function setRelayDestination(e) {
                    GoogleService.geocode({
                        location: e.latLng
                    }).then(function (geocodeResults) {
                        if (geocodeResults.length === 1 && checkTypes(geocodeResults[0].types)) {
                            return;
                        }
                        menuRelayHandler(e.latLng, geocodeResults, MappingService.addRelayMarker).then(() => {
                            Apply();
                        });
                    });
                },
                getNearestRetailers: (e) => {
                    let retailerLocation;

                    if (e.latLng) {
                        retailerLocation = new LatLong({
                            latitude: e.latLng.lat(),
                            longitude: e.latLng.lng()
                        });
                    }
                    MobilityRetailerService.resetRetailerMarkers();
                    MobilityRetailerService.fetchRetailersAndResources(retailerLocation);
                },
                getNearby: getNearby
            });
        }
    ]);
