var _ = require('lodash'),
    PriorityReason = require('@aa/malstrom-models/lib/priority-reason.model'),
    Location = require('@aa/malstrom-models/lib/location.model'),
    LatLong = require('@aa/malstrom-models/lib/lat-long.model');

const Retailer = require('@aa/mobility-models/lib/hire-site.model');

require('angular');
const _processAddressComponents = require('../../../factories/construct-address-array.factory');

module.exports = angular
    .module('aah-location-service-module', [
        require('../../../constants/mapping/mapping-urls.constants').name,
        require('../../../constants/vehicle/vehicle-fault.constants').name,

        require('../../task/task.service').name,
        require('../../tab/tab.service').name,
        require('../google/google.service').name,
        require('../../priority/priority.service').name,
        require('../../vehicle/vehicle.service').name,
        require('../../prompt/prompt.service').name,
        require('../../csh/csh.service').name,
        require('../../diagnostics/diagnostics.service').name,
        require('../../limited-membership/limited-membership.service').name,

        //factories
        require('../../../factories/safety-advice.factory').name,
        require('../../../factories/apply.factory').name,

        //constants
        require('../../../constants/priority/priority-reasons.constants').name,
        require('../../../constants/mapping/google-maps-place-types.constants').name,
        require('../../../constants/faults/faults.constants').name,
        require('../../../constants/recovery/recovery.constants').name
    ])
    .service('aahLocationService', [
        '$compile',
        '$http',
        '$q',
        '$rootScope',
        '$interval',
        '$timeout',
        'aahCategoryTypes',
        'aahTaskService',
        'aahGoogleService',
        'aahTabService',
        'aahPriorityService',
        'aahVehicleService',
        'aahVehicleFaults',
        'aahSafetyAdvice',
        'aahPriorityReasonsConstants',
        'aahApply',
        'aahMappingURLs',
        'aahLocationTextTypes',
        'aahLocationAreaTypes',
        'aahFaultsConstants',
        'aahRecoveryConstants',
        'aahPromptService',
        'aahCSHService',
        'aahDiagnosticsService',
        'aahContractValidationService',
        'aahDestResourceIDsConstants',
        function LocationService(
            $compile,
            $http,
            $q,
            $rootScope,
            $interval,
            $timeout,
            CategoryTypes,
            TaskService,
            GoogleService,
            TabService,
            PriorityReasonsService,
            VehicleService,
            VehicleFaults,
            SafetyAdvice,
            PriorityReasonsConstants,
            Apply,
            URLs,
            LocationTextTypes,
            LocationAreaTypes,
            FaultConstants,
            RecoveryConstants,
            PromptService,
            CshService,
            DiagnosticsService,
            ContractValidationService,
            DestResourceIDsConstants
        ) {
            var svc = this,
                _map,
                _assistanceMarker,
                _relayMarker,
                _resourceMarker,
                _breakdownMarkerScope,
                _resourceMarkerScope,
                _accuracyCircle,
                _beaconCircle,
                _homeStartCircle,
                _homeStartMarker,
                _depotMarker,
                _selectedLocation,
                MAX_LOC_FIELD_LENGTH = 75,
                _disableBoundingEvent = false,
                _dangerousLocationIsManualOverridden = false,
                _europeanPoi = false,
                _processAddressComponents = require('../../../factories/construct-address-array.factory'),
                _makeId = function _makeid() {
                    var text = '';
                    var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

                    for (var i = 0; i < 5; i++) text += possible.charAt(Math.floor(Math.random() * possible.length));

                    return text;
                },
                _circleRightClick = function (e) {
                    var overlay = new google.maps.OverlayView(),
                        projection,
                        pixel;

                    overlay.draw = function () {};
                    overlay.setMap(_map.map);

                    projection = overlay.getProjection();
                    pixel = projection.fromLatLngToContainerPixel(e.latLng);

                    _.extend(e, {
                        pixel: {
                            x: pixel.x,
                            y: pixel.y - 40
                        }
                    });

                    google.maps.event.trigger(_map.map, 'rightclick', e);
                };

            function setTaskRecoveryFault() {
                VehicleService.getVehicleFault(TaskService.task().fault().id()).then(function getVehicleFaultSuccess() {
                    if (VehicleService.vehicleFault().mwayRecovVehFaultId() === VehicleFaults.NOT_SET) {
                        let _diagnosticsQAList = TaskService.task().recovery().fault().diagnosticsQAList();
                        TaskService.task().recovery().fault(VehicleService.operatorForcedRecoveryVehicleFault());
                        if (_diagnosticsQAList.length > 0) {
                            TaskService.task().recovery().fault().diagnosticsQAList(_diagnosticsQAList);
                        }
                    } else {
                        VehicleService.getVehicleFault(VehicleService.vehicleFault().mwayRecovVehFaultId()).then(function getVehicleFaultForMwayRecoveryFaultSuccess() {
                            TaskService.task().recovery().fault(VehicleService.vehicleFault());
                        });
                    }
                });
            }

            function validLocation(lng, lat, type) {
                var isValid = TaskService.task().isCompleted() || (lng && lat);
                if (!isValid) {
                    PromptService.showInvalidLocationPrompt(type);
                }

                return isValid;
            }

            function _sanitiseValue(value) {
                return value === true || value === 'Y';
            }

            _.extend(svc, {
                init: function init(map) {
                    _map = map;

                    _accuracyCircle = new google.maps.Circle({
                        strokeColor: '#1080DD',
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        fillColor: '#1080DD',
                        fillOpacity: 0.35,
                        map: _map.map,
                        visible: false
                    });

                    _beaconCircle = new google.maps.Circle({
                        strokeColor: '#1080DD',
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        fillColor: '#1080DD',
                        fillOpacity: 0.8,
                        map: _map.map,
                        visible: false
                    });

                    _homeStartCircle = new google.maps.Circle({
                        strokeColor: '#735980',
                        strokeOpacity: 0.4,
                        strokeWeight: 2,
                        fillColor: '#735980',
                        fillOpacity: 0.4,
                        map: _map.map,
                        visible: false,
                        radius: 400
                    });

                    google.maps.event.addListener(_accuracyCircle, 'rightclick', _circleRightClick);
                    google.maps.event.addListener(_beaconCircle, 'rightclick', _circleRightClick);
                    google.maps.event.addListener(_homeStartCircle, 'rightclick', _circleRightClick);
                },
                addBreakdownMarker: function addBreakdownMarker(lat, lng) {
                    // svc.isMotorway(GoogleService.latLng(lat, lng), null);

                    if (!validLocation(lat, lng, 'Breakdown')) {
                        let remarks = TaskService.task().location().remarks(),
                            remarksToAppend = TaskService.task().location().remarksToAppend();

                        TaskService.task().location(new Location());
                        TaskService.task().location().remarks(remarks);
                        TaskService.task().location().remarksToAppend(remarksToAppend);
                        return;
                    }

                    var el = angular.element('<aah-breakdown-marker></aah-breakdown-marker>'),
                        item;

                    if (_breakdownMarkerScope) {
                        _breakdownMarkerScope.$destroy();
                    }

                    item = $compile(el)((_breakdownMarkerScope = $rootScope.$new(true)));

                    _map.removeOverlay(_assistanceMarker);

                    _assistanceMarker = _map.drawOverlay({
                        lat: lat,
                        lng: lng,
                        layer: 'overlayMouseTarget',
                        content: '<div id="breakdown-marker"></div>',
                        verticalAlign: 'top',
                        horizontalAlign: 'left',
                        preDraw: function postDraw() {
                            angular.element(document.getElementById('breakdown-marker')).append(item);
                        }
                    });
                },
                calculatedLocation: function calculatedLocation(lat, lng, accuracy) {
                    var animationStep = 0,
                        radiusStep = accuracy / 100,
                        opacityStep = 0.8 / 100,
                        animation,
                        lastRun = false;

                    _accuracyCircle.setCenter(GoogleService.latLng(lat, lng));
                    _accuracyCircle.setRadius(accuracy);
                    _accuracyCircle.setVisible(true);

                    _beaconCircle.setCenter(GoogleService.latLng(lat, lng));
                    _beaconCircle.setRadius(0);
                    _beaconCircle.setVisible(true);

                    animation = $interval(function () {
                        animationStep++;

                        _beaconCircle.setOptions({
                            radius: radiusStep * animationStep,
                            fillOpacity: 0.8 - opacityStep * animationStep,
                            strokeOpacity: 0.8 - opacityStep * animationStep
                        });

                        if (_beaconCircle.getRadius() >= _accuracyCircle.getRadius()) {
                            _beaconCircle.setRadius(0);
                            animationStep = 0;

                            if (lastRun) {
                                $interval.cancel(animation);
                                _beaconCircle.setVisible(false);
                            }
                        }
                    }, 10);

                    $timeout(function () {
                        lastRun = true;
                    }, 10000);
                },
                addCallerLocation: function addCallerLocation(cli) {
                    var urlPath = '/api/mapping-service/search/mobilePhone';

                    return $http
                        .get(urlPath, {
                            params: {
                                mobilePhoneNumber: cli
                            }
                        })
                        .then(function successCallback(response) {
                            svc.calculatedLocation(response.data.coordinates[1], response.data.coordinates[0], response.data.properties.radiusMeters);
                        });
                },
                addManualCallerLocation: function addManualCallerLocation(cli) {
                    var urlPath = '/api/mapping-service/search/manualMobileLookupManual';

                    return $http
                        .get(urlPath, {
                            params: {
                                mobilePhoneNumber: cli
                            }
                        })
                        .then(function successCallback(response) {
                            svc.calculatedLocation(response.data.coordinates[1], response.data.coordinates[0], response.data.properties.radiusMeters);
                        });
                },
                addHomeStartFence: function addHomeStartFence() {
                    var task = TaskService.task();

                    GoogleService.geocode({
                        address: task.entitlement().memberAddress().join(',')
                    }).then(function (response) {
                        if (!response || (response && !response.length)) {
                            return;
                        }

                        // Add the marker
                        _homeStartMarker = _map.addMarker({
                            lat: response[0].geometry.location.lat(),
                            lng: response[0].geometry.location.lng(),
                            title: 'Customers home address',
                            markerType: 'Customers home address',
                            animation: google.maps.Animation.DROP,
                            icon: {
                                url: 'assets/home.png',
                                scaledSize: new google.maps.Size(38, 39),
                                anchor: new google.maps.Point(0, 39)
                            }
                        });
                        _homeStartMarker.addListener('click', function (e) {
                            const setLocation = () => {
                                task.indicators().homeStart(true);

                                svc.addBreakdownMarker(response[0].geometry.location.lat(), response[0].geometry.location.lng());

                                TaskService.saveLocationText(_processAddressComponents(response[0].address_components, LocationTextTypes));
                                TaskService.saveLocationArea(_processAddressComponents(response[0].address_components, LocationAreaTypes));

                                TaskService.saveLocation(response[0].geometry.location);
                                TaskService.task().isDirty(true);

                                Apply();
                            };

                            if (ContractValidationService.isAdHockPromptCheckRequired(task)) {
                                PromptService.checkHomestartEntitlement(TaskService.task(), CshService.csh(), CshService.entitlement()).then((result) => {
                                    if (result !== false) {
                                        setLocation();
                                    }
                                });
                            } else {
                                setLocation();
                            }
                        });
                    });
                },
                addRelayMarker: function addRelayMarker(lat, lng) {
                    var el = angular.element('<aah-relay-marker></aah-relay-marker>'),
                        item = $compile(el)($rootScope.$new(true));

                    if (!validLocation(lat, lng, 'Relay')) {
                        TaskService.task().recovery().destination(new Location());

                        return;
                    }

                    _map.removeOverlay(_relayMarker);

                    _relayMarker = _map.drawOverlay({
                        lat: lat,
                        lng: lng,
                        layer: 'overlayMouseTarget',
                        content: '<div id="relay-marker-wrapper"></div>',
                        verticalAlign: 'top',
                        horizontalAlign: 'right',
                        preDraw: function postDraw() {
                            angular.element(document.getElementById('relay-marker-wrapper')).append(item);
                        }
                    });
                },
                addDepotMarker: function addRelayMarker(retailer) {
                    let scope = $rootScope.$new(true),
                        retailerElement = angular.element(document.getElementById(`depot-${retailer.id()}`)).children();
                    if (retailerElement && (retailerElement.length >= 1 || !retailer.id())) {
                        if (retailer.id() && retailerElement.scope() && !_.isEqual(retailer, retailerElement.scope().retailer)) {
                            retailerElement.scope().retailer = retailer;
                        }
                        return;
                    }
                    let el = angular.element(
                            '<aah-depot-marker retailer="retailer" ou-availability="retailer.ouAvailability" raf-availability="retailer.rafAvailability" l460-availability="retailer.l460Availability" matching-availability="retailer.matchingAvailability"></aah-depot-marker>'
                        ),
                        item;

                    scope.retailer = retailer;

                    item = $compile(el)(scope);

                    _depotMarker = _map.drawOverlay({
                        lat: retailer.location().latitude(),
                        lng: retailer.location().longitude(),
                        layer: 'overlayMouseTarget',
                        content: `<div id="depot-${retailer.id()}"></div>`,
                        verticalAlign: 'top',
                        horizontalAlign: 'right',
                        preDraw: function postDraw() {
                            if (angular.element(document.getElementById(`depot-${retailer.id()}`)).children().length < 1) {
                                angular.element(document.getElementById(`depot-${retailer.id()}`)).append(item);
                            }
                        }
                    });

                    _depotMarker.scope = scope;
                    return _depotMarker;
                },
                addResourceMarker: function addResourceMarker(lat, lng, update) {
                    var el = angular.element('<aah-resource-marker></aah-resource-marker>'),
                        item,
                        id = _makeId();

                    if (!validLocation(lat, lng, 'Resource')) {
                        return;
                    }

                    item = $compile(el)((_resourceMarkerScope = $rootScope.$new(true)));

                    if (update) {
                        if (_resourceMarker) {
                            _map.removeOverlay(_resourceMarker);
                        }

                        _resourceMarker = _map.drawOverlay({
                            lat: lat,
                            lng: lng,
                            layer: 'overlayMouseTarget',
                            content: '<div class="resource-marker-wrapper" id="' + id + '"></div>',
                            verticalAlign: 'top',
                            horizontalAlign: 'right',
                            preDraw: function postDraw() {
                                angular.element(document.getElementById(id)).append(item);
                            }
                        });
                    }
                },
                getCircles: function getCircles() {
                    var circles = [];

                    if (_homeStartCircle.getBounds() && _homeStartCircle.getCenter()) {
                        circles.push(_homeStartCircle);
                    }

                    if (_accuracyCircle.getBounds() && _accuracyCircle.getCenter()) {
                        circles.push(_accuracyCircle);
                    }

                    if (_beaconCircle.getBounds() && _beaconCircle.getCenter()) {
                        circles.push(_beaconCircle);
                    }

                    return circles;
                },
                getMarkers: function getMarkers() {
                    return {
                        assistance: _assistanceMarker,
                        relay: _relayMarker,
                        resource: _resourceMarker
                    };
                },
                servicesAtJunction: function servicesAtJunction(servicesName) {
                    var isNum, i;
                    for (i = 0; i < servicesName.length; i++) {
                        if (servicesName[i] === 'J') {
                            isNum = /^\d+$/.test(servicesName[i + 1]); //check if next character is a number
                            if (isNum) {
                                return true;
                            }
                        }
                    }
                    return false;
                },
                getNearestJunction: function getNearestJunction(position) {
                    return $http
                        .post(URLs.NEAREST_JUNCTION, {
                            center: [position.lng(), position.lat()]
                        })
                        .then(function nearestJunctionOk(response) {
                            var junction = response.data;

                            _map.addMarker({
                                lat: junction.geometry.coordinates[0],
                                lng: junction.geometry.coordinates[1],
                                title: junction.properties.name,
                                markerType: 'Nearest motorway junction',
                                animation: google.maps.Animation.DROP,
                                icon: {
                                    url: 'assets/motorway.png',
                                    scaledSize: new google.maps.Size(38, 39),
                                    anchor: new google.maps.Point(0, 39)
                                }
                            });

                            return junction;
                        });
                },

                DangerousLocationIsManualOverridden: (val) => {
                    _dangerousLocationIsManualOverridden = val;
                },

                isMotorway: function isMotorway(position, result, isRelayLocation = false) {
                    var resultToUse = result === null ? _.head(GoogleService.results()) : result;
                    return $http.post(URLs.DANGEROUS_LOCATION, resultToUse).then(function isMotorwayV2Success(response) {
                        if (response.data.length > 0) {
                            if (!TaskService.task().indicators().motorway()) {
                                TaskService.task()
                                    .indicators()
                                    .motorway(response.data.length > 0 ? response.data[0].motorway : false);
                                TaskService.task().indicators().dangerousLocation(true);
                                SafetyAdvice();
                            }

                            const isMotorway = TaskService.task().indicators().motorway();
                            const isDangerousLocation = TaskService.task().indicators().dangerousLocation();

                            if (isMotorway || isDangerousLocation) {
                                TaskService.task().recovery().destResourceId(DestResourceIDsConstants.LOCAL);
                            }

                            svc.refreshPriority();
                            svc.getNearestJunction(position);
                            svc.setMotorwayRelayInformation(position.lat(), position.lng());
                            //change as a dangerous locn may NOT be a mway
                            return TaskService.task().indicators().motorway();
                        } else {
                            if (TaskService.task().indicators().motorway()) {
                                if (!isRelayLocation) {
                                    TaskService.task().indicators().motorway(false);
                                    TaskService.task().recovery().motorway(false);
                                }
                                if (TaskService.task().recovery().fault().categoryCode() === 'FRE' && TaskService.task().recovery().motorway()) {
                                    TabService.clearRelay();
                                }
                            }
                            if (TaskService.task().indicators().dangerousLocation() && !isRelayLocation && !_dangerousLocationIsManualOverridden) {
                                TaskService.task().indicators().motorway(false);
                                TaskService.task().recovery().motorway(false);
                                TaskService.task().indicators().dangerousLocation(false);
                            }

                            svc.refreshPriority();
                            return false;
                        }
                    });
                },
                removeRelayMarker: function removeRelayMarker() {
                    if (_relayMarker) {
                        _map.removeOverlay(_relayMarker);
                    }
                },
                removeBreakdownMarker: function removeBreakdownMarker() {
                    if (_assistanceMarker) {
                        _map.removeOverlay(_assistanceMarker);
                        _assistanceMarker = null;
                    }
                },
                removeResourceMarker: function removeResourceMarker() {
                    if (_resourceMarker) {
                        _map.removeOverlay(_resourceMarker);
                        _resourceMarker = null;
                    }
                },
                plotAll: function plotAll() {
                    const isHireCar = TaskService.task().createReason().isHireCar();

                    // remove all existing markers ...
                    svc.removeBreakdownMarker();
                    svc.removeRelayMarker();
                    svc.removeResourceMarker();
                    //svc.removeRetailerMarkers();

                    if (TaskService.task().location().isSet()) {
                        if (isHireCar) {
                            TaskService.task().rental().collectLocation().marker = svc.addDepotMarker(TaskService.task().rental().collectLocation());
                        }
                        svc.addBreakdownMarker(TaskService.task().location().coordinates().latitude(), TaskService.task().location().coordinates().longitude());
                    }

                    // we need to test recovery is set otherwise we are plotting recovery in equator
                    if (TaskService.task().recovery().destination().isSet() && TaskService.task().recovery().isSet()) {
                        svc.addRelayMarker(TaskService.task().recovery().destination().coordinates().latitude(), TaskService.task().recovery().destination().coordinates().longitude());
                    }

                    if (TaskService.task().schedule().resource() && TaskService.task().schedule().resource().location().isSet()) {
                        svc.addResourceMarker(
                            TaskService.task().schedule().resource().location().coordinates().latitude(),
                            TaskService.task().schedule().resource().location().coordinates().longitude()
                        );
                    }

                    if (isHireCar && (TaskService.task().rental().dropOffLocation().id() || TaskService.task().recovery().isDestResourceIdSet())) {
                        TaskService.task().rental().dropOffLocation().marker = svc.addDepotMarker(
                            TaskService.task().rental().dropOffLocation() || MobilityTaskService.mobilityTask().recovery().destResourceId()
                        );
                    }

                    if (isHireCar && TaskService.task().rental().repairLocation().id()) {
                        TaskService.task().rental().repairLocation().marker = svc.addDepotMarker(TaskService.task().rental().repairLocation());
                    }
                },
                refreshPriority: function refreshPriority() {
                    if (
                        !PriorityReasonsService.hasVulnerableVehicleReasonSet() &&
                        (_sanitiseValue(TaskService.task().indicators().dangerousLocation()) || TaskService.task().indicators().motorway())
                    ) {
                        TaskService.task()
                            .priorities()
                            .push(
                                new PriorityReason({
                                    name: _.find(PriorityReasonsService.priorityReasonsRefData(), function (o) {
                                        return o.id() === PriorityReason.VULNERABLE_VEHICLE;
                                    }),
                                    id: PriorityReason.VULNERABLE_VEHICLE,
                                    text: PriorityReasonsConstants.VULNERABLE_VEHICLE_REASONS.options[1]
                                })
                            );
                    } else if (!(TaskService.task().indicators().dangerousLocation() || TaskService.task().indicators().motorway())) {
                        _.remove(TaskService.task().priorities(), function (priority) {
                            return priority.id() === PriorityReason.VULNERABLE_VEHICLE;
                        });
                    }
                },
                isDangerousLocation: function (lat, lng) {
                    var latLng = GoogleService.latLng(lat, lng);
                    return GoogleService.pinnedLocation({
                        location: latLng
                    }).then(function onSccess(results) {
                        return svc.isMotorway(latLng, results[0]);
                    });
                },
                setMotorwayRelayInformation: function setMotorwayRelayInformation(lat, lng, gazResult = null) {
                    const relayLatLng = {
                        lat: gazResult && gazResult.properties.nextJunc && gazResult.properties.nextJunc.latitude ? gazResult.properties.nextJunc.latitude : lat,
                        lng: gazResult && gazResult.properties.nextJunc && gazResult.properties.nextJunc.longitude ? gazResult.properties.nextJunc.longitude : lng
                    };

                    if (TaskService.task().indicators().motorway()) {
                        TaskService.task().recovery().motorway(true);
                    }
                    if (TaskService.task().fault().isSet()) {
                        //TODO use constants for the nearest safe place text
                        TaskService.setRelayRecoveryDestination(relayLatLng.lat, relayLatLng.lng, ['LOCAL: NEAREST SAFE PLACE WITH FACILITIES'], [TaskService.task().location().area()], true).then(
                            function (success) {
                                if (success) {
                                    TabService.enableRelay();
                                    svc.resetRelayToDetails();
                                    setTaskRecoveryFault();
                                    if (TaskService.task().indicators().motorway()) {
                                        TaskService.task().recovery().motorway(true);
                                    }
                                    TaskService.setDirectRecovery(true);
                                    svc.addRelayMarker(lat, lng);
                                }
                            }
                        );
                    }
                },
                resetRelayToDetails: function resetRelayToDetails() {
                    TaskService.task().recovery().adults(null); //to trigger the diagnostics when location gets changed and is a motorway
                    TaskService.task().recovery().children(null);
                    TaskService.task().recovery().dogs(null);
                    TaskService.task().recovery().keysLocation(null);
                    TaskService.task().recovery().destination().remarks(null);
                    //TaskService.task().recovery().destResourceId(null);
                    DiagnosticsService.clearRelayDiagnosticsQA();
                },
                isNextQuestionRelay: function isNextQuestionRelay() {
                    let isRelayQue = false;

                    isRelayQue = TaskService.task()
                        .fault()
                        .diagnosticsQAList()
                        .some((question) => {
                            return question.selectedAnswer().nextQuestionId() === RecoveryConstants.QT_DISTANCE || question.selectedAnswer().nextQuestionId() === RecoveryConstants.QT_PEOPLE;
                        });
                    return isRelayQue;
                },
                setNonMotorwaySafeRecoveryLocation: function (lat, lng) {
                    if (!TaskService.task().isRelay() || svc.isNextQuestionRelay() || (TaskService.task().isRelay() && TaskService.task().fault().mwayRecovVehFaultId())) {
                        TaskService.setRelayRecoveryDestination(lat, lng, [FaultConstants.NEAREST_SAFE_PLACE_WITH_FACILITIES], [TaskService.task().location().area()]).then(function (success) {
                            if (success) {
                                // add recovery fault otherwise nothing works ...
                                // this is rather madness as we adding a OPERATOR_FORCED_RECOVERY recovery method
                                // but we still set direct recovery indicator so UI shows 'Direct Recovery'
                                setTaskRecoveryFault();
                                svc.refreshPriority();
                                TaskService.setDirectRecovery(true);
                                svc.addRelayMarker(lat, lng);
                                TabService.touch('relayTo');
                            }
                        });
                    }
                },
                reset: function reset() {
                    if (_homeStartMarker) _map.removeMarker(_homeStartMarker);

                    _homeStartMarker = _.noop();
                    _europeanPoi = false;
                    _homeStartCircle.setVisible(false);
                    _accuracyCircle.setVisible(false);
                    _beaconCircle.setVisible(false);
                    svc.selectedLocation(null);
                },
                selectedLocation: function selectedLocationAccessor(val) {
                    return arguments.length ? (_selectedLocation = val) : _selectedLocation;
                },
                refreshMotorwayJunctionInfo: function refreshMotorwayJunctionInfo(categoryId) {
                    var regexGroups,
                        lastJunctionPassed = null,
                        travelDirection = null;

                    switch (categoryId) {
                        case CategoryTypes.MOTORWAY_JUNCTION:
                            //lastJunctionPassed = /(M)([0-9]+)(\s)(JUNCTION|JUNC|J)(\s?)([0-9]+)$/gi.exec(TaskService.task().location().text())[2];
                            regexGroups = /(JUNCTION|JUNC|JCT|J)(\s?)(t|T?)([0-9]+)(A|B?)/gi.exec(TaskService.task().location().text());
                            lastJunctionPassed = regexGroups[0];
                            break;
                        case CategoryTypes.MARKER_POST:
                        case CategoryTypes.SOS_BOX:
                            //parse marker post and sos box category types

                            //the following matches J24-->J23 from M5, (J24-->J23), APPROX(5.67 KM)
                            regexGroups = /(\s?)(J)(\s?)([0-9]*)(\s?)(-->)(\s?)(J)(\s?)([0-9]*)(\s?)/gi.exec(TaskService.task().location().area());

                            if (regexGroups.length) {
                                lastJunctionPassed = regexGroups[0].split('-->')[0];
                                travelDirection = regexGroups[0].split('-->')[1];
                            }
                            break;
                    }

                    TaskService.task().location().lastJunctionPassed(lastJunctionPassed);
                    TaskService.task().location().travellingTo(travelDirection);
                },
                setHoldingResource: (resourceId) => {
                    if (arguments.length) {
                        TaskService.task().miscFields().holdingResource(resourceId);
                        //non JLR dealers use this property to send email notifications
                        TaskService.task().recovery().destResourceId(resourceId);
                    }
                },
                processGarageAddress: (garage) => {
                    return (garage.properties.name + ',' + ' ' + garage.properties.street).length > MAX_LOC_FIELD_LENGTH
                        ? garage.properties.name + ' ' + (garage.properties.street ? ', ' + garage.properties.street.split(',')[0] : '')
                        : garage.properties.name + ' ' + (garage.properties.street ? ',' + garage.properties.street : '');
                },
                disableBoundingEvent: (...args) => (args.length ? (_disableBoundingEvent = args[0]) : _disableBoundingEvent),
                getRouteTypeLocation: (locations) => {
                    return _.find(locations, function (item) {
                        return _.indexOf(item.types, 'route') >= 0;
                    });
                },
                europeanPoiChecked: function lyonPoiChecked() {
                    return _europeanPoi;
                },
                changeEuropeanPoiFlag: function changeEuropeanPoiFlag(val) {
                    _europeanPoi = !_europeanPoi;
                }
            });
        }
    ]);
