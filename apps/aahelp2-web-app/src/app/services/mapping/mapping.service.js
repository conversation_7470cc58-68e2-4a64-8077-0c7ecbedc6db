'use strict';

var _ = require('lodash'),
    ui_bootstrap = require('angular-ui-bootstrap');

require('angular');
/* istanbul ignore next */
module.exports = angular
    .module('aah-mapping-service-module', [
        ui_bootstrap,

        // services
        require('../task/task.service').name,
        require('./location/location.service').name,
        require('./google/google.service').name,

        // constants
        require('../../constants/mapping/google-maps-place-types.constants').name,
        require('../../constants/mapping/mapping-urls.constants').name
    ])
    .service('aahMappingService', [
        '$http',
        'aahTaskService',
        '$q',
        'aahGoogleService',
        'aahLocationService',
        'aahMappingURLs',
        'aahEuropeanCategories',
        function MappingService($http, TaskService, $q, GoogleService, LocationService, MappingURLs, EuropeanCategories) {
            var svc = this,
                _showContextMenu = false,
                _catSearch = '',
                _googleMarkers = [],
                _poiMarkers = [],
                _retailers = [],
                _allPOICategories = [],
                _europeanCategories = [],
                _ukCategories = [],
                _poiCategories = [],
                _poiGaragesCapabilities = [],
                _mobileLocationCircle = null,
                _cachedPoiCategories = [],
                _map,
                _heatmap,
                _showLayers = false,
                _showGarageServices = false,
                _showEuropeanGarageServices = false;

            _.extend(svc, {
                init: () => {
                    Promise.all([svc.getPOICategories(), svc.getGaragesCapabilities()])
                        .then(([categories, capabilities]) => {
                            if (categories) {
                                let europeanCategoriesConstant = EuropeanCategories.EuropeanCategoriesConstant.map(String);
                                _allPOICategories = categories;

                                _poiGaragesCapabilities = capabilities;
                                _europeanCategories = _allPOICategories.filter((cat) => {
                                    return europeanCategoriesConstant.includes(cat.categoryId);
                                });

                                _ukCategories = _allPOICategories.filter((cat) => {
                                    return !europeanCategoriesConstant.includes(cat.categoryId);
                                });
                            }
                        })
                        .catch((error) => {
                            console.error(error);
                        });
                },
                allPOICategories: () => {
                    return _allPOICategories;
                },
                allPOICapabilities: () => {
                    return _poiGaragesCapabilities;
                },
                europeanCategories: () => {
                    return _europeanCategories;
                },
                ukCategories: () => {
                    return _ukCategories;
                },
                addResultMarkers: function addResultMarkers(results) {
                    let i = 0,
                        getIconNum = function (index) {
                            var iconNum = index + 1;

                            if (iconNum < 10) {
                                iconNum = '0' + iconNum;
                            }

                            if (iconNum > 20) iconNum = '';

                            return iconNum;
                        };

                    _.forEach(results, function eachResult(item, idx) {
                        var lat,
                            lng,
                            icon,
                            add = false;

                        if (item.properties) {
                            switch (item.properties.categoryId) {
                                case 4503:
                                    icon = {
                                        url: 'assets/markers/sos/sos-' + getIconNum(i) + '.png',
                                        scaledSize: new google.maps.Size(38, 39),
                                        anchor: new google.maps.Point(0, 39)
                                    };
                                    break;
                                case 4504:
                                    icon = {
                                        url: 'assets/markers/marker-post/marker-post-' + getIconNum(i) + '.png',
                                        scaledSize: new google.maps.Size(38, 39),
                                        anchor: new google.maps.Point(0, 39)
                                    };
                                    break;
                                case 4514:
                                case 116:
                                    icon = {
                                        url: 'assets/markers/motorway/motorway-' + getIconNum(i) + '.png',
                                        scaledSize: new google.maps.Size(38, 39),
                                        anchor: new google.maps.Point(0, 39)
                                    };
                                    break;
                                case 4505:
                                    icon = {
                                        url: 'assets/markers/motorway-services/motorway-services-' + getIconNum(i) + '.png',
                                        scaledSize: new google.maps.Size(38, 39),
                                        anchor: new google.maps.Point(0, 39)
                                    };
                                    break;

                                default:
                                    break;
                            }

                            lat = item.geometry.coordinates[1];
                            lng = item.geometry.coordinates[0];

                            add = true;
                        } else if (item.formatted_address || item.vicinity) {
                            lat = item.geometry.location.lat();
                            lng = item.geometry.location.lng();
                            icon = {
                                url: 'assets/markers/google/google-' + getIconNum(i) + '.png',
                                scaledSize: new google.maps.Size(38, 39),
                                anchor: new google.maps.Point(0, 39)
                            };

                            add = true;
                        } else if (item.properties && item.properties.supResourceId) {
                            icon = {
                                url: 'assets/markers/google/google-' + getIconNum(i) + '.png',
                                scaledSize: new google.maps.Size(38, 39),
                                anchor: new google.maps.Point(0, 39)
                            };
                            add = true;
                        }

                        if (icon && add) {
                            item.index = i + 1;

                            _googleMarkers.push(
                                _map.addMarker({
                                    lat: lat,
                                    lng: lng,
                                    title: item.name,
                                    optimized: false,
                                    icon: icon,
                                    animation: google.maps.Animation.DROP,
                                    markerType: 'SearchResult', // Define custom property of marker type to differentiate between types
                                    infoWindow: {
                                        content: item.name
                                    }
                                })
                            );

                            i++;
                        }
                    });
                },
                centerOnBreakdown: function centerOnBreakdown() {
                    _map.setCenter(TaskService.task().location().coordinates().latitude(), TaskService.task().location().coordinates().longitude());
                },
                centerOnRelay: function centerOnBreakdown() {
                    _map.setCenter(TaskService.task().recovery().destination().coordinates().latitude(), TaskService.task().recovery().destination().coordinates().longitude());
                },
                centerOnLocation: function centerOnLocation(location) {
                    _map.setCenter(location.latitude(), location.longitude());
                    _map.setZoom(17);
                },
                latLngSearch: function latLngSearch(searchTerm) {
                    let latLngArr = searchTerm.split(','),
                        position = GoogleService.latLng(latLngArr[0], latLngArr[1]);
                    return GoogleService.geocode({
                        location: position
                    }).then(function (results) {
                        _.forEach(_googleMarkers, function removeMarker(item, idx) {
                            _map.removeMarker(item);
                        });
                        _googleMarkers = [];

                        svc.moveToBounds();

                        return results;
                    });
                },
                search: function search(searchTerm) {
                    return GoogleService.placesSearch(searchTerm, _map.map.getBounds()).then(function (results) {
                        _.forEach(_googleMarkers, function removeMarker(item, idx) {
                            _map.removeMarker(item);
                        });
                        _googleMarkers = [];

                        svc.moveToBounds();

                        return results;
                    });
                },
                goToResult: function goToResult(idx) {
                    var resultMarker = _googleMarkers[idx];

                    _map.map.panTo(new google.maps.LatLng(resultMarker.options ? resultMarker.options.lat : null, resultMarker.options ? resultMarker.options.lng : null));
                    _map.map.setZoom(17);
                },
                moveToBounds: function moveToBounds() {
                    var points = [],
                        bounds = new google.maps.LatLngBounds(),
                        zoomListener;
                    _map.overlays = _.compact(_map.overlays);
                    if (_map.overlays.length + _map.markers.length + LocationService.getCircles().length > 1) {
                        _.forEach(_map.overlays, function eachOverlay(item, isx) {
                            var point = new google.maps.LatLng(item && item.options ? item.options.lat : null, item && item.options ? item.options.lng : null);

                            bounds.extend(point);
                            points.push(point);
                        });

                        _.forEach(_map.markers, function eachMarker(item, isx) {
                            var point = new google.maps.LatLng(item.position.lat(), item.position.lng());

                            bounds.extend(point);
                            points.push(point);
                        });

                        _.forEach(LocationService.getCircles(), function (circle) {
                            if (circle.getBounds() && circle.getCenter()) {
                                bounds.union(circle.getBounds());
                                points.push(circle.getCenter());
                            }
                        });

                        // zoom in first to make sure that we have to zoom out
                        _map.map.fitBounds(bounds);

                        _.forEach(points, function (point) {
                            var colWidth = angular.element(document.getElementById('left-col')).width(),
                                offSetPoint = function offSetPoint(latlng, offsetx, offsety) {
                                    // latlng is the apparent centre-point
                                    // offsetx is the distance you want that point to move to the right, in pixels
                                    // offsety is the distance you want that point to move upwards, in pixels
                                    // offset can be negative
                                    // offsetx and offsety are both optional

                                    var scale = Math.pow(2, _map.map.getZoom());
                                    var nw = new google.maps.LatLng(_map.map.getBounds().getNorthEast().lat(), _map.map.getBounds().getSouthWest().lng());

                                    var worldCoordinateCenter = _map.map.getProjection().fromLatLngToPoint(latlng);
                                    var pixelOffset = new google.maps.Point(offsetx / scale || 0, offsety / scale || 0);

                                    var worldCoordinateNewCenter = new google.maps.Point(worldCoordinateCenter.x - pixelOffset.x, worldCoordinateCenter.y + pixelOffset.y);

                                    var newCenter = _map.map.getProjection().fromPointToLatLng(worldCoordinateNewCenter);

                                    return newCenter;
                                };

                            bounds.extend(offSetPoint(point, 0 - colWidth, 0));
                            bounds.extend(offSetPoint(point, colWidth, 0));
                        });

                        _map.map.fitBounds(bounds);
                    } else if (_map.overlays.length === 1) {
                        _map.setCenter(
                            _map.overlays[0] && _map.overlays[0].options ? _map.overlays[0].options.lat : null,
                            _map.overlays[0] && _map.overlays[0].options ? _map.overlays[0].options.lng : null
                        );
                        _map.setZoom(17);
                    }
                },
                setMapCenter: function setMapCenter(lat, lng) {
                    _map.setCenter(lat, lng);
                },
                addRelayMarker: function addRelayMarker(lat, lng) {
                    LocationService.addRelayMarker(lat, lng);
                },
                addAssistanceMarker: function addAssistanceMarker(lat, lng) {
                    LocationService.addBreakdownMarker(lat, lng);
                    svc.centerOnBreakdown();
                },
                removeAssistanceMarker: function removeAssistanceMarker() {
                    LocationService.removeBreakdownMarker();
                },
                addResource: function addResource(resource) {
                    LocationService.addResourceMarker(resource.location().coordinates().latitude(), resource.location().coordinates().longitude(), resource.updateUI());
                },
                assistanceMarker: function assistanceMarkerAcessor(val) {
                    return LocationService.getMarkers().assistance;
                },
                reset: function reset() {
                    if (!_map) {
                        return;
                    }
                    _map.overlays = _.compact(_map.overlays);
                    if (_.compact(_map.overlays).length > 0) {
                        _map.removeOverlays();
                    }

                    _.forEach(_googleMarkers, function (marker) {
                        _map.removeMarker(marker);
                    });

                    _googleMarkers = [];
                    svc.resetPoiMarkers();
                    svc.resetPoi();
                    svc.resetRetailers();
                    LocationService.reset();
                },
                retailers: (...args) => {
                    return args.length ? (_retailers = args[0]) : _retailers;
                },
                resetRetailers: () => {
                    _.forEach(_retailers, (retailer) => {
                        if (retailer.marker) {
                            retailer.marker.setMap(null);
                        }
                    });

                    _retailers.length = 0;
                },
                resetPoiMarkers: () => {
                    svc.cachedPoiCategories(svc.poiCategories());

                    _.forEach(_poiMarkers, function (marker) {
                        _map.removeMarker(marker);
                    });

                    _poiMarkers = [];
                    _poiCategories = [];
                },
                resetPoi: () => {
                    svc.showLayers(false);
                    svc.showGarageServices(false);
                    svc.showEuropeanGarageServices(false);
                    svc.catSearch('');
                },
                map: function mapAcessor(val) {
                    return arguments.length ? (_map = val) : _map;
                },
                showContextMenu: function showContextMenuAcessor(val) {
                    return arguments.length ? (_showContextMenu = val) : _showContextMenu;
                },
                heatmap: function heatmapAvessor(val) {
                    return arguments.length ? (_heatmap = val) : _heatmap;
                },
                renderAndZoomToMobileCallerLocation: function renderAndZoomToMobile(mobilePhoneNumber) {
                    var urlPath = '/api/mapping-service/search/mobilePhone',
                        params = {
                            mobilePhoneNumber: mobilePhoneNumber
                        };

                    $http
                        .get(
                            urlPath,
                            {
                                params: params
                            },
                            {
                                notify: true
                            }
                        )
                        .then(function successCallback(response) {
                            var latLng = new google.maps.LatLng(response.data.coordinates[1], response.data.coordinates[0]),
                                radiusInMeters = response.data.properties.radiusMeters;

                            _mobileLocationCircle = new google.maps.Circle({
                                strokeColor: '#1080DD',
                                strokeOpacity: 0.8,
                                strokeWeight: 2,
                                fillColor: '#1080DD',
                                fillOpacity: 0.35,
                                map: _map.map,
                                center: latLng,
                                radius: radiusInMeters
                            });

                            google.maps.event.addListener(_mobileLocationCircle, 'rightclick', function (e) {
                                _.extend(e, {
                                    pixel: {
                                        x: e.rb.clientX,
                                        y: e.rb.clientY - 40
                                    }
                                });

                                google.maps.event.trigger(_map.map, 'rightclick', e);
                            });

                            _map.map.fitBounds(_mobileLocationCircle.getBounds());
                        });
                },
                motorwayMarkerPosts: function motorwayMarkerPosts(lat, lng) {
                    return $http
                        .post(
                            '/api/mapping-service/search/radius',
                            {
                                center: [lng, lat],
                                radius: 0.1,
                                catIds: [4504]
                            },
                            {
                                notify: true
                            }
                        )
                        .then(function markerPostSuccess(response) {
                            return response.data;
                        });
                },

                drawRoute: function drawRoute(lat1, lng1, lat2, lng2) {
                    _map.drawRoute({
                        origin: [lat1, lng1],
                        destination: [lat2, lng2],
                        travelMode: 'driving',
                        strokeColor: '#0486dd',
                        strokeOpacity: 1,
                        strokeWeight: 5
                    });
                },
                refreshPriority: function refreshPriority() {
                    LocationService.refreshPriority();
                },
                getBounds: () => {
                    let bounds = _map.map.getBounds(),
                        box = [];

                    box.push({
                        lng: bounds.getSouthWest().lng(),
                        lat: bounds.getSouthWest().lat()
                    });
                    box.push({
                        lng: bounds.getNorthEast().lng(),
                        lat: bounds.getNorthEast().lat()
                    });

                    return box;
                },
                getPoi: function getPoi(cats) {
                    return $http.post(
                        '/api/mapping-service/search/box',
                        {
                            points: svc.getBounds(),
                            catIds: cats
                        },
                        {
                            notify: true
                        }
                    );
                },
                poiMarkers: function poiMarkers(markers) {
                    return arguments.length ? (_poiMarkers = markers) : _poiMarkers;
                },
                poiCategories: function poiCategories(categories) {
                    return arguments.length ? (_poiCategories = categories) : _poiCategories;
                },
                cachedPoiCategories: function cachedPoiCategories(categories) {
                    return arguments.length ? (_cachedPoiCategories = categories) : _cachedPoiCategories;
                },
                googleMarkers: () => _googleMarkers,
                selectedLocation: function selectedLocation(val) {
                    return arguments.length ? LocationService.selectedLocation(val) : LocationService.selectedLocation();
                },
                getPOICategories: () => {
                    return $http
                        .get(
                            '/api/mapping-service/selectedCategories',
                            {},
                            {
                                notify: true
                            }
                        )
                        .then((resp) => {
                            let str = JSON.stringify(resp.data);
                            str = str.replace(/category-id/g, 'categoryId');
                            return JSON.parse(str);
                        });
                },
                getGaragesCapabilities() {
                    return $http.get(`/api/poi-service/garages/capabilities`, {}, { notify: true }).then((resp) => {
                        return resp.data;
                    });
                },
                showLayers: (...args) => {
                    return args.length ? (_showLayers = args[0]) : _showLayers;
                },
                showGarageServices: (...args) => {
                    return args.length ? (_showGarageServices = args[0]) : _showGarageServices;
                },
                showEuropeanGarageServices: (...args) => {
                    return args.length ? (_showEuropeanGarageServices = args[0]) : _showEuropeanGarageServices;
                },
                catSearch: (...args) => {
                    return args.length ? (_catSearch = args[0]) : _catSearch;
                },
                lock: () => {
                    _map.set('scrollwheel', false);
                },
                unlock: () => {
                    _map.set('scrollwheel', true);
                },
                calculateDistance: (orgin, destination) => {
                    return new Promise(function (resolve, reject) {
                        let distanceMatrixService = new google.maps.DistanceMatrixService();
                        distanceMatrixService.getDistanceMatrix(
                            {
                                origins: [new google.maps.LatLng(orgin.latitude, orgin.longitude)],
                                destinations: [new google.maps.LatLng(destination.latitude, destination.longitude)],
                                travelMode: 'DRIVING'
                            },
                            callback
                        );

                        function callback(response, status) {
                            resolve(response.rows[0].elements[0].distance);
                        }
                    });
                },
                saveHondaAssistDetails: () => {
                    let hondaAssistObj = {
                        taskId: TaskService.task().id(),
                        isHondaAssistChecked: true,
                        code: TaskService.task().entitlement().customerGroup().code(),
                        name: TaskService.task().entitlement().customerGroup().name()
                    };
                    return $http.post(MappingURLs.SAVE_HONDA_ASSIST_BY_TASK_ID, hondaAssistObj).then(
                        function hondaAssistSaveSuccess(response) {
                            return response.data;
                        },
                        function hondaAssistSaveError(e) {
                            return e;
                        }
                    );
                }
            });
        }
    ]);
