var _ = require('lodash'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model'),
    Task = require('@aa/malstrom-models/lib/task.model'),
    Fault = require('@aa/malstrom-models/lib/fault.model'),
    environment = require('angular-environment');

require('angular');

module.exports = angular
    .module('aah-tab-service-module', [
        require('../validation/validation.service').name,
        require('../task/task.service').name,
        require('../mobility-task/mobility-task.service').name,
        require('../audit/audit.service').name,
        require('../ecall/ecall.service').name,
        require('../../constants/faults/faults.constants').name
    ])
    .service('aahTabService', [
        '$injector',
        '$sce',
        'aahValidationService',
        'aahTaskService',
        'aahMobilityTaskService',
        'aahFaultsConstants',
        'aahAuditService',
        'aahEcallService',
        'envService',
        function TabService($injector, $sce, ValidationService, TaskService, MobilityTaskService, FaultsConstants, AuditService, EcallService, envService) {
            var svc = this,
                ccp_Phase2_flag = envService.read('ENABLE_CCP_V2'),
                EligibilityService;

            var _tabs = {
                memberDetails: {
                    friendly: $sce.trustAsHtml('<abbr>M</abbr>ember details'),
                    state: 'contact',
                    error: function error() {
                        const hasError = !(
                            ValidationService.isCustomerValid(TaskService.task().contact()) && ValidationService.areMandatoryFlexFieldsValid(TaskService.task().entitlement().variableData())
                        );
                        if (hasError && TaskService.task().entitlement().customerGroup().code() === 'FSC') {
                            _tabs.memberDetails.touched = true;
                        }
                        return hasError;
                    },
                    disabled: function relayDisabled() {
                        return false;
                    },
                    touched: false,
                    iconClass: 'icon-card'
                },
                assistance: {
                    friendly: $sce.trustAsHtml('<abbr>A</abbr>ssistance'),
                    state: 'assistance.vehicle',
                    error: function error() {
                        if (!EligibilityService) {
                            EligibilityService = $injector.get('aahEligibilityService');
                        }
                        var hasErrors = false,
                            task = TaskService.task();
                        if (
                            task.entitlement().customerGroup().isPersonal() &&
                            !task.entitlement().customerGroup().isBank() &&
                            !(EligibilityService.isWillJoin() || task.entitlement().riskCode() === 'WJ') &&
                            !task.entitlement().customerGroup().isRoadsideAddOn()
                        ) {
                            hasErrors = !TaskService.task().demandDeflection().isCUV();
                        }
                        return !ValidationService.isVehicleValid(TaskService.task().vehicle()) || !ValidationService.isFaultValid(TaskService.task().fault()) || hasErrors;
                    },
                    disabled: function relayDisabled() {
                        return false;
                    },
                    touched: false,
                    iconClass: 'icon-patrol'
                },
                location: {
                    friendly: $sce.trustAsHtml('<abbr>L</abbr>ocation'),
                    state: 'location.mapping',
                    error: function error() {
                        return !ValidationService.isLocationValid(TaskService.task().location(), TaskService.task().indicators(), TaskService.task().sequence(), TaskService.task().status());
                    },
                    disabled: function relayDisabled() {
                        return false;
                    },
                    touched: false,
                    iconClass: 'icon-pin'
                },
                harbourPoint: {
                    friendly: $sce.trustAsHtml('<abbr>H</abbr>arbour-Point'),
                    state: 'location.harbour-point',
                    error: function error() {
                        return !ValidationService.isLocationValid(TaskService.task().location(), TaskService.task().indicators(), TaskService.task().sequence(), TaskService.task().status());
                    },
                    disabled: function relayDisabled() {
                        return false;
                    },
                    touched: false,
                    iconClass: 'icon-pin'
                },
                dummyRelayTo: {
                    friendly: $sce.trustAsHtml('<abbr>R</abbr>elay to'),
                    state: ' location.relay.to', // but we will never go there, - we should actually have a empty state just in case ..
                    error: function error() {
                        return false;
                    },
                    forceDisabled: true,
                    forceEnabled: false,
                    disabled: function dissabled() {
                        console.log('dummyRelayTo : - dissabled true');
                        return true;
                    },
                    touched: false,
                    iconClass: 'icon-path'
                },
                relayTo: {
                    friendly: $sce.trustAsHtml('<abbr>R</abbr>elay to'),
                    state: 'location.relay.to',
                    error: function error() {
                        const forceEnabled = this.forceEnabled;
                        const isLocationValid = ValidationService.isLocationValid(TaskService.task().recovery().destination());
                        const isRecoveryValid = ValidationService.isRecoveryValid(TaskService.task().recovery(), TaskService.task().indicators().isRelayDiagnosticsRunning());
                        return (forceEnabled && !isLocationValid) || !isRecoveryValid;
                    },
                    forceDisabled: true,
                    forceEnabled: false,
                    disabled: function relayDisabled() {
                        if ((!this.forceDisabled && this.forceEnabled) || (TaskService.task().location().isSet() && TaskService.task().isRecovery())) {
                            TaskService.task().recovery().relay(true); // this is required to enable validation for relay-to screen
                            return false;
                        }

                        return TaskService.task().location().coordinates().latitude() === null || this.forceDisabled;
                    },
                    touched: false,
                    iconClass: 'icon-path'
                },
                supplier: {
                    friendly: $sce.trustAsHtml('S<abbr>u</abbr>pplier'),
                    state: 'supplier',
                    error: function error() {
                        return false;
                    },
                    disabled: function relayDisabled() {
                        //todo: this is not enough. we must look at locale
                        if (TaskService.task().entitlement().customerGroup().isEuroHelp()) {
                            if (['UNAC'].includes(TaskService.task().status())) {
                                return true;
                            }

                            return false;
                        }

                        return TaskService.task().status() === Task.UNAC_STATUS || TaskService.task().status() === null || TaskService.task().schedule().resource().physical(); //disabled if task already assigned to physical resource
                    },
                    touched: false,
                    iconClass: 'icon-toolbox'
                },
                summary: {
                    friendly: $sce.trustAsHtml('Summar<abbr>y</abbr>'),
                    state: 'summary',
                    error: function error() {
                        return false;
                    },
                    disabled: function summaryDisabled() {
                        return TaskService.task().status() === Task.UNAC_STATUS || TaskService.task().status() === null;
                    },
                    touched: false,
                    iconClass: 'icon-report'
                },
                relayPlus: {
                    friendly: $sce.trustAsHtml('Rela<abbr>y</abbr>Plu<abbr>s</abbr>'),
                    state: 'relayPlus',
                    error: function error() {
                        return !ValidationService.isRelayPlusValid(TaskService.task());
                    },
                    disabled: function relayPlusDisabled() {
                        return false;
                    },
                    touched: true,
                    iconClass: 'icon-path'
                },
                vehicleReDelivery: {
                    friendly: $sce.trustAsHtml('Vehicle <abbr>D</abbr>elivery'),
                    state: 'location.relay.to',
                    error: function error() {
                        return !ValidationService.isLocationValid(TaskService.task().recovery().destination());
                    },
                    disabled: function relayDisabled() {
                        return false;
                    },
                    touched: false,
                    iconClass: 'icon-path'
                },
                passengerRun: {
                    friendly: $sce.trustAsHtml('Passen<abbr>g</abbr>er Run'),
                    state: 'location.relay.passenger-run',
                    error: function error() {
                        return false;
                    },
                    disabled: function relayPlusDisabled() {
                        return false;
                    },
                    touched: true,
                    iconClass: 'icon-path'
                },
                relayLeg: {
                    friendly: $sce.trustAsHtml('Relay <abbr>L</abbr>eg'),
                    state: 'location.relay.leg',
                    error: function error() {
                        return (
                            !ValidationService.isLocationValid(TaskService.task().recovery().destination()) ||
                            !ValidationService.isRecoveryValid(TaskService.task().recovery(), TaskService.task().indicators().isRelayDiagnosticsRunning())
                        );
                    },
                    disabled: function relayPlusDisabled() {
                        return false;
                    },
                    touched: true,
                    iconClass: 'icon-path'
                },

                // JLR states
                vehicleOptions: {
                    friendly: $sce.trustAsHtml('Hire Details'),
                    state: 'vehicle-options',
                    error: function error() {
                        return false;
                    },
                    disabled: function mobilityOptionsDisabled() {
                        return false;
                    },
                    touched: true,
                    iconClass: 'icon-hirecar'
                },
                locationDetails: {
                    friendly: $sce.trustAsHtml('Mobility details'),
                    state: 'locationDetails',
                    error: function error() {
                        return false;
                    },
                    disabled: function mobilityDetailsDisabled() {
                        if (MobilityTaskService.isthirdPartySupplier()) {
                            return false;
                        } else {
                            return TaskService.task().createReason().isHireCar() && !TaskService.task().rental().hireVehicle().isSet();
                        }
                    },
                    touched: true,
                    iconClass: 'icon-pin'
                },
                insuranceDetails: {
                    friendly: $sce.trustAsHtml('Driver Details'),
                    state: 'driverDetails',
                    error: function error() {
                        return !ValidationService.areAllDriversValidated(TaskService.task().rental());
                    },
                    disabled: function insuranceDetailsDisabled() {
                        return false;
                    },
                    touched: true,
                    iconClass: 'icon-flp-path3'
                },
                accommodation: {
                    friendly: $sce.trustAsHtml(`Accommodation`),
                    state: 'accommodation',
                    error: () => !ValidationService.isValidAccommodation(TaskService.task()),
                    disabled: () => false,
                    touched: false,
                    iconClass: 'icon-home'
                },
                accommodationLocation: {
                    friendly: $sce.trustAsHtml(`Location`),
                    state: 'location.accommodation',
                    error: () => !ValidationService.isValidAccommodationLocation(TaskService.task()),
                    disabled: () => false,
                    touched: false,
                    iconClass: 'icon-pin'
                },
                transport: {
                    friendly: $sce.trustAsHtml(`Travel`),
                    state: 'transport',
                    error: () => !ValidationService.isValidTransportation(TaskService.task()),
                    disabled: () => false,
                    touched: false,
                    iconClass: 'icon-car'
                },
                transportPickupLocation: {
                    friendly: $sce.trustAsHtml(`Pick-up location`),
                    state: 'location.transportPickup',
                    error: () => !ValidationService.isValidTransportPickupLocation(TaskService.task()),
                    disabled: () => false,
                    touched: false,
                    iconClass: 'icon-pin'
                },
                transportDropoffLocation: {
                    friendly: $sce.trustAsHtml(`Drop-off location`),
                    state: 'location.transportDropoff',
                    error: () => !ValidationService.isValidTransportDropoffLocation(TaskService.task()),
                    disabled: () => false,
                    touched: true,
                    iconClass: 'icon-pickup'
                },
                ecall: {
                    friendly: $sce.trustAsHtml(`E-Call`),
                    state: 'ecall',
                    disabled: () => false,
                    touched: true,
                    iconClass: 'icon-ecall'
                },
                audit: {
                    friendly: $sce.trustAsHtml(`Audit details`),
                    state: 'audit',
                    disabled: () => false,
                    touched: true,
                    iconClass: 'icon-report'
                },
                hireaudit: {
                    friendly: $sce.trustAsHtml(`Hire Audit details`),
                    state: 'ext-hire-audit',
                    disabled: () => false,
                    touched: false,
                    iconClass: 'icon-report'
                },
                PaymentAuth: {
                    friendly: $sce.trustAsHtml(`Card Authorisation`),
                    state: 'card-payment',
                    error: function error() {
                        return false;
                    },
                    disabled: function mobilityDetailsDisabled() {
                        if (ccp_Phase2_flag && (TaskService.task().entitlement().customerGroup().isAllJLR() || TaskService.task().entitlement().customerGroup().isHyundai())) {
                            if (MobilityTaskService.isthirdPartySupplier() || TaskService.task().status() === 'UNAC') {
                                return true;
                            } else {
                                return !TaskService.task().createReason().isHireCar() && TaskService.task().rental().hireVehicle().isSet();
                            }
                        } else {
                            return true;
                        }
                    },
                    touched: true,
                    iconClass: 'icon-card'
                }
            };

            /**
             * Various evaluation steps to show operator when pre-populated task has errors
             */
            function evalTabsForSelfServiceTasks() {
                const _tabsMap = new Map(Object.entries(_tabs));
                if (
                    _tabs.assistance &&
                    TaskService.task().status() === Task.INIT_STATUS &&
                    TaskService.task().fault().id() === FaultsConstants.OVERHEATING_FAULT_ID &&
                    !(TaskService.task().fault().diagnosticsQAList() && TaskService.task().fault().diagnosticsQAList().length > 0)
                ) {
                    _tabs.assistance.touched = true;
                    TaskService.task().fault(new Fault());
                }

                // only eval errors for task created outside of aah2
                if (!TaskService.isSelfServiceAppTask(TaskService.task())) {
                    return;
                }

                // set tab as touched so any eval errors are highlighted
                for (const tab of _tabsMap) {
                    tab.touched = true;
                }
            }

            _.extend(svc, {
                getJLRTabs: () => {
                    if (ccp_Phase2_flag && (TaskService.task().entitlement().customerGroup().isAllJLR() || TaskService.task().entitlement().customerGroup().isHyundai())) {
                        return _.pick(_tabs, ['memberDetails', 'vehicleOptions', 'locationDetails', 'insuranceDetails', 'PaymentAuth', 'summary']);
                    } else {
                        return _.pick(_tabs, ['memberDetails', 'vehicleOptions', 'locationDetails', 'insuranceDetails', 'summary']);
                    }
                },
                getEcallTabs: () => {
                    return _.pick(_tabs, ['ecall', 'audit']);
                },
                getHireTab: () => {
                    return _.pick(_tabs, ['hireaudit']);
                },
                getTabs: function getTabs() {
                    var subSet = [];
                    const task = TaskService.task();
                    switch (task.createReason().id()) {
                        case CreateReason.PASSENGER_RUN:
                            subSet = ['memberDetails', 'assistance', 'location', 'passengerRun', 'supplier', 'summary'];
                            break;
                        case CreateReason.FINAL_LEG:
                        case CreateReason.RELAY_LEG:
                            subSet = ['memberDetails', 'assistance', 'harbourPoint', 'relayLeg', 'supplier', 'summary'];
                            break;
                        case CreateReason.SUPPORT_TASK_AT_DESTINATION:
                            subSet = ['memberDetails', 'assistance', 'location', 'relayLeg', 'supplier', 'summary'];
                            break;
                        case CreateReason.VEHICLE_RE_DELIVERY:
                            subSet = ['memberDetails', 'assistance', 'location', 'relayTo', 'supplier', 'summary'];
                            break;
                        case CreateReason.HIRE_CAR:
                            if (ccp_Phase2_flag && (TaskService.task().entitlement().customerGroup().isAllJLR() || TaskService.task().entitlement().customerGroup().isHyundai())) {
                                subSet = ['memberDetails', 'vehicleOptions', 'locationDetails', 'insuranceDetails', 'PaymentAuth', 'summary'];
                            } else {
                                subSet = ['memberDetails', 'vehicleOptions', 'locationDetails', 'insuranceDetails', 'summary'];
                            }
                            break;
                        case CreateReason.HOTEL:
                            if (task.createReason().isPublicTransport() || (task.transport && task.transport().isTransportSet())) {
                                subSet = ['memberDetails', 'transport', 'transportPickupLocation', 'transportDropoffLocation', 'summary'];
                            } else {
                                subSet = ['memberDetails', 'accommodation', 'accommodationLocation', 'summary'];
                            }
                            break;
                        case CreateReason.TRANSPORT:
                            subSet = ['memberDetails', 'transport', 'transportPickupLocation', 'transportDropoffLocation', 'summary'];
                            break;
                        case CreateReason.STORAGE:
                            console.warn('tab service::getTabs::STORAGE');
                            subSet = ['memberDetails', 'assistance', 'location', 'relayTo', 'supplier', 'summary'];
                            break;
                        case CreateReason.GARAGE_REPAIR:
                            console.warn('tab service::getTabs::GARAGE_REPAIR');
                            subSet = ['memberDetails', 'assistance', 'location', 'relayTo', 'supplier', 'summary'];
                            break;
                        case CreateReason.RECOVERY:
                            console.warn('tab service::getTabs::RECOVERY');
                            subSet = ['memberDetails', 'assistance', 'location', 'relayTo', 'supplier', 'summary'];
                            break;
                        default:
                            if (task.createReason().isRelayPlus()) {
                                subSet = ['memberDetails', 'assistance', 'location', 'relayTo', 'relayPlus', 'summary'];
                            } else {
                                subSet = ['memberDetails', 'assistance', 'location', 'relayTo', 'supplier', 'summary'];
                            }
                    }
                    evalTabsForSelfServiceTasks();
                    return _.pick(_tabs, subSet);
                },
                reset: function reset() {
                    _.forEach(_tabs, function eachTab(item, idx) {
                        item.touched = false;
                    });

                    _tabs.relayTo.forceDisabled = true;
                    _tabs.relayTo.forceEnabled = false;
                },
                touch: function touch(tab) {
                    _tabs[tab].touched = true;
                },
                trackBehaviours: function trackBehaviour(behaviours) {
                    // for SUPPORT_TASK_AT_DEST CB_LOC2DEST is set but
                    // I am led to beleive that for such create reason we
                    // should not set relay ...
                    _tabs.relayTo.touched = _.includes(behaviours, 'CB_DEST') || _.includes(behaviours, 'CB_LOC2DEST');
                    _tabs.assistance.touched = _.includes(behaviours, 'CB_DIAG');
                },
                clearRelay: function clearRelayTab() {
                    TaskService.clearRecovery();
                    // reset bit of the tab object
                    _tabs.relayTo.touched = false;
                    _tabs.relayTo.forceDisabled = true;
                    _tabs.relayTo.forceEnabled = false;
                },
                disableRelay: function disableRelay() {
                    _tabs.relayTo.touched = false;
                    _tabs.relayTo.forceDisabled = true;
                    _tabs.relayTo.forceEnabled = false;
                },
                enableRelay: function enableRelay() {
                    _tabs.relayTo.touched = true;
                    _tabs.relayTo.forceDisabled = false;
                    _tabs.relayTo.forceEnabled = true;
                },
                relayTouched: (...args) => {
                    var relayObj = null;
                    switch (TaskService.task().createReason().id()) {
                        case CreateReason.SUPPORT_TASK:
                        case CreateReason.SUPPORT_TASK_AT_DESTINATION:
                            relayObj = _tabs.dummyRelayTo;
                            break;
                        case CreateReason.PASSENGER_RUN:
                            relayObj = _tabs.passengerRun;
                            break;
                        case CreateReason.FINAL_LEG:
                        case CreateReason.RELAY_LEG:
                            relayObj = _tabs.relayLeg;
                            break;
                        case CreateReason.VEHICLE_RE_DELIVERY:
                            relayObj = _tabs.relayTo;
                            break;
                        default:
                            relayObj = _tabs.relayTo;
                            break;
                    }
                    return args.length > 0 ? (relayObj.touched = args[0]) : relayObj.touched;
                }
            });
        }
    ]);
