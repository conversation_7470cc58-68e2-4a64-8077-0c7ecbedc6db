/* jshint expr: true */
var _ = require('lodash'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model');
require('angular');
require('angular-hotkeys');

const Qualification = require('@aa/malstrom-models/lib/qualification.model');
const BrcEntitlement = require('@aa/malstrom-models/lib/brc-entitlement.model');
const QualificationConstants = require('../../constants/qualification/qualification.constants');

module.exports = angular
    .module('aah-task-operations-controller-module', [
        'cfp.hotkeys',

        // constants
        require('../../constants/completion/completion-reasons.constants').name,

        // services
        require('../../services/ui/ui.service').name,
        require('../../services/task/task.service').name,
        require('../../services/mobility-task/mobility-task.service').name,
        require('../../services/ecall/ecall.service').name,
        require('../../services/csh/csh.service').name,
        require('../../services/cuv/cuv.service').name,
        require('../../services/tab/tab.service').name,
        require('../../services/alert/alert.service').name,
        require('../../services/eligibility/eligibility.service').name,
        require('../../services/mapping/mapping.service').name,
        require('../../services/text-topic/text-topic.service').name,
        require('../../services/prompt/prompt.service').name,
        require('../../services/completion/completion.service').name,
        require('../../services/validation/validation.service').name,
        require('../../services/service-type/service-type.service').name,
        require('../../services/csh-search-viewer/csh-search-viewer.service').name,
        require('../../services/customer-group/customer-group.service').name,
        require('../../services/qualification/qualification.service').name,
        require('../../services/qualification/qualification-actions.service').name,
        require('../../services/side-panel/side-panel.service').name,
        require('../../constants/alert/alert-type.constants').name,
        require('../../services/supplier/supplier.service').name,
        require('../../services/vehicle/vehicle.service').name,
        require('../../services/search/search.service').name,
        require('../../services/flp/flp-task.service').name,
        require('../../constants/flexible-fields/flexible-fields.constants').name
    ])
    .controller('aahTaskOperationsController', [
        '$scope',
        '$state',
        'aahTaskService',
        'aahMobilityTaskService',
        'aahEcallService',
        'aahUIService',
        'aahMappingService',
        'aahCSHService',
        'aahCuvService',
        'aahEligibilityService',
        'aahTextTopicService',
        'aahPromptService',
        'aahCompletionService',
        'aahValidationService',
        'hotkeys',
        'aahCompletionReasons',
        'aahAlertService',
        'aahServiceTypeService',
        'aahCSHSearchViewerService',
        'aahCustomerGroupService',
        '$timeout',
        'aahQualificationService',
        'aahQualificationActionsService',
        'aahSidePanelService',
        'uibAlertTypes',
        'aahSupplierService',
        'aahVehicleService',
        'aahSearchService',
        'aahEntitlementHelperService',
        'aahFlexibleFieldsConstants',
        'aahFlpTaskService',
        'aahDemandDeflectionService',
        function TaskOperationsController(
            $scope,
            $state,
            TaskService,
            MobilityTaskService,
            EcallService,
            UIService,
            MappingService,
            CSHService,
            CuvService,
            EligibilityService,
            TextTopicService,
            PromptService,
            CompletionService,
            ValidationService,
            hotkeys,
            completionReasons,
            AlertService,
            ServiceTypeService,
            CSHSearchViewerService,
            CustomerGroupService,
            $timeout,
            QualificationService,
            QualificationActionsService,
            SidePanelService,
            AlertTypes,
            SupplierService,
            VehicleService,
            SearchService,
            EntitlementHelperService,
            flexibleFieldsConstants,
            FlpTaskService,
            DemandDeflectionService
        ) {
            var ctrl = this,
                _pendingRequest = false;

            /* istanbul ignore next */
            var performSaveActions = function () {
                //decio20190405
                _pendingRequest = true;

                if (QualificationService.isWorkingOnQualificationTask()) {
                    if (
                        (QualificationService.activeTaskType() === Qualification.TELFIX &&
                            QualificationActionsService.qualificationActionType() === QualificationConstants.actions.RESOLVED_VIA_PHONE) ||
                        QualificationActionsService.completeOnSave()
                    ) {
                        TaskService.task().isDirty(false);
                        QualificationActionsService.completeOnSave(false);
                        CompletionService.completeTask(true, 'qualification');
                        return;
                    }
                }

                TaskService.validationForRepairer().then(function () {
                    let entitlement = CSHService.entitlement();
                    let brcEntitlement = null;

                    if (entitlement) {
                        if (entitlement.hasBrc()) {
                            brcEntitlement = new BrcEntitlement();
                            brcEntitlement.contact(entitlement.contact());
                            brcEntitlement.policy(entitlement.policy());
                            brcEntitlement.products(entitlement.products());
                            brcEntitlement.systemId(entitlement.systemId());
                        }
                    }

                    TaskService.save(null, null, brcEntitlement).then(
                        function saveSuccess(responseModel) {
                            //reset supplier service after saving as next time we go to this tab we want to get new results
                            SupplierService.updateResults();

                            SearchService.locationSearch().what3WordsSearch('');

                            if (responseModel.schedule().resource().location().isSet()) {
                                MappingService.addResource(responseModel.schedule().resource());
                            }

                            if (!QualificationService.isWorkingOnQualificationTask()) {
                                $state.go('summary');
                            }

                            AlertService.createAlert('Task saved', AlertTypes.INFO, 3000);

                            $timeout(function () {
                                _pendingRequest = false;
                                //Only for Phoenix: Retrive active task to get updated status by prime
                                if (QualificationService.isWorkingOnQualificationTask()) {
                                    _getAllTasks();
                                }
                            }, 4000);
                        },
                        function () {
                            _pendingRequest = false;
                            AlertService.createAlert('Failed to save task', AlertTypes.DANGER);
                        }
                    );
                });
            };

            hotkeys.bindTo($scope).add({
                combo: 'alt+n',
                description: 'Send task',
                allowIn: ['INPUT', 'SELECT', 'TEXTAREA'],
                callback: function () {
                    ctrl.send();
                }
            });

            function _getAllTasks() {
                let customerRequestId = TaskService.task().customerRequestId();
                if (customerRequestId) {
                    TaskService.tasksByCustomerRequest(customerRequestId).then(function onSuccessFetchTasks(taskList) {
                        CSHService.loadTasks(taskList);
                    });
                }
            }

            function checkMileageType() {
                var isMileageIntType;
                _.forEach(TaskService.task().entitlement().variableData(), function forEachFlexibleField(item, idx) {
                    if (item.flexFieldFormat.fieldNumber == flexibleFieldsConstants.FLEX_FIELD_KEYS.MILEAGE && item.flexFieldData.value) {
                        isMileageIntType = !(!item.flexFieldData.value || /^[0-9]*$/.test(item.flexFieldData.value));
                    }
                });
                return isMileageIntType;
            }

            _.extend(ctrl, {
                isTaskNew: function isTaskNew() {
                    // only for new tasks ...
                    // check if there is a task in a CR which is still in newTask state
                    return TaskService.task().isNew();
                },
                inQualificationTask: function () {
                    return QualificationService.isWorkingOnQualificationTask();
                },
                isCompleted: function isCompleted() {
                    return TaskService.task().isCompleted() || _pendingRequest;
                },
                isTaskUpdateDisabled: function isTaskUpdateDisabled() {
                    return ctrl.isCompleted();
                },
                checkTaskUpdateDisabled: () => {
                    if (TaskService.task().entitlement().customerGroup().isEuroHelp() && (TaskService.task().createReason().isStorage() || TaskService.task().createReason().isGarageRepair())) {
                        if (!TaskService.task().eurohelp().price().currency() || !TaskService.task().eurohelp().price().value()) {
                            return true;
                        }
                    }
                    if (!ValidationService.saveButtonEnabled(TaskService.task())) {
                        return true;
                    }
                    if (ctrl.disableOnVrnChange()) {
                        return true;
                    }

                    return MobilityTaskService.isEnterprise() ? ctrl.isCompleted() || MobilityTaskService.isEnterpriseTaskUpdateDisabled() : ctrl.isCompleted() || checkMileageType();
                },
                setLocationRemarks: () => {
                    const patternMatch = /\/{3}[\w\s\[\]-]+\.\w+\.\w+/;
                    if (
                        SearchService.locationSearch().what3WordsSearch() &&
                        !patternMatch.test(TaskService.task().location().remarks()) &&
                        patternMatch.test(SearchService.locationSearch().what3WordsSearch())
                    ) {
                        TaskService.task().location().addRemarks(SearchService.locationSearch().what3WordsSearch());
                    } else if (patternMatch.test(TaskService.task().location().remarks())) {
                        let what3WordsText = patternMatch.test(SearchService.locationSearch().what3WordsSearch()) ? SearchService.locationSearch().what3WordsSearch() : '';
                        let remarkText = TaskService.task().location().remarks().replace(patternMatch, what3WordsText);
                        TaskService.task().location().remarks(remarkText);
                    }
                },
                canShowUpgradeCUVPrompt: () => {
                    return TaskService.task().contractValidation().roadworthiness().override() !== null || TaskService.task().vehicle().roadworthy().motStatus() === 'Valid';
                },
                send: function send() {
                    _pendingRequest = true;
                    TaskService.isNewlyCreated(true);
                    let entitlement = CSHService.entitlement();
                    let brcEntitlement = null;

                    TaskService.setKeyOnOrderCapability();
                    ctrl.setLocationRemarks();
                    TaskService.task().recovery().motorway(TaskService.task().indicators().motorway());
                    if (entitlement) {
                        if (entitlement.hasBrc()) {
                            brcEntitlement = new BrcEntitlement();
                            brcEntitlement.contact(entitlement.contact());
                            brcEntitlement.policy(entitlement.policy());
                            brcEntitlement.products(entitlement.products());
                            brcEntitlement.systemId(entitlement.systemId());
                        }
                        PromptService.checkDemandDeflection(CSHService.entitlement(), TaskService.task());

                        // let's assure that for Demand deflection check we always have a reg number
                        if (entitlement.policy().membershipType() && entitlement.policy().membershipType().toLowerCase() !== 'vehicle') {
                            entitlement.vehicle().registration(TaskService.task().vehicle().registration());
                        }
                    }
                    if ($state.paymentReference) {
                        TaskService.task().contractValidation().outcome().paymentReference($state.paymentReference);
                    }
                    TextTopicService.showTextTopicsForCategories(['SVC_DATA', 'REG_DATA', 'IM_DATA']);
                    if (TaskService.task().split()) {
                        // call add task ...
                        TaskService.addTask()
                            .then(function addSuccess(newTask) {
                                // make sure mapping is clear of all icons
                                MappingService.reset();
                                MappingService.setMapCenter(TaskService.task().location().coordinates().latitude(), TaskService.task().location().coordinates().longitude()); // Set breackdown location center on map

                                MappingService.setMapCenter(TaskService.task().location().coordinates().latitude(), TaskService.task().location().coordinates().longitude()); // Set breackdown location center on map

                                // remove temporary clone task from list and update details of parent task
                                CSHService.csh().tasks().pop();
                                CSHSearchViewerService.task(newTask);

                                TaskService.task(newTask);

                                // wire new task in csh tasks and update status ..
                                CSHService.addTask(newTask);

                                $state.go('summary');
                                _pendingRequest = false;

                                TaskService.checkCorrespondenceAddress(); // Id correspondence address is invalid show prompt and send sms
                            })
                            .catch(function addFailure() {
                                AlertService.createAlert('Failed to send task', AlertTypes.DANGER); // danger .. danger high voltage ... :)
                                _pendingRequest = false;
                            });
                    } else {
                        if (TaskService.completionCodes().length) {
                            TaskService.task().fault().outcome().completionCode(TaskService.completionCodes()[0]); // Cancellation code 71 -> "WARRANTY DRIVE IN (JAGUAR/LAND ROVER)"
                            CompletionService.completeTask(true);
                        }
                        if (TaskService.goodWill()) {
                            TaskService.task().fault().outcome().completionCode('XD'); // Cancellation code XD -> "HYUNDAI GOODWILL"
                            CompletionService.completeTask(true);
                        }
                        if (TaskService.storage()) {
                            TaskService.task().fault().outcome().completionCode('XF'); // Cancellation code XF -> "EUROHELP - STORAGE"
                            CompletionService.completeTask(true);
                        }
                        if (TaskService.repair()) {
                            TaskService.task().fault().outcome().completionCode('XG'); // Cancellation code XG -> "EUROHELP - REPAIR"
                            CompletionService.completeTask(true);
                        }
                        if (TaskService.abandonment()) {
                            TaskService.task().fault().outcome().completionCode('XH'); // Cancellation code XH -> "EUROHELP - ABANDONMENT"
                            CompletionService.completeTask(true);
                        }
                        if (TaskService.abandonment()) {
                            TaskService.task().fault().outcome().completionCode('XH'); // Cancellation code XH -> "EUROHELP - ABANDONMENT"
                            CompletionService.completeTask(true);
                        }
                        if (TaskService.task().entitlement().customerGroup().isEuroHelp() && (TaskService.task().createReason().isTransport() || TaskService.task().createReason().isHotel())) {
                            TaskService.task().fault().outcome().completionCode('E1'); // Completion code E1 -> "EUROHELP - secondary benefit task"
                            CompletionService.completeTask(true);
                        } else {
                            TaskService.send(CSHService.csh(), brcEntitlement, ctrl.canShowUpgradeCUVPrompt() ? { checkRoadAddon: true, showUpgradeCUV: true } : {})
                                .then(function saveSuccess(responseModel) {
                                    SearchService.locationSearch().what3WordsSearch('');
                                    if (TaskService.isTaskMerged()) {
                                        PromptService.taskMerged().then(() => {
                                            $state.go('task', {
                                                taskid: TaskService.task().id()
                                            });
                                        });
                                    } else {
                                        CSHService.enableNewCaseBtn(responseModel.status());
                                        if (responseModel.schedule().resource().location().isSet()) {
                                            MappingService.addResource(responseModel.schedule().resource());
                                        }
                                        UIService.updateCR(TaskService.task()); // update CR status
                                        MappingService.reset();

                                        if (TaskService.task().entitlement().customerGroup().isHondaAssistance()) {
                                            MappingService.saveHondaAssistDetails();
                                        }

                                        _pendingRequest = false;
                                        CSHService.reattendEnableDisable(CSHService.csh().tasks(), CSHService.csh().id());

                                        if (responseModel.hasAvailability()) {
                                            //show appointment view
                                            $state.go('appointment');
                                        } else {
                                            $state.go('summary');
                                        }
                                    }
                                    if (isCuv && TaskService.task().demandDeflection().isCUV() === 'yes') {
                                        let membership = entitlement.policy().membershipNumber();
                                        let vrn = TaskService.task().vehicle().registration();
                                        let taskId = TaskService.task().id();
                                        let customerRequestId = TaskService.task().customerRequestId();
                                        let remarks =
                                            'membership ' + membership + ' was used by ' + TaskService.task().contact().name() + ' on a vehicle ' + vrn + ' that is  covered  by a CUV add on';
                                        return CuvService.reportCUVToMI(membership, vrn, remarks, taskId, customerRequestId);
                                    }
                                    return true;
                                })
                                .then(() => {
                                    TaskService.checkCorrespondenceAddress(); // If correspondence address is invalid show prompt and send sms
                                })
                                .catch(function (error) {
                                    _pendingRequest = false;
                                });
                        }
                    }
                },
                isTaskValid: () => {
                    return MobilityTaskService.mobilityTask().rental().mainDriver().id() > 0;
                },
                createMobilityTask: () => {
                    return MobilityTaskService.createTask()
                        .then(() => {
                            AlertService.createAlert(' Mobility Task created', AlertTypes.INFO, 3000);
                        })
                        .catch(() => {
                            AlertService.createAlert(' Mobility Task create failed', AlertTypes.DANGER);
                        });
                },
                saveMobilityTask: () => {
                    if (ValidationService.mobilityComponentsValid(MobilityTaskService.mobilityTask(), MobilityTaskService.getBusinessRules())) {
                        let mobilityTask = MobilityTaskService.mobilityTask();
                        let thirdPartyHire = mobilityTask.rental().thirdPartyHire();
                        if (thirdPartyHire && thirdPartyHire.experianDetails() && thirdPartyHire.isManualEntReservation()) {
                            let experianDetails = thirdPartyHire.experianDetails();
                            mobilityTask.rental().thirdPartyHire().hireVehicle().regNo(experianDetails.registration());
                            mobilityTask.rental().thirdPartyHire().hireVehicle().make(VehicleService.getMakeByID(experianDetails.makeId()));
                            mobilityTask.rental().thirdPartyHire().hireVehicle().model(VehicleService.getModelByID(experianDetails.modelId()));
                            mobilityTask.rental().thirdPartyHire().hireVehicle().type(VehicleService.getTypeByID(experianDetails.typeId()));
                            mobilityTask.rental().thirdPartyHire().hireVehicle().vin(experianDetails.experianDetails().vin());
                            mobilityTask.rental().thirdPartyHire().hireVehicle().registrationDate(experianDetails.experianDetails().firstRegistered());
                            mobilityTask.rental().thirdPartyHire().hireVehicle().purchaseDate(experianDetails.experianDetails().firstRegistered());
                            mobilityTask.rental().thirdPartyHire().hireVehicle().seatNumber(experianDetails.experianDetails().seatNumber());
                            // mobilityTask.rental().thirdPartyHire().isManualEntReservation(ctrl.isManualEntReservation);
                            mobilityTask.rental().thirdPartyHire().hireVehicle().transmissionType(_.split(experianDetails.experianDetails().transmission(), ' ')[0]);
                            if (mobilityTask.rental().thirdPartyHire().isSelfCheckout()) {
                                mobilityTask.rental().thirdPartyHire().hireVehicle().vehicleValue(10000);
                            }
                        }
                        MobilityTaskService.write()
                            .then((responseModel) => {
                                if (responseModel && responseModel.schedule().resource().location().isSet()) {
                                    MappingService.addResource(responseModel.schedule().resource());
                                    $state.go('locationDetails');
                                    AlertService.createAlert(' Mobility Task sent/saved', AlertTypes.INFO, 3000);
                                }
                            })
                            .catch(() => {
                                AlertService.createAlert(' Mobility Task save/write failed', AlertTypes.DANGER);
                            });
                    }
                },
                save: function save() {
                    ctrl.setLocationRemarks();
                    TaskService.task().recovery().motorway(TaskService.task().indicators().motorway());
                    if (QualificationService.activeTaskType() === Qualification.IM && TaskService.isContactNumberEmpty()) {
                        PromptService.showSaveTaskWithoutContactNumber().then((prompt) => {
                            if (prompt.selectedOption().optionType() === 'YES') {
                                performSaveActions();
                            }
                        });
                    } else {
                        performSaveActions();
                    }
                    TaskService.isNewlyCreated(true);
                    let isCuv = TaskService.task()
                        .entitlement()
                        .benefits()
                        .find((benefit) => benefit.id() == 62);
                    if ($state.paymentReference) {
                        TaskService.task().contractValidation().outcome().paymentReference($state.paymentReference);
                    }
                    if (isCuv && TaskService.task().demandDeflection().isCUV() === 'yes') {
                        let entitlement = CSHService.entitlement();
                        let membership = entitlement.policy().membershipNumber();
                        let vrn = TaskService.task().vehicle().registration();
                        let taskId = TaskService.task().id();
                        let customerRequestId = TaskService.task().customerRequestId();
                        let remarks = 'membership ' + membership + ' was used by ' + TaskService.task().contact().name() + ' on a vehicle ' + vrn + ' that is  covered  by a CUV add on';
                        return CuvService.reportCUVToMI(membership, vrn, remarks, taskId, customerRequestId);
                    }
                },
                close: function close() {
                    if (TaskService.task().isDirty()) {
                        PromptService.showDataChangedWarning().then((ok) => {
                            ok && QualificationService.isWorkingOnQualificationTask() ? QualificationService.releaseTask() : null;
                        });
                    } else if (CSHService.hasNewTasks()) {
                        PromptService.showMemberHasNewTaskWarning();
                    } else {
                        TaskService.showDRM(false);
                        ServiceTypeService.reset();
                        CustomerGroupService.reset();
                        QualificationService.isWorkingOnQualificationTask() ? QualificationService.releaseTask() : null;
                        SidePanelService.rightPanelVisible(false);
                        $state.go('home');
                    }
                },
                isWillJoin: function isWillJoin() {
                    return EligibilityService.isWillJoin() && TaskService.task().isNew();
                },
                complete: function complete(reason) {
                    if (TaskService.task().contact().name()) {
                        TaskService.task().fault().outcome().completionCode(reason.code);
                        TaskService.task()
                            .location()
                            .remarks(reason.msg + TaskService.task().location().remarks());

                        CompletionService.completeTask();
                    }
                },
                getTask: () => TaskService.task(),
                willJoinCompletionReasons: function () {
                    return completionReasons.WILL_JOIN;
                },
                disableOnVrnChange: () => {
                    let customerGroup = TaskService.task().entitlement().customerGroup();
                    if (!customerGroup.isEuroHelp()) {
                        if (customerGroup.isRoadsideAddOn() || !(customerGroup.isPersonal() || customerGroup.isBank() || EligibilityService.isWillJoin())) {
                            if (!VehicleService.oldVRNVal) {
                                VehicleService.oldVRNVal = TaskService.task().vehicle().registration();
                            }
                            if (
                                VehicleService.oldVRNVal &&
                                TaskService.task().vehicle().registration() &&
                                VehicleService.oldVRNVal.toUpperCase() !== TaskService.task().vehicle().registration().toUpperCase() &&
                                !SearchService.hasConflictingVehicleData()
                            ) {
                                return true;
                            }
                        }
                    }
                },
                sendButtonDisabled: function () {
                    if (ctrl.disableOnVrnChange()) {
                        return true;
                    }

                    if (!ValidationService.sendButtonEnabled(TaskService.task())) {
                        if (ValidationService._taskComponentsValid(TaskService.task()) && TaskService.task().vehicle().roadworthy().motStatus() === 'Not valid') {
                            return false;
                        }
                        return true;
                    }

                    // if in the middle of the flp
                    if ($state.is('flp')) {
                        return true;
                    }

                    // TODO: uncomment if we want block send based on flp rejection
                    // if flp on the task rejected
                    // if (!FlpTaskService.canSendFLP()) {
                    //     return true;
                    // }

                    if (_pendingRequest || checkMileageType()) {
                        return true;
                    }

                    if (TaskService.task().createReason().isHireCar() && !ctrl.isTaskValid()) {
                        return true;
                    }

                    return !!EntitlementHelperService.isNoServiceAccount();
                },
                sendBtnStatus: function () {
                    return !ValidationService.sendBtnStatus();
                },
                isEcallSending: () => EcallService.isSending(),
                onCompleteEcallTask: () => {
                    if (ValidationService.ecallValid()) {
                        EcallService.completeTask()
                            .then(() => {
                                AlertService.createAlert(`E-Call task has been completed`);
                                $state.go('csh');
                            })
                            .catch((error) => {
                                AlertService.createAlert(`Ecall Task completion failed: ${error}`, AlertTypes.DANGER);
                            });
                    } else {
                        AlertService.createAlert('Task details are missing');
                    }
                },
                onComplete: () => {
                    if (ValidationService.skipCompletionState(TaskService.task())) {
                        CompletionService.completeTask(true);
                    }
                    if (TaskService.task().createReason().isPublicTransport()) {
                        if (!TaskService.task().transport().pickUpLocation().text() || !TaskService.task().transport().dropOffLocation().text()) {
                            return AlertService.createAlert('Pick up location or drop Off location is missing');
                        } else {
                            return AlertService.createAlert('Please fill the mandatory fields');
                        }
                    } else {
                        if (TaskService.task().createReason().isRelayPlus()) {
                            if (!TaskService.task().miscFields().relayPlus().rentalNum()) {
                                return AlertService.createAlert('Please provide a valid rental number to complete the job');
                            } else if (TaskService.task().miscFields().relayPlus().status() !== 'C-CANCELLATION' || TaskService.task().miscFields().relayPlus().status() !== 'S-SOURCED') {
                                return AlertService.createAlert("Please select RP Stage of either 'C-Cancellation' or 'S-SOURCED' to complete the job");
                            }
                        }
                        $state.go('completion');
                    }
                }
            });
        }
    ]);
