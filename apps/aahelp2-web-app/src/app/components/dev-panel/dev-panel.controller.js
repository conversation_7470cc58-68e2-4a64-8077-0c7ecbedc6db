require('angular');
const _ = require('lodash');
const { StateMachine } = require('../../helpers/state-machine');

const CTI_COMMAND = {
    AGENT_LOGON: 'AGENT_LOGON',
    AGENT_LOGOFF: 'AGENT_LOGOFF',
    AGENT_AVAILABLE: 'AGENT_AVAILABLE',
    AGENT_NOT_AVAILABLE: 'AGENT_NOT_AVAILABLE',
    MAKE_CALL: 'MAKE_CALL',
    HOLD_CALL: 'HOLD_CALL',
    RETRIEVE_CALL: 'RETRIEVE_CALL',
    CONSULT_CALL: 'CONSULT_CALL',
    CLEAR_CALL: 'CLEAR_CALL',
    CONFERENCE_CALL: 'CONFERENCE_CALL',
    CLEAR_DEVICE: 'CLEAR_DEVICE'
};

module.exports = angular
    .module('aah-dev-panel-controller-module', [
        require('../../services/audit/audit.service').name,
        require('../../constants/cti/cti-state-descriptions.constants').name,
        require('../../constants/cti/cti-state.constants').name,
        require('../../services/cti-task/out-bound-call-service').name
    ])
    .controller('aahDevPanelController', [
        'envService',
        '$injector',
        'hotkeys',
        '$rootScope',
        '$timeout',
        'aahCtiStateDescriptions',
        'aahCtiState',
        'aahoutbondcalleventservice',
        function aahDevPanelController(envService, $injector, hotkeys, $rootScope, $timeout, ctiStateMachineDescriptions, ctiState, outbondcalleventservice) {
            let ctrl = this;
            let enabled = envService.read('ENABLE_DEV_MODE');
            let ecallService = null;
            let ctiService = null;

            const initialState = {
                extension: null,
                lineOne: { cli: null, ddi: null, serial_number: '1111111' },
                lineTwo: { cli: null, ddi: null, serial_number: '2222222' }
            };
            const ctiSteps = [
                // Login
                {
                    id: ctiState.CTI_CONNECTING,
                    name: ctiStateMachineDescriptions.CTI_CONNECTING,
                    action: (args) => {
                        ctiStateMachine.state.extension = args.extension;
                        const event = { event: ctiState.CTI_CONNECTING };
                        ctiEventHandler(event);
                        $timeout(() => ctiStateMachine.next(), 100);
                    },
                    steps: [ctiState.CTI_CONNECTED]
                },
                {
                    id: ctiState.CTI_CONNECTED,
                    name: ctiStateMachineDescriptions.CTI_CONNECTED,
                    action: () => {
                        const event = { event: ctiState.CTI_CONNECTED };
                        ctiEventHandler(event);
                        $timeout(() => ctiStateMachine.next(), 100);
                    },
                    steps: [ctiState.AGENT_LOGGED_ON]
                },
                {
                    id: ctiState.AGENT_LOGGED_ON,
                    name: ctiStateMachineDescriptions.AGENT_LOGGED_ON,
                    action: () => {
                        const event = { event: ctiState.AGENT_LOGGED_ON };
                        ctiEventHandler(event);
                        $timeout(() => ctiStateMachine.next(), 100);
                    },
                    steps: [ctiState.AGENT_BUSY_NA]
                },
                {
                    id: ctiState.AGENT_BUSY_NA,
                    name: ctiStateMachineDescriptions.AGENT_BUSY_NA,
                    action: () => {
                        ctiStateMachine.state.lineOne.cli = null;
                        ctiStateMachine.state.lineOne.ddi = null;
                        ctiStateMachine.state.lineTwo.cli = null;
                        ctiStateMachine.state.lineTwo.ddi = null;
                        const extension = ctiStateMachine.state.extension;

                        const event = {
                            event: ctiState.AGENT_BUSY_NA,
                            cli: '',
                            ddi: '',
                            serial_number: '',
                            time_event: new Date(),
                            time_arrived: new Date(),
                            trunk: '',
                            extension,
                            tag_data: [],
                            data: { state: '' },
                            reason: ''
                        };
                        ctiEventHandler(event);
                    },
                    steps: [ctiState.AGENT_LOGGED_OFF, ctiState.AVAILABLE, ctiState.EXT_BUSY]
                },
                {
                    id: ctiState.AVAILABLE,
                    name: ctiStateMachineDescriptions.AVAILABLE,
                    action: () => {
                        const extension = ctiStateMachine.state.extension;

                        const event = {
                            event: ctiState.AVAILABLE,
                            cli: '',
                            ddi: '',
                            serial_number: '',
                            time_event: new Date(),
                            time_arrived: new Date(),
                            trunk: '',
                            extension,
                            tag_data: [],
                            data: { state: '' },
                            reason: ''
                        };

                        ctiEventHandler(event);
                    },
                    steps: [ctiState.AGENT_LOGGED_OFF, ctiState.EXT_BUSY, ctiState.AGENT_BUSY_NA, ctiState.INCOMING_CALL]
                },

                // Outgoing calling
                {
                    id: ctiState.EXT_BUSY,
                    name: ctiStateMachineDescriptions.EXT_BUSY,
                    action: (args) => {
                        const ddi = args.ddi;
                        let cli = ctiStateMachine.state.extension;
                        let serial_number;
                        if (!ctiStateMachine.state.lineOne.ddi) {
                            ctiStateMachine.state.lineOne.cli = cli;
                            ctiStateMachine.state.lineOne.ddi = ddi;
                            serial_number = ctiStateMachine.state.lineOne.serial_number;
                        } else {
                            ctiStateMachine.state.lineTwo.cli = cli;
                            ctiStateMachine.state.lineTwo.ddi = ddi;
                            serial_number = ctiStateMachine.state.lineTwo.serial_number;
                        }
                        const extension = ctiStateMachine.state.extension;

                        const event = {
                            event: ctiState.EXT_BUSY,
                            cli,
                            ddi,
                            serial_number,
                            time_event: new Date(),
                            time_arrived: new Date(),
                            trunk: '',
                            extension,
                            tag_data: [],
                            data: { state: '' },
                            reason: ''
                        };

                        ctiEventHandler(event);

                        $timeout(() => ctiStateMachine.next(ctiState.EXT_BUSY_OG, { cli, ddi, serial_number }), 100);
                    },
                    steps: [ctiState.EXT_BUSY_OG]
                },
                {
                    id: ctiState.EXT_BUSY_OG,
                    name: ctiStateMachineDescriptions.EXT_BUSY_OG,
                    action: (args) => {
                        const cli = args.cli;
                        const ddi = args.ddi;
                        const serial_number = args.serial_number;
                        const extension = ctiStateMachine.state.extension;

                        const event = {
                            event: ctiState.EXT_BUSY_OG,
                            cli,
                            ddi,
                            serial_number,
                            time_event: new Date(),
                            time_arrived: new Date(),
                            trunk: '',
                            extension,
                            tag_data: [],
                            data: { state: '' },
                            reason: ''
                        };

                        ctiEventHandler(event);

                        $timeout(
                            () =>
                                ctiStateMachine.next(ctiState.EXT_CALL_CONNECTED, {
                                    cli,
                                    ddi,
                                    serial_number
                                }),
                            100
                        );
                    },
                    steps: [ctiState.EXT_CALL_CONNECTED]
                },
                {
                    id: ctiState.EXT_CALL_CONNECTED,
                    name: ctiStateMachineDescriptions.EXT_CALL_CONNECTED,
                    action: (args) => {
                        const cli = args.cli;
                        const ddi = args.ddi;
                        const serial_number = args.serial_number;
                        const extension = ctiStateMachine.state.extension;

                        const event = {
                            event: ctiState.EXT_CALL_CONNECTED,
                            cli,
                            ddi,
                            serial_number,
                            time_event: new Date(),
                            time_arrived: new Date(),
                            trunk: '',
                            extension,
                            tag_data: [
                                { name: 'callVariable1', value: '' },
                                {
                                    name: 'callVariable2',
                                    value: ''
                                },
                                { name: 'callVariable3', value: '' }
                            ],
                            data: {
                                state: '',
                                dialogs: [
                                    {
                                        id: serial_number,
                                        fromAddress: cli,
                                        toAddress: ddi,
                                        state: 'ACTIVE',
                                        callType: 'OUT',
                                        mediaProperties: {
                                            dnis: '',
                                            callType: 'OUT',
                                            dialedNumber: ddi,
                                            callKeyCallId: '',
                                            callKeyPrefix: '',
                                            callKeySequenceNum: '',
                                            callVariables: [
                                                {
                                                    name: 'callVariable1',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable2',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable3',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable4',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable5',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable6',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable7',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable8',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable9',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable10',
                                                    value: ''
                                                }
                                            ]
                                        },
                                        participantCount: 2,
                                        participants: [
                                            {
                                                mediaAddress: cli,
                                                mediaAddressType: 'AGENT_DEVICE',
                                                startTime: new Date(),
                                                state: 'ACTIVE',
                                                stateCause: '',
                                                stateChangeTime: new Date(),
                                                actions: ['TRANSFER_SST', 'CONSULT_CALL', 'HOLD', 'UPDATE_CALL_DATA', 'SEND_DTMF', 'DROP']
                                            },
                                            {
                                                mediaAddress: ddi,
                                                mediaAddressType: '',
                                                startTime: new Date(),
                                                state: 'ACTIVE',
                                                stateCause: '',
                                                stateChangeTime: new Date(),
                                                actions: ['TRANSFER_SST', 'CONSULT_CALL', 'HOLD', 'UPDATE_CALL_DATA', 'SEND_DTMF', 'DROP']
                                            }
                                        ]
                                    }
                                ]
                            },
                            reason: ''
                        };

                        ctiEventHandler(event);
                    },
                    steps: [ctiState.TRUNK_HELD, ctiState.CALL_CLEARED, ctiState.CONFERENCED]
                },

                // Incoming call
                {
                    id: ctiState.INCOMING_CALL,
                    name: ctiStateMachineDescriptions.INCOMING_CALL,
                    action: (args) => {
                        const cli = args.cli;
                        const membershipNo = args.membershipNo;
                        const ddi = args.ddi;
                        const callFlag = args.callFlag;
                        const serial_number = ctiStateMachine.state.lineOne.serial_number;
                        const extension = ctiStateMachine.state.extension;
                        ctiStateMachine.state.lineOne.cli = cli;
                        ctiStateMachine.state.lineOne.ddi = ddi;

                        const event = {
                            event: ctiState.INCOMING_CALL,
                            cli,
                            ddi,
                            serial_number,
                            membershipNo,
                            time_event: new Date(),
                            time_arrived: new Date(),
                            trunk: '',
                            extension,
                            tag_data: [
                                { name: 'callVariable1', value: '' },
                                { name: 'callVariable2', value: '' },
                                // Used to detect e.g. Ecall
                                { name: 'callVariable3', value: `${callFlag} Some other value` }
                            ],
                            data: {
                                state: '',
                                dialogs: [
                                    {
                                        id: serial_number,
                                        fromAddress: cli,
                                        toAddress: ddi,
                                        state: 'ACTIVE',
                                        callType: 'OUT',
                                        mediaProperties: {
                                            dnis: '',
                                            callType: 'OUT',
                                            dialedNumber: ddi,
                                            callKeyCallId: '',
                                            callKeyPrefix: '',
                                            callKeySequenceNum: '',
                                            callVariables: [
                                                {
                                                    name: 'callVariable1',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable2',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable3',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable4',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable5',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable6',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable7',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable8',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable9',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable10',
                                                    value: ''
                                                }
                                            ]
                                        },
                                        participantCount: 2,
                                        participants: [
                                            {
                                                mediaAddress: cli,
                                                mediaAddressType: '',
                                                startTime: new Date(),
                                                state: 'ACTIVE',
                                                stateCause: '',
                                                stateChangeTime: new Date(),
                                                actions: ['TRANSFER_SST', 'CONSULT_CALL', 'HOLD', 'UPDATE_CALL_DATA', 'SEND_DTMF', 'DROP']
                                            },
                                            {
                                                mediaAddress: ddi,
                                                mediaAddressType: 'AGENT_DEVICE',
                                                startTime: new Date(),
                                                state: 'ACTIVE',
                                                stateCause: '',
                                                stateChangeTime: new Date(),
                                                actions: ['TRANSFER_SST', 'CONSULT_CALL', 'HOLD', 'UPDATE_CALL_DATA', 'SEND_DTMF', 'DROP']
                                            }
                                        ]
                                    }
                                ]
                            },
                            reason: ''
                        };

                        if (args.membershipNo) {
                            console.log(`Adding callVariable6 with value I|${args.membershipNo}`);
                            event.tag_data.push({ name: 'user.MemNo', value: `${args.membershipNo}` });
                        }

                        ctiEventHandler(event);

                        $timeout(() => ctiStateMachine.next(ctiState.EXT_BUSY_OG, { cli, ddi, serial_number }), 100);
                    },
                    steps: [ctiState.EXT_BUSY_OG]
                },
                // Conference
                {
                    id: ctiState.CONFERENCED,
                    name: ctiStateMachineDescriptions.CONFERENCED,
                    action: () => {
                        const extension = ctiStateMachine.state.extension;
                        const cli = ctiStateMachine.state.lineOne.cli;
                        const serial_number = ctiStateMachine.state.lineOne.serial_number;
                        const ddiOne = ctiStateMachine.state.lineOne.ddi;
                        const ddiTwo = ctiStateMachine.state.lineTwo.ddi;

                        const event = {
                            event: ctiState.CONFERENCED,
                            cli: '+447716759347',
                            ddi: '884556',
                            serial_number: '88790495',
                            time_event: new Date(),
                            time_arrived: new Date(),
                            trunk: '',
                            extension,
                            tag_data: [
                                { name: 'callVariable1', value: '' },
                                {
                                    name: 'callVariable2',
                                    value: ''
                                },
                                { name: 'callVariable3', value: '' }
                            ],
                            data: {
                                state: '',
                                dialogs: [
                                    {
                                        id: serial_number,
                                        fromAddress: cli,
                                        toAddress: ddiOne,
                                        state: 'ACTIVE',
                                        callType: 'CONFERENCE',
                                        mediaProperties: {
                                            dnis: '',
                                            callType: 'CONFERENCE',
                                            dialedNumber: '+447841594003',
                                            callKeyCallId: '',
                                            callKeyPrefix: '',
                                            callKeySequenceNum: '',
                                            callVariables: [
                                                {
                                                    name: 'callVariable1',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable2',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable3',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable4',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable5',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable6',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable7',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable8',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable9',
                                                    value: ''
                                                },
                                                {
                                                    name: 'callVariable10',
                                                    value: ''
                                                }
                                            ]
                                        },
                                        participantCount: 3,
                                        participants: [
                                            {
                                                mediaAddress: extension,
                                                mediaAddressType: 'AGENT_DEVICE',
                                                startTime: new Date(),
                                                state: 'ACTIVE',
                                                stateCause: '',
                                                stateChangeTime: new Date(),
                                                actions: ['TRANSFER_SST', 'CONSULT_CALL', 'HOLD', 'UPDATE_CALL_DATA', 'SEND_DTMF', 'DROP']
                                            },
                                            {
                                                mediaAddress: ddiOne,
                                                mediaAddressType: '',
                                                startTime: new Date(),
                                                state: 'ACTIVE',
                                                stateCause: '',
                                                stateChangeTime: new Date(),
                                                actions: ['TRANSFER_SST', 'CONSULT_CALL', 'HOLD', 'UPDATE_CALL_DATA', 'SEND_DTMF', 'DROP']
                                            },
                                            {
                                                mediaAddress: ddiTwo,
                                                mediaAddressType: '',
                                                startTime: new Date(),
                                                state: 'ACTIVE',
                                                stateCause: '',
                                                stateChangeTime: new Date(),
                                                actions: ['TRANSFER_SST', 'CONSULT_CALL', 'SEND_DTMF', 'UPDATE_CALL_DATA', 'HOLD', 'DROP']
                                            }
                                        ]
                                    }
                                ]
                            },
                            reason: ''
                        };

                        ctiEventHandler(event);
                    },
                    steps: [ctiState.EXT_CALL_CONNECTED, ctiState.CALL_CLEARED]
                },

                // Hold
                {
                    id: ctiState.TRUNK_HELD,
                    name: ctiStateMachineDescriptions.TRUNK_HELD,
                    action: (args) => {
                        const serial_number = args.serial_number;
                        let cli;
                        let ddi;
                        if (ctiStateMachine.state.lineOne.serial_number === serial_number) {
                            cli = ctiStateMachine.state.lineOne.cli;
                            ddi = ctiStateMachine.state.lineOne.ddi;
                        } else {
                            cli = ctiStateMachine.state.lineTwo.cli;
                            ddi = ctiStateMachine.state.lineTwo.ddi;
                        }
                        const extension = ctiStateMachine.state.extension;

                        const event = {
                            event: ctiState.TRUNK_HELD,
                            cli,
                            ddi,
                            serial_number,
                            time_event: new Date(),
                            time_arrived: new Date(),
                            trunk: '',
                            extension,
                            tag_data: [],
                            data: { state: '' },
                            reason: ''
                        };

                        ctiEventHandler(event);
                    },
                    steps: [ctiState.CALL_CLEARED, ctiState.EXT_BUSY, ctiState.EXT_CALL_CONNECTED, ctiState.TRUNK_HELD]
                },

                // Call dropped
                {
                    id: ctiState.CALL_CLEARED,
                    name: ctiStateMachineDescriptions.CALL_CLEARED,
                    action: (args) => {
                        const serial_number = args.serial_number;
                        let cli;
                        let ddi;
                        if (ctiStateMachine.state.lineOne.serial_number === serial_number) {
                            cli = ctiStateMachine.state.lineOne.cli;
                            ddi = ctiStateMachine.state.lineOne.ddi;
                        } else {
                            cli = ctiStateMachine.state.lineTwo.cli;
                            ddi = ctiStateMachine.state.lineTwo.ddi;
                        }
                        const extension = ctiStateMachine.state.extension;

                        const event = {
                            event: ctiState.CALL_CLEARED,
                            cli,
                            ddi,
                            serial_number,
                            time_event: new Date(),
                            time_arrived: new Date(),
                            trunk: '',
                            extension,
                            tag_data: [],
                            data: { state: '' },
                            reason: ''
                        };

                        ctiEventHandler(event);

                        $timeout(() => ctiStateMachine.next(ctiState.EXT_IDLE, { cli, ddi, serial_number }), 100);
                    },
                    steps: [ctiState.EXT_IDLE]
                },
                {
                    id: ctiState.EXT_IDLE,
                    name: ctiStateMachineDescriptions.EXT_IDLE,
                    action: (args) => {
                        const cli = args.cli;
                        const ddi = args.ddi;
                        const serial_number = args.serial_number;
                        const extension = ctiStateMachine.state.extension;

                        const event = {
                            event: ctiState.EXT_IDLE,
                            cli,
                            ddi,
                            serial_number,
                            time_event: new Date(),
                            time_arrived: new Date(),
                            trunk: '',
                            extension,
                            tag_data: [],
                            data: { state: '' },
                            reason: ''
                        };

                        ctiEventHandler(event);

                        $timeout(() => ctiStateMachine.next(), 100);
                    },
                    steps: [ctiState.AGENT_BUSY_NA]
                },

                // Logout
                {
                    id: ctiState.AGENT_LOGGED_OFF,
                    name: ctiStateMachineDescriptions.AGENT_LOGGED_OFF,
                    action: () => {
                        ctiStateMachine.state.lineOne.cli = null;
                        ctiStateMachine.state.lineOne.ddi = null;
                        ctiStateMachine.state.lineTwo.cli = null;
                        ctiStateMachine.state.lineTwo.ddi = null;
                        ctiStateMachine.state.extension = null;

                        const event = { event: ctiState.AGENT_LOGGED_OFF };
                        ctiEventHandler(event);
                        $timeout(() => ctiStateMachine.next(), 100);
                    },
                    steps: [ctiState.CTI_DISCONNECTING]
                },
                {
                    id: ctiState.CTI_DISCONNECTING,
                    name: ctiStateMachineDescriptions.CTI_DISCONNECTING,
                    action: () => {
                        const event = { event: ctiState.CTI_DISCONNECTING };
                        ctiEventHandler(event);
                        $timeout(() => ctiStateMachine.next(), 100);
                    },
                    steps: [ctiState.CTI_DISCONNECTED]
                },
                {
                    id: ctiState.CTI_DISCONNECTED,
                    name: ctiStateMachineDescriptions.CTI_DISCONNECTED,
                    action: () => {
                        const event = { event: ctiState.CTI_DISCONNECTED };
                        ctiEventHandler(event);
                        $timeout(() => ctiStateMachine.next(), 100);
                    },
                    steps: [ctiState.CTI_SHUTDOWN]
                },
                {
                    id: ctiState.CTI_SHUTDOWN,
                    name: ctiStateMachineDescriptions.CTI_SHUTDOWN,
                    action: () => {
                        const event = { event: ctiState.CTI_SHUTDOWN };

                        ctiEventHandler(event);
                    },
                    steps: [ctiState.CTI_CONNECTING]
                }
            ];
            const ctiStateMachine = new StateMachine(ctiSteps, ctiState.CTI_SHUTDOWN, initialState);

            const getService = (name) => {
                return $injector.get(name);
            };

            // TODO: dont work?
            // hotkeys.bindTo($rootScope)
            //     .add({
            //         combo: 'ctrl+alt+shift+]',
            //         description: 'Toggle dev panel',
            //         allowIn: ['INPUT', 'SELECT', 'TEXTAREA'],
            //         callback: function () {
            //             console.log('show dev panel');
            //             if (!enabled) {
            //                 console.warn('Dev panel disabled - can\'t show it!');
            //                 return;
            //             }
            //             ctrl.togglePanel();
            //         }
            //     });
            let ctiEventHandler = null;
            const ctiServerStub = (agentId, finesseDomain, _eventHandler, options) => {
                ctiEventHandler = _eventHandler;
                return {
                    sendCommand: (cmd, data, arg) => {
                        console.log('ctiServerStub command received', cmd, data, arg);

                        switch (cmd) {
                            case CTI_COMMAND.AGENT_LOGON:
                                ctiStateMachine.next(ctiState.CTI_CONNECTING, { extension: data });
                                break;
                            case CTI_COMMAND.AGENT_LOGOFF:
                                ctiStateMachine.next(ctiState.AGENT_LOGGED_OFF);
                                break;
                            case CTI_COMMAND.AGENT_AVAILABLE:
                                ctiStateMachine.next(ctiState.AVAILABLE);
                                break;
                            case CTI_COMMAND.AGENT_NOT_AVAILABLE:
                                ctiStateMachine.next(ctiState.AGENT_BUSY_NA);
                                break;
                            case CTI_COMMAND.MAKE_CALL:
                                ctiStateMachine.next(ctiState.EXT_BUSY, { ddi: data });
                                break;
                            case CTI_COMMAND.HOLD_CALL:
                                ctiStateMachine.next(ctiState.TRUNK_HELD, { serial_number: data });
                                break;
                            case CTI_COMMAND.RETRIEVE_CALL:
                                ctiStateMachine.next(ctiState.EXT_CALL_CONNECTED, { serial_number: data });
                                break;
                            case CTI_COMMAND.CONSULT_CALL:
                                ctiStateMachine.next(ctiState.TRUNK_HELD, { serial_number: ctiStateMachine.state.lineOne.serial_number });
                                $timeout(() => ctiStateMachine.next(ctiState.EXT_BUSY, { ddi: data }), 100);
                                break;
                            case CTI_COMMAND.CLEAR_CALL:
                                ctiStateMachine.next(ctiState.CALL_CLEARED, { serial_number: data });
                                break;
                            case CTI_COMMAND.CONFERENCE_CALL:
                                ctiStateMachine.next(ctiState.CONFERENCED);
                                break;
                            case CTI_COMMAND.CLEAR_DEVICE:
                                ctiStateMachine.next(ctiState.CALL_CLEARED, { serial_number: ctiStateMachine.state.lineOne.serial_number });
                                ctiStateMachine.next(ctiState.CALL_CLEARED, { serial_number: ctiStateMachine.state.lineTwo.serial_number });
                                break;
                            default:
                                console.warn('ctiServerStub unknown command', cmd, data, arg);
                        }
                    },
                    shutdown: () => {
                        console.log('ctiServerStub shutdown command received');
                        ctiStateMachine.next(ctiState.CTI_SHUTDOWN);
                    }
                };
            };

            const stubCTIService = (ctiService) => {
                if (!enabled) {
                    return;
                }

                // stub CTI service so we can control it
                window.AAFinesse = ctiServerStub;
                ctiService.getCTIServer = (agentId, finesseDomain, handler, options) => {
                    try {
                        return ctiServerStub(agentId, finesseDomain, handler, options);
                    } catch (error) {
                        throw new Error('Failure while creating CTI server instance');
                    }
                };
            };

            Object.assign(ctrl, {
                ctiState,
                ctiStateMachineDescriptions,
                enabled,
                visible: false,
                ctiServiceStubbed: false,
                expanded: {},
                ctiStateMachine: ctiStateMachine,
                stubCTIService: () => {
                    if (ctrl.ctiServiceStubbed) {
                        return;
                    }

                    stubCTIService(ctrl.ctiService());
                    ctrl.ctiServiceStubbed = true;
                },
                ecallService: () => {
                    if (!ecallService) {
                        ecallService = $injector.get('aahEcallService');
                    }

                    return ecallService;
                },
                ctiService: () => {
                    if (!ctiService) {
                        ctiService = $injector.get('cti-service');
                    }

                    return ctiService;
                },

                togglePanel: () => {
                    ctrl.visible = !ctrl.visible;
                },
                init: () => {
                    console.log(`Dev panel ${enabled ? 'enabled' : 'disabled'}!`);
                },
                getMainLineCall: () => {
                    outbondcalleventservice
                        .mainLineCLI()
                        .then(function (cli) {
                            console.log('cli: ', cli);
                            return cli; // Return cli value
                        })
                        .catch(function (error) {
                            console.error('Error fetching cli data:', error);
                            return { cli: '' };
                        });
                }
            });
        }
    ]);
