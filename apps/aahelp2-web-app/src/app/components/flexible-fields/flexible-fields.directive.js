require('angular');

module.exports = angular
    .module('aah-flexible-fields-module', [
        // controller
        require('./flexible-fields.controller').name
    ])
    .directive('aahFlexibleFields', [
        function FlexibleFieldsDirective() {
            return {
                restrict: 'E',
                templateUrl: 'partials/components/flexible-fields/flexible-fields.template.html',
                controller: 'aahFlexibleFieldsController',
                controllerAs: 'ctrl',
                bindToController: true,
                scope: {
                    getTask: '=',
                    templateGroup: '@',
                    showIds: '=',
                    showTitle: '=?'
                },
                link: function (scope, element, attrs, ctrl) {
                    if (angular.isUndefined(ctrl.showTitle)) {
                        ctrl.showTitle = true;
                    }
                }
            };
        }
    ])
    .config([
        'formlyConfigProvider',
        function config(formlyConfigProvider) {
            // set templates here
            formlyConfigProvider.setWrapper({
                name: 'bootstrapLabel',
                templateUrl: 'partials/components/flexible-fields/formly-field.template.html',
                overwriteOk: true
            });
        }
    ]);
