<div class="contract-validation-details">
    <!-- text/header area -->
    <div
        ng-if="config.description"
        class="details-container"
        ng-hide="config.manualDistance && config.manualDistance.input.showManualDistance.execute(config.manualDistance.input.showManualDistance.args)"
    >
        <div>
            <span ng-bind-html="ctrl.getSafeHtml(config.description)"></span>
            <ul ng-if="config.bulletPoints">
                <li ng-repeat="bullet in config.bulletPoints track by $index">{{bullet}}</li>
            </ul>
        </div>
    </div>
    <!-- end text/header area -->

    <!-- start overrride section  -->
    <aah-contract-validation-override-options ng-if="config.overrideOptions"></aah-contract-validation-override-options>
    <!-- end override section  -->

    <!-- dynamic data section  -->
    <div
        ng-if="config.dynamicDetails"
        class="details-container"
        ng-hide="config.dynamicDetails.input.hideDynamicDetails.execute(config.dynamicDetails.input.hideDynamicDetails.args)"
    >
        <div
            ng-if="config.dynamicDetails.input"
            class="dynamic-details-container"
            ng-class="{'done': config.dynamicDetails.input.isDone.execute(config.dynamicDetails.input.isDone.args)}"
        >
            <span class="icon {{config.dynamicDetails.input.iconClass}}"></span>
            <span><strong>{{config.dynamicDetails.input.getText.execute(config.dynamicDetails.input.getText.args)}}</strong></span>
        </div>
        <div ng-if="config.dynamicDetails.output">
            <p><span ng-bind-html="config.dynamicDetails.output.getText.execute(config.dynamicDetails.output.getText.args)"></span></p>
        </div>
    </div>
    <!-- end dynamic data section  -->

    <div
        ng-if="config.manualDistance"
        class="details-container"
        ng-show="config.manualDistance.input.showManualDistance.execute(config.manualDistance.input.showManualDistance.args)"
    >
        <div>
            <p class="manual-distance">Unable to calculate distance based on location selection.</p>
            <p>Please calculate the distance using <strong>AARoute Planner</strong> and enter the distance below.</p>

            <label
                ><input
                    class="form-field form-control manual-distance-input"
                    id="manualDistance"
                    name="manualDistance"
                    ng-model="ctrl.distanceEntered"
                    type="number"
                    min="0"
                    ng-model-options="ctrl.modelOptions"
                />
                miles
            </label>
        </div>
        <div ng-if="config.manualDistance.output">
            <p><span ng-bind-html="config.manualDistance.output.getText.execute(config.manualDistance.output.getText.args)"></span></p>
        </div>
    </div>

    <div
        ng-if="config.paymentRef"
        class="details-container"
        ng-show="config.paymentRef.input.showPaymentRef.execute"
    >
        <div>
            <p class="manual-distance">Please enter the payment reference number below.</p>

            <label
                ><input
                    class="form-field form-control manual-distance-input"
                    id="paymentRef"
                    name="paymentRef"
                    ng-model="ctrl.paymentRefEnteredValue"
                    ng-change="ctrl.paymentRefEntered(ctrl.paymentRefEnteredValue)"
                    type="text"
                    ng-model-options="ctrl.modelOptions"
                />
                Reference Number
            </label>
            <div>once the reference number has entered.select Ok and follow the FLP process for the prompt to be overriden.</div>
        </div>
    </div>

    <!-- decision area -->
    <div
        ng-if="config.decision"
        class="details-container"
    >
        <p>{{config.decision.text}}</p>
        <p ng-if="config.decision.information"><strong>{{config.decision.information}}</strong></p>
        <div
            ng-if="config.decision.options.length > 0"
            class="decision-options-container"
        >
            <button
                ng-repeat="option in config.decision.options track by $index"
                class="btn btn-prompt"
                ng-if="!option.preCondition || option.preCondition.execute(option.preCondition.args)"
                ng-click="ctrl.processActionItemAndGoNext(option)"
            >
                {{option.name}}
            </button>
        </div>
    </div>
    <!-- end decision area -->

    <!-- navigation and action buttons section -->
    <div class="nav-container">
        <!-- back button -->
        <div
            class="back-container"
            ng-if="!ctrl.renderBackButton(config.index)"
        >
            <button
                id="back"
                class="btn btn-secondary-action"
                ng-disabled="config.back && (config.back.backDisabled ||  config.back.preCondition && !config.back.preCondition.execute(config.back.preCondition.args))"
                ng-click="ctrl.goBack()"
            >
                <span class="icon-arrow-line"></span>
                Back
            </button>
        </div>
        <!-- end back button -->

        <!-- other actions -->
        <div class="actions-container">
            <button
                id="ok"
                class="btn btn-prompt"
                ng-show="config.paymentRef.input.showPaymentRef.execute"
                ng-disabled="!ctrl.paymentRefEnteredValue"
                ng-click="ctrl.goBack()"
            >
                OK
            </button>
            <div ng-repeat="action in config.navigationActions track by $index">
                <button
                    id="{{action.name}}"
                    class="btn btn-prompt"
                    ng-if="!action.preCondition || action.preCondition.execute(action.preCondition.args)"
                    ng-click="ctrl.processActionItemAndGoNext(action)"
                >
                    {{action.name}}
                </button>
            </div>
        </div>
        <!-- end other actions -->
    </div>
    <!-- end of navigation and action buttons section -->

    <!-- taskId repeat fault text area -->
    <div
        ng-if="config.associatedTaskId"
        class="task-id-container"
    >
        <p class="task-id-text">{{( 'Associated Task ID:' + ctrl.getFaultAssociatedTaskId() )}}</p>
    </div>
    <!-- end taskId repeat fault text area -->
    <!-- taskId second recovery text area -->
    <div
        ng-if="config.associatedTaskIdSecondRecovery"
        class="task-id-container"
    >
        <p class="task-id-text">{{( 'Associated Task ID:' + ctrl.getFaultAssociatedTaskIdSecondaryFault() )}}</p>
    </div>
    <!-- end taskId second recovery text area -->
    <!-- taskId repeat fault text area -->
    <div
        ng-if="config.associatedTaskIdCuv"
        class="task-id-container"
    >
        <p class="task-id-text">{{( 'Associated Task ID:' + ctrl.getFaultAssociatedTaskIdCuv() )}}</p>
    </div>
    <!-- end taskId repeat fault text area -->
</div>
