var _ = require('lodash');
const EurohelpData = require('@aa/malstrom-models/lib/eurohelp-task-data.model');
require('angular');

module.exports = angular.module('aah-eurohelp-controller-module', []).controller('aahEurohelpController', [
    'aahCSHService',
    '$scope',
    '$timeout',
    '$state',
    function EurohelpController(CSHService, $scope, $timeout, $state) {
        var ctrl = this;

        _.extend(ctrl, {
            createReason: function createReason() {
                return ctrl.getTask().createReason();
            },
            customerGroup: function customerGroup() {
                return ctrl.getTask().entitlement().customerGroup();
            },
            shouldShowEurohelp: function shouldShowEurohelp() {
                //more is better
                const isEuroHelp = ctrl.customerGroup().isEuroHelp();
                const isGarageRepair = ctrl.createReason().isGarageRepair();
                const isStorage = ctrl.createReason().isStorage();
                const isRecovery = ctrl.createReason().isRecovery();
                return isEuroHelp && (isGarageRepair || isStorage || isRecovery);
            },
            modelOptions: {
                getterSetter: true
            },
            task: () => {
                return ctrl.getTask();
            },
            vehicleTrailerOptions: () => {
                return EurohelpData.VEHICLETRAILEROPTIONS;
            }
        });
    }
]);
