<!-- Questions & Answers Section -->
<div
    class="section"
    ng-if="ctrl.shouldShowEurohelp()"
>
    <div class="section-header clear">
        <div class="col-xs-12">
            <h2>Eurohelp</h2>
        </div>
    </div>
    <div class="section-body">
        <!--
        /************************************************************************
         * Eurohelp - Scrapped - but Scrapped is just storage. No way to tell
         ************************************************************************/
        -->
        <fieldset
            ng-show="true"
            class="section-body"
            ng-if="ctrl.createReason().isStorage()"
        >
            <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4">
                <label
                    for="vehicleTrailer"
                    class="label-primary"
                    >Vehicle/Trailer</label
                >
                <div class="select-wrap">
                    <select
                        class="form-select form-control"
                        ng-model="ctrl.task().eurohelp().vehicleTrailer"
                        ng-model-options="ctrl.modelOptions"
                        ng-options="option.key as option.value for option in ctrl.vehicleTrailerOptions()"
                    ></select>
                </div>
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4">
                <label
                    for="vehicleSettlement"
                    ß
                    class="label-primary"
                    >Vehicle Settlement</label
                >
                <input
                    id="vehicleSettlement"
                    class="form-field form-control"
                    type="text"
                    ng-model="ctrl.task().eurohelp().vehicleSettlement"
                    ng-model-options="ctrl.modelOptions"
                />
            </div>

            <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4">
                <label
                    for="trailerSettlement"
                    class="label-primary"
                    >Trailer Settlement</label
                >
                <input
                    id="trailerSettlement"
                    class="form-field form-control"
                    type="text"
                    ng-model="ctrl.task().eurohelp().trailerSettlement"
                    ng-model-options="ctrl.modelOptions"
                />
            </div>
        </fieldset>

        <!--
        /************************************************************************
         * Eurohelp - Recovery
         ************************************************************************/
        -->
    </div>
</div>
