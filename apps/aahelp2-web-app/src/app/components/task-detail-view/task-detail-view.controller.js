/* jshint expr: true */
var _ = require('lodash'),
    Task = require('@aa/malstrom-models/lib/task.model'),
    PartsFitted = require('@aa/malstrom-models/lib/parts-fitted.model'),
    ContractValidation = require('@aa/malstrom-models/lib/contract-validation.model');
const CreateReason = require('@aa/malstrom-models/lib/create-reason.model');

const CreateReasonModel = require('@aa/malstrom-models/lib/create-reason.model');
const Prompt = require('@aa/malstrom-models/lib/prompt.model');

require('angular');

module.exports = angular
    .module('aah-task-detail-view-controller-module', [
        // services
        require('../../services/create-reasons/create-reasons.service').name,
        require('../../services/csh/csh.service').name,
        require('../../services/task/task.service').name,
        require('../../services/mobility-task/mobility-task.service').name,
        require('../../services/mobility/mobility-retailer.service').name,
        require('../../services/tab/tab.service').name,
        require('../../services/mapping/location/location.service').name,
        require('../../services/search/search.service').name,
        require('../../services/task-operations/task-operations.service').name,
        require('../../services/alert/alert.service').name,
        require('../../services/csh-search-viewer/csh-search-viewer.service').name,
        require('../../services/task-audit/task-audit.service').name,
        require('../../services/role-mask/role-mask.service').name,
        require('../../services/prompt/prompt.service').name,
        require('../../constants/alert/alert-type.constants').name,
        require('../../services/electronic-documents/electronic-documents.service').name,
        require('../../services/user-info/user-info.service').name,
        require('../../services/entitlement/entitlement-helper.service').name,
        require('../../services/supplier/supplier.service').name,
        require('../../services/address-lookup/address-lookup.service').name,
        require('../../services/diagnostics/diagnostics.service').name,
        require('../../services/contract-validation/contract-validation.service').name,

        //constants
        require('../../constants/csh/csh-search-types.constants').name,
        require('../../constants/hire-types/hire-types.constants').name
    ])
    .controller('aahTaskDetailViewController', [
        '$state',
        '$sce',
        'aahCreateReasonsService',
        'aahCSHService',
        'aahTaskService',
        'aahTabService',
        'aahLocationService',
        'aahSearchService',
        'aahTaskOperationsService',
        'aahAlertService',
        'aahCSHSearchViewerService',
        'aahTaskAuditService',
        'aahTextTopicService',
        'aahRoleMaskService',
        'aahElectronicDocumentsService',
        'uibAlertTypes',
        'aahMobilityRetailerService',
        'aahMobilityTaskService',
        'aahUserInfoService',
        'aahPromptService',
        'aahTaskHelper',
        'aahSupplierService',
        'aahAddressLookupService',
        'aahCshSearchTypes',
        'aahHireTypes',
        'aahEntitlementHelperService',
        'aahDiagnosticsService',
        'aahContractValidationService',
        'aahVehicleService',
        function TaskDetailViewController(
            $state,
            $sce,
            CreateReasonsService,
            CSHService,
            TaskService,
            TabService,
            LocationService,
            SearchService,
            TaskOperationsService,
            AlertService,
            CSHSearchViewerService,
            TaskAuditService,
            TextTopicService,
            RoleMaskService,
            ElectronicDocumentsService,
            AlertTypes,
            MobilityRetailerService,
            MobilityTaskService,
            UserInfoService,
            PromptService,
            TaskHelper,
            SupplierService,
            AddressLookupService,
            CshSearchTypes,
            HireTypes,
            EntitlementHelperService,
            DiagnosticsService,
            ContractValidationService,
            VehicleService
        ) {
            var ctrl = this,
                _reopenReason = null,
                _reattendReason = null,
                _partsData = [],
                _userData = null,
                _processReopenReattend = function processReopenReattend(deferred, isReattend) {
                    deferred.then(
                        function onReopenReattendSuccess(newCase) {
                            TabService.reset();
                            CSHService.entitlement(newCase.entitlement);
                            CSHService.addTask(newCase.task); // add task to existing CR
                            CSHService.crListByReattendedTasks(newCase.task.customerRequestId(), CshSearchTypes.ADD_CR);
                            TaskService.task(newCase.task); // show the user the new task

                            if (isReattend) {
                                //In case of reattend, Estimated repair time is set to default estimated time to complete than the repair of parent task - RBAUAA-259
                                DiagnosticsService.getDiagnosticQuestion(1, 1, TaskService.task().fault()).then(function getDiagnosticQuestionHandler(question) {
                                    if (question && question.answers()) {
                                        TaskService.task().fault().repairMinutes(question.answers()[0].estimatedTimeToComplete());
                                        ctrl.setCapabilitiesOnReattend(question.answers()[0].repairId());
                                    }
                                });

                                if (TaskService.task().isNew() || TaskService.isSelfServiceAppTask(TaskService.task())) {
                                    PromptService.isHondaAssistance(TaskService.task());
                                }

                                // We are assuming here that vrn is correct from loaded task, chance we should set VRN check result as passed to allow further processing
                                const vrn = TaskService.task().vehicle().registration();
                                if (vrn && VehicleService.validateVRN(vrn)) {
                                    // set VRN check result as passed to allow further processing
                                    TaskService.task().contractValidation().vrn().passed(true);
                                }

                                PromptService.checkEntitlement(newCase.entitlement, newCase.task);
                                TaskService.setVulnerableCustomerRemarks();
                                TaskService.setVulnerableCustomerAudit();
                            }
                            CSHSearchViewerService.task(newCase.task);
                            return ContractValidationService.onReattendCheck(newCase).then(() => {
                                LocationService.plotAll();
                                $state.go('contact');
                            });
                        },
                        function onReopenReattendSuccess(err) {
                            CSHService.reattendBtnDisabled(false);
                            AlertService.createAlert('Failed to reopen / reattend task', AlertTypes.DANGER);
                        }
                    );
                },
                _addTaskAfterReopen = function addTaskAfterReopen(val) {
                    CSHService.csh(CSHSearchViewerService.customerRequest());
                    SearchService.getEntitlementByCR(CSHService.csh()).then(function getEntitlementByCRSuccess(resResponse) {
                        if (TaskService.isFairPlayValue()) {
                            TaskService.entitlementExhaustedAlert();
                        }
                        if (val && val.isHireCar()) {
                            MobilityRetailerService.enterpriseBusinessRules()
                                .then(function getBussinessRules() {
                                    if (MobilityRetailerService.isEligibleForCarHire()) {
                                        AddressLookupService.resetLookupAddresses();
                                        _loadEntitlements(resResponse.entitlements, val);
                                    }
                                })
                                .catch(function getBussinessRulesError(err) {
                                    AlertService.createAlert('Use AAHELP legacy to book a hire car for this customer group', AlertTypes.DANGER);
                                });
                        } else {
                            _loadEntitlements(resResponse.entitlements, val);
                        }
                    });
                },
                _loadEntitlements = function loadEntitlements(entitlements, val) {
                    // resResponse may contain multiple entitlements ...
                    // at the moment we assume that we will pick the first one
                    if (entitlements.length === 1) {
                        PromptService.checkEntitlement(entitlements[0], ctrl.getTask()).then(function (resp) {
                            if (resp) {
                                _processReopenReattend(TaskService.reopen(val, ctrl.getTask(), entitlements[0]));
                                PromptService.toggleMergeOptionWillJoin(false);
                            }
                        });
                    } else {
                        AlertService.createAlert("Can't create reopen because failed to uniquely identify entitlement record", AlertTypes.DANGER);
                    }
                };

            if (UserInfoService.userInfo()) {
                _userData = {
                    operatorId: UserInfoService.userInfo().operatorId(),
                    userName: UserInfoService.userInfo().userName(),
                    businessLocationId: UserInfoService.userInfo().businessLocationId(),
                    ccOperatorPin: 12345
                };
            }

            _.extend(ctrl, {
                /**
                 *
                 * @description Resets the createReasons if the task is in COMP status
                 *              Reopen -    "Add Task" button on task-detail-view
                 *              Reattend -  "Reattend" button on task-detail-view
                 *
                 */
                init: function () {
                    //commenting out the old logic for now this is to address the issue of the reopen and reattend reasons not being populated RBAUAA-11814
                    // CreateReasonsService.reset();
                    // if (
                    //     _.includes([Task.COMP_STATUS], ctrl.getTask().status()) ||
                    //     (_.includes([Task.CLSD_STATUS], ctrl.getTask().status()) && MobilityTaskService.getBusinessRules().agileEnabled && ctrl.getTask().isCompletedWithin(7))
                    // ) {
                    //     CreateReasonsService.getReopenReasons(ctrl.getTask()).then(function (dataSet) {
                    //         CreateReasonsService.reopenReasons(dataSet);
                    //     });
                    //     CreateReasonsService.getReattendReasons(ctrl.getTask()).then(function (dataSet) {
                    //         CreateReasonsService.reattendReasons(dataSet);
                    //     });
                    // } else if (ctrl.enableAddTaskStatuses().indexOf(ctrl.getTask().status()) > -1) {
                    //     CreateReasonsService.getByTaskId(ctrl.getTask()).then(function (dataSet) {
                    //         CreateReasonsService.reopenReasons(dataSet);
                    //     });
                    // }
                },
                showDemandDeflection: () => {
                    // Show only for active tasks
                    return !(_.includes([Task.COMP_STATUS], ctrl.getTask().status()) || _.includes([Task.CLSD_STATUS], ctrl.getTask().status()));
                },
                getPartsData: function getPartsData() {
                    if (ctrl.getTask()) {
                        let _partsFittedData = [];
                        let _partsFittedParse = JSON.parse(sessionStorage.getItem(ctrl.getTask().id()));

                        _.forEach(_partsFittedParse, function (part) {
                            _partsFittedData.push(new PartsFitted(part));
                        });
                        if (JSON.stringify(_partsData) == JSON.stringify(_partsFittedParse)) {
                            return _partsData;
                        }
                        _partsData = _partsFittedData;
                        return _partsData;
                    }
                },
                getTask: function getTaskFunction() {
                    //the task is passed in on the scope
                    return ctrl.task;
                },
                invoiceRef: () => {
                    var _task = ctrl.getTask();
                    if (_task) {
                        return TaskHelper.generateInvoiceNumber(_task.coveringSiteId(), _task.jobNoToday(), _task.schedule().create());
                    }
                    return null;
                },
                reopen: function reopenAccessor(val) {
                    if (arguments.length && val && (val.isHotel() || val.isTransport())) {
                        TaskService.task(ctrl.task);
                        if (TaskService.isFairPlayValue()) {
                            TaskService.entitlementExhaustedAlert();
                        }
                        TaskOperationsService.addChildTask(val);
                    } else if (arguments.length && val !== null) {
                        _addTaskAfterReopen(val);
                    }
                    return arguments.length ? (_reopenReason = val) : _reopenReason;
                },
                reattend: function reattendAccessor(val) {
                    CSHService.reattendBtnDisabled(true);
                    ctrl.removeDangerousLocationRemarks();
                    if (arguments.length && val !== null) {
                        CSHService.csh(CSHSearchViewerService.customerRequest());
                        SearchService.getEntitlementByCR(CSHService.csh()).then(function (resResponse) {
                            // resResponse may contain multiple entitlements ...
                            // at the moment we assume that we will pick the first one
                            if (resResponse.entitlements.length === 1) {
                                // On reattend, clear contractvalidation object
                                let _task = ctrl.getTask();
                                _task.contractValidation(new ContractValidation());

                                PromptService.checkEntitlement(resResponse.entitlements[0], ctrl.getTask()).then(function (resp) {
                                    if (resp) {
                                        let isReattend = true;
                                        _processReopenReattend(TaskService.reattend(val, ctrl.getTask(), resResponse.entitlements[0]), isReattend);
                                        PromptService.toggleMergeOptionWillJoin(false);
                                    } else {
                                        CSHService.reattendBtnDisabled(false);
                                    }
                                });
                            } else {
                                AlertService.createAlert("Can't create reattend because failed to uniquely identify entitlement record", AlertTypes.DANGER);
                                CSHService.reattendBtnDisabled(false);
                            }
                        });
                    }
                    return arguments.length ? (_reattendReason = val) : _reattendReason;
                },
                removeDangerousLocationRemarks: function () {
                    if (TaskService.task().location().remarks()) {
                        TaskService.task().location().remarks(TaskService.task().location().remarks().split(', Dangerous')[0]);
                    }
                },
                reattendBtnDisabled: function () {
                    return CSHService.reattendBtnDisabled();
                },
                reopenReasons: function reopenReasonsAccessorAccessor(val) {
                    return CreateReasonsService.reopenReasons();
                },
                reattendReasons: function reattendReasons(val) {
                    return CreateReasonsService.reattendReasons();
                },
                isTaskCompletionPresent: () => {
                    return (
                        TaskService.driveIn() ||
                        ctrl.getTask().fault().outcome().completionCode() === '71' ||
                        TaskService.goodWill() ||
                        (ctrl.getTask().fault().outcome().completionCode() === 'XD' && ctrl.getTask().entitlement().customerGroup().isHyundai())
                    );
                },
                isGoodWill: function isGoodWill() {
                    return TaskService.goodWill() || (ctrl.getTask().fault().outcome().completionCode() === 'XD' && ctrl.getTask().entitlement().customerGroup().isHyundai());
                },
                modify: function () {
                    // place holder for implementing modify
                },
                update: function () {
                    const updateAlertIdx = AlertService.createAlert('Fetching task details! \n Please wait..', AlertTypes.INFO, null, null, true);
                    TabService.reset();
                    SupplierService.reset();
                    CSHService.csh(CSHSearchViewerService.customerRequest());
                    TaskOperationsService.setDefaultEntitlement(ctrl.getTask())
                        .then(function onSetDefaultEntitlementSuccess(task) {
                            TaskService.task(task);
                            if (TaskService.task().recovery().destResourceId() === null && TaskService.task().isActive() && TaskService.task().isRelay()) {
                                TaskService.task().recovery().destResourceId(0); //set destResourceId 0 (Other) for active task and destResourceId is null
                            }
                            if (TaskService.task().createReason().isHotel()) {
                                AlertService.removeAlert(updateAlertIdx);
                                if (TaskService.task().transport && TaskService.task().transport().isTransportSet()) {
                                    $state.go('transport');
                                } else {
                                    $state.go('accommodation');
                                }
                            } else if (TaskService.task().createReason().isTransport()) {
                                AlertService.removeAlert(updateAlertIdx);
                                $state.go('transport');
                            } else if (TaskService.task().createReason().isRelayPlus()) {
                                AlertService.removeAlert(updateAlertIdx);
                                $state.go('relayPlus');
                            } else if (TaskService.task().createReason().isHireCar()) {
                                MobilityRetailerService.resetRetailerMarkers();
                                return MobilityRetailerService.enterpriseBusinessRules().then((businessRules) => {
                                    // TODO:: Below line should be removed once required experian data is stored in Mobility Task.
                                    MobilityRetailerService.getMobilityExperianDetails(TaskService.task().rental().srcRssTaskId());

                                    return MobilityTaskService.updateRental(businessRules).then(() => {
                                        let collectLocation = MobilityTaskService.mobilityTask().rental().collectLocation().isSet()
                                            ? MobilityTaskService.mobilityTask().rental().collectLocation().location()
                                            : MobilityTaskService.mobilityTask().location().coordinates();
                                        AddressLookupService.resetLookupAddresses();
                                        return MobilityRetailerService.fetchRetailersAndResources(collectLocation).then(() => {
                                            MobilityRetailerService.getSelectedMobilityOptions().then(() => {
                                                AlertService.removeAlert(updateAlertIdx);

                                                $state.go('vehicle-options');
                                            });
                                        });
                                    });
                                });
                            } else {
                                AlertService.removeAlert(updateAlertIdx);

                                $state.go(
                                    'summary',
                                    {},
                                    {
                                        reload: true
                                    }
                                );
                            }
                            LocationService.plotAll();
                            if (ctrl.task.isCompleted()) {
                                TaskAuditService.saveTaskViewAudits(
                                    CSHSearchViewerService.task() ? CSHSearchViewerService.task().id() : null,
                                    CSHSearchViewerService.customerRequest().id(),
                                    _userData
                                );
                            }
                        })
                        .catch(function onSetDefaultEntitlementError(err) {
                            AlertService.removeAlert(updateAlertIdx);
                            AlertService.createAlert('Failed to find default entitlement', AlertTypes.DANGER);
                        });
                    TaskService.isNewlyCreated(true);
                },
                isModifyDisabled: function isModifyDisabled() {
                    return true;
                    //TODO remove the line above & uncomment the following when modify tasks implemented
                    //add legacy logic here to determine if we can modify a task
                    //    var numberOfValidDays,
                    //        returnValue = true,
                    //        isBARCZJobType = false,
                    //        statusUpdateTime = new Date(CSHSearchViewerService.customerRequest().statusUpdatedTime());
                    //
                    //    //set numberOfValidDays - 5 unless 'B' type job, then 9
                    //    numberOfValidDays = (ctrl.getTask().supplierJobType().code() === "B") ? 9 : 5;
                    //    //check for sup job type
                    //    isBARCZJobType = ctrl.getBARCZJobType();
                    //
                    //    if (RoleMaskService.isAllowModify() && ctrl.getTask().createReason().serviceType() != 'ADMIN') {
                    //        if (!ctrl.getTask().isCompleted()) {
                    //            returnValue = !isBARCZJobType;
                    //        } else {
                    //            //logic for comp tasks
                    //            if (ctrl.getTask().supplierJobType().code() === "B") {
                    //                if (ctrl.spanNumberDays(statusUpdateTime, numberOfValidDays, false)) {
                    //                    returnValue = false;
                    //                }
                    //            } else {
                    //                returnValue = !ctrl.spanNumberDays(statusUpdateTime, numberOfValidDays, true);
                    //            }
                    //        }
                    //    }
                    //    return returnValue;
                    //},
                    //getBARCZJobType: function getBARCZJobType() {
                    //    return (ctrl.getTask().supplierJobType() === "B" || ctrl.getTask().supplierJobType() === "A" ||
                    //    ctrl.getTask().supplierJobType() === "R" || ctrl.getTask().supplierJobType() === "C" ||
                    //    ctrl.getTask().supplierJobType() === "Z")
                    //},
                    //spanNumberDays: function spanNumberDays(statusUpdateTime, numberOfValidDays, onlyWeekDays) {
                    //    var count = 0,
                    //        dayOfWeek,
                    //        currentDate = new Date();
                    //    while (statusUpdateTime <= currentDate) {
                    //        var dayOfWeek = statusUpdateTime.getDay();
                    //        if (onlyWeekDays) {
                    //            if(!((dayOfWeek == 6) || (dayOfWeek == 0)))
                    //                count++;
                    //        } else {
                    //            count++;
                    //        }
                    //        statusUpdateTime.setDate(statusUpdateTime.getDate() + 1);
                    //    }
                    //    return count <= numberOfValidDays;
                },
                canReattend: function canReattend() {
                    return ctrl.reattendReasons().length > 1 && ctrl.getTask().status() === Task.COMP_STATUS && !ctrl.isTaskCompletionPresent();
                },
                reattendOnOneReason: function reattendOnOneReason() {
                    return ctrl.reattendReasons().length === 1 && ctrl.getTask().status() === Task.COMP_STATUS && !ctrl.isTaskCompletionPresent();
                },
                enableAddTaskStatuses: () => {
                    return [
                        Task.INIT_STATUS,
                        Task.CHCK_STATUS,
                        Task.PLAN_STATUS,
                        Task.PACK_STATUS,
                        Task.HEAD_STATUS,
                        Task.HIRE_STATUS,
                        Task.GDET_STATUS,
                        Task.GARR_STATUS,
                        Task.TIDY_STATUS,
                        Task.WRAP_STATUS,
                        Task.LOAD_STATUS,
                        Task.UNLD_STATUS
                    ];
                },

                enableAddTask: () => {
                    const task = ctrl.getTask();
                    const taskCreateReason = task.createReason();
                    return (
                        ctrl.enableAddTaskStatuses().concat([Task.COMP_STATUS, Task.CLSD_STATUS]).indexOf(task.status()) > -1 &&
                        ctrl.getTask().taskType().code() === 'BRK' &&
                        ctrl.getTask().status() !== Task.UNAC_STATUS &&
                        taskCreateReason &&
                        ![CreateReason.HOTEL, CreateReason.TRANSPORT].includes(taskCreateReason.id())
                    );
                },
                toggleAddTask: () => {
                    return (ctrl.enableAddTask() && ctrl.reopenReasons().length > 0) || false;
                },
                updateButtonText: function updateButtonText() {
                    if (ctrl.task.isCompleted()) {
                        return 'View';
                    }
                    return 'Update';
                },
                canModifyTask: function canModifyTask() {
                    return ctrl.task.isCompleted();
                },

                showDriverDetails: function showDriverDetails() {
                    if (ctrl.task.entitlement().customerGroup().isFleet() || ctrl.task.entitlement().customerGroup().isPFU()) {
                        return true;
                    }
                    return false;
                },
                getSpecialInstructions: function (entitlement) {
                    var triplets = [],
                        mscFieldsJson = ctrl.getTask().createReason().isHireCar() ? [] : ctrl.getTask().miscFields().toJSON();

                    if (entitlement !== null) {
                        _.forEach(entitlement.variableData(), function (triplet) {
                            if (!_.isEmpty(triplet.flexFieldFormat.label) || !_.isEmpty(triplet.flexFieldData.value)) {
                                triplets.push(triplet.flexFieldFormat.label + ' : ' + triplet.flexFieldData.value);
                            }
                        });
                    }

                    _.forEach(mscFieldsJson, function (item, idx) {
                        if (_.isString(item) || _.isBoolean(item)) {
                            triplets.push(idx + ' : ' + item);
                        } else if (_.isDate(item)) {
                            triplets.push(idx + ' : ' + item.toLocaleDateString('en-GB'));
                        }
                    });
                    return $sce.trustAsHtml(triplets.join('<br/>'));
                },
                getDateInGBFormat: function (date) {
                    return TaskHelper.getDateInENGBFormat(date);
                },
                getResourceName: function getResourceName() {
                    var resource = ctrl.task.schedule().resource();

                    return resource.callSign() ? resource.callSign() + ' - ' + resource.name() : resource.name();
                },
                outcomeDescription: function outcomeDescription() {
                    var outcome;
                    if (ctrl.task.fault().outcome().component().name()) {
                        outcome = ctrl.task.fault().outcome().component().name();
                    }
                    if (ctrl.task.fault().outcome().completionFault().name()) {
                        if (outcome) {
                            outcome += ', ' + ctrl.task.fault().outcome().completionFault().name();
                        } else {
                            outcome = ctrl.task.fault().outcome().completionFault().name();
                        }
                    }
                    if (ctrl.task.fault().outcome().completion().name()) {
                        if (outcome) {
                            outcome += ', ' + ctrl.task.fault().outcome().completion().name();
                        } else {
                            outcome = ctrl.task.fault().outcome().completion().name();
                        }
                    }
                    return outcome;
                },
                showAudit: function showAudit() {
                    TaskAuditService.showAudit();
                },
                showDiagnosticsQA: function showDiagnosticsQA() {
                    CSHSearchViewerService.hideAuditAndTextTopicsPanel();
                    TaskAuditService.task(null); //clear selection
                    TaskAuditService.customerRequest(null);
                    TextTopicService.task(null);
                    TextTopicService.customerRequest(null);
                    CSHSearchViewerService.qaSelected(false);
                    TextTopicService.showTextTopicsPanel(false);

                    TextTopicService.task(TaskService.task());
                    TaskAuditService.customerRequest(CSHService.csh());
                    ctrl.isPersonal() ? CSHSearchViewerService.qaSelected(true) : CSHSearchViewerService.showTextTopicsSelected();
                    TextTopicService.showTextTopicsPanel(true);
                },
                isPersonal: function isPersonal() {
                    return ctrl.getTask() ? ctrl.getTask().entitlement().customerGroup().isPersonal() : true;
                },
                showDocuments: function showDocuments() {
                    ElectronicDocumentsService.listByTaskId(ctrl.task.id()).then(function () {
                        $state.go('electronic-documents');
                    });
                },
                showCrPartsHistory: function showCrPartsHistory() {
                    $state.go('cr-parts-history');
                },
                showHireExtAudit: function showHireExtAudit() {
                    $state.go('ext-hire-audit', { hireAuditId: ctrl.task.id() });
                },
                isCUVEnabled: () => {
                    // for now show only for personal
                    return ctrl.isPersonal();
                },

                isHireTask: () => {
                    return ctrl.task.createReason().isHireCar() && [Task.CLSD_STATUS, Task.COMP_STATUS, Task.GARR_STATUS, Task.HIRE_STATUS].includes(ctrl.task.status());
                },

                showCuv: function showCuv() {
                    $state.go('cuv');
                },
                checkFailedCoolingOff: function checkFailedCoolingOff() {
                    if (ctrl.getTask().contractValidation()) {
                        return !!ctrl.getTask().contractValidation().coolingOff().product() && ctrl.getTask().contractValidation().failedCoolingOff();
                    }
                    return false;
                },
                ngModelOptions: {
                    getterSetter: true
                },
                isNil: (val) => {
                    return _.isNil(val);
                },
                enableSwapCarButton: () => {
                    if (ctrl.getTask().rental && TaskService.task().rental) {
                        let selectedTaskHireCase = TaskService.task().rental().hireVehicle().regNo();
                        let curruntTaskHireCase = ctrl.getTask().rental().hireVehicle().regNo();
                        return (
                            (ctrl.getTask().status() == Task.HIRE_STATUS || ctrl.getTask().status() == Task.COMP_STATUS || ctrl.getTask().status() == Task.GARR_STATUS) &&
                            selectedTaskHireCase == curruntTaskHireCase
                        );
                    }
                    return false;
                },
                swapCar: function swapCar() {
                    ctrl.reopen(CreateReasonsService.getReasonByParentTaskId());
                },
                setCapabilitiesOnReattend: (repairId) => {
                    DiagnosticsService.getCapabilities(repairId).then(function getCapabilitiesSuccess(capabilities) {
                        TaskService.task().fault().capabilities(capabilities);
                    });
                }
            });
        }
    ]);
