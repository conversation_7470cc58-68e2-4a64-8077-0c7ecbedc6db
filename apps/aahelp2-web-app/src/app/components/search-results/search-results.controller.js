var _ = require('lodash'),
    FormattingUtil = require('@aa/utilities/formatting'),
    Task = require('@aa/malstrom-models/lib/task.model'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model'),
    PartsFitted = require('@aa/malstrom-models/lib/parts-fitted.model');
const Moment = require('moment');

require('angular');

module.exports = angular
    .module('aah-search-results-controller-module', [
        //services
        require('../../services/search/search.service').name,
        require('../../services/vehicle/vehicle.service').name,
        require('../../services/task/task.service').name,
        require('../../services/device-detector/device-detector.service').name,
        require('../../services/entitlement/entitlement-helper.service').name,
        require('../../constants/task/task-type.constants').name,
        require('../../filters/no-snake.filter').name,
        require('../../services/supplier/supplier.service').name,
        require('../../services/mobility-task/mobility-task.service').name,
        require('../../services/csh/csh.service').name,
        require('../../services/appointment/appointment.service').name,
        require('../../services/service-type/service-type.service').name,
        require('../../services/create-reasons/create-reasons.service').name,
        require('../../services/prompt/prompt.service').name,
        require('../../helpers/task/task.helper').name,
        require('../../services/mapping/mapping.service').name,
        require('../../services/mapping/context-menu/context-menu.service').name,
        require('../../services/will-join/will-join.service').name,
        require('../../services/dangerous-location/dangerous-location.service').name,
        require('../../services/parts-history/parts-history.service').name,
        require('../../constants/eligibility/eligibility-roles.constants').name
    ])
    .controller('aahSearchResultsController', [
        '$timeout',
        '$state',
        'aahSearchService',
        'aahVehicleService',
        'aahTaskTypeConstants',
        'aahTaskService',
        'aahDeviceDetectorService',
        'aahEntitlementHelperService',
        'aahSupplierService',
        'aahMobilityTaskService',
        'aahCSHService',
        'aahAppointmentService',
        'aahServiceTypeService',
        'aahPromptService',
        'aahTaskHelper',
        'aahContextMenuService',
        'aahWillJoinService',
        'aahDangerousLocationService',
        'aahPartsHistoryService',
        'aahEligibilityRoles',
        'aahCreateReasonsService',
        function SearchResultsController(
            $timeout,
            $state,
            SearchService,
            VehicleService,
            TaskType,
            TaskService,
            DeviceDetectorService,
            EntitlementHelperService,
            SupplierService,
            MobilityTaskService,
            CshService,
            AppointmentService,
            ServiceTypeService,
            PromptService,
            TaskHelper,
            ContextMenuService,
            WillJoinService,
            DangerousLocationService,
            PartsHistoryService,
            aahEligibilityRoles,
            CreateReasonsService
        ) {
            /*
             results is passed in on the scope and is the entitlements from the searchService
             */

            var ctrl = this,
                _result = false,
                _selectedResult = null,
                _showEntitlementsForCompany = false,
                _registrationNumber,
                _vehicleMake,
                _vehicleModel,
                _partsFitted,
                _debounce,
                _repairLocationSelected,
                _returnLocationSelected;

            const roleOrder = [
                aahEligibilityRoles.MEMBER_ROLE_MAIN_MEMBER,
                aahEligibilityRoles.MEMBER_ROLE_1ST_ASSOC,
                aahEligibilityRoles.MEMBER_ROLE_2ND_ASSOC,
                aahEligibilityRoles.MEMBER_ROLE_3RD_ASSOC,
                aahEligibilityRoles.PARTNER,
                aahEligibilityRoles.AUTHORISED_DRIVER,
                aahEligibilityRoles.FAMILY_COVER
            ];

            _.extend(ctrl, {
                roleOrder,
                result: function resultAcessor(val, delay) {
                    if (arguments.length && delay && _result) {
                        $timeout.cancel(_debounce);

                        _debounce = $timeout(function () {
                            _result = val;
                        }, 75);
                    } else if (arguments.length) {
                        _result = val;
                    }

                    return _result;
                },
                getServiceTypeNameLabel: function getServiceTypeNameLabel(serviceTypeName) {
                    return ServiceTypeService.getServiceTypeNameLabel(serviceTypeName);
                },
                clearDelay: function clearDelay() {
                    $timeout.cancel(_debounce);
                },
                selectedResult: function selectedResultAcessor(val) {
                    return arguments.length ? (_selectedResult = val) : _selectedResult;
                },
                showResults: function showResults() {
                    return ctrl.results.length > 0;
                },
                autoPopulate: function autoPopulate() {
                    if (ctrl.results.length > 0) {
                        ctrl.result(ctrl.results[0]);
                    }
                },
                select: function select(result, index, goto, event) {
                    TaskService.isNewlyCreated(false);
                    if (event) {
                        event.stopPropagation();
                    }
                    if (result instanceof Task) {
                        CreateReasonsService.fetchReopenReattendReasons(result);
                    }
                    _selectedResult = result;
                    AppointmentService.reset();
                    ctrl.selectResult(result, index, goto, event);
                    WillJoinService.checkWillJoinTask(result);
                    DangerousLocationService.checkDangerousLocation(result);
                    if (!CshService.csh().isActive()) {
                        PromptService.checkTaskIscompletedWithinSevenDays(CshService.cshSearchResponse());
                    }
                    // check result is instanceof Task
                    if (result instanceof Task) {
                        PromptService.checkNoServiceAccount();
                    }
                },
                resultSelected: function resultSelected(result) {
                    if (DeviceDetectorService.isMobile()) {
                        //update current result item for mobile devices since mouseover is not fired
                        ctrl.result(result);
                    } else {
                        ctrl.loadTaskFunction(result);
                    }
                },
                loadTask: function loadTask() {
                    ctrl.loadTaskFunction(ctrl.result());
                },
                isMobile: function isMobile() {
                    return DeviceDetectorService.isMobile();
                },
                secondLevelValidationTypes: function secondLevelValidationTypes() {
                    return SearchService.secondLevelValidationSecondLevelValidationType();
                },
                showValidationTypes: function showValidationTypes(companyId) {
                    if (_selectedResult && companyId === _selectedResult.id()) {
                        return true;
                    }
                    return false;
                },
                getEntitlementsForCompanyAndSlvType: function getEntitlementsForCompanyAndSlvType(slvType) {
                    SearchService.secondLevelValidationCompanyAndSlvTypeSearch(_selectedResult.id(), slvType);
                    _showEntitlementsForCompany = true;
                },
                showEntitlementsForCompany: function showEntitlementsForCompany() {
                    return _showEntitlementsForCompany;
                },
                vehicleResults: function vehicleResults() {
                    return SearchService.secondLevelValidationGetCompanySlvResults();
                },
                membershipNumber: function membershipNumber() {
                    if (ctrl.result()) {
                        return ctrl.result().policy().customerGroup().isBank() ? FormattingUtil.maskString(ctrl.result().policy().membershipNumber(), 4) : ctrl.result().policy().membershipNumber();
                    }

                    return null;
                },
                ngModelOptions: {
                    getterSetter: true
                },
                registrationNumber: function registrationNumberAccessor(val) {
                    return arguments.length ? (_registrationNumber = val.toUpperCase()) : _registrationNumber;
                },
                filterByRegistration: function filterByEventFunction(element) {
                    if (_registrationNumber) {
                        return element.vehicle().registration().toUpperCase().indexOf(_registrationNumber) > -1;
                    }
                    return true;
                },
                viewTask: function viewTask(task) {
                    PartsHistoryService.partsFittedSummaryHistory(task.id());
                    if (!task.open && !task.isAdHoc()) {
                        //task is being opened, so load additional task data
                        PartsHistoryService.partsFitted(task.id()).then(function part(parts) {
                            _partsFitted = parts;
                        });
                        VehicleService.getMakesByType(task.vehicle().typeId()).then(function getMakesByTypeSuccess(makes) {
                            _vehicleMake = VehicleService.getMakeByID(task.vehicle().makeId(), makes);
                        });
                        VehicleService.getModels(task.vehicle().makeId()).then(function getModelsSuccess(models) {
                            _vehicleModel = VehicleService.getModelByID(task.vehicle().modelId(), models);
                        });
                    }

                    ctrl.select(task);
                },
                showDriveIn: (task) => {
                    if (task.createReason().isBreakdown() && task.fault().outcome().completionCode() === '71') {
                        // Cancellation code 71 -> "WARRANTY DRIVE IN (JAGUAR/LAND ROVER)"
                        return true;
                    } else {
                        return false;
                    }
                },
                showGoodWill: (task) => {
                    //Goodwill is restricted for Hyundai so MobilityTaskService.getBusinessRules().agileEnabled cant be replaced here
                    if (task.createReason().isBreakdown() && task.entitlement().customerGroup().isHyundai() && task.fault().outcome().completionCode() === 'XD') {
                        return true;
                    } else {
                        return false;
                    }
                },
                vehicleMake: function vehicleMakeAccessor() {
                    return _vehicleMake;
                },
                vehicleModel: function vehicleModelAccessor() {
                    return _vehicleModel;
                },
                partsFitted: function part(task) {
                    if (_partsFitted === undefined && task.open && sessionStorage.getItem(task.id())) {
                        let _partsFittedData = [];
                        let _partsFittedParse = JSON.parse(sessionStorage.getItem(task.id()));
                        _.forEach(_partsFittedParse, function (part) {
                            _partsFittedData.push(new PartsFitted(part));
                        });
                        _partsFitted = _partsFittedData;
                    }
                    return _partsFitted;
                },
                isAnAdhocTask: function isAnAdhocTask(task) {
                    return task.taskType().code() !== TaskType.BREAKDOWN && task.taskType().code() !== TaskType.ADMINISTRATION;
                },
                isFLPTask: function isFLPTask(task) {
                    return task.taskType().code() === TaskType.FRONT_LINE_POLICY;
                },
                sortByMemberRole: (entitlementA, entitlementB) => {
                    const roleA = entitlementA.value.contact().role() || '';
                    const roleB = entitlementB.value.contact().role() || '';
                    const indexA = roleOrder.findIndex((val) => val.toLowerCase() === roleA.toLowerCase());
                    const indexB = roleOrder.findIndex((val) => val.toLowerCase() === roleB.toLowerCase());

                    return indexA < indexB ? -1 : 1;
                },
                entitlementBasedCssClass: function entitlementBasedCssClass(searchResult) {
                    var _isExpired;

                    if (searchResult.isEuropeanBreakdownCover()) {
                        return 'european-breakdown-cover';
                    }
                    //check for an expired manufacturer - and display in grey
                    _isExpired = EntitlementHelperService.isExpiredPolicy(searchResult);

                    // I really dont like this one bit!!
                    if (searchResult.policy().customerGroup().name()) {
                        //Added this if condition because this code is failing when user loads a will join task and try to search for an entitlement against that task
                        return _isExpired ? 'manufacturer-expired' : searchResult.policy().customerGroup().name().toLowerCase().split(' ').join('-');
                    }
                },
                routeIsLocationDetails: () => $state.current.name === 'locationDetails',
                getMainContactPhoneNumber: (supplierDetails) => {
                    return SupplierService.getMainContactPhoneNumber(supplierDetails);
                },
                thirdPartySupplier: () => {
                    return MobilityTaskService.isThirdParty() || MobilityTaskService.isThirdPartyThrifty();
                },
                products: () => {
                    return ctrl.showResults() && ctrl.result() ? CshService.getProducts(ctrl.result().products()) : '';
                },
                isNaN: (val) => isNaN(val),
                hideRepairCheckbox: (val) => {
                    return ctrl.isNaN(val) || /^\d$/.test(val);
                },
                isEnterprise: () => {
                    return MobilityTaskService.isEnterprise();
                },
                isW3WSearch: function isW3WSearch() {
                    return SearchService.isW3WSearch();
                },
                isinvalidW3search: function isinvalidW3search() {
                    return SearchService.isinvalidW3search();
                },
                checkInvalidSearch: function checkInvalidSearch() {
                    return /^[\/]{2}(\w+)/.test(SearchService.locationSearch().search());
                },
                swapCarTask: (taskId) => {
                    return TaskService.swapCarTasks().find((i) => i.TASK_ID === taskId);
                }
            });
        }
    ]);
