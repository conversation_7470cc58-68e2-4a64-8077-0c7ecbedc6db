var _ = require('lodash');

require('angular');

module.exports = angular.module('aah-address-lookup-controller-module', [require('../../services/address-lookup/address-lookup.service').name]).controller('aahJlrAddressLookupController', [
    'aahAddressLookupService',

    function JlrAddressLookupController(AddressLookupService) {
        var ctrl,
            _disableSelect = true,
            _searchPostcode;

        ctrl = this;

        function _isDisableSelect() {
            return AddressLookupService.isDisableSelect();
        }

        _.extend(ctrl, {
            searchPostcode: function searchPostcode(val) {
                return arguments.length ? (_searchPostcode = ctrl.postcodeModel) : _searchPostcode;
            },
            searchForAddresses: function searchForAddresses() {
                AddressLookupService.search(ctrl.postcodeModel()).then(function () {
                    if (AddressLookupService.addresses().length > 0) {
                        AddressLookupService.isDisableSelect(false);
                    } else {
                        return false;
                    }
                });
            },
            isDisableSelect: _isDisableSelect,
            noResult: () => {
                return _noResult;
            },

            hideList: () => {
                return _disableSelect;
            },
            addresses: function addresses() {
                return AddressLookupService.addresses();
            },
            postCodePattern: (function () {
                return AddressLookupService.postCodePatternService();
            })(),
            modelOptions: {
                getterSetter: true
            }
        });
    }
]);
