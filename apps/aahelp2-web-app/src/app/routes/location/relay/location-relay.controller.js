var _ = require('lodash'),
    ui_router = require('angular-ui-router');
const CreateReason = require('@aa/malstrom-models/lib/create-reason.model');
const Prompt = require('@aa/malstrom-models/lib/prompt.model');
const promptService = require('../../../services/prompt/prompt.service');
const { type } = require('os');
require('angular');

module.exports = angular
    .module('aah-relay-controller-module', [
        ui_router,

        // directives
        require('../../../components/diagnostics/diagnostics.directive').name,

        // services
        require('../../../services/task/task.service').name,
        require('../../../services/ui/ui.service').name,
        require('../../../services/text-topic/text-topic.service').name,
        require('../../../services/recovery/recovery.service').name,
        require('../../../services/diagnostics/diagnostics.service').name,
        require('../../../services/mapping/location/location.service').name,
        require('../../../constants/recovery/dest-resource-ids.constants').name
    ])
    .controller('aahRelayController', [
        '$scope',
        '$state',
        'aahTaskService',
        'aahUIService',
        'aahRecoveryService',
        'aahDiagnosticsService',
        'aahTextTopicService',
        'aahLocationService',
        'aahDestResourceIDsConstants',
        'aahCSHService',
        'aahPromptService',
        function RelayController($scope, $state, TaskService, UIService, RecoveryService, DiagnosticsService, TextTopicService, LocationService, DestResourceIDsConstants, CSHService, PromptService) {
            var ctrl = this,
                _recovery,
                _unaccompanied = TaskService.task().recovery().unaccompanied();

            function initialiseRecoveryMethod() {
                if (TaskService.task().recovery().fault().id() !== -1) {
                    RecoveryService.selectedForcedRecoveryFault(TaskService.task().recovery().fault());
                }
            }

            _.extend(ctrl, {
                DestResourceIDsConstants,
                destResourceId: (...args) => {
                    if (args.length) {
                        const texts = {
                            '-6': 'LOCAL: STORAGE',
                            '-9': 'LOCAL: REPAIR'
                        };
                        if (texts[args[0]]) {
                            ctrl.location().text(texts[args[0]]);
                        }
                        TaskService.task()
                            .recovery()
                            .destResourceId(parseInt(args[0]) || undefined);
                    } else {
                        const destResourceId = TaskService.task().recovery().destResourceId() || '';
                        return `${destResourceId}`;
                    }
                },
                initialise: () => {
                    const task = TaskService.task();
                    _recovery = task.recovery().relay();
                    initialiseRecoveryMethod();
                    TextTopicService.showTextTopicsForCategories(['REL_DATA']);
                    if (!task.entitlement().customerGroup().isPersonal()) {
                        RecoveryService.showQA(true);
                    }
                    if (!task.isNew() && (task.recovery().destResourceId() > 0 || task.recovery().destResourceId() === DestResourceIDsConstants.REPAIRER)) {
                        TaskService.attendedScheduleWindow(true);
                    }
                    if (task.createReason().id() === CreateReason.AA_LOGISTICS) {
                        _unaccompanied = true;
                        ctrl.unaccompaniedToggle();
                    }

                    //we should only set the authorised indicator to false if the task is new
                    //lets count the number of tasks in the CSH. If there is only one task, then we can assume that it is the new task
                    const CSHTaskCount = CSHService.csh().tasks().length;
                    const isNewTask = CSHTaskCount === 1 && task.isNew();

                    if (isNewTask && task.recovery().forceRecyFault()) {
                        task.indicators().authorised(false);
                    }
                },
                location: () => {
                    return TaskService.task().recovery().destination();
                },
                isDisabled: () => {
                    return TaskService.task().uiStatus().relay();
                },
                towingToggle: () => {
                    if (TaskService.task().recovery().adults() === null) {
                        TaskService.task().recovery().adults(0);
                    }
                    DiagnosticsService.initialiseDiagnosticsQA();
                },
                shouldShowTowingQuestion: () => TaskService.task().recovery().passengerRun() || TaskService.task().createReason().isRelayLeg() || TaskService.task().createReason().isSupportTask(),

                unaccompaniedToggle: () => {
                    if (_unaccompanied) {
                        TaskService.task().recovery().adults(0);
                        TaskService.task().recovery().children(0);
                        TaskService.task().recovery().dogs(0);
                        TaskService.task().recovery().unaccompaniedAfterLoading(false);
                    } else {
                        TaskService.task().recovery().adults(1);
                        TaskService.task().recovery().children(0);
                        TaskService.task().recovery().dogs(0);
                    }
                    DiagnosticsService.initialiseDiagnosticsQA();
                },
                memberWaitingWithVehicleToggle: () => {
                    if (TaskService.task().recovery().unaccompaniedAfterLoading()) {
                        _unaccompanied = false;
                    }
                    TaskService.task().recovery().adults(1);
                    TaskService.task().recovery().children(0);
                    TaskService.task().recovery().dogs(0);
                    DiagnosticsService.initialiseDiagnosticsQA();
                },
                unaccompanied: function unaccompaniedAccessor(val) {
                    return arguments.length ? (_unaccompanied = val) : _unaccompanied;
                },
                adults: function adultsAccessor(val) {
                    if (arguments.length && val !== null) {
                        TaskService.task().recovery().adults(val);
                    } else {
                        return TaskService.task().recovery().adults() < 0 ? null : TaskService.task().recovery().adults();
                    }
                },
                children: function childrenAccessor(val) {
                    if (arguments.length && val !== null) {
                        TaskService.task().recovery().children(val);
                    } else {
                        return TaskService.task().recovery().children() < 0 ? null : TaskService.task().recovery().children();
                    }
                },
                dogs: function dogsAccessor(val) {
                    if (arguments.length && val !== null) {
                        TaskService.task().recovery().dogs(val);
                    } else {
                        return TaskService.task().recovery().dogs() < 0 ? null : TaskService.task().recovery().dogs();
                    }
                },
                isRelay: function isRelay() {
                    return $state.is('location.relay');
                },
                task: function taskAcessor() {
                    return TaskService.task();
                },
                showInfo: function showInfo(val) {
                    return arguments.length ? UIService.showRelayInfo(val) : UIService.showRelayInfo();
                },

                passengerCountChanged: () => {
                    DiagnosticsService.initialiseDiagnosticsQA();
                },
                showQA: function showQAAccessor(val) {
                    return arguments.length ? RecoveryService.showQA(val) : RecoveryService.showQA();
                },

                recovery: function recoveryAccessor(val) {
                    return arguments.length ? (_recovery = val) : _recovery;
                },
                toggleRecovery: () => {
                    if (!_recovery) {
                        TaskService.clearRecovery();
                        LocationService.removeRelayMarker();
                        $state.go('summary');
                    }
                },
                isDestinationSet: () => {
                    return TaskService.task().recovery().destination().isSet();
                },
                modelOptions: {
                    getterSetter: true,
                    updateOn: 'default keyup',
                    debounce: {
                        default: 500,
                        keyup: 0
                    },
                    allowInvalid: false
                },
                getState: function getState() {
                    return $state.current.name;
                },
                isLocalDestResource: () => TaskService.task().recovery().destResourceId() === DestResourceIDsConstants.LOCAL
            });
        }
    ]);
