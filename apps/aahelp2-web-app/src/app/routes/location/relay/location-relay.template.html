<div
    id="relay"
    class="main-panel shadow general-scrollbar"
    ng-class="{'collapse-panel':!ctrl.showInfo()}"
    ng-if="ctrl.isDestinationSet()"
    ng-init="ctrl.initialise()"
>
    <form name="relayForm">
        <div class="more-btn-wrap">
            <div
                class="more-btn"
                ng-class="{'icon-cross':!ctrl.showInfo(), 'icon-remove':ctrl.showInfo()}"
                ng-click="ctrl.showInfo(!ctrl.showInfo())"
            ></div>
        </div>
        <!-- Relay To Section -->
        <div ng-show="ctrl.showInfo()">
            <div class="section">
                <div class="section-header-relay clear">
                    <div class="col-lg-6 col-sm-6 col-xs-6">
                        <div ui-view="relayLabel"></div>
                    </div>
                </div>
                <div class="section-body-relay">
                    <div class="col-lg-6 col-sm-6 col-xs-6">
                        <label
                            for="relay-destination-location"
                            class="label-primary"
                            >Destination location</label
                        >
                        <dg-validation
                            position="bottom"
                            model="relayForm.location"
                        >
                            <input
                                class="form-field form-control"
                                id="relay-destination-location"
                                required
                                dg-validation-trigger
                                name="location"
                                ng-model="ctrl.location().text"
                                ng-maxlength="75"
                                maxlength="75"
                                ng-disabled="ctrl.isDisabled() || ctrl.isLocalDestResource()"
                                ng-model-options="ctrl.modelOptions"
                            />
                        </dg-validation>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-xs-6">
                        <label
                            for="relay-area"
                            class="label-primary"
                            >Area/Postcode</label
                        >
                        <dg-validation
                            model="relayForm.area"
                            position="bottom"
                        >
                            <input
                                class="form-field form-control"
                                id="relay-area"
                                name="area"
                                dg-validation-trigger
                                required
                                ng-model="ctrl.location().area"
                                ng-maxlength="75"
                                maxlength="75"
                                ng-disabled="ctrl.isDisabled() || ctrl.isLocalDestResource()"
                                ng-model-options="ctrl.modelOptions"
                            />
                        </dg-validation>
                    </div>
                    <div class="col-lg-2 col-sm-2 col-xs-2">
                        <label
                            for="adults"
                            class="label-primary"
                            >Adults</label
                        >
                        <dg-validation
                            model="relayForm.adults"
                            position="bottom"
                        >
                            <input
                                class="form-field form-control"
                                dg-validation-trigger
                                required
                                id="adults"
                                name="adults"
                                type="number"
                                max="99"
                                min="0"
                                ng-model="ctrl.adults"
                                ng-disabled="ctrl.isDisabled() || ctrl.task().recovery().unaccompaniedAfterLoading()"
                                ng-model-options="ctrl.modelOptions"
                                ng-change="ctrl.passengerCountChanged()"
                            />
                        </dg-validation>
                    </div>
                    <div class="col-lg-2 col-sm-2 col-xs-2">
                        <label
                            for="children"
                            class="label-primary"
                            >Children</label
                        >
                        <dg-validation
                            model="relayForm.children"
                            position="bottom"
                        >
                            <input
                                class="form-field form-control"
                                id="children"
                                name="children"
                                type="number"
                                max="99"
                                min="0"
                                ng-model="ctrl.children"
                                ng-disabled="ctrl.isDisabled() || ctrl.task().recovery().unaccompaniedAfterLoading()"
                                ng-model-options="ctrl.modelOptions"
                                ng-change="ctrl.passengerCountChanged()"
                            />
                        </dg-validation>
                    </div>
                    <div class="col-lg-2 col-sm-2 col-xs-2">
                        <label
                            for="dogs"
                            class="label-primary"
                            >Dogs</label
                        >
                        <dg-validation
                            model="relayForm.dogs"
                            position="bottom"
                        >
                            <input
                                class="form-field form-control"
                                id="dogs"
                                name="dogs"
                                type="number"
                                max="99"
                                min="0"
                                ng-model="ctrl.dogs"
                                ng-model-options="ctrl.modelOptions"
                                ng-disabled="ctrl.isDisabled() || ctrl.task().recovery().unaccompaniedAfterLoading()"
                                ng-change="ctrl.passengerCountChanged()"
                            />
                        </dg-validation>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-xs-6">
                        <label
                            for="location-of-keys"
                            class="label-primary"
                            >Location of keys</label
                        >
                        <input
                            class="form-field form-control"
                            id="location-of-keys"
                            ng-model="ctrl.task().recovery().keysLocation"
                            ng-maxlength="50"
                            maxlength="50"
                            ng-disabled="ctrl.isDisabled()"
                            ng-model-options="ctrl.modelOptions"
                        />
                    </div>
                    <div class="col-lg-6 col-sm-6 col-xs-6">
                        <label
                            for="relay-remarks"
                            class="label-primary"
                            >Relay remarks</label
                        >
                        <textarea
                            class="form-textarea form-control"
                            id="relay-remarks"
                            ng-model="ctrl.task().recovery().destination().remarks"
                            ng-maxlength="250"
                            maxlength="250"
                            ng-disabled="ctrl.isDisabled()"
                            ng-model-options="ctrl.modelOptions"
                        ></textarea>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-xs-6 mt-2 text-center">
                        <div
                            ui-view="cancellationLabel"
                            class="panel panel-info"
                        ></div>
                    </div>
                    <div class="clear"></div>
                    <div ui-view="recoveryMethod"></div>

                    <div class="col-lg-6 col-sm-6 col-xs-6">
                        <label
                            for="recovery-distance"
                            class="label-primary"
                            >Recovery distance</label
                        >
                        <input
                            class="form-field form-control"
                            id="recovery-distance"
                            ng-model="ctrl.task().eurohelp().recovery().distance()"
                            ng-disabled="ctrl.isDisabled()"
                            ng-model-options="ctrl.modelOptions"
                            ng-readonly="true"
                        />
                    </div>
                    <div class="col-lg-12 col-sm-12 col-xs-12">
                        <div class="row">
                            <div class="col-lg-6 col-sm-6 col-xs-6">
                                <div
                                    class="radio-wrap"
                                    ng-hide="ctrl.shouldShowTowingQuestion()"
                                >
                                    <input
                                        type="checkbox"
                                        name="towing"
                                        id="towing"
                                        ng-change="ctrl.towingToggle()"
                                        ng-model="ctrl.task().recovery().isTowing"
                                        class="ng-pristine ng-untouched ng-valid ng-empty"
                                        ng-model-options="ctrl.modelOptions"
                                    />
                                    <label
                                        for="towing"
                                        class="radio-label"
                                    >
                                        <span class="dot-outer">
                                            <span class="icon-tick"></span>
                                        </span>
                                        Are you towing anything ?</label
                                    >
                                </div>
                                <div class="radio-wrap">
                                    <input
                                        type="checkbox"
                                        name="waiting"
                                        id="member-waiting"
                                        ng-change="ctrl.memberWaitingWithVehicleToggle()"
                                        ng-model="ctrl.task().recovery().unaccompaniedAfterLoading"
                                        ng-disabled="ctrl.isDisabled()"
                                        ng-model-options="ctrl.modelOptions"
                                    />
                                    <label
                                        for="member-waiting"
                                        class="radio-label"
                                    >
                                        <span class="dot-outer">
                                            <span class="icon-tick"></span>
                                        </span>
                                        Driver waiting at vehicle with keys, not travelling with vehicle
                                    </label>
                                </div>
                                <div class="radio-wrap">
                                    <input
                                        type="checkbox"
                                        name="waiting"
                                        id="nobody-waiting"
                                        ng-change="ctrl.unaccompaniedToggle()"
                                        ng-disabled="ctrl.isDisabled()"
                                        ng-model="ctrl.unaccompanied"
                                        ng-model-options="ctrl.modelOptions"
                                    />
                                    <label
                                        for="nobody-waiting"
                                        class="radio-label"
                                    >
                                        <span class="dot-outer">
                                            <span class="icon-tick"></span>
                                        </span>
                                        Nobody waiting with vehicle
                                    </label>
                                </div>
                                <div
                                    class="warning-msg col-sm-12 col-lg-12"
                                    ng-if="ctrl.unaccompanied()"
                                >
                                    Ensure you have advised member the unattended recovery can take up to 48 hours to complete and there is a potential for the vehicle to be left unattended in a
                                    different location if the recovery requires to be legged.
                                </div>
                            </div>
                            <div class="col-lg-3 col-sm-3 col-xs-3">
                                <div class="radio-wrap">
                                    <input
                                        type="checkbox"
                                        id="relay-recovery"
                                        ng-model="ctrl.recovery"
                                        ng-change="ctrl.toggleRecovery()"
                                        ng-disabled="ctrl.isDisabled()"
                                        ng-model-options="ctrl.modelOptions"
                                    />
                                    <label
                                        for="relay-recovery"
                                        class="radio-label"
                                    >
                                        <span class="dot-outer"> <span class="icon-tick"></span> </span>Recovery</label
                                    >
                                </div>

                                <div class="radio-wrap">
                                    <input
                                        id="relay-authorised"
                                        type="checkbox"
                                        ng-disabled="ctrl.isDisabled()"
                                        ng-model="ctrl.task().indicators().authorised"
                                        ng-model-options="ctrl.modelOptions"
                                    />
                                    <!-- TODO - need to bind to something here - Jas to look in legacy -->
                                    <label
                                        for="relay-authorised"
                                        class="radio-label"
                                    >
                                        <span class="dot-outer"> <span class="icon-tick"></span> </span>Authorised</label
                                    >
                                </div>
                            </div>
                            <div class="col-lg-6 col-sm-6 col-xs-6">
                                <label
                                    for="destination-location-category"
                                    class="label-primary"
                                    >Destination Location Category</label
                                >

                                <select
                                    id="destination-location-category"
                                    class="form-select"
                                    ng-model="ctrl.destResourceId"
                                    ng-model-options="ctrl.modelOptions"
                                >
                                    <option
                                        class="audit-dropdown-text"
                                        value=""
                                    >
                                        ( choose id applicable )
                                    </option>
                                    <option
                                        class="audit-dropdown-text"
                                        value="{{ctrl.DestResourceIDsConstants.LOCAL_STORAGE}}"
                                    >
                                        Storage
                                    </option>
                                    <option
                                        class="audit-dropdown-text"
                                        value="{{ctrl.DestResourceIDsConstants.REPAIR}}"
                                    >
                                        Repair
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Relay To Diagnostics -->
            <div ui-view="diagnostics"></div>
        </div>
    </form>
</div>
