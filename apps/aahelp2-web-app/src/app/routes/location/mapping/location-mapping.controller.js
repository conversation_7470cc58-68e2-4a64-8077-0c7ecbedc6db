var _ = require('lodash'),
    Location = require('@aa/malstrom-models/lib/location.model'),
    ui_router = require('angular-ui-router');
const CustomerGroup = require('@aa/malstrom-models/lib/customer-group.model');

require('angular');
require('angular-hotkeys');
var Prompt = require('@aa/malstrom-models/lib/prompt.model');
module.exports = angular
    .module('aah-location-controller-module', [
        ui_router,
        'cfp.hotkeys',

        //factories
        require('../../../factories/safety-advice.factory').name,
        // services
        require('../../../services/task/task.service').name,
        require('../../../services/ui/ui.service').name,
        require('../../../services/mapping/mapping.service').name,
        require('../../../services/mapping/context-menu/context-menu.service').name,
        require('../../../services/text-topic/text-topic.service').name,
        require('../../../services/search/search.service').name,
        require('../../../services/tab/tab.service').name,
        require('../../../services/csh/csh.service').name,
        require('../../../services/mapping/location/location.service').name,
        // dialog
        require('../../../components/dialogs/safety-advice/safety-advice.controller').name,
        require('../../../services/prompt/prompt.service').name
    ])
    .controller('aahLocationController', [
        '$scope',
        '$timeout',
        '$state',
        '$window',
        'hotkeys',
        'aahSafetyAdvice',
        'aahTaskService',
        'aahUIService',
        'aahMappingService',
        'aahTextTopicService',
        'aahSearchService',
        'aahTabService',
        'aahLocationService',
        'aahContextMenuService',
        'aahPromptService',
        'aahCSHService',
        'aahDestResourceIDsConstants',
        function LocationController(
            $scope,
            $timeout,
            $state,
            $window,
            hotkeys,
            SafetyAdvice,
            TaskService,
            UIService,
            MappingService,
            TextTopicService,
            SearchService,
            TabService,
            LocationService,
            ContextMenuService,
            PromptService,
            CSHService,
            DestResourceIDsConstants
        ) {
            var ctrl = this,
                _junctionText = '',
                isMotorwayTick = false,
                isHomestartTick = false,
                isDangerousLocationTick = false,
                _areaIsTooBig = false;

            hotkeys.bindTo($scope).add({
                combo: 'alt+h',
                description: 'Homestart',
                allowIn: ['INPUT', 'SELECT', 'TEXTAREA'],
                callback: function () {
                    ContextMenuService.setAsHomeStart();
                }
            });

            _.extend(ctrl, {
                dotOptions: {
                    northBound: Location.DIRECTION_NORTH,
                    southBound: Location.DIRECTION_SOUTH,
                    eastBound: Location.DIRECTION_EAST,
                    westBound: Location.DIRECTION_WEST,
                    clockwise: Location.DIRECTION_CLOCKWISE,
                    antiClockwise: Location.DIRECTION_ANTI_CLOCKWISE
                },
                laneOptions: {
                    hardShoulder: Location.LANE_TYPE_HARD_SHOULDER,
                    laneOne: Location.LANE_TYPE_LANE_ONE,
                    centralLanes: Location.LANE_TYPE_CENTRAL_LANES,
                    outsideLane: Location.LANE_TYPE_OUTSIDE_LANE,
                    laybyOrRefugeArea: Location.LANE_TYPE_LAYBY_OR_REFUGEE_AREA
                },
                slipRoadOptions: {
                    slipEntry: Location.SLIP_ROAD_ENTRY,
                    slipExit: Location.SLIP_ROAD_EXIT,
                    linkRoad: Location.LINK_ROAD
                },
                initialise: function initialise() {
                    TextTopicService.showTextTopicsForCategories(['IM_DATA']);
                    _areaIsTooBig = ctrl.task().location().area().length > 75;
                },
                isMapping: function isMapping() {
                    return $state.is('location.mapping');
                },
                task: function taskAcessor() {
                    return TaskService.task();
                },
                area: (val) => {
                    const vwgCustGroups = [
                        CustomerGroup.VWC,
                        CustomerGroup.VWV,
                        CustomerGroup.VWFS,
                        CustomerGroup.SKA,
                        CustomerGroup.SEAT,
                        CustomerGroup.BEA,
                        CustomerGroup.AUC,
                        CustomerGroup.VWL,
                        CustomerGroup.POR,
                        CustomerGroup.CUPR
                    ];
                    const prestigeCustGroups = [CustomerGroup.JAG, CustomerGroup.JAGA, CustomerGroup.LAND, CustomerGroup.LANE, CustomerGroup.MAC, CustomerGroup.CAT, CustomerGroup.TES];
                    let customerGroupCode = TaskService.task().entitlement().customerGroup().code(),
                        area = val ? TaskService.task().location().area(val) : TaskService.task().location().area(),
                        vwgLabel = 'VWG',
                        prestigeLabel = 'DAC';

                    if (customerGroupCode && vwgCustGroups.includes(customerGroupCode) && area && !area.match(/VWG/)) {
                        return TaskService.task().location().area(`${vwgLabel} ${area}`);
                    } else if (customerGroupCode && prestigeCustGroups.includes(customerGroupCode) && area && !area.match(/DAC/)) {
                        return TaskService.task().location().area(`${prestigeLabel} ${area}`);
                    } else {
                        return TaskService.task().location().area(area);
                    }
                },
                getIconClass: function getIconClass(fault) {
                    //TODO - this a temporary hack until we get the correct fault ids from prime
                    if (fault && fault.name()) {
                        return fault
                            .name()
                            .toLowerCase()
                            .replace(/[ ':-]/g, '');
                    } else {
                        return '';
                    }
                },
                directionOfTravel: function directionOfTravelAccessor(val) {
                    return arguments.length ? ctrl.task().location().dot(val) : ctrl.task().location().dot();
                },
                isDisabled: function isDisabled() {
                    return TaskService.task().uiStatus().location();
                },
                isLocationAndAreaFieldsDisabled: function isLocationAndAreaFieldsDisabled() {
                    return ctrl.isDisabled() || TaskService.task().indicators().motorway();
                },
                isAreaDisabled: function isAreaDisabled() {
                    //if the area value length is too big (>75 chars) we override the default Disable function.
                    return _areaIsTooBig ? false : ctrl.isLocationAndAreaFieldsDisabled();
                },
                disableEditAreaIfLengthIsValid: function disableEditAreaIfLengthIsValid(event) {
                    //we can't use ctrl.task().location().area().length because the validation directive causes
                    //the model to be undefined when not valid. We use the event target of the blur event instead.
                    var target = event.target;
                    _areaIsTooBig = target.value.length > 75;
                },
                onMotorway: function onMotorway(val) {
                    return arguments.length ? TaskService.task().indicators().motorway(val) : TaskService.task().indicators().motorway();
                },

                showInfo: function showInfo(val) {
                    return arguments.length ? UIService.showLocationInfo(val) : UIService.showLocationInfo();
                },
                junctionText: function junctionText(val) {
                    return arguments.length ? (_junctionText = val) : _junctionText;
                },
                lane: function lane(val) {
                    if (arguments.length) {
                        if (val && !ctrl.task().location().lane() && !ctrl.task().location().slipRoad()) {
                            ctrl.showSafetyAdvice();
                        }

                        ctrl.task().location().lane(val);
                        ctrl.task().location().slipRoad(null); //clear
                        ctrl.task().location().isHardShoulder(false); //clear
                        ctrl.task().location().isActiveLane(false); //clear
                    }

                    return TaskService.task().location().lane();
                },
                slipRoad: function slipRoad(val) {
                    if (arguments.length) {
                        if (val && !ctrl.task().location().lane() && !ctrl.task().location().slipRoad()) {
                            ctrl.showSafetyAdvice();
                        }

                        ctrl.task().location().slipRoad(val);
                        ctrl.task().location().lane(null); //clear
                        ctrl.task().location().isHardShoulder(false); //clear
                        ctrl.task().location().isActiveLane(false); //clear
                    }

                    return TaskService.task().location().slipRoad();
                },
                isHardShoulder: function isHardShoulderAccessor(val) {
                    if (arguments.length) {
                        TaskService.task().location().isHardShoulder(val);
                        if (val === true && ctrl.slipRoad()) {
                            TaskService.task().location().isActiveLane(false); //clear
                        }
                    }
                    return TaskService.task().location().isHardShoulder();
                },
                isActiveLane: function isActiveLaneAccessor(val) {
                    if (arguments.length) {
                        TaskService.task().location().isActiveLane(val);
                        if (val === true && ctrl.slipRoad()) {
                            TaskService.task().location().isHardShoulder(false); //clear
                        }
                    }
                    return TaskService.task().location().isActiveLane();
                },
                clearLaneInfo: function clearLaneInfo() {
                    TaskService.task().location().lane(null);
                    TaskService.task().location().slipRoad(null);
                    TaskService.task().location().isHardShoulder(false);
                    TaskService.task().location().isActiveLane(false);
                },
                refreshFlags: function () {
                    if (!TaskService.task().indicators().dangerousLocation()) {
                        TabService.clearRelay();
                        LocationService.refreshPriority();
                        LocationService.removeRelayMarker(); //let's not forget the marker on the screen
                    } else {
                        // Check if we are on a motorwway and
                        // where the nearest entry junction is ...
                        // Note that this is not reliable as junctions
                        // are not correctly geolocated ...
                        LocationService.isDangerousLocation(TaskService.task().location().coordinates().latitude(), TaskService.task().location().coordinates().longitude()).then(
                            function isDangerousLocationCheck(onMotorway) {
                                if (!onMotorway) {
                                    // ok we have failed to automatically find nearest recovery point so set breakdown location
                                    LocationService.setNonMotorwaySafeRecoveryLocation(TaskService.task().location().coordinates().latitude(), TaskService.task().location().coordinates().longitude());
                                } else {
                                    LocationService.setMotorwayRelayInformation(TaskService.task().location().coordinates().latitude(), TaskService.task().location().coordinates().longitude());
                                }
                            }
                        );

                        SafetyAdvice();

                        //make sure we display the safety advice
                        LocationService.refreshPriority();
                    }
                },
                dangerousLocation: function dangerousLocation(val) {
                    if (arguments.length) {
                        TaskService.task().indicators().dangerousLocation(val);

                        //if it's a motorway or dangerous location we need to set the idResourceId to -3 (as per RBAUAA-11763)
                        const isMotorway = TaskService.task().indicators().motorway();
                        const isDangerousLocation = TaskService.task().indicators().dangerousLocation();
                        const shouldSetLocal = isMotorway || isDangerousLocation;
                        if (shouldSetLocal) {
                            $timeout(() => {
                                TaskService.task().recovery().destResourceId(DestResourceIDsConstants.LOCAL);
                            }, 0);
                        } else {
                            //if it was previously set to LOCAL, we unset it.
                            if (TaskService.task().recovery().destResourceId() === DestResourceIDsConstants.LOCAL) {
                                TaskService.task().recovery().destResourceId(null);
                            }
                        }
                    }

                    return TaskService.task().indicators().dangerousLocation();
                },
                updateManualOverride: (val) => {
                    LocationService.DangerousLocationIsManualOverridden(val);
                    if (arguments.length) {
                        ctrl.refreshFlags();
                        this.isDangerousLocationTick = val;
                    }
                    PromptService.resetOutcomeObject(TaskService.task());
                    ctrl.checkHomestart();
                },
                homeStart: function homeStart(val) {
                    if (arguments.length) {
                        TaskService.task().indicators().homeStart(val);
                        this.isHomestartTick = val;
                    } else {
                        this.isHomestartTick = false;
                    }

                    ctrl.checkHomestartEntitlement();
                    ctrl.checkDangerousOrMotorway();
                    return TaskService.task().indicators().homeStart();
                },

                checkHomestartEntitlement: async function checkHomestartEntitlement() {
                    if (this.isHomestartTick) {
                        await PromptService.checkHomestartEntitlement(TaskService.task(), CSHService.csh(), CSHService.entitlement());
                    }
                },
                checkDangerousOrMotorway: function checkDangerousOrMotorway() {
                    if (this.isHomestartTick) {
                        ctrl.dangerousLocation(false);
                        this.isDangerousLocationTick = false;
                        ctrl.isMotorway(false);
                        this.isMotorwayTick = false;
                    }
                },
                checkHomestart: function checkHomestart() {
                    if (this.isDangerousLocationTick || this.isMotorwayTick) {
                        ctrl.homeStart(false);
                        this.isHomestartTick = false;
                        PromptService.removePromptById(Prompt.NOT_ENTITLED_TO_HOMESTART);
                    }
                },
                isMotorway: function isMotorway(val) {
                    if (arguments.length) {
                        TaskService.task().indicators().motorway(val);
                        TaskService.task().recovery().motorway(val);
                        TaskService.task().recovery().relay(val);
                        this.isMotorwayTick = true;

                        //if it's a motorway or dangerous location we need to set the idResourceId to -3 (as per RBAUAA-11763)
                        const isMotorway = TaskService.task().indicators().motorway();
                        const isDangerousLocation = TaskService.task().indicators().dangerousLocation();
                        const shouldSetLocal = isMotorway || isDangerousLocation;
                        if (shouldSetLocal) {
                            $timeout(() => {
                                TaskService.task().recovery().destResourceId(DestResourceIDsConstants.LOCAL);
                            }, 0);
                        } else {
                            //if it was previously set to LOCAL, we unset it.
                            if (TaskService.task().recovery().destResourceId() === DestResourceIDsConstants.LOCAL) {
                                TaskService.task().recovery().destResourceId(null);
                            }
                        }

                        if (val) {
                            LocationService.setMotorwayRelayInformation(TaskService.task().location().coordinates().latitude(), TaskService.task().location().coordinates().longitude());
                            this.isMotorwayTick = val;
                        } else {
                            TabService.clearRelay();
                            LocationService.refreshPriority();
                            LocationService.removeRelayMarker();
                            this.isMotorwayTick = false;
                        }
                        ctrl.checkHomestart();
                        TaskService.task().location().resetMotorwayInfo();
                        MappingService.refreshPriority();
                    }

                    return TaskService.task().indicators().motorway();
                },
                showSafetyAdvice: function showSafetyAdvice() {
                    if (TaskService.task().indicators().dangerousLocation() || TaskService.task().indicators().motorway()) {
                        SafetyAdvice();
                    }
                },
                isLocationSet: function isLocationSet() {
                    return TaskService.task().location().isSet();
                },
                modelOptions: {
                    getterSetter: true,
                    debounce: 500
                }
            });
        }
    ]);
