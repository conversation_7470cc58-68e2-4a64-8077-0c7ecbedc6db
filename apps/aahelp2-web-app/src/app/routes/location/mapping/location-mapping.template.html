<div
    id="motorway"
    class="main-panel shadow general-scrollbar"
    ng-class="{'collapse-panel':!ctrl.showInfo()}"
    ng-init="ctrl.initialise()"
    ng-if="ctrl.isLocationSet()"
>
    <form name="locationForm">
        <div class="more-btn-wrap">
            <div
                class="more-btn"
                ng-class="{'icon-cross':!ctrl.showInfo(), 'icon-remove':ctrl.showInfo()}"
                ng-click="ctrl.showInfo(!ctrl.showInfo())"
            ></div>
        </div>
        <div
            class="section"
            ng-show="ctrl.showInfo()"
        >
            <div class="section-header-location clear">
                <div class="col-lg-6 col-sm-6 col-xs-10">
                    <h1>Location</h1>
                </div>
            </div>

            <div class="section-body-location">
                <div class="col-lg-6 col-sm-6 col-xs-6">
                    <label
                        class="label-primary"
                        for="new-breakdown-location"
                        >Breakdown location</label
                    >
                    <dg-validation
                        position="bottom"
                        model="locationForm.location"
                    >
                        <input
                            class="form-field form-control"
                            required
                            dg-validation-trigger
                            id="new-breakdown-location"
                            type="text"
                            name="new-breakdown-location"
                            ng-maxlength="75"
                            maxlength="75"
                            ng-model="ctrl.task().location().text"
                            ng-disabled="ctrl.isLocationAndAreaFieldsDisabled()"
                            ng-model-options="ctrl.modelOptions"
                        />
                    </dg-validation>
                </div>
                <div class="col-lg-6 col-sm-6 col-xs-6">
                    <label
                        class="label-primary"
                        for="area"
                        >Area / Postcode</label
                    >
                    <dg-validation
                        position="bottom"
                        model="locationForm.area"
                    >
                        <input
                            class="form-field form-control"
                            required
                            dg-validation-trigger
                            id="area"
                            name="area"
                            type="text"
                            ng-model="ctrl.area"
                            ng-maxlength="75"
                            maxlength="75"
                            ng-disabled="ctrl.isAreaDisabled()"
                            ng-blur="ctrl.disableEditAreaIfLengthIsValid($event)"
                            ng-model-options="ctrl.modelOptions"
                        />
                    </dg-validation>
                </div>
            </div>

            <div class="section-body-location">
                <div class="clear">
                    <div class="col-lg-8 col-sm-9 col-xs-9">
                        <div class="row">
                            <!-- current:{{ctrl.task().recovery().destResourceId()}} -->
                            <div class="col-lg-4 col-sm-5 col-xs-5">
                                <div class="radio-wrap">
                                    <input
                                        id="motorwayCheck"
                                        class="form-field form-control"
                                        name="motorwayCheck"
                                        type="checkbox"
                                        ng-model="ctrl.isMotorway"
                                        ng-model-options="ctrl.modelOptions"
                                        ng-disabled="ctrl.isDisabled()"
                                    />
                                    <label
                                        for="motorwayCheck"
                                        class="radio-label"
                                    >
                                        <span class="dot-outer">
                                            <span class="icon-tick"></span>
                                        </span>
                                        Motorway
                                    </label>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-7 col-xs-7">
                                <div class="radio-wrap">
                                    <input
                                        id="dangerousLocation"
                                        class="form-field form-control"
                                        name="dangerousLocation"
                                        type="checkbox"
                                        ng-change="ctrl.updateManualOverride(ctrl.dangerousLocation())"
                                        ng-model="ctrl.dangerousLocation"
                                        ng-model-options="ctrl.modelOptions"
                                        ng-disabled="ctrl.isDisabled()"
                                    />
                                    <label
                                        for="dangerousLocation"
                                        class="radio-label"
                                    >
                                        <span class="dot-outer">
                                            <span class="icon-tick"></span>
                                        </span>
                                        Dangerous location
                                    </label>
                                </div>
                            </div>
                            <div class="col-lg-4 col-sm-7 col-xs-7">
                                <div class="radio-wrap">
                                    <input
                                        id="homeStart"
                                        class="form-field form-control"
                                        name="homeStart"
                                        ng-model="ctrl.homeStart"
                                        type="checkbox"
                                        ng-model-options="ctrl.modelOptions"
                                    />
                                    <label
                                        for="homeStart"
                                        class="radio-label"
                                    >
                                        <span class="dot-outer">
                                            <span class="icon-tick"></span>
                                        </span>
                                        At Home
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div
                class="section-body-location"
                ng-if="ctrl.onMotorway()"
            >
                <fieldset>
                    <div class="col-sm-6 col-xs-6">
                        <label
                            for="lastJunctionPassed"
                            class="label-primary"
                            >Last junction passed</label
                        >
                        <dg-validation
                            model="locationForm.lastJunctionPassed"
                            position="bottom"
                        >
                            <input
                                class="form-field form-control"
                                id="lastJunctionPassed"
                                name="lastJunctionPassed"
                                ng-maxlength="15"
                                maxlength="15"
                                aah-motorway-junction-validator
                                required
                                dg-validation-trigger
                                ng-model="ctrl.task().location().lastJunctionPassed"
                                ng-model-options="ctrl.modelOptions"
                                aah-auto-save
                            />
                        </dg-validation>
                    </div>
                    <div class="col-sm-6 col-xs-6">
                        <label
                            for="travellingTo"
                            class="label-primary"
                            >Travelling towards</label
                        >
                        <dg-validation
                            model="locationForm.travellingTo"
                            position="bottom"
                        >
                            <input
                                class="form-field form-control"
                                id="travellingTo"
                                name="travellingTo"
                                required
                                dg-validation-trigger
                                ng-model="ctrl.task().location().travellingTo"
                                ng-model-options="ctrl.modelOptions"
                                aah-auto-save
                            />
                        </dg-validation>
                    </div>
                </fieldset>
            </div>

            <div
                class="section-body-location"
                ng-if="ctrl.onMotorway()"
            >
                <div class="col-lg-6 col-sm-12 col-xs-12">
                    <div class="row">
                        <div class="col-lg-12 col-sm-12 col-xs-12">
                            <label class="label-location">Direction of Travel</label>
                        </div>
                        <div class="col-lg-6 col-sm-6 col-xs-6">
                            <div class="radio-wrap">
                                <input
                                    id="north"
                                    type="radio"
                                    name="direction"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model="ctrl.directionOfTravel"
                                    ng-model-options="ctrl.modelOptions"
                                    value="{{::ctrl.dotOptions.northBound}}"
                                />
                                <label
                                    for="north"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>North</label
                                >
                            </div>

                            <div class="radio-wrap">
                                <input
                                    id="south"
                                    type="radio"
                                    name="direction"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model="ctrl.directionOfTravel"
                                    ng-model-options="ctrl.modelOptions"
                                    value="{{::ctrl.dotOptions.southBound}}"
                                />
                                <label
                                    for="south"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>South</label
                                >
                            </div>

                            <div class="radio-wrap">
                                <input
                                    id="east"
                                    type="radio"
                                    name="direction"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model="ctrl.directionOfTravel"
                                    ng-model-options="ctrl.modelOptions"
                                    value="{{::ctrl.dotOptions.eastBound}}"
                                />
                                <label
                                    for="east"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>East</label
                                >
                            </div>
                            <div class="radio-wrap">
                                <input
                                    id="west"
                                    type="radio"
                                    name="direction"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model="ctrl.directionOfTravel"
                                    ng-model-options="ctrl.modelOptions"
                                    value="{{::ctrl.dotOptions.westBound}}"
                                />
                                <label
                                    for="west"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>West</label
                                >
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-6 col-xs-6">
                            <div class="radio-wrap">
                                <input
                                    id="clockwise"
                                    type="radio"
                                    name="direction"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model="ctrl.directionOfTravel"
                                    ng-model-options="ctrl.modelOptions"
                                    value="{{::ctrl.dotOptions.clockwise}}"
                                />
                                <label
                                    for="clockwise"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>Clockwise</label
                                >
                            </div>
                            <div class="radio-wrap">
                                <input
                                    id="anti-clockwise"
                                    type="radio"
                                    name="direction"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model="ctrl.directionOfTravel"
                                    ng-model-options="ctrl.modelOptions"
                                    value="{{::ctrl.dotOptions.antiClockwise}}"
                                />
                                <label
                                    for="anti-clockwise"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>Anti-clockwise</label
                                >
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="col-lg-6 col-sm-12 col-xs-12"
                    ng-if="ctrl.onMotorway()"
                >
                    <fieldset>
                        <div class="col-lg-6 col-sm-6 col-xs-6 lanes">
                            <label class="label-location">Lanes</label>
                            <!-- start - hard should -->
                            <div class="radio-wrap">
                                <input
                                    id="hardShoulder"
                                    type="radio"
                                    name="laneGroup"
                                    ng-disabled="ctrl.isDisabled() || !!ctrl.slipRoad()"
                                    ng-model="ctrl.lane"
                                    ng-model-options="{getterSetter:true}"
                                    value="{{::ctrl.laneOptions.hardShoulder}}"
                                />
                                <label
                                    for="hardShoulder"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>Hard shoulder</label
                                >
                            </div>
                            <div
                                class="radio-wrap checkbox-wrap"
                                ng-show="ctrl.task().location().lane() === ctrl.laneOptions.hardShoulder"
                            >
                                <input
                                    id="active-lane"
                                    ng-disabled="ctrl.isDisabled() || !!ctrl.slipRoad()"
                                    type="checkbox"
                                    ng-model="ctrl.isActiveLane"
                                    ng-value="true"
                                    ng-model-options="{getterSetter:true}"
                                />
                                <label
                                    for="active-lane"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="icon-tick"></span></span>Active lane</label
                                >
                            </div>
                            <div class="radio-wrap">
                                <input
                                    id="laybyOrRefugeArea"
                                    type="radio"
                                    name="laneGroup"
                                    ng-disabled="ctrl.isDisabled() || !!ctrl.slipRoad()"
                                    ng-model="ctrl.lane"
                                    ng-model-options="{getterSetter:true}"
                                    value="{{::ctrl.laneOptions.laybyOrRefugeArea}}"
                                />
                                <label
                                    for="laybyOrRefugeArea"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>Layby/Refuge area</label
                                >
                            </div>
                            <!-- end - hard should -->
                            <div class="radio-wrap">
                                <input
                                    id="lane1"
                                    type="radio"
                                    name="laneGroup"
                                    ng-disabled="ctrl.isDisabled() || !!ctrl.slipRoad()"
                                    ng-model="ctrl.lane"
                                    ng-model-options="{getterSetter:true}"
                                    value="{{::ctrl.laneOptions.laneOne}}"
                                />
                                <label
                                    for="lane1"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>Lane 1</label
                                >
                            </div>
                            <div class="radio-wrap">
                                <input
                                    id="central-lanes"
                                    type="radio"
                                    name="laneGroup"
                                    ng-disabled="ctrl.isDisabled() || !!ctrl.slipRoad()"
                                    ng-model="ctrl.lane"
                                    ng-model-options="{getterSetter:true}"
                                    value="{{::ctrl.laneOptions.centralLanes}}"
                                />
                                <label
                                    for="central-lanes"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>Central lane(s)</label
                                >
                            </div>
                            <div class="radio-wrap">
                                <input
                                    id="outside-lane"
                                    type="radio"
                                    name="laneGroup"
                                    ng-disabled="ctrl.isDisabled() || !!ctrl.slipRoad()"
                                    ng-model="ctrl.lane"
                                    ng-model-options="{getterSetter:true}"
                                    value="{{::ctrl.laneOptions.outsideLane}}"
                                />
                                <label
                                    for="outside-lane"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>Outside lane</label
                                >
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-6 col-xs-6">
                            <label class="label-location">Slip road</label>
                            <!-- Start - Slip Road Entry -->
                            <div class="radio-wrap">
                                <input
                                    id="entry"
                                    ng-disabled="ctrl.isDisabled() || !!ctrl.lane()"
                                    ng-model="ctrl.slipRoad"
                                    ng-model-options="ctrl.modelOptions"
                                    type="radio"
                                    name="slipRoadGroup"
                                    value="{{::ctrl.slipRoadOptions.slipEntry}}"
                                />
                                <label
                                    for="entry"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>Slip road entry</label
                                >
                            </div>
                            <div
                                ng-show="ctrl.slipRoadOptions.slipEntry === ctrl.slipRoad()"
                                class="nested-radio-options-wrapper"
                            >
                                <div class="radio-wrap">
                                    <input
                                        id="hardShoulder1"
                                        type="radio"
                                        name="slipRoadEntryLaneTypeGroup"
                                        ng-disabled="ctrl.isDisabled()"
                                        ng-model="ctrl.isActiveLane"
                                        ng-value="true"
                                        ng-model-options="{getterSetter:true}"
                                    />
                                    <label
                                        for="hardShoulder1"
                                        class="radio-label"
                                        ><span class="dot-outer"><span class="dot"></span></span>Active lane</label
                                    >
                                </div>
                                <div class="radio-wrap">
                                    <input
                                        id="activeLane1"
                                        type="radio"
                                        name="slipRoadEntryLaneTypeGroup"
                                        ng-disabled="ctrl.isDisabled()"
                                        ng-model="ctrl.isHardShoulder"
                                        ng-value="true"
                                        ng-model-options="{getterSetter:true}"
                                    />
                                    <label
                                        for="activeLane1"
                                        class="radio-label"
                                        ><span class="dot-outer"><span class="dot"></span></span>Hard shoulder</label
                                    >
                                </div>
                            </div>
                            <!-- End - Slip Road Entry -->
                            <!-- Start - Slip Road Exit -->
                            <div class="radio-wrap">
                                <input
                                    id="exit"
                                    ng-disabled="ctrl.isDisabled() || !!ctrl.lane()"
                                    ng-model="ctrl.slipRoad"
                                    ng-model-options="ctrl.modelOptions"
                                    type="radio"
                                    name="slipRoadGroup"
                                    value="{{::ctrl.slipRoadOptions.slipExit}}"
                                />
                                <label
                                    for="exit"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>Slip road exit</label
                                >
                            </div>
                            <div
                                ng-show="ctrl.slipRoadOptions.slipExit === ctrl.slipRoad()"
                                class="nested-radio-options-wrapper"
                            >
                                <div class="radio-wrap">
                                    <input
                                        id="activeLane2"
                                        type="radio"
                                        name="slipRoadExitLaneTypeGroup"
                                        ng-disabled="ctrl.isDisabled()"
                                        ng-model="ctrl.isActiveLane"
                                        ng-value="true"
                                        ng-model-options="{getterSetter:true}"
                                    />
                                    <label
                                        for="activeLane2"
                                        class="radio-label"
                                        ><span class="dot-outer"><span class="dot"></span></span>Active lane</label
                                    >
                                </div>
                                <div class="radio-wrap">
                                    <input
                                        id="hardShoulder2"
                                        type="radio"
                                        name="slipRoadExitLaneTypeGroup"
                                        ng-disabled="ctrl.isDisabled()"
                                        ng-model="ctrl.isHardShoulder"
                                        ng-value="true"
                                        ng-model-options="{getterSetter:true}"
                                    />
                                    <label
                                        for="hardShoulder2"
                                        class="radio-label"
                                        ><span class="dot-outer"><span class="dot"></span></span>Hard shoulder</label
                                    >
                                </div>
                            </div>
                            <!-- End - Slip Road Exit -->
                            <!-- Start - Link Road -->
                            <div class="radio-wrap">
                                <input
                                    id="link-road"
                                    ng-disabled="ctrl.isDisabled() || !!ctrl.lane()"
                                    ng-model="ctrl.slipRoad"
                                    ng-model-options="ctrl.modelOptions"
                                    type="radio"
                                    name="slipRoadGroup"
                                    value="{{::ctrl.slipRoadOptions.linkRoad}}"
                                />
                                <label
                                    for="link-road"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="dot"></span></span>Link road</label
                                >
                            </div>
                            <div
                                ng-show="ctrl.slipRoadOptions.linkRoad === ctrl.slipRoad()"
                                class="nested-radio-options-wrapper"
                            >
                                <div class="radio-wrap">
                                    <input
                                        id="activeLane3"
                                        type="radio"
                                        name="linkRoadLaneTypeGroup"
                                        ng-disabled="ctrl.isDisabled()"
                                        ng-model="ctrl.isActiveLane"
                                        ng-value="true"
                                        ng-model-options="{getterSetter:true}"
                                    />
                                    <label
                                        for="activeLane3"
                                        class="radio-label"
                                        ><span class="dot-outer"><span class="dot"></span></span>Active Lane</label
                                    >
                                </div>
                                <div class="radio-wrap">
                                    <input
                                        id="hardShoulder3"
                                        type="radio"
                                        name="linkRoadLaneTypeGroup"
                                        ng-disabled="ctrl.isDisabled()"
                                        ng-model="ctrl.isHardShoulder"
                                        ng-value="true"
                                        ng-model-options="{getterSetter:true}"
                                    />
                                    <label
                                        for="hardShoulder3"
                                        class="radio-label"
                                        ><span class="dot-outer"><span class="dot"></span></span>Hard shoulder</label
                                    >
                                </div>
                            </div>
                            <!-- End - Link Road -->
                            <div class="clear-option">
                                <a
                                    href
                                    ng-click="ctrl.clearLaneInfo()"
                                    class="ng-scope"
                                    >Clear</a
                                >
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <hr ng-if="!ctrl.onMotorway()" />

            <div
                class="location-reminder section-header-alert alert alert-danger"
                ng-if="!ctrl.onMotorway()"
            >
                <h3 class="location-reminder-header">Reminder</h3>
                <span class="vertical-separator"></span>
                <span class="location-reminder-content"
                    >Ensure you use the rule of 4 when mapping. <br />
                    <a
                        target="_blank"
                        href="https://theaa.sharepoint.com/:b:/r/sites/RoadOperations/Callhandling/Shared%20Documents/Rule%20of%204%20Handout%201.0%20(2).pdf?csf=1&amp;web=1&amp;e=LTFIhy"
                        >Click here for guide</a
                    >
                </span>
            </div>
        </div>
    </form>
</div>
