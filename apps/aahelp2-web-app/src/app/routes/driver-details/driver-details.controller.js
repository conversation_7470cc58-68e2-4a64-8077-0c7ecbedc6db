'use strict';
var _ = require('lodash');
require('angular');
var CarHireDriver = require('@aa/mobility-models/lib/car-hire-driver.model');
var InsurerErrorsFactory = require('@aa/mobility-models/lib/factories/insurer-errors.factory');
var AlertTypes = require('../../constants/alert/alert-type.constants');
var MobilityInsurance = require('@aa/mobility-models/lib//mobility-insurance.model');
var ThirdPartyInsurance = require('@aa/mobility-models/lib/third-party-insurance.model');
var CarRental = require('@aa/mobility-models/lib/car-rental.model');
var RAFVehicle = require('@aa/mobility-models/lib/raf-vehicle.model');

const Moment = require('moment'),
    MomentRange = require('moment-range'),
    moment = MomentRange.extendMoment(Moment);

module.exports = angular
    .module('aah-driver-details-controller-module', [
        require('../../services/driver-details/driver-details.service').name,
        require('../../services/alert/alert.service').name,
        require('../../services/mobility-task/mobility-task.service').name,
        require('../../services/prompt/prompt.service').name,
        require('../../services/validation/validation.service').name,
        require('../../services/mobility/mobility-retailer.service').name,
        require('../../services/task/task-write-response.service').name,
        require('../../services/address-lookup/address-lookup.service').name
    ])
    .controller('aahDriverDetailsController', [
        '$q',
        'aahDriverDetailsService',
        '$scope',
        'aahAlertService',
        'aahMobilityTaskService',
        'aahPromptService',
        'aahValidationService',
        'aahMobilityRetailerService',
        'aahTaskWriteResponseService',
        'aahAddressLookupService',
        function DriverDetailsController(
            $q,
            DriverDetailsService,
            $scope,
            AlertService,
            MobilityTaskService,
            PromptService,
            ValidationService,
            MobilityRetailerService,
            TaskResponseService,
            AddressLookupService
        ) {
            const ctrl = this;

            let _driverUpdateInProgress = false,
                _enableSaveBtn = false,
                _idx,
                _formInvalid = false,
                _disablePrimary = false,
                _driverId,
                _defaultModelScope = true,
                _rental,
                _selfInsuredDataModel,
                _selfInsured,
                _formErrorList,
                _driverEligible = true;

            function _allDriver() {
                return DriverDetailsService.combineDrivers();
            }

            function _showInsuranceMessages(successMessage = '', isUpdate = false) {
                let driverData = isUpdate ? DriverDetailsService.editingDriver() : ctrl.driverDataModel();

                if (driverData.isEligible() === false || (driverData.errors() && driverData.errors().length > 0)) {
                    let errorMessages = InsurerErrorsFactory.buildErrorMessages(driverData.errors(), InsurerErrorsFactory.driverErrors().general);
                    if (errorMessages) {
                        AlertService.createAlert(errorMessages, AlertTypes.DANGER);
                    }
                    let driverAge = new Date().getFullYear() - new Date(driverData.dateOfBirth()).getFullYear();
                    _driverEligible = driverAge > 85 || (driverAge < 21 && _selfInsured !== true) ? false : true;
                    if (!driverData.manualLicenceCheck() && !!driverData.licenceType() && driverData.licenceType() !== 'GB') {
                        AlertService.createAlert('Manual check should be YES for Non GB licences', AlertTypes.INFO);
                    }
                    if (driverData.isEligible() === false && !errorMessages) {
                        //checking for false, no alert if isEligible comes up as null
                        AlertService.createAlert('Driver is not Eligible', AlertTypes.INFO);
                    }
                } else if (ctrl.isThirdParty()) {
                    _defaultModelScope = true;
                    _formInvalid = false;
                    _enableSaveBtn = false;
                    AlertService.createAlert(successMessage, AlertTypes.INFO);
                } else {
                    if (!!driverData.licenceType() && driverData.licenceType() !== 'GB' && !ctrl.isEditingDriverPrimary() && ctrl.isPrimaryDriverGB()) {
                        AlertService.createAlert('Please enter the driver with non GB licence as the main driver', 'ERROR');
                    }
                    ctrl.driverDataModel(new CarHireDriver());
                    DriverDetailsService.editingDriver(new CarHireDriver());
                    _defaultModelScope = true;
                    _enableSaveBtn = false;
                    _driverEligible = true;
                    _formInvalid = false;
                    AlertService.createAlert(successMessage, AlertTypes.INFO);
                }
            }

            let _formErrors = (form) => {
                const errors = [];
                angular.forEach(form.$error.required, (key, res) => {
                    const fields = _.startCase(key.$name);
                    errors.push(fields);
                });
                return errors;
            };

            _.extend(ctrl, {
                init: () => {
                    _driverId = DriverDetailsService.getMainDriver().id();
                    _rental = DriverDetailsService.getRental();
                    if (ctrl.changeDataModelScope()) {
                        ctrl.selectOptionChange(ctrl.driverDataModel().licenceType());
                    }
                    ctrl.driverDataModel(_driverId === -1 ? DriverDetailsService.getMainDriver() : new CarHireDriver()); //check to display the correct model in the
                    _selfInsuredDataModel = _rental.insurance().type() === ThirdPartyInsurance.TYPE ? _rental.insurance() : null;
                    _selfInsured = !!_selfInsuredDataModel;

                    if (ctrl.changeDataModelScope() && ctrl.allDrivers()[0].id() < 1 && !ctrl.allDrivers()[0].contact().name()) {
                        ctrl.changeDataModelScope().contact().name(ctrl.getTask().contact().name());
                        ctrl.changeDataModelScope().contact().telephone(ctrl.getTask().contact().telephone());
                    }

                    // load licenceTypes
                    DriverDetailsService.loadRefData();
                },
                enableSaveBtn: () => {
                    return _enableSaveBtn;
                },
                getTask: () => MobilityTaskService.mobilityTask(),
                cooperErrors: (field) => {
                    if (ctrl.driverDataModel()) {
                        return InsurerErrorsFactory.buildErrorMessages(ctrl.driverDataModel().errors(), InsurerErrorsFactory.driverErrors()[field]);
                    }
                    return null;
                },

                index: () => {
                    return _idx;
                },
                selfInsured: (...args) => {
                    return args.length ? (_selfInsured = args[0]) : _selfInsured;
                },

                allDrivers: () => {
                    return _allDriver();
                },
                hasDrivers: () => {
                    return MobilityTaskService.mobilityTask() && MobilityTaskService.mobilityTask().rental().mainDriver() ? MobilityTaskService.mobilityTask().rental().mainDriver().id() > 0 : false;
                },
                insurance: () => {
                    return _rental.insurance();
                },

                driverDataModel: (val) => {
                    return DriverDetailsService.driverModel(val);
                },
                selfInsuredDataModel: () => {
                    return _selfInsuredDataModel;
                },
                changeDataModelScope: () => {
                    let scopes = _defaultModelScope ? ctrl.driverDataModel() : DriverDetailsService.editingDriver();
                    return scopes;
                },
                areQuestionsAnswered: () => {
                    return ctrl.changeDataModelScope().hasDisqualifications() !== undefined && ctrl.changeDataModelScope().hasPenaltyPoints() !== undefined;
                },
                changeToSelfInsured: (insuranceType) => {
                    PromptService.showChangeInsuranceOptionPrompt().then(function promptSuccess(responseYes) {
                        if (responseYes) {
                            if (insuranceType === ThirdPartyInsurance.TYPE) {
                                AlertService.createAlert('Only One Driver allowed, subsequent adding will overwrite the main driver', AlertTypes.DANGER);
                                _selfInsuredDataModel = new ThirdPartyInsurance();
                                ctrl.driverDataModel(DriverDetailsService.getMainDriver());
                            } else {
                                const existingSupNetworkCode = _rental.insurance().supNetworkCode();
                                _rental.insurance(new MobilityInsurance());
                                _rental.insurance().supNetworkCode(existingSupNetworkCode);
                                ctrl.driverDataModel(_driverId === -1 ? DriverDetailsService.getMainDriver() : new CarHireDriver());
                                //this is for new appointment incase we have deleted the appointment by switching to self insured
                                return DriverDetailsService.bookAppointment().then(() => {
                                    ctrl.driverDataModel(DriverDetailsService.getMainDriver());
                                });
                            }
                        } else {
                            _selfInsured = !_selfInsured;
                        }
                    });
                },
                hasDisqualificationsOrPenaltyPoints: () => {
                    let driverHasDisqualificationsOrPenaltyPoints = false;
                    if (ctrl.changeDataModelScope().manualLicenceCheck() && (ctrl.changeDataModelScope().hasDisqualifications() || ctrl.changeDataModelScope().hasPenaltyPoints())) {
                        if (ctrl.changeDataModelScope().hasDisqualifications()) {
                            AlertService.createAlert('Driver with any disqualification in the last 5 years is not eligible', AlertTypes.DANGER);
                        }
                        if (ctrl.changeDataModelScope().hasPenaltyPoints()) {
                            AlertService.createAlert('Driver with more than 9 penalty points in the last 3 years is not eligible', AlertTypes.DANGER);
                        }
                        driverHasDisqualificationsOrPenaltyPoints = true;
                    }
                    return driverHasDisqualificationsOrPenaltyPoints;
                },
                addDriver: (form) => {
                    if (ctrl.isEnterprise() && ctrl.hasDrivers()) {
                        AlertService.createAlert(`Only one driver can be assigned to this booking. To make changes, please edit the existing driver's details`, AlertTypes.DANGER);
                        return;
                    }

                    let driverAge = new Date().getFullYear() - new Date(ctrl.driverDataModel().dateOfBirth()).getFullYear();
                    let driverEligible;
                    if (_selfInsured) {
                        driverEligible = true;
                    } else {
                        driverEligible = driverAge > 85 || driverAge < 21 ? false : true;
                    }
                    const isInvalidDriver = !MobilityTaskService.isEnterpriseOrPnC() && (!driverEligible || !ctrl.driverDataModel().dateOfBirth());
                    if (isInvalidDriver) {
                        AlertService.createAlert('Driver Age should be between 21 and 85 years', AlertTypes.DANGER);
                        return;
                    }

                    if (ctrl.allDrivers() && ctrl.allDrivers().length === 4) {
                        AlertService.createAlert('appointment cannot have more than 4 drivers', AlertTypes.DANGER);
                        return;
                    }

                    if (!ValidationService.areAllDriversValidated(ctrl.getTask().rental())) {
                        AlertService.createAlert('Please revalidate all the  previously added drivers', AlertTypes.DANGER);
                        return;
                    }
                    if (ctrl.hasDisqualificationsOrPenaltyPoints()) {
                        return;
                    }
                    if (!ctrl.isFormValid(form)) {
                        return;
                    }

                    if (_selfInsured) {
                        _formInvalid = false;
                        _driverUpdateInProgress = true;
                        return DriverDetailsService.selfInsuredDriver(_selfInsuredDataModel, ctrl.driverDataModel())
                            .then((response) => {
                                ctrl.driverDataModel().errors(response.mainDriver().errors());
                                form.$setPristine();
                                _driverUpdateInProgress = false;
                                _selfInsuredDataModel = DriverDetailsService.getRental().insurance();
                                _showInsuranceMessages('Driver added successfully');
                            })
                            .catch(() => {
                                _driverUpdateInProgress = false;
                            });
                    } else {
                        _formInvalid = false;
                        _driverUpdateInProgress = true;
                        if (ctrl.driverDataModel().isPrimaryDriver() && ctrl.driverDataModel().errors() && ctrl.driverDataModel().errors().length > 0) {
                            ctrl.driverDataModel().errors([]);
                            ctrl.driverDataModel().isEligible(null);
                            ctrl.driverDataModel().id(-1);
                        }
                        DriverDetailsService.persistDriver(ctrl.driverDataModel()) // save data to db
                            .then((resp) => {
                                if (!resp.errors().length && resp.isEligible() !== false) {
                                    ctrl.driverDataModel(new CarHireDriver());
                                }
                                ctrl.driverDataModel().errors(resp.errors());
                                ctrl.driverDataModel().isEligible(resp.isEligible());
                                _driverUpdateInProgress = false;
                                form.$setPristine();
                                _showInsuranceMessages('Driver added successfully');
                            })
                            .catch((resp) => {
                                _driverUpdateInProgress = false;
                                if (resp.data && resp.data.errMsg) {
                                    // TaskWrite failed response handled
                                    TaskResponseService.failedTaskWrite(resp);
                                } else {
                                    ctrl.driverDataModel().errors(resp.data.errors);
                                    ctrl.driverDataModel().isEligible(false);
                                    _showInsuranceMessages();
                                }
                            });
                    }
                },
                driverEligible: () => {
                    return _driverEligible;
                },

                formErrors: () => {
                    return _formErrorList;
                },
                prepareUpdate: (driver) => {
                    let editDriver;
                    _idx = _.findIndex(DriverDetailsService.combineDrivers(), (driverIdx) => driverIdx.id() === driver.id()); // store the index of selected data
                    editDriver = DriverDetailsService.combineDrivers()[_idx].toJSON();
                    ctrl.selectOptionChange(editDriver.licenceType);
                    if (!_.isEmpty(editDriver)) {
                        editDriver.dateOfBirth = editDriver.dateOfBirth ? new Date(editDriver.dateOfBirth) : editDriver.dateOfBirth;
                        editDriver.licenceExpire = editDriver.licenceExpire ? new Date(editDriver.licenceExpire) : editDriver.licenceExpire;
                    }
                    DriverDetailsService.editingDriver(new CarHireDriver(editDriver));
                    _enableSaveBtn = true;
                    _defaultModelScope = false;
                    _driverEligible = true;
                    ctrl.driverDataModel(new CarHireDriver());
                    let selectDriverAddress = editDriver.address.addressLines.join();
                    AddressLookupService.search(editDriver.address.postcode).then(function () {
                        if (AddressLookupService.addresses().length > 0) {
                            AddressLookupService.isDisableSelect(false);
                            let selectedAddress = AddressLookupService.addresses().find((addr) => addr.addressLines().join() == selectDriverAddress);
                            ctrl.changeDataModelScope().address(selectedAddress ? selectedAddress : DriverDetailsService.combineDrivers()[_idx].address());
                        } else {
                            return false;
                        }
                    });
                },

                cancelEdit: () => {
                    _defaultModelScope = true;
                    _enableSaveBtn = false;
                    DriverDetailsService.editingDriver(new CarHireDriver());
                    _allDriver();
                },
                isFormValid: (form) => {
                    if (form.$invalid) {
                        AlertService.createAlert('Please fill in all the required field(s)', AlertTypes.SUCCESS);
                        _formErrorList = _formErrors(form);
                        _formInvalid = true;
                        return false;
                    }
                    return true;
                },

                updateDriver: (form) => {
                    ctrl.selectOptionChange(DriverDetailsService.editingDriver().licenceType());
                    if (ctrl.hasDisqualificationsOrPenaltyPoints()) {
                        return;
                    }
                    if (!ctrl.isFormValid(form)) {
                        return;
                    }
                    if (_selfInsured) {
                        _driverUpdateInProgress = true;

                        return DriverDetailsService.selfInsuredDriver(_selfInsuredDataModel)
                            .then((response) => {
                                ctrl.selectOptionChange(DriverDetailsService.editingDriver().licenceType());
                                DriverDetailsService.editingDriver().errors(response.mainDriver().errors());
                                _showInsuranceMessages('Driver updated successfully', true);
                                form.$setPristine();
                                _driverUpdateInProgress = false;
                                _selfInsuredDataModel = DriverDetailsService.getRental().insurance();
                            })
                            .catch(() => {
                                _driverUpdateInProgress = false;
                            });
                    }

                    _driverUpdateInProgress = true;
                    return DriverDetailsService.updateDriver(DriverDetailsService.editingDriver()) // save data to db
                        .then((res) => {
                            MobilityTaskService.mobilityTask().rental(new CarRental(res.data.rental));
                            if (res.data.driver.isPrimaryDriver || ctrl.isThirdParty()) {
                                _rental.mainDriver(new CarHireDriver(res.data.driver));
                            } else {
                                // find additional driver in rental from editingdDriver and replace with new value
                                let matchingAdditionalDriverIdx = _.findIndex(_rental.additionalDrivers(), (driver) => {
                                    return driver.id() === DriverDetailsService.editingDriver().id();
                                });
                                _rental.additionalDrivers()[matchingAdditionalDriverIdx] = new CarHireDriver(res.data.driver);
                            }
                            _driverUpdateInProgress = false;
                            ctrl.driverDataModel().errors(res.data.driver.errors);
                            DriverDetailsService.editingDriver().errors(res.data.driver.errors);
                            DriverDetailsService.editingDriver().isEligible(res.data.driver.isEligible);
                            _showInsuranceMessages('Driver updated successfully', true);
                            form.$setPristine();
                        })
                        .catch((resp) => {
                            ctrl.driverDataModel().errors(resp.data.errors);
                            DriverDetailsService.editingDriver().errors(resp.data.errors);
                            DriverDetailsService.editingDriver().isEligible(false);
                            _driverUpdateInProgress = false;
                            _showInsuranceMessages();
                        });
                },
                removeDriver: (driver) => {
                    if (!driver.isPrimaryDriver() || ctrl.isThirdParty() || MobilityTaskService.isThirdPartyThrifty()) {
                        DriverDetailsService.deleteDriver(driver.id())
                            .then((resp) => {
                                ctrl.driverDataModel().errors(resp.data.errors);
                                _showInsuranceMessages('Additional driver removed');
                            })
                            .catch((resp) => {
                                let errorsArr = resp.data.errors ? resp.data.errors : resp.data.rootCause.errors;
                                ctrl.driverDataModel().errors(errorsArr);
                                _showInsuranceMessages();
                            });
                    } else {
                        AlertService.createAlert('you cannot delete a main driver', AlertTypes.ERROR);
                    }
                },

                swapMainDriver: (driver) => {
                    let alertIndex = AlertService.createAlert('Changing Primary Driver...', AlertTypes.INFO);
                    return DriverDetailsService.swapMainDriver(driver).finally(() => {
                        _allDriver();
                        AlertService.removeAlert(alertIndex);
                    });
                },

                licenseType: () => {
                    return DriverDetailsService.getLicenceTypes();
                },

                isEditingDriverPrimary: () => {
                    return DriverDetailsService.editingDriver() && DriverDetailsService.editingDriver().isPrimaryDriver();
                },

                isPrimaryDriverGB: () => {
                    return ctrl.getTask().rental().mainDriver() && ctrl.getTask().rental().mainDriver().licenceType() === 'GB';
                },
                disableManualCheck: () => {
                    // disable manual check if licence type is GB. Enable if MIB Hub down ie.,licenceLookupResult = 3
                    return ctrl.driverDataModel().licenceLookupResult() !== 3 && ctrl.changeDataModelScope().licenceType() === 'GB';
                },
                selectOptionChange: (data) => {
                    if (data !== '' && data === 'GB') {
                        ctrl.selectOption = true;
                        if (ctrl.disableManualCheck()) {
                            ctrl.changeDataModelScope().manualLicenceCheck(false);
                        }
                    } else {
                        ctrl.selectOption = false;
                    }
                },

                formInvalid: () => {
                    return _formInvalid;
                },
                showDrivers: () => {
                    // if (ctrl.getTask().createReason().isHireCar()) {

                    //   if (ctrl.getTask().rental().isSelfInsured()) {
                    //     return ctrl.getTask().rental().isSelfInsured();
                    //   }

                    //   return ctrl.isctrl.getTask().rental().insurance().isSet() || ctrl.getTask().rental().mainDriver().hasInsurerId();
                    // }
                    //returning true as the insurance details are always visible and so the drivers
                    return true;
                },
                isThirdParty: () => {
                    return MobilityTaskService.isThirdParty();
                },
                disableIsPrimary: () => {
                    return _disablePrimary;
                },
                disableOptions: () => {
                    let retVal = false;
                    if (MobilityTaskService.mobilityTask().isCompleted()) {
                        retVal = true;
                    } else if (
                        MobilityTaskService.mobilityTask() &&
                        MobilityTaskService.mobilityTask().checkinReady() &&
                        DriverDetailsService.editingDriver() &&
                        DriverDetailsService.editingDriver().isPrimaryDriver()
                    ) {
                        retVal = true;
                    }
                    return _driverUpdateInProgress || retVal;
                },
                disableSwapOption: () => {
                    return MobilityTaskService.mobilityTask().checkinReady() || MobilityTaskService.mobilityTask().isCompleted() || _driverUpdateInProgress;
                },
                postCodeRequired: () => {
                    return ctrl.selectOption;
                },
                isEnterprise: () => {
                    return MobilityTaskService.isEnterprise();
                },
                showInsurance: () => {
                    //Show Insurancce for only JLR/Porsche customers - need to change this logic if need to show hide for multiple customer groups
                    return (
                        !(
                            MobilityTaskService.isEnterprise() &&
                            !(
                                ctrl.getTask().entitlement().customerGroup().isAllJLR() ||
                                ctrl.getTask().entitlement().customerGroup().isPorsche() ||
                                ctrl.getTask().entitlement().customerGroup().isHyundai()
                            )
                        ) && MobilityTaskService.getBusinessRules().agileEnabled
                    );
                },
                selectOption: true,

                modelOptions: {
                    getterSetter: true,
                    allowInvalid: true
                },
                today: new Date()
            });
        }
    ])
    .filter('trueFalse', () => {
        return (text, length, end) => {
            if (text) {
                return 'Yes';
            }
            return 'No';
        };
    })
    .filter('arrayJoin', () => {
        return (arr) => {
            if (arr) {
                return arr.join('').replace(' ,', '');
            }
        };
    })
    .filter('dateToAge', () => {
        return (date) => {
            if (date) {
                let dateOfBirth = moment(date),
                    years = null,
                    months = null;
                years = moment(new Date()).diff(dateOfBirth, 'year');
                dateOfBirth.add(years, 'years');
                months = moment(new Date()).diff(dateOfBirth, 'months');
                return `${years} Y ${months ? `${months} M` : ``}`;
            }
        };
    });
