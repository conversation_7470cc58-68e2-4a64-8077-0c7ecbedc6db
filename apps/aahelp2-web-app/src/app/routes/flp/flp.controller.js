const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    FormattingUtil = require('@aa/utilities/formatting'),
    FlpTask = require('@aa/malstrom-models/lib/flp-task.model');
require('angular');

module.exports = angular
    .module('aah-flp-controller-module', [
        require('../../services/flp/flp-types.service').name,
        require('../../services/flp/flp-actions.service').name,
        require('../../services/flp/flp-reasons.service').name,
        require('../../services/csh/csh.service').name,
        require('../../services/flp/flp-task.service').name,
        require('../../services/user-info/user-info.service').name,
        require('../../services/task/task.service').name,
        require('../../services/csh/csh.service').name,
        require('../../services/prompt/prompt.service').name,
        require('../../components/custom-validators/flp-cost-validator.directive').name,
        require('../../services/uac/uac.service').name,
        require('../../components/uac/uac.directive').name,
        require('../../components/infobox/infobox.directive').name
    ])
    .controller('aahFlpController', [
        '$http',
        '$state',
        '$stateParams',
        '$scope',
        '$interval',
        '$timeout',
        'aahFlpTypesService',
        'aahFlpActionsService',
        'aahFlpReasonsService',
        'aahFlpTaskService',
        'aahUserInfoService',
        'aahTaskService',
        'cshData',
        'aahCSHService',
        'aahUACService',
        'aahPromptService',
        'envService',
        function FlpController(
            $http,
            $state,
            $stateParams,
            $scope,
            $interval,
            $timeout,
            FlpTypesService,
            FlpActionsService,
            FlpReasonsService,
            FlpTaskService,
            UserInfoService,
            TaskService,
            CshData,
            CSHService,
            UACService,
            PromptService,
            EnvService
        ) {
            let ctrl = this,
                _isReasonChanged = false;
            let flpTask = new FlpTask();
            let mainTask;
            const namespace = UACService.namespaces.FLP;
            let uacAuthData;
            const flpTypesWithUAC = EnvService.read('FLP_TYPES_WITH_UAC');

            function queueFlpTask(task) {
                var address = new Address();

                _.forEach(mainTask.entitlement().memberAddress(), function forEachAddressLine(line) {
                    address.addressLines().push(line);
                });
                address.postcode(mainTask.entitlement().memberAddress()[mainTask.entitlement().memberAddress().length - 1]);
                task.address(address);
                task.cardName(mainTask.contact().name());
                task.operatorId(UserInfoService.userInfo().operatorId());
                task.customerRequestId(mainTask.customerRequestId());
                task.contact(mainTask.contact());
                task.taskId(0);
                task.flpTypeText(FlpTaskService.getTaskType(task.taskTypeId1()));
                task.busLocnId(UserInfoService.userInfo().businessLocationId());
                task.originatorId(UserInfoService.userInfo().operatorId());
                task.originatorForename(UserInfoService.userInfo().forename());
                task.originatorSurname(UserInfoService.userInfo().surname());

                if (uacAuthData) {
                    // preserve UAC req id in the flp
                    const { requestId, deny } = uacAuthData;
                    task.uacRequestId(requestId);
                    task.uacRejected(deny);
                }

                FlpTaskService.addTask(task, uacAuthData);
            }

            _.extend(ctrl, {
                getNamespace: () => namespace,
                flpTypeDisabled: () => $stateParams.flpType !== null,
                init: () => {
                    mainTask = TaskService.task();

                    // if flp type provided
                    if ($stateParams.flpType) {
                        flpTask.taskTypeId1($stateParams.flpType);
                    }
                },
                isAuthorisationVisible: () => {
                    // require type & reason to even show authorisation
                    if (!flpTask.taskTypeId1() || !flpTask.reasonId()) {
                        return false;
                    }

                    // show if required
                    return this.isAuthorisationRequired();
                },
                isAuthorisationRequested: () => {
                    return !!(uacAuthData && uacAuthData.requestId);
                },
                isAuthorisationRequired: () => {
                    // we want authorization via UAC to happen only to the specific groups
                    const custGroup = mainTask.entitlement().customerGroup();
                    if (!custGroup.isPersonal() && !custGroup.isBank()) {
                        return false;
                    }

                    // if cust group valid for authorization and type of flp correct
                    return flpTypesWithUAC.includes(flpTask.taskTypeId1());
                },
                isDecisionProvided: () => {
                    if (!uacAuthData) {
                        return false;
                    }
                    return !!(uacAuthData.requestId && (uacAuthData.authCode || uacAuthData.deny === true));
                },
                isAuthorisationRejected: () => {
                    if (!uacAuthData) {
                        return false;
                    }
                    return !!(uacAuthData.requestId && uacAuthData.deny === true);
                },
                isAuthorisationProvided: () => {
                    if (!uacAuthData) {
                        return false;
                    }
                    return !!(uacAuthData.authCode && uacAuthData.requestId && !uacAuthData.deny);
                },
                getVrn: () => mainTask.vehicle().registration(),
                getTaskId: () => mainTask.id(),
                modelOptions: {
                    getterSetter: true
                },
                flpTypes: function flpTypesAccessor() {
                    return FlpTypesService.flpTypes();
                },
                flpReasons: function flpReasonsAccessor() {
                    return FlpReasonsService.flpReasons();
                },
                flpActions: function flpActionsAccessor() {
                    return FlpActionsService.flpActions();
                },
                getFlpTask: function getFlpTask() {
                    return flpTask;
                },
                createFlpTask: function createFlpTask() {
                    const flpApproved = !this.isAuthorisationRejected();
                    if (!this.flpHasMandatoryInfo()) {
                        return;
                    }

                    // make sure we have a task to push it to pending tasks array
                    // those tasks will be send later when main task is saved
                    queueFlpTask(flpTask);

                    return this.closePrompt(flpApproved);
                },
                disableOkBtn: function disableOkBtn() {
                    if (!this.flpHasMandatoryInfo()) {
                        return true;
                    }

                    return this.isAuthorisationRequired() && !this.isDecisionProvided();
                },
                flpHasMandatoryInfo: function flpHasMandatoryInfo() {
                    return flpTask.actionTakenId() && flpTask.taskTypeId1() && flpTask.reasonId();
                },
                getMemberName: function getMemberName() {
                    //pfu and fleet customers its the driver name which should be the member name.
                    return mainTask.altContact().name() ? mainTask.altContact().name() : mainTask.contact().name();
                },
                membershipNumber: function membershipNumber() {
                    if (CshData.entitlement) {
                        return CshData.entitlement.policy().customerGroup().isBank()
                            ? FormattingUtil.maskString(CshData.entitlement.policy().membershipNumber(), 4)
                            : CshData.entitlement.policy().membershipNumber();
                    }
                    return null;
                },
                getTaskType: function getTaskType(flpTaskId) {
                    return FlpTaskService.getTaskType(flpTaskId);
                },
                closePrompt: function closePrompt(successful = true) {
                    if ($stateParams.promptReferrer) {
                        // if successful, resolve prompt
                        if (successful) {
                            $stateParams.promptReferrer.resolve(true);
                        } else {
                            // else trigger prompt that show state
                            const nextPrompt = $stateParams.promptReferrer.prompt();
                            // chain next prompt
                            $stateParams.promptReferrer.resolve(PromptService.addPrompt(nextPrompt));
                        }
                    }

                    $state.go('contact');
                },
                cancel: function cancel() {
                    this.closePrompt(false);
                },
                reasonChanged: function reasonChanged() {
                    ctrl.isReasonChanged = false;
                    var selectedReason = _.find(FlpReasonsService.flpReasons(), function (reason) {
                        return reason.id() === flpTask.reasonId();
                    });
                    flpTask.reasonText(selectedReason.flpReasonDesc());
                    ctrl.isReasonChanged = !!selectedReason;
                },
                actionTakenChanged: function actionTakenChanged() {
                    var selectedAction = _.find(FlpActionsService.flpActions(), function (action) {
                        return action.id() === flpTask.actionTakenId();
                    });
                    flpTask.actionTakenText(selectedAction.flpActionDesc());
                },
                onUACData: function onAuthData(namespace, requestId, authCode, isEmergency) {
                    uacAuthData = { namespace, requestId, authCode, isEmergency };
                },
                onUACAuthorised: (namespace, requestId, authCode, isEmergency, decisionReason) => {
                    uacAuthData = { namespace, requestId, authCode, isEmergency, decisionReason };
                },
                onUACRejected: (namespace, requestId, decisionReason) => {
                    uacAuthData = { namespace, requestId, decisionReason, deny: true };
                }
            });
        }
    ]);
