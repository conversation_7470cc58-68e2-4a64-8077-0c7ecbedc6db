var _ = require('lodash');
require('angular');

var RefId = require('@aa/malstrom-models/lib/ref-id.model');
//models
var Fault = require('@aa/malstrom-models/lib/fault.model');
var ContractValidation = require('@aa/malstrom-models/lib/contract-validation.model');
var Prompt = require('@aa/malstrom-models/lib/prompt.model');
var MembershipQuery = require('@aa/malstrom-models/lib/membership-query.model');

module.exports = angular
    .module('aah-vehicle-controller-module', [
        'cfp.hotkeys',

        //directives
        require('../../../components/diagnostics/diagnostics.directive').name,
        require('../../../components/eurohelp/eurohelp.directive').name,

        // services
        require('../../../services/vehicle/vehicle.service').name,
        require('../../../services/task/task.service').name,
        require('../../../services/diagnostics/diagnostics.service').name,
        require('../../../services/mapping/location/location.service').name,
        require('../../../services/search/search.service').name,
        require('../../../services/csh/csh.service').name,
        require('../../../services/text-topic/text-topic.service').name,
        require('../../../services/prompt/prompt.service').name,
        require('../../../services/connected-car/connected-car.service').name,
        require('../../../services/contract-validation/contract-validation.service').name,
        require('../../../services/tab/tab.service').name,
        require('../../../services/entitlement/entitlement-helper.service').name,
        require('../../../services/demand-deflection/demand-deflection.service').name,

        // constants
        require('../../../constants/vehicle/vehicle-type.constants').name
    ])
    .controller('aahVehicleController', [
        '$location',
        '$anchorScroll',
        '$timeout',
        'aahVehicleService',
        'aahTaskService',
        'aahSearchService',
        'aahDiagnosticsService',
        'aahLocationService',
        'aahTextTopicService',
        'commonFaults',
        'vehicleTypes',
        'vehicleMakes',
        'serviceType',
        'connectedCarData',
        'lastSearchVehicle',
        'hotkeys',
        'aahCSHService',
        'aahPromptService',
        'aahTaskTypeConstants',
        'aahConnectedCarService',
        'aahContractValidationService',
        'aahVehicleTypes',
        'aahTabService',
        'aahEntitlementHelperService',
        'aahDemandDeflectionService',
        function VehicleController(
            $location,
            $anchorScroll,
            $timeout,
            VehicleService,
            TaskService,
            SearchService,
            DiagnosticsService,
            LocationService,
            TextTopicService,
            commonFaults,
            vehicleTypes,
            vehicleMakes,
            serviceType,
            connectedCarData,
            lastSearchVehicle,
            hotKeys,
            CSHService,
            PromptService,
            TaskType,
            ConnectedCarService,
            ContractValidationService,
            VehicleTypesConstant,
            TabService,
            EntitlementHelperService,
            DemandDeflectionService
        ) {
            var ctrl = this,
                _connectedCarData = connectedCarData,
                _lastSearch = lastSearchVehicle,
                _show = false,
                _showTrailer = false,
                _showIds = [],
                _asisstanceData = [],
                _showQA = false,
                _warrantyEntitlement = null,
                isStorageTick = false,
                isRepairTick = false,
                isAbandonmentTick = false,
                invalids = {
                    fault: {
                        $invalid: true,
                        $error: {
                            $required: true
                        }
                    }
                };
            function _hideFDDSAccountField() {
                $timeout(function () {
                    angular
                        .element(document)
                        .find('.form-group')
                        .each(function () {
                            var label = angular.element(this).find('label.control-label');

                            if (label.text().trim() === 'FDDS Account Name') {
                                angular.element(this).css('visibility', 'hidden');
                                angular.element(this).css('display', 'none');
                            }
                            if (label.text().trim() === 'FDDS Code') {
                                angular.element(this).css('visibility', 'hidden');
                                angular.element(this).css('display', 'none');
                            }
                        });
                }, 500); // Delay to ensure the third-party library renders the HTML
            }

            // Call the function when the controller initializes
            _hideFDDSAccountField();
            function _clearDiagnosticsQA() {
                DiagnosticsService.clearDiagnosticsQA();
                if (TaskService.task().entitlement().customerGroup().isPersonal()) {
                    _showQA = false;
                } else {
                    _showQA = true;
                    DiagnosticsService.initialiseDiagnosticsForVBMs();
                }
            }

            function _doRoadWorthyCheck() {
                var vehicleReg = ctrl.getVehicle().registration();
                if (ContractValidationService.isToggledOn()) {
                    VehicleService.roadworthyCheck(vehicleReg).then(function success(roadworthyDetails) {
                        if (roadworthyDetails) {
                            TaskService.task().vehicle().roadworthy(roadworthyDetails);
                        }
                        if (!TaskService.task().contractValidation()) {
                            TaskService.task().contractValidation(new ContractValidation());
                        }

                        TaskService.task().validateContract();
                    });
                    //TODO: remove this call that shows a popup to mock data
                    if (ContractValidationService.mockDataToggleOn()) {
                        ContractValidationService.showMockDataPopup();
                    }
                }
            }

            function _onVehicleSearchFailed() {
                var _reg, _typeId;

                PromptService.showInvalidVehicleVRNPrompt();

                //remove any previous settings keeping the reg..
                _reg = TaskService.task().vehicle().registration();
                _typeId = TaskService.task().vehicle().typeId();
                TaskService.task().vehicle().reset();
                TaskService.task().vehicle().registration(_reg);
                TaskService.task().vehicle().typeId(_typeId);

                // check reg validity
                const vrn = TaskService.task().vehicle().registration();
                if (vrn && VehicleService.validateVRN(vrn)) {
                    // set VRN check result as passed to allow further processing
                    TaskService.task().contractValidation().vrn().passed(true);
                }
            }

            function _selectValidEntitlement(entitlements) {
                const currentDate = new Date();
                return entitlements.find((entitlement) => {
                    const customerGroup = entitlement.policy().customerGroup();
                    return (
                        entitlement.policy().startDate() < currentDate &&
                        entitlement.policy().endDate() > currentDate &&
                        (customerGroup.isPFU() || customerGroup.isFleet() || customerGroup.isManufacturer())
                    );
                });
            }

            function _changeVehicle(data) {
                _clearDiagnosticsQA();

                if (data && !data.vehicle.roadworthy().registrationNumber()) {
                    _doRoadWorthyCheck();
                }

                ctrl.checkOverweightVehicle(data.vehicle.experianDetails().grossWeight());

                //go and get the relevant common faults if the type has changed..
                if (TaskService.task().vehicle().typeId() != data.vehicle.typeId()) {
                    DiagnosticsService.getCommonFaultsByTypeId(
                        data.vehicle.typeId(),
                        serviceType.code(),
                        TaskService.task().createReason().id(),
                        TaskService.task().entitlement().customerGroup().code()
                    );

                    VehicleService.loadMakesByType(data.vehicle.typeId()).then(function success(makeList) {
                        vehicleMakes = makeList;
                    });
                }

                // to give a popup when user enters an EV reg
                if (data.vehicle.experianDetails().fuel() === 'ELECTRIC') {
                    PromptService.electricVehicleprompt();
                }

                TaskService.updateVehicle(data.vehicle);

                // set VRN as passed when changed vehicle
                const vrn = TaskService.task().vehicle().registration();
                if (vrn && VehicleService.validateVRN(vrn)) {
                    // set VRN check result as passed to allow further processing
                    TaskService.task().contractValidation().vrn().passed(true);
                }

                //clean up rest of UI
                LocationService.removeRelayMarker();
                SearchService.vehicleSearchTerm().search(null);

                //Adds vehicle type it's if not avilable in types
                if (!VehicleService.isVehicleTypeExist(vehicleTypes, data.type)) {
                    vehicleTypes.push(new RefId(data.type));
                }

                // as we changed vehicle reg lets check demand deflection
                // we do not forceCheck (last param) because changing vehicle reg resets the fault
                PromptService.checkDemandDeflection(CSHService.entitlement(), TaskService.task());
            }

            function _processQuestionAnswerPromise(data) {
                var nextQuestion = data.question;

                if (nextQuestion && nextQuestion.isHidden()) {
                    //if question only has 1 possible answer, auto select the answer and remove from QA list i.e. QA list should only contain user selected answers
                    nextQuestion.selectedAnswer(_.head(nextQuestion.answers()));
                    DiagnosticsService.diagnosticsQAList().splice(DiagnosticsService.diagnosticsQAList().indexOf(nextQuestion)); //remove question from QA list
                    return ctrl.processQuestionAnswer(nextQuestion);
                }

                return Promise.resolve();
            }

            function resetConnectedCarUi() {
                _connectedCarData = null;
            }

            function isVehicleMembership() {
                return CSHService.entitlement().policy().membershipType() && CSHService.entitlement().policy().membershipType().toLowerCase() === 'vehicle';
            }

            _.extend(ctrl, {
                modelOptions: {
                    getterSetter: true
                },
                isVin: function isVin() {
                    //temporary fix for Vin
                    var vehicleReg = ctrl.getVehicle().registration();
                    return vehicleReg && vehicleReg.length === VehicleTypesConstant.EXPECTED_VIN_LENGTH ? true : false;
                },
                initialise: async function initialise() {
                    // to give a popup when user has single vehicle and its EV clicled on assistance Tab
                    if (TaskService.task().vehicle().experianDetails().fuel() === 'ELECTRIC') {
                        PromptService.electricVehicleprompt();
                    }

                    _showQA = DiagnosticsService.diagnosticsQAList().length > 0;

                    TextTopicService.showTextTopicsForCategories(['REG_DATA', 'DIAG_DATA', 'VEHICLE_DATA']);
                    if (!TaskService.task().entitlement().customerGroup().isPersonal()) {
                        _showQA = !TaskService.task().isCompleted();
                    }
                    if (TaskService.task().entitlement().customerGroup().showCustomMileage()) {
                        _showIds = [886];
                    } else {
                        _showIds = [730];
                    }
                    if (TaskService.task().createReason().isLogistics()) {
                        _showQA = false;
                    }

                    const isEuroHelp = TaskService.task().entitlement().customerGroup().isEuroHelp();
                    const isManualDiag = TaskService.task().createReason().generalBehaviours().includes('GB_MANUAL_DIAG');
                    if (isEuroHelp && isManualDiag) {
                        _showQA = false;
                    }

                    //Show conflicting records prompt on vehicle based membership search and vehicle different from vehicle of that membership
                    if (isVehicleMembership() && CSHService.entitlement() && CSHService.entitlement().vehicle().registration() !== ctrl.getVehicle().registration() && !ctrl.isVin()) {
                        PromptService.showConflictingRecordsFound();
                    }
                    if (!VehicleService.lastSearchVehicle()) {
                        ctrl.checkOverweightVehicle(TaskService.task().vehicle().experianDetails().grossWeight());
                        let vehicleReg = ctrl.getVehicle().registration();
                        VehicleService.lastSearchVehicle(vehicleReg);
                    }
                    _asisstanceData = await TaskService.getAssistanceCheckbox();
                    if (TaskService.completionCodes().length) {
                        _asisstanceData.map((item) => {
                            if (TaskService.completionCodes().includes(item.completionCode)) {
                                item.selected = true;
                            } else {
                                item.selected = false;
                            }
                        });
                    }
                },
                getAssistanceCheckbox: () => {
                    return _asisstanceData;
                },
                disableAssistanceCheckbox: () => {
                    return TaskService.task().status() === 'COMP';
                },
                checkOverweightVehicle: function checkOverweightVehicle(vehicleGrossWeight) {
                    if (
                        parseInt(vehicleGrossWeight) >= VehicleTypesConstant.GROSS_WEIGHT_LIMIT &&
                        !TaskService.task().entitlement().customerGroup().isPFU() &&
                        !TaskService.task().entitlement().customerGroup().isAdmiral()
                    ) {
                        PromptService.vehicleGrossWeightOverLimit(TaskService.task().entitlement().customerGroup().code());
                    }
                    if (parseInt(vehicleGrossWeight) > VehicleTypesConstant.GROSS_WEIGHT_LIMIT_ADMIRAL && TaskService.task().entitlement().customerGroup().isAdmiral()) {
                        PromptService.vehicleAdmiralGrossWeightOverLimit();
                    }
                },
                selectedCommonFault: function selectedCommonFaultAccessor() {
                    if (DiagnosticsService.selectedCommonFault() && DiagnosticsService.selectedCommonFault().id() > 0) {
                        return DiagnosticsService.selectedCommonFault();
                    } else {
                        return null;
                    }
                },
                isActiveFault: function (fault) {
                    let faultName = fault && fault.name() ? fault.name().toUpperCase() : null,
                        selectedFault = ctrl.selectedFault() && ctrl.selectedFault().name() ? ctrl.selectedFault().name().toUpperCase() : null,
                        selectedCommonFault = ctrl.selectedCommonFault() && ctrl.selectedCommonFault().name() ? ctrl.selectedCommonFault().name().toUpperCase() : null;
                    if (selectedFault === faultName || selectedCommonFault === faultName) {
                        return true;
                    }
                    return false;
                },
                commonFaultSelected: function (fault) {
                    // this shouldn't be invoked if task is completed
                    DiagnosticsService.clearDiagnosticsQA();
                    DiagnosticsService.diagnosticsQAList([]);

                    if (TaskService.task().indicators().motorway() || TaskService.task().indicators().dangerousLocation()) {
                        //reset relay to tab details
                        LocationService.resetRelayToDetails();
                    }
                    DiagnosticsService.selectedCommonFault(fault);
                    TaskService.setDirectRecovery(false);
                    //Reset relay tab on selecting fault
                    TabService.clearRelay();

                    // only for recovery keep the recovery assigned by server
                    if (!TaskService.task().createReason().isLogistics()) {
                        TaskService.task().recovery().fault(new Fault());
                    }

                    _showQA = true;
                    ctrl.processCommonFault(fault).then(() => {
                        // as we changed fault lets check demand deflection (we ignore diagnostic questions, focus on common faults)
                        PromptService.resetOutcomeObject(TaskService.task());

                        const forceCheckBecauseFaultIsFB = fault.code().code() === 'FB';
                        PromptService.checkDemandDeflection(CSHService.entitlement(), TaskService.task(), { forceCheck: forceCheckBecauseFaultIsFB });
                    });
                },
                processCommonFault: function processCommonFault(fault) {
                    return DiagnosticsService.processCommonFault(fault).then(function processQuestionAnswerHandler(data) {
                        if (data.fault) {
                            _showQA = false;
                        } else {
                            return _processQuestionAnswerPromise(data);
                        }
                    });
                },
                processQuestionAnswer: function processQuestionAnswer(question, answer) {
                    if (answer) {
                        question.selectedAnswer(answer);
                    }
                    return DiagnosticsService.processQuestionAnswer(question).then(function processQuestionAnswerHandler(data) {
                        $location.hash('bottom-of-answered-questions');
                        $anchorScroll();
                        if (data.fault) {
                            _showQA = !TaskService.task().entitlement().customerGroup().isPersonal();
                            if (!TaskService.task().entitlement().customerGroup().isPersonal() && !TaskService.task().isCompleted()) {
                                DiagnosticsService.initialiseDiagnosticsForVBMs();
                            }
                            return Promise.resolve();
                        } else {
                            return _processQuestionAnswerPromise(data);
                        }
                    });
                },
                getIconClass: function getIconClass(fault) {
                    return DiagnosticsService.getFaultIconClass(fault);
                },
                getTask: function getTask() {
                    return TaskService.task();
                },
                searchVehicle: function searchVehicle() {
                    //start a vehicle search..
                    var vehicleReg = ctrl.getVehicle().registration();
                    resetConnectedCarUi();
                    PromptService.removePromptById(Prompt.CONFLICTING_RECORDS_FOUND);

                    if ((vehicleReg && _lastSearch && vehicleReg !== _lastSearch) || (vehicleReg && !_lastSearch)) {
                        _lastSearch = vehicleReg;
                        VehicleService.lastSearchVehicle(_lastSearch);
                        SearchService.vehicleSearchTerm().search(vehicleReg);

                        // Search vehicle by VRN
                        SearchService.vehicleSearch()
                            .then(function searchSuccess(data) {
                                var searchQuery = SearchService.membershipSearchTerm();
                                searchQuery.param().vehicleRegistrationNumber(vehicleReg.toLowerCase());

                                const vrn = TaskService.task().vehicle().registration();
                                if (vrn && VehicleService.validateVRN(vrn)) {
                                    // set VRN check result as passed to allow further processing
                                    TaskService.task().contractValidation().vrn().passed(true);
                                }

                                const entitlement = CSHService.entitlement();
                                // check whether entitlement is present and task has vehicle based membership or task is not new.
                                if (entitlement && entitlement.riskCode() !== 'WJ' && entitlement.vehicle().registration()) {
                                    // clear membership serach
                                    SearchService.resetMembershipSearchModel();
                                    // set search term as new VRN
                                    SearchService.membershipSearchTerm(
                                        new MembershipQuery({
                                            search: vehicleReg
                                        })
                                    );
                                    // search Membership associated with VRN
                                    SearchService.membershipSearch()
                                        .then(function searchSuccessMembership(result) {
                                            const newEntitlement = _selectValidEntitlement(result);
                                            EntitlementHelperService.checkAdmiralEV(newEntitlement);

                                            if (newEntitlement) {
                                                PromptService.checkVehicleChanged(vehicleReg, CSHService.entitlement()).then((prompt) => {
                                                    if (prompt && prompt instanceof Prompt && prompt.selectedOption().optionType() === 'YES') {
                                                        if (TaskService.task().isNew() && newEntitlement && newEntitlement.policy() && newEntitlement.policy().customerGroup()) {
                                                            EntitlementHelperService.getSummary(newEntitlement, newEntitlement.policy().customerGroup()).then((entitlementSummary) => {
                                                                TaskService.task().entitlement(entitlementSummary);
                                                                EntitlementHelperService.selectedEntitlement(newEntitlement);
                                                                CSHService.entitlement(newEntitlement);
                                                            });
                                                        } else {
                                                            // Fetching a same task again because we lost vehicle details of current task.
                                                            TaskService.fetchTaskById(TaskService.task().id()).then(function (fetchedTask) {
                                                                // Switch task will completes the current task and loads new task.
                                                                EntitlementHelperService.switchTask(newEntitlement, fetchedTask, serviceType.code());
                                                            });
                                                        }
                                                    } else {
                                                        _changeVehicle(data);
                                                    }
                                                });
                                            } else {
                                                PromptService.showVehicleChanged().then((prompt) => {
                                                    _changeVehicle(data);
                                                });
                                            }
                                        })
                                        .catch(function () {
                                            PromptService.showVehicleChanged().then((prompt) => {
                                                _changeVehicle(data);
                                            });
                                        });
                                } else if (data && data.vehicle && vehicleReg.toLowerCase() !== data.vehicle.registration().toLowerCase() && !ctrl.isVin()) {
                                    //temporary fix for VIN

                                    PromptService.showConflictingRecordsFound();
                                } else {
                                    _changeVehicle(data);
                                }
                            })
                            .catch((ex) => {
                                return _onVehicleSearchFailed();
                            })
                            .finally(_doRoadWorthyCheck);

                        ConnectedCarService.getTelematicsData(vehicleReg).then(
                            (telematicsData) => {
                                if (telematicsData) {
                                    _connectedCarData = telematicsData;
                                }
                            },
                            () =>
                                (_connectedCarData = {
                                    message: 'Unable to complete telematics search.'
                                })
                        );

                        if (TaskService.task().entitlement().customerGroup().isPersonal()) {
                            SearchService.vehicleWarrantySearch(TaskService.task().entitlement().customerGroup().code()).then(function vehicleWarrantySearchSuccess() {
                                PromptService.showVehicleHasAdditionalPolicies(TaskService.task(), SearchService.getWarrantyEntitlements().length).then(function promptSuccess(showPolicies) {
                                    if (showPolicies) {
                                        SearchService.prepareWarrantyEntitlementsForDisplay();
                                        TextTopicService.showTextTopicsPanel(false);
                                    }
                                });
                            });
                        }
                    }
                },
                connectedCarData: function () {
                    return _connectedCarData;
                },
                searchTerm: function searchTermAccessor(val) {
                    return arguments.length ? SearchService.vehicleSearchTerm().search(val) : SearchService.vehicleSearchTerm().search();
                },
                getVehicle: function vehicleAcessor() {
                    return TaskService.task().vehicle();
                },
                types: function typesAcessor() {
                    return vehicleTypes;
                },
                makes: function makesAcessor() {
                    return vehicleMakes;
                },
                models: function modelsAcessor() {
                    return VehicleService.models(TaskService.task().vehicle().typeId());
                },
                isVehicleDisabled: function isVehicleDisabled() {
                    return TaskService.task().uiStatus().vehicle();
                },
                commonFaults: function () {
                    return commonFaults;
                },
                experianShown: function experianShown() {
                    return _show;
                },
                toggleExperian: function toggleExperian() {
                    _show = !_show;
                },
                trailerShown: function trailerShown() {
                    return _showTrailer;
                },
                toggleTrailer: function toggleTrailer() {
                    _showTrailer = !_showTrailer;
                },
                diagnosticQuestions: function diagnosticQuestionsAccessor() {
                    return DiagnosticsService.diagnosticsQAList();
                },
                previouslyAnsweredQuestions: function previouslyAnsweredQuestions() {
                    return TaskService.task().fault().diagnosticsQAList();
                },
                isNonCommonFault: function isNonCommonFault() {
                    let isInSet, selectedFaultName;
                    selectedFaultName = ctrl.selectedFault().name() ? ctrl.selectedFault().name().toUpperCase() : null;
                    isInSet = _.find(commonFaults, function (item) {
                        return item.name().toUpperCase() === selectedFaultName;
                    });
                    if (TaskService.task().fault().additionalInfo() && TaskService.task().fault().additionalInfo().toUpperCase().indexOf('SELF SERVICE') > -1 && !isInSet) {
                        return true;
                    }
                    return TaskService.task().fault().diagnosticsQAList().length > 0 && !isInSet;
                },
                selectedFault: function selectedFault() {
                    return TaskService.task().fault();
                },
                showQA: function showQAAccessor(val) {
                    if (arguments.length) {
                        _showQA = val;
                        if (_showQA === true) {
                            DiagnosticsService.resetQuestionsAndAnswers();
                            PromptService.resetOutcomeObject(TaskService.task());
                        }
                    }

                    return _showQA;
                },
                warrantyEntitlements: function warrantyEntitlements() {
                    return SearchService.getWarrantyEntitlements();
                },
                warrantyEntitlement: function warrantyEntitlementAccessor(val) {
                    return arguments.length ? (_warrantyEntitlement = val) : _warrantyEntitlement;
                },
                vehicleTypeChanged: function vehicleTypeChanged() {
                    _clearDiagnosticsQA();
                    TaskService.task().vehicle().makeId(-1);
                    TaskService.task().vehicle().modelId(-1);

                    VehicleService.loadMakesByType(TaskService.task().vehicle().typeId()).then(function (makeList) {
                        vehicleMakes = makeList; // update with new list
                    });

                    DiagnosticsService.getCommonFaultsByTypeId(
                        TaskService.task().vehicle().typeId(),
                        serviceType.code(),
                        TaskService.task().createReason().id(),
                        TaskService.task().entitlement().customerGroup().code()
                    );
                },
                vehicleMakeChanged: function vehicleMakeChanged() {
                    _clearDiagnosticsQA();
                    TaskService.task().vehicle().modelId(-1);
                    VehicleService.loadModels(ctrl.getVehicle().makeId());
                },
                vehicleModelChanged: function vehicleModelChanged() {
                    _clearDiagnosticsQA();
                },
                isFaultDisabled: function isFaultDisabled() {
                    return TaskService.task().uiStatus().fault() || TaskService.task().status() === 'COMP' || TaskService.task().status() === 'CLSD';
                },
                showCommonFaults: function showCommonFaults() {
                    return ctrl.isFaultDisabled() ? false : TaskService.task().vehicle().validVehicle();
                },
                showIds: function () {
                    // additional fields to display
                    return _showIds;
                },
                showOtherFaultsOption: function () {
                    return ctrl.showQA() && ctrl.selectedFault().id() === -1 && !ctrl.selectedCommonFault() && TaskService.task().createReason().isBreakdown();
                },
                displayOtherFaultsButton: function () {
                    return TaskService.task().createReason().serviceType() === 'RSS';
                },
                isInvalid: function isInvalid(val) {
                    return true;
                },
                commonFaultModel: function commonFaultModel() {
                    if (TaskService.task().fault().code().code()) {
                        invalids.fault.$invalid = false;
                        invalids.fault.$error.fault = false;
                    } else {
                        invalids.fault.$invalid = true;
                        invalids.fault.$error.fault = true;
                    }

                    return invalids.fault;
                },
                taskConditions: function taskConditions() {
                    return ctrl.validateTaskType() || ctrl.validategoodWill();
                },
                validateTaskType: function validateTaskType() {
                    return _asisstanceData.length > 0 && _asisstanceData[0].name === 'Drive-In' && ctrl.getTask().taskType().code() === TaskType.BREAKDOWN;
                },
                validategoodWill: function validategoodWill() {
                    return _asisstanceData.length > 1 && _asisstanceData[1].name === 'Goodwill';
                },
                validateEuroHelpTaskType: function validateEuroHelpTaskType() {
                    return ctrl.getTask().entitlement().customerGroup().isEuroHelp() && ctrl.getTask().taskType().code() === TaskType.BREAKDOWN;
                },
                validateTaskType: function validateTaskType() {
                    return _asisstanceData.length > 0 && _asisstanceData[0].name === 'Drive-In' && ctrl.getTask().taskType().code() === TaskType.BREAKDOWN;
                },
                validategoodWill: function validategoodWill() {
                    return _asisstanceData.length > 1 && _asisstanceData[1].name === 'Goodwill';
                },
                disableDriveIn: function disableDriveIn() {
                    return TaskService.task().status() === 'COMP';
                },
                completionCode: function completionCode() {
                    return TaskService.completionCode();
                },
                selectCompletionCode: function selectCompletionCode(val) {
                    if (arguments.length && val != undefined) {
                        _asisstanceData.filter((a) => {
                            return a.completionCode == val.completionCode;
                        }).selected = true;
                        TaskService.completionCodes(
                            _asisstanceData
                                .filter((e) => {
                                    return e.selected;
                                })
                                .map((i) => {
                                    return i.completionCode;
                                })
                        );
                    }
                    return _asisstanceData;
                },
                driveIn: function driveIn(val) {
                    return arguments.length ? TaskService.driveIn(val) : TaskService.driveIn();
                },
                goodWill: function goodWill(val) {
                    return arguments.length ? TaskService.goodWill(val) : TaskService.goodWill();
                },
                disableStorage: function disableStorage() {
                    return TaskService.task().status() === 'COMP';
                },
                storage: function storage(val) {
                    if (arguments.length) {
                        this.isStorageTick = val;
                    } else {
                        this.isStorageTick = false;
                    }
                    ctrl.checkRepairAbandonment();
                    return arguments.length ? TaskService.storage(val) : TaskService.storage();
                },
                disableRepair: function disableRepair() {
                    return TaskService.task().status() === 'COMP';
                },
                repair: function repair(val) {
                    if (arguments.length) {
                        this.isRepairTick = val;
                    } else {
                        this.isRepairTick = false;
                    }
                    ctrl.checkStorageAbandonment();
                    return arguments.length ? TaskService.repair(val) : TaskService.repair();
                },
                disableAbandonment: function disableAbandonment() {
                    return TaskService.task().status() === 'COMP';
                },
                abandonment: function abandonment(val) {
                    if (arguments.length) {
                        this.isAbandonmentTick = val;
                    } else {
                        this.isAbandonmentTick = false;
                    }
                    ctrl.checkStorageRepair();
                    return arguments.length ? TaskService.abandonment(val) : TaskService.abandonment();
                },

                checkRepairAbandonment: function checkRepairAbandonment() {
                    if (this.isStorageTick) {
                        ctrl.repair(false);
                        this.isRepairTick = false;
                        ctrl.abandonment(false);
                        this.isAbandonmentTick = false;
                    }
                },
                checkStorageAbandonment: function checkStorageAbandonment() {
                    if (this.isRepairTick) {
                        ctrl.storage(false);
                        this.isStorageTick = false;
                        ctrl.abandonment(false);
                        this.isAbandonmentTick = false;
                    }
                },
                checkStorageRepair: function checkStorageRepair() {
                    if (this.isAbandonmentTick) {
                        ctrl.storage(false);
                        this.isStorageTick = false;
                        ctrl.repair(false);
                        this.isRepairTick = false;
                    }
                }
            });
        }
    ]);
