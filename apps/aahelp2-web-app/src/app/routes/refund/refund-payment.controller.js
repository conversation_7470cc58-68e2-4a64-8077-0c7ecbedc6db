var _ = require('lodash');
require('angular');

var AlertTypes = require('../../constants/alert/alert-type.constants');
var SaveCardTask = require('@aa/malstrom-models/lib/save-card-task.model');
var FinancialTask = require('@aa/malstrom-models/lib/financial-task.model');
var RefundReasonConstant = require('@aa/malstrom-models/lib/constants/refund-reason.constants');
var PaymentReason = require('@aa/malstrom-models/lib/payment-reason.model');
var SavedCardPayment = require('@aa/malstrom-models/lib/save-card-txn-task.model');
var RefundEnquiry = require('@aa/malstrom-models/lib/refund-enquiry.model');

module.exports = angular
    .module('aah-refund-payment-controller-module', [
        require('../../services/driver-details/driver-details.service').name,
        require('../../services/alert/alert.service').name,
        require('../../services/payment/payment.service').name,
        require('../../components/payment-gateway/payment-gateway.directive').name,
        require('../../services/user-info/user-info.service').name,
        require('../../constants/payment/payment-types.constants').name,
        require('../../constants/payment/payment.constants').name,
        require('../../services/csh-search-viewer/csh-search-viewer.service').name,
        require('../../services/blob-upload/blob-upload.service').name,
        require('../../components/upload-button/upload-button.directive').name,
        require('../../services/electronic-documents/electronic-documents.service').name
    ])
    .controller('aahRefundPaymentController', [
        '$scope',
        '$state',
        '$q',
        '$stateParams',
        'aahDriverDetailsService',
        'aahAlertService',
        'aahTaskService',
        'aahPaymentService',
        'aahUserInfoService',
        'aahPaymentTypes',
        'aahPaymentConstants',
        'aahCSHSearchViewerService',
        'aahCSHService',
        'aahBlobUploadService',
        'aahElectronicDocumentsService',
        '$uibModal',
        function RefundPaymentController(
            $scope,
            $state,
            $q,
            $stateParams,
            DriverDetailsService,
            AlertService,
            TaskService,
            PaymentService,
            UserService,
            PaymentTypes,
            PaymentConstants,
            CSHSearchViewerService,
            CSHService,
            BlobUploadService,
            ElectronicDocumentsService,
            $uibModal
        ) {
            var ctrl = this,
                _tokenDebounce,
                _token,
                _shouldShowPayment,
                _saveCardTasks = [],
                _supportDocs = [],
                _documentEndpoint,
                selectedDocumentIndex = 0,
                _finTaskId,
                _finTaskDet;

            let imageTemplate = `
				<div>
					<img id="uploadedDoc" src="{{imageSource}}" />
					<br/>
					<button class="btn btn-secondary-action" type="button" ng-click="cancel()">Close</button>
					<button class="btn btn-secondary-action" type="button" onclick="window.print()">Print</button>
				</div>
				`;
            let pdfTemplate = `
				<div>
				<object data={{imageSource}}
						type='application/pdf'
						width='100%'
						height='500px'>
				<embed src={{imageSource}} width="600" height="500" alt="pdf" pluginspage="http://www.adobe.com/products/acrobat/readstep2.html">
					<br/>
					<button class="btn btn-secondary-action" type="button" ng-click="cancel()">Close</button>
				</div>
				`;
            function getBase64Img(callback) {
                var xhr = new XMLHttpRequest();
                xhr.onreadystatechange = function () {
                    if (xhr.readyState == XMLHttpRequest.DONE) {
                        callback(xhr.responseText);
                    }
                };
                xhr.open('GET', _documentEndpoint, true);
                xhr.send(null);
            }

            function _finalisePayment(custReqId) {
                if ($stateParams.promptReferrer) {
                    $stateParams.promptReferrer.resolve(true);
                }
                let tryFetch = function () {
                    TaskService.tasksByCustomerRequest(custReqId).then((tasksList) => {
                        CSHService.loadTasks(tasksList);
                        ctrl._showSpinner = false;
                        $state.go('payment.refundsuccess');
                    });
                };
                setTimeout(tryFetch, 5000);
            }

            function _failedPayment(custReqId) {
                if ($stateParams.promptReferrer) {
                    $stateParams.promptReferrer.resolve(true);
                }
                let tryFetch = function () {
                    TaskService.tasksByCustomerRequest(custReqId).then((tasksList) => {
                        CSHService.loadTasks(tasksList);
                        ctrl._showSpinner = false;
                        $state.go('payment.refundfail');
                    });
                };
                setTimeout(tryFetch, 5000);
            }

            const base64toBlob = (data) => {
                const base64WithoutPrefix = data.substr('data:application/pdf;base64,'.length);

                const bytes = atob(base64WithoutPrefix); //TODO: use Buffer.from(str, 'base64')Buffer.from(str,'base64')
                let length = bytes.length;
                let out = new Uint8Array(length);

                while (length--) {
                    out[length] = bytes.charCodeAt(length);
                }

                return new Blob([out], { type: 'application/pdf' });
            };

            var _defaultModelScope = new SavedCardPayment();
            var _refundModelScope = new RefundEnquiry();
            var _paymentReasonModelScope = new PaymentReason();
            var _refundType = ['Rebate'];
            var _refundReasons;

            _.extend(ctrl, {
                init: () => {
                    ctrl._showSpinner = false;
                    _supportDocs = [];
                    ctrl.changeDataModelScope.viewDocDisabled = true;
                    ctrl.changeDataModelScope.deleteDocDisabled = true;
                    PaymentService.getSavedCard(TaskService.task().customerRequestId()).then(function (response) {
                        _saveCardTasks = response;
                        PaymentService.getFinancialTaskDetail($stateParams.financialTaskId)
                            .then((resp) => {
                                _defaultModelScope = new SavedCardPayment(resp.data.savedCardPayment);
                                _finTaskDet = resp.data.savedCardPayment;
                                ctrl._selectedCard = _saveCardTasks.filter((item) => {
                                    return item.taskId() == _finTaskDet.savedCardTaskId;
                                })[0];
                                ctrl._cardNumber = ctrl._selectedCard.cardDetails().accountNumber();
                                ctrl._operatorId = resp.data.savedCardPayment.operatorId;
                                ctrl._paymentReason = resp.data.savedCardPayment.paymentReason;
                                ctrl._dateFineWasIssued = resp.data.savedCardPayment.miscellaneous.invoiceDetails.dateFineWasIssued;
                                ctrl._dateFineWasPaidByAA = resp.data.savedCardPayment.miscellaneous.invoiceDetails.dateFineWasPaidByAA;
                                var _isAdminFeeRefund = resp.data.savedCardPayment.paymentReasonId == 164 || resp.data.savedCardPayment.paymentReasonId == 165;
                                ctrl._amount = _isAdminFeeRefund ? 0 : Math.round(resp.data.savedCardPayment.amount * 100) / 100; //In case of admin fee business wants it to be viewed as admin fee only
                                ctrl._adminFee = _isAdminFeeRefund ? Math.round(resp.data.savedCardPayment.amount * 100) / 100 : resp.data.savedCardPayment.miscellaneous.invoiceDetails.adminFee;
                                ctrl._lexInvoiceReference = resp.data.savedCardPayment.miscellaneous.lexInvoiceReference;

                                if (resp.data.savedCardPayment.status === 'S') {
                                    return (ctrl._paymentStatus = 'SUCCESS');
                                }
                                if (resp.data.savedCardPayment.status === 'P') {
                                    return (ctrl._paymentStatus = 'PENDING');
                                }
                                if (resp.data.savedCardPayment.status === 'CR') {
                                    return (ctrl._paymentStatus = 'CREATE');
                                }
                                if (resp.data.savedCardPayment.status === 'F') {
                                    return (ctrl._paymentStatus = 'FAILED');
                                }
                                if (resp.data.savedCardPayment.status === 'C') {
                                    return (ctrl._paymentStatus = 'CANCELLED');
                                }
                                if (resp.data.savedCardPayment.status === 'IN') {
                                    return (ctrl._paymentStatus = 'INPROGRESS');
                                }
                            })
                            .then(() => {
                                _defaultModelScope.miscellaneous().refundDetails().orderId($stateParams.orderNumber);
                                PaymentService.getRefundableAmount($stateParams.orderNumber, _defaultModelScope.taskId()).then((resp) => {
                                    _refundModelScope = new RefundEnquiry(resp.data);
                                    _refundModelScope.refundableAmount(resp.data.refundableAmount);
                                    ctrl._refundMaxAmtVar = resp.data.refundableAmount;
                                });
                                _defaultModelScope.assocTaskId(_finTaskDet.taskId);
                                _defaultModelScope.taskId(null);
                                PaymentService.addFinancialTask(_defaultModelScope, (addPaymentResp) => {
                                    _finTaskId = addPaymentResp.data.savedCardPayment.taskId;
                                    _defaultModelScope.taskId(_finTaskId);
                                });
                            });
                    });

                    _refundReasons = PaymentService.getRefundReasons();
                },
                onSelectCard: (model) => {
                    if (model) {
                        ctrl._cardReferrence = model.cardReferrence();
                    } else {
                        ctrl._cardReferrence = null;
                    }
                },
                changeDataModelScope: () => {
                    return _defaultModelScope;
                },
                changeDataRefundModelScope: () => {
                    return _refundModelScope;
                },
                paymentReasonModel: () => {
                    return _paymentReasonModelScope;
                },
                supportDocumentsList: function supportDocumentsList() {
                    return _supportDocs.map((doc) => {
                        let x = 'Document created at ' + new Date(doc.createdTime()).toLocaleString();
                        doc.message = x;
                        return doc;
                    });
                },
                documentSelected: (val) => {
                    _documentEndpoint = val[0].documentId().substr(2);
                    ctrl.changeDataModelScope.viewDocDisabled = false;
                    if (!ctrl.changeDataModelScope.paymentButtonHidden) {
                        ctrl.changeDataModelScope.deleteDocDisabled = false;
                    }
                    selectedDocumentIndex = _supportDocs.findIndex((item) => item.documentId() === val[0].documentId());
                },
                viewDocument: () => {
                    getBase64Img((base64RespText) => {
                        let source = base64RespText;
                        if (base64RespText.length > 0) {
                            const isPDF = base64RespText.indexOf('data:application/pdf;base64,') > -1;
                            let modalInstance = $uibModal.open({
                                animation: true,
                                controllerAs: 'ctrl',
                                controller: function ($scope, $uibModalInstance) {
                                    if (isPDF) {
                                        source = URL.createObjectURL(base64toBlob(base64RespText));
                                    }
                                    $scope.imageSource = source;
                                    $scope.cancel = function () {
                                        $uibModalInstance.close(false);
                                    };
                                },
                                template: () => {
                                    return isPDF ? pdfTemplate : imageTemplate;
                                }
                            });
                        } else {
                            AlertService.createAlert('Document acess denied', AlertTypes.INFO);
                        }
                    });
                },
                deleteDocument: () => {
                    let docId = ctrl.defaultDocumentModel[0].documentId().split('/')[4].split('?')[0];
                    ElectronicDocumentsService.deleteDocument(docId).then(() => {
                        _supportDocs.splice(selectedDocumentIndex, 1);
                    });
                    ctrl.changeDataModelScope.deleteDocDisabled = true;
                    ctrl.changeDataModelScope.viewDocDisabled = true;
                },
                addDocument: function addDocs(doc) {
                    return ElectronicDocumentsService.documentToken(_finTaskId, TaskService.task().customerRequestId()).then((sasToken) => {
                        if (sasToken != undefined) {
                            BlobUploadService.uploadBlobStream(sasToken, doc, (response) => {
                                if (response.result._response.status == 201) {
                                    ElectronicDocumentsService.listByFinTaskId(_finTaskId).then((resp) => {
                                        _supportDocs = resp;
                                    });
                                    AlertService.createAlert('Document Uploaded', AlertTypes.INFO);
                                    ctrl.changeDataModelScope.deleteDocDisabled = true;
                                    ctrl.changeDataModelScope.viewDocDisabled = true;
                                } else {
                                    AlertService.createAlert('Error in uploading the document', AlertTypes.INFO);
                                }
                            });
                        } else {
                            AlertService.createAlert('Document Upload Failed', AlertTypes.INFO);
                        }
                    });
                },
                refundPayment: function refundPayment() {
                    if (_defaultModelScope.miscellaneous().refundDetails().refundTypeCode() === '' || _defaultModelScope.miscellaneous().refundDetails().refundTypeCode() === null) {
                        AlertService.createAlert('Please Select the Refund Type.', AlertTypes.INFO);
                        return false;
                    }

                    if (_defaultModelScope.miscellaneous().refundDetails().refundTypeCode() === 'Rebate') {
                        if (ctrl.refundPaymentForm.maxRefundAmtRebate.$modelValue <= 0) {
                            AlertService.createAlert('Full refund amount is already processed. No more refunds possible further.', AlertTypes.INFO);
                            return false;
                        }
                        if (ctrl.refundPaymentForm.refundAmount.$modelValue > ctrl.refundPaymentForm.maxRefundAmtRebate.$modelValue) {
                            AlertService.createAlert('Refund Amount cannot be Greater Than Maximum Refund Amount.', AlertTypes.INFO);
                            return false;
                        }
                        if (ctrl.refundPaymentForm.refundAmount.$modelValue == 0 || ctrl.refundPaymentForm.refundAmount.$modelValue === null || ctrl.refundPaymentForm.refundAmount === '') {
                            AlertService.createAlert('Refund Amount cannot be Zero.', AlertTypes.INFO);
                            return false;
                        }
                        if (ctrl.refundPaymentForm.refundAmount.$modelValue < 0) {
                            AlertService.createAlert('Refund Amount cannot be a negative No.', AlertTypes.INFO);
                            return false;
                        }
                        if (ctrl.refundPaymentForm.lexInvoiceReference === '' || ctrl.refundPaymentForm.lexInvoiceReference === null) {
                            AlertService.createAlert('Lex Invoice Number is Mandetory.', AlertTypes.INFO);
                            return false;
                        }

                        if (ctrl._refundReasonId === '' || ctrl._refundReasonId === null) {
                            AlertService.createAlert('Please select a Refund Reason.', AlertTypes.INFO);
                            return false;
                        }

                        if (window.confirm('Are you sure you wish to make a refund?')) {
                            _defaultModelScope.transactionType('rebate');
                            ctrl._showSpinner = true;
                            PaymentService.refundRebateAmount(_defaultModelScope, TaskService.task().customerRequestId())
                                .then((resp) => {
                                    if (resp.data.isTransactionSuccessful === true) {
                                        _finalisePayment(TaskService.task().customerRequestId());
                                    } else {
                                        _failedPayment(TaskService.task().customerRequestId());
                                    }
                                })
                                .catch(function (error) {
                                    _failedPayment(TaskService.task().customerRequestId());
                                });
                        } else {
                            return false;
                        }
                    }

                    if (_defaultModelScope.miscellaneous().refundDetails().refundTypeCode() === 'Credit') {
                        if (ctrl.refundPaymentForm.refundAmount.$modelValue > ctrl.refundPaymentForm.maxRefundAmtCredit.$modelValue) {
                            AlertService.createAlert('Refund Amount cannot be Greater Than Maximum Refund Amount.', AlertTypes.INFO);
                            return false;
                        }
                        if (ctrl.refundPaymentForm.refundAmount.$modelValue == 0) {
                            AlertService.createAlert('Refund Amount cannot be Zero.', AlertTypes.INFO);
                            return false;
                        }
                        if (ctrl.refundPaymentForm.refundAmount.$modelValue < 0) {
                            AlertService.createAlert('Refund Amount cannot be a negative No.', AlertTypes.INFO);
                            return false;
                        }
                        if (ctrl.refundPaymentForm.lexInvoiceReference === '' || ctrl.refundPaymentForm.lexInvoiceReference === null) {
                            AlertService.createAlert('Lex Invoice Number is Mandetory.', AlertTypes.INFO);
                            return false;
                        }
                        if (ctrl.refundPaymentForm.maxRefundAmtCredit.$modelValue <= 0) {
                            AlertService.createAlert('Maximum Refund Amount cannot be Zero.', AlertTypes.INFO);
                            return false;
                        }
                        if (ctrl._refundReasonId === '' || ctrl._refundReasonId === null) {
                            AlertService.createAlert('Please select a Refund Reason.', AlertTypes.INFO);
                            return false;
                        }
                        if (ctrl._cardReferrence === '' || ctrl._cardReferrence === null) {
                            AlertService.createAlert('Please select an Authorised Card to Refund.', AlertTypes.INFO);
                            return false;
                        }
                        if (window.confirm('Are you sure you wish to make a refund?')) {
                            _defaultModelScope.transactionType('credit');
                            ctrl._showSpinner = true;
                            PaymentService.creditRebateAmount(_defaultModelScope, TaskService.task().customerRequestId(), ctrl._cardReferrence)
                                .then((resp) => {
                                    if (resp.data.isTransactionSuccessful === true) {
                                        _finalisePayment(TaskService.task().customerRequestId());
                                    } else {
                                        _failedPayment(TaskService.task().customerRequestId());
                                    }
                                })
                                .catch(function (error) {
                                    _failedPayment(TaskService.task().customerRequestId());
                                });
                        } else {
                            return false;
                        }
                    }
                },
                changeRefundType: function changeRefundType(val) {
                    _defaultModelScope.miscellaneous().refundDetails().refundTypeCode(val);
                    if (val === 'Rebate') {
                        ctrl._hideSelectCard = false;
                        ctrl._hideCreditMaxAmt = false;
                        ctrl._hideRebateMaxAmt = true;
                        if (ctrl._refundMaxAmtVar <= 0) {
                            ctrl._paymentBtnDisable = true;
                            AlertService.createAlert('Full refund amount is already processed. No more refunds possible further.', AlertTypes.INFO);
                            return false;
                        }
                    }

                    if (val === 'Credit') {
                        PaymentService.getSavedCard(TaskService.task().customerRequestId()).then(function (response) {
                            _saveCardTasks = response;
                            ctrl._hideSelectCard = true;
                            ctrl._hideCreditMaxAmt = true;
                            ctrl._hideRebateMaxAmt = false;
                        });
                    }
                },
                getRefundReasons: () => {
                    return _refundReasons;
                },
                refundReasonSelected: (val) => {
                    if (val) {
                        ctrl._refundReasonId = val.id();
                        _defaultModelScope.miscellaneous().refundDetails().refundReasonDesc(val.description());
                        _defaultModelScope.miscellaneous().refundDetails().refundReasonId(val.id());
                    } else {
                        ctrl._refundReasonId = null;
                    }
                },
                getSavedCards: () => {
                    return _saveCardTasks;
                },
                getRefundType: () => {
                    return _refundType;
                },
                defaultDocumentModel: {},
                defaultSavedCardModel: {},
                defaultRefundReasonModel: {},
                defaultRefundTypeModel: {},
                _refundReasonId: null,
                _cardNumber: null,
                _selectedCard: null,
                _paymentStatus: null,
                _operatorId: null,
                _paymentReason: null,
                _dateFineWasIssued: null,
                _dateFineWasPaidByAA: null,
                _amount: null,
                _adminFee: null,
                _lexInvoiceReference: null,
                _cardReferrence: null,
                _hideSelectCard: null,
                _hideRebateMaxAmt: null,
                _hideCreditMaxAmt: null,
                _showSpinner: true,
                _refundMaxAmtVar: null,
                _paymentBtnDisable: false,
                selectOption: true,
                modelOptions: {
                    getterSetter: true,
                    allowInvalid: true
                }
            });
        }
    ]);
