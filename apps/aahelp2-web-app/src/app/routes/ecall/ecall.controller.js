var _ = require('lodash');
require('angular');
const { CustomerGroup, Vehicle } = require('@aa/data-models/common');
var EcallTask = require('@aa/malstrom-models/lib/ecall-task.model');
var Location = require('@aa/malstrom-models/lib/location.model');
var RefCode = require('@aa/malstrom-models/lib/ref-code.model');

const { countries, countryCodes, countryDialCodes } = require('@aa/malstrom-models/lib/constants/country-code.constants');
var geolib = require('geolib');

module.exports = angular
    .module('aah-ecall-controller-module', [
        require('../../constants/error/error.constants').name,
        require('../../services/ecall/ecall.service').name,
        require('../../services/completion/completion.service').name,
        require('../../services/csh/csh.service').name,
        require('../../services/task/task.service').name,
        require('../../services/search/search.service').name,
        require('../../services/mapping/mapping.service').name,
        require('../../constants/csh/csh-urls.constants').name,
        require('../../constants/task/task-type.constants').name,
        require('../../services/ui/ui.service').name,
        require('../../components/toggle/toggle.directive').name,
        require('../../constants/namespace/namespace.constants').name
    ])
    .controller('aahEcallController', [
        '$http',
        '$q',
        '$scope',
        'aahEcallService',
        'aahCSHURLs',
        'aahTaskService',
        'aahCSHService',
        'aahTaskTypeConstants',
        'aahCompletionService',
        'aahMappingService',
        'aahUIService',
        'aahSearchService',
        'aahAuditService',
        'aahSidePanelService',
        'aahNamespace',
        'aahConsentDisplayName',
        function EcallController(
            $http,
            $q,
            $scope,
            EcallService,
            CSHURLs,
            TaskService,
            CSHService,
            TaskType,
            CompletionService,
            MappingService,
            UIService,
            SearchService,
            AuditService,
            SidePanelService,
            namespace,
            ConsentDisplayName
        ) {
            const ctrl = this,
                easting = '',
                northing = '',
                w3w = '';

            ctrl.countryDialCodes = countryDialCodes;
            ctrl.selectedCountryCode = countryDialCodes['GB'].dialCode;

            // get cross-border ecall outcome
            const crossBorderOutcome = CompletionService.getEcallCodes().find((entry) => {
                return entry.code() === 'EL';
            });
            const triggers = [
                {
                    code: EcallTask.arcTwinTriggers.AUTOMATIC,
                    name: 'Automated'
                },
                {
                    code: EcallTask.arcTwinTriggers.MANUAL,
                    name: 'Manually'
                }
            ];

            const brands = [
                { name: 'Jaguar Land Rover', code: CustomerGroup.JGEC },
                { name: 'Smart', code: CustomerGroup.SMEC },
                { name: 'Lotus', code: CustomerGroup.LTEC },
                { name: 'Cross-border', code: CustomerGroup.XBEC }
            ];

            let oldLocation = null;
            let oldVehicle = null;
            let oldVehicleType = '';
            let oldConsents = {};
            let nonManualPreviousCustomerGroup = null;
            let trace;
            let searchVehiclePending = false;
            let searchVehicleFailed = false;
            let searchNearestCityPending = false;
            let searchNearestCityFailed = false;
            const ukCountryCode = countryCodes[countries.UNITED_KINGDOM].code;
            const ukCountryRef = new RefCode(countryCodes[countries.UNITED_KINGDOM]);

            const onSearchVehicle = (data) => {
                searchVehiclePending = false;
                // if failed search
                if (!data.vehicle) {
                    searchVehicleFailed = true;
                    return;
                }
                searchVehicleFailed = false;
                const oldMake = oldVehicle && oldVehicle.experianDetails().make();
                const oldModel = oldVehicle && oldVehicle.experianDetails().model();
                const oldColour = oldVehicle && oldVehicle.experianDetails().colour();

                // get type from backend service - that's a custom aa vehicle
                // type mapping, different from vehicle.typeId
                const newVehicleType = data.type && data.type.name;
                this.ecallTask().vehicleType(newVehicleType);

                const newVehicle = data.vehicle;
                const newMake = newVehicle.experianDetails().make();
                const newModel = newVehicle.experianDetails().model();
                const newColour = newVehicle.experianDetails().colour();

                EcallService.ecallTask().vehicle(newVehicle);

                if (oldVehicleType !== newVehicleType) {
                    AuditService.reportInteraction(trace, 'vehicle type', oldVehicleType, newVehicleType);
                }
                if (oldMake !== newMake) {
                    AuditService.reportInteraction(trace, 'vehicle make', oldMake, newMake);
                }
                if (oldModel !== newModel) {
                    AuditService.reportInteraction(trace, 'vehicle model', oldModel, newModel);
                }
                if (oldColour !== newColour) {
                    AuditService.reportInteraction(trace, 'vehicle colour', oldColour, newColour);
                }

                // decouple to protect prev data from change
                oldVehicle = new Vehicle(newVehicle.toJSON());
            };

            const onSearchNearestCity = (data) => {
                searchNearestCityPending = false;
                // TODO: if country diff from current country - show alert so operator can swap country
                const oldNearestCity = oldLocation && oldLocation.text();

                const newLocation = this.ecallTask().location();

                const nearestCityResult = data.find((entry) => {
                    return entry.types.find((type) => type === 'postal_town');
                });

                if (!nearestCityResult) {
                    searchNearestCityFailed = true;
                    return;
                }
                searchNearestCityFailed = false;

                const newNearestCity = nearestCityResult && nearestCityResult.formatted_address;
                // preserve nearest city
                newLocation.text(newNearestCity);

                if (oldNearestCity !== newNearestCity) {
                    AuditService.reportInteraction(trace, 'nearest city', oldNearestCity, newNearestCity);
                }

                // decouple to protect prev data from change
                oldLocation = new Location(newLocation.toJSON());
            };

            const adjustCrossBorderOutcome = () => {
                const task = EcallService.ecallTask();
                // check if AA is the end handler
                const ours = task.isDomestic() || task.isCrossBorderDomestic();
                // let's handle the outcome for cross border
                const outcome = task.outcome() && task.outcome().code();
                if (ours && (!outcome || outcome === crossBorderOutcome.code())) {
                    // if domestic but outcome set to cross-border reset outcome
                    task.outcome(null);
                } else if (!ours && (!outcome || outcome !== crossBorderOutcome.code())) {
                    // if cross-border make sure to set outcome to cross-border
                    task.outcome(crossBorderOutcome);
                }
            };

            const adjustCrossBorderCustGroup = () => {
                const task = EcallService.ecallTask();
                // check if AA is the end handler
                const crossBorder = task.isCrossBorder();
                const customerGroup = task.customerGroup();

                // if ecall triggered via call lets preserve cust group just in case someone changed its mind
                if (customerGroup && customerGroup !== CustomerGroup.XBEC) {
                    if (task.telephonyCLINumber()) {
                        nonManualPreviousCustomerGroup = customerGroup;
                    }
                }

                // let's handle the customer group for cross border
                if (crossBorder) {
                    task.customerGroup(CustomerGroup.XBEC);
                } else if (task.isDomestic()) {
                    // if domestic but customerGroup set to cross-border reset to previous customerGroup
                    task.customerGroup(nonManualPreviousCustomerGroup);
                }
            };

            const adjustCrossBorderCountry = () => {
                if (EcallService.ecallTask().homeCountry().code() === ukCountryCode) {
                    EcallService.ecallTask().homeCountry(new RefCode());
                } else {
                    EcallService.ecallTask().homeCountry(new RefCode(countryCodes[countries.UNITED_KINGDOM]));
                }

                ctrl.ecallTask().location().country(ukCountryRef);
            };

            const getNorthingEasting = () => {
                // Define the latitude and longitude coordinates
                const latitude = ctrl.ecallTask().location().coordinates().latitude();
                const longitude = ctrl.ecallTask().location().coordinates().longitude();

                // Convert decimal latitude and longitude to sexagesimal format
                const latDMS = geolib.decimal2sexagesimal(latitude);
                const longDMS = geolib.decimal2sexagesimal(longitude);

                // Format northing and easting coordinates
                ctrl.northing = `${latDMS} N`;
                ctrl.easting = `${longDMS} W`;
            };

            _.extend(ctrl, {
                ukCountryCode,
                isDisabled: () => {
                    // disable fields when sending
                    return EcallService.isSending();
                },
                searchVehiclePending: () => searchVehiclePending,
                searchVehicleFailed: () => searchVehicleFailed,
                searchNearestCityPending: () => searchNearestCityPending,
                searchNearestCityFailed: () => searchNearestCityFailed,
                customerGroup: (val) => {
                    let code;
                    if (typeof val === 'undefined') {
                        code = this.ecallTask().customerGroup();
                    } else {
                        code = this.ecallTask().customerGroup(val.code);
                    }

                    return brands.find((brand) => {
                        return brand.code === code;
                    });
                },
                arcTwinTrigger: (val) => {
                    let code;
                    if (typeof val === 'undefined') {
                        code = this.ecallTask().arcTwinTrigger();
                    } else {
                        code = this.ecallTask().arcTwinTrigger(val.code);
                    }

                    return triggers.find((trigger) => {
                        return trigger.code === code;
                    });
                },
                brands: () => brands,
                onFormChange: (id, prevValue, value) => {
                    // alter audit names
                    switch (id) {
                        case 'type':
                            AuditService.reportInteraction(trace, 'vehicle type', prevValue, value);
                            break;
                        case 'make':
                            AuditService.reportInteraction(trace, 'vehicle make', prevValue, value);
                            break;
                        case 'model':
                            AuditService.reportInteraction(trace, 'vehicle model', prevValue, value);
                            break;
                        case 'colour':
                            AuditService.reportInteraction(trace, 'vehicle colour', prevValue, value);
                            break;
                        default:
                            AuditService.reportInteraction(trace, id, prevValue, value);
                    }
                },
                isCrossBorderForeign: () => EcallService.ecallTask().isCrossBorderForeign(),
                onCrossBorderDomesticChange: () => {
                    adjustCrossBorderCountry();
                    adjustCrossBorderOutcome();
                    adjustCrossBorderCustGroup();
                },
                countries: () => EcallService.countries(),
                homeCountries: () => EcallService.countries().filter(this.filterHomeCountries),
                isSending: () => EcallService.isSending(),
                onIncidentCountryChange: () => {
                    adjustCrossBorderOutcome();
                    adjustCrossBorderCustGroup();
                },
                getHandler: () => EcallService.getHandler(),
                onCompValidationFailed: () => EcallService.onCompValidationFailed(),
                onConsentChange: () => {
                    const consents = EcallService.ecallTask().consents().getAll();

                    // check value change for each consent
                    for (const consent of consents) {
                        const prevValue = oldConsents[consent.name()] ? 'Consent granted' : 'Consent not granted';
                        const value = consent.granted() ? 'Consent granted' : 'Consent not granted';
                        if (prevValue !== value) {
                            const name = ConsentDisplayName[consent.name()];
                            AuditService.reportInteraction(trace, `Consent for: ${name}`, prevValue, value);
                        }

                        oldConsents[consent.name()] = consent.granted();
                    }
                },
                showError: (formField) => {
                    if (!formField) {
                        return;
                    }

                    // sho error if field invalid and either touched or user tried to complete task
                    // and validation failed
                    return formField.$invalid && (formField.$dirty || EcallService.onCompValidationFailed());
                },
                onBlurCustomerDDINumber: () => {
                    const customerDDINumber = EcallService.formatCustomerDDINumber(EcallService.ecallTask().customerDDINumber());
                    EcallService.ecallTask().customerDDINumber(customerDDINumber);
                },
                getHandlerDescription: () => {
                    const ecallTask = EcallService.ecallTask();
                    const handler = this.getHandler();

                    if (ecallTask.isDomestic() || ecallTask.isCrossBorderDomestic()) {
                        return `${handler.providerName} | we are a handler`;
                    }

                    return `${handler.providerName} | foreign handler`;
                },
                ecallTask: () => EcallService.ecallTask(),
                reasons: () => CompletionService.getEcallCodes(),
                getVehicle: () => EcallService.ecallTask().vehicle(),
                triggers: () => triggers,
                searchVehicle: function searchVehicle() {
                    const newVRN = ctrl.ecallTask().vehicle().registration();
                    const prevVRN = oldVehicle && oldVehicle.registration();

                    // If VRN did not change - bail out
                    if (prevVRN && prevVRN === newVRN) {
                        return;
                    }

                    // if vrn missing, dont query
                    if (!newVRN) {
                        return onSearchVehicle({ vehicle: null });
                    }

                    searchVehiclePending = true;
                    searchVehicleFailed = false;
                    return SearchService.vehicleSearch(newVRN)
                        .then(onSearchVehicle)
                        .catch(() => onSearchVehicle({ vehicle: null }));
                },

                /**
                 * Initialise ecall panel withs initial data if needed
                 */
                init: function init() {
                    // if view triggered by menu or navigating url
                    if (!EcallService.ecallTask()) {
                        EcallService.createEcallTask();
                    }

                    trace = AuditService.getTrace(namespace.ECALL, EcallService.auditId());

                    // set initial old values
                    oldVehicle = new Vehicle(ctrl.ecallTask().vehicle().toJSON());
                    oldVehicleType = ctrl.ecallTask().vehicleType();
                    oldLocation = new Location(ctrl.ecallTask().location().toJSON());

                    nonManualPreviousCustomerGroup = this.ecallTask().customerGroup();
                },
                modelOptions: {
                    getterSetter: true,
                    updateOn: 'default blur',
                    allowInvalid: true
                },
                searchNearestCity: () => {
                    const newLocation = ctrl.ecallTask().location();
                    const newLatLong = newLocation.coordinates();

                    if (oldLocation) {
                        const prevLatLong = oldLocation.coordinates();

                        // If LatLong did not change - bail out
                        if (newLatLong.latitude() === prevLatLong.latitude() && newLatLong.longitude() === prevLatLong.longitude()) {
                            return;
                        }
                    }

                    // if lat and long were copied into latitude field
                    if (newLatLong.latitude() && newLatLong.latitude().includes(' ')) {
                        const cordsArray = newLatLong.latitude().split(' ');
                        newLatLong.latitude(cordsArray[0]);
                        newLatLong.longitude(cordsArray[1]);
                    }

                    // if lat and long were copied into longitude field
                    if (newLatLong.longitude() && newLatLong.longitude().includes(' ')) {
                        const cordsArray = newLatLong.longitude().split(' ');
                        newLatLong.latitude(cordsArray[0]);
                        newLatLong.longitude(cordsArray[1]);
                    }

                    // if lat or long missing, don't query
                    if (!newLatLong.latitude() || !newLatLong.longitude()) {
                        return;
                    }

                    searchNearestCityPending = true;
                    searchNearestCityFailed = false;
                    SearchService.getW3WForLatLong(newLatLong.latitude(), newLatLong.longitude()).then((w3wwords) => {
                        ctrl.w3w = w3wwords;
                    });
                    getNorthingEasting();
                    return MappingService.latLngSearch(`${newLatLong.latitude()},${newLatLong.longitude()}`)
                        .then(onSearchNearestCity)
                        .catch(() => onSearchNearestCity([]));
                },
                filterHomeCountries: (country) => {
                    // if country other than uk let if be used
                    if (country.code() !== ukCountryCode) {
                        return true;
                    }
                    // don't show UK on the list of countries for domestic cross border
                    return !ctrl.ecallTask().isCrossBorderDomestic();
                }
            });
        }
    ]);
