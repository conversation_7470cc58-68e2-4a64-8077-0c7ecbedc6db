var _ = require('lodash');
require('angular');
require('angular-hotkeys');

module.exports = angular
    .module('aah-customer-service-details-search-controller-module', [
        'cfp.hotkeys',

        // directives ...showTaskLocked
        require('../../../components/task-detail-view/task-detail-view.directive').name,
        require('../../../components/adhoc-task-detail-view/adhoc-task-detail-view.directive').name,
        // services
        require('../../../services/task/task.service').name,
        require('../../../services/csh/csh.service').name,
        require('../../../services/side-panel/side-panel.service').name,

        require('../../../services/task-audit/task-audit.service').name,
        require('../../../services/callinfo/callinfo.service').name,
        require('../../../services/mapping/mapping.service').name,
        require('../../../services/alert/alert.service').name,
        require('../../../services/customer-group/customer-group.service').name,
        require('../../../services/user-info/user-info.service').name,
        require('../../../services/text-topic/text-topic.service').name,
        require('../../../services/service-type/service-type.service').name,
        require('../../../services/ui/ui.service').name,
        require('../../../services/prompt/prompt.service').name,
        require('../../../services/appointment/appointment.service').name,

        // constants
        require('../../../constants/csh/csh-order-by.constants').name,
        require('../../../constants/task/task-order-by.constants').name,
        require('../../../constants/alert/alert-type.constants').name,
        require('../../../constants/error/error.constants').name,

        require('../../../services/eligibility/eligibility.service').name,
        require('../../../services/search/search.service').name,
        require('../../../services/entitlement/entitlement-helper.service').name,
        require('../../../services/customer-group/customer-group.service').name,
        require('../../../services/completion/completion.service').name,
        require('../../../services/csh-search-viewer/csh-search-viewer.service').name,

        require('../../../services/task-operations/task-operations.service').name,
        require('../../../services/will-join/will-join.service').name
    ])
    .controller('aahCustomerServiceDetailsSearchController', [
        '$state',
        '$scope',
        'aahCSHService',
        'aahTaskService',
        'aahTaskAuditService',
        'aahCallinfoService',
        'aahMappingService',
        'aahAlertService',
        'aahCustomerGroupService',
        'aahUserInfoService',
        'aahServiceTypeService',
        'aahCshOrderBy',
        'aahTaskOrderBy',
        'uibAlertTypes',
        'aahEligibilityService',
        'aahSearchService',
        'aahEntitlementHelperService',
        'aahCompletionService',
        'hotkeys',
        'aahUIService',
        'aahTaskOperationsService',
        'aahCSHSearchViewerService',
        'aahErrorConstants',
        'aahPromptService',
        'aahSidePanelService',
        'aahAppointmentService',
        'aahWillJoinService',

        function CustomerServiceDetailsSearchController(
            $state,
            $scope,
            CSHService,
            TaskService,
            TaskAuditService,
            CallinfoService,
            MappingService,
            AlertService,
            CustomerGroupService,
            UserInfoService,
            ServiceTypeService,
            CshOrderBy,
            TaskOrderBy,
            AlertTypes,
            EligibilityService,
            SearchService,
            EntitlementHelperService,
            CompletetionService,
            hotKeys,
            UiService,
            TaskOperationsService,
            CSHSearchViewerService,
            Errors,
            PromptService,
            SidePanelService,
            AppointmentService,
            WillJoinService
        ) {
            var ctrl = this,
                _resultsAreAvailable = false,
                _showCustReqSummary = true,
                _cshOrderByObj = '', // set detault order by function
                _taskOrderByObj = '', // set detault order by function
                refreshUI = function refreshUI(task) {
                    CSHSearchViewerService.task(task);
                    MappingService.reset();
                    AppointmentService.reset();

                    if (task.location().isSet()) {
                        MappingService.addAssistanceMarker(task.location().coordinates().latitude(), task.location().coordinates().longitude());

                        MappingService.centerOnBreakdown();
                    }

                    // tmp marker until the problem with MappingService.showRecoverDestinationMarker is resolved ..
                    if (task.recovery().showMarker()) {
                        MappingService.addRelayMarker(task.recovery().destination().coordinates().latitude(), task.recovery().destination().coordinates().longitude());
                    }

                    // update audit ...
                    if (task.status() !== 'UNAC') {
                        TaskAuditService.getTransactionHistory(task.customerRequestId(), task.id(), CSHService.seLocator());
                        CSHService.newCaseBtnDisabled(false);
                    } else {
                        TaskAuditService.reset();
                    }

                    if (task.schedule().resource().location().isSet()) {
                        MappingService.addResource(task.schedule().resource());
                    }

                    MappingService.moveToBounds();

                    // create possible task locked alert
                    UiService.showTaskLocked(task.indicators().locked());

                    _showCustReqSummary = true;
                },
                loadCRTasks = function loadCRTasks(cr, taskList) {
                    CSHSearchViewerService.loadTasks(cr, taskList);
                    CSHService.reattendEnableDisable(taskList, cr.id());
                    _resultsAreAvailable = taskList.length > 0;
                    if (_resultsAreAvailable) {
                        //just show task list
                        _showCustReqSummary = true;
                    } else {
                        AlertService.createAlert('No tasks are available for this Customer Request', AlertTypes.DANGER);
                    }
                };

            hotKeys.bindTo($scope).add({
                combo: 'alt+m',
                description: 'Customer search history',
                allowIn: ['INPUT', 'SELECT', 'TEXTAREA'],
                callback: function () {
                    ctrl.toggleExpand();
                }
            });

            _.extend(ctrl, {
                filterByRole: EntitlementHelperService.isFilterByRoleEnabled(),
                toggleFilterByRole: () => {
                    ctrl.filterByRole = !ctrl.filterByRole;
                    EntitlementHelperService.loadTask(CSHService.entitlement(), null, ctrl.filterByRole, ctrl.filterByRole);
                },
                newCase: function newCase() {
                    CSHService.newCaseBtnDisabled(true);
                    PromptService.checkHasLiveTask(CSHService.cshResponse().customerRequestHistory()).then(function promptSuccess(isAddNewCase) {
                        if (isAddNewCase) {
                            SearchService.getEntitlementByCR(CSHService.csh()).then(function (crEntitlements) {
                                if (crEntitlements.unListedSlvEntitlement === null && crEntitlements.entitlements.length === 1) {
                                    const newEntitlement = crEntitlements.entitlements[0];
                                    return EntitlementHelperService.addNewCase(newEntitlement, ServiceTypeService.serviceType().code()) // need to inject ServiceType we already gone to contact
                                        .then(function createCRSuccess() {
                                            refreshUI(TaskService.task());
                                            PromptService.toggleMergeOptionWillJoin(false);
                                            PromptService.checkVulnerableCustomer(newEntitlement);
                                        });
                                } else {
                                    CSHService.newCaseBtnDisabled(false);
                                    AlertService.createAlert('Failed to find an entitlement', AlertTypes.DANGER);
                                }
                            });
                        } else {
                            PromptService.checkVulnerableCustomer();
                            CSHService.newCaseBtnDisabled(false);
                        }
                    });
                },
                cshOrderByObj: function cshOrderByAcessor(val) {
                    return arguments.length ? (_cshOrderByObj = val) : _cshOrderByObj;
                },
                taskOrderByObj: function taskOrderByAcessor(val) {
                    return arguments.length ? (_taskOrderByObj = val) : _taskOrderByObj;
                },
                taskOrderByList: function () {
                    return TaskOrderBy;
                },
                newCaseBtnDisabled: function () {
                    return CSHService.newCaseBtnDisabled();
                },
                cshOrderByList: function () {
                    return CshOrderBy;
                },
                modelOptions: {
                    getterSetter: true
                },
                back: function backToSearch() {
                    $state.go('home', {
                        resetAll: false
                    });
                },
                /**
                 * we have selected a task
                 * @param  {Task} task [description]
                 * @return {[type]}        [description]
                 */
                select: function selectTask(task) {
                    _.forEach(ctrl.getResults(), function _forEachTask(_task) {
                        //reset all CSH flags on all other cr tasks
                        if (_task !== task) {
                            _task.open = false;
                        }
                        _task.isExpanded = false;
                    });
                    SidePanelService.updateState(task);
                    if (task === CSHSearchViewerService.task() && task.open) {
                        //deselect
                        CSHSearchViewerService.task(null);
                    } else {
                        CSHSearchViewerService.task(task);
                    }
                },
                // search results
                getResults: function () {
                    if (CSHSearchViewerService.customerRequest()) {
                        return CSHSearchViewerService.customerRequest().tasks();
                    }
                },
                customerRequest: function () {
                    return CSHService.csh();
                },
                //make customer group available if the view shows in this route
                customerGroup: function customerGroupAccessor(val) {
                    return arguments.length ? CustomerGroupService.customerGroup(val) : CustomerGroupService.customerGroup();
                },
                customerRequests: function () {
                    return CSHService.getResults();
                },
                task: function () {
                    return TaskService.task();
                },
                extraOperations: function () {
                    return {};
                },
                /**
                 * show list of tasks
                 * @return {boolean}
                 */
                showResults: function showResults() {
                    return !CSHService.isAdvancedCshSearchVisible() && _resultsAreAvailable;
                },
                reset: function reset() {},
                /**
                 * toggle display of advance search
                 * @return {[type]} [description]
                 */
                advanced: function advanced() {
                    CSHService.isAdvancedCshSearchVisible(!CSHService.isAdvancedCshSearchVisible());
                },
                isAdvancedCshSearchVisible: function isAdvancedCshSearchVisibleAccessor(val) {
                    return arguments.length ? CSHService.isAdvancedCshSearchVisible(val) : CSHService.isAdvancedCshSearchVisible();
                },
                showCustReqSummary: function showCustReqSummaryAccessor(val) {
                    return arguments.length ? (_showCustReqSummary = val) : _showCustReqSummary;
                },
                selectCustomerRequest: function selectCustomerRequest(cr) {
                    if (CSHSearchViewerService.customerRequest().id() !== cr.id()) {
                        // fetch tasks for CR if
                        // CR is active & not in 'UNAC' - 'UNAC' means that this is a new CR and not in AAHELP
                        // there are no tasks under the CR
                        if ((cr.isActive() && cr.status().code() !== 'UNAC') || _.isEmpty(cr.tasks())) {
                            TaskService.tasksByCustomerRequest(cr.id()).then(function onSuccessFetchTasks(taskList) {
                                loadCRTasks(cr, taskList);
                            });
                        } else {
                            loadCRTasks(cr, cr.tasks());
                        }
                    } else {
                        _showCustReqSummary = true;
                        $state.go('summary');
                        CSHService.reattendEnableDisable(CSHSearchViewerService.customerRequest().tasks(), CSHSearchViewerService.customerRequest().id());
                    }
                },
                isWillJoin: function isWillJoin() {
                    return EligibilityService.isWillJoin() && $state.is('summary');
                },
                updateEntitlement: function updateEntitlement() {
                    SearchService.membershipSearch().then(
                        function membershipSearchSuccess(data) {
                            if (data.length) {
                                EntitlementHelperService.getSummary(_.head(data), CustomerGroupService.findCustomerGroupByCode('PERS')).then(function (entSum) {
                                    TaskService.task().entitlement(entSum);
                                    TaskService.task().entitlement().riskCode(null);
                                    EligibilityService.riskCode(null);
                                });
                            }
                        },
                        function membershipSearchFailure(response) {
                            var message = Errors.SYSTEM_ERROR_MESSAGE;
                            if (response.data && response.status === Errors.HTTP_INTERNAL_SERVER_ERROR_CODE) {
                                message = response.data.msg ? response.data.msg : response.data;
                            }
                            return AlertService.createAlert(message, AlertTypes.DANGER);
                        }
                    );
                },
                membershipNumber: function membershipNumber() {
                    return SearchService.membershipSearchTerm().param().membershipNumber;
                },
                complete: function complete(code, msg) {
                    if (TaskService.task().contact().name()) {
                        TaskService.task().fault().outcome().completionCode(code);
                        ctrl.task()
                            .location()
                            .remarks(msg + ctrl.task().location().remarks());

                        CompletetionService.completeTask();
                    }
                },
                getCustServiceHistory: function getCustServiceHistory() {
                    _showCustReqSummary = !_showCustReqSummary;
                    if (!_showCustReqSummary) {
                        CSHService.findBySeLocator(CSHService.csh().seLocatorId());
                    }
                }
            });
        }
    ]);
