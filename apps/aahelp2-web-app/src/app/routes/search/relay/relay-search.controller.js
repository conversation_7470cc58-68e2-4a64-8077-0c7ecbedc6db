var _ = require('lodash');
require('angular');

module.exports = angular
    .module('aah-relay-search-controller-module', [
        // services
        require('../../../services/search/search.service').name,
        require('../../../services/task/task.service').name,
        require('../../../services/mapping/mapping.service').name,
        require('../../../services/diagnostics/diagnostics.service').name,
        require('../../../services/ui/ui.service').name,
        require('../../../services/mapping/location/location.service').name,

        require('../../../services/mapping/context-menu/context-menu.service').name,
        require('../../../services/mapping/google/google.service').name,
        require('../../../constants/mapping/google-maps-place-types.constants').name,

        require('../../../factories/apply.factory').name,
        require('../../../helpers/processLocation/process-results.helper').name
    ])
    .controller('aahRelaySearchController', [
        '$rootScope',
        '$http',
        '$state',
        'aahApply',
        'aahContextMenuService',
        'aahSearchService',
        'aahTaskService',
        'aahMappingService',
        'aahDiagnosticsService',
        'aahGoogleService',
        'aahLocationTextTypes',
        'aahLocationAreaTypes',
        'aahUIService',
        'aahLocationService',
        'aahProcessResults',
        'aahDestResourceIDsConstants',
        function LocationSearchController(
            $rootScope,
            $http,
            $state,
            Apply,
            ContextMenuService,
            SearchService,
            TaskService,
            MappingService,
            DiagnosticsService,
            GoogleService,
            LocationTextTypes,
            LocationAreaTypes,
            UIService,
            LocationService,
            processResult,
            DestResourceIDsConstants
        ) {
            var ctrl = this,
                _processAddressComponents = require('../../../factories/construct-address-array.factory');

            _.extend(ctrl, {
                placeholderText: function placeHolderText() {
                    return 'Search relay destination...';
                },
                isDisabled: function isDisabled() {
                    return TaskService.task().uiStatus().relay();
                },
                task: function task() {
                    return TaskService.task();
                },
                search: function search() {
                    UIService.showRelayInfo(false);
                    SearchService.searchLocation().then(function () {
                        MappingService.moveToBounds();
                    });
                },
                getResults: function getResults() {
                    return SearchService.locationResults().compiled;
                },
                isDealer: () => SearchService.locationResults().dealers.length > 0,
                searchTerm: SearchService.locationSearch().search,
                postRender: function postRender() {},
                showMoreResults: function showMoreResults() {
                    console.log('TODO: Implement me');
                },
                select: function select(result, moveOnly) {
                    var geocodeObj, poiLatLng, position;

                    if (ctrl.task().recovery().destResourceId() && result.properties && result.properties.supResourceId !== ctrl.task().recovery().destResourceId()) {
                        TaskService.attendedScheduleWindow(false);
                        TaskService.recoveryOptionOrRepairerChanged(true);
                    }

                    if (result.index > 1) {
                        UIService.showRelayInfo(true);
                    }
                    // Reset relay questions and answers when user change location
                    DiagnosticsService.initialiseDiagnosticsQA();

                    if (result.hasOwnProperty('addressAsString')) {
                        geocodeObj = {
                            address: result.addressAsString()
                        };
                    } else if (result.formatted_address || result.vicinity) {
                        GoogleService.places().getDetails(
                            {
                                placeId: result.place_id,
                                fields: [
                                    'address_component',
                                    'adr_address',
                                    'formatted_address',
                                    'geometry',
                                    'icon',
                                    'name',
                                    'business_status',
                                    'photo',
                                    'place_id',
                                    'plus_code',
                                    'type',
                                    'url',
                                    'vicinity'
                                ]
                            },
                            function (detailedResult) {
                                processResult({
                                    latLng: result.geometry.location,
                                    text: _processAddressComponents(detailedResult.address_components, LocationTextTypes, result.name),
                                    area: _processAddressComponents(detailedResult.address_components, LocationAreaTypes),
                                    placeTypes: detailedResult.types,
                                    moveOnly: moveOnly || false,
                                    resourceId: null
                                });
                            }
                        );
                    } else if (result.properties.categoryId) {
                        if (result.properties.categoryId === 4505 && result.properties.prevJunc.latitude !== 0 && result.properties.prevJunc.longitude !== 0) {
                            //services need to be positioned at the previous junction to ensure correct garage is assigned
                            position = GoogleService.latLng(result.properties.prevJunc.latitude, result.properties.prevJunc.longitude);
                        } else {
                            position = GoogleService.latLng(result.geometry.coordinates[1], result.geometry.coordinates[0]);
                        }

                        return GoogleService.geocode({
                            location: position
                        }).then(function (results) {
                            processResult({
                                latLng: position,
                                text: LocationService.processGarageAddress(result) || _processAddressComponents(results[0].address_components, LocationTextTypes, result.name || ''),
                                area: result.properties.motorway ? result.properties.area : _processAddressComponents(results[0].address_components, LocationAreaTypes),
                                placeTypes: results[0].types,
                                moveOnly: moveOnly || false,
                                resourceId: result.properties.supResourceId ? result.properties.supResourceId : null,
                                flags: {
                                    // only set for motorway and dangerous locations ..
                                    motorway: result.properties.motorway,
                                    dangerousLocation: result.properties.dangerousLocation
                                },
                                properties: result.properties
                            }).then(function () {
                                var finalResult,
                                    checkDangerous = true;

                                if (result.properties.categoryId) {
                                    switch (result.properties.categoryId) {
                                        case 4505: //motorway services
                                            TaskService.task().indicators().motorway(false);
                                            TaskService.task().indicators().dangerousLocation(false);
                                            checkDangerous = false;
                                            break;
                                        case 4514: //motorway junction
                                        case 4504: //motorway marker posts
                                        case 4503: //sos boxes
                                            TaskService.task().indicators().motorway(true);
                                            TaskService.task().indicators().dangerousLocation(true);
                                            TaskService.task().recovery().destResourceId(DestResourceIDsConstants.LOCAL);
                                            TaskService.setDirectRecovery(true);

                                            LocationService.refreshPriority();
                                            LocationService.setMotorwayRelayInformation(position.lat(), position.lng(), result);

                                            checkDangerous = false;
                                            break;
                                    }
                                }
                                console.warn('setting destResourceId to -1');
                                TaskService.task().recovery().destResourceId(-1);

                                if (results.length > 1) {
                                    finalResult = _.find(results, function (item) {
                                        return _.indexOf(item.types, 'route') >= 0;
                                    });
                                } else {
                                    finalResult = _.head(results);
                                }

                                if (checkDangerous) {
                                    LocationService.isMotorway(position, finalResult, true);
                                }
                            });
                        });
                    }

                    if (geocodeObj) {
                        GoogleService.geocode(geocodeObj).then(function geoCodePromiseResolved(results) {
                            processResult({
                                latLng: poiLatLng || results[0].geometry.location,
                                text: [results[0].name].concat(_processAddressComponents(results[0].address_components, LocationTextTypes)),
                                area: _processAddressComponents(results[0].address_components, LocationAreaTypes),
                                placeTypes: results[0].types,
                                moveOnly: moveOnly || false
                            });
                        });
                    }
                },
                goToCshView: function goToCshView() {
                    $state.go('location.relay.csh');
                }
            });
        }
    ]);
