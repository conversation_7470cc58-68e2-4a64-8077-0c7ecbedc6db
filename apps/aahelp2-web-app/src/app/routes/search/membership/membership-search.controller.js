var _ = require('lodash');
require('angular');

var Prompt = require('@aa/malstrom-models/lib/prompt.model');

module.exports = angular
    .module('aah-membership-search-controller-module', [
        // services
        require('../../../services/search/search.service').name,
        require('../../../services/task/task.service').name,
        require('../../../services/mapping/location/location.service').name,
        require('../../../services/callinfo/callinfo.service').name,
        require('../../../services/task-audit/task-audit.service').name,
        require('../../../services/entitlement/entitlement-helper.service').name,
        require('../../../services/alert/alert.service').name,
        require('../../../services/customer-group/customer-group.service').name,
        require('../../../services/mobile-app/mobile-app.service').name,
        require('../../../services/service-type/service-type.service').name,
        require('../../../services/prompt/prompt.service').name,
        require('../../../services/mapping/mapping.service').name,
        require('../../../services/csh/csh.service').name,
        require('../../../services/tab/tab.service').name,
        require('../../../factories/phone-numbers/is-mobile-phone-number.factory').name,
        require('../../../services/qualification/qualification.service').name,
        require('../../../services/supplier/supplier.service').name,
        require('../../../services/appointment/appointment.service').name,

        require('../../../constants/alert/alert-type.constants').name,
        require('../../../constants/error/error.constants').name,
        require('../../../services/bcaps-search-event/bcasp-search-event.service').name
    ])
    .controller('aahMembershipSearchController', [
        '$state',
        'aahCallinfoService',
        'aahSearchService',
        'aahTaskService',
        'aahLocationService',
        'aahTaskAuditService',
        'aahEntitlementHelperService',
        'aahAlertService',
        'aahCustomerGroupService',
        'aahMobileAppService',
        'aahServiceTypeService',
        'aahPromptService',
        'uibAlertTypes',
        'aahErrorConstants',
        'aahMappingService',
        'aahCSHService',
        'aahTabService',
        'aahQualificationService',
        'aahSupplierService',
        'aahAppointmentService',
        'aahBcaspEventService',
        function MembershipSearchController(
            $state,
            CallinfoService,
            SearchService,
            TaskService,
            LocationService,
            TaskAuditService,
            EntitlementHelperService,
            AlertService,
            CustomerGroupService,
            MobileAppService,
            ServiceType,
            PromptService,
            AlertTypes,
            Errors,
            MappingService,
            CSHService,
            TabService,
            QualificationService,
            SupplierService,
            AppointmentService,
            BcaspEventService
        ) {
            var ctrl = this,
                _disableSearchBtn = false;
            SearchService.isAdvancedMembershipSearchVisible(false);

            _.extend(ctrl, {
                // search box
                search: function search() {
                    BcaspEventService.searchTask();
                },
                isSearchDisabled: function getDisableSearch() {
                    return _disableSearchBtn;
                },

                reset: function reset() {
                    SearchService.resetMembershipSearchModel();
                },

                advanced: function advanced() {
                    SearchService.toggleAdvancedMembershipSearch();
                },

                advancedMembership: function advancedMembership() {
                    SearchService.isAdvancedMembershipSearchVisible(true);
                },

                searchTerm: SearchService.membershipSearchTerm().search,

                advancedMembershipQuery: SearchService.membershipSearchTerm().param,

                // search results
                getResults: SearchService.getMembershipResults,

                getCompanyResults: SearchService.getCompanyResults,

                loadTask: function loadTask(entitlement) {
                    //If entitlment, memeberstatus is stopped, then restrict from selecting entitlment
                    if (entitlement.isDisabled()) {
                        return true;
                    }
                    // if in qualification streams, and select the same membership, load the task with the qualification prompts
                    if (QualificationService.isWorkingOnQualificationTask() && CSHService.entitlement().policy().membershipNumber() === entitlement.policy().membershipNumber()) {
                        $state.go('csh');
                    } else {
                        MappingService.reset();
                        CSHService.reset();
                        TabService.reset();
                        SupplierService.reset();
                        //Retain will join prompt to merge with searched entitlement and reset all prompts except will join prompt and road-side-support.
                        PromptService.resetAllPromptsExceptIds([Prompt.WILL_JOIN_TASK, Prompt.VULNERABLE_CUSTOMER]);
                        PromptService.toggleMergeOptionWillJoin(true);
                        QualificationService.reset();
                        AppointmentService.reset();
                        TaskService.resetTask();
                        const filterByRole = EntitlementHelperService.isFilterByRoleEnabled(entitlement);
                        EntitlementHelperService.loadTask(entitlement, null, filterByRole);
                    }
                    if (entitlement.policy().customerGroup().isAdmiral()) {
                        let address = entitlement.contact().address();
                        TaskService.task()
                            .location()
                            .remarks(address.addressLines().join(', ') + ' ' + address.postcode());
                    }
                },

                callinfoList: CallinfoService.callinfoList,

                taskAuditList: TaskAuditService.taskAuditList,

                extraOperations: [],

                customerGroups: CustomerGroupService.customerGroups,

                isAdvancedMembershipSearchVisible: SearchService.isAdvancedMembershipSearchVisible,

                getTask: function getTask() {
                    return TaskService.task();
                },
                showMoreResults: function showMoreResults() {
                    SearchService.membershipSearch(true);
                },
                selectedEntitlement: function getSelectedEntitlement() {
                    return EntitlementHelperService.selectedEntitlement();
                },
                modelOptions: {
                    getterSetter: true
                }
            });
        }
    ]);
