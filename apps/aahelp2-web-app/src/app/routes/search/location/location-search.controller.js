var _ = require('lodash'),
    Prompt = require('@aa/malstrom-models/lib/prompt.model'),
    Fault = require('@aa/malstrom-models/lib/fault.model'),
    PriorityReason = require('@aa/malstrom-models/lib/priority-reason.model');

require('angular');
require('angular-hotkeys');

module.exports = angular
    .module('aah-location-search-controller-module', [
        'cfp.hotkeys',

        // services
        require('../../../services/search/search.service').name,
        require('../../../services/task/task.service').name,
        require('../../../services/mapping/mapping.service').name,
        require('../../../services/mapping/location/location.service').name,
        require('../../../services/mapping/context-menu/context-menu.service').name,
        require('../../../services/mapping/google/google.service').name,
        require('../../../services/prompt/prompt.service').name,
        require('../../../services/ui/ui.service').name,
        require('../../../helpers/processLocation/process-results.helper').name,
        require('../../../components/go-to-csh/go-to-csh.directive').name,
        require('../../../constants/mapping/google-maps-place-types.constants').name,
        require('../../../services/cti/line-management.service').name,
        require('../../../services/tab/tab.service').name,
        require('../../../services/diagnostics/diagnostics.service').name
    ])
    .controller('aahLocationSearchController', [
        '$q',
        '$state',
        'aahContextMenuService',
        'aahSearchService',
        'aahTaskService',
        'aahCSHService',
        'aahMappingService',
        'aahGoogleService',
        'aahPromptService',
        'aahUIService',
        'aahLocationTextTypes',
        'aahLocationAreaTypes',
        'aahPlaceTypes',
        'aahLocationService',
        'hotkeys',
        'aahProcessResults',
        'aahLineManagementService',
        'aahDiagnosticsService',
        'aahTabService',
        'aahDestResourceIDsConstants',
        function LocationSearchController(
            $q,
            $state,
            ContextMenuService,
            SearchService,
            TaskService,
            CshService,
            MappingService,
            GoogleService,
            PromptService,
            UIService,
            LocationTextTypes,
            LocationAreaTypes,
            PlaceTypes,
            LocationService,
            hotKeys,
            processResult,
            LineManagementService,
            DiagnosticsService,
            TabService,
            DestResourceIDsConstants
        ) {
            var ctrl = this,
                _processAddressComponents = require('../../../factories/construct-address-array.factory'),
                _mainLine = LineManagementService.getMainLine();

            _.extend(ctrl, {
                placeholderText: function placeHolderText() {
                    return 'Search location...';
                },
                isDisabled: function () {
                    return TaskService.task().uiStatus().location() || !TaskService.task().isMoveable();
                },
                // search box
                search: function search() {
                    UIService.showLocationInfo(false);
                    PromptService.removePromptById(Prompt.INACCURATE_LOCATION);
                    PromptService.removePromptById(Prompt.LOCAL_DRIVER_PROMPT);
                    SearchService.locationSearch().what3WordsSearch('');
                    SearchService.searchLocation().then(function (data) {
                        if (!data.compiled.length) {
                            SearchService.locationResults().compiled = [];

                            return;
                        }

                        MappingService.moveToBounds();
                    });
                },
                getResults: function getResults() {
                    return ctrl.isDisabled() ? null : SearchService.locationResults().compiled;
                },
                isW3WSearch: function isW3WSearch() {
                    return SearchService.isW3WSearch();
                },
                searchTerm: SearchService.locationSearch().search,
                postRender: function postRender() {},
                showMoreResults: function showMoreResults() {
                    console.log('TODO: Implement me');
                },
                selectedLocation: function getSelectedLocation() {
                    if (SearchService.locationSearch().search() && /^.*[\/]{3}(\w+)/.test(SearchService.locationSearch().search())) {
                        SearchService.locationSearch().what3WordsSearch(SearchService.locationSearch().search());
                    } else {
                        SearchService.locationSearch().what3WordsSearch('');
                    }
                    return LocationService.selectedLocation();
                },
                modelOptions: {
                    getterSetter: true
                },
                select: function select(result, moveOnly) {
                    var geocodeObj, poiLatLng, position;

                    if (LocationService.europeanPoiChecked()) {
                        MappingService.map().map.panTo(result.geometry.location);
                        MappingService.map().map.setZoom(17);
                    } else {
                        //reset the homestart indicator in case we've already set it
                        TaskService.task().indicators().homeStart(false);
                        PromptService.removePromptById(Prompt.INACCURATE_LOCATION);
                        PromptService.removePromptById(Prompt.LOCAL_DRIVER_PROMPT);
                        LocationService.selectedLocation(result);
                        PromptService.resetOutcomeObject(TaskService.task());

                        if (result.hasOwnProperty('addressAsString')) {
                            geocodeObj = {
                                address: result.addressAsString()
                            };
                        } else if (result.formatted_address || result.vicinity) {
                            if (_.indexOf(result.types, 'route') >= 0) {
                                poiLatLng = GoogleService.latLng(result.geometry.location.lat(), result.geometry.location.lng());
                                position = poiLatLng;
                                geocodeObj = {
                                    location: poiLatLng
                                };
                            } else {
                                GoogleService.places().getDetails(
                                    {
                                        placeId: result.place_id,
                                        fields: [
                                            'address_component',
                                            'adr_address',
                                            'formatted_address',
                                            'geometry',
                                            'icon',
                                            'name',
                                            'business_status',
                                            'photo',
                                            'place_id',
                                            'plus_code',
                                            'type',
                                            'url',
                                            'vicinity'
                                        ]
                                    },
                                    function (detailedResult) {
                                        var resultName = _.indexOf(result.types, 'postal_code') > -1 ? null : result.name;

                                        if (_.indexOf(result.types, 'intersection') > -1) {
                                            resultName = ctrl.searchTerm();
                                        }

                                        if (GoogleService.isIrelandAddress(detailedResult.address_components)) {
                                            PromptService.ROI();
                                        } else {
                                            // Reset fault and capabilities, when breakdown location changed from motorway to normal location -RBAUAA 2010
                                            if ((TaskService.task().indicators().motorway() || TaskService.task().indicators().dangerousLocation()) && TaskService.task().isRelay()) {
                                                TaskService.task().recovery().relay(false);
                                                TaskService.task().fault(new Fault());
                                                DiagnosticsService.selectedCommonFault(new Fault());
                                                DiagnosticsService.clearDiagnosticsQA();
                                                DiagnosticsService.diagnosticsQAList([]);
                                                TabService.clearRelay();
                                            }
                                            ctrl.resetOnLocationChange();
                                            processResult({
                                                latLng: result.geometry.location,
                                                text: _processAddressComponents(detailedResult.address_components, LocationTextTypes, resultName, detailedResult.adr_address),
                                                area: _processAddressComponents(detailedResult.address_components, LocationAreaTypes),
                                                placeTypes: _.concat(detailedResult.types, result.types ? result.types : []),
                                                moveOnly: moveOnly || false
                                            });
                                        }
                                    }
                                );
                            }
                        } else if (result.properties.categoryId) {
                            //this is a gazetteer result item
                            var processPOIResult = function (position, result) {
                                GoogleService.geocode({
                                    location: position
                                }).then(function (results) {
                                    let isSOSBox = result.properties.categoryId === 4503;
                                    if (GoogleService.isIrelandAddress(results[0].address_components)) {
                                        PromptService.ROI();
                                    } else {
                                        processResult({
                                            latLng: isSOSBox ? new google.maps.LatLng(result.geometry.coordinates[1], result.geometry.coordinates[0]) : position,
                                            text: LocationService.processGarageAddress(result) || _processAddressComponents(results[0].address_components, LocationTextTypes, result.name || ''),
                                            area: result.properties.motorway || isSOSBox ? result.properties.area : _processAddressComponents(results[0].address_components, LocationAreaTypes),
                                            placeTypes: [PlaceTypes.GAZETTEER],
                                            resourceId: result.properties.supResourceId ? result.properties.supResourceId : null,
                                            moveOnly: moveOnly || false,
                                            flags: {
                                                // only set for motorway and dangerous locations ..
                                                motorway: result.properties.motorway,
                                                dangerousLocation: result.properties.dangerousLocation
                                            },
                                            properties: result.properties
                                        }).then(function () {
                                            var finalResult,
                                                checkDangerous = true;

                                            switch (result.properties.categoryId) {
                                                case 4505: //motorway services
                                                    TaskService.task().indicators().motorway(false);
                                                    TaskService.task().indicators().dangerousLocation(false);
                                                    checkDangerous = false;
                                                    break;
                                                case 4514: //motorway junction
                                                case 4504: //motorway marker posts
                                                case 4503: //sos boxes
                                                    TaskService.task().indicators().motorway(true);
                                                    TaskService.task().indicators().dangerousLocation(true);
                                                    TaskService.task().recovery().destResourceId(DestResourceIDsConstants.LOCAL);
                                                    TaskService.setDirectRecovery(TaskService.task().fault().mwayRecovVehFaultId() === 0);

                                                    LocationService.refreshPriority();
                                                    LocationService.setMotorwayRelayInformation(position.lat(), position.lng(), result);

                                                    checkDangerous = false;
                                                    break;
                                            }

                                            LocationService.refreshMotorwayJunctionInfo(result.properties.categoryId);

                                            if (results.length > 1) {
                                                finalResult = _.find(results, function (item) {
                                                    return _.indexOf(item.types, 'route') >= 0;
                                                });
                                            } else {
                                                finalResult = _.head(results);
                                            }

                                            if (checkDangerous) {
                                                LocationService.isMotorway(position, finalResult);
                                            }
                                        });
                                    }
                                });
                            };

                            if (
                                _.indexOf([4503, 4504, 4505], result.properties.categoryId) > -1 &&
                                !LocationService.servicesAtJunction(result.properties.name) &&
                                result.properties.prevJunc.latitude !== 0 &&
                                result.properties.prevJunc.longitude !== 0
                            ) {
                                //services need to be positioned at the previous junction to ensure correct garage is assigned
                                position = GoogleService.latLng(result.properties.prevJunc.latitude, result.properties.prevJunc.longitude);
                            } else if (_.indexOf([4503, 4504, 4505], result.properties.categoryId) > -1 && !LocationService.servicesAtJunction(result.properties.name)) {
                                LocationService.getNearestJunction(GoogleService.latLng(result.geometry.coordinates[1], result.geometry.coordinates[0]))
                                    .then(function nearestJunctionOk(junction) {
                                        position = GoogleService.latLng(junction.geometry.coordinates[0], junction.geometry.coordinates[1]);

                                        return processPOIResult(position, result);
                                    })
                                    .catch(function nearestJunctionError(error) {
                                        //TODO handle this error to alert the user
                                        return error;
                                    });
                            } else {
                                position = GoogleService.latLng(result.geometry.coordinates[1], result.geometry.coordinates[0]);
                            }

                            return processPOIResult(position, result);
                        }

                        if (geocodeObj) {
                            var routeResult;
                            GoogleService.geocode(geocodeObj).then(function geoCodePromiseResolved(results) {
                                routeResult = results[0];
                                if (GoogleService.isIrelandAddress(results[0].address_components)) {
                                    PromptService.ROI();
                                } else {
                                    processResult({
                                        latLng: poiLatLng || routeResult.geometry.location,
                                        text: _processAddressComponents(routeResult.address_components, LocationTextTypes, result.name || ''),
                                        area: _processAddressComponents(routeResult.address_components, LocationAreaTypes),
                                        placeTypes: routeResult.types,
                                        moveOnly: moveOnly || false
                                    }).then(function () {
                                        if (result) {
                                            if (results.length > 1) {
                                                result = _.find(results, function (item) {
                                                    return _.indexOf(item.types, 'route') >= 0;
                                                });
                                            } else {
                                                result = _.head(results);
                                            }
                                            LocationService.isMotorway(position, result);
                                        }
                                    });
                                }
                            });
                        }
                        if (!TaskService.task().indicators().dangerousLocation()) {
                            // Check for Homestart entitlement, if user is under 1/4mile of Home location
                            let { geometry } = result;
                            if (_.has(geometry, 'location')) {
                                ContextMenuService.checkNearHomeStart(`${geometry.location.lat()},${geometry.location.lng()}`);
                            } else if (_.has(geometry, 'coordinates')) {
                                ContextMenuService.checkNearHomeStart(`${geometry.coordinates[1]},${geometry.coordinates[0]}`);
                            }
                            ctrl.getNearby(result);
                        }
                    }
                },
                getNearby: function (result, limit) {
                    ContextMenuService.getNearby(ctrl.getLatLng(result), result, limit);
                },
                getLatLng: function (result) {
                    if (_.has(result, 'geometry')) {
                        let geometry = result.geometry;
                        if (_.has(geometry, 'location')) {
                            return new google.maps.LatLng(result.geometry.location.lat(), result.geometry.location.lng());
                        } else if (_.has(geometry, 'coordinates')) {
                            return new google.maps.LatLng(geometry.coordinates[1], geometry.coordinates[0]);
                        }
                    }
                },
                resetOnLocationChange: function resetOnLocationChange() {
                    // Reset fault and capabilities, when breakdown location changed from motorway to normal location -RBAUAA 2010
                    if ((TaskService.task().indicators().motorway() || TaskService.task().indicators().dangerousLocation()) && TaskService.task().isRelay()) {
                        //Clear Relay
                        TaskService.task().recovery().relay(false);
                        TabService.clearRelay();

                        //Reset fault
                        TaskService.task().fault(new Fault());
                        DiagnosticsService.selectedCommonFault(new Fault());
                        TabService.touch('assistance');

                        //Reset Diagnostics
                        DiagnosticsService.clearDiagnosticsQA();
                        DiagnosticsService.diagnosticsQAList([]);

                        //Clear dangerous/motorway priorities
                        TaskService.task().indicators().safetyAdviceGiven(false);
                        _.remove(TaskService.task().priorities(), function (priority) {
                            return priority.id() === PriorityReason.VULNERABLE_VEHICLE;
                        });
                    }
                }
            });
        }
    ]);
