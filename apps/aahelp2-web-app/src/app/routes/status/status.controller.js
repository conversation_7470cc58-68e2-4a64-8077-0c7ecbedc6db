var _ = require('lodash');
require('angular');

var CreateReason = require('@aa/malstrom-models/lib/create-reason.model');
module.exports = angular
    .module('aah-status-controller-module', [require('../../services/task/task.service').name, require('../../services/notification/notification.service').name])
    .controller('aahStatusController', [
        '$http',
        'aahNotificationService',
        'aahTaskService',
        function StatusController($http, NotificationService, TaskService) {
            const ctrl = this,
                taskTypes = ['BRK', 'ADM'];
            let isLocalhost = PHOENIX_HOST == 'REPLACE_WITH_PHOENIX_HOST' ? true : false;
            const currentVersion = require('../../../package.json').version;
            _.extend(ctrl, {
                countOperations: function countOperations() {
                    return NotificationService.count();
                },
                operations: function operations() {
                    return NotificationService.operationList();
                },
                isLocalhost: () => {
                    return isLocalhost;
                },
                currentVersion: () => {
                    return currentVersion;
                },
                debugInfo: function debugInfo() {
                    var result = '';
                    if (TaskService.task() && TaskService.task().createReason().id() !== CreateReason.HIRE_CAR) {
                        result += 'id:' + TaskService.task().id();
                        if (TaskService.task().taskType().code() === 'BRK') {
                            result += '@' + TaskService.task().sequence();
                            result += TaskService.task().recovery().isSet() ? ' recy?Y' : ' recy?N';
                            result += TaskService.task().indicators().motorway() ? ' mway?Y' : ' mway?N';
                            result += ' sd?'; //TODO find out where to obtain this information from (options for this is Y/N)
                            result += TaskService.task().indicators().authorised() ? ' au?Y' : ' au?N';
                            result += ' rp:' + TaskService.task().fault().repairProbability();
                            result += ' rg:N/A';
                            result += ' re:N/A';
                            result += ' rd:N/A';
                            result += TaskService.task().jobsheet() !== null ? ' js:Y' : ' js:N';
                            result += ' cr:' + TaskService.task().createReason().name();
                            result += ' cdr:'; //TODO also determine whether the appt is IM and customer driven
                            result += TaskService.task().indicators().incidentManaged() ? ' bookd:Y' : ' bookd:N';
                            result += ' dRes: ' + TaskService.task().recovery().destResourceId();
                        }
                    }

                    if (TaskService.task() && TaskService.task().createReason().isHireCar()) {
                        result += 'id:' + TaskService.task().id();
                        if (TaskService.task().taskType().code() === 'ADM') {
                            result += '@' + TaskService.task().sequence();
                            result += TaskService.task().recovery().isSet() ? ' recy?Y' : ' recy?N';
                            result += ' mwa: N/A';
                            result += ' sd?'; //TODO find out where to obtain this information from (options for this is Y/N)
                            result += TaskService.task().indicators().authorised() ? ' au?Y' : ' au?N';
                            result += ' rp: N/A';
                            result += ' rg:N/A';
                            result += ' re:N/A';
                            result += ' rd:N/A';
                            result += ' js:N/A';
                            result += ' cr:' + TaskService.task().createReason().name();
                            result += ' cdr:'; //TODO also determine whether the appt is IM and customer driven
                            result += TaskService.task().indicators().incidentManaged() ? ' bookd:Y' : ' bookd:N';
                            result += ' dRes: ' + TaskService.task().recovery().destResourceId();
                        }
                    }

                    return result;
                },
                showDebugInfo: function showDebugInfo() {
                    return taskTypes.includes(TaskService.task().taskType().code()) && TaskService.task().id() > -1;
                }
            });
        }
    ]);
