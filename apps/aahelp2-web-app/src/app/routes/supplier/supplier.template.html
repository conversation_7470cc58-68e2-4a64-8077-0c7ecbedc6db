<div
    id="supplier"
    class="main-panel shadow general-scrollbar"
>
    <!-- Supplier Section -->
    <div class="section">
        <div class="section-header-supplier clear">
            <div class="col-lg-12 col-sm-12 col-xs-12">
                <h2 class="contact-header">Supplier</h2>
            </div>
        </div>
        <div class="section-body-supplier">
            <form>
                <fieldset>
                    <div class="col-lg-6 col-sm-6 col-xs-6">
                        <label class="label-primary"><span class="label-secondary">Name:</span> {{ctrl.supplier().supplierName()}}</label>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-xs-6">
                        <label class="label-primary"><span class="label-secondary">Account number:</span> {{ctrl.supplier().supplierAccountNo()}}</label>
                    </div>
                    <div class="col-lg-12 col-sm-12 col-xs-12">
                        <label class="label-primary"><span class="label-secondary">Address: </span>{{ctrl.supplier().supplierAddress().addressAsString()}}</label>
                    </div>
                </fieldset>
            </form>
        </div>
    </div>

    <hr class="divider" />
    <!--
    /************************************************************************
    * Eurohelp
    ************************************************************************/
    -->
    <div
        class="section"
        ng-if="ctrl.shouldShowEurohelp()"
    >
        <div class="section-header-supplier clear">
            <div class="col-lg-12 col-sm-12 col-xs-12">
                <h2 class="contact-header">Eurohelp</h2>
            </div>
        </div>
        <!--
        /************************************************************************
         * Eurohelp - repair
         ************************************************************************/
        -->
        <div
            class="section-body-supplier"
            ng-if="ctrl.createReason().isGarageRepair()"
        >
            <form
                name="supplierForm"
                class="form-dirty"
            >
                <fieldset>
                    <div class="col-lg-6 col-sm-6 col-xs-6">
                        <div class="row col-xs-12">
                            <label
                                for="supplier-garageBookingReference"
                                class="label-secondary"
                                >Booking Reference</label
                            >
                            <input
                                type="text"
                                id="supplier-garageBookingReference"
                                ng-model="ctrl.task().eurohelp().garageBookingReference"
                                ng-model-options="ctrl.modelOptions"
                                class="form-control"
                            />
                        </div>
                        <div class="row col-xs-12">
                            <label
                                for="supplier-remarks"
                                class="label-secondary"
                                >Message (Description of Service)</label
                            >

                            <textarea
                                id="supplier-message"
                                ng-model="ctrl.task().eurohelp().message"
                                ng-model-options="ctrl.modelOptions"
                                class="form-textarea form-control general-scrollbar"
                            ></textarea>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <label
                            for="serviceTotalPrice"
                            class="label-primary"
                            >Total price</label
                        >
                        <div class="service-total-price-container">
                            <div class="select-wrap service-currency-wrapper">
                                <select
                                    class="form-select form-control"
                                    id="serviceCurrency"
                                    ng-model="ctrl.task().eurohelp().price().currency"
                                    ng-model-options="ctrl.modelOptions"
                                    required
                                >
                                    <option
                                        value="{{::key}}"
                                        label="{{::value}}"
                                        ng-repeat="(key, value) in ctrl.currencyTypes()"
                                    ></option>
                                </select>
                            </div>
                            <dg-validation
                                model="supplierForm.serviceTotalPrice"
                                position="bottom"
                            >
                                <input
                                    class="form-field form-control"
                                    dg-validation-trigger
                                    required
                                    id="serviceTotalPrice"
                                    name="serviceTotalPrice"
                                    type="number"
                                    min="1"
                                    ng-model="ctrl.task().eurohelp().price().value"
                                    ng-model-options="{getterSetter:true, debounce: 10}"
                                />
                            </dg-validation>
                        </div>
                    </div>
                </fieldset>
            </form>
        </div>
        <!--
        /************************************************************************
         * Eurohelp - storage
         ************************************************************************/
        -->
        <div
            class="section-body-supplier"
            ng-if="ctrl.createReason().isStorage()"
        >
            <form
                name="supplierForm"
                class="form-dirty"
            >
                <fieldset>
                    <div class="col-md-6">
                        <label
                            for="serviceTotalPrice"
                            class="label-primary"
                            >Total price</label
                        >
                        <div class="service-total-price-container">
                            <div class="select-wrap service-currency-wrapper">
                                <select
                                    class="form-select form-control"
                                    id="serviceCurrency"
                                    ng-model="ctrl.task().eurohelp().price().currency"
                                    ng-model-options="ctrl.modelOptions"
                                    required
                                >
                                    <option
                                        value="{{::key}}"
                                        label="{{::value}}"
                                        ng-repeat="(key, value) in ctrl.currencyTypes()"
                                    ></option>
                                </select>
                            </div>
                            <dg-validation
                                model="supplierForm.serviceTotalPrice"
                                position="bottom"
                            >
                                <input
                                    class="form-field form-control"
                                    dg-validation-trigger
                                    required
                                    id="serviceTotalPrice"
                                    name="serviceTotalPrice"
                                    type="number"
                                    min="1"
                                    ng-model="ctrl.task().eurohelp().price().value"
                                    ng-model-options="{getterSetter:true, debounce: 10}"
                                />
                            </dg-validation>
                        </div>
                    </div>
                </fieldset>
            </form>
        </div>

        <!--
            /************************************************************************
             * Eurohelp - recovery
             ************************************************************************/
            -->
        <div
            class="section-body-supplier"
            ng-if="ctrl.createReason().isRecovery()"
        >
            <form
                name="supplierForm"
                class="form-dirty"
            >
                <fieldset>
                    <div class="col-md-6">
                        <label
                            for="serviceTotalPrice"
                            class="label-primary"
                            >Total price</label
                        >
                        <div class="service-total-price-container">
                            <div class="select-wrap service-currency-wrapper">
                                <select
                                    class="form-select form-control"
                                    id="serviceCurrency"
                                    ng-model="ctrl.task().eurohelp().price().currency"
                                    ng-model-options="ctrl.modelOptions"
                                >
                                    <option
                                        value="{{::key}}"
                                        label="{{::value}}"
                                        ng-repeat="(key, value) in ctrl.currencyTypes()"
                                    ></option>
                                </select>
                            </div>
                            <dg-validation
                                model="supplierForm.serviceTotalPrice"
                                position="bottom"
                            >
                                <input
                                    class="form-field form-control"
                                    dg-validation-trigger
                                    required
                                    id="serviceTotalPrice"
                                    name="serviceTotalPrice"
                                    type="1"
                                    ng-model="ctrl.task().eurohelp().price().value"
                                    ng-model-options="{getterSetter:true, debounce: 10}"
                                />
                            </dg-validation>
                        </div>
                    </div>
                </fieldset>
            </form>
        </div>

        <!--
    /************************************************************************
    * Eurohelp
    ************************************************************************/
    -->

        <div class="section">
            <div class="section-body-supplier">
                <form>
                    <fieldset>
                        <div class="col-lg-12 col-sm-12 col-xs-12">
                            <label class="label-primary"><span class="label-secondary">Job type: </span> {{ctrl.taskSupplierJobType().name()}}</label>
                        </div>
                        <div class="col-lg-4 col-sm-6 col-xs-6">
                            <div class="radio-wrap">
                                <input
                                    type="checkbox"
                                    id="supplier-cheques"
                                    ng-model="ctrl.supplier().supplierDetails().chequesAcceptedIndicator"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model-options="ctrl.modelOptions"
                                />
                                <label
                                    for="supplier-cheques"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="icon-tick"></span></span>Cheques accepted</label
                                >
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-6 col-xs-6">
                            <div class="radio-wrap">
                                <input
                                    type="checkbox"
                                    id="supplier-credit"
                                    ng-model="ctrl.supplier().supplierDetails().creditCardsAcceptedIndicator"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model-options="ctrl.modelOptions"
                                />
                                <label
                                    for="supplier-credit"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="icon-tick"></span></span>Credit cards accepted</label
                                >
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-6 col-xs-6">
                            <div class="radio-wrap">
                                <input
                                    type="checkbox"
                                    id="a120-forms"
                                    ng-model="ctrl.supplier().issA120FormsInd"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model-options="ctrl.modelOptions"
                                />
                                <label
                                    for="a120-forms"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="icon-tick"></span></span>A120 forms</label
                                >
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-6 col-xs-6">
                            <div class="radio-wrap">
                                <input
                                    id="relay-plus"
                                    type="checkbox"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model="ctrl.supplier().supplierDetails().relayPlusVoucherIndicator"
                                    ng-model-options="ctrl.modelOptions"
                                />
                                <label
                                    for="relay-plus"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="icon-tick"></span></span>Relay plus vouchers</label
                                >
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-6 col-xs-6">
                            <div class="radio-wrap">
                                <input
                                    type="checkbox"
                                    id="supplier-contract-agent"
                                    ng-model="ctrl.supplier"
                                    ng-disabled="ctrl.isDisabled()"
                                    ng-model-options="ctrl.modelOptions"
                                />
                                <label
                                    for="supplier-contract-agent"
                                    class="radio-label"
                                    ><span class="dot-outer"><span class="icon-tick"></span></span>Contract agent</label
                                >
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
        </div>
        <div class="section">
            <div class="section-body-supplier">
                <form>
                    <fieldset>
                        <div class="col-lg-6 col-sm-6 col-xs-6">
                            <div class="row col-xs-12">
                                <label
                                    for="supplier-remarks"
                                    class="label-secondary"
                                    >Remarks</label
                                >
                                <textarea
                                    id="supplier-remarks"
                                    ng-model="ctrl.supplier().supplierDetails().remarks"
                                    ng-model-options="ctrl.modelOptions"
                                    ng-disabled="ctrl.isDisabled()"
                                    class="form-textarea form-control general-scrollbar"
                                ></textarea>
                            </div>
                            <ul class="list-unstyled">
                                <li><span class="label-secondary">Supplier network: </span>{{ctrl.supplier().supplierNetworkName()}}</li>
                            </ul>
                            <ul
                                class="list-unstyled telephone-list"
                                ng-repeat="telehoneAndType in ctrl.supplier().supplierDetails().resourceTelephoneNumbersAndType() track by $index"
                            >
                                <li><span class="label-secondary">{{telehoneAndType.telephoneNumberType()}} :</span> {{telehoneAndType.telephoneNumber()}}</li>
                            </ul>
                        </div>
                        <div class="col-lg-6 col-sm-6 col-xs-6">
                            <label class="label-secondary">Capabilities</label>
                            <div class="capabilities-box">
                                <div class="no-arrow-scrollbar capability-height">
                                    <ul
                                        class="list-unstyled"
                                        ng-repeat="capability in ctrl.supplier().capabilities() track by $index"
                                    >
                                        <li>{{capability.name()}}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
        </div>
        <!-- Section End -->
        <div
            ng-hide="ctrl.hideButtons()"
            class="action-buttons"
        >
            <!--   RBAUAA-11425 and RBAUAA-11480 disabled allocate button
       <button
            disabled="disabled"
            class="btn btn-secondary-action"
             ng-click="ctrl.allocateTaskToSupplier()"
        >-->
            <button
                disabled="disabled"
                class="btn btn-secondary-action"
            >
                Allocate
            </button>
            <button
                disabled="disabled"
                class="btn btn-secondary-action"
            >
                Refuse
            </button>
        </div>
    </div>
</div>
