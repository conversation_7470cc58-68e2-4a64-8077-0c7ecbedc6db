var _ = require('lodash'),
    ui_router = require('angular-ui-router');
require('angular');
const Currencies = require('@aa/malstrom-models/lib/constants/currency-iso-code.constants');

module.exports = angular
    .module('aah-supplier-controller-module', [
        ui_router,

        require('../../services/supplier/supplier.service').name,
        require('../../services/task/task.service').name,
        require('../../services/alert/alert.service').name,

        require('../../constants/supplier/supplier-job-type.constants').name,
        require('../../constants/alert/alert-type.constants').name
    ])
    .controller('aahSupplierController', [
        'aahSupplierService',
        'aahTaskService',
        'aahAlertService',
        'aahCSHService',
        'aahSupplierJobTypeConstants',
        'uibAlertTypes',
        function SupplierController(SupplierService, TaskService, AlertService, CSHService, SupplierJobTypes, AlertTypes) {
            var ctrl = this;

            function requiredFieldsPopulated() {
                if (!TaskService.task().supJobTypeCode()) {
                    AlertService.createAlert('Please select a Job Type', AlertTypes.DANGER);
                    return false;
                }
                if (!SupplierService.supplier()) {
                    AlertService.createAlert('Please select a Supplier', AlertTypes.DANGER);
                    return false;
                }
                return true;
            }

            _.extend(ctrl, {
                currencyTypes: () => {
                    return Currencies;
                },
                createReason: function createReason() {
                    return TaskService.task().createReason();
                },
                customerGroup: function customerGroup() {
                    return TaskService.task().entitlement().customerGroup();
                },
                shouldShowEurohelp: function shouldShowEurohelp() {
                    //more is better
                    const isEuroHelp = ctrl.customerGroup().isEuroHelp();
                    const isGarageRepair = ctrl.createReason().isGarageRepair();
                    const isStorage = ctrl.createReason().isStorage();
                    const isRecovery = ctrl.createReason().isRecovery();
                    return isEuroHelp && (isGarageRepair || isStorage || isRecovery);
                },
                task: function taskAccessor(val) {
                    return TaskService.task();
                },
                supplier: function supplierAccessor() {
                    return SupplierService.supplier();
                },
                supplierJobTypes: function supplierJobTypes() {
                    return SupplierService.supplierJobTypes();
                },
                taskSupplierJobType: function taskSupplierJobTypeAccessor(val) {
                    return arguments.length ? TaskService.task().supplierJobType(val) : TaskService.task().supplierJobType();
                },
                removeSmrFilter: function removeSmrFilter(jobType) {
                    if (jobType.code() === SupplierJobTypes.SMR_JOB_TYPE_CODE) {
                        return false;
                    }
                    return true;
                },
                allocateTaskToSupplier: function allocateTaskToSupplier() {
                    //if (requiredFieldsPopulated()) {
                    TaskService.task().schedule().resource().id(SupplierService.supplier().resourceId());
                    TaskService.save();
                    AlertService.createAlert('Task allocated to supplier ' + SupplierService.supplier().supplierName(), AlertTypes.INFO);
                    //}
                },
                isDisabled: function isDisabled() {
                    return TaskService.task().uiStatus().supplier();
                },
                hideButtons: function hideButtons() {
                    return TaskService.task().isCompleted();
                },
                modelOptions: {
                    getterSetter: true,
                    updateOn: 'blur'
                }
            });
        }
    ]);
