var _ = require('lodash'),
    CustomerGroup = require('@aa/malstrom-models/lib/customer-group.model'),
    EligibilityDetails = require('@aa/malstrom-models/lib/eligibility-details.model');
require('angular');
//Model
var Fault = require('@aa/malstrom-models/lib/fault.model');

module.exports = angular.module('aah-task-helper-module', [require('../../constants/faults/faults.constants').name]).service('aahTaskHelper', [
    '$filter',
    'aahFaultsConstants',
    'aahTabService',
    'aahVehicleService',
    'aahTaskService',
    function TaskHelper($filter, FaultsConstants, TabService, VehicleService, TaskService) {
        var svc = this;

        function _shouldMergeTasks(fromTask, toTask) {
            //if both tasks are new
            if (toTask.isNew() && fromTask.isNew()) {
                return true;
            }
            return false;
        }

        function _shouldMergeWillJoinTasks(activeTask, willJoinTask) {
            //if active task is new and from task is Will Join task
            if (activeTask.isNew() && willJoinTask.entitlement().riskCode() === 'WJ') {
                return true;
            }
            return false;
        }

        _.extend(svc, {
            merge: function merge(toTask, fromTask) {
                var mergedRemark = [];
                if (_shouldMergeTasks(fromTask, toTask)) {
                    toTask.id(fromTask.id());
                    toTask.customerRequestId(fromTask.customerRequestId());
                    toTask.entitlement(fromTask.entitlement());
                    toTask.status(fromTask.status());
                    toTask.sequence(fromTask.sequence());
                    toTask.priorities(fromTask.priorities());
                    toTask.createReason(fromTask.createReason());
                    toTask.indicators(fromTask.indicators());
                    toTask.location().remarksToAppend(fromTask.location().remarksToAppend());

                    if (!_.isString(toTask.contact().name())) {
                        toTask.contact().name(fromTask.contact().name());
                    }

                    if (_.isString(toTask.location().remarks())) {
                        mergedRemark.push(toTask.location().remarks().trim());
                    }

                    if (_.isString(fromTask.location().remarks())) {
                        mergedRemark.push(fromTask.location().remarks().trim());
                    }

                    toTask.location().remarks(mergedRemark.join(' '));

                    // on tak creation remarksToAppend is populated with details from entitlement post code and date of birth or if we have BRC customer then populated with BRC remark
                    // this needs to merged into remakrs field ...
                    // Address till the first comma of address to be included in remarks RBAUAA-1406
                    if (toTask.location().remarksToAppend()) {
                        var door_Num = toTask
                            .entitlement()
                            .memberAddress()[0]
                            .replace(/^(.+?),/g, '');
                        toTask
                            .location()
                            .remarks(
                                door_Num
                                    ? door_Num + ', ' + toTask.location().remarksToAppend()
                                    : toTask.entitlement().memberAddress()[0] +
                                          ', ' +
                                          toTask.location().remarksToAppend() +
                                          (toTask.location().remarks() && toTask.location().remarks().indexOf(toTask.location().remarksToAppend()) === -1 ? ' ' + toTask.location().remarks() : '')
                            );
                    }

                    // vehicle based entitlement ...

                    // merge recovery details if toTaks is empty ..
                    if (!toTask.recovery().isSet() && fromTask.recovery().isSet()) {
                        toTask.recovery(fromTask.recovery());
                    }

                    toTask.uiStatus(fromTask.uiStatus());

                    // to pick up the misc fields in eligibility questions
                    var sDealerName = toTask.miscFields() && toTask.miscFields().serviceDealerName() ? toTask.miscFields().serviceDealerName() : null;
                    var sDealerLocation = toTask.miscFields() && toTask.miscFields().serviceDealerLocation() ? toTask.miscFields().serviceDealerLocation() : null;
                    var sDealerDateOfService = toTask.miscFields() && toTask.miscFields().serviceDealerDateOfService() ? toTask.miscFields().serviceDealerDateOfService() : null;

                    // don't forget the misc fields ...
                    toTask.miscFields(fromTask.miscFields());
                    toTask.miscFields().serviceDealerName(sDealerName);
                    toTask.miscFields().serviceDealerLocation(sDealerLocation);
                    toTask.miscFields().serviceDealerDateOfService(sDealerDateOfService);

                    // event hooks
                    toTask.eventHooks(fromTask.eventHooks());
                } else {
                    // on tak creation remarksToAppend is populated with details from entitlement post code and date of birth or if we have BRC customer then populated with BRC remark
                    // this needs to merged into remakrs field ...
                    // Address till the first comma of address to be included in remarks RBAUAA-1406
                    if (fromTask.location().remarksToAppend()) {
                        var door_num = fromTask
                            .entitlement()
                            .memberAddress()[0]
                            .replace(/^(.+?),/g, '');
                        fromTask
                            .location()
                            .remarks(
                                door_num
                                    ? door_num + ', ' + fromTask.location().remarksToAppend()
                                    : fromTask.entitlement().memberAddress()[0] +
                                          ', ' +
                                          fromTask.location().remarksToAppend() +
                                          (fromTask.location().remarks() && fromTask.location().remarks().indexOf(fromTask.location().remarksToAppend()) === -1
                                              ? ' ' + fromTask.location().remarks()
                                              : '')
                            );
                    }
                    toTask = fromTask; // otherwise we overwrite the task with what we  just received
                }

                return toTask;
            },
            mergeWillJoinTask: function merge(activeTask, willJoinTask) {
                let mergedTask = {
                    success: false,
                    task: activeTask
                };
                if (_shouldMergeWillJoinTasks(activeTask, willJoinTask)) {
                    let mergedRemarks = [activeTask.location().remarks(), willJoinTask.location().remarks()].filter(Boolean).join(', ');

                    activeTask.contact(willJoinTask.contact());
                    if (willJoinTask.status() === 'INIT' && willJoinTask.fault().id() === FaultsConstants.OVERHEATING_FAULT_ID) {
                        willJoinTask.fault(new Fault());
                    }
                    activeTask.fault(willJoinTask.fault());
                    //To display error in case fault empty (Will join overheating task)
                    TabService.touch('assistance');
                    activeTask.vehicle(willJoinTask.vehicle());
                    let coordinates = willJoinTask.location().coordinates();
                    if (coordinates.longitude() && coordinates.latitude()) {
                        activeTask.location(willJoinTask.location());
                    }

                    activeTask.location().remarksToAppend(willJoinTask.location().remarksToAppend());
                    if (_.isString(willJoinTask.location().remarks())) {
                        activeTask.location().remarks(mergedRemarks);
                    }

                    // We are ignoring incorrect task vrn if no-vrn selected or valid vrn passed
                    const vrn = TaskService.task().vehicle().registration();
                    if (vrn && VehicleService.validateVRN(vrn)) {
                        // set VRN check result as passed to allow further processing
                        TaskService.task().contractValidation().vrn().passed(true);
                    }

                    mergedTask = {
                        success: true,
                        task: activeTask
                    };
                }
                return mergedTask;
            },
            generateInvoiceNumber: (siteId, jobNoToday, date) => {
                let baseZeros = '00000';

                function appendZeros(digits, val) {
                    return baseZeros.slice(0, -digits) + val;
                }

                function createYear(dateArray) {
                    var calculatedYear = null,
                        year = null;

                    if (dateArray[2] >= 2000) {
                        calculatedYear = dateArray[2] - 2000;
                    } else {
                        calculatedYear = dateArray[2] - 1900;
                    }
                    year = calculatedYear.toString().length > 1 ? calculatedYear : appendZeros(dateArray[2].length, calculatedYear);
                    return {
                        day: dateArray[0],
                        month: dateArray[1],
                        year: year
                    };
                }

                if (siteId && jobNoToday && date) {
                    let _siteId = siteId ? (siteId.toString().length > 1 ? siteId : '0' + siteId.toString()) : null,
                        _jobNoToday = jobNoToday ? appendZeros(jobNoToday.toString().length, jobNoToday) : null,
                        _date = $filter('date')(date, 'dd/MM/yyyy').split('/'),
                        _formatedDate = createYear(_date);

                    return `${_siteId}${_jobNoToday}${_formatedDate.day}${_formatedDate.month}${_formatedDate.year}`;
                }
                return null;
            },
            getDateInENGBFormat: (data) => {
                let tempDate = data ? new Date(data) : data;
                return tempDate ? tempDate.toLocaleDateString('en-GB') : tempDate;
            }
        });
    }
]);
