require('angular');

var ui_bootstrap = require('angular-ui-bootstrap'),
    ui_router = require('angular-ui-router'),
    //ui_loadingBar = require('angular-loading-bar'),
    ui_scrollbar = require('ng-scrollbar'),
    ng_messages = require('angular-messages'),
    contextMenu = require('../../non-npm/contextMenu'),
    environment = require('angular-environment'),
    uiMask = require('angular-ui-mask'),
    _ = require('lodash');
const Completion = require('@aa/malstrom-models/lib/completion.model');
const CustomerGroup = require('@aa/malstrom-models/lib/customer-group.model');

module.exports = angular
    .module('aah-call-handling', [
        // dependencies
        ui_bootstrap,
        ui_router,
        //ui_loadingBar,
        'ngScrollbar',
        ng_messages,
        uiMask,
        'environment',

        // routes
        require('./routes/home/<USER>').name,
        require('./routes/csh/csh.route').name,
        require('./routes/contact/contact.route').name,
        require('./routes/assistance/assistance.route').name,
        require('./routes/summary/summary.route').name,
        require('./routes/location/location.route').name,
        require('./routes/completion/completion.route').name,
        require('./routes/appointment/appointment.route').name,
        require('./routes/supplier/supplier.route').name,
        require('./routes/eligibility/eligibility.route').name,
        require('./routes/payment/payment.route').name,
        require('./routes/ecall/ecall.route').name,
        require('./routes/audit/audit.route').name,
        require('./routes/service-declined/service-declined.route').name,
        require('./routes/company/company.route').name,
        require('./routes/priority/priority.route').name,
        require('./routes/flp/flp.route').name,
        require('./routes/csh-sub-route/csh-sub.route').name,
        require('./routes/relay-plus/relay-plus.route').name,
        require('./routes/incident-management/incident-management.route').name,
        require('./routes/commendation/commendation.route').name,
        require('./routes/electronic-documents/electronic-documents.route').name,
        require('./routes/cr-parts-history/cr-parts-history.route').name,
        require('./routes/cuv/cuv.route').name,
        require('./routes/driver-details/driver-details.route').name,
        require('./routes/vehicle-options/vehicle-options.route').name,
        require('./routes/vehicle-details/vehicle-details.route').name,
        require('./routes/insurance-details/insurance-details.route').name,
        require('./routes/car-hire/car-hire.route').name,
        require('./routes/fault-history/fault-history.route').name,
        require('./routes/transport/transport.route').name,
        require('./routes/accommodation/accommodation.route').name,
        require('./routes/location/accommodation/accommodation-location.route').name,
        require('./routes/transport/transport.route').name,
        require('./routes/location/transport/pickup/transport-pickup-location.route').name,
        require('./routes/location/transport/dropoff/transport-dropoff-location.route').name,
        require('./routes/debit-auth-card/card-payment.route').name,
        require('./routes/refund/refund-payment.route').name,
        require('./routes/hire-ext-audit/hire-audit.route').name,

        // directives
        require('./components/dev-panel/dev-panel.directive').name,
        require('./components/map/map.directive').name,
        require('./components/enter/enter.directive').name,
        require('./components/click-once/click-once.directive').name,
        require('./components/timeout-countdown/timeout-countdown.directive').name,
        require('./components/input-label/input-radio-label.directive').name,
        require('./components/input-label/input-checkbox-label.directive').name,
        require('./components/decimal-numbers/decimal-numbers.directive').name,
        require('./components/vehicle-owner-details/vehicle-owner-details.directive').name,

        // services
        require('./services/customer-group/customer-group.service').name,
        require('./services/service-type/service-type.service').name,
        require('./services/authentication/authentication.service').name,
        require('./services/authentication-interceptor/authentication-interceptor.service').name,
        require('./services/notification/notification.service').name,
        require('./services/callinfo/callinfo.service').name,
        require('./services/recovery/recovery.service').name,
        require('./services/side-panel/side-panel.service').name,
        require('./services/assistance-types/assistance-types.service').name,
        require('./services/payment/payment.service').name,
        require('./services/static-prompt/static-prompt.service').name,
        require('./services/package/package.service').name,
        require('./services/emergency-messages/emergency-messages.service').name,
        require('./services/centurion/centurion.service').name,
        require('./services/user-info/user-info.service').name,
        require('./services/driver-details/driver-details.service').name,
        require('./services/search/search.service').name,
        require('./services/completion/completion.service').name,
        require('./services/frontend-migration/frontend-migration.service').name,
        require('./services/audit/audit.service').name,
        require('./services/user/user.service').name,
        require('./services/comms-worker/comms-worker.service').name,
        require('./services/load-task/load-task.service').name,
        require('./services/sso/sso.service').name,
        require('./services/additional-task/additional-task.service').name
    ])
    .config([
        '$compileProvider',
        function ($compileProvider) {
            $compileProvider.debugInfoEnabled(true);
        }
    ])
    .config([
        '$httpProvider',
        function ($httpProvider) {
            $httpProvider.interceptors.push('aahAuthenticationInterceptorService');

            $httpProvider.interceptors.push('aahNotificationService');
        }
    ])
    .config([
        'envServiceProvider',
        function (envServiceProvider) {
            // TODO: SSO: fill the env variable config
            // set the domains and variables for each environment
            envServiceProvider.config({
                domains: {
                    test: ['lhost'],
                    local: ['localhost'],
                    development: [],
                    integration: ['rh0114p:7100', 'rh0114p.theaa.local:7100', 'aahelp2-int'],
                    verification: ['rh0112p:8100', 'rh0112p.theaa.local:8100', 'aahelp2-ver'],
                    UAT: ['rh0113p:5600', 'rh0113p.theaa.local:5600', 'rh0112p:5600', 'rh0112p.theaa.local:5600', 'aahelp2-uat'],
                    UAT2: ['localhost', 'rh0115v:8100', 'rh0115v.theaa.local:8100', 'aahelp2-uat2', 'aahelp2-uat2.theaa.local'],
                    training: ['aah2rb04:5600', 'aah2rb04.theaa.local:5600', 'aah2rb04:5601', 'aah2rb04.theaa.local:5601', 'aahelp-train'],
                    hotfix: ['rh0113p:5601', 'rh0113p.theaa.local:5601', 'aahelp2-hot'],
                    prelive: [
                        'aah2rr03.theaa.local:5601',
                        'aah2rr03.theaa.local:5600',
                        'aah2rr03.theaa.local',
                        'aah2rr03:5601',
                        'aah2rr03:5600',
                        'aah2rr03',
                        'aahelp2-prelive',
                        'aahelp2-prelive.theaa.local'
                    ],
                    live: [
                        'aah2rb06.theaa.local:5601',
                        'aah2rb06.theaa.local:5600',
                        'aah2rb06.theaa.local',
                        'aah2rb06:5601',
                        'aah2rb06:5600',
                        'aah2rb06',
                        'aah2rb04.theaa.local:5600',
                        'aah2rb04.theaa.local',
                        'aah2rb04:5600',
                        'aah2rb04',
                        'aah2rr02.theaa.local:5601',
                        'aah2rr02.theaa.local:5600',
                        'aah2rr02.theaa.local',
                        'aah2rr02:5601',
                        'aah2rr02:5600',
                        'aah2rr02',
                        'aah2rr01.theaa.local:5601',
                        'aah2rr01.theaa.local:5600',
                        'aah2rr01.theaa.local',
                        'aah2rr01:5601',
                        'aah2rr01:5600',
                        'aah2rr01',
                        'aah2rh02.theaa.local:5601',
                        'aah2rh02.theaa.local:5600',
                        'aah2rh02.theaa.local',
                        'aah2rh02:5601',
                        'aah2rh02:5600',
                        'aah2rh02',
                        'aah2rh01.theaa.local:5601',
                        'aah2rh01.theaa.local:5600',
                        'aah2rh01.theaa.local',
                        'aah2rh01:5601',
                        'aah2rh01:5600',
                        'aah2rh01',
                        'aah2rf02.theaa.local:5600',
                        'aah2rf02.theaa.local',
                        'aah2rf02:5600',
                        'aah2rf02',
                        'aah2rf01.theaa.local:5601',
                        'aah2rf01.theaa.local:5600',
                        'aah2rf01.theaa.local',
                        'aah2rf01:5601',
                        'aah2rf01:5600',
                        'aah2rf01',
                        'aah2rb02.theaa.local:5601',
                        'aah2rb02.theaa.local:5600',
                        'aah2rb02.theaa.local',
                        'aah2rb02:5601',
                        'aah2rb02:5600',
                        'aah2rb02',
                        'aah2rb01.theaa.local:5601',
                        'aah2rb01.theaa.local:5600',
                        'aah2rb01.theaa.local',
                        'aah2rb01:5601',
                        'aah2rb01:5600',
                        'aah2rb01',
                        'aahelp2-bolt',
                        'aahelp2',
                        'aahelp2-bolt.theaa.local',
                        'aah2rf02.theaa.local:5601',
                        'aah2rf02:5601',
                        'aah2rb04.theaa.local:5601',
                        'aah2rb04:5601',
                        'aahelp2.theaa.local',
                        'aah2rf00.theaa.local:5601',
                        'aah2rf00:5601',
                        'aah2rr00.theaa.local:5601',
                        'aah2rr00:5601',
                        'aah2rf00.theaa.local:5600',
                        'aah2rf00:5600',
                        'aah2rf00.theaa.local',
                        'aah2rf00',
                        'aah2rr00.theaa.local:5600',
                        'aah2rr00:5600',
                        'aah2rr00.theaa.local',
                        'aah2rr00',
                        'aah2rh00.theaa.local:5601',
                        'aah2rh00:5601',
                        'aah2rh00.theaa.local:5600',
                        'aah2rh00:5600',
                        'aah2rh00.theaa.local',
                        'aah2rh00'
                    ]
                },
                vars: {
                    /*
                     *   using angular-environment plugin
                     *   https://www.npmjs.com/package/angular-environment
                     *
                     *   ENABLE_SERVICE_ABUSE = -1 => service abuse functionality turned off for all users
                     *   ENABLE_SERVICE_ABUSE = 1 => service abuse functionality turned on for all users
                     *   ENABLE_SERVICE_ABUSE = 2 => service abuse functionality turned on for users specified in the list
                     */
                    test: {
                        ENABLE_DEV_MODE: false,
                        ENABLE_SERVICE_ABUSE: 'test',
                        CENTURION_ROLE: 'aah2CenturionUat',
                        ENABLE_SSO: false,
                        ENABLE_ECALL: true,
                        AZURE_DOMAIN: 'XXXXXXXXXXX',
                        SSO_AD_CLIENT_ID: 'XXXXXXXXXX',
                        SSO_AD_API_CLIENT_ID: 'XXXXXXXXXX',
                        SSO_AD_TENANT_ID: 'XXXXXXXXXX',
                        SSO_AUTH_SERVICE_URL: 'XXXXXXXXXX',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'XXXXXXXXXX',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'XXXXXXXXXX',
                        AZURE_STORAGE_ACCOUNT_CON_STR:
                            'DefaultEndpointsProtocol=https;AccountName=roadopsvehicleleasingint;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net',
                        ENABLE_CCP: true,
                        ENABLE_CCP_V2: true,
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true
                    },
                    local: {
                        ENABLE_DEV_MODE: true,
                        // Development is a default branch
                        ENABLE_SERVICE_ABUSE: 1,
                        CENTURION_ROLE: 'aah2CenturionInt',
                        ENABLE_SSO: true,
                        ENABLE_ECALL: true,
                        AZURE_DOMAIN: 'https://azne-web-roadnon-d-{{APP_NAME}}.azurewebsites.net',
                        SSO_AD_CLIENT_ID: 'a322bbe9-5856-4dd1-a772-72fb6b9822d4',
                        SSO_AD_API_CLIENT_ID: 'b72fab54-440e-4aed-a659-cae2ba6fc061',
                        SSO_AD_TENANT_ID: '52da6ceb-c432-45d8-9cee-97902996ced9',
                        SSO_AUTH_SERVICE_URL: 'api/unified-auth-service',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'https://agile-int.theaa.com/auth.html',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'https://agile-int.theaa.com/auth.html',
                        ENABLE_CCP: true,
                        ENABLE_CCP_V2: true,
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true
                    },
                    development: {
                        // Development is a default branch, let it fallback to the defaults values
                        // in case production domain is not matched
                    },
                    integration: {
                        ENABLE_DEV_MODE: true,
                        ENABLE_SERVICE_ABUSE: 1,
                        CENTURION_ROLE: 'aah2CenturionInt',
                        ENABLE_SSO: true,
                        ENABLE_ECALL: true,
                        AZURE_DOMAIN: 'https://azne-web-roadnon-d-{{APP_NAME}}.azurewebsites.net',
                        SSO_AD_CLIENT_ID: 'a322bbe9-5856-4dd1-a772-72fb6b9822d4',
                        SSO_AD_API_CLIENT_ID: 'b72fab54-440e-4aed-a659-cae2ba6fc061',
                        SSO_AD_TENANT_ID: '52da6ceb-c432-45d8-9cee-97902996ced9',
                        SSO_AUTH_SERVICE_URL: 'api/unified-auth-service',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'https://agile-int.theaa.com/auth.html',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'https://agile-int.theaa.com/auth.html',
                        ENABLE_CCP: true,
                        ENABLE_CCP_V2: true,
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true
                    },
                    verification: {
                        ENABLE_DEV_MODE: true,
                        ENABLE_SERVICE_ABUSE: 1,
                        CENTURION_ROLE: 'aah2CenturionVer',
                        ENABLE_SSO: true,
                        ENABLE_ECALL: true,
                        AZURE_DOMAIN: 'https://azne-web-roadnon-d-{{APP_NAME}}.azurewebsites.net',
                        SSO_AD_CLIENT_ID: 'a322bbe9-5856-4dd1-a772-72fb6b9822d4',
                        SSO_AD_API_CLIENT_ID: 'b72fab54-440e-4aed-a659-cae2ba6fc061',
                        SSO_AD_TENANT_ID: '52da6ceb-c432-45d8-9cee-97902996ced9',
                        SSO_AUTH_SERVICE_URL: 'api/unified-auth-service',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'https://agile-int.theaa.com/auth.html',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'https://agile-int.theaa.com/auth.html',
                        ENABLE_CCP: true,
                        ENABLE_CCP_V2: true,
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true
                    },
                    training: {
                        ENABLE_DEV_MODE: false,
                        ENABLE_SERVICE_ABUSE: 1,
                        CENTURION_ROLE: 'aah2CenturionUat',
                        SITE: 'training',
                        ENABLE_ECALL: true,
                        ENABLE_SSO: false,
                        AZURE_DOMAIN: 'XXXXXXXXXX',
                        SSO_AD_CLIENT_ID: 'XXXXXXXXXX',
                        SSO_AD_API_CLIENT_ID: 'XXXXXXXXXX',
                        SSO_AD_TENANT_ID: 'XXXXXXXXXX',
                        SSO_AUTH_SERVICE_URL: 'XXXXXXXXXX',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'XXXXXXXXXX',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'XXXXXXXXXX',
                        AZURE_STORAGE_ACCOUNT_CON_STR:
                            'DefaultEndpointsProtocol=https;AccountName=roadopsvehicleleasingint;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net',
                        ENABLE_CCP: true,
                        ENABLE_CCP_V2: true,
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true
                    },
                    hotfix: {
                        ENABLE_DEV_MODE: false,
                        ENABLE_SERVICE_ABUSE: 1,
                        CENTURION_ROLE: 'aah2CenturionUat',
                        ENABLE_SSO: false,
                        ENABLE_ECALL: true,
                        AZURE_DOMAIN: 'XXXXXXXXXX',
                        SSO_AD_CLIENT_ID: '14911a1f-450a-4155-8a78-eebb9d0f21d7',
                        SSO_AD_API_CLIENT_ID: '8d648c8b-19d5-49b2-80e8-003e30181d47',
                        SSO_AD_TENANT_ID: '52da6ceb-c432-45d8-9cee-97902996ced9',
                        SSO_AUTH_SERVICE_URL: 'api/unified-auth-service',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'https://agile-uat.theaa.com/auth.html',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'https://agile-uat.theaa.com/auth.html',
                        ENABLE_CCP: true,
                        AZURE_STORAGE_ACCOUNT_CON_STR:
                            'DefaultEndpointsProtocol=https;AccountName=roadopsvehicleleasinghot;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net',
                        ENABLE_CCP_V2: true,
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true
                    },
                    UAT: {
                        ENABLE_DEV_MODE: true,
                        ENABLE_SERVICE_ABUSE: 1,
                        CENTURION_ROLE: 'aah2CenturionUat',
                        ENABLE_SSO: true,
                        ENABLE_ECALL: true,
                        AZURE_DOMAIN: 'https://azne-web-roadnon-u-{{APP_NAME}}.azurewebsites.net',
                        SSO_AD_CLIENT_ID: '14911a1f-450a-4155-8a78-eebb9d0f21d7',
                        SSO_AD_API_CLIENT_ID: '8d648c8b-19d5-49b2-80e8-003e30181d47',
                        SSO_AD_TENANT_ID: '52da6ceb-c432-45d8-9cee-97902996ced9',
                        SSO_AUTH_SERVICE_URL: 'api/unified-auth-service',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'https://agile-uat.theaa.com/auth.html',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'https://agile-uat.theaa.com/auth.html',
                        ENABLE_CCP: true,
                        ENABLE_CCP_V2: true,
                        ECALL_DDI_VEREX: '**************',
                        ECALL_DDI_HANDLER_OVERRIDE: '**************',
                        AZURE_STORAGE_ACCOUNT_CON_STR:
                            'DefaultEndpointsProtocol=https;AccountName=roadopsvehicleleasinguat;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net',
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true
                    },
                    UAT2: {
                        ENABLE_DEV_MODE: true,
                        ENABLE_SERVICE_ABUSE: 1,
                        CENTURION_ROLE: 'aah2CenturionUat',
                        ENABLE_SSO: true,
                        ENABLE_ECALL: true,
                        AZURE_DOMAIN: 'https://azne-web-roadnon-u-{{APP_NAME}}.azurewebsites.net',
                        SSO_AD_CLIENT_ID: '14911a1f-450a-4155-8a78-eebb9d0f21d7',
                        SSO_AD_API_CLIENT_ID: '8d648c8b-19d5-49b2-80e8-003e30181d47',
                        SSO_AD_TENANT_ID: '52da6ceb-c432-45d8-9cee-97902996ced9',
                        SSO_AUTH_SERVICE_URL: 'api/unified-auth-service',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'https://agile-uat.theaa.com/auth.html',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'https://agile-uat.theaa.com/auth.html',
                        ENABLE_CCP: true,
                        ENABLE_CCP_V2: true,
                        ECALL_DDI_VEREX: '**************',
                        ECALL_DDI_HANDLER_OVERRIDE: '**************',
                        AZURE_STORAGE_ACCOUNT_CON_STR:
                            'DefaultEndpointsProtocol=https;AccountName=roadopsvehicleleasinguat;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net',
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true
                    },
                    prelive: {
                        ENABLE_DEV_MODE: false,
                        ENABLE_SERVICE_ABUSE: 1,
                        CENTURION_ROLE: 'aah2CenturionProd',
                        ENABLE_SSO: false,
                        ENABLE_ECALL: true,
                        AZURE_DOMAIN: 'XXXXXXXXXX',
                        SSO_AD_CLIENT_ID: '126f7ec8-455a-4058-8017-bfe928107bc1',
                        SSO_AD_API_CLIENT_ID: '5f3c17be-4b8d-46f1-95a5-8460dd46d2cb',
                        SSO_AD_TENANT_ID: '52da6ceb-c432-45d8-9cee-97902996ced9',
                        SSO_AUTH_SERVICE_URL: 'api/unified-auth-service',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'XXXXXXXXXX',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'XXXXXXXXXX',
                        AZURE_STORAGE_ACCOUNT_CON_STR:
                            'DefaultEndpointsProtocol=https;AccountName=roadopsvehicleleasing;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net',
                        ENABLE_CCP: true,
                        ENABLE_CCP_V2: true,
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true
                    },
                    live: {
                        ENABLE_DEV_MODE: false,
                        ENABLE_SERVICE_ABUSE: 1,
                        CENTURION_ROLE: 'aah2CenturionProd',
                        ENABLE_SSO: false,
                        ENABLE_ECALL: true,
                        AZURE_DOMAIN: 'XXXXXXXXXX',
                        SSO_AD_CLIENT_ID: '126f7ec8-455a-4058-8017-bfe928107bc1',
                        SSO_AD_API_CLIENT_ID: '5f3c17be-4b8d-46f1-95a5-8460dd46d2cb',
                        SSO_AD_TENANT_ID: '52da6ceb-c432-45d8-9cee-97902996ced9',
                        SSO_AUTH_SERVICE_URL: 'api/unified-auth-service',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'XXXXXXXXXX',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'XXXXXXXXXX',
                        AZURE_STORAGE_ACCOUNT_CON_STR:
                            'DefaultEndpointsProtocol=https;AccountName=roadopsvehicleleasing;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net',
                        ENABLE_CCP: true,
                        ENABLE_CCP_V2: true,
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true
                    },
                    defaults: {
                        ENABLE_DEV_MODE: false,
                        ENABLE_SERVICE_ABUSE: 1,
                        CENTURION_ROLE: 'aah2CenturionProd',
                        ENABLE_SSO: false,
                        ENABLE_ECALL: true,
                        AZURE_DOMAIN: 'XXXXXXXXXX',
                        SSO_AD_CLIENT_ID: '126f7ec8-455a-4058-8017-bfe928107bc1',
                        SSO_AD_API_CLIENT_ID: '5f3c17be-4b8d-46f1-95a5-8460dd46d2cb',
                        SSO_AD_TENANT_ID: '52da6ceb-c432-45d8-9cee-97902996ced9',
                        SSO_AUTH_SERVICE_URL: 'api/unified-auth-service',
                        SSO_AD_LOGIN_REDIRECT_URL: '',
                        SSO_FORGEROCK_REALM: 'partners',
                        SSO_FORGEROCK_CLIENT_ID: 'retailapp-agent',
                        SSO_FORGEROCK_LOGIN_REDIRECT_URL: 'XXXXXXXXXX',
                        SSO_FORGEROCK_LOGOUT_REDIRECT_URL: 'XXXXXXXXXX',
                        ENABLE_CCP: true,
                        ENABLE_CCP_V2: true,
                        ENABLE_PORSCHE_HIRE: true,
                        ENABLE_HYUNDAI_HIRE: true,
                        ECALL_DDI_VEREX: '02039856243',
                        ECALL_DDI_HANDLER_OVERRIDE: null,
                        AZURE_CCP_UPLOAD_CONTAINER: 'jlr', //The container names is supposed to be same on all environements
                        FLP_TYPES_WITH_UAC: [
                            55, // COURTESY HOMESTART
                            56, // COURTESY RECOVERY
                            60, // PFU SECOND RECOVERY
                            61, // PFU REPEAT FAULT
                            62, // FLP CUV
                            63, // NO TAX
                            64, // NO MOT
                            65, // NO TAX NO MOT
                            66, // SORN
                            68 // INVALID VRN
                        ],
                        FLP_SEC_RECOVERY_CODE: 60,
                        // TODO: remove when correct groups flow to the user
                        FLP_CEC_OP_IDS: [
                            // QA
                            9134881, // Nandita Nandakumar
                            9121263, // Vinit Ransubhe
                            9107233, // Vishnu Ram
                            9097990, // Unnikrishnan Thekkethil
                            // Devs
                            9099453, // Dr Fill
                            9105231, //Jeevan
                            // Prod vehicle-owners
                            9095438, // Dan Smith
                            // Business
                            9090540, // Amar Aujla
                            9117411, // Ajay Rai
                            9089452, // Altmash Hussain
                            9096780, // Balpreet Kler
                            9113222, // Ethan Somaiya
                            9102517, // Giorgia Aldridge
                            9126217, // Harry Griffin
                            9117350, // Jerome Hudson
                            9111817, // Katie	Colucci Manders
                            9112862, // Mohammed Muxiydain
                            9079452, // Shaun Cooper
                            9137339, // Taloot Hussain
                            9089277, // Usman Ahmed
                            9098090, // Emily Elwell
                            9106082, // Adam Darby
                            9116138, // Ciaran Flanagan
                            9102518, // Danielle Perigo
                            9069658, // Danielle Solomon
                            9108724, // Denise Green
                            9127781, // Hayley Gilbert
                            9111082, // Ismail Aziz
                            9106081, // Jon Oerton
                            9101695, // Jordan Butwell
                            9089777, // Manoj Chopra
                            9139250, //Regan    Carthey
                            9117060, // Swarn Sahota
                            9071035, // Waheed Hussain
                            9113550, // Josie Ward
                            9125242, // Jacob Cleary
                            9111997, // Ethan Perry
                            9117857, // Jack Fletcher
                            9129754, //Jessica    Hackett
                            9121248, // Millie Beard
                            9132206, //Isla    Davies
                            9117186, //Tyrone    Palmer
                            9116680, // Greg Medd
                            9111591, //Rajbinder    Kaur
                            9112577, //Zoe    Cooper
                            9089099, // Sam Hartland
                            9066609, // Pete Longley
                            9085917, // Kelly Harris
                            9117221, // Ben White
                            9102838, // Megan Collins
                            9104426, // Chris Stanford
                            9099770, // Robert Evans
                            9090387, // Ash Cripps
                            9073766, // Dave Allen
                            9089452, //Altmash     Hussain
                            9106078, // Taloot Hussain
                            9137339, //Taloot    Hussain
                            9089277, //Usman    Ahmed
                            9111082, //Ismail    Aziz
                            9071035, //Waheed    Hussain
                            9069658, //Danielle    Solomon
                            9115676, // Mark Cheshire
                            9087980, // Bev Wright
                            9096204, // Arron Cheshire
                            9070327, //Lyndon    Tyler
                            9095907, //Liam    Massey
                            9116212, // Mohseen Ebrahim
                            9126216, // Hamzah Hannan,
                            9117186,
                            9097554, // Kirk Jones
                            9135660, // Jason Davies
                            9105795, // Reece Murphy
                            9106375, // Zack Summers
                            9103463, // Luke Harris
                            9112469, // Franky Coleman
                            9134935, //Jamie O'Neill
                            9136450, //Amy Bonell
                            9134485, //Emma O'Neill
                            9125937, // Ash Simms,
                            9125240, //Paige Hemmings
                            9116796 //Eden Edwards
                        ]
                    }
                }
            });

            envServiceProvider.check();
        }
    ])
    .controller('aahAppController', [
        'aahSidePanelService',
        'aahNotificationService',
        function aahAppController(SidePanelService, NotificationService) {
            var ctrl = this;

            SidePanelService.init();

            _.extend(ctrl, {
                showLeft: function showLeft() {
                    return SidePanelService.leftPanelVisible();
                },
                showRight: function showRight() {
                    return SidePanelService.rightPanelVisible();
                },
                toggleLeft: function toggleLeft() {
                    SidePanelService.leftPanelVisible(!SidePanelService.leftPanelVisible());
                },
                toggleRight: function toggleRight() {
                    SidePanelService.rightPanelVisible(!SidePanelService.rightPanelVisible());
                },
                showLoader: () => {
                    return NotificationService.operationList().length > 0;
                }
            });
        }
    ])
    .run([
        '$state',
        '$rootScope',
        '$q',
        'aahAuthenticationService',
        'aahTaskService',
        'aahCustomerGroupService',
        'aahCallinfoService',
        'aahRecoveryService',
        'aahServiceTypeService',
        'aahAssistanceTypesService',
        'aahPaymentService',
        'aahStaticPromptService',
        'aahVehicleService',
        'aahPackageService',
        'aahEmergencyMessagesService',
        'aahSidePanelService',
        'aahCenturionService',
        'aahDriverDetailsService',
        'aahUserInfoService',
        'aahMappingService',
        'aahSearchService',
        'aahContractValidationService',
        'aahMobilityTaskService',
        'aahCiscoCtiEventService',
        'envService',
        'aahSSOService',
        'aahCompletionService',
        'aahCommsWorkerService',
        function (
            $state,
            $rootScope,
            $q,
            AuthenticationService,
            TaskService,
            CustomerGroupService,
            CallinfoService,
            RecoveryService,
            ServiceTypeService,
            AssistanceTypesService,
            PaymentService,
            StaticPromptService,
            VehicleService,
            PackageService,
            EmergencyMessagesService,
            SidePanelService,
            CenturionService,
            DriverDetailsSvc,
            UserInfoService,
            MappingService,
            SearchService,
            ContractValidationService,
            MobilityTaskService,
            CiscoCtiEventService,
            envService,
            aahSSOService,
            CompletionService,
            commsWorkerService
        ) {
            $rootScope.$on('$stateChangeError', function (event, toState, toParams, fromState, fromParams, error) {
                console.log('!!!!!!!!!!' + error);
            });
            const ssoEnabled = aahSSOService.isEnabled();
            let centurionRole = envService.read('CENTURION_ROLE');
            //authenticate to get initial load

            AuthenticationService.signOn()
                .then(() => {
                    // initialise web worker comms service. Let's not wait or block the app for this in case it fails.
                    commsWorkerService.start();
                    return $q.resolve();
                })
                .then(function signOnSuccess(userInfo) {
                    //load all ref data
                    return $q.all([
                        CustomerGroupService.loadCustomerGroups(),
                        ServiceTypeService.loadServiceTypes(),
                        CallinfoService.loadCallinfoTypes(),
                        RecoveryService.getForcedRecoveryFaults(),
                        AssistanceTypesService.loadAssistanceTypes(),
                        PaymentService.getPaymentGateway(),
                        PaymentService.loadPaymentReasons(),
                        CenturionService.loadQualificationOptions(),
                        CenturionService.loadLiveryOptions(),
                        CompletionService.getCancellationReasons(),
                        VehicleService.loadOperatorForcedRecoveryVehicleFault(),
                        PackageService.loadPackages(),
                        MappingService.init(),
                        ContractValidationService.enableServiceAbuse(envService.read('ENABLE_SERVICE_ABUSE')),
                        ContractValidationService.showToggleOnOFFSelector(),
                        CiscoCtiEventService.init(),
                        // aahSSOService.reportAuthFailure(userInfo)
                        //CCP Feature flag
                        MobilityTaskService.enableCCPHire(envService.read('ENABLE_CCP')),
                        MobilityTaskService.enableCCP2Hire(envService.read('ENABLE_CCP_V2')),
                        MobilityTaskService.enablePorscheHire(envService.read('ENABLE_PORSCHE_HIRE')),
                        MobilityTaskService.enableHyundaiHire(envService.read('ENABLE_HYUNDAI_HIRE'))
                    ]);
                })

                .then(function () {
                    if (!SearchService.searchTaskByQueryParam()) {
                        $state.go('home');
                    } else {
                        SearchService.searchTaskByQueryParam(false);
                    }
                })
                .then(function () {
                    //if there are emergency messages to show then make the right panel visible
                    if (EmergencyMessagesService.getEmergencyMessages().length) {
                        SidePanelService.rightPanelVisible(true);
                    }
                    // set legacy centurion group
                    centurionRole = ssoEnabled ? centurionRole : 'centurion';
                    // also bind to Centurion services ... io is a global javascript variable created by including socketio client ..
                    if (typeof io !== 'undefined' && UserInfoService.isWithinGroup(centurionRole)) {
                        console.log('Phoenix enabled for this user, connecting...');
                        CenturionService.connect(io);
                    }
                })
                .catch((error) => {
                    console.error('Failure during app initialisation.');
                    console.error(error);
                });
        }
    ])
    .run([
        '$rootScope',
        '$state',
        '$timeout',
        function ($rootScope, $state, $timeout) {
            $rootScope.$on('$viewContentLoaded', function (e) {
                $timeout(function () {
                    var state = $state.current.name,
                        el;

                    switch (state) {
                        case 'contact':
                            el = angular.element(document.getElementById('telephone'))[0];
                            break;
                        default:
                            el = angular.element(document.getElementById('search-input'))[0];
                            break;
                    }

                    if (el) {
                        el.focus();
                    }
                }, 50);
            });
        }
    ])
    .value('emptyKey', '$empty')
    .directive('emptyTypeahead', [
        'emptyKey',
        function (emptyKey) {
            return {
                require: 'ngModel',
                link: function (scope, element, attrs, modelCtrl) {
                    // this parser run before typeahead's parser
                    modelCtrl.$parsers.unshift(function (inputValue) {
                        var value = inputValue ? inputValue : emptyKey; // replace empty string with secretEmptyKey to bypass typeahead-min-length check
                        modelCtrl.$viewValue = value; // this $viewValue must match the inputValue pass to typehead directive
                        return value;
                    });

                    // this parser run after typeahead's parser
                    modelCtrl.$parsers.push(function (inputValue) {
                        return inputValue === emptyKey ? '' : inputValue; // set the secretEmptyKey back to empty string
                    });
                }
            };
        }
    ])
    .run([
        function () {
            window.onbeforeunload = function (event) {
                return 'are you sure you want to close AA Help 2?';
            };
        }
    ])
    .run([
        function () {
            console.log(
                ' __  __    _    _     ____ _____ ____   ___  __  __ \n|  \\/  |  / \\  | |   / ___|_   _|  _ \\ / _ \\|  \\/  |\n| |\\/| | / _ \\ | |   \\___ \\ | | | |_) | | | | |\\/| |\n| |  | |/ ___ \\| |___ ___) || | |  _ <| |_| | |  | |\n|_|  |_/_/   \\_\\_____|____/ |_| |_| \\_\\\\___/|_|  |_|'
            );
        }
    ])
    .value('night-theme', false);
