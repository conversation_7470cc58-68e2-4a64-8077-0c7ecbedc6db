var _ = require('lodash');
require('angular');

module.exports = angular.module('aah-speed-dials-module', []).constant('aahCTISpeedDials', [
    {
        site: '',
        name: 'AA claims - address of where to send documents ',
        number: '881514',
        obyNumber: '1304',
        chNumber: ''
    },
    {
        site: '',
        name: 'Key Assist Xfer',
        number: '881501',
        obyNumber: '183',
        chNumber: ''
    },
    {
        site: '',
        name: 'CEC SD',
        number: '881503',
        obyNumber: '166',
        chNumber: ''
    },
    {
        site: '',
        name: 'Battery Assist Call Handling',
        number: '881504',
        obyNumber: '119',
        chNumber: ''
    },
    {
        site: '',
        name: 'Wheels team',
        number: '881500',
        obyNumber: '1777',
        chNumber: ''
    },
    {
        site: '',
        name: 'BRC claims ',
        number: '881502',
        obyNumber: '1302',
        chNumber: ''
    },
    {
        site: '',
        name: 'Vehicle Inspections Car Data Check Sales Line',
        number: '1722820853',
        obyNumber: '101',
        chNumber: ''
    },
    {
        site: '',
        name: 'Jag CRC ',
        number: '1926691728',
        obyNumber: '314',
        chNumber: ''
    },
    {
        site: '',
        name: 'LR CRC',
        number: '1926691737',
        obyNumber: '315',
        chNumber: ''
    },
    {
        site: '',
        name: 'LANDROVER CAR CARE PLAN',
        number: '3445738055',
        obyNumber: '311',
        chNumber: ''
    },
    {
        site: '',
        name: 'JAGUAR CAR CARE PLAN',
        number: '3445738065',
        obyNumber: '310',
        chNumber: ''
    },
    {
        site: '',
        name: 'SAM Lloyds',
        number: '881505',
        obyNumber: '104',
        chNumber: ''
    },
    {
        site: '',
        name: 'Affinities',
        number: '881506',
        obyNumber: '1627',
        chNumber: ''
    },
    {
        site: '',
        name: 'VW Renewals',
        number: '881507',
        obyNumber: '1145',
        chNumber: ''
    },
    {
        site: '',
        name: 'Audi Renewals',
        number: '881508',
        obyNumber: '1146',
        chNumber: ''
    },
    {
        site: '',
        name: 'Skoda Renewals',
        number: '881509',
        obyNumber: '1147',
        chNumber: ''
    },
    {
        site: '',
        name: 'VWG CMMT',
        number: '1908467420',
        obyNumber: '179',
        chNumber: ''
    },
    {
        site: '',
        name: "VW Euro Enq Passenger Veh's ",
        number: '1414252802',
        obyNumber: '220',
        chNumber: ''
    },
    {
        site: '',
        name: "VW Euro Enq Commercial Veh's",
        number: '1414252803',
        obyNumber: '221',
        chNumber: ''
    },
    {
        site: '',
        name: "VW Euro Enq Press Veh's",
        number: '1414252804',
        obyNumber: '222',
        chNumber: ''
    },
    {
        site: '',
        name: "VW Euro Enq Staff Veh's",
        number: '1414252805',
        obyNumber: '223',
        chNumber: ''
    },
    {
        site: '',
        name: 'VW Hire Cars',
        number: '1908467459',
        obyNumber: '225',
        chNumber: ''
    },
    {
        site: '',
        name: 'VW Enquiry Call',
        number: '881510',
        obyNumber: '1144',
        chNumber: ''
    },
    {
        site: '',
        name: 'Personal Support Line',
        number: '881512',
        obyNumber: '1747',
        chNumber: ''
    },
    {
        site: '',
        name: 'VW Patrol Support Line',
        number: '881530',
        obyNumber: '224',
        chNumber: ''
    },
    {
        site: '',
        name: 'Staff Membership',
        number: '881511',
        obyNumber: '146',
        chNumber: ''
    },
    {
        site: '',
        name: 'NMU to SAM',
        number: '881606',
        obyNumber: '1120',
        chNumber: ''
    },
    {
        site: '',
        name: 'MSAS Transfer',
        number: '881607',
        obyNumber: '1301',
        chNumber: ''
    },
    {
        site: '',
        name: 'MSAS Transfer',
        number: '881608',
        obyNumber: '180',
        chNumber: ''
    },
    {
        site: '',
        name: 'STAY Transfers',
        number: '881609',
        obyNumber: '1199',
        chNumber: ''
    },
    {
        site: '',
        name: 'Smart Breakdown Sales (MSAS)',
        number: '881610',
        obyNumber: '108',
        chNumber: ''
    },
    {
        site: '',
        name: 'CATHIE Cathie  Support Team',
        number: '881611',
        obyNumber: '1180',
        chNumber: ''
    },
    {
        site: '',
        name: 'CATHIE Cathie Renewals',
        number: '881612',
        obyNumber: '1181',
        chNumber: ''
    },
    {
        site: '',
        name: 'CATHIE STAY',
        number: '881613',
        obyNumber: '1182',
        chNumber: ''
    },
    {
        site: '',
        name: 'New Business Xfer',
        number: '881614',
        obyNumber: '215',
        chNumber: ''
    },
    {
        site: '',
        name: 'AA Help Payment Queries',
        number: '881615',
        obyNumber: '207',
        chNumber: ''
    },
    {
        site: '',
        name: 'MSAS DPM Absence',
        number: '881516',
        obyNumber: '195',
        chNumber: ''
    },
    {
        site: '',
        name: 'SD to RCU/Recovery Logistics',
        number: '881517',
        obyNumber: '236',
        chNumber: ''
    },
    {
        site: '',
        name: 'EBC Renewals TIARA Trained',
        number: '881616',
        obyNumber: '216',
        chNumber: ''
    },
    {
        site: '',
        name: 'Cathie MSAS enquiries',
        number: '881617',
        obyNumber: '1183',
        chNumber: ''
    },
    {
        site: '',
        name: 'New Business',
        number: '881519',
        obyNumber: '406',
        chNumber: ''
    },
    {
        site: '',
        name: 'Renewal Business',
        number: '881520',
        obyNumber: '405',
        chNumber: ''
    },
    {
        site: '',
        name: 'Fleet Membership',
        number: '881521',
        obyNumber: '1555',
        chNumber: ''
    },
    {
        site: '',
        name: 'Business Support',
        number: '881618',
        obyNumber: '403',
        chNumber: ''
    },
    {
        site: '',
        name: 'Relay Plus',
        number: '881522',
        obyNumber: '130',
        chNumber: ''
    },
    {
        site: '',
        name: 'Sick Line',
        number: '881523',
        obyNumber: '149',
        chNumber: ''
    },
    {
        site: '',
        name: 'EBC Renewals CATHIE Trained',
        number: '881619',
        obyNumber: '1139',
        chNumber: ''
    },
    {
        site: '',
        name: 'EBC Service CATHIE Trained',
        number: '881620',
        obyNumber: '1141',
        chNumber: ''
    },
    {
        site: '',
        name: 'Telesales transfer to NMU',
        number: '881621',
        obyNumber: '150',
        chNumber: ''
    },
    {
        site: '',
        name: 'MREL Complaints',
        number: '881524',
        obyNumber: '151',
        chNumber: ''
    },
    {
        site: '',
        name: 'Accident Management - LTSB ',
        number: '881622',
        obyNumber: '1133',
        chNumber: ''
    },
    {
        site: '',
        name: 'MR TM',
        number: '881623',
        obyNumber: '1350',
        chNumber: ''
    },
    {
        site: '',
        name: 'EBC Service',
        number: '881525',
        obyNumber: '1160',
        chNumber: ''
    },
    {
        site: '',
        name: 'EBC Sales',
        number: '881526',
        obyNumber: '1165',
        chNumber: ''
    },
    {
        site: '',
        name: 'Manufacture Conversions (NMU)',
        number: '881527',
        obyNumber: '1626',
        chNumber: ''
    },
    {
        site: '',
        name: 'Gift',
        number: '881528',
        obyNumber: '1100',
        chNumber: ''
    },
    {
        site: '',
        name: 'Member relations - existing complaints only ',
        number: '881529',
        obyNumber: '1303',
        chNumber: ''
    },
    {
        site: '',
        name: 'Enterprise',
        number: '881576',
        obyNumber: '300',
        chNumber: ''
    },
    {
        site: '',
        name: 'MSAS Transactional',
        number: '881577',
        obyNumber: '160',
        chNumber: ''
    },
    {
        site: '',
        name: 'Tech Support',
        number: '881531',
        obyNumber: '123',
        chNumber: ''
    },
    {
        site: '',
        name: 'Tech Help Member Line',
        number: '881579',
        obyNumber: '186',
        chNumber: ''
    },
    {
        site: '',
        name: 'Halifax AVA',
        number: '**********',
        obyNumber: '1152',
        chNumber: ''
    },
    {
        site: '',
        name: 'Bank of Scotland AVA',
        number: '**********',
        obyNumber: '1142',
        chNumber: ''
    },
    {
        site: '',
        name: 'CH to TSC',
        number: '881580',
        obyNumber: '228',
        chNumber: ''
    },
    {
        site: '',
        name: 'Tech Help Take Care',
        number: '881581',
        obyNumber: '153',
        chNumber: ''
    },
    {
        site: '',
        name: 'Car Genie Enquiries',
        number: '881582',
        obyNumber: '197',
        chNumber: ''
    },
    {
        site: '',
        name: 'Car Genie Sales',
        number: '881583',
        obyNumber: '192',
        chNumber: ''
    },
    {
        site: '',
        name: 'Car Genie THD IVR',
        number: '881532',
        obyNumber: '229',
        chNumber: ''
    },
    {
        site: '',
        name: 'MSAS to Connected Services ',
        number: '881585',
        obyNumber: '255',
        chNumber: ''
    },
    {
        site: '',
        name: 'Battery Assist to NMU',
        number: '881586',
        obyNumber: '1151',
        chNumber: ''
    },
    {
        site: '',
        name: 'Accident Management - Pay for use accounts',
        number: '881587',
        obyNumber: '1134',
        chNumber: ''
    },
    {
        site: '',
        name: 'Accident Management - Centrica',
        number: '881588',
        obyNumber: '1131',
        chNumber: ''
    },
    {
        site: '',
        name: 'Accident Management - Alfa + Fiat',
        number: '881589',
        obyNumber: '1130',
        chNumber: ''
    },
    {
        site: '',
        name: 'Bravo',
        number: '881590',
        obyNumber: '114',
        chNumber: ''
    },
    {
        site: '',
        name: 'Mrel to TSC ',
        number: '881591',
        obyNumber: '1800',
        chNumber: ''
    },
    {
        site: '',
        name: 'EB Ringbacks from ACT to EB ',
        number: '881592',
        obyNumber: '185',
        chNumber: ''
    },
    {
        site: '',
        name: 'Overlength Vehicles',
        number: '881593',
        obyNumber: '131',
        chNumber: ''
    },
    {
        site: '',
        name: 'Training Role Play',
        number: '881594',
        obyNumber: '113',
        chNumber: ''
    },
    {
        site: '',
        name: 'Vehicle Assessment Technicians',
        number: '881595',
        obyNumber: '172',
        chNumber: ''
    },
    {
        site: '',
        name: 'Excessive Servicing',
        number: '881596',
        obyNumber: '182',
        chNumber: ''
    },
    {
        site: '',
        name: 'NMU/SD to EB trade ups',
        number: '881597',
        obyNumber: '1101',
        chNumber: ''
    },
    {
        site: '',
        name: 'Buzby Transfer',
        number: '881598',
        obyNumber: '213',
        chNumber: ''
    },
    {
        site: '',
        name: 'HSU ',
        number: '881599',
        obyNumber: '206',
        chNumber: ''
    },
    {
        site: '',
        name: 'Tradeups for non FCA compliant Staff',
        number: '881600',
        obyNumber: '124',
        chNumber: ''
    },
    {
        site: '',
        name: 'CATHIE WJ Trained',
        number: '881601',
        obyNumber: '330',
        chNumber: ''
    },
    {
        site: '',
        name: 'My AA App - Admin.com',
        number: '881603',
        obyNumber: '1108',
        chNumber: ''
    },
    {
        site: '',
        name: 'Personal Breakdown and Will Joins ',
        number: '881604',
        obyNumber: '1300',
        chNumber: ''
    },
    {
        site: '',
        name: 'Motorcycle Ins',
        number: '1454410470',
        obyNumber: '460',
        chNumber: ''
    },
    {
        site: '',
        name: 'Thrifty Central Reservations',
        number: '1494751566',
        obyNumber: '305',
        chNumber: ''
    },
    {
        site: '',
        name: 'Mis - directed calls to Cigna',
        number: '1613330787',
        obyNumber: '1201',
        chNumber: ''
    },
    {
        site: '',
        name: 'TI Go Warm Transfer',
        number: '1752451360',
        obyNumber: '1113',
        chNumber: ''
    },
    {
        site: '',
        name: 'TI Sales Warm Transfer',
        number: '1752451361',
        obyNumber: '1122',
        chNumber: ''
    },
    {
        site: '',
        name: 'TI 1 Stop Warm Transfer',
        number: '1752451362',
        obyNumber: '1114',
        chNumber: ''
    },
    {
        site: '',
        name: 'Classi Car',
        number: '3301233879',
        obyNumber: '1421',
        chNumber: ''
    },
    {
        site: '',
        name: 'Short Term Insurance',
        number: '3445575250',
        obyNumber: '1418',
        chNumber: ''
    },
    {
        site: '',
        name: 'AA Select',
        number: '3456070734',
        obyNumber: '1422',
        chNumber: ''
    },
    {
        site: '',
        name: 'Credit Card Applications',
        number: '8001712034',
        obyNumber: '145',
        chNumber: ''
    },
    {
        site: '',
        name: 'Pet Insurance',
        number: '8002942713',
        obyNumber: '458',
        chNumber: ''
    },
    {
        site: '',
        name: 'Autowinshields',
        number: '8003167437',
        obyNumber: '120',
        chNumber: ''
    },
    {
        site: '',
        name: 'Minibus Ins',
        number: '8003891727',
        obyNumber: '275',
        chNumber: ''
    },
    {
        site: '',
        name: 'Classic Car Ins',
        number: '903301233879',
        obyNumber: '390',
        chNumber: ''
    },
    {
        site: '',
        name: 'AA Travel Insurance - Drakefield ',
        number: '8009125002',
        obyNumber: '1170',
        chNumber: ''
    },
    {
        site: '',
        name: 'Caravan Ins',
        number: '**********',
        obyNumber: '453',
        chNumber: ''
    },
    {
        site: '',
        name: 'NCR',
        number: '************',
        obyNumber: '301',
        chNumber: ''
    },
    {
        site: '',
        name: 'Accident Response ',
        number: '************',
        obyNumber: '252',
        chNumber: ''
    },
    {
        site: '',
        name: 'Ultimate Reward Current Account Enq',
        number: '**********',
        obyNumber: '152',
        chNumber: ''
    },
    {
        site: '',
        name: 'Stay to Cathie Support',
        number: '881605',
        obyNumber: '1179',
        chNumber: ''
    },
    {
        site: '',
        name: 'BSCC BELGIUM',
        number: '**************',
        obyNumber: '205',
        chNumber: ''
    },
    {
        site: '',
        name: 'BSCC FRANCE',
        number: '**************',
        obyNumber: '201',
        chNumber: ''
    },
    {
        site: '',
        name: 'BSCC GERMANY',
        number: '***************',
        obyNumber: '204',
        chNumber: ''
    },
    {
        site: '',
        name: 'BSCC ITALY',
        number: '***************',
        obyNumber: '202',
        chNumber: ''
    },
    {
        site: '',
        name: 'BSCC SPAIN',
        number: '**************',
        obyNumber: '203',
        chNumber: ''
    },
    {
        site: '',
        name: 'Tax Ins',
        number: '2086919751',
        obyNumber: '457',
        chNumber: ''
    },
    {
        site: '',
        name: 'Credit Cards',
        number: '2392899221',
        obyNumber: '210',
        chNumber: ''
    },
    {
        site: '',
        name: 'Freedom (Used for TI staff)',
        number: '1223446931',
        obyNumber: '1125',
        chNumber: ''
    },
    {
        site: '',
        name: 'Life Ins',
        number: '1243819891',
        obyNumber: '990',
        chNumber: ''
    },
    {
        site: '',
        name: 'Ducatti Internation ',
        number: '90080033228877',
        obyNumber: '231',
        chNumber: ''
    },
    {
        site: '',
        name: 'Mercedes Internation',
        number: '90080037777777',
        obyNumber: '232',
        chNumber: ''
    },
    {
        site: '',
        name: 'Infiniti France ',
        number: '90033141858585',
        obyNumber: '233',
        chNumber: ''
    },
    {
        site: '',
        name: 'Infiniti Poland',
        number: '90048225222904',
        obyNumber: '234',
        chNumber: ''
    },
    {
        site: '',
        name: 'Infiniti Italy ',
        number: '900390226609615',
        obyNumber: '235',
        chNumber: ''
    },
    {
        site: '',
        name: 'Business Insurance(Moorhouse)',
        number: '2920849564',
        obyNumber: '190',
        chNumber: ''
    },
    {
        site: '',
        name: 'Business Ins',
        number: '2920849574',
        obyNumber: '459',
        chNumber: ''
    },
    {
        site: '',
        name: 'Insurance',
        number: '881558',
        obyNumber: '112',
        chNumber: ''
    },
    {
        site: '',
        name: 'PST to AA Select',
        number: '881559',
        obyNumber: '1104',
        chNumber: ''
    },
    {
        site: '',
        name: 'Home Transfers Road to Insurance',
        number: '881560',
        obyNumber: '189',
        chNumber: ''
    },
    {
        site: '',
        name: 'Motor Insurance Renewals ',
        number: '881561',
        obyNumber: '254',
        chNumber: ''
    },
    {
        site: '',
        name: 'CCU to CSU',
        number: '881562',
        obyNumber: '1103',
        chNumber: ''
    },
    {
        site: '',
        name: 'Motor Insurance Service',
        number: '881563',
        obyNumber: '1405',
        chNumber: ''
    },
    {
        site: '',
        name: 'AA Insurance - Claimline',
        number: '881564',
        obyNumber: '1404',
        chNumber: ''
    },
    {
        site: '',
        name: 'Installments',
        number: '881565',
        obyNumber: '121',
        chNumber: ''
    },
    {
        site: '',
        name: 'Credit TIA',
        number: '881566',
        obyNumber: '122',
        chNumber: ''
    },
    {
        site: '',
        name: 'AA Home Insurance',
        number: '881567',
        obyNumber: '1401',
        chNumber: ''
    },
    {
        site: '',
        name: 'Home Renewals',
        number: '881568',
        obyNumber: '1425',
        chNumber: ''
    },
    {
        site: '',
        name: 'Project Red BAU - Road to Insurance',
        number: '881569',
        obyNumber: '1158',
        chNumber: ''
    },
    {
        site: '',
        name: 'MSAS Motor Leads to Insurance',
        number: '881570',
        obyNumber: '212',
        chNumber: ''
    },
    {
        site: '',
        name: 'Project Glasgow',
        number: '881571',
        obyNumber: '102',
        chNumber: ''
    },
    {
        site: '',
        name: 'Collections',
        number: '881572',
        obyNumber: '118',
        chNumber: ''
    },
    {
        site: '',
        name: 'Claimline',
        number: '881573',
        obyNumber: '139',
        chNumber: ''
    },
    {
        site: '',
        name: 'Accident Assist xfer to Claims',
        number: '881574',
        obyNumber: '162',
        chNumber: ''
    },
    {
        site: '',
        name: 'SIU',
        number: '881575',
        obyNumber: '143',
        chNumber: ''
    },
    {
        site: '',
        name: 'Fuel Assist Sales Team',
        number: '881557',
        obyNumber: '127',
        chNumber: ''
    },
    {
        site: '',
        name: '',
        number: '881556',
        obyNumber: '214',
        chNumber: ''
    },
    {
        site: '',
        name: 'Will Joins',
        number: '881555',
        obyNumber: '208',
        chNumber: ''
    },
    {
        site: '',
        name: 'Lyon European Operations',
        number: '881624',
        obyNumber: '',
        chNumber: ''
    },
    {
        site: '',
        name: "ROAD IT - don't use",
        number: '881628',
        obyNumber: '',
        chNumber: ''
    },
    {
        site: '',
        name: 'RMS',
        number: '903300418298',
        obyNumber: '03300418298',
        chNumber: ''
    },
    {
        site: '',
        name: 'Toyota',
        number: '01216290485',
        obyNumber: '',
        chNumber: ''
    },
    {
        site: '',
        name: 'Lexus',
        number: '01216290486',
        obyNumber: '',
        chNumber: ''
    }
]);
