import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Case } from '@aa/data-models/common';
import { DataStore, DataStoreProviderType, JobHistoryStore } from '@aa/data-store';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubCheckpoint, EventHubReceiver, EventHubReceiverConfig, QueueEventHandler } from '@aa/queue';
import { BackendEnvironment } from '@aa/utils';

const appName = 'case-processor';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.CASE_SERVICE_PROCESSOR;
    protected auxStreamReceiver: EventHubReceiver<CUVEvents.CR_CREATED, Case, void>;
    protected legacyDatastore: DataStore;
    protected store: JobHistoryStore;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.MONGODB]
        });

        this.legacyDatastore = new DataStore({
            logger: this.logger,
            providers: [
                {
                    type: DataStoreProviderType.MONGODB,
                    config: {
                        logger: this.logger,
                        system: this.system,
                        connectionString: environment.mongodb.mongodbUrl,
                        appName: this.name
                    }
                },
                {
                    type: DataStoreProviderType.ORACLE,
                    config: {
                        logger: this.logger,
                        system: this.system,
                        appName,
                        ...environment.oracle
                    }
                }
            ]
        });

        this.store = new JobHistoryStore({
            dataStore: this.legacyDatastore,
            databaseName: environment.mongodb.mongoJobDbName,
            logger: this.logger
        });

        const checkpoint = new EventHubCheckpoint({
            accountUrl: environment.queue.storageAccountUrl || '',
            accountName: environment.queue.storageAccountName || '',
            accountKey: environment.queue.storageAccountKey || '',
            containerName: 'case-processor',
            logger: this.logger
        });

        const ehBaseConfig: Omit<EventHubReceiverConfig, 'eventHubName' | 'consumerGroup' | 'checkpoint'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            maxBatchSize: 1, // we want to process even 1
            maxDelayPerBatch: 2 // 2s
        };

        //from task-service
        this.auxStreamReceiver = new EventHubReceiver({
            ...ehBaseConfig,
            checkpoint,
            eventHubName: 'aux-stream',
            consumerGroup: 'case-processor'
        });

        console.log('listening on ', CUVEvents.CR_CREATED);
        this.auxStreamReceiver.on(CUVEvents.CR_CREATED, this.crCreatedHandler);
    }

    protected crCreatedHandler: QueueEventHandler<CUVEvents.CR_CREATED, Case, void> = async (context) => {
        this.logger.info({
            sourceName: this.name,
            message: 'auxStreamReceiver event received',
            data: { context }
        });
        try {
            const { entry } = context;
            const data = entry.data;
            console.log('######################################################');
            console.log('data', data);
            console.log('######################################################');
            this.logger.log(`auxStreamReceiver inserting for crId ${data.customerRequestId}`);
            await this.store.insertCase(data);
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                message: 'auxStreamReceiver event error',
                data: { error }
            });
        }
    };
}
