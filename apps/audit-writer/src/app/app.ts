import { AuditEvent } from '@aa/data-models/common';
import { AuditStore } from '@aa/data-store';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubCheckpoint, QueueEventHandler, EventHubReceiver } from '@aa/queue';
import { BackendEnvironment } from '@aa/utils';

export class App extends Microservice {
    public name = 'Audit Writer';
    public application = BackendApplication.AUDIT_WRITER;
    protected auditStreamReceiver!: EventHubReceiver<string, AuditEvent, void>;
    protected store: AuditStore;

    constructor(environment: BackendEnvironment) {
        super({ environment, appName: 'audit-writer' });

        this.store = new AuditStore({
            logger: this.logger,
            dataStore: this.dataStore
        });

        const checkpoint = new EventHubCheckpoint({
            accountUrl: environment.queue.storageAccountUrl || '',
            accountName: environment.queue.storageAccountName || '',
            accountKey: environment.queue.storageAccountKey || '',
            containerName: 'audit-writer',
            logger: this.logger
        });

        this.auditStreamReceiver = new EventHubReceiver({
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            eventHubName: 'audit-stream',
            consumerGroup: 'audit-writer',
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            checkpoint,
            // we can be relaxed with audit processing
            maxBatchSize: 10, // we want to process even 1
            maxDelayPerBatch: 10, // 10s, no rush here
            regexEventTypes: true
        });

        // match any audit event
        this.auditStreamReceiver.on('^create\\/audit\\/[a-zA-Z1-9]+$', this.auditWriter);
    }

    private auditWriter: QueueEventHandler<string, AuditEvent, void> = async ({ action, entry }) => {
        this.logger.log(`Audit processor of event action ${action}, trace ${entry.data.trace}`);

        await this.store.setAuditEntry(entry.data);
    };
}
