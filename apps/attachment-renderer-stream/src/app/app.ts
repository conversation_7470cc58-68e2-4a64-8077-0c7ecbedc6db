import { AuditClient } from '@aa/audit-client';
import {
    Attachment,
    EmailStatus,
    EventType,
    MimeType,
    Namespace,
    NotUploadedAttachment,
    SendableEmail,
    StoredEmail,
    UploadedAttachment,
    WithDataId,
    WithSendableAttachments
} from '@aa/data-models/common';
import { DataStoreProviderType, MongodbDataProvider } from '@aa/data-store';
import { DocumentClient } from '@aa/document-client';
import { EventCode, Exception } from '@aa/exception';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { PageData, PDFRenderer } from '@aa/pdf-renderer';
import { EventHubSender, EventHubSenderConfig, QueueEventHandler, ServiceBusReceiver, ServiceBusReceiverConfig } from '@aa/queue';
import { BackendEnvironment } from '@aa/utils';
import { Buffer } from 'buffer';
import { ObjectId, WithId } from 'mongodb';

export class App extends Microservice {
    public name = 'Attachment Renderer Stream';
    public application = BackendApplication.ATTACHMENT_RENDERER_STREAM;
    protected mongoProvider!: MongodbDataProvider;
    protected emailsAttachmentsReceiver: ServiceBusReceiver<EventType.RENDER_ATTACHMENT, WithSendableAttachments<WithDataId<SendableEmail>>>;
    protected auditClient: AuditClient;
    protected commsStreamSender: EventHubSender<EventType.OUTBOUND_EMAIL, WithDataId<SendableEmail>>;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName: 'attachment-renderer-stream',
            dataStoreProviders: [DataStoreProviderType.MONGODB]
        });

        this.mongoProvider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);

        this.auditClient = new AuditClient({
            application: BackendApplication.ATTACHMENT_RENDERER_STREAM,
            connector: this.connector,
            operatorId: -1
        });

        const sbqBaseConfigReceiver: Omit<ServiceBusReceiverConfig, 'queueName'> = {
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            connectionString: environment.queue.SBQConnectionString,
            hasDeadletter: true
        };

        const evhSenderBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };

        this.commsStreamSender = new EventHubSender({
            ...evhSenderBase,
            eventHubName: 'comms-stream'
        });

        this.emailsAttachmentsReceiver = new ServiceBusReceiver({
            ...sbqBaseConfigReceiver,
            queueName: 'attachment-renderer-queue'
        });

        this.emailsAttachmentsReceiver.on(EventType.RENDER_ATTACHMENT, this.renderAttachments);
    }

    protected renderAttachments: QueueEventHandler<EventType.RENDER_ATTACHMENT, WithSendableAttachments<WithDataId<SendableEmail>>, void> = async (context) => {
        try {
            const {
                entry: { data: email }
            } = context;
            const collection = await this.mongoProvider.collection<StoredEmail>('entities', 'euopsMessages');

            const storedEmail = await collection.findOne({ _id: new ObjectId(email._id) });

            // if status sending attachments rendering bailout
            if (!storedEmail || storedEmail.status === EmailStatus.SENDING_ATTACHMENT_RENDERING) {
                return;
            }

            const processedEmail: WithSendableAttachments<WithId<SendableEmail>> = {
                ...storedEmail,
                ...email,
                _id: new ObjectId(),
                status: EmailStatus.SENDING_ATTACHMENT_RENDERING
            };

            await collection.updateOne(
                { _id: processedEmail._id },
                {
                    $set: processedEmail
                }
            );

            const trace = this.auditClient.getTrace(Namespace.EUOPS, processedEmail._id.toString());
            await this.auditClient.reportAction(trace, {
                message: 'Attachment renderer queue email audit',
                data: { ...processedEmail }
            });
            const attachments: UploadedAttachment[] = [];
            let outboundEmail: WithSendableAttachments<WithDataId<SendableEmail>> = { ...email, attachments: [] };

            if (processedEmail.attachments) {
                for (const attachment of processedEmail.attachments) {
                    // if not uploaded attachment but can be rendered as PDF
                    if (isNotUploadedAttachment(attachment)) {
                        const client = new DocumentClient({
                            httpClient: this.httpClient,
                            connector: this.connector
                        });
                        const pdfRenderer = new PDFRenderer();
                        // TODO: this needs to detect type of file before it renders it
                        const partials: PageData[] = [{ type: MimeType.HTML, data: attachment.data }];
                        const pdfResult = await pdfRenderer.render(partials);
                        const pdfResultBuffer = Buffer.from(pdfResult);
                        const result = await client.upload(attachment, pdfResultBuffer, true);
                        const { name, mimeType, docName, container } = attachment;
                        const uploadedAttachment: UploadedAttachment = {
                            name,
                            mimeType,
                            docId: result.docName,
                            container
                        };
                        attachments.push(uploadedAttachment);
                    } else if (isUploadedAttachment(attachment)) {
                        attachments.push(attachment);
                    } else {
                        throw new Exception({
                            source: this.name,
                            message: `${this.name}: Unknown attachment type`
                        });
                    }
                }

                outboundEmail = {
                    ...processedEmail,
                    attachments,
                    _id: processedEmail._id.toString()
                };

                await this.commsStreamSender.send(EventType.OUTBOUND_EMAIL, outboundEmail);
            }

            await this.commsStreamSender.send(EventType.OUTBOUND_EMAIL, outboundEmail);
        } catch (error) {
            throw new Exception({
                sourceName: this.name,
                code: EventCode.DB_OPS_FAIL,
                error,
                message: 'Failed to render attachments'
            });
        }
    };
}

function isNotUploadedAttachment(attachment: Attachment): attachment is NotUploadedAttachment {
    return 'custReqId' in attachment && 'taskId' in attachment;
}

function isUploadedAttachment(attachment: Attachment): attachment is UploadedAttachment {
    return 'docId' in attachment;
}
