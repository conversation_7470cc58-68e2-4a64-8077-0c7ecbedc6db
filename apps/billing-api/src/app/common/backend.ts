import { AuditClient } from '@aa/audit-client';
import { applicationBasePaths } from '@aa/connector';
import { CreditNote, ExchangeRate, Invoice, InvoiceLineItem, MiEvents, RemittanceBatch, TripClaimCost, TripFeeCost, TripServiceCost } from '@aa/data-models/common';
import { DataStoreProviderType } from '@aa/data-store';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { ProductBenefitClient } from '@aa/product-benefit-client';
import { EventHubSender, EventHubSenderConfig } from '@aa/queue';
import { environment } from '@aa/system';
import { BackendEnvironment } from '@aa/utils';
import { SupplierClient } from '@aa/supplier-client';

class Backend extends Microservice {
    public name: string;
    public application: BackendApplication;
    public miStreamClaimCostSender: EventHubSender<MiEvents.CLAIM_COSTS, TripClaimCost>;
    public miStreamFeeCostSender: EventHubSender<MiEvents.FEE_COSTS, TripFeeCost>;
    public miStreamServiceCostSender: EventHubSender<MiEvents.SERVICE_COSTS, TripServiceCost>;
    public miStreamInvoiceSender: EventHubSender<MiEvents.INVOICE, Invoice>;
    public miStreamInvoiceLineItemSender: EventHubSender<MiEvents.INVOICE_LINE_ITEM, InvoiceLineItem>;
    public miStreamRemittanceBatchSender: EventHubSender<MiEvents.REMITTANCE_BATCH, RemittanceBatch>;
    public miStreamCreditNoteSender: EventHubSender<MiEvents.CREDIT_NOTE, CreditNote>;
    public miStreamExchangeRateSender: EventHubSender<MiEvents.EXCHANGE_RATE, ExchangeRate>;
    public auditClient: AuditClient;
    public supplierClient: SupplierClient;
    public productBenefitClient: ProductBenefitClient;

    constructor(appName: string, environment: BackendEnvironment, application: BackendApplication, dataStoreProviders = [DataStoreProviderType.MONGODB]) {
        super({
            environment,
            appName,
            dataStoreProviders
        });
        this.name = appName;
        this.application = application;

        const configBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };

        this.auditClient = new AuditClient({
            application: BackendApplication.BILLING_API,
            connector: this.connector,
            operatorId: -1
        });
        this.supplierClient = new SupplierClient({ httpClient: this.httpClient, connector: this.connector });
        this.productBenefitClient = new ProductBenefitClient({ httpClient: this.httpClient, connector: this.connector });
        // TODO: below is not an efficient way, we should follow on example of EventEmitterV2
        this.miStreamClaimCostSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
        this.miStreamFeeCostSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
        this.miStreamServiceCostSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
        this.miStreamInvoiceSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
        this.miStreamInvoiceLineItemSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
        this.miStreamRemittanceBatchSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
        this.miStreamCreditNoteSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
        this.miStreamExchangeRateSender = new EventHubSender({
            ...configBase,
            eventHubName: 'mi-stream'
        });
    }
}

environment.server.port = parseInt(process.env.billingApiServicePort || process.env.PORT || '') || 7833;
environment.server.basePath = process.env.billingApiServiceEndPoint || applicationBasePaths.BILLING_API;

export const backend = new Backend('billing-api', environment, BackendApplication.BILLING_API, [DataStoreProviderType.MONGODB, DataStoreProviderType.REDIS]);
