import {
    BenefitLimit,
    BenefitType,
    ChargeDefinition,
    ChargeType,
    CostDefinition,
    CreateReasonLabels,
    CreditNote,
    CreditNoteStatus,
    Currency,
    ExtendedPaginationQueryResult,
    InvoiceLineItem,
    MiEvents,
    NativeValue,
    QueryTripCost,
    QueryTripCostSchema,
    SimplifiedValidationResult,
    Trip,
    TripCost,
    TripCostEstimate,
    TripCostEstimateResponse,
    TripCostStatus,
    TripCostType,
    UpsertTripCost,
    UpsertTripCostSchema,
    Value
} from '@aa/data-models/common';
import { isFLPTask } from '@aa/data-models/entities/flp-task';
import {
    BreakdownTaskPreview,
    CarHireTaskPreview,
    HotelTaskPreview,
    isBreakdownTaskPreview,
    isCarHireTaskPreview,
    isGarageRepairTaskPreview,
    isHotelTaskPreview,
    isRecoveryTaskPreview,
    isStorageTaskPreview,
    isTransportTaskPreview,
    RecoveryTaskPreview,
    StorageTaskPreview,
    TaskPreview,
    TransportTaskPreview
} from '@aa/data-models/entities/task-preview';
import { DataStoreProviderType } from '@aa/data-store';
import { MongodbUtils } from '@aa/data-store-utils';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { EntitlementBenefitClient } from '@aa/entitlement-benefit-client';
import { getResponse } from '@aa/server-utils';
import { SupplierClient } from '@aa/supplier-client';
import { TaskClient } from '@aa/task-client';
import { TripClient } from '@aa/trip-clients';
import { Utils } from '@aa/utils';
import { Request, Response } from 'express';
import { Filter } from 'mongodb';
import { backend } from '../common/backend';
import { canExtend, canUnextend } from '../common/can-extend';
import { convertExchangeRate } from '../common/convert-exchange-rate';
import { EntitlementData } from '@aa/data-models/aux/entitlement';

export function registerCostEndpoints() {
    backend.server.post('/cost', createHandler, { schema: UpsertTripCostSchema });
    backend.server.get('/cost/estimate/:taskId', estimateHandler);
    backend.server.get('/cost/:code', getHandler);
    backend.server.get('/cost/:code/complete', completeHandler);
    backend.server.get('/cost/:code/cancel', cancelHandler);
    backend.server.get('/cost/:code/extend', extendHandler);
    backend.server.get('/cost/:code/unextend', unextendHandler);
    backend.server.get('/cost/:code/can-extend', canExtendHandler);
    backend.server.get('/cost/:code/can-unextend', canUnextendHandler);
    backend.server.post('/cost/:code', updateHandler, {
        schema: UpsertTripCostSchema
    });
    backend.server.post('/costs', queryCosts, { schema: QueryTripCostSchema });
}

const createHandler = async (req: Request<unknown, unknown, UpsertTripCost, unknown>, res: Response<TripCost>): Promise<void> => {
    try {
        backend.logger.log('#####################################################################################################');
        backend.logger.log('#####################################################################################################');
        backend.logger.log(`Adding cost: ${req.body}`);
        backend.logger.log('#####################################################################################################');
        backend.logger.log('#####################################################################################################');
        const tripCostData = req.body;

        const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<TripCost>('entities', 'tripCosts');

        // get native value for cost
        const { forecast } = tripCostData;
        const forecastNative = await convertExchangeRate({
            from: forecast.currency,
            to: Currency.GBP,
            amount: forecast.amount,
            date: new Date()
        });

        if (!forecastNative) {
            throw new Error('Failed while calculating forecast in native currency');
        }

        const date = new Date();
        const tripCost: TripCost = {
            ...tripCostData,
            status: TripCostStatus.READY,
            code: Utils.uuid(),
            extended: false,
            updated: date,
            created: date,
            forecastNative: forecastNative as NativeValue,
            actual: { amount: 0, currency: forecast.currency },
            actualNative: { amount: 0, currency: Currency.GBP }
        };
        // Updating if cost exists, otherwise create new cost
        await collection.updateOne({ taskId: tripCost.taskId }, { $setOnInsert: tripCost }, { upsert: true });

        await sendTripCostsToMI(tripCost);

        return getResponse(res, ServerResponseCode.OK, tripCost);
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed creating new trip cost`,
            data: { ...req.body }
        });
        backend.logger.error(exception);
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};

const getHandler = async (
    req: Request<
        {
            code: string;
        },
        unknown,
        unknown,
        unknown
    >,
    res: Response<TripCost>
): Promise<void> => {
    try {
        const { code } = req.params;
        backend.logger.log(`Getting cost: ${code}`);

        const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<TripCost>('entities', 'tripCosts');

        const result = await collection.findOne({ code });

        if (!result) {
            return getResponse(res, ServerResponseCode.NOT_FOUND, {
                message: 'Cost not found for provided code'
            });
        }

        const withoutId = MongodbUtils.withoutId(result);
        return getResponse(res, ServerResponseCode.OK, withoutId);
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed getting cost`
        });
        backend.logger.error(exception);
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};

const estimateHandler = async (
    req: Request<
        {
            taskId: string;
        },
        unknown,
        unknown,
        unknown
    >,
    res: Response<TripCostEstimateResponse>
): Promise<void> => {
    const taskId = Number.parseInt(req.params.taskId);
    try {
        backend.logger.log({
            sourceName: backend.name,
            message: `Getting estimate for task`,
            data: { taskId }
        });

        if (!taskId) {
            backend.logger.warn({
                sourceName: backend.name,
                message: `Missing task id`,
                data: { taskId }
            });
            return getResponse(res, ServerResponseCode.BAD_REQUEST, { message: 'Missing task id' });
        }

        const taskClient = new TaskClient({ httpClient: backend.httpClient, connector: backend.connector });
        const task = await taskClient.getTaskPreviewByTaskId(taskId);

        if (!task) {
            backend.logger.warn({
                sourceName: backend.name,
                message: `Task not found`,
                data: { taskId }
            });
            return getResponse(res, ServerResponseCode.BAD_REQUEST, { message: 'Task not found for provided id' });
        }
        if (isFLPTask(task)) {
            backend.logger.warn({
                sourceName: backend.name,
                message: `FLP task is not supported`,
                data: { taskId }
            });
            return getResponse(res, ServerResponseCode.BAD_REQUEST, { message: 'FLP task is not supported' });
        }

        const customerRequestId = task.customerRequestId;
        const entitlement = await backend.connector.entitlement.find.byTaskId(taskId);
        const customerGroup = entitlement?.policy?.customerGroup.code;

        if (!customerGroup) {
            backend.logger.warn({
                sourceName: backend.name,
                message: `Unable to find customer group for task`,
                data: { taskId }
            });
            return getResponse(res, ServerResponseCode.BAD_REQUEST, { message: 'Unable to find customer group for task' });
        }

        // get trip for customer request so we can get list of benefits we can charge against
        const tripClient = new TripClient({ httpClient: backend.httpClient, connector: backend.connector });
        const trip = await tripClient.getTripByCR(customerRequestId);

        if (!trip) {
            backend.logger.warn({
                sourceName: backend.name,
                message: `Trip associated with task not found`,
                data: { taskId, customerRequestId }
            });
            return getResponse(res, ServerResponseCode.BAD_REQUEST, {
                message: 'Unable to find trip associated with task'
            });
        }

        // if no benefit limits assigned

        if (!trip.benefitLimits.length) {
            backend.logger.warn({
                sourceName: backend.name,
                message: `No benefit limits found for trip`,
                data: { taskId, tripCode: trip.code }
            });
        }

        // get all benefit limits so we can assess what we include in estimate
        const entitlementBenefitClient = new EntitlementBenefitClient({
            httpClient: backend.httpClient,
            connector: backend.connector
        });

        const benefitLimits: BenefitLimit[] = [];
        for (const benefitLimitCode of trip.benefitLimits) {
            const limit = await entitlementBenefitClient.getBenefitLimitByCode(benefitLimitCode);
            if (!limit) {
                backend.logger.warn({
                    sourceName: backend.name,
                    message: `No benefit limit found`,
                    data: { taskId, tripCode: trip.code, benefitLimitCode }
                });
            } else {
                benefitLimits.push(limit);
            }
        }

        let allEstimates: TripCostEstimateResponse;

        // Depending on task type
        // - generate cost estimate(s)
        // - dont generate cost estimate(s) - free of charge
        // TODO: Add support for:
        //   - Flight task
        //   - Financial task - Claim	Task data
        //   - Financial task - Fee	Task data
        //   - What about storage task
        //   use getFinancialTaskEstimates
        if (isBreakdownTaskPreview(task)) {
            allEstimates = await getBreakdownTaskEstimates(task, entitlement, benefitLimits, trip);
        } else if (isRecoveryTaskPreview(task)) {
            allEstimates = await getRecoveryTaskEstimates(task, entitlement, benefitLimits, trip);
        } else if (isCarHireTaskPreview(task)) {
            allEstimates = await getHireCarTaskEstimates(task, entitlement, benefitLimits, trip);
        } else if (isTransportTaskPreview(task)) {
            allEstimates = await getTransportTaskEstimates(task, entitlement, benefitLimits, trip);
        } else if (isHotelTaskPreview(task)) {
            allEstimates = await getHotelTaskEstimates(task, entitlement, benefitLimits, trip);
        } else if (isStorageTaskPreview(task)) {
            allEstimates = await getStorageTaskEstimates(task, entitlement, benefitLimits, trip);
        } else if (isGarageRepairTaskPreview(task)) {
            allEstimates = await getGarageRepairTaskEstimates(task, entitlement, benefitLimits, trip);
        } else {
            allEstimates = await getUnsupportedTaskEstimates(task, entitlement, benefitLimits, trip);
        }

        backend.logger.info({
            sourceName: backend.name,
            message: `${allEstimates.estimates.length ? 'Estimates generated' : 'No estimates generated'}`,
            data: { taskId, tripCode: trip.code, estimates: allEstimates }
        });
        return getResponse(res, ServerResponseCode.OK, allEstimates);
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed getting cost`
        });
        backend.logger.error({
            sourceName: backend.name,
            message: `Failed getting cost`,
            error: exception,
            data: { taskId }
        });
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};

const updateHandler = async (
    req: Request<
        {
            code: string;
        },
        unknown,
        UpsertTripCost,
        unknown
    >,
    res: Response<TripCost>
): Promise<void> => {
    try {
        const { code } = req.params;
        const tripCost = req.body;
        backend.logger.log(`Updating trip cost: ${code}`);

        const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<TripCost>('entities', 'tripCosts');

        const prevTripCost = await collection.findOne({ code });

        if (!prevTripCost) {
            return getResponse(res, ServerResponseCode.NOT_FOUND, {
                message: 'Trip cost not found for provided code'
            });
        }

        // if forecast updated, lets calculate native forecast
        let forecastNative = prevTripCost.forecastNative;
        if (tripCost.forecast) {
            // get native value for cost
            const { forecast } = tripCost;
            const updatedForecastNative = await convertExchangeRate({
                from: forecast.currency,
                to: Currency.GBP,
                amount: forecast.amount,
                date: new Date()
            });

            if (!updatedForecastNative) {
                throw new Error('Failed while calculating forecast in native currency');
            }

            forecastNative = updatedForecastNative as NativeValue;
        }

        const update = { ...tripCost, forecastNative, updated: new Date() };
        await collection.updateOne({ code }, { $set: update });

        const updatedTripCost = { ...prevTripCost, ...update };

        const withoutId = MongodbUtils.withoutId(updatedTripCost);
        await sendTripCostsToMI(updatedTripCost);

        return getResponse(res, ServerResponseCode.OK, withoutId);
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed updating trip cost`,
            data: { ...req.body }
        });
        backend.logger.error(exception);
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};

const completeHandler = async (
    req: Request<
        {
            code: string;
        },
        unknown,
        unknown,
        unknown
    >,
    res: Response<TripCost>
): Promise<void> => {
    try {
        const { code } = req.params;
        backend.logger.log(`Completing trip cost: ${code}`);

        const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<TripCost>('entities', 'tripCosts');

        const tripCost = await collection.findOne({ code });

        if (!tripCost) {
            return getResponse(res, ServerResponseCode.NOT_FOUND, {
                message: 'Trip cost not found for provided code'
            });
        }

        if (tripCost.status === TripCostStatus.COMPLETED) {
            const withoutId = MongodbUtils.withoutId(tripCost);
            return getResponse(res, ServerResponseCode.OK, withoutId);
        }

        if (tripCost.status === TripCostStatus.CANCELLED) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST, {
                message: 'Trip cost already cancelled'
            });
        }

        const update = {
            status: TripCostStatus.COMPLETED,
            updated: new Date()
        };
        await collection.updateOne(
            { code },
            {
                $set: update
            }
        );

        const updatedTripCost = { ...tripCost, ...update };
        const withoutId = MongodbUtils.withoutId(updatedTripCost);
        await sendTripCostsToMI(updatedTripCost);

        return getResponse(res, ServerResponseCode.OK, withoutId);
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed completing trip cost`,
            data: { params: req.params }
        });
        backend.logger.error(exception);
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};

const extendHandler = async (
    req: Request<{
        code: string;
    }>,
    res: Response<TripCost>
): Promise<void> => {
    try {
        const { code } = req.params;
        backend.logger.log(`Extending trip cost: ${code}`);

        const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<TripCost>('entities', 'tripCosts');

        const prevTripCost = await collection.findOne({ code });

        if (!prevTripCost) {
            return getResponse(res, ServerResponseCode.NOT_FOUND, {
                message: 'Trip cost not found for provided code'
            });
        }

        const canExtendCost = await canExtend(prevTripCost);
        if (!canExtendCost.valid) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST, {
                message: canExtendCost.message
            });
        }

        const update = { extended: true, updated: new Date() };
        await collection.updateOne({ code }, { $set: update });

        const updatedTripCost = { ...prevTripCost, ...update };
        await sendTripCostsToMI(updatedTripCost);
        const withoutId = MongodbUtils.withoutId(updatedTripCost);

        return getResponse(res, ServerResponseCode.OK, withoutId);
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed extending trip cost`,
            data: { ...req.body }
        });
        backend.logger.error(exception);
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};

const unextendHandler = async (
    req: Request<{
        code: string;
    }>,
    res: Response<TripCost>
): Promise<void> => {
    try {
        const { code } = req.params;
        backend.logger.log(`Unextending trip cost: ${code}`);

        const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<TripCost>('entities', 'tripCosts');

        const prevTripCost = await collection.findOne({ code });

        if (!prevTripCost) {
            return getResponse(res, ServerResponseCode.NOT_FOUND, {
                message: 'Trip cost not found for provided code'
            });
        }

        const canUnextendCost = await canUnextend(prevTripCost);
        if (!canUnextendCost.valid) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST, {
                message: canUnextendCost.message
            });
        }

        const update: Partial<TripCost> = { extended: false, updated: new Date() };
        await collection.updateOne({ code }, { $set: update });

        const updatedTripCost = { ...prevTripCost, ...update };
        const withoutId = MongodbUtils.withoutId(updatedTripCost);

        return getResponse(res, ServerResponseCode.OK, withoutId);
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed unextending trip cost`,
            data: { ...req.body }
        });
        backend.logger.error(exception);
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};

const canExtendHandler = async (
    req: Request<{
        code: string;
    }>,
    res: Response<SimplifiedValidationResult>
): Promise<void> => {
    try {
        const { code } = req.params;
        backend.logger.log(`Checking if can extend trip cost: ${code}`);

        const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<TripCost>('entities', 'tripCosts');

        const prevTripCost = await collection.findOne({ code });

        if (!prevTripCost) {
            return getResponse(res, ServerResponseCode.OK, {
                valid: false,
                message: 'Trip cost not found for provided code'
            });
        }

        const canExtendCost = await canExtend(prevTripCost);

        return getResponse(res, ServerResponseCode.OK, canExtendCost);
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed checking if can extend trip cost`,
            data: { ...req.body }
        });
        backend.logger.error(exception);
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};
const canUnextendHandler = async (
    req: Request<{
        code: string;
    }>,
    res: Response<SimplifiedValidationResult>
): Promise<void> => {
    try {
        const { code } = req.params;
        backend.logger.log(`Checking if can unextend trip cost: ${code}`);

        const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<TripCost>('entities', 'tripCosts');

        const prevTripCost = await collection.findOne({ code });

        if (!prevTripCost) {
            return getResponse(res, ServerResponseCode.OK, {
                valid: false,
                message: 'Trip cost not found for provided code'
            });
        }

        const canExtendCost = await canUnextend(prevTripCost);

        return getResponse(res, ServerResponseCode.OK, canExtendCost);
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed checking if can unextend trip cost`,
            data: { ...req.body }
        });
        backend.logger.error(exception);
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};

const cancelHandler = async (
    req: Request<
        {
            code: string;
        },
        unknown,
        unknown,
        unknown
    >,
    res: Response<TripCost>
): Promise<void> => {
    try {
        const { code } = req.params;
        backend.logger.log(`Cancelling trip cost: ${code}`);

        const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<TripCost>('entities', 'tripCosts');

        const tripCost = await collection.findOne({ code });

        if (!tripCost) {
            return getResponse(res, ServerResponseCode.NOT_FOUND, {
                message: 'Trip cost not found for provided code'
            });
        }

        if (tripCost.status === TripCostStatus.CANCELLED) {
            const withoutId = MongodbUtils.withoutId(tripCost);
            return getResponse(res, ServerResponseCode.OK, withoutId);
        }

        if (tripCost.status === TripCostStatus.COMPLETED) {
            return getResponse(res, ServerResponseCode.BAD_REQUEST, {
                message: 'Trip cost already completed'
            });
        }

        const update = { status: TripCostStatus.CANCELLED, updated: new Date() };
        await collection.updateOne({ code }, { $set: update });

        const updatedTripCost = { ...tripCost, ...update };
        await sendTripCostsToMI(updatedTripCost);
        const withoutId = MongodbUtils.withoutId(updatedTripCost);

        return getResponse(res, ServerResponseCode.OK, withoutId);
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed cancelling trip cost`,
            data: { params: req.params }
        });
        backend.logger.error(exception);
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};

const queryCosts = async (req: Request<unknown, unknown, QueryTripCost, unknown>, res: Response<ExtendedPaginationQueryResult<TripCost>>): Promise<void> => {
    try {
        const { limit, skip, supplierResourceId, statuses, currencies, types, query, ...restOfQuery } = req.body;
        backend.logger.log(`Querying trip costs`);

        const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
        const collection = await provider.collection<TripCost>('entities', 'tripCosts');
        const queryObject: Filter<TripCost> = { ...restOfQuery };

        if (supplierResourceId) {
            queryObject.supplierResourceId = supplierResourceId;
        }
        if (currencies) {
            queryObject.currency = { $in: currencies };
        }
        if (statuses) {
            queryObject.status = { $in: statuses };
        }
        if (types) {
            queryObject.type = { $in: types };
        }

        if (query) {
            // if taskId-like
            const taskIdFromQuery = Number(query);
            if (!isNaN(taskIdFromQuery)) {
                queryObject.$or = [{ tripCode: { $regex: new RegExp(query, 'i') } }, { taskId: taskIdFromQuery }, { serviceRequesterRef: { $regex: new RegExp(query, 'i') } }];
            } else {
                queryObject.$or = [{ tripCode: { $regex: new RegExp(query, 'i') } }, { serviceRequesterRef: { $regex: new RegExp(query, 'i') } }];
            }
        }
        if (queryObject.benefitLimitId) {
            const benefitCollection = await provider.collection('entities', 'benefitLimits');
            const benefitCodes = await benefitCollection
                .find({ type: { $in: queryObject.benefitLimitId } })
                .map((benefit) => benefit.code)
                .toArray();

            if (benefitCodes.length) {
                queryObject.benefitLimitId = { $in: benefitCodes };
            } else {
                delete queryObject.benefitLimitId;
                return getResponse(res, ServerResponseCode.OK, {
                    results: [],
                    more: false,
                    total: 0,
                    limit,
                    skip
                });
            }
        }
        const cursor = await collection.find(queryObject);
        const countResult = await MongodbUtils.count(cursor, limit);

        if (!countResult.total) {
            return getResponse(res, ServerResponseCode.OK, {
                results: [],
                more: false,
                ...countResult
            });
        }

        const paginationResult = await MongodbUtils.paginate(cursor, { limit, skip });
        const paginationResultWithoutIds = {
            ...paginationResult,
            results: paginationResult.results.map(MongodbUtils.withoutId)
        };

        return getResponse(res, ServerResponseCode.OK, {
            ...paginationResultWithoutIds,
            ...countResult
        });
    } catch (error) {
        const exception = new Exception({
            error,
            message: `Failed querying trip costs`
        });
        backend.logger.error(exception);
        return getResponse(res, ServerResponseCode.SERVER_ERROR);
    }
};

async function getFinancialTaskEstimates(task: TaskPreview, entitlement: EntitlementData, benefitLimits: BenefitLimit[], trip: Trip): Promise<TripCostEstimateResponse> {
    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for financial task`,
        data: { taskId: task.id, benefitLimits }
    });

    const response = { estimates: [] };

    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for financial task completed`,
        data: { taskId: task.id, benefitLimits, estimates: response }
    });

    // TODO: get estimate cost from financial task - forward fee cost or claim cost
    return response;
}

async function getHotelTaskEstimates(task: HotelTaskPreview, entitlement: EntitlementData, benefitLimits: BenefitLimit[], trip: Trip): Promise<TripCostEstimateResponse> {
    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for hotel task`,
        data: { taskId: task.id, benefitLimits }
    });

    const { value: amount, currency } = task.hotel.price;

    if (!amount || !currency) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to calculate estimate for hotel task, price missing`,
            data: { taskId: task.id, benefitLimits, schedule: task.schedule, hotel: task.hotel }
        });
        return { estimates: [] };
    }

    const benefitLimit = benefitLimits.find((benefitLimit) => benefitLimit.type === BenefitType.HOTEL);

    if (!benefitLimit) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to find benefit limit for hotel task`,
            data: { taskId: task.id, benefitLimits }
        });
        throw new Error(`No benefit limit found for provided hotel task with id ${task.id}`);
    }

    const value: Value = { amount, currency };
    const estimates = await getCostDefinitionEstimates(task, entitlement, benefitLimit, TripCostType.SERVICE_COST, trip, { value });

    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for hotel task completed`,
        data: { taskId: task.id, benefitLimits, estimates }
    });
    return estimates;
}

async function getStorageTaskEstimates(task: StorageTaskPreview, entitlement: EntitlementData, benefitLimits: BenefitLimit[], trip: Trip): Promise<TripCostEstimateResponse> {
    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for storage task`,
        data: { taskId: task.id, benefitLimits }
    });

    const { value: amount, currency } = task.eurohelp.price;

    if (!amount || !currency) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to calculate estimate for storage task, price missing`,
            data: { taskId: task.id, benefitLimits, schedule: task.schedule, eurohelp: task.eurohelp }
        });
        return { estimates: [] };
    }

    const benefitLimit = benefitLimits.find((benefitLimit) => benefitLimit.type === BenefitType.STORAGE);

    if (!benefitLimit) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to find benefit limit for storage task`,
            data: { taskId: task.id, benefitLimits }
        });
        throw new Error(`No benefit limit found for provided storage task with id ${task.id}`);
    }

    const value: Value = { amount, currency };
    const estimates = await getCostDefinitionEstimates(task, entitlement, benefitLimit, TripCostType.SERVICE_COST, trip, { value });

    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for storage task completed`,
        data: { taskId: task.id, benefitLimits, estimates }
    });
    return estimates;
}

async function getGarageRepairTaskEstimates(task: StorageTaskPreview, entitlement: EntitlementData, benefitLimits: BenefitLimit[], trip: Trip): Promise<TripCostEstimateResponse> {
    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for garage repair task`,
        data: { taskId: task.id, benefitLimits }
    });

    const { value: amount, currency } = task.eurohelp.price;

    if (!amount || !currency) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to calculate estimate for garage repair task, price missing`,
            data: { taskId: task.id, benefitLimits, schedule: task.schedule, eurohelp: task.eurohelp }
        });
        return { estimates: [] };
    }

    const benefitLimit = benefitLimits.find((benefitLimit) => benefitLimit.type === BenefitType.GARAGE_REPAIR);

    if (!benefitLimit) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to find benefit limit for garage repair task`,
            data: { taskId: task.id, benefitLimits }
        });
        throw new Error(`No benefit limit found for provided garage repair task with id ${task.id}`);
    }

    const value: Value = { amount, currency };
    const estimates = await getCostDefinitionEstimates(task, entitlement, benefitLimit, TripCostType.SERVICE_COST, trip, { value });

    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for garage repair task completed`,
        data: { taskId: task.id, benefitLimits, estimates }
    });
    return estimates;
}

async function getTransportTaskEstimates(task: TransportTaskPreview, entitlement: EntitlementData, benefitLimits: BenefitLimit[], trip: Trip): Promise<TripCostEstimateResponse> {
    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for transport task`,
        data: { taskId: task.id, benefitLimits }
    });

    const { value: amount, currency } = task.transport.price;

    if (!amount || !currency) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to calculate estimate for transport task, price missing`,
            data: { taskId: task.id, benefitLimits, schedule: task.schedule, transport: task.transport }
        });
        return { estimates: [] };
    }

    const benefitLimit = benefitLimits.find((benefitLimit) => benefitLimit.type === BenefitType.PUBLIC_TRANSPORT);

    if (!benefitLimit) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to find benefit limit for transport task`,
            data: { taskId: task.id, benefitLimits }
        });
        throw new Error(`No benefit limit found for provided transport task with id ${task.id}`);
    }

    const value: Value = { amount, currency };
    const estimates = await getCostDefinitionEstimates(task, entitlement, benefitLimit, TripCostType.SERVICE_COST, trip, { value });

    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for transport task completed`,
        data: { taskId: task.id, benefitLimits, estimates }
    });
    return estimates;
}

async function getHireCarTaskEstimates(task: CarHireTaskPreview, entitlement: EntitlementData, benefitLimits: BenefitLimit[], trip: Trip): Promise<TripCostEstimateResponse> {
    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for car hire task`,
        data: { taskId: task.id, benefitLimits }
    });

    const days = Utils.dayDiff(new Date(task.schedule.arrive), new Date(task.schedule.complete));

    if (!days || isNaN(days)) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to calculate estimate for car hire task, days can't be calculated from task schedule`,
            data: { taskId: task.id, benefitLimits, schedule: task.schedule }
        });
        return { estimates: [] };
    }

    const benefitLimit = benefitLimits.find((benefitLimit) => benefitLimit.type === BenefitType.CAR_HIRE);

    if (!benefitLimit) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to find benefit limit for hire car task`,
            data: { taskId: task.id, benefitLimits }
        });
        throw new Error(`No benefit limit found for provided hire car task with id ${task.id}`);
    }

    const estimates = await getCostDefinitionEstimates(task, entitlement, benefitLimit, TripCostType.SERVICE_COST, trip, { days });

    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for car hire task completed`,
        data: { taskId: task.id, benefitLimits, estimates }
    });
    return estimates;
}

async function getBreakdownTaskEstimates(task: BreakdownTaskPreview, entitlement: EntitlementData, benefitLimits: BenefitLimit[], trip: Trip): Promise<TripCostEstimateResponse> {
    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for breakdown task`,
        data: { taskId: task.id, benefitLimits }
    });

    const benefitLimit = benefitLimits.find((benefitLimit) => benefitLimit.type === BenefitType.RSS);

    if (!benefitLimit) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to find benefit limit for breakdown task`,
            data: { taskId: task.id, benefitLimits }
        });
        throw new Error(`No benefit limit found for provided breakdown task with id ${task.id}`);
    }

    // if recovery distance on the task
    const miles = task.recovery?.distance || 0;
    const estimates = await getCostDefinitionEstimates(task, entitlement, benefitLimit, TripCostType.SERVICE_COST, trip, { miles });

    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for breakdown task completed`,
        data: { taskId: task.id, benefitLimits, estimates }
    });
    return estimates;
}

async function getRecoveryTaskEstimates(task: RecoveryTaskPreview, entitlement: EntitlementData, benefitLimits: BenefitLimit[], trip: Trip): Promise<TripCostEstimateResponse> {
    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for recover task`,
        data: { taskId: task.id, benefitLimits }
    });

    const benefitLimit = benefitLimits.find((benefitLimit) => benefitLimit.type === BenefitType.RECOVERY);

    if (!benefitLimit) {
        backend.logger.warn({
            sourceName: backend.name,
            message: `Unable to find benefit limit for recovery task`,
            data: { taskId: task.id, benefitLimits }
        });
        throw new Error(`No benefit limit found for provided recovery task with id ${task.id}`);
    }

    // if recovery distance on the task
    const miles = task.eurohelp?.recovery?.distance || 0;
    const estimates = await getCostDefinitionEstimates(task, entitlement, benefitLimit, TripCostType.SERVICE_COST, trip, { miles });

    backend.logger.info({
        sourceName: backend.name,
        message: `Generating estimates for recover task completed`,
        data: { taskId: task.id, benefitLimits, estimates }
    });
    return estimates;
}

async function getCostDefinitions(task: TaskPreview, entitlement: EntitlementData, trip: Trip): Promise<CostDefinition[]> {
    const createReason = task.createReason?.id;

    if (typeof createReason === 'undefined') {
        throw new Error(`No create reason found for task with id ${task.id}`);
    }

    const customerGroup = entitlement?.policy?.customerGroup?.code;
    const { contractKey } = trip;

    const entitlementBenefit = await backend.entitlementBenefitClient.getEntitlementBenefitByContract(contractKey, customerGroup as string);
    if (!entitlementBenefit) {
        throw new Error(`Unable to find product benefit for contract ${trip.contractKey}`);
    }
    const productCode = entitlementBenefit.code;

    // To find cost definition we will search using filters with decreasing specificity.
    const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
    const costDefinitionCollection = await provider.collection<CostDefinition>('system-config', 'costDefinitions');

    // Queries we will use in exact order
    const queries: Filter<CostDefinition>[] = [
        // First lets search using filter with highest specificity - match
        // for specific customerGroupCode, createReason and product code
        {
            customerGroupCode: customerGroup,
            createReasons: createReason,
            productCodes: productCode
        },
        // Then lets search using filter with lower specificity - match
        // for any defined customerGroupCode and specific createReason and product code
        {
            customerGroupCode: '*',
            createReasons: createReason,
            productCodes: productCode
        },
        // Then lets search using filter with even lower specificity - match
        // for specific customerGroupCode, createReason and no product code
        {
            customerGroupCode: customerGroup,
            createReasons: createReason,
            // required to omit results specific for selected products
            productCodes: { $size: 0 }
        },
        // Finally lets search using filter with lowest specificity - match
        // for any customerGroupCode and specific createReason, while no product code
        {
            customerGroupCode: '*',
            createReasons: createReason,
            // required to omit results specific for selected products
            productCodes: { $size: 0 }
        }
    ];

    let costDefinitions: CostDefinition[] = [];
    let i = 0;
    for (const query of queries) {
        backend.logger.log({
            message: `Searching cost def using filter no ${i + 1}`,
            data: { createReason, taskId: task.id, customerGroup, query }
        });

        costDefinitions = await costDefinitionCollection.find(query).toArray();

        backend.logger.log({
            message: 'Searching cost def using filter no ${i+1} completed',
            data: { createReason, taskId: task.id, customerGroup, query }
        });

        if (costDefinitions.length) {
            backend.logger.log({
                message: 'Cost definitions found',
                data: { createReason, taskId: task.id, customerGroup }
            });
            break;
        }

        i++;
    }

    if (!costDefinitions.length) {
        backend.logger.warn({
            message: 'Unable to find any cost definition',
            data: { createReason, taskId: task.id, customerGroup }
        });
    }
    return costDefinitions;
}

async function getCostDefinitionEstimates(
    task: TaskPreview,
    entitlement: EntitlementData,
    benefitLimit: BenefitLimit,
    type: TripCostType,
    trip: Trip,
    auxData?: { days?: number; miles?: number; value?: Value }
): Promise<TripCostEstimateResponse> {
    try {
        if (!benefitLimit) {
            throw new Error(`No benefit limit found for provided task with id ${task.id}`);
        }

        let costDefinitions = await getCostDefinitions(task, entitlement, trip);
        // filter cost definitions by benefit type
        costDefinitions = costDefinitions.filter((costDefinition) => costDefinition.benefitType === benefitLimit.type);
        const response: TripCostEstimateResponse = { estimates: [] };

        // for each cost definition produce an estimate,
        for (const costDefinition of costDefinitions) {
            const { requireConfirmation } = costDefinition;

            const forecast = await calculateForecast(costDefinition, auxData);
            const estimate = await getEstimate({ task, type, benefitLimit, forecast });

            response.estimates.push({ estimate, requireConfirmation });
        }

        return response;
    } catch (error) {
        throw new Error(`Failure while estimating task cost: ${(error as Error).message}`);
    }
}

async function getUnsupportedTaskEstimates(task: TaskPreview, entitlement: EntitlementData, benefitLimits: BenefitLimit[], trip: Trip): Promise<TripCostEstimateResponse> {
    // no need to create estimates for non-supported
    return { estimates: [] };
}

async function getSupplierResourceId(task: TaskPreview): Promise<number> {
    try {
        const taskId = task.id;
        if (taskId === null) {
            return -1;
        }
        const supplierClient = new SupplierClient({ httpClient: backend.httpClient, connector: backend.connector });
        const supplierData = await supplierClient.billingSupplier(taskId);

        if (!supplierData || (supplierData && !supplierData.supplierResourceId)) {
            return -1;
        }

        return supplierData.supplierResourceId;
    } catch (error) {
        throw new Error(`Failed to retrieve supplier resource ID: ${error.message}`);
    }
}

async function getEstimate(config: { task: TaskPreview; benefitLimit: BenefitLimit; forecast: Value; type: TripCostType }): Promise<TripCostEstimate> {
    try {
        const { task, benefitLimit, forecast, type } = config;
        const createReason = task.createReason?.id;

        if (typeof createReason === 'undefined') {
            throw new Error(`No create reason found for task with id ${task.id}`);
        }

        const { customerRequestId, id: taskId } = task;

        const supplierResourceId = await getSupplierResourceId(task);

        if (!supplierResourceId) {
            throw new Error(`Supplier not found for the task ${taskId}`);
        }

        if (typeof taskId === 'undefined' || taskId === null) {
            throw new Error(`Task id is undefined`);
        }

        switch (type) {
            case TripCostType.SERVICE_COST:
                return {
                    customerRequestId,
                    taskId,
                    name: `Cost for ${CreateReasonLabels[createReason]} for ${task.id}`,
                    benefitLimitId: benefitLimit?.code,
                    serviceRequesterRef: taskId.toString(),
                    currency: forecast.currency,
                    supplierResourceId,
                    type,
                    forecast
                };
            case TripCostType.FEE:
                return {
                    customerRequestId,
                    taskId,
                    name: `Cost for ${CreateReasonLabels[createReason]} for ${task.id}`,
                    benefitLimitId: benefitLimit?.code,
                    serviceRequesterRef: taskId.toString(),
                    currency: forecast.currency,
                    supplierResourceId,
                    type,
                    forecast
                };
            case TripCostType.CLAIM:
                return {
                    customerRequestId,
                    taskId,
                    name: `Cost for ${CreateReasonLabels[createReason]} for ${task.id}`,
                    benefitLimitId: benefitLimit?.code,
                    serviceRequesterRef: taskId.toString(),
                    currency: forecast.currency,
                    supplierResourceId,
                    type,
                    forecast
                };
        }
    } catch (error) {
        throw new Error(`Error while generating final estimate: ${(error as Error).message}`);
    }
}

export async function calculateForecast(
    costDefinition: { charge: ChargeDefinition },
    auxData?: {
        days?: number;
        miles?: number;
        value?: Value;
    }
): Promise<Value> {
    const { charge } = costDefinition;
    const date = new Date();
    let amount: number | undefined;
    let currency: Currency | undefined;
    let chargeTargetValue: Value | undefined;
    let chargeableMiles: number;
    let chargeableDays: number;

    switch (charge.type) {
        case ChargeType.PER_MILE:
            if (auxData?.miles === undefined) {
                throw new Error('Miles data is required for PER_MILE charge type');
            }

            // let's respect charge config for non-chargeable days
            chargeableMiles = (auxData.miles ?? 0) - (charge.noChargeThreshold ?? 0);
            amount = chargeableMiles <= 0 ? 0 : charge.value.amount * chargeableMiles;
            currency = charge.value.currency;
            break;
        case ChargeType.PER_DAY:
            if (auxData?.days === undefined) {
                throw new Error('Days data is required for PER_DAY charge type');
            }

            // let's respect charge config for non-chargeable days
            chargeableDays = (auxData.days ?? 0) - (charge.noChargeThreshold ?? 0);
            amount = chargeableDays <= 0 ? 0 : charge.value.amount * chargeableDays;
            currency = charge.value.currency;
            break;
        case ChargeType.CUSTOM:
            if (auxData?.value === undefined) {
                throw new Error('Value data is required for CUSTOM charge type');
            }

            // always respect the currency provided to us as a custom cost
            chargeTargetValue = await convertExchangeRate({
                from: charge.value.currency,
                to: auxData.value.currency,
                date,
                amount: charge.value.amount
            });

            if (!chargeTargetValue) {
                throw new Error(`Unable to convert charge value from ${charge.value.currency} to the ${auxData.value.currency} currency of custom cost`);
            }

            // Value from cost definition used as an addon charge to custom input value
            amount = auxData.value.amount + chargeTargetValue.amount;
            currency = auxData.value.currency;
            break;
        case ChargeType.FIXED:
            amount = charge.value.amount;
            currency = charge.value.currency;
            break;
        default:
            throw new Error(`Unsupported charge type: ${JSON.stringify(charge)}`);
    }

    return { amount, currency };
}

export async function calculateActual(tripCost: TripCost): Promise<Value> {
    const provider = backend.dataStore.getProvider(DataStoreProviderType.MONGODB);
    const lineItemCollection = await provider.collection<InvoiceLineItem>('entities', 'invoiceLineItems');
    const lineItem = await lineItemCollection.findOne({ costCode: tripCost.code });

    if (!lineItem) {
        throw new Error('Line item cant be found');
    }

    // if credit note linked to the line item, lets include it in the calculations
    const date = new Date();
    let creditNoteGross: Value = { amount: 0, currency: lineItem.currency };
    if (lineItem.creditNoteCode) {
        const creditNoteCollection = await provider.collection<CreditNote>('entities', 'creditNotes');
        const creditNote = await creditNoteCollection.findOne({ code: lineItem.creditNoteCode });

        if (!creditNote) {
            throw new Error('Credit note for the line item cant be found');
        }

        if (creditNote.status !== CreditNoteStatus.AUTHORISED) {
            throw new Error('Credit note for the line item not authorised');
        }

        // match currencies
        const creditNoteGrossExchanged = await convertExchangeRate({
            from: creditNote.gross.currency,
            to: lineItem.gross.currency,
            date,
            amount: creditNote.gross.amount
        });

        if (!creditNoteGrossExchanged) {
            throw new Error('Unable to convert credit note currency to forecast currency');
        }

        creditNoteGross = creditNoteGrossExchanged;
    }

    // calculate actual based on
    return {
        amount: lineItem.gross.amount - creditNoteGross.amount,
        currency: tripCost.forecast.currency
    };
}

export async function calculateOverspend(tripCost: TripCost, actual: Value): Promise<Value | undefined> {
    // match currencies
    const actualExchanged = await convertExchangeRate({
        from: actual.currency,
        to: tripCost.forecast.currency,
        date: new Date(),
        amount: actual.amount
    });

    if (!actualExchanged) {
        throw new Error('Unable to convert actual value currency to forecast currency');
    }

    // if forecast same or more than actual, bailout
    if (tripCost.forecast.amount >= actualExchanged.amount) {
        return;
    }

    // else calculate overspend
    return {
        amount: actualExchanged.amount - tripCost.forecast.amount,
        currency: tripCost.forecast.currency
    };
}

export async function sendTripCostsToMI(tripCost: TripCost) {
    switch (tripCost.type) {
        case TripCostType.SERVICE_COST:
            await backend.miStreamServiceCostSender.send(MiEvents.SERVICE_COSTS, tripCost);
            break;
        case TripCostType.FEE:
            await backend.miStreamFeeCostSender.send(MiEvents.FEE_COSTS, tripCost);
            break;
        case TripCostType.CLAIM:
            await backend.miStreamClaimCostSender.send(MiEvents.CLAIM_COSTS, tripCost);
            break;
        default:
            break;
    }
}
