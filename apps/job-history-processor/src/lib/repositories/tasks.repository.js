'use strict';

let _ = require('lodash'),
    async = require('async'),
    Q = require('q'),
    Task = require('@aa/malstrom-models/lib/task.model'),
    logger = require('winston'),
    restfulRequest = require('@aa/endpoints').restfulRequest;

function TasksRepository(config) {
    let repo = this,
        _config = config;

    logger.info('TasksRepository init', _config);
    _.extend(repo, {
        find: function (taskId, token) {
            return restfulRequest({
                method: 'GET',
                rejectUnauthorized: false, // this is because aa's certificate are not authorized ...
                baseUrl: _config.endPointUrl,
                uri: '/task/' + taskId,
                timeout: _config.timeout,
                headers: {
                    'content-type': 'application/json',
                    'security-token': token
                }
            }).then(function (resp) {
                return new Task(resp.data);
            });
        },
        findAllTasksSeries: function findAllTasksSeries(serviceHistory, activeCR, token) {
            let deferred = Q.defer(),
                todate = Date.now(), // today in milliseconds
                sevenDays = 604800000,
                tasksLimitDuration = parseInt(process.env.TASKS_LIMIT_DURATION, 10) || 5 * 365 * 24 * 60 * 60 * 1000; // in milliseconds
            logger.info('searching for ', serviceHistory.customerRequestHistory().length);
            async.eachLimit(
                serviceHistory.customerRequestHistory(),
                2,
                function (cr, done) {
                    logger.info('searching ', cr.id());
                    if (todate - cr.creationTime().getTime() <= tasksLimitDuration) {
                        restfulRequest({
                            rejectUnauthorized: false,
                            method: 'GET',
                            baseUrl: _config.endPointUrl,
                            uri: '/tasks/' + cr.id(),
                            timeout: _config.timeout,
                            headers: {
                                'content-type': 'application/json',
                                'security-token': token
                            }
                        })
                            .then(function (caseResp) {
                                logger.info('done search cr %d tasks %d', cr.id(), caseResp.data.length);
                                _.forEach(caseResp.data, function (task) {
                                    cr.tasks().push(new Task(task));
                                });
                                done(); //done(null, true);
                            })
                            .catch(function (err) {
                                done(err, false); // failed to get this one so we abort ..
                            });
                    } else {
                        done(); // skip as we don't have any data
                    }
                },
                function _done(err, result) {
                    logger.info('full search done --', err, result);
                    if (err) {
                        deferred.reject({});
                    } else {
                        serviceHistory.customerRequestHistory().push(activeCR); // add the active cr to process...
                        deferred.resolve(serviceHistory);
                    }
                }
            );

            return deferred.promise;
        },
        findAllTasks: function findAllTasks(serviceHistory, token) {
            let promises = [];
            _.forEach(serviceHistory.customerRequestHistory(), function (cr) {
                logger.info('searching for cr : ', cr.id());
                promises.push(
                    restfulRequest({
                        rejectUnauthorized: false,
                        method: 'GET',
                        baseUrl: _config.endPointUrl,
                        uri: '/tasks/' + cr.id(),
                        timeout: _config.timeout,
                        headers: {
                            'content-type': 'application/json',
                            'security-token': token
                        }
                    })
                );
            });
            logger.info('total no of cr to search ', promises.length);
            return Q.allSettled(promises).then(function (results) {
                _.forEach(results, function (item, idx) {
                    if (item.state === 'fulfilled') {
                        _.forEach(item.value.data, function (task) {
                            logger.info('processing task ', task.id);
                            serviceHistory.customerRequestHistory()[idx].tasks().push(new Task(task));
                        });
                    }
                });
                return serviceHistory;
            });
        }
    });
}

module.exports = TasksRepository;
