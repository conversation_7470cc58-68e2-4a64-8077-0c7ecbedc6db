const oracledb = require('oracledb');
const { timingSafeEqual } = require('crypto');
const Q = require('q');

const ConnectionPoolService = require('@aa/oracle-utilities').ConnectionPoolService;
let _db = null;
module.exports = {
    /**
     * initialise connection to oracle you need to use connect method to obtain a connection to transact against
     * @param {object} params details of the connectivity
     * @param {string} params.user username for connecting to db
     * @param {string} params.password of the db connection
     * @param {string[]} params.connectStrings multiple db connection string
     * @param {string} params.appName name of the app/ web app
     * @returns {Promise} resolves when connect to data is made
     */
    init: (params) => {
        return ConnectionPoolService.getInstance(params).then((db) => {
            _db = db;
            return db;
        });
    }
};
