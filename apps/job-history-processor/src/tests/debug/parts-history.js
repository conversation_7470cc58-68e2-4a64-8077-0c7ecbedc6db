require('dotenv').config({
    path: '/Users/<USER>/projects/work/aa-job-history-processor-service/.env'
});

const ConnectionPoolService = require('@aa/oracle-utilities').ConnectionPoolService;
const JobHistoryProcessService = require('../../lib/services/job-history-processor.service');
const JobHistoryProcessorService = require('../../lib/services/job-history-processor.service');
const appName = 'job-history-processor';
const centParams = {
    user: process.env.centUser,
    password: process.env.centPassword,
    connectString: process.env.centConnectStrings,
    appName
};

const cshParams = {
    user: process.env.cshUser,
    password: process.env.cshPassowrd,
    // for multiple oracle dbs set oracle urls seperate with commas
    connectString: process.env.cshConnectStrings,
    appName
};

const run = async () => {
    console.log(cshParams);
    const db = await ConnectionPoolService.getInstance(cshParams);
    const jobHistorySvc = new JobHistoryProcessorService(
        {
            taskSvcUrl: {
                endPointUrl: process.env.taskServiceUrl,
                timeout: 30000
            }
        },
        db
    );

    const history = await jobHistorySvc.find(51076196, true);
    //    const history = await jobHistorySvc.find(51076208);51076196; 51063394
    //    console.log(JSON.stringify(history));
};

run()
    .then(() => {
        console.log('all ok');
        process.exit(0);
    })
    .catch((err) => {
        console.log(err);
        process.exit(1);
    });
