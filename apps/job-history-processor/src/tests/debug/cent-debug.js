require('dotenv').config();

const ConnectionPoolService = require('@aa/oracle-utilities').ConnectionPoolService;
const VehiclePartsCatalogue = require('../../lib/repositories/vehicle-parts-catalogue.respository');
const appName = 'job-history-processor';
const params = {
    user: process.env.centUser,
    password: process.env.centPassword,
    connectString: process.env.centConnectStrings,
    appName
};

const run = async () => {
    console.log(params);
    const db = await ConnectionPoolService.getInstance(params);
    const vehiclePartsCatalogue = new VehiclePartsCatalogue(db);

    return vehiclePartsCatalogue.find(['YBX5075', 'YBX5096']).then((codes) => {
        console.log(codes);
    });
};

run()
    .then(() => {
        console.log('all ok');
        process.exit(0);
    })
    .catch((err) => {
        console.log(err);
        process.exit(1);
    });
