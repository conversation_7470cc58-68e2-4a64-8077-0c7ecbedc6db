require('dotenv').config();

const ConnectionPoolService = require('@aa/oracle-utilities').ConnectionPoolService;
const LineCodeCleansingSvc = require('../../lib/services/line-code-cleansing.service');
const { jobHistory } = require('../spec/mockData');
const appName = 'job-history-processor';
const params = {
    user: process.env.centUser,
    password: process.env.centPassword,
    connectString: process.env.centConnectStrings,
    appName
};

const run = async () => {
    console.log(params);
    const db = await ConnectionPoolService.getInstance(params);
    const lineCodeSvc = new LineCodeCleansingSvc(db);

    return lineCodeSvc.process(jobHistory()).then((codes) => {
        console.log(JSON.stringify(codes));
    });
};

run()
    .then(() => {
        console.log('all ok');
        process.exit(0);
    })
    .catch((err) => {
        console.log(err);
        process.exit(1);
    });
