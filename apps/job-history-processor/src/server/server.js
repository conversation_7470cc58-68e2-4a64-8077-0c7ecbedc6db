'use strict';

let logger = require('winston'),
    nconf = require('nconf'),
    config = require('./config'),
    jobHistoryProcessorSvc = require('../lib');

// setup configuration
nconf.argv().env();
nconf.defaults(config);

logger.handleExceptions(
    new logger.transports.File({
        filename: 'exceptions.log'
    })
);

logger.add(logger.transports.File, {
    filename: 'job-history-processor.service.log'
});

logger.cli();

const structuredConfig = {
    cshDb: {
        user: nconf.get('cshUser'),
        password: nconf.get('cshPassword'),
        connectStrings: nconf.get('cshConnectStrings').split(','),
        appName: 'job-history-processor'
    },
    cent: {
        user: nconf.get('centUser'),
        password: nconf.get('centPassowrd'),
        connectStrings: nconf.get('centConnectStrings')
    },
    taskSvcUrl: {
        endPointUrl: nconf.get('taskServiceUrl'),
        timeout: 30000
    },
    svcBus: {
        partsWarrantySvcBusConnectionString: nconf.get('partsWarrantySvcBusConnectionString'),
        partsWarrantyQueueName: nconf.get('partsWarrantyQueueName')
    },
    azStorage: {
        aaAzureStorage: nconf.get('partsWarrantyaaAzureStorage'), // storage name
        aaAzureStorageAccessKey: nconf.get('partsWarrantyaaAzureStorageAccessKey'),
        aaAzureBlobContainer: nconf.get('partsWarrantyaaAzureBlobContainer')
    }
};

jobHistoryProcessorSvc
    .init(structuredConfig)
    .then(function () {
        logger.info(`services.init:: success ::  Started listening on queue : ${structuredConfig.svcBus.partsWarrantyQueueName}`);
    })
    .catch(function onCshConnectError(error) {
        logger.error('services.init:: fail to connect to db', error);
        process.exit(1); // terminate ..
    });
