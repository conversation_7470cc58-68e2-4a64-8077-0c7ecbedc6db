'use strict';

var path = require('path');

// Export the config object based on the NODE_ENV
// ==============================================
module.exports = {
    env: process.env.NODE_ENV || 'development',

    // Root path of server
    root: path.normalize(__dirname + '/../..'),

    // Server port
    //    port: process.env.COMMON_FAULTS_MICRO_SERVICE_PORT || 7107,
    ip: '0.0.0.0',

    port: process.env.aahPodHttpPort || process.env.commonFaultServicePort || 7805,
    apiEndPoint: process.env.aahPodEndpoint || process.env.commonFaultApiEndPoint || '/api/common-faults-service',

    //aahelpProxyUrl : process.env.aahelpProxyUrl || 'http://localhost:7530/api/prime-proxy',
    aahelpProxyUrl: process.env.aahelpProxyUrl || 'http://rh0113p:7815/api/prime-proxy'
};
