'use strict';

const Q = require('q');

const aaOracle = require('@aa/oracle-utilities'),
    oracledb = require('oracledb');

/* for another day ...
function cshConnectDetails(config) {
	const {user, password, connectStrings} = config;
	const oraHostsRegEx = /([a-zA-Z0-9]+):(\d+)\/([a-zA-Z0-9._]+),?([a-zA-Z0-9]+)?:?(\d+)?\/?([a-zA-Z0-9._]+)?/gm;

	const addressList = oraHostsRegEx.exec(connectStrings);
	const connectString = addressList[4]!==undefined ?
			`(DESCRIPTION=(FAILOVER=on)(ADDRESS_LIST=(ADDRESS=(protocol=tcp)(host=${addressList[1]})(port=${addressList[2]}))(ADDRESS=(protocol=tcp)(host=${addressList[1]})(port=${parseInt(addressList[2])})))(CONNECT_DATA=(SERVICE_NAME=${addressList[3]})))`:
			`(DESCRIPTION=(FAILOVER=on)(ADDRESS_LIST=(ADDRESS=(protocol=tcp)(host=${addressList[1]})(port=${addressList[2]})))(CONNECT_DATA=(SERVICE_NAME=${addressList[3]})))`;
	return {
			user ,
			password ,
			connectString
	};
}
*/

const user = process.env.cshUser || 'vanda',
    password = process.env.cshPassword || 'va.77.av',
    connectStrings = process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : ['sun54:1521/VACSH'], // repm no longer exists ...
    appName = 'bolt-auth';

oracledb.fetchAsString = [oracledb.CLOB];

module.exports = {
    init: () => {
        console.log({
            user,
            password,
            connectStrings
        });
        return aaOracle.init({
            user,
            password,
            connectStrings,
            appName
        });
    },
    connect: () => aaOracle.connect(),
    release: (db) => aaOracle.release(db),
    select: (dbConn, sql, bindvars) => {
        const defer = Q.defer();

        dbConn.execute(
            sql,
            bindvars || [],
            {
                outFormat: oracledb.OBJECT
            },
            {
                resultSet: true
            },
            function (err, result) {
                let resultStream = null,
                    results = [];
                if (err) {
                    aaOracle.release(dbConn);
                    defer.reject(err.message);

                    return;
                }
                resultStream = result.resultSet.toQueryStream();
                resultStream.on('error', function (error) {
                    aaOracle.release(dbConn);
                    defer.reject(error);
                });
                resultStream.on('data', function (data) {
                    results.push(data);
                });
                resultStream.on('end', function () {
                    defer.resolve(results);
                });
            }
        );
        return defer.promise;
    },
    cursorRead: (dbConn, sql, bindvars) => {
        const defer = Q.defer();

        dbConn.execute(
            sql,
            bindvars,
            {
                outFormat: oracledb.OBJECT
            },
            function (err, result) {
                let resultStream = null,
                    results = [];

                if (err) {
                    defer.reject(err.message);
                    return;
                }
                resultStream = result.outBinds.cursor.toQueryStream();

                resultStream.on('error', function (error) {
                    defer.reject(error);
                });
                resultStream.on('data', function (data) {
                    results.push(data);
                });
                resultStream.on('end', function () {
                    resultStream.destroy();
                });

                resultStream.on('close', function () {
                    defer.resolve(results);
                });
            }
        );
        return defer.promise;
    }
};
