import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { QueueExplorer } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';

export class App extends Microservice {
    public name = 'Queue Status';
    public application = BackendApplication.QUEUE_STATUS;
    private queueExplorer: QueueExplorer<string>;

    constructor(environment: BackendEnvironment) {
        super({ environment, appName: 'queue-status' });

        this.queueExplorer = new QueueExplorer<string>({
            dataStore: this.dataStore,
            logger: this.logger
        });

        this.server.get('/status/read/:id', this.statusRead);
    }

    protected statusRead = async (req: Request, res: Response) => {
        try {
            const { id } = req.params;
            this.logger.log(`Task status for id ${id}`);
            const queueEntry = await this.queueExplorer.status(id);
            if (queueEntry) {
                return getResponse(res, ServerResponseCode.OK, queueEntry);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
}
