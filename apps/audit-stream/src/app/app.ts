import { AuditClient } from '@aa/audit-client';
import { Email, EventType, Namespace } from '@aa/data-models/common';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubCheckpoint, EventHubReceiver, EventHubReceiverConfig, QueueEventHandler } from '@aa/queue';
import { BackendEnvironment } from '@aa/utils';

export class App extends Microservice {
    public name = 'Audit Stream';
    public application = BackendApplication.AUDIT_STREAM;
    protected auxStreamReceiver: EventHubReceiver<EventType.INBOUND_EMAIL | EventType.OUTBOUND_EMAIL, Email, void>;
    protected auditClient: AuditClient;

    constructor(environment: BackendEnvironment) {
        super({ environment, appName: 'audit-stream' });

        this.auditClient = new AuditClient({
            application: BackendApplication.AUDIT_STREAM,
            connector: this.connector,
            operatorId: -1
        });

        const checkpoint = new EventHubCheckpoint({
            accountUrl: environment.queue.storageAccountUrl || '',
            accountName: environment.queue.storageAccountName || '',
            accountKey: environment.queue.storageAccountKey || '',
            containerName: 'audit-stream',
            logger: this.logger
        });

        const ehReceiverBaseConfig: Omit<EventHubReceiverConfig, 'eventHubName' | 'consumerGroup' | 'checkpoint'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            maxBatchSize: 1, // we want to process even 1
            maxDelayPerBatch: 2 // 2s
        };

        this.auxStreamReceiver = new EventHubReceiver({
            ...ehReceiverBaseConfig,
            checkpoint,
            eventHubName: 'aux-stream',
            consumerGroup: 'audit-stream'
        });

        this.auxStreamReceiver.on(EventType.INBOUND_EMAIL, this.processEmail);
        this.auxStreamReceiver.on(EventType.OUTBOUND_EMAIL, this.processEmail);
    }

    protected processEmail: QueueEventHandler<EventType.INBOUND_EMAIL | EventType.OUTBOUND_EMAIL, Email, void> = async (context) => {
        const emailRegex = /^[a-zA-Z]+(?:_[a-zA-Z]*)*_EMAIL$/;
        // const { type, namespace, from  } = context.entry.data;
        const {
            entry: { data: email }
        } = context;

        if (emailRegex.test(email.type) && email.namespace === Namespace.EUOPS) {
            const trace = this.auditClient.getTrace(Namespace.EUOPS, email.from + email.subject);

            await this.auditClient.reportAction(trace, {
                message: `${this.name}: Processing email audit`,
                data: { ...email }
            });
            this.logger.info({
                sourceName: this.name,
                message: `${this.name}: Processed email with type ${email.type} and namespace ${email.namespace}`
            });
        }
    };
}
