const Q = require('q');

const aaOracle = require('@aa/oracle-utilities');
const oracledb = require('oracledb');

const user = process.env.cshUser || 'vanda',
    password = process.env.cshPassword || 'va.77.av',
    connectStrings = process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : [],
    appName = 'fleet-trakm8';

oracledb.fetchAsString = [oracledb.CLOB];

const svc = {};

svc.init = init.bind(svc);
svc.connect = connect.bind(svc);
svc.release = release.bind(svc);
svc.selectRead = selectRead.bind(svc);
svc.cursorRead = cursorRead.bind(svc);

module.exports = svc;

function init() {
    return aaOracle.init({
        user,
        password,
        connectStrings,
        appName
    });
}

function connect() {
    return aaOracle.connect();
}

function release(db) {
    return aaOracle.release(db);
}

function selectRead(dbConn, sql, bindvars) {
    const defer = Q.defer();

    dbConn.execute(
        sql,
        bindvars,
        {
            outFormat: oracledb.OBJECT
        },
        function (err, result) {
            let resultStream = null,
                results = [];
            if (err) {
                aaOracle.release(dbConn);
                defer.reject(err.message);

                return;
            }
            resultStream = result.resultSet.toQueryStream();
            resultStream.on('error', function (error) {
                aaOracle.release(dbConn);
                defer.reject(error);
            });
            resultStream.on('data', function (data) {
                results.push(data);
            });
            resultStream.on('end', function () {
                defer.resolve(results);
            });
        }
    );
    return defer.promise;
}

function cursorRead(dbConn, sql, bindvars) {
    const defer = Q.defer();

    dbConn.execute(
        sql,
        bindvars,
        {
            outFormat: oracledb.OBJECT
        },
        function (err, result) {
            let resultStream = null,
                results = [];

            if (err) {
                aaOracle.release(dbConn);
                defer.reject(err.message);

                return;
            }
            resultStream = result.outBinds.cursor.toQueryStream();
            resultStream.on('error', function (error) {
                aaOracle.release(dbConn);
                defer.reject(error);
            });
            resultStream.on('data', function (data) {
                results.push(data);
            });
            resultStream.on('end', function () {
                defer.resolve(results);
            });
        }
    );
    return defer.promise;
}
