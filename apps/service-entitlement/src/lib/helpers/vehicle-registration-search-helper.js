'use strict';

var VEHICLE_REGISTRATION_NUMBER_REG_EXP = /(([A-Z]{2}[0-9]{2} ?[A-Z]{3})|([A-Z][0-9]{1,3} ?[A-Z]{3})|([A-Z]{3} ?[0-9]{1,3}[A-Z]))|([a-z]{3}[0-9]{4})/i;

/**
 * @param inputString {string}
 * @returns {string}
 */
function getVehicleRegistrationNumber(inputString) {
    var firstMatchGroups = VEHICLE_REGISTRATION_NUMBER_REG_EXP.exec(inputString);

    if (firstMatchGroups) {
        return firstMatchGroups[0];
    }

    return null;
}

function recheckVehicleRegistrationNumber(inputString) {
    const replacements = {
        0: 'O',
        O: '0'
    };
    return inputString.replace(/[0O!]/g, (match) => replacements[match]);
}

module.exports = {
    getVehicleRegistrationNumber: getVehicleRegistrationNumber,
    recheckVehicleRegistrationNumber: recheckVehicleRegistrationNumber
};
