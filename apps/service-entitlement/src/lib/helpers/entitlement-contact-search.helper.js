'use strict';

var _ = require('lodash'),
    escapeStringRegexp = require('escape-string-regexp'),
    CustomerGroup = require('@aa/malstrom-models/lib/customer-group.model'),
    errors = require('../constants/entitlement-contact-search-error.constants copy'),
    logger = require('winston'),
    searchTypes = require('../constants/search-type.constants'),
    //these customer groups have either weird titles or no titles at all so we bypass the check for title

    //this could all be done on the entitlement but this is going as a hotfix so we cannot. again.
    shouldOverrideTitleCheck = (entitlement) => {
        return ['NBS'].includes(entitlement.policy().customerGroup().code()) || isNAtWest(entitlement);
    },
    isNAtWest = (entitlement) => {
        return ['NWGB', 'NWGP', 'RBSB', 'RBSP'].includes(entitlement.policy().customerGroup().code());
    },
    doInitialsOrFirstNameMatch = (firstName, entitlement) => {
        let match;
        try {
            if (!entitlement.contact()) {
                return false;
            }
            let contact = entitlement.contact().firstName() || entitlement.contact().initials() || '';
            if (entitlement.contact().initials()) {
                match = firstName.match(new RegExp(`^${contact}`, 'gi'));
            } else {
                match = contact.match(new RegExp(`^${firstName}`, 'gi'));
            }
            return match;
        } catch (ex) {
            logger.info('entitlement-contact-search-helper:: doInitialsOrFirstNameMatch : ', ex.stack);
            return false;
        }
    },
    doDobAndCustGroupCodeMatch = (dob, customerGroupCode, entitlement) => {
        let dobMatch, custGroupMatch;
        try {
            if (!entitlement.contact()) {
                return false;
            }
            let dobContact = entitlement.contact().dateOfBirth() || '';
            const formattedDobContact = new Date(dobContact).toISOString().slice(0, 10);
            let customerGroup = entitlement.policy().customerGroup().code() || '';
            if (dobContact && customerGroup) {
                dobMatch = dob.match(new RegExp(`^${formattedDobContact}`, 'gi'));
                custGroupMatch = customerGroupCode.match(new RegExp(`^${customerGroup}`, 'gi'));
                if (dobMatch && custGroupMatch) {
                    return dobMatch;
                }
            }
        } catch (ex) {
            logger.info('entitlement-contact-search-helper:: doDobOrCustGroupCodeMatch : ', ex.stack);
            return false;
        }
    };

module.exports = {
    /**
     * match a named contact from within a set of entitlements using title, firstName and surname
     * @param  {string} searchType different types of searches (fuzzy, potCode) have different needs.
     * @param  {Entitlement} entitlements list of entitlements
     * @param  {string} title        of the person we are searching for ...
     * @param  {string} firstName     of the individual
     * @param  {string} memberRole optional role within membership
     * @return {Entitlement|null}              match or null
     */
    find: function (searchType, entitlements, title, firstName, surname, memberRole) {
        logger.info(`searchType : ${searchType} , title : ${title} , firstName : ${firstName} , surname : ${surname} , memberRole : ${memberRole} , entitlements : ${JSON.stringify(entitlements)}`);
        const matchesByInitialAndSurname = [],
            roleRegEx = new RegExp(memberRole ? escapeStringRegexp(memberRole) : '.+', 'i'), // if memberRole is not defined then accept anything
            surnameRegEx = new RegExp('^' + escapeStringRegexp(surname || '') + '$', 'i'),
            titleRegEx = new RegExp('^' + escapeStringRegexp(title || '') + '$', 'i');

        //RBAUAA-2797 says We don't want to allow authorised drivers.
        //TODO: remove all stuff regarding authorized drivers when we're sure this is what we want.
        entitlements = _.filter(entitlements, (entitlement) => {
            return !entitlement.contact().isAuthorisedDriver();
        });

        var searchSet = entitlements;

        // single hit so we return that - this would work for vehicle based entitlement
        // but only if customer does not have cover through bank! And if it's a fuzzy search! We cannot do this for postcode searches, for example.
        if (searchType === searchTypes.FUZZY_SEARCH && entitlements.length === 1) {
            let customerGroup = new CustomerGroup(entitlements[0].policy().customerGroup().toJSON());
            if (!customerGroup.isBank()) {
                return { entitlement: entitlements[0] };
            }
        }

        // let isAuthorisedDriverAllowed = false;
        // let authorisedDriverEntitlement = null;

        // try and match initials / firstName ...
        if (firstName) {
            _.forEach(entitlements, (entitlement) => {
                const initialsOrFirstNameMatch = doInitialsOrFirstNameMatch(firstName, entitlement);
                if (initialsOrFirstNameMatch && surnameRegEx.test(entitlement.contact().surname())) {
                    matchesByInitialAndSurname.push(entitlement);
                }
                //if (entitlement.policy().customerGroup().isAuthorisedDriverAllowed()) {
                //    isAuthorisedDriverAllowed = true;
                //}
                //if (entitlement.contact().isAnyDriverAllowed()) {
                //    authorisedDriverEntitlement = entitlement;
                //}

                // if (entitlement.policy().customerGroup().isSelfServiceAuthorisedDriverAllowed()) {
                //     isAuthorisedDriverAllowed = true;
                // }
                // if (entitlement.contact().isAuthorisedDriver()) {
                //     authorisedDriverEntitlement = entitlement;
                // }
            });
        }

        const matchesByFullName = matchesByInitialAndSurname.filter((entitlement) => {
            //NBS entitlements do not grace us with a proper title. Some are things like "Major the Lord"...
            //...so we just go ahead and say it's good to go... Natwest also don't have titles...
            //This should be done better but it's going on a hotfix so I won't touch malstrom-models. Maybe one day...
            if (shouldOverrideTitleCheck(entitlement)) {
                return true;
            }

            return titleRegEx.test(entitlement.contact().title());
        });

        // use Authorised driver entitlement when it is Allowed for Customer Group
        // and supplied Title, Initial & surname do not match with any of entitlements
        // if (isAuthorisedDriverAllowed && !matchesByFullName.length && authorisedDriverEntitlement) {
        //     return { entitlement: authorisedDriverEntitlement };
        // }

        // have one one fit then return
        if (matchesByInitialAndSurname.length === 1) {
            return { entitlement: matchesByInitialAndSurname[0] };
        }

        const matchesByRole = matchesByInitialAndSurname.filter((entitlement) => {
            return roleRegEx.test(entitlement.contact().role());
        });

        // if still here then try title
        searchSet = matchesByRole.length === 0 ? entitlements : matchesByRole;

        // filter by title and surname now ..
        const matchesByTitle = _.filter(searchSet, function (entitlement) {
            return (
                (shouldOverrideTitleCheck(entitlement) ? true : titleRegEx.test(entitlement.contact().title())) &&
                surnameRegEx.test(entitlement.contact().surname()) &&
                roleRegEx.test(entitlement.contact().role())
            );
        });

        // we should have one ... no idea what to do if still have no match
        if (searchType === searchTypes.FUZZY_SEARCH && matchesByTitle.length === 1) {
            return { entitlement: matchesByTitle[0] };
        }

        //no match or more than one match. let's bubble it up and let the client app worry about it
        if (matchesByTitle.length === 0) {
            return { searchError: errors.ENTITLEMENT_SEARCH_FOUND_NONE };
        } else {
            return { searchError: errors.ENTITLEMENT_SEARCH_FOUND_MULTIPLE };
        }
    },

    // ID&V changes - new findV2 function:
    // 1. title replaced by dob in the input param
    // 2. customerGroupCode is passed as input instead of memberRole
    // Order of checks carried out : 1. dob, customerGroupCode & surname, 2. initials or firstname

    findV2: function (searchType, entitlements, dob, firstName, surname, customerGroupCode) {
        logger.info(`searchType : ${searchType} , dob : ${dob} , firstName : ${firstName} , surname : ${surname} , entitlements : ${JSON.stringify(entitlements)}`);
        const matchesByDobAndCustGroupAndSurname = [],
            matchesByInitialorFirstName = [],
            surnameRegEx = new RegExp('^' + escapeStringRegexp(surname || '') + '$', 'i');
        //RBAUAA-2797 says We don't want to allow authorised drivers.
        //TODO: remove all stuff regarding authorized drivers when we're sure this is what we want.
        entitlements = _.filter(entitlements, (entitlement) => {
            return !entitlement.contact().isAuthorisedDriver();
        });
        // single hit so we return that - this would work for vehicle based entitlement
        // but only if customer does not have cover through bank! And if it's a fuzzy search! We cannot do this for postcode searches, for example.
        if (searchType === searchTypes.FUZZY_SEARCH && entitlements.length === 1) {
            let customerGroup = new CustomerGroup(entitlements[0].policy().customerGroup().toJSON());
            if (!customerGroup.isBank()) {
                return { entitlement: entitlements[0] };
            }
        }

        // Match the entitlements by dob, customer group code & surname
        if (dob) {
            _.forEach(entitlements, (entitlement) => {
                const dobAndCustomerGroupCodeMatch = doDobAndCustGroupCodeMatch(dob, customerGroupCode, entitlement);
                if (dobAndCustomerGroupCodeMatch && surnameRegEx.test(entitlement.contact().surname())) {
                    matchesByDobAndCustGroupAndSurname.push(entitlement);
                }
            });
        }

        // If single match found, return the entitlement
        if (matchesByDobAndCustGroupAndSurname.length === 1) {
            return { entitlement: matchesByDobAndCustGroupAndSurname[0] };
        }

        // If more than one entitlement, proceed to match with initials or firstname
        if (firstName) {
            _.forEach(entitlements, (entitlement) => {
                const initialsOrFirstNameMatch = doInitialsOrFirstNameMatch(firstName, entitlement);
                if (initialsOrFirstNameMatch && surnameRegEx.test(entitlement.contact().surname())) {
                    matchesByInitialorFirstName.push(entitlement);
                }
            });
        }

        // If single match found, return the entitlement
        if (matchesByInitialorFirstName.length === 1) {
            return { entitlement: matchesByInitialorFirstName[0] };
        }

        // If no match or still multiple matches found, throw error
        if (matchesByInitialorFirstName.length === 0) {
            return { searchError: errors.ENTITLEMENT_SEARCH_FOUND_NONE };
        } else {
            return { searchError: errors.ENTITLEMENT_SEARCH_FOUND_MULTIPLE };
        }
    }
};
