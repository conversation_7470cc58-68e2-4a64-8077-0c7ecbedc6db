'use strict';

const { Entitlement, Product, Benefit } = require('@aa/data-models/aux/entitlement');
const { Policy, Vehicle, CustomerGroup, Address, Phone } = require('@aa/data-models/common');
var _ = require('lodash'),
    Contact = require('@aa/malstrom-models/lib/entitlement-contact.model'),
    refData = require('@aa/ref-data'),
    BenefitsNotFoundError = require('@aa/malstrom-models/lib/exceptions/create-eligibility-benefits-not-found-error.model'),
    memberDetailsForPrimeFactory = require('./prime-member-details-factory'),
    Eligibility = require('@aa/malstrom-models/lib/eligibility-details.model');

function createEntitlementFromEligibility(eligibilityDetails, membershipNumber) {
    var startDate = new Date(),
        endDate = new Date(),
        entitlement = new Entitlement(),
        contact = new Contact(),
        address = new Address(),
        homePhone = new Phone(),
        workPhone = new Phone(),
        policy = new Policy(),
        customerGroup = new CustomerGroup(),
        vehicle = new Vehicle(),
        eligibility = new Eligibility(eligibilityDetails),
        phones = [],
        benefits,
        seSystem,
        pItem;

    endDate.setTime(startDate.getTime() + 24 * 3600 * 1000); // add one date
    seSystem = refData.seSystems().findSync(4);
    entitlement.systemId(seSystem.code());

    contact.initials(eligibility.initials() || eligibility.forename()[0]);
    contact.firstName(eligibility.forename());
    contact.surname(eligibility.surname());
    contact.title(eligibility.title());
    contact.role(eligibility.role());

    homePhone.phoneNumber(eligibility.eveningTelephoneNumber());
    homePhone.extension(eligibility.eveningTelephoneNumberExtension());
    homePhone.type('HOME');
    phones.push(homePhone);
    workPhone.phoneNumber(eligibility.daytimeTelephoneNumber());
    workPhone.extension(eligibility.daytimeTelephoneNumberExtension());
    workPhone.type('WORK');
    phones.push(workPhone);
    contact.phones(phones);

    benefits = refData.riskBenefits().findSync(eligibility.riskCode());

    if (!benefits) {
        throw new BenefitsNotFoundError('Benefits not found for risk code: ' + eligibility.riskCode());
    }

    // make a copy ,,,
    _.forEach(benefits.benefits(), function (bItem) {
        entitlement.benefits().push(new Benefit(bItem.toJSON()));
    });

    policy.membershipNumber(membershipNumber);

    policy.systemId(seSystem.code()); // more WJ magic numbers ..

    // special WJ magic
    if (eligibility.riskCode() === 'WJ') {
        // add WJ benefit so that eva get's it .. we do this here rather than on the 'send'
        entitlement.benefits().push(
            new Benefit({
                id: entitlement.benefits().length,
                code: 'WJ',
                name: 'Will Join'
            })
        );

        contact.address().addressLines(['WILL JOIN QUOTE']);
        // for manufacturer will joins default customer group should be AAHelp

        policy.contractKey('WJ'); // set these WJ values on the obvious place .. UI only has to reference WJ risk code ..
        policy.customerKey('WJ QUOTE');
    } else {
        policy.customerKey(eligibility.cardNumber());

        address.postcode(eligibility.address().postcode());
        address.addressLines(eligibility.address().addressLines());
    }

    customerGroup.code(eligibility.customerGroup().code());
    customerGroup.name(eligibility.customerGroup().name());

    if (eligibility.riskCode() === 'WJ' && eligibility.customerGroup().isManufacturer()) {
        customerGroup.code('AAHE');
        customerGroup.name('AAHELP');
    }

    contact.address(address);

    // copy details on product so that we can show it on the screen
    _.forEach(entitlement.benefits(), function (bItem) {
        pItem = new Product({
            name: bItem.name(),
            code: bItem.code(),
            benefitCode: bItem.code()
        });
        pItem.startDate(startDate);
        pItem.endDate(endDate);
        entitlement.products().push(pItem);
    });

    policy.inceptionDate(new Date());
    policy.seSystem(seSystem);

    policy.customerGroup(customerGroup);

    vehicle.registration(eligibility.vehicle().registration());
    vehicle.makeId(eligibility.vehicle().makeId());
    vehicle.modelId(eligibility.vehicle().modelId());

    entitlement.policy(policy);
    entitlement.vehicle(vehicle);
    entitlement.contact(contact);
    entitlement.riskCode(eligibility.riskCode());
    entitlement.memberDetailsForPrime(memberDetailsForPrimeFactory.createDetails(entitlement));

    return entitlement;
}

//api
module.exports = {
    createEntitlementFromEligibility: createEntitlementFromEligibility
};
