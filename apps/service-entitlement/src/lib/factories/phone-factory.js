'use strict';

var _ = require('lodash');
const { Phone } = require('@aa/data-models/common');

/**
 * Creates an array of Phone objects from supplied raw RES phone array
 *
 * @param {array} sourcePhone
 * @returns {Array.<Contact>}
 */
function createPhones(sourcePhone) {
    var result = [];
    _.forEach(sourcePhone, function createPhone(phoneData) {
        result.push(new Phone(phoneData));
    });
    return result;
}

module.exports = {
    createPhones: createPhones
};
