'use strict';

const { Policy, SeSystem } = require('@aa/data-models/common');

var _ = require('lodash'),
    formatting = require('@aa/utilities/aa-utilities').formatting,
    fleet = require('@aa/utilities/aa-utilities').fleet,
    refRepo = require('@aa/ref-data');

function computeInceptionDateAlert(membershipInceptionDate) {
    var memDate, now;
    if (membershipInceptionDate) {
        now = new Date();
        now.setDate(now.getDate() - 1);
        memDate = new Date(membershipInceptionDate);
        return now.getTime() < memDate.getTime();
    }

    return false;
}

function unlistedSlvPolicy(unlistedSlvEntitlement) {
    var seSystem,
        cg,
        policy = new Policy();

    policy.startDate(unlistedSlvEntitlement.unListedCover.startDate);
    policy.endDate(unlistedSlvEntitlement.unListedCover.endDate);
    cg = fleet.checkFleetInsured(unlistedSlvEntitlement.payForUse, refRepo.customerGroups().findSyncAndClone(unlistedSlvEntitlement.custGroup.trim()));

    policy.customerGroup(cg);
    seSystem = refRepo.seSystems().findSync(cg.systemId());
    if (seSystem) {
        policy.seSystem(seSystem);
    }

    policy.systemId(unlistedSlvEntitlement.systemId);

    policy.policyNumber(unlistedSlvEntitlement.policyId); // this should be renamed to contractKey
    policy.membershipNumber(unlistedSlvEntitlement.flv);
    policy.contractKey(unlistedSlvEntitlement.policyId);
    policy.customerKey(unlistedSlvEntitlement.cardNoFlv);
    policy.supplierOwnReference(unlistedSlvEntitlement.supplierOwnRef);

    policy.status(unlistedSlvEntitlement.status);

    return policy;
}

/**
 *
 * @param resEntitlement
 * @param vehicleDetail
 * @param driverDetail
 * @returns {Policy|exports|module.exports}
 */
function createPolicy(resEntitlement, vehicleDetail, driverDetail) {
    var seSystem,
        cg,
        customerGroupCode = resEntitlement.custGroup || resEntitlement.customerGroupKey,
        policy = new Policy();

    if (driverDetail || vehicleDetail) {
        policy.startDate(vehicleDetail ? new Date(vehicleDetail.startDate) : new Date(driverDetail.startDate));
        policy.endDate(vehicleDetail ? new Date(vehicleDetail.endDate) : new Date(driverDetail.endDate));

        if (
            resEntitlement.systemId === SeSystem.SYSTEM_ID_TIA &&
            !_.isNaN(policy.endDate().getTime()) &&
            !_.isNaN(policy.startDate().getTime()) &&
            policy.endDate().getUTCFullYear() === policy.startDate().getUTCFullYear() &&
            policy.endDate().getUTCMonth() === policy.startDate().getUTCMonth() &&
            policy.endDate().getUTCDate() === policy.startDate().getUTCDate()
        ) {
            //if end date and start date are the same, assume new policy so add 7 days to end date
            policy.endDate(new Date(policy.endDate().getUTCFullYear(), policy.endDate().getUTCMonth(), policy.endDate().getUTCDate() + 7));
        }
    }

    if (_.isString(resEntitlement.cardType)) {
        policy.cardType(formatting.toTitleCase(resEntitlement.cardType));
    }

    cg = fleet.checkFleetInsured(resEntitlement.payForUse, refRepo.customerGroups().findSyncAndClone(customerGroupCode));

    policy.customerGroup(cg);
    policy.membershipType(resEntitlement.membershipType);

    policy.inceptionDate(resEntitlement.membershipInceptionDate);
    policy.inceptionDateAlert(computeInceptionDateAlert(resEntitlement.membershipInceptionDate));
    policy.systemId(resEntitlement.systemId);

    policy.membershipNumber(resEntitlement.membershipNo);
    policy.status(resEntitlement.status);
    policy.supplierOwnReference(resEntitlement.supplierOwnRef);

    // policy no greater than 20 and custGRP as ADM
    if (resEntitlement.custGroup === 'ADM' && policy.supplierOwnReference().length >= 20) {
        policy.supplierOwnReference(_.first(resEntitlement.vehicleDetails).regNum);
        //policy.supplierCustomerReference(_.first(resEntitlement.vehicleDetails).regNum); //suss
    }

    if (driverDetail) {
        // we do this only for UBE
        if (driverDetail.supplierCustomerReference) {
            policy.supplierCustomerReference(driverDetail.supplierCustomerReference);
        }

        if (driverDetail.memberStatus) {
            policy.memberStatus(driverDetail.memberStatus);
        }
    }

    policy.policyNumber(resEntitlement.policyId); // this should be renamed to contractKey
    policy.contractKey(resEntitlement.policyId); // this should be renamed to contractKey
    policy.customerKey(resEntitlement.cardNoFlv); //

    seSystem = refRepo.seSystems().findSync(cg.systemId());

    if (seSystem) {
        policy.seSystem(seSystem);
    }

    return policy;
}

module.exports = {
    createPolicy: createPolicy,
    unlistedSlvPolicy: unlistedSlvPolicy
};
