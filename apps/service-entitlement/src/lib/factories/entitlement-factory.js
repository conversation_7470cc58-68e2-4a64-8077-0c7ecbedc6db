'use strict';

const { Entitlement, Product, Benefit } = require('@aa/data-models/aux/entitlement');
const { Policy, CustomerGroup, Address, Vehicle, Phone, CorrespondenceAddress } = require('@aa/data-models/common');
var HomeInsuranceEntitlementFactory = require('./home-insurance-entitlements.factory'),
    contactFactory = require('./contact-factory'),
    policyFactory = require('./policy-factory'),
    vehicleFactory = require('./vehicle-factory'),
    productFactory = require('./product-factory'),
    narrativeFactory = require('./narrative-factory'),
    coverFactory = require('./cover-factory'),
    FlexFieldsFormat = require('./flex-fields.format'),
    memberDetailsForPrimeFactory = require('./prime-member-details-factory'),
    benefitFactory = require('./benefit-factory'),
    sagaTextTopicFactory = require('./saga-text-topic.factory'),
    _ = require('lodash'),
    moment = require('moment'),
    Contact = require('@aa/malstrom-models/lib/entitlement-contact.model'),
    refData = require('@aa/ref-data'),
    BenefitsNotFoundError = require('@aa/malstrom-models/lib/exceptions/create-eligibility-benefits-not-found-error.model'),
    MembershipAmendsConstants = require('../constants/membership-amends.constants'),
    CarHireEntitlmentFactory = require('./car-hire-entitlement-factory');

function _buildEntitlementsForDriver(driverDetail, resEntitlement, coverItems, vehicleDetail, sagaTextTopics) {
    var entitlement = new Entitlement(),
        products = [],
        allEntitlementVariableData = [];

    allEntitlementVariableData = allEntitlementVariableData.concat(FlexFieldsFormat.removeLegacyFormat(resEntitlement.entitlementVariableData));
    allEntitlementVariableData = allEntitlementVariableData.concat(FlexFieldsFormat.removeLegacyFormat(driverDetail.entitlementVariableData));

    entitlement.payForUse(resEntitlement.payForUse);
    entitlement.systemId(resEntitlement.systemId);
    entitlement.fairPlay(resEntitlement.fairPlay);
    entitlement.vipServiceCode(resEntitlement.vipServiceCode);
    entitlement.contact(contactFactory.createContactFromResDriverDetails(driverDetail));
    entitlement.policy(policyFactory.createPolicy(resEntitlement, vehicleDetail, driverDetail));
    entitlement.isSplit(resEntitlement.isSplit);

    // Set correspondence address
    if (resEntitlement.correspondenceAddress) {
        entitlement.correspondenceAddress(resEntitlement.correspondenceAddress);
    }

    if (resEntitlement.custGroup === 'SAGA') {
        _.forEach(sagaTextTopics, function (sagaTextTopic) {
            allEntitlementVariableData.push(sagaTextTopicFactory.create(sagaTextTopic));
        });
    }

    if (driverDetail && driverDetail.cover) {
        entitlement.cover(coverFactory.createCover(driverDetail.cover));
    }

    if (vehicleDetail && vehicleDetail.cover && vehicleDetail.cover.package) {
        // for reason I do not understand we need to show the name o f the package ...
        // simplest way is to treat is as the Product
        products.push(
            productFactory.createProduct(
                {
                    product: vehicleDetail.cover.package,
                    endDate: vehicleDetail.cover.endDate,
                    startDate: vehicleDetail.cover.startDate
                },
                resEntitlement.systemId
            )
        );
    }

    if (driverDetail && driverDetail.cover && driverDetail.cover.package) {
        products.push(
            productFactory.createProduct(
                {
                    product: driverDetail.cover.package,
                    endDate: driverDetail.cover.endDate,
                    startDate: driverDetail.cover.startDate
                },
                resEntitlement.systemId
            )
        );
    }

    //seems Continuous Monthly Payments have the product endDate to the year 2100.
    //We can't have that. Not on my watch!
    coverItems.forEach((coverItem) => {
        if (!driverDetail || !driverDetail.cover) {
            return;
        }
        if (coverItem.endDate > driverDetail.cover.endDate) {
            coverItem.endDate = driverDetail.cover.endDate;
        }
    });

    entitlement.products(products.concat(productFactory.createProducts(coverItems, resEntitlement.systemId)));

    entitlement.narratives(narrativeFactory.createNarratives(driverDetail.narrative));

    // get benefit codes
    entitlement.benefits(benefitFactory.map(coverItems));
    entitlement.carRental(CarHireEntitlmentFactory.map(coverItems));

    if (vehicleDetail) {
        entitlement.vehicle(vehicleFactory.createVehicle(vehicleDetail));
        allEntitlementVariableData = allEntitlementVariableData.concat(FlexFieldsFormat.removeLegacyFormat(vehicleDetail.entitlementVariableData));
    }

    entitlement.memberDetailsForPrime(memberDetailsForPrimeFactory.createDetails(entitlement));

    entitlement.entitlementVariableData(allEntitlementVariableData);

    if (
        entitlement.policy().status() === 'A' &&
        entitlement.policy().customerGroup().isLLOYDSGroup() &&
        !entitlement.benefits().some((item) => item.code() === 'H') &&
        MembershipAmendsConstants.AMEND_HOME_START.LLOYDS_FREE_HS_START_DATE.getTime() < Date.now() &&
        Date.now() < MembershipAmendsConstants.AMEND_HOME_START.LLOYDS_FREE_HS_END_DATE.getTime()
    ) {
        const AMEND_START_DATE = MembershipAmendsConstants.AMEND_HOME_START.LLOYDS_FREE_HS_START_DATE;
        const AMEND_END_DATE = MembershipAmendsConstants.AMEND_HOME_START.LLOYDS_FREE_HS_END_DATE;
        entitlement.benefits().push(
            new Benefit({
                id: 2,
                code: 'H',
                name: 'Home Start',
                promptTextId: 18
            })
        );
        entitlement.products().push(
            new Product({
                additionalProperties: [],
                benefitCode: 'H',
                code: 'HomeStart',
                endDate: AMEND_END_DATE,
                expired: false,
                name: `HomeStart (${moment(AMEND_START_DATE).format('l')} - ${moment(AMEND_END_DATE).format('l')})`,
                startDate: AMEND_START_DATE
            })
        );
    }
    //RBAUAA-12045 EBC for PERS entitlements will have tripduration, less than 365 days means single trip
    //if trip duration is more than 365 days then it is multi trip
    if (resEntitlement.custGroup === 'PERS') {
        entitlement.daysCovered(resEntitlement.tripDuration);
        if (resEntitlement.tripDuration && resEntitlement.tripDuration < 365) {
            entitlement.tripCountLimit(1);
        }
        entitlement.tripCountLimit(-1);
    } else {
        //setting default value to -1, this indicated no limit
        //TODO: check with business if this is correct and need to be changed after benefic group creation
        entitlement.tripCountLimit(-1);
    }

    return entitlement;
}

function _buildEntitlementsForVehicle(resEntitlement, vehicleDetail, sagaTextTopics) {
    var entitlement = new Entitlement(),
        products = [],
        allEntitlementVariableData = [];

    allEntitlementVariableData = allEntitlementVariableData.concat(FlexFieldsFormat.removeLegacyFormat(resEntitlement.entitlementVariableData));
    allEntitlementVariableData = allEntitlementVariableData.concat(FlexFieldsFormat.removeLegacyFormat(vehicleDetail.entitlementVariableData));

    if (resEntitlement.custGroup === 'SAGA') {
        _.forEach(sagaTextTopics, function (sagaTextTopic) {
            allEntitlementVariableData.push(sagaTextTopicFactory.create(sagaTextTopic));
        });
    }

    entitlement.payForUse(resEntitlement.payForUse);
    entitlement.systemId(resEntitlement.systemId);
    entitlement.fairPlay(resEntitlement.fairPlay);
    entitlement.contact(contactFactory.bcasContact(resEntitlement));
    entitlement.isSplit(resEntitlement.isSplit);

    // for reason I do not understand we need to show the name of the package ...
    // simplest way is to treat is as the Product
    products.push(
        productFactory.createProduct({
            product: vehicleDetail.cover.package,
            endDate: vehicleDetail.cover.endDate,
            startDate: vehicleDetail.cover.startDate
        })
    );
    products = products.concat(productFactory.createProducts(vehicleDetail.cover.coverItems));
    entitlement.cover(coverFactory.createCover(vehicleDetail.cover));

    entitlement.products(products);

    // get benefit codes merge those of the company and vehicle ..
    entitlement.benefits(benefitFactory.map(vehicleDetail.cover.coverItems));
    entitlement.carRental(CarHireEntitlmentFactory.map(vehicleDetail.cover.coverItems));

    entitlement.vehicle(vehicleFactory.createVehicle(vehicleDetail));
    entitlement.policy(policyFactory.createPolicy(resEntitlement, vehicleDetail, null));

    entitlement.memberDetailsForPrime(memberDetailsForPrimeFactory.bcasMemberDetails(resEntitlement));

    entitlement.entitlementVariableData(allEntitlementVariableData);
    //RBAUAA-12045 EBC for PERS entitlements will have tripduration
    if (resEntitlement.custGroup === 'PERS') {
        entitlement.daysCovered(resEntitlement.tripDuration);
        if (resEntitlement.tripDuration && resEntitlement.tripDuration < 365) {
            entitlement.tripCountLimit(1);
        }
        entitlement.tripCountLimit(-1);
    } else {
        //setting default value to -1, this indicated no limit
        //TODO: check with business if this is correct and need to be changed after benefic group creation
        entitlement.tripCountLimit(-1);
    }

    return entitlement;
}

function processSagaRecords(mainDriver, entitlement, coverItems, vehicleDetails, textTopics) {
    // saga always has a vehicle record and may have additional drivers.
    // membershipType V indicates vehicle based, P indicated Personal.
    var entitlements = [];

    //take care of the mandatory vehicle entitlement
    entitlements.push(_buildEntitlementsForDriver(mainDriver, entitlement, coverItems, vehicleDetails, textTopics));

    //if membership type is Personal, add in the driver entitlements
    if (entitlement.membershipType === 'P') {
        _.forEach(entitlement.driverDetails, function (driver) {
            entitlements.push(_buildEntitlementsForDriver(driver, entitlement, coverItems, null, textTopics));
        });
    }
    return entitlements;
}

function processEntitlementRecords(mainDriver, entitlement, sagaTextTopics) {
    var entitlements = [];

    if (entitlement.driverDetails.length) {
        // if threre are any vehicle in the policy then
        if (entitlement.vehicleDetails.length > 0) {
            if ([CustomerGroup.UBE, CustomerGroup.NBS, CustomerGroup.NWGB, CustomerGroup.NWGP, CustomerGroup.RBSB, CustomerGroup.RBSP].includes(entitlement.custGroup)) {
                /// only for UBER and Nationwide ... where a vehicle based membership can have multiple driver
                _.forEach(entitlement.driverDetails, function (driver) {
                    entitlements.push(_buildEntitlementsForDriver(driver, entitlement, _.head(entitlement.vehicleDetails).cover.coverItems, _.head(entitlement.vehicleDetails), sagaTextTopics));
                });
            } else {
                // create only one entry with the main driver
                entitlements.push(_buildEntitlementsForDriver(mainDriver, entitlement, _.head(entitlement.vehicleDetails).cover.coverItems, _.head(entitlement.vehicleDetails), sagaTextTopics));
            }
        } else {
            // otherwise create one entitlement per driver
            _.forEach(entitlement.driverDetails, function (driver) {
                entitlements.push(_buildEntitlementsForDriver(driver, entitlement, mainDriver.cover.coverItems, null, sagaTextTopics));
            });
        }
    }

    if (entitlement.driverDetails.length === 0 && entitlement.vehicleDetails.length > 0) {
        _.forEach(entitlement.vehicleDetails, function (vehicle) {
            entitlements.push(_buildEntitlementsForVehicle(entitlement, vehicle, sagaTextTopics));
        });
    }

    return entitlements;
}

function createEntitlements(resEntitlements, sagaTextTopics) {
    var listOfEntitlements = [];
    var entitlement = null;
    var mainDriver;

    _.forEach(resEntitlements.entitlement, function (rawEntitlement) {
        var driverDetails = rawEntitlement.driverDetails;
        var vehicleDetails = rawEntitlement.vehicleDetails;

        mainDriver = _.head(
            _.concat(
                _.filter(driverDetails, function (item) {
                    return item.mainDriver === true;
                }),
                driverDetails
            )
        );

        if (rawEntitlement.custGroup === 'SAGA' && vehicleDetails.length > 0) {
            listOfEntitlements = listOfEntitlements.concat(processSagaRecords(mainDriver, rawEntitlement, _.head(vehicleDetails).cover.coverItems, _.head(vehicleDetails), sagaTextTopics));
        } else {
            listOfEntitlements = listOfEntitlements.concat(processEntitlementRecords(mainDriver, rawEntitlement, sagaTextTopics));
        }
    });

    if (resEntitlements.insuranceEntitlements && resEntitlements.insuranceEntitlements.length > 0) {
        listOfEntitlements.push.apply(listOfEntitlements, HomeInsuranceEntitlementFactory.build(resEntitlements.insuranceEntitlements)); //appends to listOfEntitlements array without creating a new array
    }

    return listOfEntitlements;
}

/**
 * Create entitlement using customer request & seAaHelp data
 *
 * @param {Object} cr               CustomerRequest model
 * @param {Object} seAaHelp         SeAahelp model
 * @returns {object} entitlement    Entitlement model
 */
function createNonValidationEntitlements(cr, seAaHelp) {
    var startDate = new Date(),
        endDate = new Date(),
        entitlement = new Entitlement(),
        contact = new Contact(),
        address = new Address(),
        homePhone = new Phone(),
        workPhone = new Phone(),
        policy = new Policy(),
        customerGroup = new CustomerGroup(),
        vehicle = new Vehicle(),
        phones = [],
        benefits,
        seSystem,
        pItem;

    endDate.setTime(startDate.getTime() + 24 * 3600 * 1000); // add one date
    seSystem = refData.seSystems().findSync(4);
    entitlement.systemId(seSystem.code());

    var custShortName = cr.custShortName();
    // extracting initial from custShortName (last charcter)
    contact.initials(custShortName[custShortName.length - 1]);
    // no source to find out firstName
    contact.firstName('');
    contact.surname(seAaHelp.surname());
    contact.title(seAaHelp.title());
    contact.role(cr.customerKey());

    homePhone.phoneNumber(seAaHelp.contact().eveningTelephone());
    homePhone.extension(seAaHelp.contact().eveningExtension());
    homePhone.type('HOME');
    phones.push(homePhone);
    workPhone.phoneNumber(seAaHelp.contact().telephone());
    workPhone.extension(seAaHelp.contact().extension());
    workPhone.type('WORK');
    phones.push(workPhone);
    contact.phones(phones);

    benefits = refData.riskBenefits().findSync(seAaHelp.riskCode());

    if (!benefits) {
        throw new BenefitsNotFoundError('Benefits not found for risk code: ' + seAaHelp.riskCode());
    }

    // make a copy ,,,
    _.forEach(benefits.benefits(), function (bItem) {
        entitlement.benefits().push(new Benefit(bItem.toJSON()));
    });

    policy.membershipNumber(seAaHelp.contractKey());

    policy.systemId(seSystem.code()); // more WJ magic numbers ..

    // special WJ magic
    if (seAaHelp.riskCode() === 'WJ') {
        // add WJ benefit so that eva get's it .. we do this here rather than on the 'send'
        entitlement.benefits().push(
            new Benefit({
                id: entitlement.benefits().length,
                code: 'WJ',
                name: 'Will Join'
            })
        );

        contact.address().addressLines(['WILL JOIN QUOTE']);
        // for manufacturer will joins default customer group should be AAHelp

        policy.contractKey('WJ'); // set these WJ values on the obvious place .. UI only has to reference WJ risk code ..
        policy.customerKey('WJ QUOTE');
    } else {
        policy.customerKey(null);

        // Not able to find postcode & address.. possiblity to extract if from seAahelp.memberDetails()
        address.postcode('');
        address.addressLines('');
    }

    customerGroup.code(cr.assistType().customerGroup().code());
    customerGroup.name(cr.assistType().customerGroup().name());

    if (seAaHelp.riskCode() === 'WJ' && cr.assistType().customerGroup().isManufacturer()) {
        customerGroup.code('AAHE');
        customerGroup.name('AAHELP');
    }

    contact.address(address);

    // copy details on product so that we can show it on the screen
    _.forEach(entitlement.benefits(), function (bItem) {
        pItem = new Product({
            name: bItem.name(),
            code: bItem.code(),
            benefitCode: bItem.code()
        });
        pItem.startDate(startDate);
        pItem.endDate(endDate);
        entitlement.products().push(pItem);
    });

    policy.inceptionDate(new Date());
    policy.seSystem(seSystem);

    policy.customerGroup(customerGroup);

    // not able to find out vehicle details
    vehicle.registration('');
    vehicle.makeId('');
    vehicle.modelId('');

    entitlement.policy(policy);
    entitlement.vehicle(vehicle);
    entitlement.contact(contact);
    entitlement.riskCode(seAaHelp.riskCode());
    entitlement.memberDetailsForPrime(seAaHelp.memberDetails());

    return entitlement;
}

//api
module.exports = {
    createEntitlements: createEntitlements,
    createNonValidationEntitlements: createNonValidationEntitlements
};
