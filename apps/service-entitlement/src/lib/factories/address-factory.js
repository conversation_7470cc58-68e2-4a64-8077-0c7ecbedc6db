'use strict';
const { Address } = require('@aa/data-models/common');

function createAddress(sourceAddress) {
    var address = new Address();
    if (sourceAddress) {
        address.houseNoName(sourceAddress.houseNoName);
        address.addressLines(sourceAddress.line);
        address.postcode(sourceAddress.outPostCode + ' ' + sourceAddress.inPostCode);
    }
    return address;
}

module.exports = {
    createAddress: createAddress
};
