'use strict';

const { Usage, SeSystem } = require('@aa/data-models/common');
const { Product } = require('@aa/data-models/aux/entitlement');
var _ = require('lodash'),
    KeyValue = require('@aa/malstrom-models/lib/key-value.model');

function createProducts(rawProducts, systemId) {
    var products = [];

    _.forEach(rawProducts, function _forEachProduct(rawProduct) {
        products.push(createProduct(rawProduct, systemId));
    });
    return products;
}

/**
 * Creates a product instance
 * @param rawProduct
 * @param systemId
 * @returns {Product|exports|module.exports}
 */
function createProduct(rawProduct, systemId) {
    var product = new Product();

    product.name(rawProduct.product);
    product.code(rawProduct.productCode);
    product.claimFromDateTime(rawProduct.claimFromDateTime ? new Date(rawProduct.claimFromDateTime) : null);
    product.startDate(new Date(rawProduct.startDate));
    product.endDate(new Date(rawProduct.endDate));
    product.benefitCode(rawProduct.benefitCode);
    if (['BRC'].includes(rawProduct.benefitCode)) {
        // isValidVehicle - BRC Start Date after considering cooling off period - dont include a vehicle in coveredVehicle if it is under cooling off period
        let filteredVehicles = [],
            currentDate = new Date();
        filteredVehicles = rawProduct.coveredVehicle.filter((vehicle) => {
            let isValidVehicle = vehicle.claimFromDateTime ? new Date(vehicle.claimFromDateTime) < currentDate : new Date(vehicle.startDate) < currentDate;
            return isValidVehicle;
        });
        product.coveredVehicle(_.map(filteredVehicles, 'regNum'));
    } else {
        product.coveredVehicle(_.map(rawProduct.coveredVehicle, 'regNum'));
    }

    if (rawProduct.coverItemParameters && rawProduct.coverItemParameters.coverParameter && rawProduct.coverItemParameters.coverParameter.length) {
        product.additionalProperties(
            rawProduct.coverItemParameters.coverParameter.map((parameter) => {
                return new KeyValue({
                    key: parameter.name,
                    value: parameter.value
                });
            })
        );
    }

    if (
        systemId === SeSystem.SYSTEM_ID_TIA &&
        !_.isNaN(product.endDate().getTime()) &&
        !_.isNaN(product.startDate().getTime()) &&
        product.endDate().getUTCFullYear() === product.startDate().getUTCFullYear() &&
        product.endDate().getUTCMonth() === product.startDate().getUTCMonth() &&
        product.endDate().getUTCDate() === product.startDate().getUTCDate()
    ) {
        //if end date and start date are the same, assume newly added product and add 7 days to end date
        product.endDate(new Date(product.endDate().getUTCFullYear(), product.endDate().getUTCMonth(), product.endDate().getUTCDate() + 7));
    }

    product.expired(product.endDate() ? product.endDate().getTime() < Date.now() : false);

    //usage
    if (rawProduct.usage && rawProduct.usage.length) {
        let usageAri = rawProduct.usage.map((usage) => new Usage(usage));
        product.usage(usageAri);
    }

    return product;
}

module.exports = {
    createProducts: createProducts,
    createProduct: createProduct
};
