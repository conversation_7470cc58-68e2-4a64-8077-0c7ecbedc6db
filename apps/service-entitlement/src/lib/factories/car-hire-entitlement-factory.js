'use strict';

const { CarRentalEntitlement } = require('@aa/data-models/aux/entitlement');
var _ = require('lodash'),
    Q = require('q'),
    logger = require('winston'),
    brEnums = require('../constants/business-rules.enum');

let _hireRulesCache = {};

/**
 * Extract business rules from coverItems.
 * Business rules from RES exists only for RelayPlus(RP)/StayMobile product and B2B customer
 * B2C customer - alwayws res has null value for carRental
 * Every entitlement has coverItems. Business rules from RES are comming as carRental inside coverItems
 * @param {*} coverItems
 */
function carRentalRulesMapper(coverItems) {
    let carRental = null;
    _.forEach(coverItems, function benefitMapper(item) {
        if (item.productCode === 'RP') {
            carRental = item.carRental ? new CarRentalEntitlement(item.carRental) : null;
        }
    });
    return carRental;
}
/**
 * Find business rules in the list of entitlements and cached based on customerReqId
 * @param {*} customerReqId
 * @param {*} entitlements
 */

function processEntitlements(customerReqId, data) {
    const { entitlements } = data;

    // If cache has already rules for customerReqId then not needed to check in entitlements
    if (_hireRulesCache.hasOwnProperty(customerReqId)) {
        return;
    }

    if (customerReqId > 0 && entitlements && entitlements.length > 0) {
        const result = _.find(entitlements, function process(item) {
            if (item && item.carRental) {
                return typeof item.carRental === 'object' ? item.carRental : typeof item.carRental === 'function' ? item.carRental() : false;
            }
            return false;
        });
        if (result) {
            _hireRulesCache[customerReqId] = typeof result.carRental === 'object' ? result.carRental : result.carRental().toJSON(); // JSON Object
            logger.info(`car hire entitlements from RES for customerReqId ${customerReqId} - ${JSON.stringify(_hireRulesCache[customerReqId])}`);
        }
    }
}

/**
 * Find business rules from entitlement and cached based on customerReqId
 * @param {*} customerReqId
 * @param {*} entitlement
 */

function processEntitlement(customerReqId, entitlement) {
    // If cache has already rules for customerReqId then not needed to check in entitlements
    if (_hireRulesCache.hasOwnProperty(customerReqId)) {
        return;
    }
    if (customerReqId > 0 && entitlement && entitlement.carRental && entitlement.carRental()) {
        logger.info(`car hire entitlements from RES for customerReqId ${customerReqId} - ${entitlement.carRental().toJSON()}`);
        _hireRulesCache[customerReqId] = entitlement.carRental().toJSON();
    }
}

/**
 * Prepare and Return cached business rules based on customerReqId
 * @param {*} customerReqId
 * @returns {object} business rules
 */
function getCachedRules(customerReqId) {
    if (_hireRulesCache.hasOwnProperty(customerReqId)) {
        const rules = new CarRentalEntitlement(_hireRulesCache[customerReqId]);
        return Q.resolve({
            allowedCarCategory: rules.rentalCategory(),
            initialHireDays: rules.initialRental().period().value(),
            initialHireDaysType: rules.initialRental().period().unit(),
            officeHoursSupport: brEnums.officeHoursSupport.TRUE,
            outOfOfficeHoursSupport: brEnums.outOfOfficeHoursSupport.TRUE,
            fuelDepositRequired: rules.fuelDepositRequired(),
            licenseRequired: rules.licenseRequired(),
            creditCardRequired: rules.creditCardRequired(),
            faultRestrictions: {
                rtc: {
                    period: {
                        value: rules.faultRestrictions().rtc().period().value(),
                        unit: rules.faultRestrictions().rtc().period().unit()
                    },
                    allowed: rules.faultRestrictions().rtc().allowed()
                },
                misfuel: {
                    period: {
                        value: rules.faultRestrictions().misfuel().period().value(),
                        unit: rules.faultRestrictions().misfuel().period().unit()
                    },
                    allowed: rules.faultRestrictions().misfuel().allowed()
                },
                frozenPartFault: {
                    period: {
                        value: rules.faultRestrictions().frozenPartFault().period().value(),
                        unit: rules.faultRestrictions().frozenPartFault().period().unit()
                    },
                    allowed: rules.faultRestrictions().frozenPartFault().allowed()
                },
                punctureFault: {
                    period: {
                        value: rules.faultRestrictions().punctureFault().period().value(),
                        unit: rules.faultRestrictions().punctureFault().period().unit()
                    },
                    allowed: rules.faultRestrictions().punctureFault().allowed()
                },
                heatingSystemFault: {
                    period: {
                        value: rules.faultRestrictions().heatingSystemFault().period().value(),
                        unit: rules.faultRestrictions().heatingSystemFault().period().unit()
                    },
                    allowed: rules.faultRestrictions().heatingSystemFault().allowed()
                },
                vehicleKeysFault: {
                    period: {
                        value: rules.faultRestrictions().vehicleKeysFault().period().value(),
                        unit: rules.faultRestrictions().vehicleKeysFault().period().unit()
                    },
                    allowed: rules.faultRestrictions().vehicleKeysFault().allowed()
                },
                adBlueInFuelFault: {
                    period: {
                        value: rules.faultRestrictions().adBlueInFuelFault().period().value(),
                        unit: rules.faultRestrictions().adBlueInFuelFault().period().unit()
                    },
                    allowed: rules.faultRestrictions().adBlueInFuelFault().allowed()
                },
                stuckInFault: {
                    period: {
                        value: rules.faultRestrictions().stuckInFault().period().value(),
                        unit: rules.faultRestrictions().stuckInFault().period().unit()
                    },
                    allowed: rules.faultRestrictions().stuckInFault().allowed()
                },
                towingFault: {
                    period: {
                        value: rules.faultRestrictions().towingFault().period().value(),
                        unit: rules.faultRestrictions().towingFault().period().unit()
                    },
                    allowed: rules.faultRestrictions().towingFault().allowed()
                },
                miscFault: {
                    period: {
                        value: rules.faultRestrictions().miscFault().period().value(),
                        unit: rules.faultRestrictions().miscFault().period().unit()
                    },
                    allowed: rules.faultRestrictions().miscFault().allowed()
                },
                waterInFuelFault: {
                    period: {
                        value: rules.faultRestrictions().waterInFuelFault().period().value(),
                        unit: rules.faultRestrictions().waterInFuelFault().period().unit()
                    },
                    allowed: rules.faultRestrictions().waterInFuelFault().allowed()
                },
                insuranceCoveredFaults: {
                    period: {
                        value: rules.faultRestrictions().insuranceCoveredFaults().period().value(),
                        unit: rules.faultRestrictions().insuranceCoveredFaults().period().unit()
                    },
                    allowed: rules.faultRestrictions().insuranceCoveredFaults().allowed()
                },
                outOfFuelFault: {
                    period: {
                        value: rules.faultRestrictions().outOfFuelFault().period().value(),
                        unit: rules.faultRestrictions().outOfFuelFault().period().unit()
                    },
                    allowed: rules.faultRestrictions().outOfFuelFault().allowed()
                }
            },
            iDRequired: rules.idRequired(),
            extensionAllowed: rules.extension().allowed(),
            extensionDays: rules.extension().period().value(),
            extensionDaysType: rules.extension().period().unit(),
            requestOnly: rules.initialRental().requestOnly(),
            vehicleDelayType: rules.coolOffPeriod().period().unit(),
            vehicleDelay: rules.coolOffPeriod().period().value(),
            towbarAllowed: rules.towbarAllowed(),
            followRecoveryRequired: rules.followRecoveryRequired(),
            canPatrolBook: rules.canPatrolBook()
        });
    }
    return Q.resolve(null);
}

module.exports = {
    map: carRentalRulesMapper,
    getCachedRules,
    processEntitlements,
    processEntitlement
};
