'use strict';

const dealerCodes = require('../constants/enterprise-dealer-codes.constants');
let entBusinessRules;
require('../constants/business-rules.constants').then((i) => (entBusinessRules = i));

var _ = require('lodash'),
    logger = require('winston'),
    errors = require('../constants/error.constants'),
    taskService = require('../services/task.service'),
    businessRulesSvc = require('../services/business-rules.service'),
    CacheService = require('@aa/utilities/aa-utilities').CacheService,
    Task = require('@aa/malstrom-models/lib/task.model');

//models
var MembershipQuery = require('@aa/malstrom-models/lib/membership-query.model'),
    FuzzyEntitlementSearchRequest = require('@aa/malstrom-models/lib/fuzzy-entitlement-search-request.model'),
    CustomerRequest = require('@aa/mobility-models-common/lib/customer-request.model');

//helpers
var PostcodeSearchHelper = require('../helpers/postcode-search-helper'),
    membershipSearchHelper = require('../helpers/membership-search-helper'),
    officeHourHelper = require('../helpers/office-hour.helper');

//builders
var QueryBuilder = require('../builders/query-builder');

//factories
var entitlementFactory = require('../factories/entitlement-factory'),
    carHireEntitlementFactory = require('../factories/car-hire-entitlement-factory');
const { getFaultCodes } = require('../services/hire-fault-codes.service');

var companyService, customerRequestEntitlementService, refDataRepo, searchService, telematicsSearchService, seAaHelpDataRepo, cacheService;

module.exports = {
    init: init,
    search: search,
    getCompanyById: getCompanyById,
    getTelematicsEntitlements: getTelematicsEntitlements,
    getEntitlementsByCompanyIdAndSlvType: getEntitlementsByCompanyIdAndSlvType,
    scheduleB2B: scheduleB2B,
    getEntitlementByCustomerRequest: getEntitlementByCustomerRequest,
    fuzzyEntitlementSearch: fuzzyEntitlementSearch,
    fetchBusinessRules: getBusinessRules,
    getFaultCodeMapping: getFaultCodeMapping,
    throwErrorIfConfigSettingMissing: throwErrorIfConfigSettingMissing,
    bcaspEntitlementSearch,
    businessRules,
    getCachedResBusinessRules,
    vehicleRegEntitlementSearch,
    postcodeEntitlementSearch,
    postcodeEntitlementSearchV2,
    getPoliciesByCustomerGroupCode,
    getProductsByCustomerGroupCode
};

/**
 * [init description]
 * @param  {[type]} companySvc       [description]
 * @param  {[type]} crEntitlementSvc [description]
 * @param  {[type]} srchSvc          [description]
 * @param  {[type]} telematicSrchSvc [description]
 * @param  {[type]} aahelpRepo       [description]
 * @param  {[type]} seAahelpRepo     [description]
 * @param  {CacheService} cacheSvc   [description]
 * @return {[type]}                  [description]
 */
function init(companySvc, crEntitlementSvc, srchSvc, telematicSrchSvc, aahelpRepo, seAahelpRepo, cacheSvc) {
    companyService = companySvc;
    customerRequestEntitlementService = crEntitlementSvc;
    searchService = srchSvc;
    telematicsSearchService = telematicSrchSvc;
    refDataRepo = aahelpRepo;
    seAaHelpDataRepo = seAahelpRepo;
    cacheService = cacheSvc;
}

/**
 * Post request endpoint for handling membership no search
 * @param req containing the MembershipQuery object
 * @param res
 */
function search(req, res) {
    var query, postcodeDetails, membershipQuery, customerGroup, searchCriteriaCheck;

    if (!req.body.membershipSearch) {
        throw new Error('SearchController.search :: missing membershipSearch query object');
    }

    membershipQuery = new MembershipQuery(req.body.membershipSearch);

    // need to default this to 1 otherwise res will return nothing
    if (membershipQuery.param().page() === 0) {
        membershipQuery.param().page(1);
    }

    if (membershipQuery.param().customerGroupCode()) {
        customerGroup = refDataRepo.customerGroups().findSync(membershipQuery.param().customerGroupCode());
    }

    if (membershipQuery.search()) {
        //construct AdvancedMembershipQuery from query string
        query = QueryBuilder.build(membershipQuery.search(), customerGroup);

        query.page(membershipQuery.param().page());
        query.pageSize(membershipQuery.param().pageSize());

        searchCriteriaCheck = membershipSearchHelper.validMembershipSearch(query);
        if (!searchCriteriaCheck.valid) {
            return res.status(searchCriteriaCheck.returnStatus).json(searchCriteriaCheck.error);
        }

        if (
            !query.membershipNumber() &&
            !query.policyId() &&
            !query.inPostcode() &&
            !query.outPostcode() &&
            !query.vehicleIdentificationNumber() &&
            !query.vehicleRegistrationNumber() &&
            !query.surname() &&
            !query.town()
        ) {
            res.status(500).send(JSON.stringify(errors.failedToParseSearchCriteria()));

            return;
        }
    } else {
        //get advanced membership search and break down the postcode to outer and inner
        query = membershipQuery.param();
        postcodeDetails = PostcodeSearchHelper.getPostcodeDetails(query.outPostcode());
        query.outPostcode(postcodeDetails.outPostcode);
        query.inPostcode(postcodeDetails.inPostcode);
        if (query.membershipNumber() && /(^|\b)\s?(AA)?\d{7}($|\b)/.test(query.membershipNumber())) {
            query.policyId(query.membershipNumber());
        }
        query.membershipNumber(membershipSearchHelper.getMembershipNumberDetails(query.policyId() !== null ? query.policyId() : query.membershipNumber(), customerGroup).formatted);
        if (customerGroup && customerGroup.isPersonal()) {
            query.customerGroupCode(null); //clear this for BCAS search
            customerGroup = null;
        }
    }
    searchService
        .search(query, customerGroup)
        .then(function searchSuccessHandler(entitlements) {
            res.status(200).json(entitlements);
        })
        .catch(function searchErrorHandler(err) {
            res.status(500).send(JSON.stringify(err));
        });
}

function getCompanyById(req, res) {
    companyService
        .getByCompanyId(req.params.companyId)
        .then(function findByCompanyIdSuccessHandler(company) {
            res.status(200).json(company);
        })
        .catch(function findByCompanyIdErrorHandler(err) {
            res.status(500).send(JSON.stringify(err));
        });
}

/**
 * Post request endpoint for searching for telematics entitlements
 * @param {Express.Request} req request containing the membership query
 * @param {string} req.regNo vehicle registration to search for
 * @param {Express.Response} res response
 */
function getTelematicsEntitlements(req, res) {
    if (!req.body.regNo) {
        logger.info('SearchController.getTelematicsEntitlements :: missing membershipSearch query object');
        return res.status(400).json({ 'Bad Request': 'required parameter was missing' });
    }

    const regNo = req.body.regNo;

    cacheService
        .getOrSet(regNo, telematicsSearchService.search.bind(telematicsSearchService, regNo))
        .then((result) => {
            if (!result.results.length) {
                return res.status(404).json({ msg: 'Search criteria cannot be resolved' });
            } else {
                return res.status(200).json(result);
            }
        })
        .catch((err) => {
            logger.info('SearchController.getTelematicsEntitlements :: Error Occurred', err);
            return res.status(500).json(err.message ? err.message : err);
        });
}

/**
 * Post request endpoint for handling company slv search
 * @param req request containing the companyId and the slvType parameters
 * @param res response
 */
function getEntitlementsByCompanyIdAndSlvType(req, res) {
    companyService
        .getEntitlementsByCompanyIdAndSlvType(req.params.companyId, req.params.slvType, req.query.page)
        .then(function getEntitlementsByCompanyIdAndSlvTypeSuccess(entitlements) {
            res.status(200).json(entitlements);
        })
        .catch(function getEntitlementsByCompanyIdAndSlvTypeErrorHandler(err) {
            logger.error(err);
            res.status(500).send(JSON.stringify(err));
        });
}

/**
 * @private
 * @param {string} settingName
 * @param {*} config Configuration object with properties containing settings
 */
function throwErrorIfConfigSettingMissing(settingName, config) {
    // TODO: Look to move this into a common config handling module?

    var errorMessage;

    if (!_.has(config, settingName)) {
        errorMessage = 'The required configuration setting "' + settingName + '" is missing';

        logger.error(errorMessage);
        logger.log(config);

        throw new Error(errorMessage);
    }
}

/**
 * look for a specific BCAS entitlement using schedule cr details
 * @param  {[type]} req [description]
 * @param  {[type]} res [description]
 * @return {[type]}     [description]
 */
function scheduleB2B(req, res) {
    companyService
        .scheduleB2B(req.body.scheduleB2B)
        .then(function getScheduleB2BSuccess(companies) {
            res.status(200).json(companies);
        })
        .catch(function getScheduleB2BErrorHandler(err) {
            res.status(500).send(JSON.stringify(err));
        });
}

/**
 * Get the entitlement by a given customer request.
 * @param  {[type]} req [description]
 * @param  {[type]} res [description]
 * @return {Entitlement[]} Returns an entitlement array containing the given entitlement
 */
function getEntitlementByCustomerRequest(req, res) {
    var cr = new CustomerRequest(req.body.cr);
    var vehicleReg = req.body.vehicleReg;
    if (cr.contractKey().indexOf('ZZZ') > -1 && cr.isCompleted()) {
        seAaHelpDataRepo
            .find(cr.contractKey())
            .then(function searchSuccessHandler(seAaHelp) {
                if (seAaHelp && seAaHelp.length) {
                    var entitlement = entitlementFactory.createNonValidationEntitlements(cr, seAaHelp[0]);
                    carHireEntitlementFactory.processEntitlement(cr.id(), entitlement); // extract business rules
                    res.status(200).json({
                        unListedSlvEntitlement: null,
                        entitlements: [entitlement.toJSON()]
                    });
                } else {
                    logger.error('Failed to find SeAaHelp data for contactKey=' + cr.contractKey());
                    res.status(500).json('Failed to identify entitlement');
                }
            })
            .catch(function searchErrorHandler(err) {
                logger.error(err);
                res.status(500).json(err.message);
            });
    } else {
        customerRequestEntitlementService
            .search(cr, vehicleReg)
            .then(function getEntitlementByCustomerRequest(entitlements) {
                carHireEntitlementFactory.processEntitlements(cr.id(), entitlements); // extract business rules
                res.status(200).json(entitlements);
            })
            .catch(function getEntitlementByCustomerRequestErrorHandler(err) {
                res.status(500).send(JSON.stringify(err));
            });
    }
}

/**
 * Find an entilement using a fuzzy search criteria. returns a single entitlement or none.
 * @param  {[type]} req [description]
 * @param  {[type]} res [description]
 * @return {Entitlement | null} Entitlement or null if none could be matched
 */
function fuzzyEntitlementSearch(req, resp) {
    logger.info(`SearchController.fuzzyEntitlementSearch :: payload: ${JSON.stringify(req.body)}`);
    var query = new FuzzyEntitlementSearchRequest(req.body.validate),
        customerGroup = refDataRepo.customerGroups().findSyncAndClone(query.customerGroupCode()),
        memberRole;

    searchService
        .fuzzySearch(query, customerGroup, memberRole)
        .then(function validateSuccess({ entitlement, searchError }) {
            resp.status(200).send({ entitlement, searchError });
        })
        .catch(function validateError(err) {
            resp.status(500).send(JSON.stringify(err));
        });
}

/**
 * Find an entilement by regNo. returns a single entitlement or none.
 * @param  {[type]} req [description]
 * @param  {[type]} res [description]
 * @return {Entitlement | null} Entitlement or null if none could be matched
 */
function vehicleRegEntitlementSearch(req, resp) {
    var vehicleReg = req.body.vehicleReg,
        customerGroup = req.body.customerGroupCode,
        memberRole;

    searchService
        .vehicleRegSearch(vehicleReg, customerGroup, memberRole)
        .then(function validateSuccess(entitlement) {
            //if(process.env.AAH_ENVIRONMENT !== 'live'){}
            resp.status(200).json({ entitlement });
        })
        .catch(function validateError(err) {
            resp.status(500).send(JSON.stringify(err));
        });
}

function postcodeEntitlementSearch(req, resp) {
    logger.info('SearchController.postcodeEntitlementSearch :: receive request', JSON.stringify(req.body));

    var postcode = req.body.postcode,
        surname = req.body.surname,
        firstName = req.body.firstName,
        title = req.body.title,
        customerGroupCode = req.body.customerGroupCode;

    searchService
        .postcodeSearch({ postcode, surname, firstName, title }, customerGroupCode)
        .then(function validateSuccess(result) {
            resp.status(200).json(result);
        })
        .catch(function validateError(err) {
            logger.info('SearchController.postcodeEntitlementSearch :: query failed', JSON.stringify(err));
            resp.status(500).send(JSON.stringify(err));
        });
}

// ID&V changes: request to have dob replacing title

function postcodeEntitlementSearchV2(req, resp) {
    logger.info('SearchController.postcodeEntitlementSearchV2 :: receive request', JSON.stringify(req.body));

    var postcode = req.body.postcode,
        surname = req.body.surname,
        firstName = req.body.firstName,
        dob = req.body.dob,
        customerGroupCode = req.body.customerGroupCode;

    searchService
        .postcodeSearchV2({ postcode, surname, firstName, dob }, customerGroupCode)
        .then(function validateSuccess(result) {
            resp.status(200).json(result);
        })
        .catch(function validateError(err) {
            logger.info('SearchController.postcodeEntitlementSearchV2 :: query failed', JSON.stringify(err));
            resp.status(500).send(JSON.stringify(err));
        });
}

/**
 * Find an entilement BCASP.
 * @param  {[type]} req [description]
 * @param  {[type]} res [description]
 * @return {Entitlement | null} Entitlement or null if none could be matched
 */
function bcaspEntitlementSearch(req, resp) {
    var query = new FuzzyEntitlementSearchRequest(req.body.validate),
        customerGroup = refDataRepo.customerGroups().findSyncAndClone(query.customerGroupCode());

    searchService
        .searchByBcasp(query.bcasp(), customerGroup.code())
        .then(function validateSuccess(entitlement) {
            resp.status(200).send(
                JSON.stringify({
                    entitlement: entitlement ? entitlement.toJSON() : null
                })
            );
        })
        .catch(function validateError(err) {
            resp.status(500).send(JSON.stringify(err));
        });
}

function getProductsByCustomerGroupCode(req, resp) {
    var custGroupCode = req.body.customerGroupCode;
    var BCASP = req.body.policyNumber;

    companyService.findByCustomerGroupCode(custGroupCode).then(async (companies) => {
        if (companies) {
            searchService
                .searchByBcasp(BCASP, custGroupCode)
                .then(function validateSuccess(entitlement) {
                    const products = entitlement ? entitlement.products() : [];
                    resp.status(200).send({ products });
                })
                .catch(function validateError(err) {
                    logger.error(err);
                    resp.status(500).send(JSON.stringify(err));
                });
        } else {
            resp.status(404).send('Company not found');
        }
    });
}

function getPoliciesByCustomerGroupCode(req, resp) {
    var custGroupCode = req.body.customerGroupCode;
    try {
        logger.info(`SearchController.getEntitlementsByCustomerGroupCode :: customerGroupCode: ${custGroupCode}`);
        companyService.findByCustomerGroupCode(custGroupCode).then(async (companies) => {
            logger.info(`SearchController.getEntitlementsByCustomerGroupCode :: companies: ${JSON.stringify(companies)}`);
            if (companies) {
                // don't forget: seLocator is:
                // entitlement.policy().contractKey() +
                // entitlement.policy().customerKey() +
                // vrn;
                const entitlements = companies.map((company) => {
                    const unlistedSlvEntitlement = company.unlistedSlvEntitlement();
                    const policy = unlistedSlvEntitlement.policy();
                    const products = unlistedSlvEntitlement.products();
                    if (!policy || !products) {
                        return null;
                    }
                    return {
                        policyNumber: policy.policyNumber(),
                        //customerKey: policy.customerKey(),//these seem to be redundant since they're the same as the BCASP number?? Why?
                        //contractKey: policy.contractKey(),
                        productName: products[0].name()
                    };
                });

                resp.status(200).send(entitlements.filter(Boolean));
            } else {
                resp.status(404).send('Company not found');
            }
        });
    } catch (err) {
        logger.error(err);
        resp.status(500).send(JSON.stringify(err));
    }
}

function prepareBusinessRulesFromTaskId(taskId, cRTasks, source) {
    return taskService
        .readTask(taskId)
        .then((task) => {
            if (!task.entitlement && !task.entitlement.customerGroup.code) {
                const error = `Customer group missing for task : ${taskId}`;
                return { error: error };
            } else {
                let businessRules = Object.assign({}, entBusinessRules[task.entitlement.customerGroup.code]),
                    customerRequestId = task.customerRequestId;

                if (_.isEmpty(businessRules)) {
                    return { error: 'No Bussiness Rule found' };
                }

                if (source !== 'aah2') {
                    if (!businessRules.canPatrolBook) {
                        setCarHireEligibility(businessRules, `Patrols are unable to book hire cars for this customer group, please contact the indoor team.`);
                        return businessRules;
                    }
                }

                // fairPlay 26 means exhausted
                if (task.entitlement && task.entitlement.fairPlay) {
                    businessRules.fairPlay = task.entitlement.fairPlay === 26 ? true : false;
                } else {
                    businessRules.fairPlay = null;
                }

                //OH and OOH check
                if (officeHourHelper.isNowOfficeHour()) {
                    businessRules.allowedOfficeHoursSupport = businessRules.officeHoursSupport ? true : false;
                } else {
                    businessRules.allowedOfficeHoursSupport = null;
                }
                if (!officeHourHelper.isNowOfficeHour()) {
                    businessRules.allowOutOfOfficeHoursSupport = !businessRules.outOfOfficeHoursSupport ? false : true;
                } else {
                    businessRules.allowOutOfOfficeHoursSupport = null;
                }

                // Dealer Codes for PORSHE group to be used in AAH2/EVALITE
                if (task.entitlement.customerGroup.code === 'POR') {
                    businessRules.dealers = Object.assign([], dealerCodes['POR']);
                }

                if (businessRules.followRecoveryRequired) {
                    if (task.recovery.follow) {
                        return businessRules;
                    }
                    //Getting tasks from EVALITE flow, so dont need to fetch it again
                    if (!cRTasks) {
                        return taskService.fetchTasksByCustReqId(customerRequestId).then((data) => {
                            return hasRecoveryTask(data)
                                ? businessRules
                                : setCarHireEligibility(businessRules, 'Follow or Recovery job is required to provide a car hire. Please create one or refer to PSU for further assistance');
                        });
                    }
                    return hasRecoveryTask(cRTasks)
                        ? businessRules
                        : setCarHireEligibility(businessRules, 'Follow or Recovery job is required to provide a car hire. Please create one or refer to PSU for further assistance');
                }

                if (businessRules.faultRestrictions) {
                    let { faultRestrictions } = businessRules;
                    let faultsHavingPeriod = ['misfuel', 'rtc', 'punctureFault'];

                    for (var faultProp in faultRestrictions) {
                        const faultCodeMap = getFaultCodes(task.entitlement.customerGroup.code, faultProp);

                        if (!faultRestrictions[faultProp] || (faultsHavingPeriod.includes(faultProp) && !faultRestrictions[faultProp]['allowed'])) {
                            if (
                                faultCodeMap &&
                                faultCodeMap['VEHICLE_FAULT_CODE'] &&
                                faultCodeMap['VEHICLE_FAULT_CODE'].length > 0 &&
                                faultCodeMap['VEHICLE_FAULT_CODE'].includes(task.fault.code.code)
                            ) {
                                const faultMsg = `Car hire is not allowed for the breakdown fault - '${faultCodeMap['BCAS_FAULT_NAME']}'`;
                                const callMsg = `please call PSU/Oldbury to proceed with car hire booking.`;
                                const msg = source === 'aah2' ? faultMsg : faultMsg + ', ' + callMsg;

                                setCarHireEligibility(businessRules, msg);
                                return businessRules;
                            }
                        } else if (faultsHavingPeriod.includes(faultProp) && faultRestrictions[faultProp]['allowed'] && businessRules.faultRestrictions[faultProp].period) {
                            if (faultCodeMap && faultCodeMap['VEHICLE_FAULT_CODE'] && faultCodeMap['VEHICLE_FAULT_CODE'].length && faultCodeMap['VEHICLE_FAULT_CODE'].includes(task.fault.code.code)) {
                                businessRules.initialHireDays = businessRules.faultRestrictions[faultProp].period.value;
                                businessRules.initialHireDaysType = businessRules.faultRestrictions[faultProp].period.unit;
                            }
                        }
                    }
                }

                return businessRules;
            }
        })
        .catch((err) => {
            return { error: err };
        });
}

function hasRecoveryTask(tasks) {
    return tasks.filter((task) => task.createReason.id === 8 || task.createReason.id === 29).length > 0 ? true : false;
}

function setCarHireEligibility(rules, message) {
    //carHireEligibility - this property is added only when car hire not allowed. AAH2/EVALITE can use this property
    rules.carHireEligibility = {
        status: false,
        message
    };
    return rules;
}

/**
 * Post request endpoint for fetching business rules
 * @param req request containing the do to perform the search on like custgroup, taskId, memebership or contract info
 * @param res response
 */
function getBusinessRules(req, res) {
    //In future this can be exteded to accept memebership details and contract key to search for business rules
    //No need source flag once business rules are getting from RES.
    const { customerGroupCode, taskId, customerReqId, source } = req.body;

    if (!(customerGroupCode || taskId || customerReqId)) {
        logger.error(`SearchController.getBusinessRules error :: paramaters are missing/Invalid`);
        res.status(500).send('Business rule search paramaters are missing/Invalid');
        return;
    }

    logger.info(`SearchController.getBusinessRules :: parameters - customerGroupCode : taskId : customerReqId :: ${customerGroupCode} : ${taskId} : ${customerReqId}`);
    /**
     * This is new implementation for getting rule
     * This feature flag is maintained for managing old and new flows
     * carHireResEntitlementFlag - true => getting rules from res
     * carHireResEntitlementFlag - false => use rules from enum file
     */
    if (businessRulesSvc.enableCarHireResEntitlment()) {
        businessRulesSvc
            .getRules(req.body)
            .then((rules) => {
                if (rules.error) {
                    res.status(500).send(rules.error);
                    return;
                }
                res.status(200).send(rules);
            })
            .catch((err) => {
                res.status(200).send(err);
            });
    } else {
        /**
         * Remove this else part when rules data is coming from RES
         */
        if (customerGroupCode) {
            res.status(200).send(entBusinessRules[customerGroupCode]);
        } else if (customerReqId) {
            // Evalite :: redirect end point to hire-entitlement-service
            taskService
                .getRules(customerReqId)
                .then((bussinessRules) => {
                    res.status(200).send(bussinessRules);
                })
                .catch((err) => {
                    res.status(500).send(err);
                });

            //OLD-FLOW
            /*
            taskService.fetchTasksByCustReqId(customerReqId).then((tasks) => {
                const statuses = [Task.PLAN_STATUS, Task.GDET_STATUS, Task.HEAD_STATUS, Task.HIRE_STATUS, Task.GARR_STATUS];
                let allowHire = tasks.filter(task => (task.taskType.code === "ADM" && statuses.includes(task.status))).length > 0 ? false : true;

                if (!taskId) {
                    res.status(500).send("Task Id required with Customer request Id");
                    return;
                }

                prepareBusinessRulesFromTaskId(taskId, tasks, source).then((bussinessRules) => {
                    if (bussinessRules.error) {
                        res.status(500).send(bussinessRules.error);
                        return;
                    }
                    bussinessRules.allowHire = allowHire;
                    res.status(200).send(bussinessRules);
                });
            }).catch((err) => {
                res.status(200).send(err);
            }); */
        } else if (taskId) {
            prepareBusinessRulesFromTaskId(taskId)
                .then((bussinessRulesByTask) => {
                    if (bussinessRulesByTask.error) {
                        res.status(500).send(bussinessRulesByTask.error);
                        return;
                    }
                    if (bussinessRulesByTask.hasOwnProperty('allowHire')) {
                        delete bussinessRulesByTask.allowHire;
                    }
                    res.status(200).send(bussinessRulesByTask);
                })
                .catch((err) => {
                    res.status(200).send(err);
                });
        }
    }
}
/**
 * Post request endpoint for fetching fault code mappings
 * @param req request containing the do to perform the search on like custgroup, fault
 * @param res response
 */
function getFaultCodeMapping(req, res) {
    //In future this can be exteded to accept memebership details and contract key to search for business rules
    //No need source flag once business rules are getting from RES.
    const { fault, customerGroupCode } = req.body;

    if (!(customerGroupCode || fault)) {
        logger.error(`SearchController.getFaultCodeMapping error :: paramaters are missing/Invalid`);
        res.status(500).send('Search paramaters are missing/Invalid');
        return;
    }

    logger.info(`SearchController.getFaultCodeMapping :: parameters - customerGroupCode : fault :: ${customerGroupCode} : ${fault}`);

    const faultCodeMappings = getFaultCodes(customerGroupCode, fault)['VEHICLE_FAULT_CODE'];

    return res.status(200).send(faultCodeMappings ? faultCodeMappings : []);
}

/*
 * Get business rules based on customerReqId and customerGroupCode
 * @param {*} req
 * @param {*} res
 * @returns {object} business rules object.
 */
function businessRules(req, res) {
    businessRulesSvc
        .fetchRules(req.body)
        .then((rules) => {
            res.status(200).send(rules);
        })
        .catch((err) => {
            res.status(500).send(err);
        });
}

/**
 * Get Cached RES business rules only based on customerReqId
 * @param {*} req
 * @param {*} res
 * @returns {object} business rules object.
 */
function getCachedResBusinessRules(req, res) {
    businessRulesSvc
        .getCachedResBusinessRules(req.body)
        .then((rules) => {
            res.status(200).send(rules);
        })
        .catch((err) => {
            res.status(500).send(err);
        });
}
