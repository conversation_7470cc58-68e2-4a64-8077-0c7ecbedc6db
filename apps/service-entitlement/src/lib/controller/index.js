'use strict';

const logger = require('winston'),
    // controllers
    MobileAppSearchController = require('./mobile-app-search.controller'),
    EligibilityController = require('./eligibility.controller'),
    RefDataController = require('./ref-data.controller'),
    SearchController = require('./search.controller'),
    TaskCliController = require('./task-cli.controller'),
    SeAahelpController = require('./se-aahelp-controller'),
    CliEntitlementController = require('./cli-entitlement.controller'),
    OverseasEntitlementController = require('./overseas-entitlement.controller'),
    // repos
    EligibilityRepository = require('../repository/eligibility.repository'),
    TaskCliRepository = require('../repository/task-cli.repository'),
    SeAahelpRepository = require('../repository/se-aahelp.repository'),
    CacheService = require('@aa/utilities/aa-utilities').CacheService,
    // services
    CompanySearchService = require('../services/company-search.service'),
    CustomerRequestEntilementSearchService = require('../services/customer-request-entitlement-search.service'),
    MobileAppSearch = require('../services/mobile-app-search.service').MobileAppSearch,
    SearchService = require('../services/search.service'),
    TelematicsSearchService = require('../services/telematics-search.service'),
    CliEntitlementService = require('../services/cli-entitlement.service'),
    OverseasEntitlementService = require('../services/overseas-entitlement.service');

module.exports = {
    /**
     * [init description]
     * @param  {Object} config defines configuration
     * @param {Object} config.res res endpoint defintion
     * @param {Object} config.aahelpRepo referrence to ref data
     * @param  {Router} router express router
     * @return {[type]}        [description]
     */
    init: function init(config, router) {
        logger.info('init service entitlement controller');

        // init controllers
        EligibilityController.init(new EligibilityRepository(config.aahelpRepo));
        MobileAppSearchController.init(new MobileAppSearch(config.mobileAppSearch));
        RefDataController.init(config.aahelpRepo);
        SeAahelpController.init(new SeAahelpRepository(config.aahelpRepo));

        SearchController.init(
            new CompanySearchService(config),
            new CustomerRequestEntilementSearchService(config),
            new SearchService(config),
            new TelematicsSearchService(config),
            config.aahelpRepo,
            new SeAahelpRepository(config.aahelpRepo),
            new CacheService(config.cache)
        );

        TaskCliController.init(new TaskCliRepository(config.aahelpRepo));

        CliEntitlementController.init(new CliEntitlementService(config));
        OverseasEntitlementController.init(new OverseasEntitlementService(config));
        //
        //  setup routes
        //

        // look for company
        router.get('/company/:companyId', SearchController.getCompanyById);

        // find task by cli -- no idea why this is here ..
        router.get('/cli/search/:cli', TaskCliController.search);

        // find entitlement by cli
        router.get('/search/cli', CliEntitlementController.search);

        // create eligibility record
        router.post('/eligibility', EligibilityController.createEligibility);

        // get telematics entitlements by vrn
        router.post('/search/telematics', SearchController.getTelematicsEntitlements);

        // get company entitlement by id and slvType
        router.get('/entitlements/:companyId/slv/:slvType', SearchController.getEntitlementsByCompanyIdAndSlvType);

        // search entitlement is registered with an iOS or Android app
        router.get('/mobileApp/search', MobileAppSearchController.search);

        // core entitlement search
        router.post('/search', SearchController.search);

        router.post('/fuzzySearch', SearchController.fuzzyEntitlementSearch);
        router.post('/bcaspSearch', SearchController.bcaspEntitlementSearch);
        router.post('/vehicleRegSearch', SearchController.vehicleRegEntitlementSearch);
        router.post('/postcodeSearch', SearchController.postcodeEntitlementSearch);
        // ID&V change - versioning of API
        router.post('/v2/postcodeSearch', SearchController.postcodeEntitlementSearchV2);

        // entitlement search by customer request
        router.post('/search/cr', SearchController.getEntitlementByCustomerRequest);

        router.post('/search/scheduleB2B', SearchController.scheduleB2B);

        // magic of javascript
        router.get('/ref/:dataSet', RefDataController.find);

        router.get('/seaahelp/:id', SeAahelpController.find);

        router.get('/healthCheck', (req, res) => {
            res.send('OK');
        });

        // fetch business rules
        router.post('/getBusinessRules', SearchController.fetchBusinessRules);

        router.post('/getFaultCodeMapping', SearchController.getFaultCodeMapping); //TODO: Will be moved to hire-entitlemnt service in 137
        router.post('/businessRules', SearchController.businessRules); // new API call for aa-hire-entitlement-service

        router.post('/getCachedBusinessRules', SearchController.getCachedResBusinessRules);

        router.post('/getOverseasEntitlement', OverseasEntitlementController.getOverseasEntitlement);

        router.post(
            '/getPoliciesByCustomerGroupCode', //temporary endpoint name. Will be changed to something more meaningful
            SearchController.getPoliciesByCustomerGroupCode
        );

        router.post(
            '/getProductsByCustomerGroupCode', //temporary endpoint name. Will be changed to something more meaningful
            SearchController.getProductsByCustomerGroupCode
        );
    }
};
