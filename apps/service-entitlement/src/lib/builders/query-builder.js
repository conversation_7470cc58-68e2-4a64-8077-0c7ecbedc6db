'use strict';

//helpers
var membershipSearchHelper = require('../helpers/membership-search-helper');
var postcodeSearchHelper = require('../helpers/postcode-search-helper');
var vehicleRegSearchHelper = require('../helpers/vehicle-registration-search-helper');
var vinSearchHelper = require('../helpers/vin-search-helper');
var unqualifiedTextSearchHelper = require('../helpers/unqualified-text-search-helper');

//models
var AdvancedMembershipQuery = require('@aa/malstrom-models/lib/advanced-membership-query.model');

/**
 * Constructs AdvancedMembershipQuery object from the supplied search string
 * @param {string} inputString the search string
 * @param {CustomerGroup} customerGroup
 * @returns {AdvancedMembershipQuery} object containing query params
 */
function build(inputString, customerGroup) {
    var vehicleRegistrationNumber = null;
    var recheckVehicleRegistrationNumber = null;
    var vehicleIdentificationNumber = null;
    var surname = null;
    var town = null;
    var unqualifiedSearchValues = null;
    var postcodeDetails = null;
    var membershipNumberDetails = null;
    var policyIdDetails = null;
    var originalInput = inputString;

    if (inputString) {
        postcodeDetails = postcodeSearchHelper.getPostcodeDetails(inputString);
        inputString = postcodeDetails.outPostcode ? inputString.replace(postcodeDetails.outPostcode, '') : inputString;
        inputString = postcodeDetails.inPostcode ? inputString.replace(postcodeDetails.inPostcode, '') : inputString;

        membershipNumberDetails = membershipSearchHelper.getMembershipNumberDetails(inputString, customerGroup);
        inputString = membershipNumberDetails ? inputString.replace(membershipNumberDetails.original, '') : inputString;

        vehicleIdentificationNumber = vinSearchHelper.getVehicleIdentificationNumber(inputString);
        inputString = vehicleIdentificationNumber ? inputString.replace(vehicleIdentificationNumber, '') : inputString;

        vehicleRegistrationNumber = vehicleRegSearchHelper.getVehicleRegistrationNumber(inputString);
        recheckVehicleRegistrationNumber = vehicleRegSearchHelper.recheckVehicleRegistrationNumber(inputString);
        inputString = vehicleRegistrationNumber ? inputString.replace(vehicleRegistrationNumber, '') : inputString;

        unqualifiedSearchValues = unqualifiedTextSearchHelper.getUnqualifiedValues(inputString);
        surname = unqualifiedSearchValues.surname;
        town = unqualifiedSearchValues.town;
    }

    return new AdvancedMembershipQuery({
        inPostcode: postcodeDetails ? postcodeDetails.inPostcode : null,
        outPostcode: postcodeDetails ? postcodeDetails.outPostcode : null,
        membershipNumber: membershipNumberDetails ? membershipNumberDetails.formatted : null,
        policyId: policyIdDetails ? policyIdDetails.original : null,
        vehicleRegistrationNumber: vehicleRegistrationNumber,
        recheckVehicleRegistrationNumber: recheckVehicleRegistrationNumber,
        vehicleIdentificationNumber: vehicleIdentificationNumber,
        surname: surname,
        town: town,
        customerGroupCode: customerGroup ? customerGroup.code() : null,
        original: originalInput
    });
}

module.exports = {
    build: build
};
