const { MongoClient } = require('mongodb');
const svc = {};
svc.connectDB = connectDB.bind(svc);
svc.getCollection = getCollection.bind(svc);
svc.getCollectionData = getCollectionData.bind(svc);
svc.storeCollectionData = storeCollectionData.bind(svc);
svc.dropCollectionData = dropCollectionData.bind(svc);
svc.dropCollectionDataByIds = dropCollectionDataByIds.bind(svc);
module.exports = svc;
let client = null;
let logger = require('winston');
async function connectDB({ mongoUrl, dbName }) {
    try {
        mongoUrl = mongoUrl || process.env.mongodbUrl || 'mongodb+srv://aah2intAtlas:<EMAIL>';
        dbName = dbName || 'cache';

        client =
            client ||
            (await MongoClient.connect(mongoUrl, {
                useNewUrlParser: true
            }));
        return client.db(dbName);
    } catch (err) {
        logger.error(`Mongodb client connection failed with error : ${err}`);
    }
}

async function getCollection({ mongoUrl, dbName, collectionName }) {
    const db = await connectDB({ mongoUrl, dbName });
    return db.collection(collectionName);
}

async function getCollectionData({ dbName, collectionName, filter }) {
    filter = filter || {};
    try {
        const collection = await getCollection({ dbName, collectionName });
        return collection.find(filter).toArray();
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}

async function storeCollectionData({ collectionName, data }) {
    try {
        const collection = await getCollection({ collectionName });
        return await collection.insertMany(data);
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}

async function dropCollectionData({ collectionName }) {
    try {
        const collections = await listCollections();
        if (collections.includes(collectionName)) {
            const collection = await getCollection({ collectionName });
            await collection.drop();
        }
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}

async function dropCollectionDataByIds({ collectionName, ids }) {
    try {
        const collections = await listCollections();
        if (collections.includes(collectionName)) {
            const collection = await getCollection({ collectionName });
            collection.deleteMany({ id: { $in: ids } });
        }
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}

async function listCollections() {
    try {
        const db = await connectDB({});
        return (await db.listCollections().toArray()).map((collection) => collection.name);
    } catch (err) {
        throw { message: err.message, stack: err.stack };
    }
}
