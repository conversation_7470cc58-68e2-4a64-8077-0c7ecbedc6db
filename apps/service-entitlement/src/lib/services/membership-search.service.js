'use strict';
var _ = require('lodash'),
    errors = require('../constants/error.constants'),
    logger = require('winston'),
    EntitlementFactory = require('../factories/entitlement-factory'),
    postcodeSearchHelper = require('../helpers/postcode-search-helper'),
    ResClient = require('../client/res.client'),
    SortService = require('./sort.service'),
    Q = require('q');

module.exports = function MembershipSearchService(config) {
    var _svc = this,
        _resClient = null,
        _sagaTextTopics = []; //null;

    if (config.res) {
        _resClient = new ResClient(config.res);
    }

    if (config.aahelpRepo) {
        _sagaTextTopics = config.aahelpRepo.sagaTextTopic().findSync();
    }

    _.extend(_svc, {
        resClient: function entitlementSearchClientAccessor(val) {
            return arguments.length ? (_resClient = val) : _resClient;
        },
        /**
         * find by membership id
         * @param  {string} membershipNo      AA membership no
         * @param  {string} customerGroupCode represents the customer group
         * @param  {number} page              paged result set
         * @param  {number} pageSize          number of results per page
         * @return {Promise}
         */
        findByMembershipId: function findByMembershipId(membershipNo, customerGroupCode, page, pageSize) {
            if (!membershipNo) {
                return Q.reject(errors.failedToFindMembershipSearchCriteria());
            } else {
                return _resClient
                    .membership(membershipNo, customerGroupCode, null)
                    .then(function membershipSearchSuccessHandler(data) {
                        var entitlements = EntitlementFactory.createEntitlements(data, _sagaTextTopics);
                        return {
                            entitlements: SortService.sortCustomerEntitlements(entitlements),
                            nextPage: data.nextPage || 0
                        };
                    })
                    .catch(function membershipSearchErrorHandler(err) {
                        logger.error('MembershipSearchService.findByMembershipId :: error processing response ::', err);
                        return Q.reject(err);
                    });
            }
        },
        /**
         * find entitlement by person details - mandetory fields are post code or town and surname
         * @param  {string} outPostcode       out postcode of the entitlement billing address
         * @param  {string} inPostcode        in postcode onf the entitlement billing address
         * @param  {string} surname           of entitlement holder
         * @param  {string} initials          of entitlement holder - optional
         * @param  {string} town              of billing address on the account - optional
         * @param  {number} page              set of results to return starts 0
         * @param  {number} pageSize          number of results per page
         * @param  {string} customerGroupCode
         * @return {Promise}                  [description]
         */
        findByPerson: function findByPerson(outPostcode, inPostcode, surname, initials, town, page, pageSize, customerGroupCode, supplierOwnDriverId) {
            if (customerGroupCode !== 'UBE' && (!surname || (!outPostcode && !town))) {
                return Q.reject(errors.failedToFindPersonSearchDetails());
            } else {
                return _resClient
                    .person(outPostcode, inPostcode, surname, initials, town, page, pageSize, customerGroupCode, supplierOwnDriverId)
                    .then(function membershipSearchSuccessHandler(data) {
                        var entitlements = EntitlementFactory.createEntitlements(data, _sagaTextTopics);
                        return {
                            entitlements: SortService.sortCustomerEntitlements(entitlements),
                            nextPage: data.nextPage || 0
                        };
                    })
                    .catch(function membershipSearchErrorHandler(err) {
                        logger.error('MembershipSearchService.findByPerson :: error processing response ::', err);
                        return Q.reject(err);
                    });
            }
        },
        /**
         * search for entitlement by matching postcode , surname and then filter on last 4 digits of contract key
         * @param  {string} postcode               full postcode
         * @param  {string] surname               of an individual on the membership
         * @param  {string} contractKey            contains full or partial contract key
         * @param  {string} customerGroupCode      the entitlement should be part off
         * @return {Promise}                        return promise that is resolved contains an array of [Entitlements]
         */
        last4Digits: function last4Digits(postcode, surname, contractKey, customerGroupCode) {
            var postcodeDetails = postcodeSearchHelper.getPostcodeDetails(postcode);
            return _svc.findByPerson(postcodeDetails.outPostcode, postcodeDetails.inPostcode, surname, null, null, 0, 20, customerGroupCode).then(function (results) {
                return _.filter(results.entitlements, function (item) {
                    return item.policy().membershipNumber().slice(-4) === contractKey;
                });
            });
        },
        /**
         * search for entitlement by matching postcode, firstName, surname and title
         * @param  {string} postcode               full postcode
         * @param  {string} postcode               full postcode
         * @param  {string] surname               of an individual on the membership
         * @param  {string} customerGroupCode      the entitlement should be part off
         * @return {Promise}                        return promise that is resolved contains an array of [Entitlements]
         */
        searchByPostcode: function searchByPostcode({ postcode, firstName, surname, title }, customerGroupCode) {
            var postcodeDetails = postcodeSearchHelper.getPostcodeDetails(postcode);
            const initials = firstName ? firstName.trim().charAt(0) : null;
            return _svc.findByPerson(postcodeDetails.outPostcode, postcodeDetails.inPostcode, surname, initials, null, 0, 20, customerGroupCode).then(function (results) {
                return {
                    entitlements: results.entitlements
                };
            });
        },

        searchByPostcodeV2: function searchByPostcodeV2({ postcode, firstName, surname, dob }, customerGroupCode) {
            var postcodeDetails = postcodeSearchHelper.getPostcodeDetails(postcode);
            const initials = firstName ? firstName.trim().charAt(0) : null;
            return _svc.findByPerson(postcodeDetails.outPostcode, postcodeDetails.inPostcode, surname, initials, null, 0, 20, customerGroupCode).then(function (results) {
                return {
                    entitlements: results.entitlements
                };
            });
        }
    });
};
