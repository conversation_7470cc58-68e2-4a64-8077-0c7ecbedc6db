'use strict';

var path = require('path');

// Export the config object based on the NODE_ENV
// ==============================================
module.exports = {
    env: process.env.NODE_ENV || process.env.aah2_environment || 'development',
    loadbalancer: process.env.loadbalancer || 'rh0113p:5600',

    // Root path of server
    root: path.normalize(__dirname + '/../..'),

    // Server port
    port: process.env.aahPodHttpPort || process.env.serviceEntitlementPort || 7106,
    ip: '0.0.0.0',

    resEndPoint: 'http://rh0113p:8143/res/read/rest', //uat
    //resEndPoint: 'http://aah2-res-read:8143/res/read/rest', //live

    resTimeout: 20000,
    pageSize: process.env.resPageSize || 10,
    /*
    'http://rh0113p:8143/res/read/rest',
    'http://aah2rb03:8143/res/read/rest',
    'http://aah-uat01:7004',
    'http://dkt920027:7004',

    */
    // set a default proxy here so that we can test it ..
    aahelpProxyUrl: process.env.aahelpProxyUrl || 'http://rh0113p:7815/api/prime-proxy',

    mobileAppSearchServiceEndPoint: process.env.mobileAppSearchServiceEndPoint || 'http://aa-ap488v:8080/AAHelp.asmx',
    mobileAppSearchServiceEndPointTimeout: process.env.mobileAppSearchServiceEndPointTimeout || 2000,
    apiEndPoint: process.env.aahPodEndpoint || '/api/service-entitlement',
    taskApiEndPoint: '/api/task-service',
    hireEntitlementEndPoint: '/api/hire-entitlement-service',
    cacheConnectionString: process.env.mongodbConnectionString || 'mongodb+srv://aah2Admin:<EMAIL>/?maxIdleTimeMS=500',
    cacheDbname: process.env.resTelematicsCacheDbname || 'resTelematics',
    cacheCollectionName: process.env.resCacheCollectionName || 'resTelematicsEntitlements',
    // Live check on csh config just to safegaurd we don't use default config in production
    cshUser: process.env.aah2_environment === 'live' ? process.env.cshUser : process.env.cshUser || 'vanda',
    cshPassword: process.env.aah2_environment === 'live' ? process.env.cshPassword : process.env.cshPassword || 'va.77.av',
    cshConnectStrings: process.env.aah2_environment === 'live' ? process.env.cshConnectStrings : process.env.cshConnectStrings || 'vulexa-scan:1521/AAHLPT'
};
