'use strict';

const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    SupplierDetails = require('@aa/malstrom-models/lib/supplier-details.model'),
    telephoneNumberTypeFactory = require('./telephone-number-and-type.factory'),
    formatting = require('@aa/utilities/aa-utilities').formatting;

function create(rawSupplierDetails) {
    var supplierDetails,
        address,
        telephoneAndTypes = [];

    supplierDetails = new SupplierDetails();
    address = new Address();

    supplierDetails.chequesAcceptedIndicator(rawSupplierDetails.chequesAcceptedIndicator === 'Y' ? true : false);
    supplierDetails.creditCardsAcceptedIndicator(rawSupplierDetails.creditCardsAcceptedIndicator === 'Y' ? true : false);
    supplierDetails.extraCapabilities(rawSupplierDetails.extraCapabilities);
    supplierDetails.incidentManagedIndicator(rawSupplierDetails.imgtInd === 'Y' ? true : false);
    supplierDetails.issA120FormsIndicator(rawSupplierDetails.issA120FormsIndicator === 'Y' ? true : false);
    supplierDetails.primeAgentIndicator(rawSupplierDetails.primeAgentIndicator === 'Y' ? true : false);
    supplierDetails.relayPlusVoucherIndicator(rawSupplierDetails.relayPlusVoucherIndicator === 'Y' ? true : false);
    supplierDetails.remarks(rawSupplierDetails.remarks);
    supplierDetails.contactName(rawSupplierDetails.supContactName);

    if (rawSupplierDetails.resourceTelTypeNo1) {
        telephoneAndTypes.push(telephoneNumberTypeFactory.create(rawSupplierDetails.resourceTelNo1, rawSupplierDetails.resourceTelTypeNo1));
    }
    if (rawSupplierDetails.resourceTelTypeNo2) {
        telephoneAndTypes.push(telephoneNumberTypeFactory.create(rawSupplierDetails.resourceTelNo2, rawSupplierDetails.resourceTelTypeNo2));
    }
    if (rawSupplierDetails.resourceTelTypeNo3) {
        telephoneAndTypes.push(telephoneNumberTypeFactory.create(rawSupplierDetails.resourceTelNo3, rawSupplierDetails.resourceTelTypeNo3));
    }
    if (rawSupplierDetails.resourceTelTypeNo4) {
        telephoneAndTypes.push(telephoneNumberTypeFactory.create(rawSupplierDetails.resourceTelNo4, rawSupplierDetails.resourceTelTypeNo4));
    }
    if (rawSupplierDetails.resourceTelTypeNo5) {
        telephoneAndTypes.push(telephoneNumberTypeFactory.create(rawSupplierDetails.resourceTelNo5, rawSupplierDetails.resourceTelTypeNo5));
    }
    supplierDetails.resourceTelephoneNumbersAndType(telephoneAndTypes);

    address.postcode(rawSupplierDetails.address.postcode);

    _.forEach(rawSupplierDetails.address.addressLines, function (addressLine) {
        address.addressLines().push(formatting.toTitleCase(addressLine));
    });

    supplierDetails.address(address);

    return supplierDetails;
}

module.exports = {
    create: create
};
