const logger = require('winston'),
    oracledb = require('oracledb'),
    OracleTsnFactory = require('./oracle-tsn.factory');

class OracleDataSource {
    _logger = logger;
    _config = {};
    _isActive = false;

    constructor(config) {
        const { connectString } = config;
        this.validate(config);
        this._config = config;
        logger.info(`OracleDataSource.constructor : ${this._config.poolAlias} user ${this._config.user} connectString ${this._config.connectString}`);
        // replace connect string with service name
        this._config[`serviceName`] = OracleTsnFactory.parserConnectString(connectString);
    }

    validate(config) {
        if (!config) {
            throw new Error('config is required');
        }
        const { user, password, connectString, poolAlias } = config;
        if (!user) {
            throw new Error('user is required');
        }
        if (!password) {
            throw new Error('password is required');
        }
        if (!connectString) {
            throw new Error('connectString is required');
        }
        if (!poolAlias) {
            throw new Error('poolAlias is required');
        }
        return config;
    }

    /**
     * This method is used to convert the connection string to a service name.
     * This allows the oracle client library to automatically failover to the next address in the list
     * @param {*} sidString
     * @returns
     */
    toServiceName(sidString) {
        var addresses = [];
        const oralceRegx = /([\w|_|\.]+):(\d+)\/([\w|_|\.]+)(,([\w|_|\.]+):(\d+)\/([\w|_|\.]+))?/gm;

        const sidStringParts = oralceRegx.exec(sidString);

        if (sidStringParts === null) {
            throw new Error(`Invalid sidString : ${sidString}`);
        }

        addresses.push(`(ADDRESS=(protocol=tcp)(host=${sidStringParts[1]})(port=${sidStringParts[2]}))`);
        logger.info(`OracleDataSource.toServiceName : ${sidStringParts[1]}:${sidStringParts[2]} / ${sidStringParts[3]}`);
        if (sidStringParts.length === 8 && sidStringParts[5] && sidStringParts[6]) {
            addresses.push(`(ADDRESS=(protocol=tcp)(host=${sidStringParts[5]})(port=${sidStringParts[6]}))`);
            logger.info(`OracleDataSource.toServiceName : ${sidStringParts[5]}:${sidStringParts[6]} / ${sidStringParts[7]}`);
        }

        return `(DESCRIPTION=(FAILOVER=on)(ADDRESS_LIST=${addresses.join('')})(CONNECT_DATA=(SERVICE_NAME=${sidStringParts[3]})))`;
    }

    /**
     * tests if the connection string is a service name
     * @param {*} connectionString
     * @returns
     */
    testServiceName(connectionString) {
        const regex = /CONNECT_DATA=\(SERVICE_NAME=/gim;
        return regex.test(connectionString);
    }

    toServiceNameFromHacked(connectionString) {
        const regex = /(\(DESCRIPTION=(.+)\)),?(.+)?/gim;
        const parts = regex.exec(connectionString);

        if (Array.isArray(parts) && parts.length > 2 && this.testTnsString(parts[1])) {
            return parts[1];
        }
        return null;
    }
    /**
     * parses the connection string to a service name
     * @param {*} conectString
     * @returns string service name {DESCRIPTION=(FAILOVER=on)(ADDRESS_LIST=(ADDRESS=(protocol=tcp)(host=)(port=))(ADDRESS=(protocol=tcp)(host=)(port=)))(CONNECT_DATA=(SERVICE_NAME=)))}
     */
    parserConnectString(conectString) {
        if (this.testServiceName(conectString)) {
            return conectString;
        }

        const tnsString = factory.toServiceNameFromHacked(conectString);
        if (tnsString) {
            return tnsString;
        }

        const serviceName = this.toServiceName(conectString);
        if (this.testServiceName(serviceName)) {
            return serviceName;
        }

        throw new Error(`Invalid connection string: ${conectString} - serviceName ${serviceName}`);
    }

    async createPool(config) {
        const { user, password, serviceName, poolAlias, poolMax } = config;
        this._logger.info(`OracleDataSource.createPool : pool alias : ${poolAlias} user : ${user} `);
        this._logger.info(`OracleDataSource.createPool : pool alias : ${poolAlias} serviceName : ${serviceName}`);
        this._logger.info(`OracleDataSource.createPool : pool alias : ${poolAlias} poolMax : ${poolMax ? poolMax : 10}`);
        this._isActive = true;

        await oracledb.createPool({
            user: user,
            password: password,
            connectString: serviceName,
            poolIncrement: 1,
            poolMax: poolMax ? poolMax : 10, // max number of connections
            poolMin: 1,
            poolAlias: poolAlias
        });

        this._logger.info(`OracleDataSource.createPool : pool alias : ${poolAlias} isActive ${this._isActive}`);
    }

    async bootstrap() {
        this._logger.info(`OracleDataSource.bootstrap pool instance : ${this._config.poolAlias}`);
        if (this._isActive) {
            return;
        }
        await this.createPool(this._config);
    }

    async close() {
        if (this._isActive) {
            await oracledb.getPool(this._config.poolAlias).close();
        }
    }

    async connect() {
        if (!this._isActive) {
            throw new Error('Pool is not active ... botstrap needs to tbe called first');
        }
        this._logger.info(`OracleDataSource.connect : ${this._config.poolAlias}`);
        return await oracledb.getConnection(this._config.poolAlias);
    }
}

class OracleDataSourceFactory {
    static _instance = null;
    _logger = logger;
    _dataSources;
    constructor() {
        this._dataSources = new Map();
    }

    /**
     * helper to simply add csh data source ...
     * @param {*} config optional config object
     * @param {*} config.user user name
     * @param {*} config.password password
     * @param {*} config.connectString connection string
     * @returns Oracle
     */
    addCshDataSource(config) {
        if (this._dataSources.has('CSH')) {
            logger.info('OracleDataSourceFactory.addCshDataSource : ++++++++++++++++++++++++++++++');
            logger.info('OracleDataSourceFactory.addCshDataSource : using existing CSH data source');
            logger.info('OracleDataSourceFactory.addCshDataSource : ++++++++++++++++++++++++++++++');
            return this._dataSources.get('CSH');
        }
        logger.info('OracleDataSourceFactory.addCshDataSource : adding CSH data source');
        if (!config) {
            config = {
                user: process.env.cshUser,
                password: process.env.cshPassword,
                connectString: process.env.cshConnectStrings,
                poolAlias: 'CSH'
            };
        } else {
            config[`poolAlias`] = 'CSH'; // force pool alias to CSH
        }

        return this.addDataSource(config);
    }

    csh() {
        if (!this._dataSources.has('CSH')) {
            throw new Error('CSH data source is not configured ... you need to call addCshDataSource first');
        }
        return this._dataSources.get('CSH');
    }

    addDataSource(config) {
        const { poolAlias } = config;
        if (this._dataSources.has(poolAlias)) {
            throw new Error(`Pool alias ${poolAlias} already exists`);
        }
        const datasource = new OracleDataSource(config);
        this._dataSources.set(poolAlias, datasource);
        return datasource;
    }

    static getInstance() {
        if (OracleDataSourceFactory._instance === null) {
            OracleDataSourceFactory._instance = new OracleDataSourceFactory();
            //            logger.info('OracleDataSourceFactory getInstance creating new instance');
        }
        return OracleDataSourceFactory._instance;
    }

    static async bootstrap() {
        if (OracleDataSourceFactory._instance === null) {
            throw new Error('OracleDataSourceFactory is not initialised');
        }
        return OracleDataSourceFactory.getInstance().bootstrapDataSources();
    }
    /**
     * bootstrap all data sources
     * @returns true if all data sources are bootstrapped
     */
    async bootstrapDataSources() {
        if (OracleDataSourceFactory._instance === null) {
            throw new Error('OracleDataSourceFactory is not initialised');
        }
        this._logger.info('OracleDataSourceFactory bootstrap');
        for await (const [key, value] of this._dataSources) {
            await value.bootstrap();
        }
        return Promise.resolve(true);
    }

    /**
     * shutdown all data sources
     */
    async shutdown() {
        for await (const [key, value] of this._dataSources) {
            await value.close();
        }
    }

    /**
     * create csh oracle data source . this should be called once
     * @param {*} config
     * @returns OracleDataSource
     */
    static createCshDataSource(config) {
        return OracleDataSourceFactory.getInstance().addCshDataSource(config);
    }
}

module.exports = OracleDataSourceFactory;
