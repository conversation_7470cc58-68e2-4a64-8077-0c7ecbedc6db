const logger = require('winston');

module.exports = class OracleTsnFactory {
    constructor() {
        this._logger = logger;
    }

    toServiceName(sidString) {
        var addresses = [];
        const oralceRegx = /([\w|_|\.]+):(\d+)\/([\w|_|\.]+)(,([\w|_|\.]+):(\d+)\/([\w|_|\.]+))?/gm;

        const sidStringParts = oralceRegx.exec(sidString);

        if (sidStringParts === null) {
            throw new Error(`OracleTsnFactory.toServiceName : Invalid sidString : ${sidString}`);
        }

        addresses.push(`(ADDRESS=(protocol=tcp)(host=${sidStringParts[1]})(port=${sidStringParts[2]}))`);
        this._logger.info(`OracleTsnFactory.toServiceName : ${sidStringParts[1]}:${sidStringParts[2]} / ${sidStringParts[3]}`);
        if (sidStringParts.length === 8 && sidStringParts[5] && sidStringParts[6]) {
            addresses.push(`(ADDRESS=(protocol=tcp)(host=${sidStringParts[5]})(port=${sidStringParts[6]}))`);
            this._logger.info(`OracleTsnFactory.toServiceName : ${sidStringParts[5]}:${sidStringParts[6]} / ${sidStringParts[7]}`);
        }

        return `(DESCRIPTION=(FAILOVER=on)(ADDRESS_LIST=${addresses.join('')})(CONNECT_DATA=(SERVICE_NAME=${sidStringParts[3]})))`;
    }

    testServiceName(connectionString) {
        const regex = /CONNECT_DATA=\(SERVICE_NAME=/gim;
        return regex.test(connectionString);
    }

    testTnsString(connectionString) {
        const regex = /\(DESCRIPTION=(.+)\)/gim;
        return regex.test(connectionString);
    }

    toServiceNameFromHacked(connectionString) {
        const regex = /(\(DESCRIPTION=(.+)\)),?(.+)?/gim;
        const parts = regex.exec(connectionString);

        if (Array.isArray(parts) && parts.length > 2 && this.testTnsString(parts[1])) {
            return parts[1];
        }
        return null;
    }

    /**
     * parses the connection string to a service name
     * @param {*} conectString
     * @returns string service name {DESCRIPTION=(FAILOVER=on)(ADDRESS_LIST=(ADDRESS=(protocol=tcp)(host=)(port=))(ADDRESS=(protocol=tcp)(host=)(port=)))(CONNECT_DATA=(SERVICE_NAME=)))}
     */
    static parserConnectString(conectString) {
        const factory = new OracleTsnFactory();

        if (factory.testServiceName(conectString)) {
            return conectString;
        }

        const tnsString = factory.toServiceNameFromHacked(conectString);
        if (tnsString) {
            return tnsString;
        }

        const serviceName = factory.toServiceName(conectString);
        if (factory.testServiceName(serviceName)) {
            return serviceName;
        }

        throw new Error(`OracleTnsFactory.parserConnectString : Invalid connection string: ${conectString} - serviceName ${serviceName}`, { code: 'OTNSF-001' });
    }
};
