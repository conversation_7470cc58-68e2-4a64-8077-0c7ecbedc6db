const OracleTsnDactory = require('../../lib/data-source/oracle-tsn.factory');

describe('oracle-tsn.factory', () => {
    let conf;
    let aacsh, livecsh, aacshSingle, hackedConnectionString, fromHackedConnectionString;
    beforeAll(() => {
        conf = {
            user: `cshUser`,
            password: `process.env.cshPassword`,
            connectString: `farodadb03.theaa.local:1521/LAAHTXN_RW.theaa.local,reaodadb03.theaa.local:1521/LAAHTXN_RW.theaa.local`,
            poolAlias: 'CSH'
        };

        aacsh =
            '(DESCRIPTION=(FAILOVER=on)(ADDRESS_LIST=(ADDRESS=(protocol=tcp)(host=reaodadb04)(port=1521))(ADDRESS=(protocol=tcp)(host=farodadb04)(port=1521)))(CONNECT_DATA=(SERVICE_NAME=AAAHTXN_RW.theaa.local)))';
        aacshSingle = '(DESCRIPTION=(FAILOVER=on)(ADDRESS_LIST=(ADDRESS=(protocol=tcp)(host=reaodadb04)(port=1521)))(CONNECT_DATA=(SERVICE_NAME=AAAHTXN_RW.theaa.local)))';

        livecsh = `(DESCRIPTION=(FAILOVER=on)(ADDRESS_LIST=(ADDRESS=(protocol=tcp)(host=farodadb03.theaa.local)(port=1521))(ADDRESS=(protocol=tcp)(host=reaodadb03.theaa.local)(port=1521)))(CONNECT_DATA=(SERVICE_NAME=LAAHTXN_RW.theaa.local)))`;

        hackedConnectionString =
            '(DESCRIPTION=(CONNECT_TIMEOUT=5)(ADDRESS_LIST=(FAILOVER=ON)(ENABLE=BROKEN)(LOAD_BALANCE=FALSE)(ADDRESS=(PROTOCOL=TCP)(HOST=reaodadb04.theaa.local)(PORT=1521))(ADDRESS=(PROTOCOL=TCP)(HOST=farodadb04.theaa.local)(PORT=1521)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=VAAHTXN_RW.theaa.local))),farodadb04:1521/VAAHTXN_RW.theaa.local';
        fromHackedConnectionString =
            '(DESCRIPTION=(CONNECT_TIMEOUT=5)(ADDRESS_LIST=(FAILOVER=ON)(ENABLE=BROKEN)(LOAD_BALANCE=FALSE)(ADDRESS=(PROTOCOL=TCP)(HOST=reaodadb04.theaa.local)(PORT=1521))(ADDRESS=(PROTOCOL=TCP)(HOST=farodadb04.theaa.local)(PORT=1521)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=VAAHTXN_RW.theaa.local)))';
    });

    it(`should return live csh service name`, () => {
        expect(OracleTsnDactory.parserConnectString(conf.connectString)).toBe(livecsh);
    });

    it(`should return acceptance csh service name`, () => {
        const conectString = 'reaodadb04:1521/AAAHTXN_RW.theaa.local,farodadb04:1521/AAAHTXN_RW.theaa.local';
        expect(OracleTsnDactory.parserConnectString(conectString)).toBe(aacsh);
    });

    it(`should return acceptance - single csh service name`, () => {
        const conectString = 'reaodadb04:1521/AAAHTXN_RW.theaa.local';
        expect(OracleTsnDactory.parserConnectString(conectString)).toBe(aacshSingle);
    });

    it(`should return hacked connection string`, () => {
        expect(OracleTsnDactory.parserConnectString(hackedConnectionString)).toBe(fromHackedConnectionString);
    });
});
