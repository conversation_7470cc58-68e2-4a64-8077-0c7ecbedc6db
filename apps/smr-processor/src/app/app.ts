import { OutdoorEvents, OutdoorTaskComplete, OutdoorSMRComplete } from '@aa/data-models/common';
import { Exception } from '@aa/exception';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubCheckpoint, EventHubReceiver, EventHubReceiverConfig, EventHubSender, QueueEventHandler, ServiceBusSender, ServiceBusSenderConfig } from '@aa/queue';
import { BackendEnvironment } from '@aa/utils';

const appName = 'smr-processor';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.SMR_PROCESSOR;
    protected auxStreamReceiver: EventHubReceiver<OutdoorEvents.COMPLETE_BREAKDOWN_TASK | OutdoorEvents.COMPLETE_SMR, OutdoorTaskComplete | OutdoorSMRComplete, void>;
    protected taskCompletionSender: ServiceBusSender<OutdoorEvents.COMPLETE_BREAKDOWN_TASK, OutdoorTaskComplete>;

    protected miStramSMRSender: EventHubSender<OutdoorEvents.MI_SMR, OutdoorSMRComplete>;

    constructor(environment: BackendEnvironment) {
        super({ environment, appName });

        const checkpoint = new EventHubCheckpoint({
            accountUrl: environment.queue.storageAccountUrl || '',
            accountName: environment.queue.storageAccountName || '',
            accountKey: environment.queue.storageAccountKey || '',
            containerName: 'smr-processor',
            logger: this.logger
        });

        const ehBaseConfig: Omit<EventHubReceiverConfig, 'eventHubName' | 'consumerGroup' | 'checkpoint'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            maxBatchSize: 1, // we want to process even 1
            maxDelayPerBatch: 2 // 2s
        };

        const sbqBaseConfig: Omit<ServiceBusSenderConfig, 'queueName'> = {
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            connectionString: environment.queue.SBQConnectionString
        };

        this.auxStreamReceiver = new EventHubReceiver({
            ...ehBaseConfig,
            checkpoint,
            eventHubName: 'aux-stream',
            consumerGroup: 'smr-processor'
        });
        this.auxStreamReceiver.on(OutdoorEvents.COMPLETE_BREAKDOWN_TASK, this.onCompleteBreakdownTask);
        this.auxStreamReceiver.on(OutdoorEvents.COMPLETE_SMR, this.onCompleteSMR);

        this.taskCompletionSender = new ServiceBusSender({
            ...sbqBaseConfig,
            queueName: 'unity-queue'
        });

        // Event hub sender for MI/DAVE
        this.miStramSMRSender = new EventHubSender({
            ...ehBaseConfig,
            eventHubName: 'mi-stream'
        });
    }

    protected onCompleteBreakdownTask: QueueEventHandler<OutdoorEvents.COMPLETE_BREAKDOWN_TASK | OutdoorEvents.COMPLETE_SMR, OutdoorTaskComplete | OutdoorSMRComplete, void> = async (context) => {
        try {
            const {
                entry: { data }
            } = context;
            this.logger.info({
                sourceName: this.name,
                message: 'On complete event received',
                data: { data }
            });
            await this.taskCompletionSender.send(OutdoorEvents.COMPLETE_BREAKDOWN_TASK, data);
        } catch (error) {
            throw new Exception({
                message: 'Error while sending event to SBQ',
                data: { context },
                error
            });
        }
    };

    protected onCompleteSMR: QueueEventHandler<OutdoorEvents.COMPLETE_BREAKDOWN_TASK | OutdoorEvents.COMPLETE_SMR, OutdoorTaskComplete | OutdoorSMRComplete, void> = async (context) => {
        try {
            const {
                entry: { data }
            } = context;
            this.logger.info({
                sourceName: this.name,
                message: 'On complete event received',
                data: { data }
            });
            await this.miStramSMRSender.send(OutdoorEvents.MI_SMR, <OutdoorSMRComplete>data);
        } catch (error) {
            throw new Exception({
                message: 'Error while sending event to SBQ',
                data: { context },
                error
            });
        }
    };
}
