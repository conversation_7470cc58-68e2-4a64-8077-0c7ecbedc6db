'use strict';

var path = require('path');

// Export the config object based on the NODE_ENV
// ==============================================
module.exports = {
    env: process.env.NODE_ENV || 'development',

    // Root path of server
    root: path.normalize(__dirname + '/../..'),

    // Server port
    port: process.env.aahPodHttpPort || process.env.authServicePort || 7110,
    ip: '0.0.0.0',

    apiEndPoint: process.env.aahPodEndpoint || process.env.authServiceApiEndPoint || '/api/auth-service',

    mongoAuthUrl: 'mongodb://rh0113p:27017/auth',

    authDomainCtrl: 'ldap://aa-dc113,ldap://aa-dc114',
    authDomain: 'theaa',

    operatorEndPoint: 'http://dkt920027:7010',
    operatorTimeout: 2000,

    adUrl: process.env.adUrl || 'ldap://aa-dc114,ldap://aa-dc113',

    // these need to be moved to .aahelpNode
    adUserName: process.env.adUserName || 'SVC-AAH2-INTEGRAT',
    adPassword: process.env.adPassword || '1ntgrat$',

    adAAHelp2Group: process.env.adAAHelp2Group || 'GGRP-AAH2-LIVE',

    aaHelpLdapUrl: process.env.aaHelpLdapUrl || 'ldap://AALDAP:389',
    aaHelpLdapUserName: process.env.aaHelpLdapUserName || 'Directory Manager',
    aaHelpLdapPassword: process.env.aaHelpLdapPassword || 'xxxxxxx'
};
