import { useMap, AdvancedMarker } from '@vis.gl/react-google-maps';
import { useEffect, useRef, useState } from 'react';
import { useEvent } from '../../context/eventContext';
import { GridAlgorithm, MarkerClusterer, SuperClusterAlgorithm } from '@googlemaps/markerclusterer';
import type { Marker } from '@googlemaps/markerclusterer';
import Icon from '../../../assets/icomoon/icon';

type Point = google.maps.LatLngLiteral & { key: string } & { name: string } & { regNos: Array<string> };
type Props = { points: Point[] };

// Define custom cluster styles
const clusterStyles = [
    {
        url: './assets/fleet-images/m1.png', // Replace with your custom cluster image URL
        height: 50,
        width: 50,
        textColor: 'Black',
        textSize: 11
    },
    {
        url: './assets/fleet-images/m2.png', // Larger clusters
        height: 60,
        width: 60,
        textColor: 'Black',
        textSize: 11
    },
    {
        url: './assets/fleet-images/m3.png', // Largest clusters
        height: 70,
        width: 70,
        textColor: 'Black',
        textSize: 11
    },
    {
        url: './assets/fleet-images/m4.png', // Largest clusters
        height: 70,
        width: 70,
        textColor: 'Black',
        textSize: 11
    }
];

const Markers = ({ points }: Props) => {
    const map = useMap();
    const [markers, setMarkers] = useState<{ [key: string]: Marker }>({});
    const [expanded, setExpanded] = useState<string | null>(null);
    const clusterer = useRef<MarkerClusterer | null>(null);
    const { setScrollWheelValue, clearMarkers, setClearMarkers, filteredClusted, setFilteredClusted } = useEvent();

    useEffect(() => {
        if (!map) return;
        if (!clusterer.current) {
            const googleMarker: Marker[] = []; // Define googleMarker as an empty array
            clusterer.current = new MarkerClusterer({
                algorithm: new SuperClusterAlgorithm({ minPoints: 1, maxZoom: 20, radius: 160 }),
                map: map,
                markers: googleMarker,
                renderer: {
                    render({ count, position, markers }: { count: number; position: any; markers?: any }) {
                        let cnt = 0;
                        markers?.forEach((marker: any) => {
                            cnt = cnt + parseInt(marker?.qr);
                        });
                        count = cnt;
                        let clusterStyle;
                        if (count > 1499) {
                            clusterStyle = clusterStyles[3]; // Pink for clusters greater than 100
                        } else if (count > 99) {
                            clusterStyle = clusterStyles[2]; // Red for clusters greater than 100
                        } else if (count > 9) {
                            clusterStyle = clusterStyles[1]; // Yellow for clusters between 10 and 100
                        } else {
                            clusterStyle = clusterStyles[0]; // Blue for clusters less than 10
                        }

                        const marker = new google.maps.marker.AdvancedMarkerElement({
                            position,
                            zIndex: 9,
                            content: document.createElement('div')
                        });

                        const iconDiv = document.createElement('div');
                        iconDiv.style.backgroundImage = `url(${clusterStyle.url})`;
                        iconDiv.style.width = `${clusterStyle.width}px`;
                        iconDiv.style.height = `${clusterStyle.height}px`;
                        iconDiv.style.display = 'flex';
                        iconDiv.style.alignItems = 'center';
                        iconDiv.style.justifyContent = 'center';
                        iconDiv.style.color = clusterStyle.textColor;
                        iconDiv.style.fontSize = `${clusterStyle.textSize}px`;
                        const countSpan = document.createElement('span');
                        if (count <= 9) {
                            countSpan.id = 'markerClusterSpan';
                        } else if (count >= 10 && count < 99) {
                            countSpan.id = 'markerClusterSpanMedium';
                        } else if (count >= 100 && count < 1499) {
                            countSpan.id = 'markerClusterSpanLarge';
                        } else if (count >= 1500) {
                            countSpan.id = 'markerClusterSpanExtraLarge';
                        }
                        countSpan.innerText = String(count);
                        iconDiv.appendChild(countSpan);

                        if (marker.content) {
                            marker.content.appendChild(iconDiv);
                        }

                        marker.addListener('click', () => {
                            map.setZoom(5);
                            map.setCenter(marker.position as google.maps.LatLng);
                        });
                        return marker;
                    }
                }
            });
        }
    }, [map]);

    const checkMarkersLoaded = () => {
        return Object.values(markers).every((marker) => marker !== null);
    };

    useEffect(() => {
        if (clearMarkers) {
            clusterer?.current?.clearMarkers();
            setClearMarkers(false);
            setMarkers({});
        }
        if (checkMarkersLoaded()) {
            if (filteredClusted) {
                return;
            }
            clusterer?.current?.addMarkers(Object.values(markers));

            setTimeout(() => {
                setFilteredClusted(true);
            }, 5000);
        }
    }, [clearMarkers, markers]);

    const setMarkerRef = (marker: Marker | null, key: any, regNo: any) => {
        if (filteredClusted) {
            return;
        }
        const keyObj = JSON.parse(key);
        keyObj.regNo = regNo;
        key = JSON.stringify(keyObj);
        if (marker && markers[key]) return;
        if (!marker && !markers[key]) return;
        if (!marker && !markers[key]) return;
        setMarkers((prev) => {
            if (marker) {
                return { ...prev, [key]: marker };
            } else {
                const newMarkers = { ...prev };
                delete newMarkers[key];
                return newMarkers;
            }
        });
    };

    return (
        <>
            {points.map((point, idx) => (
                <AdvancedMarker
                    position={{ lat: isNaN(point.lat) ? 0 : point.lat, lng: isNaN(point.lng) ? 0 : point.lng }}
                    key={point.key + idx}
                    ref={(marker: any) => setMarkerRef(marker, point?.key, point?.regNos?.length)}
                    title={`${point?.regNos?.length}`}
                    onClick={() => console.log('Marker clicked' + point.regNos.length)}
                >
                    <div className="absolute">
                        <div className="retailer-marker">
                            <div className="marker-arrow"></div>
                            <div
                                className="wrapper"
                                onMouseEnter={() => {
                                    const element = document.getElementById(`details-${point.key}`);
                                    if (element) element.style.display = 'block';
                                    setScrollWheelValue(false);
                                }}
                                onMouseLeave={() => {
                                    const element = document.getElementById(`details-${point.key}`);
                                    if (element) {
                                        element.style.display = 'none';
                                        setExpanded(null);
                                    }
                                    setScrollWheelValue(true);
                                }}
                            >
                                <div className="left">
                                    <div className="marker-content">{point.regNos.length}</div>
                                </div>
                                <div
                                    id={`details-${point.key}`}
                                    className="right"
                                    style={{ display: 'none' }}
                                >
                                    <div className="retailer-data">
                                        <div className="name">{point?.name}</div>
                                        <div className="mt-1">
                                            {expanded === point.key ? (
                                                <Icon
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        setExpanded(null);
                                                    }}
                                                    icon="collapse"
                                                    color="#fff"
                                                />
                                            ) : (
                                                <Icon
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        setExpanded(point.key);
                                                    }}
                                                    icon="expand"
                                                    color="#fff"
                                                />
                                            )}
                                        </div>
                                    </div>
                                    {expanded === point.key && (
                                        <div className="vehicles">
                                            {point.regNos.map((regNo: any, idex) => (
                                                <div
                                                    className="vehicle"
                                                    key={idex}
                                                    onClick={() => console.log(`Vehicle selected: ${regNo}`)}
                                                >
                                                    <div className="status"></div>
                                                    <div className="vehicle-details">
                                                        <span className="regno">{regNo?.registrationNo}</span>
                                                        <br />
                                                        <span className="makemodel">
                                                            {regNo?.make} {regNo?.model}
                                                        </span>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </AdvancedMarker>
            ))}
        </>
    );
};
export default Markers;
