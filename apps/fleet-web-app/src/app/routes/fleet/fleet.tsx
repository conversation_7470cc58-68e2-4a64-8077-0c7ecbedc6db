import './fleet.css';
import { APIProvider, Map } from '@vis.gl/react-google-maps';
import { useEffect, useState, useMemo } from 'react';
import Icon from '../../../assets/icomoon/icon';
import History from '../history/history';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { Accordion, AccordionContent, AccordionItem, AccordionTriggerFleet } from '../../components/ui/accordion';
import { setRetailersData } from '../../services/rafDataService';
import { Retailer, Region, RegionList } from '../../interface/retailer';
import { useEvent } from '../../context/eventContext';
import RightNotification from '../rightNotification/rightNotification';
import FilterLeft from '../filterLeft/filterLeft';

import { useCommonStore } from '../../context/commonStore';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import Markers from './fleetMarker';
import { InformationRightProvider } from '../rightInformation/rightInformation';
import { getExtensionHistoryData } from '../../services/generalService';
import DialogWithAccordian from '../../components/dialogAccord/dialogWithAccordian';
import {
    codesToSupplierTypes,
    hasValidDataVehicle,
    normaliseData,
    setupRetailerData,
    setupSLAData,
    slaLevels,
    statuses,
    statusLabels,
    supplierTypeCodeToLabels,
    supplierTypes,
    TPSCodeToSupplierTypeCodes,
    TPSTypes
} from '../../helpers/fleetHelpers';
import packageJson from '../../../package.json';

interface Model {
    label: string;
    value: string;
}
export type MapConfig = {
    id: string;
    label: string;
    mapId?: string;
    mapTypeId?: string;
    backgroundColor?: string;
    styles?: google.maps.MapTypeStyle[];
};
const vitaminCStyles = [
    {
        featureType: 'water',
        elementType: 'geometry',
        stylers: [
            {
                color: '#78d5e9'
            }
        ]
    },
    {
        featureType: 'water',
        elementType: 'labels.text.fill',
        stylers: [
            {
                color: '#515c6d'
            }
        ]
    },
    {
        featureType: 'water',
        elementType: 'labels.text.stroke',
        stylers: [
            {
                color: '#17263c'
            }
        ]
    }
];

const makeListToCompare = ['jaguar', 'land rover', 'porsche', 'unknown', 'hyundai', 'audi'];

const MAP_CONFIGS: MapConfig[] = [
    {
        id: 'styled2',
        label: 'Fleet',
        mapId: 'FleetMap',
        mapTypeId: 'roadmap',
        backgroundColor: '#1d1d1d',
        styles: vitaminCStyles
    }
];
var tasksByResourceId: any = {};
var vehicleGroupsByRetailerFiltered: any[] = [];
export default function Fleet() {
    const { scrollWheelValue, setClearMarkers, setFilterClicked, setFilteredClusted } = useEvent();
    const [filterClick, setFilterClick] = useState(false);
    const [searchVisible, setSearchVisible] = useState(false);
    const [vehicles, setVehicles] = useState<any[]>([]);
    const [extensionDetails, setExtensionDetails] = useState(false);
    const [showAccord, setShowAccord] = useState<boolean>(true);

    //const [tasksByResourceId, setTasksByResourceId] = useState<{ [key: string]: any }>({});
    var resourceIdCheck: any[] = [];
    const filterToggle = () => {
        setFilterClick(!filterClick);
        setFilterClicked(!filterClick);
        setSearchVisible(!searchVisible);
    };
    interface ClickValue {
        click: boolean;
        value: boolean;
        clickValue: boolean;
    }
    const handleFilterClick = (clickValue: ClickValue) => {
        setFilterClick(clickValue.click);
        setFilterClicked(clickValue.clickValue);
        setSearchVisible(clickValue.value);
    };
    const FormSchema = z.object({
        filters: z.array(z.string().min(1)).min(1).nonempty('Please select at least one framework.')
    });
    function onSubmit(data: z.infer<typeof FormSchema>) {
        toast(`You have selected following filters: ${data.filters.join(', ')}.`);
    }
    const form = useForm<z.infer<typeof FormSchema>>({
        resolver: zodResolver(FormSchema),
        defaultValues: {
            filters: ['react.tsx', 'react.ts']
        }
    });
    const [retailers, setRetailers] = useState<Retailer[]>([]);
    const [regionData, setRegionData] = useState<RegionList[]>([]);
    const [make, setMake] = useState<any>();
    const [model, setModel] = useState<Model[]>([]);
    const { activeMenu, setActiveMenu, preloader, setPreloader, setActiveSubMenu } = useEvent();
    const [rafData, setRafData] = useState<any[]>([]);
    const [owningRetailer, setOwningRetailer] = useState<any[]>([]);
    const [totalOwningRetailer, setTotalOwningRetailer] = useState<any[]>(owningRetailer);
    const [filteredData, setFilteredData] = useState<any[]>([]);
    const [fleetGroupData, setFleetGroupData] = useState<any[]>([]);
    const {
        isFilter,
        suppliers,
        regionStore,
        rafStore,
        mobiltyTaskStore,
        rafMobilityCombinedStore,
        setRafMobilityCombinedStore,
        userData,
        extensionHistory,
        setExtensionHistory,
        isCenterMap,
        setIsCenterMap,
        expectedExtension,
        approvedExtension,
        pendingExtension,
        rejectedExtension,
        extensionRules,
        setTskId,
        tasksByResourceId,
        setTasksByResourceId
    } = useCommonStore() || {};
    const [resourceData, setResourceData] = useState<any>();
    const [center, setCenter] = useState({ lat: 55.07, lng: -6.0 });
    const [zoomValue, setZoomValue] = useState(6);
    const [taskId, setTaskId] = useState<number | undefined>();

    const [repairingRetailer, setRepairingRetailer] = useState<any[]>([]);
    const [repairingList, setRepairingList] = useState<any[]>([]);
    const [wholeRepairingList, setWholeRepairingList] = useState<any[]>([]);
    const [selectedExtensionTab, setSelectedExtensionTab] = useState('all');
    const [expectedExtnLength, setExpectedExtnLength] = useState(0);
    interface RepairingRetailerCount {
        all: number;
        requested: number;
        approved: number;
        expected: number;
    }

    const [repairingRetailerCount, setRepairingRetailerCount] = useState<RepairingRetailerCount>({
        all: 0,
        requested: 0,
        approved: 0,
        expected: 0
    });
    const [totalRepairingRetailerCount, setTotalRepairingRetailerCount] = useState<RepairingRetailerCount>({
        all: 0,
        requested: 0,
        approved: 0,
        expected: 0
    });
    const [uniqueVehicles, setUniqueVehicles] = useState<any[]>([]);
    const setUpInitialData = () => {
        if (regionStore && rafStore && suppliers && mobiltyTaskStore) {
            setRepairingList(wholeRepairingList);
            repairingRetailerSelected(selectedExtensionTab, wholeRepairingList);
            setRetailers(suppliers);

            const regionList = convertResToRegion(regionStore);

            setRegionData(regionList);
            const updatedRafData = [...rafStore];
            setRafData(updatedRafData);
            setRafMobilityCombinedStore?.(updatedRafData);
        }
    };

    useEffect(() => {
        if (rafStore && mobiltyTaskStore) {
            setUpInitialData();
            loadVehicles(rafStore);
        }
    }, [rafStore, mobiltyTaskStore]);

    useEffect(() => {
        const uniqueVehicle = Array.from(new Set(vehicles.map((vehicle: any) => vehicle.resourceId))).map((resourceId) => {
            return vehicles.find((vehicle: any) => vehicle.resourceId === resourceId);
        });
        vehicleGroupsByRetailerFiltered = [];
        setUniqueVehicles(uniqueVehicle);
    }, [vehicles]);

    useEffect(() => {
        let Data = renderVehcile(uniqueVehicles);
        //setTimeout(() => {
        mapData(uniqueVehicles);
        //}, 100);
        setFleetGroupData(Data);
        setTotalOwningRetailer(owningRetailer);
        setTotalRepairingRetailerCount(repairingRetailerCount);
        if (Object.keys(globalFilterData).length > 0) {
            globalFilterAllValue(globalFilterData);
        } else {
            setFilteredData?.(Data);
        }
        const makeList = rafMobilityCombinedStore && getMakeList(rafMobilityCombinedStore);
        setMake(makeList);
        const modelList = rafMobilityCombinedStore && getModelList(rafMobilityCombinedStore);
        setModel(modelList);
    }, [rafData, uniqueVehicles]);

    useEffect(() => {
        if (selectedResourceValue === 'extension') {
            if (selectedExtensionTab === 'all') {
                allExtension(repairingList);
            } else if (selectedExtensionTab === 'requested') {
                allExtension(repairingList, false);
                repairingRetailerSelected('requested');
            } else if (selectedExtensionTab === 'approved') {
                allExtension(repairingList, false);
                repairingRetailerSelected('approved');
            } else if (selectedExtensionTab === 'expected') {
                allExtension(repairingList, false);
                repairingRetailerSelected('expected');
            }
        }
    }, [pendingExtension, approvedExtension]);

    //Changes for Repairing Retailers
    const repairingRetailerSelected = (data: any, repairData: any = null) => {
        let repairingListData = repairData || repairingList;
        setSelectedExtensionTab(data);
        switch (data) {
            case 'all':
                allExtension(repairingListData);
                break;
            case 'requested':
                let requestedExtn = repairingListData
                    .filter((task: any) => pendingExtension.some((ext: any) => ext.taskId === task.id))
                    .map((task: any) => {
                        const extension = pendingExtension.find((ext: any) => ext.taskId === task.id);
                        return {
                            ...task,
                            vehicleStatus: 'requested',
                            extensionId: extension?.id,
                            extensionName: extensionRules.find((rule: any) => rule.id === extension?.extensionRuleId)?.description || 'Unknown'
                        };
                    });
                repairingRetailerCount.requested = requestedExtn.length;
                setRepairingRetailerCount(repairingRetailerCount);
                let requestedExtnData = setRepairngListTab(requestedExtn);
                setRepairingRetailer(requestedExtnData);
                break;
            case 'approved':
                let approvedExtn = repairingListData
                    .filter((task: any) => approvedExtension.some((ext: any) => ext.taskId === task.id))
                    .map((task: any) => {
                        const extension = approvedExtension.find((ext: any) => ext.taskId === task.id);
                        return {
                            ...task,
                            vehicleStatus: 'approved',
                            extensionId: extension?.id,
                            extensionName: extensionRules.find((rule: any) => rule.id === extension?.extensionRuleId)?.description || 'Unknown'
                        };
                    });
                // get approved extn without extension
                let approvedExtnwithoutExtn = getApprovedTasksWithoutExtentionFiltered(repairingListData);
                let approvedExt = [...approvedExtn, ...approvedExtnwithoutExtn].map((task: any) => ({ ...task, vehicleStatus: 'approved' }));
                repairingRetailerCount.approved = approvedExtn.length;
                let approvedExtnData = setRepairngListTab(approvedExt);
                setRepairingRetailer(approvedExtnData);
                break;
            case 'expected':
                let expectedExtn = getExpectedExtn(repairingListData);
                repairingRetailerCount.expected = expectedExtn.length;
                let expectedExt = setRepairngListTab(expectedExtn);
                setRepairingRetailer(expectedExt);
                break;
            default:
                // setRepairngListTab(repairingList);
                break;
        }
    };
    const setupExtensionData = () => {
        if (!expectedExtension) return;
        const hireTaskIds = Object.keys(tasksOnHireById);
        let mobilityTaskData = mobiltyTaskStore.filter((task: any) => hireTaskIds.includes(task.id.toString()));
        // let mobilityTaskData = mobiltyTaskStore.filter((task: any) => task && hasValidData(task) && task.hireTask && ['HIRE', 'GARR', 'ARVD'].includes(task.status)).map((task: any) => task);
        mobilityTaskData = getMobilityTaskDataforExtension(mobilityTaskData);
        setRepairingList(mobilityTaskData);
        setWholeRepairingList(mobilityTaskData);
        if (Object.keys(globalFilterData).length > 0) {
            globalfilterRepairingList(globalFilterData, mobilityTaskData);
        } else {
            allExtension(mobilityTaskData);
        }
    };

    const allExtension = (mobilityTaskData: any, isSetRepairingRetailer = true) => {
        //get requested extn
        let requestedExtn = mobilityTaskData.filter((task: any) => pendingExtension.some((ext: any) => ext.taskId === task.id));
        // let requestedExt = setRepairngListTab(requestedExtn, 'requested');
        let requestedExt = requestedExtn.map((task: any) => ({ ...task, vehicleStatus: 'requested' }));
        repairingRetailerCount.requested = requestedExtn.length;

        //approved Extn
        let approvedExtn = mobilityTaskData.filter((task: any) => approvedExtension.some((ext: any) => ext.taskId === task.id));
        // get approved extn without extension
        let approvedExtnwithoutExtn = getApprovedTasksWithoutExtentionFiltered(mobilityTaskData);
        // let approvedExt = setRepairngListTab([...approvedExtn,...approvedExtnwithoutExtn], 'approved');
        let approvedExt = [...approvedExtn, ...approvedExtnwithoutExtn].map((task: any) => ({ ...task, vehicleStatus: 'approved' }));
        repairingRetailerCount.approved = approvedExtn.length;

        //get expected extn
        let expectedExt = getExpectedExtn(mobilityTaskData);
        repairingRetailerCount.expected = expectedExt.length;
        repairingRetailerCount.all = repairingRetailerCount.requested + repairingRetailerCount.approved + repairingRetailerCount.expected;
        setRepairingRetailerCount(repairingRetailerCount);
        setTotalRepairingRetailerCount(repairingRetailerCount);

        if (isSetRepairingRetailer) {
            let allextn: any = setRepairngListTab([...requestedExt, ...approvedExt, ...expectedExt]);
            const sortedRepairingRetailer = allextn.sort((a: any, b: any) => a.groupName?.localeCompare(b.groupName));
            setRepairingRetailer(sortedRepairingRetailer);
        }
    };

    const getExpectedExtn = (mobilityTaskData: any) => {
        let expectedExtn = mobilityTaskData.filter((task: any) => {
            const taskTime = task?.vehicleSchedule && task?.vehicleSchedule?.complete && new Date(task?.vehicleSchedule?.complete).getTime();
            if (!taskTime) {
                return false;
            }
            const isExpected = diff_day(new Date(task?.vehicleSchedule?.complete)) < 1;
            const isNotRequested = !pendingExtension.some((ext: any) => ext.taskId === task.id);
            const isNotApproved = !approvedExtension.some((ext: any) => ext.taskId === task.id);
            // const isRejectedExtension = !rejectedExtension.data.some((ext: any) => ext.taskId === task.id);
            return isExpected && isNotRequested && isNotApproved;
        });

        let expectedExtnTotal = mobilityTaskData.filter((task: any) => {
            const taskTime = task?.vehicleSchedule && task?.vehicleSchedule?.complete && new Date(task?.vehicleSchedule?.complete).getTime();
            if (!taskTime) {
                return false;
            }
            const isExpected = diff_day(new Date(task?.vehicleSchedule?.complete)) < 1;
            const isNotApproved = !approvedExtension.some((ext: any) => ext.taskId === task.id);
            const isRejectedExtension = !rejectedExtension.data.some((ext: any) => ext.taskId === task.id);
            return isExpected && isRejectedExtension && isNotApproved;
        });
        setExpectedExtnLength(expectedExtnTotal.length);

        // let rejectedExtn = mobilityTaskData.filter((task: any) => rejectedExtension.data.some((ext: any) => ext.taskId === task.id));
        let rejectedExtnNotinApprovedOrPending = mobilityTaskData.filter((task: any) => {
            const isRejected = rejectedExtension.data.some((ext: any) => ext.taskId === task.id);
            const isNotApproved = !approvedExtension.some((ext: any) => ext.taskId === task.id);
            const isNotPending = !pendingExtension.some((ext: any) => ext.taskId === task.id);
            return isRejected && isNotApproved && isNotPending;
        });
        // let expectedExt = setRepairngListTab([...expectedExtn,...rejectedExtnNotinApprovedOrPending], 'expected');
        return [...expectedExtn, ...rejectedExtnNotinApprovedOrPending].map((task: any) => ({ ...task, vehicleStatus: 'expected' }));
    };

    const diff_day = (dt: Date) => {
        let now = new Date(),
            timeDiff,
            diff;

        now.setHours(0);
        now.setMinutes(0);
        now.setSeconds(0);

        timeDiff = dt.getTime() - now.getTime();

        diff = timeDiff / (1000 * 3600 * 24);

        return Math.floor(diff);
    };

    const getApprovedTasksWithoutExtentionFiltered = (mobilityTaskData: any) => {
        const now = new Date().getTime();
        // only tasks that have no extension

        let hireTaskIdsNoExtension = mobilityTaskData.filter((task: any) => {
            const taskTime = task?.vehicleSchedule && task?.vehicleSchedule?.complete && new Date(task?.vehicleSchedule?.complete).getTime();
            if (!taskTime) {
                return false;
            }
            const isNotRequested = !pendingExtension.some((ext: any) => ext.taskId === task.id);
            const isNotApproved = !approvedExtension.some((ext: any) => ext.taskId === task.id);
            // const isRejectedExtension = !rejectedExtension.data.some((ext: any) => ext.taskId === task.id);
            return isNotRequested && isNotApproved;
        });
        return hireTaskIdsNoExtension.filter((task: any) => {
            const taskTime = task.vehicleSchedule && task.vehicleSchedule.complete && new Date(task.vehicleSchedule.complete).getTime();
            if (!taskTime) {
                return false;
            }
            if (!task.rental.srcRentalTaskId) {
                return false;
            }

            //Check this task is extended by initial diagnostic period only
            const arriveDate = new Date(task.vehicleSchedule.arrive);
            const todaysDate = new Date();
            if (arriveDate.setHours(0, 0, 0, 0) !== todaysDate.setHours(0, 0, 0, 0)) {
                return false;
            }

            return diff_day(new Date(task.vehicleSchedule.complete)) > 0;
        });
    };

    const setRepairngListTab = (mobilityTaskData: any) => {
        let repairingRetailerData: any = [];
        // let retailerdt = setRetailersData(retailers);
        mobilityTaskData.forEach((vehicle: any) => {
            vehicle.make.name = vehicle?.make?.name ? vehicle.make.name.toLowerCase() : '';
            vehicle.model.name = vehicle?.model?.name ? vehicle.model.name.toLowerCase() : '';
            vehicle.retailers = vehicle.retailerData;
            const retailer: any = suppliers.find((supplier: any) => supplier.id === vehicle?.retailers?.id);
            // if (vehicle.retailers?.postcode) {
            let retailerGroup = repairingRetailerData.find((r: any) => r?.groupId === (retailer?.id || 'Unknown'));
            if (!retailerGroup) {
                retailerGroup = {
                    postCode: retailer?.name?.toUpperCase() || 'Unknown',
                    groupName: retailer?.name?.toUpperCase() || 'Unknown',
                    groupId: retailer?.id || 'Unknown',
                    supplierNetName: vehicle?.retailers?.supplierNet?.supNetworkName,
                    count: 0,
                    redSlaCount: 0,
                    resourcesData: []
                };
                retailerGroup.resourcesData.push(vehicle);
                repairingRetailerData.push(retailerGroup);
            } else {
                if (vehicle.regNo) {
                    retailerGroup.resourcesData.push(vehicle);
                }
            }
            retailerGroup.count++;
            // }
        });
        repairingRetailerData = repairingRetailerData.sort((a: any, b: any) => {
            if (a.groupName === b.groupName) {
                return b.count - a.count;
            }
            return a?.groupName?.localeCompare(b.groupName);
        });
        repairingRetailerData = repairingRetailerData.reduce((acc: any[], retailer: any) => {
            const existingGroup = acc.find((group) => group?.groupId === retailer?.groupId);

            if (existingGroup) {
                existingGroup.resourcesData.push(...retailer.resourcesData);
                existingGroup.count += retailer.count;
                existingGroup.redSlaCount += retailer.redSlaCount;
            } else {
                acc.push(retailer);
            }
            return acc;
        }, []);
        return repairingRetailerData;
        // console.log('repairingRetailerData', repairingRetailerData);
        // setRepairingRetailer(repairingRetailerData);
    };

    const setupThirdPartyHireTask = (task: any) => {
        // Virtual vehicle have data missing - we need to set it manually:
        const repairLocationId = task?.hireTask?.rental?.repairLocation?.id;
        let supplierTypeCode = task?.hireTask?.rental?.thirdPartyHire?.hireVehicle?.supplierTypeCode;
        // const supplierTypeDesc = resolveTpsSupplierDesc(supplierTypeCode, task.rental.thirdPartyHire.hireVehicle.supplierTypeDesc);

        let retailerId;
        let collectionLocationId;

        if (['ENTERPRISE', 'LCH', '3', '4'].includes(supplierTypeCode)) {
            retailerId = repairLocationId;
            collectionLocationId = repairLocationId;
        } else {
            // Else TPS is Enterprise
            retailerId = task?.hireTask?.rental?.dropOffLocation?.id;
            collectionLocationId = task?.hireTask?.rental?.collectLocation?.id;
        }
        let TPSCodeToSupplierTypeCodes: any = {
            THRIFTY: '2',
            ENTERPRISE: '3',
            LCH: '4',
            PNC: '5',
            UNKNOWN: 'UNKNOWN'
        };

        let SupplierTypes: any = {
            RAF: '0',
            OUV: '1',
            THRIFTY: '2',
            ENTERPRISE: '3',
            LCH: '4',
            PNC: '5',
            L460: '6',
            PMF: '7',
            HAF: '8',
            UNKNOWN: 'UNKNOWN'
        };

        // convert TPS string type (incorrect) value to valid Supplier type
        supplierTypeCode = TPSCodeToSupplierTypeCodes[supplierTypeCode] || SupplierTypes[supplierTypeCode] || SupplierTypes.UNKNOWN;

        const retailer: any = suppliers.find((supplier: any) => supplier.id === retailerId);

        // if issue with data store in incompleteTpsTasksById & bailout
        if (!retailer || !supplierTypeCode || !repairLocationId || !collectionLocationId) {
            // svc.incompleteTpsTasksById[task.id] = task;
            return false;
        }

        const regionCode = retailer?.regionCode;

        // if issue with data bailout
        if (!regionCode) {
            return false;
        }

        const hireVehicle = task?.hireTask?.rental?.thirdPartyHire?.hireVehicle;

        // if issue with data bailout
        if (!hireVehicle) {
            return false;
        }
        return true;
    };
    let count = 1;
    const hasValidData = (task: any) => {
        // exist
        if (!task) {
            return false;
        }
        count++;
        // has valid task hire
        if (isThirdPartyHireTask(task)) {
            if (!task.hireTask.rental.thirdPartyHire.hireVehicle) {
                return false;
            }
            if (!task.hireTask.rental.thirdPartyHire.hireVehicle.resourceId) {
                return false;
            }
            return setupThirdPartyHireTask(task);
        }

        return true;
    };

    const isThirdPartyHireTask = (task: any) => {
        return !!(task && task?.hireTask?.rental && task?.hireTask?.rental?.thirdPartyHire);
    };

    const getMobilityTaskDataforExtension = (mobilityTask: any, hideFilter: boolean = false) => {
        const vehicleDetails = mobilityTask.map((task: any) => {
            let vehicleData = {
                id: task?.id,
                registration: task?.hireTask?.vehicle?.registration,
                regNo: task.hireTask?.rental?.hireVehicle?.regNo || task.hireTask?.rental?.thirdPartyHire?.hireVehicle?.regNo,
                resourceId: task?.hireTask?.rental?.thirdPartyHire?.hireVehicle?.resourceId ? task?.hireTask?.rental?.thirdPartyHire?.hireVehicle?.resourceId : task.hireTask?.rental?.hireVehicle?.id,
                transmissionType: task?.hireTask?.rental?.hireVehicle?.transmissionType,
                fuelType: task.hireTask?.rental?.hireVehicle?.fuelType,
                status: task?.status,
                customerDetails: task?.hireTask?.rental?.mainDriver,
                customerVehicleDetails: task?.hireTask?.vehicle,
                breakDownFault: task?.hireTask?.fault,
                completionCode: task?.hireTask?.fault?.outcome?.completionCode && task?.hireTask?.fault?.outcome?.completionCode,
                vehicleSchedule: task?.vehicleSchedule,
                customerLocationData: task?.hireTask?.rental,
                vin: task?.hireTask?.rental?.hireVehicle?.vin,
                customerCode: task?.hireTask?.entitlement?.customerGroup?.code,
                supNetworkCode: task?.hireTask?.entitlement?.customerGroup?.code,
                supNetworkName: task?.hireTask?.entitlement?.customerGroup?.name,
                customerRequestId: task?.customerRequestId,
                supplierTypeCode: task.status === 'GARR' ? task?.hireTask?.rental?.thirdPartyHire?.hireVehicle?.supplierTypeCode : task.hireTask?.rental?.hireVehicle?.supplierTypeCode,
                supplierTypeDesc: task?.hireTask?.rental?.thirdPartyHire?.hireVehicle?.supplierTypeCode,
                regionCode: task.hireTask?.rental?.hireVehicle?.regionCode,
                colour: task.hireTask?.rental?.hireVehicle?.colour,
                seatNumber: task.hireTask?.rental?.hireVehicle?.seatNumber,
                currentDepotId: task?.hireTask?.rental?.thirdPartyHire?.hireVehicle?.resourceId,
                insuranceId: task?.hireTask?.rental?.mainDriver?.Id,
                contactName: task?.hireTask?.rental?.mainDriver?.contact?.name,
                telephone: task?.hireTask?.rental?.mainDriver?.contact?.telephone,
                email: task?.hireTask?.rental?.mainDriver?.contact?.email,
                addressLine: task?.hireTask?.rental?.mainDriver?.address?.addressLines,
                rental: task?.hireTask?.rental,
                sla: task?.hireTask?.rental?.repairLocation?.sla,
                retailerData: {
                    postcode: task?.hireTask?.rental?.repairLocation?.address?.split(',')[2],
                    name: task?.hireTask?.rental?.repairLocation?.name?.split(',')[0],
                    id: task?.hireTask?.rental?.repairLocation?.id
                    //account: setRetailersData(task)
                },
                make: {
                    id: task.status === 'GARR' ? task?.hireTask?.rental?.thirdPartyHire?.hireVehicle?.make?.id : task.hireTask?.rental?.hireVehicle?.make?.id,
                    name: task.status === 'GARR' ? task?.hireTask?.rental?.thirdPartyHire?.hireVehicle?.make?.name : task.hireTask?.rental?.hireVehicle?.make?.name
                },
                model: {
                    id: task.status === 'GARR' ? task?.hireTask?.rental?.thirdPartyHire?.hireVehicle?.model?.id : task.hireTask?.rental?.hireVehicle?.model?.id,
                    name: task.status === 'GARR' ? task?.hireTask?.rental?.thirdPartyHire?.hireVehicle?.model?.name : task.hireTask?.rental?.hireVehicle?.model?.name
                },
                location: {
                    latitude: task.hireTask?.location?.coordinates?.latitude,
                    longitude: task.hireTask?.location?.coordinates?.longitude
                },
                unproductive: task.hireTask?.rental?.hireVehicle?.unproductive1,
                booking: {
                    startDate: task.status === 'GARR' ? task?.hireTask?.appointment?.earliest : task?.appointment?.earliest,
                    endDate: task.status === 'GARR' ? task?.hireTask?.appointment?.latest : task?.appointment?.latest
                },
                hire: {
                    startDate: task?.status === 'GARR' ? task?.vehicleSchedule?.arrive : task?.schedule?.arrive,
                    endDate: task?.status === 'GARR' ? task?.vehicleSchedule?.complete : task?.schedule?.complete
                }
            };

            if (!vehicleData.supplierTypeDesc) {
                vehicleData.supplierTypeDesc = vehicleData.supplierTypeCode;
            }

            vehicleData.supplierTypeDesc = vehicleData?.supplierTypeDesc?.replace('Vehicle', '').replace('CAF', 'RAF').replace('AA', 'RAF').trim().replace('Hyundai', 'HAF');
            return vehicleData;
        });
        return vehicleDetails;
    };

    const [mapMarkers, setMapMarkers] = useState<Point[]>([]);

    const renderVehcile = (vehicles: any, filtering?: boolean) => {
        const fleetGroup: any[] = [];
        let retailerdt = setRetailersData(retailers);
        if (filtering) {
            vehicleGroupsByRetailerFiltered = [];
        }
        vehicles.forEach((vehicle: any) => {
            const statusMap: { [key: string]: string } = {
                FREE: 'Available',
                MAIN: 'Maintenance',
                HEAD: 'Booked',
                GDET: 'Booked',
                ARVD: 'On Hire',
                HIRE: 'On Hire',
                GARR: 'On Hire',
                VLET: 'Valet',
                DFLT: 'Defleet',
                BUSY: 'Booked'
            };

            const groupName = statusMap[vehicle.status];
            let retailer = retailerdt?.retailersById[vehicle.currentDepotId];
            // group by retailer
            let key = retailer && retailer.id ? retailer.id : 'Unknown';
            let name = retailer && retailer.name ? retailer.name : 'Unknown';
            groupOrUngroupVehicle(key, name, vehicleGroupsByRetailerFiltered, vehicle, true);

            if (groupName) {
                let group = fleetGroup.find((g) => g.groupName === groupName);
                if (!group) {
                    group = {
                        groupName: groupName,
                        count: 0,
                        redSlaCount: 0,
                        resourcesData: []
                    };
                    fleetGroup.push(group);
                }

                group.count++;
                group.resourcesData.push(vehicle);
                if ((vehicle.sla.days !== undefined && vehicle.sla.days > 0) || (vehicle.sla.hours !== undefined && vehicle.sla.hours > 0)) {
                    if (group.resourcesData.some((v: any) => v.regNo === vehicle.regNo)) {
                        if (vehicle.sla.status === 'RED') {
                            group.redSlaCount++;
                        }
                    }
                }
            }
        });
        vehicleGroupsByRetailerFiltered.sort((a, b) => {
            if (a.name.localeCompare(b.name) === 0) {
                return b.redSlaCount - a.redSlaCount;
            }
            return a.name.localeCompare(b.name);
        });
        sortDataOnSla(vehicleGroupsByRetailerFiltered);
        setOwningRetailer(vehicleGroupsByRetailerFiltered);
        sortDataOnSla(fleetGroup);
        fleetGroup.sort((a, b) => a?.groupName?.localeCompare(b.groupName));

        const allResourcesData = fleetGroup?.flatMap((group) => group?.resourcesData);
        // mapData(allResourcesData);
        return fleetGroup;
    };

    const mapData = (allResourcesData: any, filtering?: boolean) => {
        if (filtering) {
            setMapMarkers([]);
            setClearMarkers(true);
            setFilteredClusted(false);
        }
        if (isFilter) {
            const mapDt = allResourcesData?.reduce((acc: any, vehicle: any) => {
                const existing = acc.find((item: any) => item.postcode === vehicle?.retailer?.postcode?.trim());
                let retailerdt = setRetailersData(retailers);
                vehicle.retailerData = retailerdt?.retailersById[vehicle.currentDepotId];

                if (existing) {
                    existing.regNos.push({ registrationNo: vehicle?.regNo, make: vehicle?.make?.name, model: vehicle?.model?.name });
                    existing.key = JSON.stringify({
                        name: vehicle?.retailer?.name?.toUpperCase(),
                        lat: Number(vehicle?.retailerData?.location?.latitude),
                        lng: Number(vehicle?.retailerData?.location?.longitude),
                        regNo: vehicle?.regNo
                    });
                } else {
                    acc.push({
                        name: vehicle?.retailer?.name?.toUpperCase(),
                        regNos: [{ registrationNo: vehicle?.regNo, make: vehicle?.make?.name, model: vehicle?.model?.name }],
                        lat: Number(vehicle?.retailerData?.location?.latitude),
                        lng: Number(vehicle?.retailerData?.location?.longitude),
                        postcode: vehicle?.retailer?.postcode?.trim(),
                        key: JSON.stringify({
                            name: vehicle?.retailer?.name,
                            lat: Number(vehicle?.retailerData?.location?.latitude),
                            lng: Number(vehicle?.retailerData?.location?.longitude),
                            regNo: vehicle.regNo
                        })
                    });
                }

                return acc;
            }, []);
            setMapMarkers(mapDt);
        }
    };
    const groupOrUngroupVehicle = (groupKey: string, groupName: string, groupStore: any[], vehicle: any, initialLoad = false) => {
        let groupIndex = groupStore.findIndex((group) => group.key === groupKey);
        const newGroup = groupIndex === -1;

        // if filtered vehicle group it
        if (vehiclesByResourceId[vehicle.resourceId]) {
            // if new group
            if (newGroup) {
                groupIndex =
                    groupStore.push({
                        key: groupKey,
                        name: groupName,
                        resourcesData: [],
                        redSlaCount: 0
                    }) - 1;
            }

            // if already grouped correctly lets stop here
            if (groupStore[groupIndex].resourcesData.includes(vehicle.resourceId)) {
                return;
            }

            if (!newGroup || (newGroup && !initialLoad)) {
                // remove from any other group
                ungroupVehicle(groupStore, vehicle);
            }

            // count red sla
            if (vehicle.sla.status === 'RED') {
                groupStore[groupIndex].redSlaCount++;
            }

            // only add if unique entry
            groupStore[groupIndex].resourcesData.push(vehicle);
        } else if (!newGroup) {
            ungroupVehicle(groupStore, vehicle);
        }
    };

    function ungroupVehicle(groupStore: any[], vehicle: any) {
        groupStore.forEach((group) => {
            const index = group.resourcesData.indexOf(vehicle.resourceId);
            if (index !== -1) {
                group.resourcesData.splice(index, 1);
                if (vehicle.sla.status === 'RED') {
                    group.redSlaCount--;
                }
            }
        });
    }
    const sortDataOnSla = (fleetGroup: any) => {
        fleetGroup.forEach((group: any) => {
            group.resourcesData.sort((a: any, b: any) => {
                if (a?.sla?.status === 'RED' && b?.sla?.status !== 'RED') {
                    return -1;
                } else if (a?.sla?.status !== 'RED' && b?.sla?.status === 'RED') {
                    return 1;
                } else if (a?.sla?.status === 'AMBER' && b?.sla?.status !== 'AMBER') {
                    return -1;
                } else if (a?.sla?.status !== 'AMBER' && b?.sla?.status === 'AMBER') {
                    return 1;
                } else if (a?.sla?.status === 'RED' && b?.sla?.status === 'RED') {
                    if (a?.sla?.days === '?' || b?.sla?.days === '?') {
                        return a?.sla?.days === '?' ? 1 : -1;
                    } else if (a?.sla?.days && b?.sla?.days) {
                        return b.sla.days - a.sla.days;
                    } else if (a?.sla?.days) {
                        return -1;
                    } else if (b?.sla?.days) {
                        return 1;
                    } else if (a?.sla?.hours && b?.sla?.hours) {
                        return b.sla.hours - a.sla.hours;
                    } else if (a?.sla?.hours) {
                        return -1;
                    } else if (b?.sla?.hours) {
                        return 1;
                    } else {
                        return 0;
                    }
                } else if (a?.sla?.status === 'AMBER' && b?.sla?.status === 'AMBER') {
                    if (a?.sla?.days === '?' || b?.sla?.days === '?') {
                        return a?.sla?.days === '?' ? 1 : -1;
                    } else if (a?.sla?.days && b?.sla?.days) {
                        return b.sla.days - a.sla.days;
                    } else if (a?.sla?.days) {
                        return -1;
                    } else if (b?.sla?.days) {
                        return 1;
                    } else if (a?.sla?.hours && b?.sla?.hours) {
                        return b.sla.hours - a.sla.hours;
                    } else if (a?.sla?.hours) {
                        return -1;
                    } else if (b?.sla?.hours) {
                        return 1;
                    } else {
                        return 0;
                    }
                } else if (a?.sla?.status === 'GREEN' && b?.sla?.status === 'GREEN') {
                    if (a?.sla?.days === '?' || b?.sla?.days === '?') {
                        return a?.sla?.days === '?' ? 1 : -1;
                    } else if (a?.sla?.days && b?.sla?.days) {
                        return b.sla.days - a.sla.days;
                    } else if (a?.sla?.days) {
                        return -1;
                    } else if (b?.sla?.days) {
                        return 1;
                    } else if (a?.sla?.hours && b?.sla?.hours) {
                        return b.sla.hours - a.sla.hours;
                    } else if (a?.sla?.hours) {
                        return -1;
                    } else if (b?.sla?.hours) {
                        return 1;
                    } else {
                        return 0;
                    }
                } else {
                    return 0;
                }
            });
        });
    };

    const setupVehicleDates = (vehicle: any) => {
        const defaultDates = { startDate: null, endDate: null };
        vehicle.defleet = { ineffectiveDate: null, defleetDate: null };
        vehicle.valet = { ...defaultDates };
        vehicle.maintenance = { ...defaultDates };
        vehicle.booking = vehicle.booking || { ...defaultDates };
        vehicle.hire = vehicle.hire || { ...defaultDates };

        vehicle?.unproductive?.forEach((unproductiveState: any) => {
            const { status, startTime, endTime } = unproductiveState;
            if (status === statuses.DFLT) {
                vehicle.defleet = { ineffectiveDate: startTime, defleetDate: endTime };
            } else if (status === statuses.VLET) {
                vehicle.valet = { startDate: startTime, endDate: endTime };
            } else if (status === statuses.MAIN) {
                vehicle.maintenance = { startDate: startTime, endDate: endTime };
            }
        });

        const task = tasksByResourceId[vehicle.resourceId];
        if (task) {
            vehicle.customerGroup = task?.entitlement?.customerGroup?.code;
            if (task.appointment) {
                vehicle.booking = { startDate: task?.appointment?.earliest, endDate: task?.appointment?.latest };
                vehicle.hire = { startDate: task?.schedule?.arrive, endDate: task?.schedule?.complete };
            }
        }

        return vehicle;
    };

    const convertResToRegion = useMemo(
        () => (Region: Region[]) => {
            return Region.map((region) => ({
                label: region.description,
                value: region.paramCode
            }));
        },
        []
    );
    const getMakeList = (vehicles: any) => {
        const uniqueMakes = Array.from(new Set(vehicles?.map((vehicle: any) => vehicle?.make?.id))).map((id: any) => {
            const vehicle = vehicles.length > 0 && vehicles?.find((v: any) => v?.make?.id === id);
            return {
                label: vehicle?.make?.name,
                value: vehicle?.make?.id
            };
        });
        return uniqueMakes.filter((make) => make?.label && makeListToCompare.includes(make.label.toLowerCase()));
    };
    const getModelList = (vehicles: any): { label: string; value: string }[] => {
        const uniqueModels = Array.from(new Set(vehicles?.map((vehicle: any) => vehicle?.model?.id))).map((id) => {
            const vehicle = vehicles?.find((v: any) => v?.model?.id === id);
            return {
                label: vehicle?.model?.name,
                value: vehicle?.model?.id
            };
        });
        return uniqueModels?.filter((model) => model?.label !== undefined);
    };

    const [clickedValueFleet, setClickedValueFleet] = useState<string>('');
    const openInformationPanel = (clickValue: string) => {
        setActiveMenu('information');
        setTaskId(undefined);
        setClickedValueFleet(clickValue);
    };

    const openHistoryPanel = (clickValue: string) => {
        setActiveMenu('timer');
        setActiveSubMenu('extension');
        setTaskId(undefined);
        setClickedValueFleet(clickValue);
    };

    const [selectedResourceValue, setSelectedResourceValue] = useState<string>('status');

    const selectedResources = (value: string) => {
        setSelectedResourceValue(value);
        if (value === 'extension') {
            setupExtensionData();
        }
        setExtensionDetails(false);
    };

    const [resID, setResourceId] = useState<string>('');
    const [vehicleData, setVehicleData] = useState<any>();
    const [fleetSelected, setFleetSelected] = useState<string>('');
    const [resourceBy, setResourceBy] = useState<any>();
    const availableVehicleClick = (value: string, fleet: string, vehicleData: any, resourcesBy?: string) => {
        vehicleData.task = resourceSelectedCallback(vehicleData);
        setFleetSelected(fleet);
        setResourceId(value);
        setVehicleData(vehicleData);
        setResourceBy(resourcesBy);
    };

    const resourceSelectedCallback = (vehicle: any) => {
        if (!vehicle) {
            return;
        }
        const task = vehicle ? tasksByResourceId[vehicle.resourceId] : null;

        if (!task) {
            return;
        }
        return task;
    };
    const [noDataFound, setNoDataFound] = useState(false);
    const [globalFilterData, setglobalFilterData] = useState({});
    const globalFilterAllValue = useMemo(
        () => (data: any) => {
            setPreloader(true);
            const filters = data;
            setglobalFilterData(data);
            let filterData = fleetGroupData.flatMap((group: any) =>
                group.resourcesData.filter((vehicle: any) => {
                    const matchesRegion = !filters.region || filters.region.length === 0 || filters.region.includes(vehicle?.regionCode);
                    const matchesMake = !filters.make || filters.make.length === 0 || filters.make.includes(vehicle?.make?.id);
                    const matchesModel = !filters.model || filters.model.length === 0 || filters.model.includes(vehicle?.model?.id);
                    const matchesFleetType = !filters.fleetType || filters.fleetType.length === 0 || filters.fleetType.includes(vehicle?.supplierTypeDesc);
                    const matchesStatus = !filters.status || filters.status.length === 0 || filters.status.includes(vehicle?.status);
                    const matchesOwningRetailer = !filters.owningRetailer || filters.owningRetailer.length === 0 || filters.owningRetailer.includes(vehicle?.retailerData?.id?.toString());
                    const matchesCustomerGroup =
                        !filters.customerGroup ||
                        filters.customerGroup.length === 0 ||
                        (((Array.isArray(vehicle?.customerGroups) && vehicle?.customerGroups.some((group: string) => filters?.customerGroup?.includes(group))) ||
                            filters.customerGroup.includes(vehicle?.customerGroup) ||
                            filters.customerGroup.includes(vehicle?.supNetworkCode)) &&
                            vehicle?.sla?.status !== 'UNKNOWN');
                    const matchesSearchValue =
                        !filters.searchValue ||
                        vehicle?.regNo?.includes(filters.searchValue) ||
                        vehicle?.retailer?.name?.toLocaleLowerCase().includes(filters?.searchValue.toLocaleLowerCase()) ||
                        vehicle?.retailer?.postcode?.toLocaleLowerCase().includes(filters?.searchValue.toLocaleLowerCase()) ||
                        vehicle?.make?.name?.toLocaleLowerCase().includes(filters?.searchValue.toLocaleLowerCase());

                    return matchesRegion && matchesMake && matchesModel && matchesFleetType && matchesStatus && matchesOwningRetailer && matchesCustomerGroup && matchesSearchValue;
                })
            );
            globalfilterRepairingList(filters);
            if (Object.values(filters).every((value) => !value || (Array.isArray(value) && value.length === 0))) {
                setFilteredData(fleetGroupData);
            } else {
                if (filterData.length > 0) {
                    let data = renderVehcile(filterData, true);
                    setFilteredData(data);
                    const allResourcesData = data?.flatMap((group) => group?.resourcesData);
                    mapData(allResourcesData, true);
                    setNoDataFound(false);
                } else {
                    setNoDataFound(true);
                }
            }
            setPreloader(false);
        },
        [fleetGroupData]
    );

    const setExtensionData = () => {
        const combinedExt = [...approvedExtension, ...pendingExtension];
        const extData = repairingRetailer.flatMap((item: any) => item?.resourcesData) || [];
        const extensionReasons = extData
            .filter((ext: any) => combinedExt.some((data: any) => data.taskId === ext.id))
            .map((ext: any) => {
                const matchingExtension = combinedExt.find((data: any) => data.taskId === ext.id);
                return {
                    value: ext.id,
                    extensionRuleId: matchingExtension?.extensionRuleId || null
                };
            });

        const previousExtensionReasons = extData
            .filter((ext: any) => combinedExt.some((data: any) => data.taskId === ext.id))
            .map((ext: any) => {
                const matchingExtension = combinedExt.find((data: any) => data.taskId === ext.id);
                return {
                    value: ext.id,
                    previousExtensionRuleId: matchingExtension?.previousExtensionRuleId || null
                };
            });

        const mapReasonsWithDescription = (reasons: any[], ruleIdKey: string) =>
            reasons.map((reason: any) => {
                const matchingRule = extensionRules?.find((rule: any) => rule.id === reason[ruleIdKey]);
                return {
                    value: matchingRule?.id,
                    label: matchingRule?.description || 'No extensions found'
                };
            });

        const mapPreviousReasonsWithDescription = (reasons: any[], ruleIdKey: string) =>
            reasons.map((reason: any) => {
                const matchingRule = extensionRules?.find((rule: any) => rule.id === reason[ruleIdKey]);
                return {
                    value: reason.value,
                    label: matchingRule?.description || 'No previous extensions'
                };
            });

        const uniqueReasons = (reasons: { value: string; label: string }[]) =>
            reasons.reduce((acc: { value: string; label: string }[], current) => {
                if (!acc.some((item) => item.label === current.label)) {
                    acc.push(current);
                }
                return acc;
            }, []);

        // setExtensionReason(uniqueReasons(mapReasonsWithDescription(extensionReasons, 'extensionRuleId')));
        // setPreviousExtensionReason(uniqueReasons(mapPreviousReasonsWithDescription(previousExtensionReasons, 'previousExtensionRuleId')));
    };

    const globalfilterRepairingList = (filters: any, data = null) => {
        let repairingList = data || wholeRepairingList;
        let filterReparingData = repairingList.filter((vehicle: any) => {
            const matchesRegion = !filters.region || filters.region.length === 0 || filters.region.includes(vehicle?.regionCode);
            const matchesMake = !filters.make || filters.make.length === 0 || filters.make.includes(vehicle?.make?.id);
            const matchesModel = !filters.model || filters.model.length === 0 || filters.model.includes(vehicle?.model?.id);
            const matchesFleetType = !filters.fleetType || filters.fleetType.length === 0 || filters.fleetType.includes(vehicle?.supplierTypeDesc);
            const matchesStatus = !filters.status || filters.status.length === 0 || filters.status.includes(vehicle?.status);
            const matchesRepairingRetailer = !filters.repairingRetailer || filters.repairingRetailer.length === 0 || filters.repairingRetailer.includes(vehicle?.retailerData?.id?.toString());
            const matchesCustomerGroup =
                !filters.customerGroup ||
                filters.customerGroup.length === 0 ||
                (((Array.isArray(vehicle?.customerGroups) && vehicle?.customerGroups.some((group: string) => filters?.customerGroup?.includes(group))) ||
                    filters.customerGroup.includes(vehicle?.customerGroup) ||
                    filters.customerGroup.includes(vehicle?.supNetworkCode)) &&
                    vehicle?.sla?.status !== 'UNKNOWN');
            const matchesSearchValue =
                !filters.searchValue ||
                vehicle?.regNo?.includes(filters.searchValue) ||
                vehicle?.retailerData?.name?.toLocaleLowerCase().includes(filters?.searchValue.toLocaleLowerCase()) ||
                vehicle?.retailerData?.postcode?.toLocaleLowerCase().includes(filters?.searchValue.toLocaleLowerCase()) ||
                vehicle?.make?.name?.toLocaleLowerCase().includes(filters?.searchValue.toLocaleLowerCase());
            const matchesExtensionReason = !filters.extensionReason || filters.extensionReason.length === 0 || filters.extensionReason.includes(vehicle?.extensionReason);
            const matchesPreviousExtensionReason =
                !filters.previousExtensionReason || filters.previousExtensionReason.length === 0 || filters.previousExtensionReason.includes(vehicle?.previousExtensionReason);
            const matchesTaskType = !filters.taskType || filters?.completionCode?.length === 0 || filters.taskType.includes(vehicle?.completionCode || '00');
            return (
                matchesRegion &&
                matchesMake &&
                matchesModel &&
                matchesFleetType &&
                matchesStatus &&
                matchesRepairingRetailer &&
                matchesCustomerGroup &&
                matchesSearchValue &&
                matchesExtensionReason &&
                matchesPreviousExtensionReason &&
                matchesTaskType
            );
        });

        if (Object.values(filters).every((value) => !value || (Array.isArray(value) && value.length === 0))) {
            setRepairingList(wholeRepairingList);
            repairingRetailerSelected(selectedExtensionTab, wholeRepairingList);
        } else {
            if (filterReparingData.length > 0) {
                setRepairingList(filterReparingData);
                repairingRetailerSelected(selectedExtensionTab, filterReparingData);
            }
        }
    };

    const globalFilterValue = (data: string) => {
        data = data.trim();
        const filteredData = data
            ? fleetGroupData.flatMap((group) =>
                  group.resourcesData.filter(
                      (vehicle: any) =>
                          vehicle.regNo === data ||
                          vehicle.retailers?.name.toLocaleLowerCase().includes(data.toLocaleLowerCase()) ||
                          vehicle.retailers?.postcode.toLocaleLowerCase().includes(data.toLocaleLowerCase()) ||
                          vehicle.make?.name.toLocaleLowerCase().includes(data.toLocaleLowerCase())
                  )
              )
            : rafStore;
        if (filteredData.length > 0) {
            setRafData(filteredData);
        } else {
            setNoDataFound(true);
        }
    };
    const clearGlobalFilter = (data: string) => {
        globalFilterAllValue({});
        setNoDataFound(false);
        setMapMarkers([]);
        setFilteredClusted(false);
        setClearMarkers(true);
        //mapData(vehicles);
    };

    //New Mobility Task Data START
    let startIndex = 0;
    const loadVehicles = useMemo(
        () => (rawVehicles: any) => {
            //setPreloader(true);
            // const processVehicles = () => {
            //     for (let i = startIndex; i < rawVehicles.length; i++) {
            //         const vehicle = rawVehicles[i];
            //         if (!hasValidDataVehicle(vehicle, suppliers)) {
            //             continue;
            //         }
            //         const setupVehicle = setup(vehicle);
            //         positionVehicleBySLA(setupVehicle);
            //         startIndex++;
            //         if (startIndex === rawVehicles.length - 2) {
            //             setPreloader(false);
            //             return; // Stop processing when all vehicles are processed
            //         }
            //     }
            // };

            // const processAllVehicles = () => {
            //     const processChunk = (deadline: IdleDeadline) => {
            //         while (startIndex < rawVehicles?.length && deadline.timeRemaining() > 0) {
            //             processVehicles();
            //         }
            //         if (startIndex < rawVehicles?.length) {
            //             requestIdleCallback(processChunk);
            //         } else {
            //             // All data processed
            //             setVehiclesSorted(true);
            //         }
            //     };
            //     requestIdleCallback(processChunk);
            // };

            // processAllVehicles();

            rawVehicles.forEach((vehicle: any) => {
                //ehicle = new RafVehicle(vehicle).toJSON();

                // if vehicle in illegal state bailout
                if (!hasValidData(vehicle)) {
                    return;
                }

                vehicle = setup(vehicle);
                // svc.vehicles.push(vehicle);
                positionVehicleBySLA(vehicle);
            });
            updateIndexes(rawVehicles);

            vehicles.sort(sortVehiclesBySLA);
            setVehiclesSorted(true);
        },
        [rafStore]
    );

    const setupRafVehicle = (task: any) => {
        const vehicle = { ...task.hireTask.rental.hireVehicle, ...task.vehicleSchedule.resource };
        task.hireTask.rental.hireVehicle = vehicle;

        if (task.hireTask.rental.thirdPartyHire && !task.hireTask.rental.thirdPartyHire.hireVehicle) {
            task.hireTask.rental.thirdPartyHire.hireVehicle = vehicle;
        }

        return task;
    };
    const thirdPartyHireTask = (task: any): boolean => {
        return Boolean(task?.rental?.thirdPartyHire);
    };
    const hasValidDataMobility = (task: any): boolean => {
        if (!task) return false;
        if (isThirdPartyHireTask(task)) {
            const hireVehicle = task.rental.thirdPartyHire.hireVehicle;
            if (!hireVehicle || !hireVehicle.resourceId) return false;
        }
        return true;
    };
    const SupplierTypes = {
        ENTERPRISE: '3',
        HAF: '8',
        L460: '6',
        LCH: '4',
        OUV: '1',
        PMF: '7',
        PNC: '5',
        RAF: '0',
        THRIFTY: '2',
        UNKNOWN: 'UNKNOWN'
    };

    const setupThirdPartyHireTaskMobility = (task: any) => {
        const repairLocationId = task?.rental?.repairLocation?.id;
        let supplierTypeCode = task?.rental?.thirdPartyHire?.hireVehicle?.supplierTypeCode;
        const supplierTypeDesc = resolveTpsSupplierDesc(supplierTypeCode, task?.rental?.thirdPartyHire?.hireVehicle?.supplierTypeDesc);

        const isEnterpriseOrLCH = [TPSTypes.ENTERPRISE, TPSTypes.LCH, SupplierTypes.ENTERPRISE, SupplierTypes.LCH].includes(supplierTypeCode);
        const retailerId = isEnterpriseOrLCH ? repairLocationId : task?.rental?.dropOffLocation?.id;
        const collectionLocationId = isEnterpriseOrLCH ? repairLocationId : task?.rental?.collectLocation?.id;

        supplierTypeCode = TPSCodeToSupplierTypeCodes[supplierTypeCode] || SupplierTypes[supplierTypeCode as keyof typeof SupplierTypes] || SupplierTypes.UNKNOWN;

        const retailer: any = suppliers.find((supplier: any) => supplier.id === retailerId);
        const regionCode = retailer?.regionCode;
        if (!regionCode) return;

        const hireVehicle = task.rental.thirdPartyHire.hireVehicle;
        if (!hireVehicle) return;

        const updateVehicleData = (vehicle: any) => {
            vehicle.status = TPSVehicleStatusForTask(task);
            vehicle.supplierTypeCode = supplierTypeCode;
            vehicle.supplierTypeDesc = supplierTypeDesc;
            vehicle.regionCode = regionCode;
            vehicle.homeDepotId = collectionLocationId;
            vehicle.currentDepotId = repairLocationId;
            vehicle.dropOffLocation = task.rental.dropOffLocation;
            vehicle.collectLocation = task.rental.collectLocation;
            vehicle.repairLocation = task.rental.repairLocation;
            vehicle.regNo = vehicle.regNo || task.rental.hireVehicle.regNo;
        };

        updateVehicleData(task.rental.hireVehicle);
        updateVehicleData(task.rental.thirdPartyHire.hireVehicle);

        const resourceIds = update(hireVehicle);
        task.rental.hireVehicle.resourceId = resourceIds[0];
        task.rental.thirdPartyHire.hireVehicle.resourceId = resourceIds[0];

        return task;
    };
    const update = (vehicles: any) => {
        const ids: string[] = [];
        vehicles = Array.isArray(vehicles) ? vehicles : [vehicles];

        vehicles = vehicles.reduce((acc: any[], vehicle: any) => {
            if (hasValidDataVehicle(vehicle, suppliers)) {
                vehicle = setup(vehicle);
                updateOrAddVehicle(vehicle);
                ids.push(vehicle.resourceId);
                acc.push(vehicle);
            }
            return acc;
        }, []);

        return ids;
    };
    const updateOrAddVehicle = (vehicleToUpdate: any) => {
        positionVehicleBySLA(vehicleToUpdate);
        updateIndexes(vehicleToUpdate);

        if (selectedTask?.resourceId === vehicleToUpdate.resourceId) {
            setSelectedTask(vehicleToUpdate);
        }
    };

    const [vehiclesSorted, setVehiclesSorted] = useState<boolean>(false);
    const positionVehicleBySLA = (vehicleToPosition: any) => {
        setVehicles((prevVehicles) => {
            const newVehicles = prevVehicles.filter((vehicle) => vehicle.resourceId !== vehicleToPosition.resourceId);

            if (vehiclesSorted) {
                const sortedIndex = newVehicles.findIndex((sortedVehicle) => sortVehiclesBySLA(sortedVehicle, vehicleToPosition) === 1);
                if (sortedIndex !== -1) {
                    newVehicles.splice(sortedIndex, 0, vehicleToPosition);
                } else {
                    newVehicles.push(vehicleToPosition);
                }
            } else {
                newVehicles.push(vehicleToPosition);
            }

            return newVehicles;
        });
    };
    const [vehiclesByResourceId, setVehiclesByResourceId] = useState<any>({});
    const [makesById, setMakesById] = useState<any>({});
    const [modelsById, setModelsById] = useState<any>({});
    const [statusesById, setStatusesById] = useState<any>({});

    const [supplierTypesById, setSupplierTypesById] = useState<any>({});
    const updateIndexes = (vehicles: any) => {
        const vehiclesArray = Array.isArray(vehicles) ? vehicles : [vehicles];

        const newVehiclesByResourceId: any = {};
        const newMakesById: any = {};
        const newModelsById: any = {};
        const newStatusesById: any = {};
        const newSupplierTypesById: any = {};

        vehiclesArray.forEach((vehicle: any) => {
            // Update index for vehicles
            newVehiclesByResourceId[vehicle?.resourceId] = vehicle;

            // Update index for makes & models
            newMakesById[vehicle?.make.id] = vehicle?.make;
            newModelsById[vehicle?.model.id] = vehicle?.model;

            // Update index for statuses
            const statusId = statuses[vehicle?.status as keyof typeof statuses] || statuses.UNKNOWN;
            newStatusesById[statusId] = {
                id: statusId,
                name: statusLabels[statusId]
            };

            // Update index for types
            const supplierTypeId = codesToSupplierTypes[vehicle?.supplierTypeCode] || SupplierTypes.UNKNOWN;
            newSupplierTypesById[supplierTypeId] = {
                id: supplierTypeId,
                name: supplierTypeCodeToLabels[supplierTypeId]
            };
        });

        setVehiclesByResourceId((prev: any) => ({ ...prev, ...newVehiclesByResourceId }));
        setMakesById((prev: any) => ({ ...prev, ...newMakesById }));
        setModelsById((prev: any) => ({ ...prev, ...newModelsById }));
        setStatusesById((prev: any) => ({ ...prev, ...newStatusesById }));
        setSupplierTypesById((prev: any) => ({ ...prev, ...newSupplierTypesById }));
    };

    const sortVehiclesBySLA = (vehicleA: any, vehicleB: any): number => {
        const statusOrder = [slaLevels.RED, slaLevels.AMBER, slaLevels.GREEN, slaLevels.UNKNOWN];
        const getStatusIndex = (status: string) => statusOrder.indexOf(status);

        const statusIndexA = getStatusIndex(vehicleA.sla.status);
        const statusIndexB = getStatusIndex(vehicleB.sla.status);

        if (statusIndexA !== statusIndexB) {
            return statusIndexA - statusIndexB;
        }

        const hasDaysA = typeof vehicleA.sla.days === 'number';
        const hasDaysB = typeof vehicleB.sla.days === 'number';

        if (hasDaysA && hasDaysB) {
            return vehicleB.sla.days - vehicleA.sla.days;
        }
        if (hasDaysA || hasDaysB) {
            return hasDaysA ? -1 : 1;
        }

        const hasHoursA = typeof vehicleA.sla.hours === 'number';
        const hasHoursB = typeof vehicleB.sla.hours === 'number';

        if (hasHoursA && hasHoursB) {
            return vehicleB.sla.hours - vehicleA.sla.hours;
        }
        if (hasHoursA || hasHoursB) {
            return hasHoursA ? -1 : 1;
        }

        return 0;
    };
    const setup = (vehicle: any) => {
        vehicle = normaliseData(vehicle);
        vehicle = setupVirtualVehicle(vehicle);
        vehicle = setupRetailerData(vehicle, suppliers);
        vehicle = setupVehicleDates(vehicle);
        const referenceDate = new Date();
        vehicle = setupSLAData(vehicle, referenceDate);
        return vehicle;
    };
    const [virtualResources, setVirtualResources] = useState<{ [key: string]: any }>({});
    const setupVirtualVehicle = (vehicle: any) => {
        if (!vehicle.resourceId || Object.values(supplierTypes).includes(vehicle.supplierTypeCode)) {
            if (!virtualResources[vehicle.resourceId]) {
                const generatedId = `TPS-VEHICLE-${vehicle.regNo}`;
                vehicle.resourceId = generatedId;
                setVirtualResources((prev) => ({ ...prev, [generatedId]: vehicle }));
            }
        }
        return vehicle;
    };
    const TPSVehicleStatusForTask = (task: any) => {
        if (task.status === statuses.GARR) {
            return statuses.GARR;
        } else {
            return statuses.GDET;
        }
    };
    const resolveTpsSupplierDesc = (supplierTypeCode: string, supplierTypeDesc: string): string | null => {
        return supplierTypeCode ? (supplierTypeDesc ? supplierTypeDesc : supplierTypeCode + ' Vehicle') : null;
    };
    const [tasksData, setTasksData] = useState<any[]>([]);
    const [selectedTask, setSelectedTask] = useState<any>();
    const [tasksById, setTasksById] = useState<{ [key: string]: any }>({});

    const isHire = (status: string) => {
        return ['HIRE', 'ARVD', 'GARR'].includes(status);
    };
    const [tasksOnHireById, setTasksOnHireById] = useState<{ [key: string]: any }>({});
    const updateOrAdd = (taskToUpdate: any) => {
        const index = tasksData.findIndex((task: any) => task.id === taskToUpdate.id);

        if (index > -1) {
            tasksData[index] = taskToUpdate;
            if (selectedTask && selectedTask.id === taskToUpdate.id) {
                setSelectedTask(taskToUpdate);
            }
        } else {
            setTasksData((prevTasks) => [...prevTasks, taskToUpdate]);
        }

        // set cache
        setTasksById((prevTasksById) => ({ ...prevTasksById, [taskToUpdate.id]: taskToUpdate }));
        tasksByResourceId[taskToUpdate.rental.hireVehicle.resourceId] = taskToUpdate;
        setTasksByResourceId?.(tasksByResourceId);
        if (isHire(taskToUpdate.status)) {
            setTasksOnHireById((prevTasksOnHireById) => ({
                ...prevTasksOnHireById,
                [taskToUpdate.id]: taskToUpdate
            }));
        }
    };
    //Ends
    const navigateMap = (location: any) => {
        setCenter({ lat: location?.latitude === undefined ? 0 : location?.latitude, lng: location?.longitude === undefined ? 0 : location?.longitude });
        setZoomValue(12);
    };

    useEffect(() => {
        if (isCenterMap) {
            setCenter({ lat: 55.07, lng: -6.0 });
            setZoomValue(6);
            setShowAccord(false);
            setTimeout(() => {
                setShowAccord(true);
            }, 0);
        }
    }, [isCenterMap]);

    useEffect(() => {
        const fetchExtensionHistory = async () => {
            if (taskId !== undefined) {
                try {
                    const extensionData = await getExtensionHistoryData(userData.authorization, taskId);
                    setExtensionHistory?.(extensionData);
                } catch (error) {
                    console.error('Error fetching extension history:', error);
                }
            } else {
                setExtensionHistory?.([]);
            }
        };

        fetchExtensionHistory();
    }, [taskId]);

    const [flag, setFlag] = useState<string>('');

    const getExtensionDetails = async (vehicleDt: any, isCCP: any, flag: string) => {
        setExtensionDetails(true);
        setFlag(flag);
        setTimeout(() => {
            document.body.style.pointerEvents = 'auto';
        }, 1000);
        if (vehicleDt?.id !== undefined) {
            const extHistData = await getExtensionHistory(vehicleDt?.id);
            if (extHistData) {
                setExtensionHistory?.(extHistData);
                setTskId?.({ ccp: isCCP, taskId: vehicleDt?.id, vehicleData: vehicleDt });
            }
        }
    };

    const getExtensionHistory = async (taskId: number) => {
        try {
            if (taskId !== undefined) {
                const extensionData = await getExtensionHistoryData(userData.authorization, taskId);
                //setExtensionHistory?.(extensionData);
                return extensionData;
            }
        } catch (error) {
            console.error('error', error);
        }
        return [];
    };

    const closeExt = (val: boolean) => {
        setExtensionDetails(val);
    };

    const getMobilityTaskData = useMemo(() => {
        if (mobiltyTaskStore !== undefined) {
            return mobiltyTaskStore
                .map((task: any) => {
                    task = setupRafVehicle(task);
                    const incomingTask = task;
                    task = task.hireTask;
                    task.appointment = incomingTask.hireAppointment;
                    task.schedule = incomingTask.vehicleSchedule;
                    task.status = incomingTask.status;
                    if (thirdPartyHireTask(task)) {
                        task = setupThirdPartyHireTaskMobility(task);
                    }
                    if (!hasValidDataMobility(task)) {
                        return null;
                    }
                    updateOrAdd(task);
                    return task;
                })
                .filter((task: any) => task !== null);
        }
        return [];
    }, [mobiltyTaskStore]);

    return (
        <div className="flex flex-wrap h-screen main-content">
            <div
                className={`flex-grow-3 flex-3 ${filterClick ? 'flexBasis580' : 'flexBasis372'}`}
                style={{ maxWidth: '100%', flexShrink: 0 }}
            >
                <div className="bg-white shadow rounded-lg h-full">
                    <div className="flex h-full">
                        <div
                            id="filterCol"
                            className={`${filterClick ? 'flexBasis280 bg-[#f3f3f3]' : 'flexBasis70 bg-[#4a4a4a]'}`}
                            style={{ maxWidth: '100%', flexShrink: 0 }}
                        >
                            {searchVisible && (
                                <FilterLeft
                                    region={regionData}
                                    make={make}
                                    model={model}
                                    rafData={rafData}
                                    retailers={retailers}
                                    selectedResourceValue={selectedResourceValue}
                                    filterClick={(data: ClickValue) => handleFilterClick(data)}
                                    filterVallue={(data: string) => globalFilterAllValue(data)}
                                    clearFilters={(data: string) => clearGlobalFilter(data)}
                                    data={filteredData}
                                    extensionData={repairingRetailer}
                                    fleetGroupData={fleetGroupData}
                                />
                            )}
                            {!searchVisible && (
                                <>
                                    <div
                                        className="relative menu-item"
                                        onClick={filterToggle}
                                    >
                                        <Icon
                                            icon="search"
                                            style={{ width: '25px', height: '25px' }}
                                            color="#a7a9ac"
                                            className="bigSearchIcon"
                                        />
                                    </div>
                                    <div className="bottom-0 absolute version">v{packageJson.version}</div>
                                </>
                            )}
                        </div>
                        <div
                            className="w-9/10"
                            style={{ boxShadow: '0 6px 5px 2px #0000001c' }}
                        >
                            <div className="vehicle-container">
                                <div className="p-[15px]">
                                    <div className="relative">
                                        <select
                                            id="selectResource"
                                            onChange={(e) => {
                                                e.stopPropagation();
                                                selectedResources(e.target.value);
                                            }}
                                            className='block bg-[white] border focus-visible:border border-gray-300 focus:border-blue-500 focus-visible:rounded-none focus-visible:outline-none" focus:ring-blue-500 w-full h-[30px] text-sm'
                                        >
                                            <option value="status">Resources by Status</option>
                                            <option value="retailer">Resources by Owning Retailer</option>
                                            <option value="extension">Extensions by Repairing Retailer</option>
                                        </select>
                                    </div>
                                </div>
                                <section className="">
                                    <div className="fleet-vehicle-group">
                                        {selectedResourceValue === 'status' && noDataFound === false && showAccord && (
                                            <Accordion
                                                type="single"
                                                collapsible
                                                className="w-full"
                                            >
                                                {filteredData?.map((group: any, index: number) => (
                                                    <AccordionItem
                                                        value={`item-${index}`}
                                                        key={`value-${index}`}
                                                        className={group.redSlaCount > 0 ? 'highlight' : ''}
                                                    >
                                                        <AccordionTriggerFleet>
                                                            <span>{group.groupName}</span>
                                                            <div className={`mr-3 ml-auto ${group.redSlaCount > 0 ? 'small' : ''}`}>
                                                                <div className="m-auto sla">{group.redSlaCount > 0 && <span className="days">{group.redSlaCount}</span>}</div>
                                                            </div>
                                                            <div className="fleet-quantity">{group.resourcesData.length}</div>
                                                        </AccordionTriggerFleet>

                                                        <AccordionContent>
                                                            {group.resourcesData.map((vehicle: any, idx: number) => (
                                                                <div
                                                                    key={`innerItem-${idx}`}
                                                                    onClick={(e) => {
                                                                        e.preventDefault();
                                                                        availableVehicleClick(vehicle.resourceId, group.groupName, vehicle);
                                                                    }}
                                                                    className="border-top-accord fleet-vehicle-details-container"
                                                                    title={`Resource id: ${vehicle.resourceId}`}
                                                                >
                                                                    <div className={`fleet-vehicle-details-wrapper ${vehicle.resourceId === resID ? 'selected' : ''}`}>
                                                                        <div className={`fleet-vehicle-${group?.groupName?.toLowerCase().replace(/\s+/g, '-')} fleet-vehicle-status`}></div>
                                                                        <div
                                                                            className="fleet-vehicle-details"
                                                                            onClick={(e) => {
                                                                                e.preventDefault();
                                                                                openInformationPanel(vehicle.resourceId);
                                                                                navigateMap(vehicle.lastKnownLocation);
                                                                            }}
                                                                        >
                                                                            <div className="resource-header">
                                                                                <span className="font-bold text-[12px] separate">{vehicle?.regNo}</span>
                                                                                <span
                                                                                    className={`resource-type ${
                                                                                        (group.groupName === 'Booked' || group.groupName === 'On Hire') &&
                                                                                        (vehicle?.sla?.days !== '?' || vehicle?.sla?.hours !== undefined)
                                                                                            ? 'separate'
                                                                                            : ''
                                                                                    }`}
                                                                                >
                                                                                    {vehicle?.supplierTypeDesc?.split(' ')[0] === 'Hyundai' ? 'HAF' : vehicle?.supplierTypeDesc?.split(' ')[0]}
                                                                                </span>
                                                                                {(group.groupName === 'Booked' || group.groupName === 'On Hire') &&
                                                                                    (vehicle?.sla?.days !== '?' || vehicle?.sla?.hours !== undefined) && (
                                                                                        <span className="resource-type">
                                                                                            {vehicle?.customerGroup === 'HYUA'
                                                                                                ? 'HYU'
                                                                                                : vehicle.status === 'GARR'
                                                                                                ? vehicle?.supNetworkCode
                                                                                                : vehicle?.supNetworkCode === 'HYUA'
                                                                                                ? 'HYU'
                                                                                                : vehicle?.customerGroup}
                                                                                        </span>
                                                                                    )}
                                                                            </div>
                                                                            <div className="vehicle-details-accord">
                                                                                <span className="vehicle-make">
                                                                                    {vehicle?.make?.name
                                                                                        .split(' ')
                                                                                        .map((word: any) => word.charAt(0)?.toUpperCase() + word.slice(1))
                                                                                        .join(' ')}
                                                                                </span>
                                                                                <span className="ml-1 vehicle-model">
                                                                                    {vehicle?.model?.name
                                                                                        .split(' ')
                                                                                        .map((word: any) => word.charAt(0)?.toUpperCase() + word.slice(1))
                                                                                        .join(' ')}
                                                                                </span>
                                                                            </div>
                                                                            <div className="retailer-details">
                                                                                <span className="retailer-postcode separate">{vehicle?.retailer?.postcode}</span>
                                                                                <span className="">
                                                                                    {vehicle?.retailer?.name
                                                                                        ?.split(' ')
                                                                                        .map((word: any) => word.charAt(0)?.toUpperCase() + word.slice(1))
                                                                                        .join(' ')}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                        {vehicle?.sla?.days || vehicle?.sla?.hours ? (
                                                                            <div className={`${vehicle?.sla?.status?.toLowerCase()} sla-wrapper`}>
                                                                                <div className="m-auto sla">
                                                                                    <span className="days">
                                                                                        {vehicle?.sla?.days
                                                                                            ? vehicle.sla.days === '?'
                                                                                                ? '?'
                                                                                                : `${vehicle.sla.days}d`
                                                                                            : vehicle?.sla?.hours
                                                                                            ? `${vehicle.sla.hours}h`
                                                                                            : '?'}
                                                                                    </span>
                                                                                </div>
                                                                            </div>
                                                                        ) : null}
                                                                    </div>
                                                                </div>
                                                            ))}
                                                        </AccordionContent>
                                                    </AccordionItem>
                                                ))}
                                            </Accordion>
                                        )}
                                        {selectedResourceValue === 'retailer' && noDataFound === false && (
                                            <Accordion
                                                type="single"
                                                collapsible
                                                className="w-full"
                                            >
                                                {owningRetailer.map((group: any, index: number) => (
                                                    <AccordionItem
                                                        id="owningRetailerAccord"
                                                        value={`item-${index}`}
                                                        key={`value-${index}`}
                                                    >
                                                        <AccordionTriggerFleet>
                                                            <span>{group?.name?.toUpperCase()}</span>
                                                            <div className={`mr-3 ml-auto ${group.redSlaCount > 0 ? 'small' : ''}`}>
                                                                <div className="m-auto sla">{group.redSlaCount > 0 && <span className="days">{group.redSlaCount}</span>}</div>
                                                            </div>
                                                            <div className="fleet-quantity">{group.resourcesData.length}</div>
                                                        </AccordionTriggerFleet>

                                                        <AccordionContent>
                                                            {group.resourcesData.map((vehicle: any, idx: number) => (
                                                                <div
                                                                    key={`innerItem-${idx}`}
                                                                    onClick={(e) => {
                                                                        e.preventDefault();
                                                                        availableVehicleClick(vehicle.resourceId, vehicle.status, vehicle, 'owningRetailer');
                                                                    }}
                                                                    className="border-top-accord fleet-vehicle-details-container"
                                                                    title={`Resource id: ${vehicle.resourceId}`}
                                                                >
                                                                    <div
                                                                        className={`fleet-vehicle-details-wrapper ${vehicle.resourceId === resID ? 'selected' : ''}`}
                                                                        style={{ height: '53.42px' }}
                                                                    >
                                                                        <div
                                                                            className={`fleet-vehicle-${
                                                                                vehicle.status === 'MAIN'
                                                                                    ? 'maintenance'
                                                                                    : vehicle.status === 'ARVD' || vehicle.status === 'GARR' || vehicle.status === 'HIRE'
                                                                                    ? 'on-hire'
                                                                                    : vehicle.status === 'VLET'
                                                                                    ? 'valet'
                                                                                    : vehicle.status === 'DFLT'
                                                                                    ? 'defleet'
                                                                                    : vehicle.status === 'HEAD'
                                                                                    ? 'booked'
                                                                                    : vehicle.status === 'FREE'
                                                                                    ? 'available'
                                                                                    : ''
                                                                            } fleet-vehicle-status`}
                                                                        ></div>
                                                                        <div
                                                                            className="fleet-vehicle-details"
                                                                            onClick={(e) => {
                                                                                e.preventDefault();
                                                                                openInformationPanel(vehicle.id);
                                                                                navigateMap(vehicle.lastKnownLocation);
                                                                            }}
                                                                        >
                                                                            <div className="resource-header">
                                                                                <span className="font-bold text-[12px] separate">{vehicle?.regNo}</span>
                                                                                <span
                                                                                    className={`resource-type ${
                                                                                        vehicle.status === 'HEAD' ||
                                                                                        vehicle.status === 'GDET' ||
                                                                                        vehicle.status === 'BUSY' ||
                                                                                        vehicle.status === 'ARVD' ||
                                                                                        vehicle.status === 'HIRE' ||
                                                                                        vehicle.status === 'GARR'
                                                                                            ? 'separate'
                                                                                            : ''
                                                                                    }`}
                                                                                >
                                                                                    {vehicle?.supplierTypeDesc?.split(' ')[0]}
                                                                                </span>
                                                                                {(vehicle.status === 'HEAD' ||
                                                                                    vehicle.status === 'GDET' ||
                                                                                    vehicle.status === 'BUSY' ||
                                                                                    vehicle.status === 'ARVD' ||
                                                                                    vehicle.status === 'HIRE' ||
                                                                                    vehicle.status === 'GARR') && <span className="resource-type">{vehicle?.customerGroup}</span>}
                                                                            </div>
                                                                            <div className="vehicle-details-accord">
                                                                                <span className="vehicle-make">
                                                                                    {vehicle?.make?.name
                                                                                        ?.split(' ')
                                                                                        .map((word: any) => word.charAt(0)?.toUpperCase() + word.slice(1))
                                                                                        .join(' ')}
                                                                                </span>
                                                                                <span className="ml-1 vehicle-model">
                                                                                    {vehicle?.model?.name
                                                                                        ?.split(' ')
                                                                                        .map((word: any) => word.charAt(0)?.toUpperCase() + word.slice(1))
                                                                                        .join(' ')}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                        {vehicle?.sla?.days || vehicle?.sla?.hours ? (
                                                                            <div className={`${vehicle?.sla?.status?.toLowerCase()} sla-wrapper`}>
                                                                                <div className="m-auto sla">
                                                                                    <span className="days">
                                                                                        {vehicle?.sla?.days
                                                                                            ? vehicle.sla.days === '?'
                                                                                                ? '?'
                                                                                                : `${vehicle.sla.days}d`
                                                                                            : vehicle?.sla?.hours
                                                                                            ? `${vehicle.sla.hours}h`
                                                                                            : '?'}
                                                                                    </span>
                                                                                </div>
                                                                            </div>
                                                                        ) : null}
                                                                    </div>
                                                                </div>
                                                            ))}
                                                        </AccordionContent>
                                                    </AccordionItem>
                                                ))}
                                            </Accordion>
                                        )}
                                        {selectedResourceValue === 'extension' && noDataFound === false && (
                                            <>
                                                <Tabs
                                                    defaultValue="all"
                                                    className="w-[400px]"
                                                >
                                                    <TabsList>
                                                        <TabsTrigger
                                                            value="all"
                                                            onClick={() => {
                                                                repairingRetailerSelected('all');
                                                                setExtensionDetails(false);
                                                            }}
                                                        >
                                                            <div className="label">All</div>
                                                            <div className="fleet-quantity">{repairingRetailerCount.all}</div>
                                                        </TabsTrigger>
                                                        <TabsTrigger
                                                            value="requested"
                                                            onClick={() => {
                                                                repairingRetailerSelected('requested');
                                                                setExtensionDetails(false);
                                                            }}
                                                        >
                                                            <div className="label">Requested</div>
                                                            <div className="fleet-quantity">{repairingRetailerCount.requested}</div>
                                                        </TabsTrigger>
                                                        <TabsTrigger
                                                            value="approved"
                                                            onClick={() => {
                                                                repairingRetailerSelected('approved');
                                                                setExtensionDetails(false);
                                                            }}
                                                        >
                                                            <div className="label">Approved</div>
                                                            <div className="fleet-quantity">{repairingRetailerCount.approved}</div>
                                                        </TabsTrigger>
                                                        <TabsTrigger
                                                            value="expected"
                                                            onClick={() => {
                                                                repairingRetailerSelected('expected');
                                                                setExtensionDetails(false);
                                                            }}
                                                        >
                                                            <div className="label">Expected</div>
                                                            <div className="fleet-quantity">{repairingRetailerCount.expected}</div>
                                                        </TabsTrigger>
                                                    </TabsList>
                                                    <TabsContent value="all">
                                                        <Accordion
                                                            type="single"
                                                            collapsible
                                                            className="w-full"
                                                        >
                                                            {repairingRetailer.map((group: any, index: number) => (
                                                                <AccordionItem
                                                                    value={`item-${index}`}
                                                                    key={`value-${index}`}
                                                                >
                                                                    <AccordionTriggerFleet>
                                                                        <span>{group.groupName}</span>
                                                                        <div className={`mr-3 ml-auto ${group.redSlaCount > 0 ? 'small' : ''}`}>
                                                                            <div className="m-auto sla">{group.redSlaCount > 0 && <span className="days">{group.redSlaCount}</span>}</div>
                                                                        </div>
                                                                        <div className="fleet-quantity">{group.resourcesData.length}</div>
                                                                    </AccordionTriggerFleet>

                                                                    <AccordionContent>
                                                                        {group.resourcesData.map((vehicle: any, idx: number) => (
                                                                            <div
                                                                                title={`task id : ${vehicle.id}| Extension id : ${vehicle.extensionId ? vehicle.extensionId : 'not requested'}`}
                                                                                key={`innerItem-${idx}`}
                                                                                onClick={(e) => {
                                                                                    e.preventDefault();
                                                                                }}
                                                                                className="border-top-accord fleet-vehicle-details-container"
                                                                            >
                                                                                <div className={`fleet-vehicle-details-wrapper ${vehicle.resourceId === resID ? 'selected' : ''}`}>
                                                                                    <div className={`${vehicle.vehicleStatus} fleet-vehicle-status`}></div>
                                                                                    <div
                                                                                        className="fleet-vehicle-details"
                                                                                        onClick={(e) => {
                                                                                            e.preventDefault();
                                                                                            //         openInformationPanel(vehicle.id);
                                                                                            navigateMap(vehicle.lastKnownLocation);
                                                                                        }}
                                                                                    >
                                                                                        <div className="resource-header">
                                                                                            <span className="font-bold text-[12px] separate">{vehicle?.regNo}</span>
                                                                                            <span className="resource-type separate">{vehicle?.supplierTypeDesc?.split(' ')[0]}</span>
                                                                                            <span className="font-regular text-[12px] separate">{vehicle?.supNetworkCode}</span>

                                                                                            {(vehicle?.customerCode === 'JAGA' || vehicle?.customerCode === 'LANE') && (
                                                                                                <span className="font-regular text-[12px] text-red-500">CCP</span>
                                                                                            )}
                                                                                            {(group.groupName === 'Booked' || group.groupName === 'On Hire') && (
                                                                                                <span className="resource-type">{vehicle?.supNetworkCode}</span>
                                                                                            )}
                                                                                        </div>
                                                                                        <div className="vehicle-details-accord">
                                                                                            <span className="vehicle-make">{vehicle?.make?.name}</span>
                                                                                            <span className="ml-1 vehicle-model">{vehicle.model.name}</span>
                                                                                        </div>
                                                                                        <div className="vehicle-details-accord">
                                                                                            <span className="separate vehicle-make">{vehicle?.registration}</span>
                                                                                            <span className="ml-1 vehicle-model">{vehicle.contactName}</span>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        ))}
                                                                    </AccordionContent>
                                                                </AccordionItem>
                                                            ))}
                                                        </Accordion>
                                                    </TabsContent>
                                                    <TabsContent value="expected">
                                                        <Accordion
                                                            type="single"
                                                            collapsible
                                                            className="w-full"
                                                        >
                                                            {repairingRetailer.map((group: any, index: number) => (
                                                                <AccordionItem
                                                                    value={`item-${index}`}
                                                                    key={`value-${index}`}
                                                                >
                                                                    <AccordionTriggerFleet>
                                                                        <span>{group.groupName}</span>
                                                                        <div className={`mr-3 ml-auto ${group.redSlaCount > 0 ? 'small' : ''}`}>
                                                                            <div className="m-auto sla">{group.redSlaCount > 0 && <span className="days">{group.redSlaCount}</span>}</div>
                                                                        </div>
                                                                        <div className="fleet-quantity">{group.resourcesData.length}</div>
                                                                    </AccordionTriggerFleet>

                                                                    <AccordionContent>
                                                                        {group.resourcesData.map((vehicle: any, idx: number) => (
                                                                            <div
                                                                                key={`innerItem-${idx}`}
                                                                                onClick={(e) => {
                                                                                    e.preventDefault();
                                                                                    openInformationPanel(vehicle.id);
                                                                                    navigateMap(vehicle.lastKnownLocation);
                                                                                }}
                                                                                className="border-top-accord fleet-vehicle-details-container"
                                                                                title={`Task id : ${vehicle.id}| Extension id : not requested`}
                                                                            >
                                                                                <div className={`fleet-vehicle-details-wrapper ${vehicle.resourceId === resID ? 'selected' : ''}`}>
                                                                                    <div className={`${vehicle.vehicleStatus} fleet-vehicle-status`}></div>
                                                                                    <div
                                                                                        className="fleet-vehicle-details"
                                                                                        onClick={(e) => {
                                                                                            e.preventDefault();
                                                                                            // openHistoryPanel(vehicle.id);
                                                                                        }}
                                                                                    >
                                                                                        <div className="resource-header">
                                                                                            <span className="font-bold text-[12px] separate">{vehicle?.regNo}</span>
                                                                                            <span className="resource-type separate">{vehicle?.supplierTypeDesc?.split(' ')[0]}</span>
                                                                                            <span className="font-regular text-[12px] separate">{vehicle?.supNetworkCode}</span>

                                                                                            {(vehicle?.customerCode === 'JAGA' || vehicle?.customerCode === 'LANE') && (
                                                                                                <span className="font-regular text-[12px] text-red-500">CCP</span>
                                                                                            )}
                                                                                            {(group.groupName === 'Booked' || group.groupName === 'On Hire') && (
                                                                                                <span className="resource-type">{vehicle?.supNetworkCode}</span>
                                                                                            )}
                                                                                        </div>
                                                                                        <div className="vehicle-details-accord">
                                                                                            <span className="vehicle-make">{vehicle?.make?.name}</span>
                                                                                            <span className="ml-1 vehicle-model">{vehicle.model.name}</span>
                                                                                        </div>
                                                                                        <div className="vehicle-details-accord">
                                                                                            <span className="separate vehicle-make">{vehicle?.registration}</span>
                                                                                            <span className="ml-1 vehicle-model">{vehicle.contactName}</span>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        ))}
                                                                    </AccordionContent>
                                                                </AccordionItem>
                                                            ))}
                                                        </Accordion>
                                                    </TabsContent>
                                                    <TabsContent value="requested">
                                                        <Accordion
                                                            type="single"
                                                            collapsible
                                                            className="w-full"
                                                        >
                                                            {repairingRetailer.length === 0 && <div className="no-extension-available">No Extension Found</div>}
                                                            {repairingRetailer.map((group: any, index: number) => (
                                                                <AccordionItem
                                                                    value={`item-${index}`}
                                                                    key={`value-${index}`}
                                                                >
                                                                    <AccordionTriggerFleet>
                                                                        <span>{group.groupName}</span>
                                                                        <div className={`mr-3 ml-auto ${group.redSlaCount > 0 ? 'small' : ''}`}>
                                                                            <div className="m-auto sla">{group.redSlaCount > 0 && <span className="days">{group.redSlaCount}</span>}</div>
                                                                        </div>
                                                                        <div className="fleet-quantity">{group.resourcesData.length}</div>
                                                                    </AccordionTriggerFleet>

                                                                    <AccordionContent>
                                                                        {group.resourcesData.map((vehicle: any, idx: number) => (
                                                                            <div
                                                                                key={`innerItem-${idx}`}
                                                                                onClick={(e) => {
                                                                                    e.preventDefault();
                                                                                    availableVehicleClick(vehicle.resourceId, vehicle.status, vehicle, 'repairingRetailer');
                                                                                    let isCCPFlag = '';
                                                                                    vehicle?.customerCode === 'JAGA' || vehicle?.customerCode === 'LANE' ? (isCCPFlag = 'CCP') : (isCCPFlag = '');
                                                                                    getExtensionDetails(vehicle, isCCPFlag, 'requested');
                                                                                }}
                                                                                className="border-top-accord fleet-vehicle-details-container"
                                                                                title={`Task id : ${vehicle.id}| Extension id : ${vehicle.extensionId}`}
                                                                            >
                                                                                <div className={`fleet-vehicle-details-wrapper ${vehicle.resourceId === resID ? 'selected' : ''}`}>
                                                                                    <div className={`${vehicle.vehicleStatus} fleet-vehicle-status`}></div>
                                                                                    <div
                                                                                        className="fleet-vehicle-details"
                                                                                        onClick={(e) => {
                                                                                            e.preventDefault();
                                                                                            openHistoryPanel(vehicle.id);
                                                                                        }}
                                                                                    >
                                                                                        <div className="resource-header">
                                                                                            <span className="font-bold text-[12px] separate">{vehicle?.regNo}</span>
                                                                                            <span className="resource-type separate">{vehicle?.supplierTypeDesc?.split(' ')[0]}</span>
                                                                                            <span className="font-regular text-[12px] separate">{vehicle?.supNetworkCode}</span>

                                                                                            {(vehicle?.customerCode === 'JAGA' || vehicle?.customerCode === 'LANE') && (
                                                                                                <span className="font-regular text-[12px] text-red-500">CCP</span>
                                                                                            )}
                                                                                            {(group.groupName === 'Booked' || group.groupName === 'On Hire') && (
                                                                                                <span className="resource-type">{vehicle?.supNetworkCode}</span>
                                                                                            )}
                                                                                        </div>
                                                                                        <div className="vehicle-details-accord">
                                                                                            <span className="vehicle-make">{vehicle?.make?.name}</span>
                                                                                            <span className="ml-1 vehicle-model">{vehicle.model.name}</span>
                                                                                        </div>
                                                                                        <div className="vehicle-details-accord">
                                                                                            <span className="separate vehicle-make">{vehicle?.registration}</span>
                                                                                            <span className="ml-1 vehicle-model">{vehicle.contactName}</span>
                                                                                        </div>
                                                                                        <div className="vehicle-details-accord">
                                                                                            <span className="separate vehicle-make">{vehicle?.extensionName}</span>
                                                                                        </div>
                                                                                    </div>
                                                                                    {/* <div className="unknown sla-wrapper"><div className="m-auto sla"><span className="days">?</span></div></div> */}
                                                                                    <div className="unknown sla-wrapper">
                                                                                        <div className="m-auto sla">
                                                                                            <span className="days reqExtension">?d</span>
                                                                                            <span className="type">VOR</span>
                                                                                        </div>
                                                                                    </div>
                                                                                    {/* {vehicle?.sla?.days || vehicle?.sla?.hours ? (
                                                                                        <div className={`${vehicle?.sla?.status?.toLowerCase()} sla-wrapper`}>
                                                                                            <div className="m-auto sla">
                                                                                                <span className="days">
                                                                                                    {vehicle?.sla?.days
                                                                                                        ? vehicle.sla.days === '?'
                                                                                                            ? '?'
                                                                                                            : `${vehicle.sla.days}d`
                                                                                                        : vehicle?.sla?.hours
                                                                                                        ? `${vehicle.sla.hours}h`
                                                                                                        : '?'}
                                                                                                </span>
                                                                                            </div>
                                                                                        </div>
                                                                                    ) : null} */}
                                                                                </div>
                                                                            </div>
                                                                        ))}
                                                                    </AccordionContent>
                                                                </AccordionItem>
                                                            ))}
                                                        </Accordion>
                                                    </TabsContent>
                                                    <TabsContent value="approved">
                                                        <Accordion
                                                            type="single"
                                                            collapsible
                                                            className="w-full"
                                                        >
                                                            {repairingRetailer.length === 0 && <div className="no-extension-available">No Extension Found</div>}
                                                            {repairingRetailer.map((group: any, index: number) => (
                                                                <AccordionItem
                                                                    value={`item-${index}`}
                                                                    key={`value-${index}`}
                                                                >
                                                                    <AccordionTriggerFleet>
                                                                        <span>{group.groupName}</span>
                                                                        <div className={`mr-3 ml-auto ${group.redSlaCount > 0 ? 'small' : ''}`}>
                                                                            <div className="m-auto sla">{group.redSlaCount > 0 && <span className="days">{group.redSlaCount}</span>}</div>
                                                                        </div>
                                                                        <div className="fleet-quantity">{group.resourcesData.length}</div>
                                                                    </AccordionTriggerFleet>

                                                                    <AccordionContent>
                                                                        {group.resourcesData.map((vehicle: any, idx: number) => (
                                                                            <div
                                                                                key={`innerItem-${idx}`}
                                                                                onClick={(e) => {
                                                                                    e.preventDefault();
                                                                                    availableVehicleClick(vehicle.resourceId, vehicle.status, vehicle, 'repairingRetailer');
                                                                                    let isCCPFlag = '';
                                                                                    vehicle?.customerCode === 'JAGA' || vehicle?.customerCode === 'LANE' ? (isCCPFlag = 'CCP') : (isCCPFlag = '');
                                                                                    getExtensionDetails(vehicle, isCCPFlag, 'approved');
                                                                                }}
                                                                                className="border-top-accord fleet-vehicle-details-container"
                                                                                title={`Task id : ${vehicle.id}| Extension id : ${vehicle.extensionId}`}
                                                                            >
                                                                                <div className={`fleet-vehicle-details-wrapper ${vehicle.resourceId === resID ? 'selected' : ''}`}>
                                                                                    <div className={`${vehicle.vehicleStatus} fleet-vehicle-status`}></div>
                                                                                    <div
                                                                                        className="fleet-vehicle-details"
                                                                                        onClick={(e) => {
                                                                                            e.preventDefault();
                                                                                            openHistoryPanel(vehicle.id);
                                                                                        }}
                                                                                    >
                                                                                        <div className="resource-header">
                                                                                            <span className="font-bold text-[12px] separate">{vehicle?.regNo}</span>
                                                                                            <span className="resource-type separate">{vehicle?.supplierTypeDesc?.split(' ')[0]}</span>
                                                                                            <span className="font-regular text-[12px] separate">{vehicle?.supNetworkCode}</span>

                                                                                            {(vehicle?.customerCode === 'JAGA' || vehicle?.customerCode === 'LANE') && (
                                                                                                <span className="font-regular text-[12px] text-red-500">CCP</span>
                                                                                            )}
                                                                                            {(group.groupName === 'Booked' || group.groupName === 'On Hire') && (
                                                                                                <span className="resource-type">{vehicle?.supNetworkCode}</span>
                                                                                            )}
                                                                                        </div>
                                                                                        <div className="vehicle-details-accord">
                                                                                            <span className="vehicle-make">{vehicle?.make?.name}</span>
                                                                                            <span className="ml-1 vehicle-model">{vehicle.model.name}</span>
                                                                                        </div>
                                                                                        <div className="vehicle-details-accord">
                                                                                            <span className="separate vehicle-make">{vehicle?.registration}</span>
                                                                                            <span className="ml-1 vehicle-model">{vehicle.contactName}</span>
                                                                                        </div>
                                                                                        <div className="vehicle-details-accord">
                                                                                            <span className="separate vehicle-make">{vehicle?.extensionName}</span>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        ))}
                                                                    </AccordionContent>
                                                                </AccordionItem>
                                                            ))}
                                                        </Accordion>
                                                    </TabsContent>
                                                </Tabs>
                                            </>
                                        )}
                                        {noDataFound === true && (
                                            <section className="w-full">
                                                <div className="no-records-found">No resources found</div>
                                            </section>
                                        )}
                                    </div>
                                </section>
                            </div>
                            {/* <!-- Counter --> */}
                            <div id="fleetVehiclesCounterContainer">
                                <span id="fleetVehiclesCounter">
                                    {selectedResourceValue === 'status' && (
                                        <>
                                            Results: <span>{filteredData.flatMap((group: any) => group.resourcesData).length}</span> of{' '}
                                            <span>{fleetGroupData.flatMap((group: any) => group.resourcesData).length}</span>
                                        </>
                                    )}
                                    {selectedResourceValue === 'retailer' && (
                                        <>
                                            Results: <span>{owningRetailer.flatMap((group: any) => group.resourcesData).length}</span> of{' '}
                                            <span>{totalOwningRetailer.flatMap((group: any) => group.resourcesData).length}</span>
                                        </>
                                    )}
                                    {selectedResourceValue === 'extension' && (
                                        <>
                                            Results: <span>{repairingRetailer.flatMap((group: any) => group.resourcesData).length}</span> of{' '}
                                            <span>
                                                {selectedExtensionTab === 'all' && totalRepairingRetailerCount.all}
                                                {selectedExtensionTab === 'requested' && totalRepairingRetailerCount.requested}
                                                {selectedExtensionTab === 'approved' && totalRepairingRetailerCount.approved}
                                                {selectedExtensionTab === 'expected' && totalRepairingRetailerCount.expected}
                                            </span>
                                        </>
                                    )}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="flex-grow-1 flex-1">
                <div className="bg-white shadow rounded-lg h-full">
                    {extensionDetails && (
                        <DialogWithAccordian
                            isOpen={extensionDetails}
                            flag={flag}
                            closeExtenstion={(val: boolean) => closeExt(val)}
                        />
                    )}
                    {center && (
                        <APIProvider apiKey={'AIzaSyDu2_ls_B_2CC101pjFQMJRNo4HbhxrhrI'}>
                            <Map
                                onZoomChanged={(zoom: any) => {
                                    setZoomValue(zoom);
                                }}
                                onCameraChanged={(data: any) => {
                                    setCenter({ lat: data.detail.center.lat, lng: data.detail.center.lng });
                                    setIsCenterMap?.(false);
                                }}
                                defaultZoom={zoomValue}
                                gestureHandling={'greedy'}
                                zoom={zoomValue}
                                mapId="c6d3fd7283bdab2d"
                                scrollwheel={scrollWheelValue}
                                center={center}
                            >
                                <Markers points={mapMarkers} />
                            </Map>
                        </APIProvider>
                    )}
                </div>
                {/* <DialogWithAccordian isOpen={true} /> */}
            </div>
            {activeMenu && (
                <div className="right-panel">
                    {activeMenu === 'notification' && (
                        <div>
                            <div className="notification-container">
                                <div className="fleet-tab">
                                    {/* Notification Component */}
                                    <RightNotification clickedValueFleet={clickedValueFleet} />
                                </div>
                            </div>
                        </div>
                    )}
                    {activeMenu === 'information' && (
                        <div
                            id="informationMainDiv"
                            className="infoContainer"
                        >
                            <InformationRightProvider
                                clickedValue={clickedValueFleet}
                                fleetSelected={fleetSelected}
                                vehicleData={vehicleData}
                                resourceBy={resourceBy}
                                setTaskId={setTaskId}
                                taskId={taskId}
                            />
                        </div>
                    )}
                    {activeMenu === 'timer' && (
                        <div>
                            <div className="notification-container">
                                <div className="fleet-tab">
                                    {/* Notification Component */}
                                    <History
                                        resourceID={resID}
                                        resourceData={resourceData}
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}
type Point = google.maps.LatLngLiteral & { key: string } & { name: string } & { regNos: Array<string> };
type Props = { points: Point[] };
