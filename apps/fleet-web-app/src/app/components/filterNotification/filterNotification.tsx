import React, { useCallback, useEffect } from 'react';
import { Checkbox } from '../ui/checkbox';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import calender from '../../../assets/fleet-images/calender.png';

interface FilterNotificationProps {
    checkBoxData: {
        all: boolean;
        allocateVehicle: boolean;
        thirdPartyCheckin: boolean;
        trackerDisabled: boolean;
        unauthorizedVehicleUse: boolean;
    };
    setCheckBoxData: (checkBoxData: checkBoxData) => void;
    toggleFilterView: (action: string) => void;
    filterDataOnDate: any;
    setFilterDataOnDate: React.Dispatch<React.SetStateAction<any>>;
    setChange: React.Dispatch<React.SetStateAction<boolean>>;
    setCheckChange?: React.Dispatch<React.SetStateAction<any>>;
    isFilter: boolean;
    setIsFilter: React.Dispatch<React.SetStateAction<boolean>>;
    setDateChange: React.Dispatch<React.SetStateAction<boolean>>;
}
interface checkBoxData {
    all: boolean;
    allocateVehicle: boolean;
    thirdPartyCheckin: boolean;
    trackerDisabled: boolean;
    unauthorizedVehicleUse: boolean;
}

const FilterNotification: React.FC<FilterNotificationProps> = ({
    checkBoxData,
    setCheckBoxData,
    toggleFilterView,
    filterDataOnDate,
    setFilterDataOnDate,
    setChange,
    setCheckChange,
    isFilter,
    setIsFilter,
    setDateChange
}) => {
    const [DateFrom, setDateFrom] = React.useState(new Date(new Date().setDate(new Date().getDate() - 7)));
    const [DateTo, setDateTo] = React.useState(new Date());
    const [calendarOpen, setCalendarOpen] = React.useState({ from: false, to: false });
    const handleApply = () => {
        if (setCheckChange) {
            setCheckChange(
                [data.allocateVehicle ? 0 : null, data.thirdPartyCheckin ? 2 : null, data.trackerDisabled ? 3 : null, data.unauthorizedVehicleUse ? 4 : null].filter((item) => item !== null)
            );
        }
        toggleFilterView('applyFilter');
        setFilterDataOnDate({ dateFrom: DateFrom.toISOString().split('T')[0], dateTo: DateTo.toISOString().split('T')[0] });
        setCheckBoxData(data);
        setIsFilter(true);
    };

    useEffect(() => {
        if (!isFilter) {
            setDateFrom(new Date(new Date().setDate(new Date().getDate() - 7)));
            setDateTo(new Date());
            setCalendarOpen({ from: false, to: false });
            setChange(false);
            setCheckBoxData({
                all: true,
                allocateVehicle: true,
                thirdPartyCheckin: true,
                trackerDisabled: true,
                unauthorizedVehicleUse: true
            });
        }
    }, [isFilter]);

    //   useEffect(() => {
    //         if (!isFilter) {
    //             setFilterDataOnDate({
    //                 dateFrom: new Date(new Date().setDate(new Date().getDate() - 7)).getTime(),
    //                 dateTo: new Date().getTime(),
    //             });
    //             setCheckBoxData({
    //                 all: true,
    //                 allocateVehicle: true,
    //                 thirdPartyCheckin: true,
    //                 trackerDisabled: true,
    //                 unauthorizedVehicleUse: true,
    //             });
    //         }
    //     }, [isFilter, setCheckBoxData]);

    const [data, setData] = React.useState<any>(checkBoxData);

    const handleCheckBoxes = useCallback(
        (status: boolean, type: keyof checkBoxData) => {
            setData((prev: checkBoxData) => {
                const newcheckBoxData: checkBoxData = {
                    ...prev,
                    [type]: status
                };

                if (!status && type === 'all') {
                    return {
                        all: false,
                        allocateVehicle: false,
                        thirdPartyCheckin: false,
                        trackerDisabled: false,
                        unauthorizedVehicleUse: false
                    };
                }

                if (newcheckBoxData.allocateVehicle && newcheckBoxData.thirdPartyCheckin && newcheckBoxData.trackerDisabled && newcheckBoxData.unauthorizedVehicleUse) {
                    newcheckBoxData.all = true;
                } else if (!status) {
                    newcheckBoxData.all = false;
                }

                return newcheckBoxData;
            });
        },
        [setData]
    );

    useEffect(() => {
        if (data.all) {
            setData({
                all: true,
                allocateVehicle: true,
                thirdPartyCheckin: true,
                trackerDisabled: true,
                unauthorizedVehicleUse: true
            });
        }
    }, [data.all]);
    return (
        <div
            id="filterPanel"
            className="relative notification-filter"
            style={{ top: 0, zIndex: 1 }}
        >
            <div className="wrapper">
                <div className="filter-details">
                    <section className="block types">
                        <h4 className="mt-2">Show</h4>
                        <div className="mt-[20px]">
                            <div className="flex items-center space-x-2 mt-[10px] mb-[10px]">
                                <Checkbox
                                    id="allNotifications"
                                    className="filterCheckbox"
                                    checked={data.all}
                                    data-testid="allNotifications"
                                    onClick={() => {
                                        handleCheckBoxes(!data.all, 'all');
                                        setChange(true);
                                    }}
                                />
                                <label
                                    htmlFor="allNotifications"
                                    className="peer-disabled:opacity-70 font-medium text-xs leading-none peer-disabled:cursor-not-allowed"
                                >
                                    All notifications
                                </label>
                            </div>
                            <div className="flex items-center space-x-2 mt-[10px] mb-[10px]">
                                <Checkbox
                                    id="alloctaeVehicle"
                                    className="filterCheckbox"
                                    checked={data.allocateVehicle}
                                    data-testid="allocateVehicle"
                                    onClick={() => {
                                        handleCheckBoxes(!data.allocateVehicle, 'allocateVehicle');
                                        setChange(true);
                                    }}
                                />
                                <label
                                    htmlFor="alloctaeVehicle"
                                    className="peer-disabled:opacity-70 font-medium text-xs leading-none peer-disabled:cursor-not-allowed"
                                >
                                    Allocate vehicle
                                </label>
                            </div>
                            <div className="flex items-center space-x-2 mt-[10px] mb-[10px]">
                                <Checkbox
                                    id="thirdPartyCheckin"
                                    className="filterCheckbox"
                                    checked={data.thirdPartyCheckin}
                                    data-testid="thirdPartyCheckin"
                                    onClick={() => {
                                        handleCheckBoxes(!data.thirdPartyCheckin, 'thirdPartyCheckin');
                                        setChange(true);
                                    }}
                                />
                                <label
                                    htmlFor="thirdPartyCheckin"
                                    className="peer-disabled:opacity-70 font-medium text-xs leading-none peer-disabled:cursor-not-allowed"
                                >
                                    Third part vehicle checked-in
                                </label>
                            </div>
                            <div className="flex items-center space-x-2 mt-[10px] mb-[10px]">
                                <Checkbox
                                    id="trackerDisabled"
                                    className="filterCheckbox"
                                    checked={data.trackerDisabled}
                                    data-testid="trackerDisabled"
                                    onClick={() => {
                                        handleCheckBoxes(!data.trackerDisabled, 'trackerDisabled');
                                        setChange(true);
                                    }}
                                />
                                <label
                                    htmlFor="trackerDisabled"
                                    className="peer-disabled:opacity-70 font-medium text-xs leading-none peer-disabled:cursor-not-allowed"
                                >
                                    Tracker disabled
                                </label>
                            </div>
                            <div className="flex items-center space-x-2 mt-[10px] mb-[10px]">
                                <Checkbox
                                    id="unauthorizedVehicleUse"
                                    className="filterCheckbox"
                                    checked={data.unauthorizedVehicleUse}
                                    data-testid="unauthorizedVehicleUse"
                                    onClick={() => {
                                        handleCheckBoxes(!data.unauthorizedVehicleUse, 'unauthorizedVehicleUse');
                                        setChange(true);
                                    }}
                                />
                                <label
                                    htmlFor="unauthorizedVehicleUse"
                                    className="peer-disabled:opacity-70 font-medium text-xs leading-none peer-disabled:cursor-not-allowed"
                                >
                                    Unauthorized vehicle use
                                </label>
                            </div>
                        </div>
                    </section>
                    <section className="block mt-5 dates">
                        <h4>Period</h4>
                        <div className="form-calendar">
                            <div>From</div>
                            <div className="flex flex-row gap-2">
                                <div className="flex-col flex-1 justify-end align-center">
                                    <div id="FilterDate ">
                                        <>
                                            <div
                                                className="flex items-center justify-between border h-[34px]"
                                                style={{ position: 'relative', borderRadius: '4px' }}
                                            >
                                                <input
                                                    type="text"
                                                    value={moment(DateFrom).format('DD MMM, YYYY')}
                                                    readOnly
                                                    className="border-none bg-transparent flex-grow w-full px-2 py-1.5 h-[34px] text-[#555555]"
                                                />
                                                <span
                                                    className="absolute right-0"
                                                    style={{ paddingRight: '5px' }}
                                                >
                                                    <img
                                                        src={calender}
                                                        alt="Calendar Icon"
                                                        style={{ height: '20px', width: '20px', color: '#555555', opacity: '0.4' }}
                                                        onClick={() => setCalendarOpen({ from: !calendarOpen.from, to: false })}
                                                    />
                                                </span>
                                            </div>
                                            {calendarOpen.from && (
                                                <div
                                                    className="absolute z-10 mt-[2px]"
                                                    onClick={(e) => e.stopPropagation()}
                                                >
                                                    <DatePicker
                                                        selected={new Date(DateFrom)}
                                                        onChange={(date) => {
                                                            if (date !== null) setDateFrom(date);
                                                            setDateChange(true);
                                                            setCalendarOpen({ from: false, to: false });
                                                        }}
                                                        inline
                                                        minDate={new Date(new Date().setMonth(new Date().getMonth() - 6))}
                                                    />
                                                </div>
                                            )}
                                            {calendarOpen.from && (
                                                <div
                                                    className="fixed inset-0 z-0"
                                                    onClick={() => setCalendarOpen({ from: false, to: false })}
                                                />
                                            )}
                                        </>
                                    </div>
                                </div>
                                <div className="flex flex-row justify-end">
                                    <input
                                        className="timeInput"
                                        type="number"
                                        minLength={2}
                                        maxLength={2}
                                        style={{ width: '50px', textAlign: 'center' }}
                                        inputMode="numeric"
                                        pattern="\d{2}"
                                        defaultValue={DateFrom?.getHours().toString().padStart(2, '0')}
                                        onChange={(e) => {
                                            const value = parseInt(e.target.value, 10);
                                            if (value >= 0 && value <= 23) {
                                                const newDate = new Date(DateFrom);
                                                newDate.setHours(value);
                                                setDateFrom(newDate);
                                            }
                                            setDateChange(true);
                                        }}
                                    />
                                    <span className="self-center">&nbsp;:&nbsp;</span>
                                    <input
                                        className="timeInput"
                                        type="number"
                                        minLength={2}
                                        maxLength={2}
                                        style={{ width: '50px', textAlign: 'center' }}
                                        inputMode="numeric"
                                        pattern="\d{2}"
                                        defaultValue={DateFrom?.getMinutes().toString().padStart(2, '0')}
                                        onChange={(e) => {
                                            const value = parseInt(e.target.value, 10);
                                            if (value >= 0 && value <= 59) {
                                                const newDate = new Date(DateFrom);
                                                newDate.setMinutes(value);
                                                setDateFrom(newDate);
                                            }
                                            setDateChange(true);
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="form-calendar">
                            <span>To</span>
                            <div className="flex flex-row gap-2">
                                <div className="flex-col flex-1 justify-end align-center">
                                    <div id="FilterDate ">
                                        <>
                                            <div
                                                className="flex items-center justify-between border h-[34px]"
                                                style={{ position: 'relative', borderRadius: '4px' }}
                                            >
                                                <input
                                                    type="text"
                                                    value={moment(DateTo).format('DD MMM, YYYY')}
                                                    readOnly
                                                    className="border-none bg-transparent flex-grow w-full px-2 py-1.5 h-[34px] text-[#555555]"
                                                />
                                                <span
                                                    className="absolute right-0"
                                                    style={{ paddingRight: '5px' }}
                                                >
                                                    <img
                                                        src={calender}
                                                        alt="Calendar Icon"
                                                        style={{ height: '20px', width: '20px', color: '#555555', opacity: '0.4' }}
                                                        onClick={() => setCalendarOpen({ from: false, to: !calendarOpen.to })}
                                                    />
                                                </span>
                                            </div>
                                            {calendarOpen.to && (
                                                <>
                                                    <div
                                                        className="absolute z-10 mt-[2px]"
                                                        onClick={(e) => e.stopPropagation()}
                                                    >
                                                        <DatePicker
                                                            selected={new Date(DateTo)}
                                                            onChange={(date) => {
                                                                if (date !== null) setDateTo(date);
                                                                setDateChange(true);
                                                                setCalendarOpen({ from: false, to: false });
                                                            }}
                                                            inline
                                                            minDate={new Date(new Date().setMonth(new Date().getMonth() - 6))}
                                                        />
                                                    </div>
                                                    <div
                                                        className="fixed inset-0 z-0"
                                                        onClick={() => setCalendarOpen({ from: false, to: false })}
                                                    />
                                                </>
                                            )}
                                        </>
                                    </div>
                                </div>
                                <div className="flex flex-row justify-end">
                                    <input
                                        className="timeInput"
                                        type="number"
                                        minLength={2}
                                        maxLength={2}
                                        style={{ width: '50px', textAlign: 'center' }}
                                        inputMode="numeric"
                                        pattern="\d{2}"
                                        defaultValue={DateTo?.getHours().toString().padStart(2, '0')}
                                        onChange={(e) => {
                                            const value = parseInt(e.target.value, 10);
                                            if (value >= 0 && value <= 23) {
                                                const newDate = new Date(DateTo);
                                                newDate.setHours(value);
                                                setDateTo(newDate);
                                            }
                                            setDateChange(true);
                                        }}
                                    />
                                    <span className="self-center"> &nbsp;:&nbsp;</span>
                                    <input
                                        className="timeInput"
                                        type="number"
                                        minLength={2}
                                        maxLength={2}
                                        style={{ width: '50px', textAlign: 'center' }}
                                        inputMode="numeric"
                                        pattern="\d{2}"
                                        defaultValue={DateTo?.getMinutes().toString().padStart(2, '0')}
                                        onChange={(e) => {
                                            const value = parseInt(e.target.value, 10);
                                            if (value >= 0 && value <= 59) {
                                                const newDate = new Date(DateTo);
                                                newDate.setMinutes(value);
                                                setDateTo(newDate);
                                            }
                                            setDateChange(true);
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </section>
                    <hr className="mt-5" />
                    <section className="flex justify-between mt-5">
                        <button
                            className="fleet-btn-default"
                            data-testid="closeFilter"
                            onClick={() => {
                                toggleFilterView('closeFilter');
                                window.scrollTo({ top: 0, behavior: 'smooth' });
                            }}
                        >
                            Close
                        </button>
                        <button
                            className="fleet-btn-default"
                            data-testid="applyFilter"
                            onClick={() => handleApply()}
                        >
                            Apply
                        </button>
                    </section>
                </div>
            </div>
        </div>
    );
};

export default FilterNotification;
