import * as React from 'react';
import * as AccordionPrimitive from '@radix-ui/react-accordion';
import { cn } from '../../lib/utils';
import { ChevronDownIcon } from '@radix-ui/react-icons';

const Accordion = AccordionPrimitive.Root;

const AccordionItem = React.forwardRef<React.ElementRef<typeof AccordionPrimitive.Item>, React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>>(({ className, ...props }, ref) => (
    <AccordionPrimitive.Item
        id="accordionItem"
        ref={ref}
        className={cn('border-b', className)}
        {...props}
    />
));
AccordionItem.displayName = 'AccordionItem';

const AccordionTrigger = React.forwardRef<React.ElementRef<typeof AccordionPrimitive.Trigger>, React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>>(
    ({ className, children, ...props }, ref) => (
        <AccordionPrimitive.Header className="flex">
            <AccordionPrimitive.Trigger
                id="accoriontrigger"
                ref={ref}
                className={cn('flex flex-1 items-center justify-between py-3 text-sm font-medium transition-all hover:underline text-left [&[data-state=open]>svg]:rotate-180', className)}
                {...props}
            >
                {children}
                <ChevronDownIcon
                    id="svgAccord"
                    className="w-4 h-4 text-muted-foreground transition-transform duration-200 shrink-0"
                />
            </AccordionPrimitive.Trigger>
        </AccordionPrimitive.Header>
    )
);
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

const AccordionTriggerFleet = React.forwardRef<React.ElementRef<typeof AccordionPrimitive.Trigger>, React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>>(
    ({ className, children, ...props }, ref) => (
        <AccordionPrimitive.Header className="flex">
            <AccordionPrimitive.Trigger
                id="accoriontriggerFleet"
                ref={ref}
                className={cn('flex flex-1 items-center justify-between py-2 px-[15px] text-sm font-medium transition-all hover:underline text-left [&[data-state=open]>svg]:rotate-180', className)}
                {...props}
            >
                {children}
                <ChevronDownIcon className="w-5 h-5 text-muted-foreground transition-transform duration-200 shrink-0" />
            </AccordionPrimitive.Trigger>
        </AccordionPrimitive.Header>
    )
);
AccordionTriggerFleet.displayName = AccordionPrimitive.Trigger.displayName;

const AccordionContent = React.forwardRef<React.ElementRef<typeof AccordionPrimitive.Content>, React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>>(
    ({ className, children, ...props }, ref) => (
        <AccordionPrimitive.Content
            id="accordionContent"
            ref={ref}
            className="overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
            {...props}
        >
            <div className={cn(' pt-0', className)}>{children}</div>
        </AccordionPrimitive.Content>
    )
);
AccordionContent.displayName = AccordionPrimitive.Content.displayName;

export { Accordion, AccordionItem, AccordionTriggerFleet, AccordionTrigger, AccordionContent };
