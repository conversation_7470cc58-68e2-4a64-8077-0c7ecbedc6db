import React, { useState, useContext } from 'react';
interface GeneralContextType {
    suppliers: any;
    setSuppliers: React.Dispatch<any>;
    regionStore: any;
    setRegionStore: React.Dispatch<any>;
    rafStore: any;
    setRafStore: React.Dispatch<any>;
    mobiltyTaskStore: any;
    setMobiltyTaskStore: React.Dispatch<any>;
    filterCount: any;
    setFilterCount: React.Dispatch<any>;
    allNotificationData: any;
    setAllNotificationData: React.Dispatch<any>;
    assignedNotificationData: any;
    setAssignedNotificationData: React.Dispatch<any>;
    closedNotificationData: any;
    setClosedNotificationData: React.Dispatch<any>;
    customerData: any;
    setCustomerData: React.Dispatch<any>;
    userData: any;
    setUserData: React.Dispatch<any>;
    rafMobilityCombinedStore: any;
    setRafMobilityCombinedStore: React.Dispatch<any>;
    isFilter: any;
    setIsFilter: React.Dispatch<any>;
    filterOptions: any;
    setFilterOptions: React.Dispatch<any>;
    extensionHistory: any;
    setExtensionHistory: React.Dispatch<any>;
    customer: any;
    setCustomer: React.Dispatch<any>;
    isCenterMap: any;
    setIsCenterMap: React.Dispatch<any>;
    approvedExtension: any;
    setApprovedExtension: React.Dispatch<any>;
    pendingExtension: any;
    setPendingExtension: React.Dispatch<any>;
    rejectedExtension: any;
    setRejectedExtension: React.Dispatch<any>;
    expectedExtension: any;
    setExpectedExtension: React.Dispatch<any>;
    tskId: any;
    setTskId: React.Dispatch<any>;
    extensionRules: any;
    setExtensionRules: React.Dispatch<any>;
    tasksByResourceId: any;
    setTasksByResourceId: React.Dispatch<any>;
}

const CommonContextStore = React.createContext<GeneralContextType | null>(null);
export function useCommonStore() {
    return useContext(CommonContextStore);
}
export function CommonProvider(props: any) {
    const [suppliers, setSuppliers] = useState({});
    const [regionStore, setRegionStore] = useState({});
    const [rafStore, setRafStore] = useState({});
    const [mobiltyTaskStore, setMobiltyTaskStore] = useState({});
    const [filterCount, setFilterCount] = useState<number>(0);
    const [isFilter, setIsFilter] = useState(true);
    const [allNotificationData, setAllNotificationData] = useState<[]>([]);
    const [assignedNotificationData, setAssignedNotificationData] = useState<[]>([]);
    const [closedNotificationData, setClosedNotificationData] = useState<[]>([]);
    const [customerData, setCustomerData] = useState<[]>([]);
    const [userData, setUserData] = useState<[]>([]);
    const [rafMobilityCombinedStore, setRafMobilityCombinedStore] = useState([]);
    const [extensionHistory, setExtensionHistory] = useState<[]>([]);
    const [customer, setCustomer] = useState<[]>([]);
    const [isCenterMap, setIsCenterMap] = useState<boolean>(false);
    const [extensionRules, setExtensionRules] = useState<any[]>([]);
    const [filterOptions, setFilterOptions] = useState({
        options: {
            make: [],
            model: [],
            fleetType: [],
            status: [],
            owningRetailer: []
        }
    });

    const [approvedExtension, setApprovedExtension] = useState([]);
    const [pendingExtension, setPendingExtension] = useState([]);
    const [rejectedExtension, setRejectedExtension] = useState([]);
    const [expectedExtension, setExpectedExtension] = useState([]);
    const [tskId, setTskId] = useState('');
    const [tasksByResourceId, setTasksByResourceId] = useState({});
    const value = React.useMemo(
        () => ({
            suppliers,
            setSuppliers,
            regionStore,
            setRegionStore,
            rafStore,
            setRafStore,
            mobiltyTaskStore,
            setMobiltyTaskStore,
            filterCount,
            setFilterCount,
            isFilter,
            setIsFilter,
            allNotificationData,
            setAllNotificationData,
            assignedNotificationData,
            setAssignedNotificationData,
            closedNotificationData,
            setClosedNotificationData,
            customerData,
            setCustomerData,
            userData,
            setUserData,
            rafMobilityCombinedStore,
            setRafMobilityCombinedStore,
            filterOptions,
            setFilterOptions,
            extensionHistory,
            setExtensionHistory,
            customer,
            setCustomer,
            isCenterMap,
            setIsCenterMap,
            approvedExtension,
            setApprovedExtension,
            pendingExtension,
            setPendingExtension,
            rejectedExtension,
            setRejectedExtension,
            expectedExtension,
            setExpectedExtension,
            tskId,
            setTskId,
            extensionRules,
            setExtensionRules,
            tasksByResourceId,
            setTasksByResourceId
        }),
        [
            suppliers,
            regionStore,
            rafStore,
            mobiltyTaskStore,
            filterCount,
            isFilter,
            allNotificationData,
            assignedNotificationData,
            closedNotificationData,
            customerData,
            userData,
            rafMobilityCombinedStore,
            filterOptions,
            extensionHistory,
            customer,
            isCenterMap,
            approvedExtension,
            pendingExtension,
            rejectedExtension,
            expectedExtension,
            tskId,
            extensionRules,
            tasksByResourceId
        ]
    );
    return <CommonContextStore.Provider value={value}>{props.children}</CommonContextStore.Provider>;
}
