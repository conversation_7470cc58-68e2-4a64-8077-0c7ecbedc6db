import { ErrorResponse } from '@aa/connector';
import { CustomerGroupCode, CUVEvents, CUVStatus, CUVData, AuditOperation } from '@aa/data-models/common';
import { DataStore, DataStoreProviderType, DemandDeflectionStore, JobHistoryStore } from '@aa/data-store';
import { EventSourceMeta, SanitizedEntity } from '@aa/event-source';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubSender, EventHubSenderConfig } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';
import { stat } from 'fs';

const appName = 'demand-deflect';
/**
 * Check if invalid fault code
 * @param {number} faultCode
 * @return {faultCode is number}
 */
function isValidFaultCode(faultCode: unknown): faultCode is number {
    return typeof faultCode === 'number';
}

/**
 * Check if invalid membership
 * @param {unknown} membership
 * @return {membership is string}
 */
function isValidMembership(membership: unknown): membership is string {
    return typeof membership === 'string' && !!membership.length;
}

/**
 *
 * Check if invalid cust group
 * @param {number} custGroup
 * @return {custGroup is CustomerGroupCode}
 */
function isValidCustGroup(custGroup: unknown): custGroup is CustomerGroupCode {
    return typeof custGroup === 'string' && !!custGroup.length;
}

/**
 * Check if invalid remarks
 * @param {unknown} remarks
 * @return {remarks is string}
 */
function isValidRemarks(remarks: unknown): remarks is string {
    return typeof remarks === 'string' && !!remarks.length;
}

/**
 *
 * Check if invalid bank flag
 * @param {number} custGroup
 * @return {custGroup is boolean}
 */
function isValidBankFlag(isBank: unknown): isBank is boolean {
    return typeof isBank === 'boolean';
}

/**
 *
 * Check if invalid vrn
 * @param {number} vrn
 * @return {vrn is string}
 */
function isValidVRN(vrn: unknown): vrn is string {
    return typeof vrn === 'string' && !!vrn.length;
}

/**
 *
 * Check if invalid vrn
 * @param {number} vrn
 * @return {vrn is string}
 */
function isValidCustomerRequestId(customerRequestId: unknown): customerRequestId is string {
    return typeof customerRequestId === 'string' && !!customerRequestId.length;
}

/**
 *
 * Check if invalid days
 * @param {number} days
 * @return {days is number}
 */
function isValidDays(days: unknown): days is number {
    return typeof days === 'number' && !!days;
}

/**
 *
 * Check if invalid lat-long
 * @param {unknown} val
 * @return {val is number }
 */
function isNumber(val: unknown): val is number {
    return typeof val === 'number';
}

/**
 * Handle invalid request
 * @param {string} name
 * @param {e.Response} res
 */
function handleInvalidRequeest(name: string, res: Response) {
    const response: ErrorResponse = {
        status: 'INVALID_REQ',
        error: { code: 'ERROR', msg: `Invalid format of ${name}` }
    };
    return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
}

export class App extends Microservice {
    public application = BackendApplication.DEMAND_DEFLECT;
    protected auxStreamSender: EventHubSender<CUVEvents.TASK_UPDATE, CUVData>;
    public name = 'Demand deflect';
    protected legacyDatastore: DataStore;
    protected store: DemandDeflectionStore;
    protected jobHistoryStore: JobHistoryStore;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.REDIS, DataStoreProviderType.MONGODB, DataStoreProviderType.ORACLE, DataStoreProviderType.CATHIE]
        });

        const configBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };

        // For cuv we are using old mongodb
        this.legacyDatastore = new DataStore({
            logger: this.logger,
            providers: [
                {
                    type: DataStoreProviderType.MONGODB,
                    config: {
                        logger: this.logger,
                        system: this.system,
                        connectionString: environment.mongodb.mongodbUrl,
                        appName
                    }
                },
                {
                    type: DataStoreProviderType.ORACLE,
                    config: {
                        logger: this.logger,
                        system: this.system,
                        appName,
                        ...environment.oracle
                    }
                }
            ]
        });

        //to cuv-processor
        this.auxStreamSender = new EventHubSender({
            ...configBase,
            eventHubName: 'aux-stream'
        });

        this.store = new DemandDeflectionStore({
            dataStore: this.legacyDatastore,
            databaseName: environment.mongodb.mongoTaskDbName,
            logger: this.logger
        });

        this.jobHistoryStore = new JobHistoryStore({
            dataStore: this.legacyDatastore,
            databaseName: environment.mongodb.mongoJobDbName,
            logger: this.logger
        });

        this.server.post('/second-recovery', this.secondRecovery);
        this.server.post('/repeat-fault', this.repeatFault);

        // CUV
        // TODO:
        //  - add requires check of min data set
        //  - use POST to /cuv/create
        this.server.post('/cuv/add', this.upsertCUV);
        // TODO:
        //  - add requires a partial data but only if record exists
        //  - use POST to /cuv/:membership/:vrn
        this.server.post('/cuv/update', this.upsertCUV);
        this.server.get('/cuv/:membership', this.getCUVs);
        this.server.get('/cuv/:membership/:vrn', this.getCUV);
        this.server.get('/cuv/:membership/:vrn/status', this.getStatusCUV);
        // TODO:
        //  - use POST to /cuv/:membership/:vrn/revoke
        this.server.post('/cuv/remove', this.revokeCUV);
        this.server.post('/cuv/reportToMI', this.reportCUVToMI);
        this.server.post('/cuv/updateCover', this.updateCover);
    }

    /**
     *
     * @param req
     * @param res
     * @returns
     */
    protected updateCover = async (req: Request<unknown, unknown, CUVupdateCover>, res: Response): Promise<void> => {
        this.logger.info({
            sourceName: this.name,
            message: 'updateCover http request received',
            data: req.body
        });
        try {
            const payload = req.body;

            const { isCuvCovered, customerRequestId, vrn, membership, taskId } = payload;
            // in an ideal world we could just get the isCuvCovered via caseData from mongo but who knows if the event to create it has already arrived to the case-processor.
            // so we have to use the isCuvCovered delivered to us by bcall
            // leaving it here so future devs don't have to wonder why we are not using it
            //const caseData = await this.jobHistoryStore.isCuvCovered({ vrn, customerRequestId });
            const cuvData = await this.store.getCUV({ membership, vrn });
            const wasVrnPreviouslyFlagged = await this.store.isRedFlagged({
                membership,
                vrn
            });

            if (!cuvData) {
                // nothing in the system.
                if (isCuvCovered) {
                    const cuv: SanitizedEntity<CUVData, 'membership' | 'vrn'> = {
                        customerRequestId,
                        membership,
                        taskId,
                        vrn,
                        status: CUVStatus.CUV_COVER_CLAIM,
                        updated: new Date()
                    };
                    const response = await this.store.upsertCUV(cuv);
                    return getResponse(res, ServerResponseCode.OK, response);
                }
            }

            if (cuvData?.status === CUVStatus.CUV_COVER_CLAIM && !isCuvCovered) {
                // it used to be covered but no longer ...
                const cuv: SanitizedEntity<CUVData, 'membership' | 'vrn'> = {
                    customerRequestId,
                    membership,
                    taskId,
                    vrn,
                    status: CUVStatus.CUV_NOT_COVERED,
                    updated: new Date()
                };
                const response = await this.store.upsertCUV(cuv);

                if (wasVrnPreviouslyFlagged) {
                    // was flagged
                    const cuv: SanitizedEntity<CUVData, 'membership' | 'vrn'> = {
                        customerRequestId,
                        membership,
                        taskId,
                        vrn,
                        status: CUVStatus.YELLOW,
                        updated: new Date()
                    };
                    const response = await this.store.upsertCUV(cuv);
                    return getResponse(res, ServerResponseCode.OK, response);
                }
                return getResponse(res, ServerResponseCode.OK, response);
            }

            if (cuvData?.status === CUVStatus.YELLOW && isCuvCovered) {
                // we have an entry record that we have another claim
                const cuv: SanitizedEntity<CUVData, 'membership' | 'vrn'> = {
                    customerRequestId,
                    membership,
                    taskId,
                    vrn,
                    status: CUVStatus.CUV_COVER_CLAIM,
                    updated: new Date()
                };
                const response = await this.store.upsertCUV(cuv);
                return getResponse(res, ServerResponseCode.OK, response);
            }

            if (cuvData?.status === CUVStatus.REVOKED && !isCuvCovered) {
                return getResponse(res, ServerResponseCode.OK, {
                    status: 'success'
                });
            }

            return getResponse(res, ServerResponseCode.OK, {
                status: 'success'
            });
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while retrieving CUV data'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Report CUV to MI
     * @param {e.Request<unknown, unknown, {membership: string, vrn: string, remarks: string, taskId: number; customerRequestId: number}>} req
     * @param {e.Response} res
     * @return {Promise<void>}
     */
    protected reportCUVToMI = async (
        req: Request<
            unknown,
            unknown,
            {
                membership: string;
                vrn: string;
                remarks: string;
                taskId: number;
                customerRequestId: number;
            }
        >,
        res: Response
    ): Promise<void> => {
        this.logger.info({
            sourceName: this.name,
            message: 'CUV report To MI http request received',
            data: req.body
        });
        try {
            const { membership, vrn, remarks, taskId, customerRequestId } = req.body;

            if (!isValidMembership(membership)) {
                return handleInvalidRequeest('membership id', res);
            }
            if (vrn && !isValidVRN(vrn)) {
                return handleInvalidRequeest('vrn', res);
            }
            if (remarks && !isValidVRN(remarks)) {
                return handleInvalidRequeest('remarks', res);
            }

            if (customerRequestId && !isNumber(customerRequestId)) {
                return handleInvalidRequeest('customerRequest Id', res);
            }

            if (taskId && !isNumber(taskId)) {
                return handleInvalidRequeest('task Id', res);
            }

            const body: CUVData = {
                taskId: taskId,
                customerRequestId: customerRequestId,
                membership: membership,
                usageType: 'COMMERCIAL_USE',
                vrn: vrn,
                created: new Date(),
                updated: new Date(),
                remarks: remarks,
                status: CUVStatus.CUV_COVER_CLAIM
            };

            // nothing in the system.
            const meta: EventSourceMeta = {
                action: AuditOperation.CREATE
            };
            const result = await this.store.upsertCUV(body, meta, 'cuv-logs');

            this.logger.info({
                sourceName: this.name,
                message: 'adding details to db',
                data: result
            });

            //to cuv-processor
            if (body) {
                this.logger.info({
                    sourceName: this.name,
                    message: 'sending CUV revoke event to cuv-processor',
                    data: body
                });
                await this.auxStreamSender.send(CUVEvents.TASK_UPDATE, body, false);
            }

            return getResponse(res, ServerResponseCode.OK);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while Reporting CUV to MI'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     Update or add CUV
     * @param {e.Request<unknown, unknown, CUVData>} req
     * @param {e.Response} res
     * @return {Promise<void>}
     */
    protected upsertCUV = async (req: Request<unknown, unknown, CUVData>, res: Response): Promise<void> => {
        // TODO: if new CUV we need to make sure we are adding CUVMinumunData
        this.logger.info({
            sourceName: this.name,
            message: 'CUV get data http request received',
            data: req.params
        });
        try {
            const payload = req.body;

            const response = await this.store.upsertCUV({
                ...payload,
                updated: new Date()
            });

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while retrieving CUV data'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get CUVs for membership
     * @param {e.Request<{membership: string}>} req
     * @param {e.Response} res
     * @return {Promise<void>}
     */
    protected getCUVs = async (req: Request<{ membership: string }>, res: Response): Promise<void> => {
        this.logger.info({
            sourceName: this.name,
            message: 'CUV get data http request received',
            data: req.params
        });
        try {
            const { membership } = req.params;

            if (!isValidMembership(membership)) {
                return handleInvalidRequeest('membership id', res);
            }

            const response = await this.store.getCUVsByMembership({
                membership
            });

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while retrieving CUV data'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get CUV
     * @param {e.Request<{membership: string, vrn: string}>} req
     * @param {e.Response} res
     * @return {Promise<void>}
     */
    protected getCUV = async (req: Request<{ membership: string; vrn: string }>, res: Response): Promise<void> => {
        this.logger.info({
            sourceName: this.name,
            message: 'CUV get http request received',
            data: req.params
        });
        try {
            const { membership, vrn } = req.params;

            if (!isValidMembership(membership)) {
                return handleInvalidRequeest('membership id', res);
            }
            if (vrn && !isValidVRN(vrn)) {
                return handleInvalidRequeest('vrn', res);
            }

            const response = await this.store.getCUV({ membership, vrn });

            if (!response) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while checking CUV status'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get CUV status
     * @param {e.Request<{membership: string, vrn: string}>} req
     * @param {e.Response} res
     * @return {Promise<void>}
     */
    protected getStatusCUV = async (
        req: Request<{
            membership: string;
            vrn: string;
            customerRequestId: string;
        }>,
        res: Response
    ): Promise<void> => {
        this.logger.info({
            sourceName: this.name,
            message: 'CUV get status http request received',
            data: { params: req.params, body: req.body }
        });
        try {
            const { membership, vrn, customerRequestId } = req.params;

            if (!isValidMembership(membership)) {
                return handleInvalidRequeest('membership id', res);
            }
            if (vrn && !isValidVRN(vrn)) {
                return handleInvalidRequeest('vrn', res);
            }
            if (customerRequestId && !isValidCustomerRequestId(customerRequestId)) {
                return handleInvalidRequeest('customerRequestId', res);
            }

            const response = await this.store.getCUVStatus({ membership, vrn });
            // we are not going to do this for now. the client will have to check the cuv status by using the entitlement's isCuvCovered(vrn) method
            // const caseData = await this.jobHistoryStore.isCuvCovered({
            //     vrn,
            //     customerRequestId,
            // });

            if (!response) {
                return getResponse(res, ServerResponseCode.OK, {
                    passed: true
                });
            }

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while checking CUV status'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Revoke CUV
     * @param {e.Request<unknown, unknown, {membership: string, vrn: string, remarks: string}>} req
     * @param {e.Response} res
     * @return {Promise<void>}
     */
    protected revokeCUV = async (req: Request<unknown, unknown, { membership: string; vrn: string; remarks: string }>, res: Response): Promise<void> => {
        this.logger.info({
            sourceName: this.name,
            message: 'CUV revoke http request received',
            data: req.body
        });
        try {
            const { membership, vrn, remarks } = req.body;

            if (!isValidMembership(membership)) {
                return handleInvalidRequeest('membership id', res);
            }
            if (vrn && !isValidVRN(vrn)) {
                return handleInvalidRequeest('vrn', res);
            }
            if (remarks && !isValidVRN(remarks)) {
                return handleInvalidRequeest('remarks', res);
            }

            const response = await this.store.revokeCUV({
                membership,
                vrn,
                remarks
            });
            const body = await this.store.getCUV({ membership, vrn });
            //to cuv-processor
            if (body) {
                this.logger.info({
                    sourceName: this.name,
                    message: 'sending CUV revoke event to cuv-processor',
                    data: body
                });
                await this.auxStreamSender.send(CUVEvents.TASK_UPDATE, body, false);
            }

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while revoking CUV'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected secondRecovery = async (
        req: Request<
            any,
            any,
            {
                days?: string;
                membership: string;
                custGroup: CustomerGroupCode;
                isBank?: boolean;
                vrn: string;
                lat: number;
                long: number;
            }
        >,
        res: Response
    ) => {
        try {
            const { membership, vrn, isBank = false, custGroup, lat, long } = req.body;
            const days = (req.body.days && Number.parseInt(req.body.days)) || 28;

            if (!isValidMembership(membership)) {
                return handleInvalidRequeest('membership id', res);
            }
            if (!isValidCustGroup(custGroup)) {
                return handleInvalidRequeest('customer group code', res);
            }
            if (!isValidVRN(vrn)) {
                return handleInvalidRequeest('vrn', res);
            }
            if (!isValidBankFlag(isBank)) {
                return handleInvalidRequeest('is-bank', res);
            }
            if (!isValidDays(days)) {
                return handleInvalidRequeest('days', res);
            }
            if (!isNumber(lat) || !isNumber(long)) {
                return handleInvalidRequeest('lat-long', res);
            }

            const response = await this.store.secondRecovery({
                membership,
                vrn,
                days,
                isBank,
                custGroup,
                lat,
                long
            });

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while checking secondary recovery'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected repeatFault = async (
        req: Request<
            any,
            any,
            {
                custGroup: CustomerGroupCode;
                vrn: string;
                faultCode: number;
            }
        >,
        res: Response
    ) => {
        try {
            const { faultCode, vrn, custGroup } = req.body;

            if (!isValidFaultCode(faultCode)) {
                return handleInvalidRequeest('fault code', res);
            }
            if (!isValidCustGroup(custGroup)) {
                return handleInvalidRequeest('customer group code', res);
            }
            if (!isValidVRN(vrn)) {
                return handleInvalidRequeest('vrn', res);
            }

            const response = await this.store.repeatFault({
                faultCode,
                vrn,
                custGroup
            });

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while checking repeat fault'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
}

interface CUVupdateCover {
    isCuvCovered: boolean;
    customerRequestId: number;
    vrn: string;
    membership: string;
    taskId: number;
}
