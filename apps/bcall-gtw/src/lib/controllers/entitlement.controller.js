'use strict';

// Vendor libraries
const logger = require('../helpers/logger.helper');
// AA libraries
const FuzzyEntitlementSearchRequest = require('@aa/malstrom-models/lib/fuzzy-entitlement-search-request.model');
// Local imports
const authTokenSvc = require('../services/auth-token.service');

module.exports = class EntitlementController {
    constructor(entitlementService, recaptchaService) {
        this._logger = logger;
        this._entitlementService = entitlementService;
        this._recaptchaService = recaptchaService;
    }

    async vehicleEntitlementCheck(req, resp) {
        const vehicleReg = req.body.vehicleReg;
        //const custGroup = req.header('custGroup');
        const recaptchaToken = req.body.recaptchaToken;
        this._logger.info('###############################################################################################################################################################');
        this._logger.info(
            '#### entitlement.controller.vehiclecheck:: receive request::',
            JSON.stringify({
                body: req.body,
                headers: req.headers,
                'rabo-token': req.headers['rabo-token']
            })
        );
        try {
            await this._recaptchaService.validate(recaptchaToken, req);

            //Some partners, like CHASE, are special cases. We will always return identitystate:0
            //adding this as a very simple if statement. simple is better.
            //this is so wrong...
            let response = {};
            if (req.signedMember) {
                if (req.signedMember.customerGroup === 'CHAS') {
                    response = { identityState: 0 };
                    return resp.status(200).json(response);
                }
            }

            let customerGroupCode = req.signedMember?.customerGroup || req.body.customerGroupCode || null;
            //TODO: await this._signedMemberValidatorService.validate.bind(this._signedMemberValidatorService, req);
            const payload = {
                fuzzyEntitlement: {
                    customerGroupCode
                }
            };
            const result = await this._entitlementService.vehicleEntitlementCheck(vehicleReg, payload);
            const { entitlement = null, searchError = null } = result;

            response = { identityState: 1 };
            if (entitlement) {
                response = { identityState: 0 };
            } else if (searchError) {
                switch (searchError.id) {
                    //DQ20240406 - RBAUAA-5102 FSC multiple hits is a valid ID&V.
                    //This needs refactoring. In the old days multiple valid entilements meant an error and we would not use the entitlement. Now we do. Both entitlement service and bcall need to be refactored to make sense.
                    case 'ENTITLEMENT_SEARCH_FOUND_MULTIPLE':
                        if (searchError.customerGroupCode === 'FSC') {
                            response = { identityState: 0 };
                        }
                        break;
                }
            }
            if (process.env.AAH_ENVIRONMENT !== 'live') {
                response.debug = { result };
            }
            this._logger.info('entitlement.controller.vehicleEntitlementCheck:: response:', JSON.stringify(response));
            resp.status(200).json(response);
        } catch (err) {
            this._logger.error(`entitlement.controller.entitlementCheck :: query failed `, err.msg ? err.msg : err);
            let failedStatus = {
                status: 'failed'
            };
            if (process.env.AAH_ENVIRONMENT === 'acceptance') {
                failedStatus.debug = {
                    err
                };
            }
            resp.status(400).json(failedStatus);
        }
    }

    async postcodecheck(req, resp) {
        const postcode = req.body.postcode;
        const firstName = req.body.firstName;
        const surname = req.body.surname;
        const title = req.body.title;
        const customerGroupCode = req.body.customerGroupCode || null;
        //const recaptchaToken = req.body.recaptchaToken;
        this._logger.info('###############################################################################################################################################################');
        this._logger.info('#### entitlement.controller.postcodecheck:: receive request::', JSON.stringify(req.body));
        try {
            //await this._recaptchaService.validate(recaptchaToken);
            const result = await this._entitlementService.postcodeSearch(postcode, firstName, surname, title, customerGroupCode, authTokenSvc.create(customerGroupCode));
            const entitlement = result && result.entitlement;
            resp.status(200).json({ status: entitlement ? 'ok' : 'NOT_FOUND' });
        } catch (err) {
            this._logger.error(`entitlement.controller.postcodecheck :: query failed `, err.msg ? err.msg : err);
            let failedStatus = {
                status: 'failed'
            };
            if (process.env.AAH_ENVIRONMENT === 'acceptance') {
                failedStatus.debug = {
                    err
                };
            }
            resp.status(400).json(failedStatus);
        }
    }

    // ID&V changes: Versioning the functions to not conflict with the existing functionality
    // Request payload would have dob replacing title and no customergroupcode incoming (defaulted to 'PERS')

    async postcodecheckV2(req, resp) {
        const postcode = req.body.postcode;
        const firstName = req.body.firstName;
        const surname = req.body.surname;
        const dob = req.body.dob;
        this._logger.info('###############################################################################################################################################################');
        this._logger.info('#### entitlement.controller.postcodecheckV2:: receive request::', JSON.stringify(req.body));
        try {
            const result = await this._entitlementService.postcodeSearchV2(postcode, firstName, surname, dob, authTokenSvc.create(customerGroupCode));
            const entitlement = result && result.entitlement;
            resp.status(200).json({ status: entitlement ? 'ok' : 'NOT_FOUND' });
        } catch (err) {
            this._logger.error(`entitlement.controller.postcodecheckV2 :: query failed `, err.msg ? err.msg : err);
            let failedStatus = {
                status: 'failed'
            };
            if (process.env.AAH_ENVIRONMENT === 'acceptance') {
                failedStatus.debug = {
                    err
                };
            }
            resp.status(400).json(failedStatus);
        }
    }

    check(req, resp) {
        const params = new FuzzyEntitlementSearchRequest(req.body.entitlement),
            custGroup = req.header('custGroup') || 'PERS';
        params.customerGroupCode(custGroup);

        this._logger.info('###############################################################################################################################################################');
        this._logger.info('#### entitlement.controller.entitlementCheck:: receive request::', custGroup, params.toString());

        this._entitlementService
            .fuzzySearch(params, authTokenSvc.create(custGroup))
            .then((entitlement) => {
                resp.status(200).json({
                    status: entitlement ? 'ok' : 'failed'
                });
            })
            .catch((err) => {
                this._logger.error(`entitlement.controller.entitlementCheck :: query failed `, err.msg ? err.msg : err);
                let failedStatus = {
                    status: 'failed'
                };
                if (process.env.AAH_ENVIRONMENT === 'acceptance') {
                    failedStatus.debug = {
                        err
                    };
                }
                resp.status(400).json(failedStatus);
            });
    }
};
