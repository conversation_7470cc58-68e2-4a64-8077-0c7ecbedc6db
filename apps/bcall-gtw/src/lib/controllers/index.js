'user strict';

const AssistanceController = require('./assistance.controller'),
    VehicleController = require('./vehicle.controller'),
    LocationController = require('./location.controller'),
    EntitlementController = require('./entitlement.controller'),
    QuickCodeFaultController = require('./quick-code-faults.controller'),
    AddressController = require('./address.controller'),
    RaboProblemsController = require('./rabo-problems.controller'),
    RaboErrorsController = require('./rabo-errors.controller'),
    LookupController = require('./lookup.controller'),
    interceptor = require('@aa/rabo-key-interceptor').interceptor,
    noCheckAuth = require('../middleware/auth.middleware').noCheckAuth(),
    sanitiser = require('../middleware/sanitise.middleware');

module.exports = class BCallController {
    constructor(serviceInstances) {
        this._assistanceController = new AssistanceController(
            serviceInstances.assistanceService,
            serviceInstances.messagingService,
            serviceInstances.recaptchaService,
            serviceInstances.signedMemberValidatorService,
            serviceInstances.raboErrorsService,
            serviceInstances.actionService
        );
        this._vehicleController = new VehicleController(serviceInstances.vehicleDetailsService, serviceInstances.recaptchaService);
        this._locationController = new LocationController(serviceInstances.locationService, serviceInstances.mappingService);
        this._entitlementController = new EntitlementController(serviceInstances.entitlementService, serviceInstances.recaptchaService);
        this._quickCodeFaultController = new QuickCodeFaultController(serviceInstances.quickCodeFaultService);
        this._addressController = new AddressController(serviceInstances.addressLookupService);
        this._raboProblemsController = new RaboProblemsController(serviceInstances.raboProblemsService);
        this._raboErrorsController = new RaboErrorsController(serviceInstances.raboErrorsService);
        this._lookupController = new LookupController(serviceInstances.lookupService);
    }

    init(router) {
        router.get('/version', (req, resp) => {
            return resp.json({
                version: require('../../package.json').version
            });
        });

        router.post('/request/assistance', sanitiser.breakdownRequest, interceptor, this._assistanceController.post.bind(this._assistanceController));
        // ID&V changes : API versioning for backward compatibility
        router.post('/v2/request/assistance', sanitiser.breakdownRequest, interceptor, this._assistanceController.post.bind(this._assistanceController));
        router.post('/request/assistance/nocheck', noCheckAuth, this._assistanceController.noCaptchaPost.bind(this._assistanceController));
        router.post('/b2b/request/assistance', this._assistanceController.b2bPost.bind(this._assistanceController));
        router.post('/legacy/request/assistance', this._assistanceController.legacyPost.bind(this._assistanceController));
        router.put('/request/task/update', this._assistanceController.update.bind(this._assistanceController));
        router.post('/request/task/complete', this._assistanceController.complete.bind(this._assistanceController));
        router.get('/locationCheck', this._locationController.locationCheck.bind(this._locationController));
        router.get('/vehicleCheck', this._vehicleController.check.bind(this._vehicleController));
        router.post('/vehicleCheck', this._vehicleController.check.bind(this._vehicleController));
        router.post('/entitlementCheck', this._entitlementController.check.bind(this._entitlementController));
        router.post('/vehicleEntitlementCheck', interceptor, this._entitlementController.vehicleEntitlementCheck.bind(this._entitlementController));
        router.post('/postcodeEntitlementCheck', this._entitlementController.postcodecheck.bind(this._entitlementController));
        // ID&V changes -- versioning of postcodeEntitlmentCheck API
        router.post('/v2/postcodeEntitlementCheck', this._entitlementController.postcodecheckV2.bind(this._entitlementController));
        router.get('/quickFaultCodes/list', this._quickCodeFaultController.list.bind(this._quickCodeFaultController));
        router.get('/quickFaultCodes/faultList', this._quickCodeFaultController.faultList.bind(this._quickCodeFaultController));
        router.get('/quickFaultCodes/faultDetail', this._quickCodeFaultController.faultDetail.bind(this._quickCodeFaultController));
        router.get('/addresses/lookup', this._addressController.lookup.bind(this._addressController));
        router.post('/addresses/lookup', sanitiser.addressLookupRequest, this._addressController.lookup.bind(this._addressController));
        router.get('/problems', interceptor, this._raboProblemsController.lookup.bind(this._raboProblemsController));
        router.get('/errors', interceptor, this._raboErrorsController.lookup.bind(this._raboErrorsController));
        router.post('/membership/lookup', this._lookupController.lookup.bind(this._lookupController));
        router.get('/membership/lookup/:vehicleReg', this._lookupController.lookup.bind(this._lookupController));

        return router;
    }
};
