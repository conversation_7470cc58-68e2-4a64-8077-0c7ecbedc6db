'use strict';

const BCallAssistanceRequest = require('@aa/malstrom-models/lib/bcall-assistance-request.model');
const BcapContants = require('../constants/bcasp.constants');

/**
 * Parse for honda request ... You can see that this is written in C#
 * @param  {Object} details details of the request
 * @return {Object}         mapped to our own specs ..
 */
function _hondaRequestParse(details) {
    return new BCallAssistanceRequest({
        remarks: details.payload?.remarks,
        pos: {
            latitude: details.position.latitude,
            longitude: details.position.longitude
        },
        contact: {
            name: details.firstName + ' ' + details.lastName,
            telephone: details.mobileNumber
        },
        vehicleReg: details.licensePlate,
        fuzzyEntitlement: {
            vehicleReg: details.licensePlate,
            customerGroupCode: 'HOND'
        },
        callInfo: details.payload?.callInfo || []
    });
}

/**
 * Parse for ford request from task JSON...
 * @param  {Object} details details of the request
 * @return {Object}         mapped to our own specs ..
 */
function _fordRequestParse(reqData) {
    return new BCallAssistanceRequest({
        remarks: reqData.payload.remarks,
        pos: {
            latitude: reqData.payload.location.coordinates.latitude,
            longitude: reqData.payload.location.coordinates.longitude
        },
        contact: {
            name: reqData.payload.contact.name,
            telephone: reqData.payload.contact.telephone,
            email: reqData.payload.contact.email
        },

        vehicleReg: reqData.payload.vehicle.registration,
        vin: reqData.payload.vehicle.experianDetails.vin,
        fuzzyEntitlement: {
            vehicleReg: reqData.payload.vehicle.registration,
            vin: reqData.payload.vehicle.experianDetails.vin,
            customerGroupCode: 'FORD',
            bcasp: BcapContants.FORD
        },
        channel: reqData.channel,
        callInfo: reqData.payload.callInfo || []
    });
}

/**
 * Parse for overseas request
 * @param  {Object} reqData details of the request
 * @return {Object}         mapped to our own specs ..
 */
function _overseasRequestParse(reqData) {
    if (reqData.requestorCountry) {
        return new BCallAssistanceRequest({
            remarks: reqData.payload.remarks,
            pos: {
                latitude: reqData.payload.location.coordinates.latitude,
                longitude: reqData.payload.location.coordinates.longitude
            },
            contact: {
                name: reqData.payload.contact.name,
                telephone: reqData.payload.contact.telephone,
                email: reqData.payload.contact.email
            },

            vehicleReg: reqData.payload.vehicle.registration,
            vin: reqData.payload.vehicle.experianDetails.vin,
            fuzzyEntitlement: {
                vehicleReg: reqData.payload.vehicle.registration,
                vin: reqData.payload.vehicle.experianDetails.vin,
                customerGroupCode: 'OSC',
                country: reqData.requestorCountry,
                contractType: 'B2B'
            },
            channel: reqData.channel,
            callInfo: reqData.payload.callInfo || []
        });
    }
    return null;
}

/**
 * Parse Bosch request
 * @param {Object} reqData
 * @return {BCallAssistanceRequest}
 */
function _boschRequestParse(reqData) {
    return new BCallAssistanceRequest({
        remarks: reqData.payload.remarks,
        pos: {
            latitude: reqData.payload.location.coordinates.latitude,
            longitude: reqData.payload.location.coordinates.longitude
        },
        contact: {
            name: reqData.payload.contact.name,
            telephone: reqData.payload.contact.telephone,
            email: reqData.payload.contact.email
        },
        vehicleReg: reqData.payload.vehicle.registration,
        vin: reqData.payload.vehicle.experianDetails.vin,
        fuzzyEntitlement: {
            vehicleReg: reqData.payload.vehicle.registration,
            vin: reqData.payload.vehicle.experianDetails.vin,
            customerGroupCode: 'BOSC',
            bcasp: BcapContants.BOSCH
        },
        channel: reqData.channel,
        callInfo: reqData.payload.callInfo || []
    });
}

function genericBCASPRequestParse(reqData, bcasp) {
    return new BCallAssistanceRequest({
        remarks: reqData.payload.remarks,
        pos: {
            latitude: reqData.payload.location.coordinates.latitude,
            longitude: reqData.payload.location.coordinates.longitude
        },
        contact: {
            name: reqData.payload.contact.name,
            telephone: reqData.payload.contact.telephone,
            email: reqData.payload.contact.email
        },
        vehicleReg: reqData.payload.vehicle.registration,
        vin: reqData.payload.vehicle.experianDetails.vin,
        fuzzyEntitlement: {
            vehicleReg: reqData.payload.vehicle.registration,
            vin: reqData.payload.vehicle.experianDetails.vin,
            customerGroupCode: bcasp,
            bcasp: BcapContants[bcasp]
        },
        channel: reqData.channel,
        callInfo: reqData.payload.callInfo || []
    });
}

/**
 * normalize request. do stuff like uppercases where they're needed for aa-service-entitlement to search correctly. sheesh.
 * note to self: doing it here because of time constraints. service entitlement should be more robust.
 * @param  {Object} body details of the request
 * @return {Object}
 */
function _normalizeData(body) {
    //feeling extra careful today. let's check the full path
    if (body.fuzzyEntitlement) {
        if (body.fuzzyEntitlement.firstName) {
            body.fuzzyEntitlement.firstName = body.fuzzyEntitlement.firstName.toUpperCase();
        }
        if (body.fuzzyEntitlement.membershipNo) {
            body.fuzzyEntitlement.membershipNo = body.fuzzyEntitlement.membershipNo.replace(/\s/g, '');
        }
    }

    if (body.dangerousLocationOverrideReason) {
        body.dangerousLocationOverrideReason = BCallAssistanceRequest.DANGEROUS_LOCATION_OVERRIDE_REASONS[body.dangerousLocationOverrideReason.toUpperCase()] || null;
    }

    if (body.vehicleReg) {
        body.vehicleReg = body.vehicleReg.replace(/\s/g, '');
        body.vehicleReg = body.vehicleReg.toUpperCase();
    }

    return body;
}

module.exports = {
    parse: (body, custGroup) => {
        body = _normalizeData(body);

        if (body.fuzzyEntitlement && body.fuzzyEntitlement.membershipNo && body.fuzzyEntitlement.membershipNo.match(/^CDLMOBILE/)) {
            body.vCustGroup = 'CDL'; //Admiral specs says this is deprecated
            body.identityState = 0;
        }
        if (body.fuzzyEntitlement && body.fuzzyEntitlement.membershipNo && body.fuzzyEntitlement.membershipNo.match(/^CDLVMOBILE/)) {
            body.vCustGroup = 'CDLV'; //Admiral specs says this is deprecated
            body.identityState = 0;
        }

        let model = null;
        switch (custGroup) {
            case 'HOND':
                model = _hondaRequestParse(body);
                break;
            case 'FORD':
                model = _fordRequestParse(body);
                break;
            case 'PERS':
                model = new BCallAssistanceRequest(body);
                model.fuzzyEntitlement().customerGroupCode(custGroup);
                break;
            case 'OSC':
                model = _overseasRequestParse(body);
                break;
            case 'BOSC':
                model = _boschRequestParse(body);
                break;
            case 'LOTE':
                model = genericBCASPRequestParse(body, 'LOTE');
                break;
            case 'KTM':
                model = genericBCASPRequestParse(body, 'KTM');
                break;
            case 'GWA':
                model = genericBCASPRequestParse(body, 'GWA');
                break;
            case 'BYD':
                model = genericBCASPRequestParse(body, 'BYD');
                break;
            case 'SMA':
                model = genericBCASPRequestParse(body, 'SMA');
                break;
            case 'SMAS':
                model = genericBCASPRequestParse(body, 'SMA');
                break;
            case 'SMASCP':
                model = genericBCASPRequestParse(body, 'SMA');
                break;
            case 'MCR':
                model = genericBCASPRequestParse(body, 'MCR');
                break;
            case 'ASST':
            case 'TCB':
            case 'ADAC':
            case 'AAIR':
            case 'ANWB':
            case 'SPAINXXX':
            case 'AAP':
            case 'ACA':
            case 'BIHA':
            case 'UAB':
            case 'HAK':
            case 'ODYK':
            case 'UAMK':
            case 'FDMV':
            case 'RGEO':
            case 'ALFI':
            case 'AEF':
            case 'AEH':
            case 'MAK':
            case 'KROK':
            case 'AGS':
            case 'KTA':
            case 'SIAA':
            case 'EAS':
            case 'ACLX':
            case 'AMSM':
            case 'RMF':
            case 'AMSC':
            case 'SOSV':
            case 'AEP':
            case 'ACP':
            case 'ATFO':
            case 'RAMC':
            case 'AMSS':
            case 'ASA':
            case 'AMZS':
            case 'SOSI':
            case 'TCS':
            case 'MARM':
            case 'GARA':
            case 'RACE':
                model = genericBCASPRequestParse(body, custGroup);
                break;
            default:
                model = new BCallAssistanceRequest(body);
                model.fuzzyEntitlement().customerGroupCode(custGroup);
                break;
        }
        return model;
    },
    isOverseasRequest: (bCallAssistanceRequest) => {
        return bCallAssistanceRequest.fuzzyEntitlement().customerGroupCode() === 'OSC' && bCallAssistanceRequest.fuzzyEntitlement().country();
    }
};
