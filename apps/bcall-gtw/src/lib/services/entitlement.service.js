'use strict';

// Vendor Libraries
const Q = require('q');
const logger = require('../helpers/logger.helper');
// AA Libraries
const aaEndpoints = require('@aa/endpoints');
const Entitlement = require('@aa/malstrom-models/lib/entitlement.model');
// Local Imports
const errors = require('../constants/error.constants');
const queryBuilder = require('../repositories/query-builder');
const authTokenSvc = require('./auth-token.service');
const authToken = authTokenSvc.create('PERS');

module.exports = class EntitlementService {
    constructor() {
        this._aaEndpoints = aaEndpoints;
    }

    /**
     * do a standard fuzzy search and, optionally, an aditional search by BCASP if first search yielded no results
     * @param {object} params
     * @param {string} authToken
     */
    compositeFuzzySearch(params) {
        //searchs by vCustoGroup and postcode should not default to a bcaspsearch.
        //this is getting confusing.
        try {
            logger.info(`EntitlementService.compositeFuzzySearch:: searching with:`, JSON.stringify(params));

            //identityState===0 usualy means we had a positive when searching for regNo.
            //but CHAS is a special case. For CHAS, identitystate equals 0 just means we got a rabo-token and we know who they are and we should do a fuzzy.
            //So let's not use vehicleEntitlementCheck for CHAS and do the fuzzy search instead.
            //this is so wrong...
            let overrideVehicleEntitlementCheck = false;
            if (params.fuzzyEntitlement().customerGroupCode()) {
                overrideVehicleEntitlementCheck = ['CHAS'].includes(params.fuzzyEntitlement().customerGroupCode());
            }

            if (params.identityState() === 0 && !overrideVehicleEntitlementCheck) {
                logger.info(`EntitlementService.compositeFuzzySearch:: going to vehicleRegSearch`);
                return this.vehicleEntitlementCheck(params.vehicleReg(), params.toJSON());
            } else if (params.fuzzyEntitlement().postcode()) {
                logger.info(`EntitlementService.compositeFuzzySearch:: going to postcodeSearch`);
                if (params.fuzzyEntitlement().title()) {
                    const { postcode, firstName, surname, title, customerGroupCode } = params.fuzzyEntitlement().toJSON();
                    return this.postcodeSearch(postcode, firstName, surname, title, customerGroupCode);
                }
                if (params.fuzzyEntitlement().dob()) {
                    const { postcode, firstName, surname, dob, customerGroupCode } = params.fuzzyEntitlement().toJSON();
                    return this.postcodeSearchV2(postcode, firstName, surname, dob, customerGroupCode);
                }
            } else {
                logger.info(`EntitlementService.compositeFuzzySearch:: going to _fuzzySearch`);
                const promise = this._fuzzySearch(params.fuzzyEntitlement());
                return promise.catch(() => {
                    let isBCASP = params.fuzzyEntitlement().bcasp();
                    if (isBCASP) {
                        logger.info(`EntitlementService.compositeFuzzySearch:: _fuzzySearch failed and this isBCASP. going to _bcaspSearch`);
                        return this._bcaspSearch(params);
                    }
                    return { entitlement: null };
                });
            }
        } catch (err) {
            return Promise.reject(err);
        }
    }

    /**
     * check an entitlement details match to one entitlement... If yes then return just that entitlement
     * @param  {object} params    object encopsulating search details
     * @param {string} params.initials initials name
     * @param {string} params.surname member surname,
     * @param {string} params.postcode postcode entitlement is registered
     * @param {string} params.contractKeyLast4Digits last four characters of the entielement number / account no
     * @param {string} params.customerGroupCode customer group this should default to PERS,
     * @param {string} params.vehicleReg vehicle reg no if entitlement is vehicle based
     * @param  {string} authToken [description]
     * @return {Promise}           on success return Entitlement or null
     */
    _fuzzySearch(fuzzyEntitlement) {
        const body = {
            validate: fuzzyEntitlement.toJSON()
        };
        const query = queryBuilder._postQueryBuilder('/api/service-entitlement/fuzzySearch', body, authToken);
        //logger.info(`EntitlementService._fuzzySearch:: query:`, query); //done by endponts

        return this._aaEndpoints
            .restfulRequest(query)
            .then((res) => {
                logger.info(`EntitlementService._fuzzySearch:: res entitlement:`, res.data);
                if (res && res.data && res.data.searchError) {
                    return { searchError: res.data.searchError };
                }
                if (res && res.data && res.data.entitlement) {
                    const entitlement = new Entitlement(res.data.entitlement);
                    if (this.isValid(entitlement, body.validate)) {
                        return { entitlement };
                    }
                }
                return { entitlement: null };
            })
            .catch((err) => {
                logger.error('EntitlementService._fuzzySearch :', err.msg ? err.msg : err);
                return Q.reject(errors.create(12001, 'entitlementSearch error', err));
            });
    }

    vehicleEntitlementCheck(vehicleReg, params) {
        const body = {
            vehicleReg,
            customerGroupCode: ['PERS', 'ADM'].includes(params?.fuzzyEntitlement?.customerGroupCode) ? params.fuzzyEntitlement.customerGroupCode : null //this is weird
        };
        const query = queryBuilder._postQueryBuilder('/api/service-entitlement/vehicleRegSearch', body, authToken);
        //logger.info(`EntitlementService.vehicleEntitlementCheck:: query:`, query);//done by endponts

        return this._aaEndpoints
            .restfulRequest(query)
            .then((res) => {
                logger.info(`EntitlementService.vehicleEntitlementCheck:: res entitlement:`, res.data);

                if (res?.data?.entitlement?.searchError) {
                    return { searchError: res.data.entitlement.searchError };
                }
                if (res && res.data && res.data.entitlement) {
                    const entitlement = new Entitlement(res.data.entitlement);
                    if (this.isValid(entitlement, params)) {
                        return { entitlement };
                    }
                }
                return {
                    entitlement: null,
                    searchError: 'No entitlement found'
                };
            })
            .catch((err) => {
                logger.error('EntitlementService.vehicleEntitlementCheck :', err.msg ? err.msg : err);
                return Q.reject(errors.create(12001, 'entitlementSearch error', err));
            });
    }

    postcodeSearch(postcode, firstName, surname, title, customerGroupCode) {
        const body = {
            postcode,
            firstName,
            surname,
            title,
            customerGroupCode
        };
        const query = queryBuilder._postQueryBuilder('/api/service-entitlement/postcodeSearch', body, authToken);

        return this._aaEndpoints
            .restfulRequest(query)
            .then((res) => {
                logger.info(`EntitlementService.postcodeSearch:: res entitlement:`, res.data);
                if (res?.data?.entitlement?.searchError) {
                    return { searchError: res?.data?.entitlement?.searchError };
                }
                if (res && res.data && res.data.entitlement) {
                    const entitlement = new Entitlement(res.data.entitlement);
                    return { entitlement };
                }
                return null;
            })
            .catch((err) => {
                logger.error('EntitlementService.postcodeSearch :', err.msg ? err.msg : err);
                return Q.reject(errors.create(12001, 'entitlementSearch error', err));
            });
    }

    // ID&V changes: dob replacing title param in the request

    postcodeSearchV2(postcode, firstName, surname, dob, customerGroupCode) {
        const body = {
            postcode,
            firstName,
            surname,
            dob,
            customerGroupCode
        };
        const query = queryBuilder._postQueryBuilder('/api/service-entitlement/v2/postcodeSearch', body, authToken);

        return this._aaEndpoints
            .restfulRequest(query)
            .then((res) => {
                logger.info(`EntitlementService.postcodeSearchV2:: res entitlement:`, res.data);
                if (res?.data?.entitlement?.searchError) {
                    return { searchError: res?.data?.entitlement?.searchError };
                }
                if (res && res.data && res.data.entitlement) {
                    const entitlement = new Entitlement(res.data.entitlement);
                    return { entitlement };
                }
                return null;
            })
            .catch((err) => {
                logger.error('EntitlementService.postcodeSearchV2 :', err.msg ? err.msg : err);
                return Q.reject(errors.create(12001, 'entitlementSearch error', err));
            });
    }

    _bcaspSearch(params) {
        const body = {
            validate: params.fuzzyEntitlement().toJSON()
        };
        const query = queryBuilder._postQueryBuilder('/api/service-entitlement/bcaspSearch', body, authToken);
        logger.info(`EntitlementService._bcaspSearch:: query:`, query);

        return this._aaEndpoints
            .restfulRequest(query)
            .then((res) => {
                logger.info(`EntitlementService._bcaspSearch:: response:`, res.data);
                if (res && res.data && res.data.entitlement) {
                    const entitlement = new Entitlement(res.data.entitlement);
                    if (this.isValid(entitlement, body.validate)) {
                        return { entitlement };
                    }
                }
                return null;
            })
            .catch((err) => {
                logger.error('EntitlementService._bcaspSearch:: searching query ::', err.msg ? err.msg : err);
                return Q.reject(errors.create(12001, 'entitlementSearch error', err));
            });
    }
    /**
     * Create a will join entitlement for assistance requests where the member entitlement cannot be matched
     * @param  {object} eligibilityDetails    object encapsulating prospective member details
     * @param  {string} authToken [description]
     * @return {Promise}           on success return Entitlement or null
     */
    createWillJoinEntitlement(eligibilityDetails) {
        const body = {
            eligibilityDetails: eligibilityDetails.toJSON()
        };

        return this._aaEndpoints
            .restfulRequest(queryBuilder._postQueryBuilder('/api/service-entitlement/eligibility', body, authToken))
            .then((resp) => {
                if (resp && resp.data) {
                    return new Entitlement(resp.data);
                }
                logger.error('EntitlementService.createWillJoinEntitlement:: error creating entitlement, no entitlement data');
                return null;
            })
            .catch((err) => {
                logger.error('EntitlementService.createWillJoinEntitlement:: error creating entitlement', err.msg ? err.msg : err);
                return null;
            });
    }

    isValid(entitlement, searchDetails) {
        if (this.isPersonal(entitlement) && !this.isVehicleBased(entitlement)) {
            return (searchDetails && this.validatePersonalDetails(entitlement, searchDetails)) || true;
        }

        return true;
    }

    isPersonal(entitlement) {
        return entitlement.policy().customerGroup().code() === 'PERS';
    }

    isVehicleBased(entitlement) {
        return entitlement.policy().membershipType().toLowerCase() === 'vehicle';
    }

    validatePersonalDetails(entitlement, searchDetails) {
        if (entitlement.contact().initials()) {
            if (searchDetails.firstName && searchDetails.firstName[0].toLowerCase() !== entitlement.contact().initials().toLowerCase()) {
                return false;
            }
        }

        if (entitlement.contact().surname()) {
            if (searchDetails.surname && searchDetails.surname.toLowerCase() !== entitlement.contact().surname().toLowerCase()) {
                return false;
            }
        }

        if (entitlement.contact().title()) {
            if (searchDetails.title && searchDetails.title.toLowerCase() !== entitlement.contact().title().toLowerCase()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get
     * @param {object} params
     * @param {string} authToken
     */
    overseasEntitlement(params) {
        const body = params.fuzzyEntitlement().toJSON();
        logger.info(`EntitlementService.overseasEntitlement:: searching query:: customerGroup=${body.customerGroupCode} company=${body.country} contractType${body.contractType}`);

        return this._aaEndpoints
            .restfulRequest(queryBuilder._postQueryBuilder('/api/service-entitlement/getOverseasEntitlement', body, authToken))
            .then((res) => {
                if (res && res.data && res.data) {
                    return { entitlement: new Entitlement(res.data) };
                }
                return null;
            })
            .catch((err) => {
                logger.error('EntitlementService.overseasEntitlement:: searching query ::', err.msg ? err.msg : err);
                return Q.reject(errors.create(12001, 'entitlementSearch error', err));
            });
    }

    membershipNoSeach({ membershipNo }) {
        const body = { membershipNo };
        logger.info(`EntitlementService.membershipNoSeach:: searching query:: membershipNo=${membershipNo}`);

        return this._aaEndpoints
            .restfulRequest(queryBuilder._postQueryBuilder('/api/service-entitlement/search/membershipNo', body, authToken))
            .then((res) => {
                if (res && res.data && res.data) {
                    return { entitlement: new Entitlement(res.data) };
                }
                return null;
            })
            .catch((err) => {
                logger.error('EntitlementService.membershipNoSeach:: searching query ::', err.msg ? err.msg : err);
                return Q.reject(errors.create(12001, 'entitlementSearch error', err));
            });
    }

    lookup(params) {
        if (params.personalDetails) {
            if (params.personalDetails.membershipNo) {
                return this.membershipNoSeach(params.personalDetails.membershipNo);
            } else if (params.personalDetails.postCode) {
                const { title, firstName, surname, postCode } = params.personalDetails;
                return this.postcodeSearch(postCode, firstName, surname, title, 'PERS');
            }
        } else {
            return this.vehicleEntitlementCheck(params.vehicleReg);
        }
    }
};
