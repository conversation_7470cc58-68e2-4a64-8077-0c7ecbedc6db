'use strict';

// Vendor Libraries
const Q = require('q');
const logger = require('winston');
// AA Libraries
const aaEndpoints = require('@aa/endpoints');
const { Vehicle } = require('@aa/data-models/common');
// Local Imports
const queryBuilder = require('../repositories/query-builder');

module.exports = class VehicleDetailsService {
    constructor() {
        this._logger = logger;
    }

    vehicle(regNo, authToken) {
        this._logger.info(`AAHelp2Client.vehicleSearch:: regNo ${regNo}`);
        return aaEndpoints
            .restfulRequest(queryBuilder._getQueryBuilder('/api/vehicle-details-service/vehicle/' + regNo, null, authToken))
            .then((resp) => {
                return new Vehicle(resp.data.vehicle);
            })
            .catch((err) => {
                this._logger.error(`AAHelp2Client.vehicleSearch:: regNo ${regNo}::`, err.msg ? err.msg : err);
                return Q.resolve(null);
            });
    }

    vehicleCheck(regNo, authToken) {
        this._logger.info(`AAHelp2Client.vehicleCheck:: regNo ${regNo}`);
        return aaEndpoints.restfulRequest(queryBuilder._getQueryBuilder('/api/vehicle-details-service/check/' + regNo, null, authToken)).then((response) => {
            return response.data;
        });
    }

    roadworthy(regNo, authToken) {
        return aaEndpoints.restfulRequest(queryBuilder._getQueryBuilder('/api/vehicle-details-service/roadworthy/' + regNo, null, authToken)).then((response) => {
            this._logger.info(`vehicleDetailsService :: roadworthy : (regNo ${regNo}): result`, JSON.stringify(response.data));
            return response.data;
        });
    }

    hasRequiredData(vehicle) {
        if (!vehicle || !vehicle.model().name() || !vehicle.experianDetails().make()) {
            return false;
        }
        return true;
    }

    isWithinVehicleWeightLimit(vehicle, group) {
        if (!vehicle || !vehicle.model() || !vehicle.model().weight()) {
            // assume that having no weight is being within limit
            return true;
        }
        if (vehicle.model().weight() > 4250 && group.isAdmiral()) {
            return false;
        }
        if (vehicle.model().weight() > 3500) {
            return false;
        }
        return true;
    }
};
