'use strict';
// Vendor Libraries
const Q = require('q');
const geolib = require('geolib');
// AA Libraries
const Product = require('@aa/data-models/aux/entitlement');
const { Vehicle } = require('@aa/data-models/common');
const LatLong = require('@aa/malstrom-models/lib/lat-long.model');
const EligibilityDetails = require('@aa/malstrom-models/lib/eligibility-details.model');
// Local Imports
const errors = require('../constants/error.constants');
const CONTRACT_VALIDATION = require('../constants/contract-validation.constants');
const EligibilityFactory = require('../factories/eligibility.factory');
const aaEndpoints = require('@aa/endpoints');
const queryBuilder = require('../repositories/query-builder');
const URLS = require('../constants/aahelp2-url.constants');
const BCallRequestFactory = require('../factories/brkd-call-request.factory');
const regionsUK = require('../constants/regions-uk.constants.js');
const logger = require('../helpers/logger.helper');

//const HOMESTART_RANGE_METERS = 402.336; // TODO: Move to Product model
const METERS_TO_MILES = 0.000621371192;
const EntitlementVariableData = require('@aa/malstrom-models/lib/constants/entitlement-variable-data.constants');
const BCallAssistanceRequest = require('@aa/malstrom-models/lib/bcall-assistance-request.model');

module.exports = class AssistanceService {
    constructor(
        customerRequestService,
        entitlementService,
        locationService,
        mappingService,
        quickCodeService,
        taskService,
        vehicleDetailsService,
        roadWorthyService,
        callOutHistoryService,
        auditService,
        raboErrorsService,
        refDataRepo
    ) {
        this._customerRequestService = customerRequestService;
        this._entitlementService = entitlementService;
        this._locationService = locationService;
        this._mappingService = mappingService;
        this._quickCodeService = quickCodeService;
        this._taskService = taskService;
        this._vehicleDetailsService = vehicleDetailsService;
        this._roadWorthyService = roadWorthyService;
        this._geoLib = geolib;
        this._callOutHistoryService = callOutHistoryService;
        this._auditService = auditService;
        this._raboErrorsService = raboErrorsService;
        this._refDataRepo = refDataRepo;
    }

    _isNatWest(entitlement) {
        return ['NWGB', 'NWGP', 'RBSB', 'RBSP'].includes(entitlement.policy().customerGroup().code());
    }

    _shouldTerminate(entitlement) {
        if (this._isNatWest(entitlement)) {
            try {
                const currentDate = new Date(),
                    customerGroupCode = entitlement.policy().customerGroup().code(),
                    customerGroup = this._refDataRepo.customerGroups().findSync(customerGroupCode),
                    customerGroupEnabled = customerGroup.displayInd();

                //customer group is not active. We should terminate.
                if (!customerGroupEnabled) {
                    return true;
                }

                const roadSideBenefit = entitlement.products().find((p) => p.benefitCode() === 'B'),
                    claimFromDateTime = roadSideBenefit.claimFromDateTime().valueOf(),
                    isValidDate = currentDate > claimFromDateTime;

                //claim from date is in the future. We should terminate.
                if (!isValidDate) {
                    return true;
                }

                //all is well. We shall not terminate.
                return false;
            } catch (ex) {
                return false;
            }
        }
        return false;
    }

    /**
     * format member name with title
     * @param {JSON} fuzzyEntitlement
     */
    _formatNameFromfuzzyEntitlement(fuzzyEntitlement) {
        return `${fuzzyEntitlement.surname || ''} ${fuzzyEntitlement.title || ''} ${fuzzyEntitlement.firstName || ''}`.trim().replace(/ {2,}/g, ' ');
    }

    _findHomestartProduct(entitlement) {
        return entitlement.products().find((product) => {
            return product.benefitCode() === 'H';
        });
    }

    _findAdmiralEVonly(entitlement) {
        return entitlement.products().find((product) => {
            return product.name().toLowerCase().includes('ev only');
        });
    }

    _isWillJoinEntitlement(entitlement) {
        return entitlement.riskCode() === EligibilityDetails.WILL_JOIN;
    }

    async _getEntitlement(request) {
        const isOverseasRequest = BCallRequestFactory.isOverseasRequest(request);
        let { entitlement } = isOverseasRequest
            ? await this._entitlementService.overseasEntitlement(request, request.authToken)
            : await this._entitlementService.compositeFuzzySearch(request, request.authToken);

        if (entitlement) {
            return entitlement;
        }

        //no entitlement found so we'll go with a will join
        entitlement = await this._entitlementService.createWillJoinEntitlement(EligibilityFactory.build(request), request.authToken);

        if (!entitlement) {
            return Q.reject(errors.COULD_NOT_CREATE_WILL_JOIN);
        }

        return entitlement;
    }

    /**
     * create assistance request
     * @param {BCallAssistanceRequest} assistReq query details
     * @return {Promise}
     */
    async process(request) {
        const entitlement = await this._getEntitlement(request);

        if (this._shouldTerminate(entitlement)) {
            const errorKey = `CUSTOMERGROUP_NOT_ENABLED_${entitlement.policy().customerGroup().code()}`;
            return Q.reject(errors[errorKey]);
        }

        //passing parameters like this is extremely verbose. Might be more readable/debuggable, though. I think?
        const { vehicleDetails, fault, locationDetails, homeLocation, roadWorthy } = await this.validateRequestData(request, entitlement);
        const { calculatedDistance, calculatedDistanceAsCrowFlies } = await this.calculateBreakdownDistanceAndCallout(request, entitlement, locationDetails, homeLocation);
        const { taskDetails, cshResponse } = await this.verifyBusinessRules(
            request,
            entitlement,
            vehicleDetails,
            fault,
            locationDetails,
            homeLocation,
            roadWorthy,
            calculatedDistance,
            calculatedDistanceAsCrowFlies
        );

        const result = await this.finaliseTask(request, taskDetails, cshResponse);
        await this.postProcess(request, taskDetails, entitlement, result);

        return { ...result, entitlement };
    }

    async postProcess(request, taskDetails, entitlement, result) {
        //if(taskDetails.demandDeflection.cuv.passed && taskDetails.demandDeflection.cuv.isCuvCovered) {
        //notify demand-deflection svc so we can add status CUV_COVER to the audit trail
        await this._callOutHistoryService.notifyCuvCovered(request, entitlement, taskDetails, result.processResponse.activeTask);
        //}

        if (request.dangerousLocationOverrideReason()) {
            const text = BCallAssistanceRequest.DANGEROUS_LOCATION_OVERRIDE_REASONS[request.dangerousLocationOverrideReason()];
            const task = result.processResponse.activeTask;
            await this._auditService.sendCallinfo(task, `Dangerous location Override reason: ${text}`);
        }
        //adding callinfo type check just to be sure malstrom-models is uptated
        if (typeof request.callInfo === 'function' && request.callInfo().length) {
            const task = result.processResponse.activeTask;
            for (let callInfo of request.callInfo()) {
                await this._auditService.sendCallinfo(task, callInfo);
            }
        }
        return;
    }

    async validateRequestData(request, entitlement) {
        let homeLocation = null;

        if (!this._isWillJoinEntitlement(entitlement)) {
            const address = entitlement.contact().address().addressAsString();
            let homeLocationGeocodeResult = await this._mappingService.geocodeForceUK({ address });
            //we consider the first result only. Should we?
            if (homeLocationGeocodeResult.length > 0) {
                //let's try to use one that passes the UK test AND has a street number
                let homeLocationGeocoded = homeLocationGeocodeResult.find((loc) => {
                    const isUK = this.isRegionInUK(loc.address_components.map((c) => c.long_name).join(','));
                    const hasStreetNumber = loc.address_components.find((c) => {
                        return c.types.includes('street_number');
                    });
                    return isUK && hasStreetNumber;
                });
                //if we don't have one with a street number, let's use the first one that passes the UK filter
                homeLocationGeocoded =
                    homeLocationGeocoded ||
                    homeLocationGeocodeResult.find((loc) => {
                        return this.isRegionInUK(loc.address_components.map((c) => c.long_name).join(','));
                    });
                //if we still don't have one, let's use the first one
                homeLocationGeocoded = homeLocationGeocoded || homeLocationGeocodeResult[0];
                let homeLocationLatLng = homeLocationGeocoded.geometry.location;
                homeLocation = new LatLong({
                    latitude: homeLocationLatLng.lat,
                    longitude: homeLocationLatLng.lng
                });
                homeLocation.address = entitlement.contact().address().addressAsString();
                homeLocation.street_number =
                    entitlement.contact().address().houseNoName() || (entitlement.contact().address().addressLines().length > 0 && entitlement.contact().address().addressLines()[0]) || null;
                homeLocation.geocoded = homeLocationGeocoded;
            }
        } else {
            request.validationErrors.push(errors.MISSING_ENTITLEMENT_FOUND_NONE);
        }

        let locationDetails = null;
        //we cannot accept both homestart flag and an address. It shouldn't happen anyways but better check.
        if (request.atHomeAddress() && request.address()) {
            return Promise.reject('Cannot accept both a homestart flag and an address');
        }

        //we cannot accept both homestart and a latLng. It shouldn't happen anyways but better check.
        if (request.atHomeAddress() && (request.pos().latitude() || request.pos().longitude())) {
            return Promise.reject('Cannot accept both a homeStart and latLng');
        }

        //we cannot accept both and address and a latLng. It shouldn't happen anyways but better check.
        if (request.address() && (request.pos().latitude() || request.pos().longitude())) {
            return Promise.reject('Cannot accept both an address and a latLng');
        }

        //if client has set homestart flag to true, we set the incident location to homeAddress
        if (request.atHomeAddress() && homeLocation) {
            locationDetails = this._locationService.locateFromEntitlement(entitlement, homeLocation);
            logger.info('assistance.service:: locationDetails:: homeAddress is', homeLocation.address);
        } else {
            if (request.pos().latitude() && request.pos().longitude()) {
                const latLng = {
                    lat: request.pos().latitude(),
                    lng: request.pos().longitude()
                };
                locationDetails = await this._locationService.locate({ latLng }, request.authToken);
            } else if (request.address()) {
                const address = request.address() || null;
                locationDetails = await this._locationService.locateAsIs({
                    address
                });
            }
            logger.info('assistance.service:: locationDetails:: geocoded address is', locationDetails?.formatted_address || 'n/a');
        }

        const vehicleDetails = await this._vehicleDetailsService.vehicle(request.vehicleReg() || request.vin(), request.authToken);
        const fault = await this._quickCodeService.fault(request.faultCode(), 'PERS');
        const roadWorthy = await this._roadWorthyService.lookup(vehicleDetails, request.vehicleReg(), request.authToken);

        return {
            vehicleDetails,
            fault,
            locationDetails,
            homeLocation,
            roadWorthy
        };
    }

    async calculateBreakdownDistanceAndCallout(request, entitlement, locationDetails, homeLocation) {
        const calculatedDistance = await this.calculateDistanceInMiles(request, locationDetails, homeLocation);
        const calculatedDistanceAsCrowFlies = await this.calculateDistanceAsCrowFlies(request, locationDetails, homeLocation);

        return {
            calculatedDistance,
            calculatedDistanceAsCrowFlies
        };
    }

    isRegionInUK(area) {
        let reg = new RegExp(regionsUK.join('|'), 'i');
        return reg.test(area);
    }

    async verifyBusinessRules(request, entitlement, vehicleDetails, fault, locationDetails, homeLocation, roadWorthy, calculatedDistance, calculatedDistanceAsCrowFlies) {
        let taskDetails = {
            roadWorthy,
            location: locationDetails,
            vehicle: vehicleDetails,
            fault: fault,
            homestartIndicator: false,
            demandDeflection: null
        };

        // Abort if location details not within UK
        //if (!request.atHomeAddress() && locationDetails && locationDetails.location.area()) {
        if (!entitlement.policy().customerGroup().isEuroHelp()) {
            if (locationDetails) {
                const isUK = this.isRegionInUK(locationDetails.location.area());
                const isGeocodedInUk = this.isRegionInUK(homeLocation?.geocoded.formatted_address);
                // if it's a atHomeAddress request it means we're using potentially low quality res home addresses. It also means did a geocode on the home address to get the latlng.
                // we will consider to be outside UK if both the res home address and the geocoded address are not in the UK
                // I imagine we could make do with just checking the geocoded address but I'm not sure if there are any edge cases where the res home address is in the UK but the geocoded one is not
                if (request.atHomeAddress()) {
                    if (!isUK && !isGeocodedInUk) {
                        return Q.reject(errors.OUTSIDE_UK);
                    }
                } else {
                    if (!isUK) {
                        return Q.reject(errors.OUTSIDE_UK);
                    }
                }
            }
        }

        // Abort if we still have no entitlement
        if (!entitlement) {
            return Q.reject(errors.MISSING_ENTITLEMENT_FOUND_NONE);
        }

        // Check that the vrn was supplied
        if (!request.vehicleReg()) {
            request.validationErrors.push(errors.MISSING_VRN);
        }

        // Check that required vehicle details were found
        if (vehicleDetails && !this._vehicleDetailsService.hasRequiredData(vehicleDetails)) {
            request.validationErrors.push(errors.MISSING_VEHICLE_DATA);
        }

        // Check that vehicle is within weight limits
        if (vehicleDetails && !this._vehicleDetailsService.isWithinVehicleWeightLimit(vehicleDetails, entitlement.policy().customerGroup())) {
            request.validationErrors.push(errors.EXCEEDS_WEIGHT_LIMIT);
        }

        // Check that a vehicle record was found
        if (!vehicleDetails) {
            request.validationErrors.push(errors.MISSING_VEHICLE);
            // Create empty vehicle details to ensure vrn is stored
            if (request.vehicleReg()) {
                vehicleDetails = new Vehicle({
                    registration: request.vehicleReg()
                });
                // assign vehicle details into task if it gets changes from above snippet
                taskDetails.vehicle = vehicleDetails;
            }
        }

        // Check that a contact record was found
        if (request.contact()) {
            taskDetails.contact = Object.assign({}, taskDetails.contact, request.contact().toJSON());
            //we need to put the title into the contact name. we'll always do it for RABO and APP tasks.
            //for some reason for sparx and ford it should only happen if the name is empty.
            let requestSources = [EntitlementVariableData.RABO.value, EntitlementVariableData.AA_APP_CREATED.value];
            if (requestSources.includes(request.requestSource.value) || !request.contact().name()) {
                taskDetails.contact.name = this._formatNameFromfuzzyEntitlement(request.fuzzyEntitlement().toJSON());
            }
        }

        // Check that we have the covered vehicle if this is a vehicle based membership
        if (!this.isVehicleValidAgainstEntitlement(request.vehicleReg(), entitlement)) {
            request.validationErrors.push(errors.MISSING_ENTITLEMENT_WRONG_VEHICLE);
        }

        // Check the entitlement is not expired
        if (entitlement.hasExpiredProductByName(Product.ROADSIDE)) {
            request.validationErrors.push(errors.MISSING_ENTITLEMENT_FOUND_EXPIRED);
        }

        // Check that the location is present
        // moved here because we are now setting location to null if in dangerous location
        if (!locationDetails) {
            request.validationErrors.push(errors.MISSING_LOCATION);
        }

        //Check 24 hour policy cool-off period for CUV add-on
        if (entitlement.hasValidProductByName(Product.COMMERCIAL_USE)) {
            let coolOffResponse = this.validatePolicyCoolOffPeriod(entitlement.getProduct(Product.COMMERCIAL_USE), entitlement);
            if (!coolOffResponse.isProductBeyondCoolingOff) {
                this.addTaskContractValidation(taskDetails, coolOffResponse);
                request.validationErrors.push(errors.CUV_COOLOFF_PERIOD);
            }
        }

        //Check 24 hour policy cool-off period
        if (entitlement.hasValidProductByName(Product.ROADSIDE)) {
            let coolOffResponse = this.validatePolicyCoolOffPeriod(entitlement.getProduct(Product.ROADSIDE), entitlement);
            if (!coolOffResponse.isProductBeyondCoolingOff) {
                this.addTaskContractValidation(taskDetails, coolOffResponse);
                switch (coolOffResponse.productName.toUpperCase()) {
                    case 'VEHICLE':
                        request.validationErrors.push(errors.MTA_VEHICLE_COOLOFF_PERIOD);
                        break;
                    case 'ROADSIDE':
                        request.validationErrors.push(errors.POLICY_COOLOFF_PERIOD);
                        break;
                }
            }
        }

        // Check that the location is not with homestart range if the member doesn't have homestart.
        if (locationDetails && homeLocation) {
            const ONE_FORTH_OF_A_MILE = 0.25;
            // Check for Homestart entitlement, if user is under 1/4mile of Home location
            if (!isNaN(calculatedDistance) && calculatedDistance <= ONE_FORTH_OF_A_MILE) {
                //for Uber it's "Home Start". It should really go into the model but im not doing it now because I dont want to risk having to spend half a day fixing jenkins. again. and again.
                //so sue me!
                let homestartProductFound = this._findHomestartProduct(entitlement);
                let homestartProduct = homestartProductFound ? homestartProductFound.name() : null;

                if (entitlement.hasValidProductByName(homestartProduct)) {
                    taskDetails.homestartIndicator = true;

                    let coolOffResponse = this.validatePolicyCoolOffPeriod(entitlement.getProduct(homestartProduct), entitlement);
                    if (!coolOffResponse.isProductBeyondCoolingOff && !taskDetails.contractValidation) {
                        this.addTaskContractValidation(taskDetails, coolOffResponse);
                        request.validationErrors.push(errors.MTA_HOMESTART_COOLOFF_PERIOD);
                    }
                    taskDetails.homestartIndicator = true;
                } else {
                    //if its atHomeAddress then we dont remove the location
                    if (!request.atHomeAddress()) {
                        taskDetails.location = null;
                        request.validationErrors.push(errors.MISSING_LOCATION);
                    }
                    request.validationErrors.push(errors.MISSING_ENTITLEMENT_NO_HOMESTART);
                }
            }
        }

        //check if it is LocalDriver entitlement and distance from home more than 20 miles as crow flies.
        if (entitlement.isLocalDriverTooFar(calculatedDistanceAsCrowFlies)) {
            request.validationErrors.push(errors.LOCALDRIVER_20MILE);
        }

        //Check StandBy
        if (entitlement.isStandby()) {
            request.validationErrors.push(errors.STANDBY_DRIVER);
        }

        let vrn = vehicleDetails && vehicleDetails.registration() ? vehicleDetails.registration() : request.vehicleReg();

        //if the entitlement has no regNo we set it to be the vrn we got earlier
        if (!entitlement.vehicle().registration() && vrn) {
            entitlement.vehicle().registration(vrn);
        }

        const [action, cshResponse] = await this._customerRequestService.createCustomerRequest(request.contact(), request.faultCode(), entitlement, vrn, request.authToken);
        logger.info('assistance.service:: verifyBusinessRules:: action is', action);

        taskDetails.demandDeflection = await this._callOutHistoryService.validate(request, entitlement, cshResponse);

        if (!taskDetails.demandDeflection.excessiveUSe.allowed) {
            if (entitlement.policy().customerGroup().isUBER()) {
                request.validationErrors.push(errors.BREAKDOWN_LIMIT_EXCEEDED);
            } else if (entitlement.policy().customerGroup().code() === 'PERS') {
                request.validationErrors.push(errors.EXCESSIVE_USE);
            }
        }

        if (this._findAdmiralEVonly(entitlement)) {
            request.validationErrors.push(errors.ADMIRAL_EV_ONLY);
            if (taskDetails.fault) {
                taskDetails.fault = null;
            }
        }

        //checking repeat battery and fault
        if (request.faultCode()) {
            // const isFlatBattery = this._quickCodeService.isFlatBattery(request.faultCode());

            // if (isFlatBattery && !demandDeflection.repeatBattery.passed) {
            //     request.validationErrors.push(errors.REPEAT_BATTERY);
            // } else {
            if (!taskDetails.demandDeflection.repeatFault.passed) {
                request.validationErrors.push(errors.REPEAT_FAULT);
            }
            // }
        }

        if (!taskDetails.demandDeflection.cuv.passed) {
            //if it isn't a CUV covered/add on then must be INIT
            if (!taskDetails.demandDeflection.cuv.isCuvCovered) {
                request.validationErrors.push(errors.CUV_VIOLATION);
            }
        }

        if (taskDetails.demandDeflection.cuv.passed || taskDetails.demandDeflection.cuv.isCuvCovered) {
            //add flexfield
        }

        //RBAUAA-1305
        if (homeLocation && homeLocation.street_number) {
            //we don't want to add the street number if it is already there, do we?
            if (!new RegExp(`^${homeLocation.street_number},`).test(cshResponse.activeTask().location().remarks())) {
                const remarksToAppend = homeLocation.street_number + ', ' + cshResponse.activeTask().location().remarksToAppend();
                cshResponse.activeTask().location().remarksToAppend(remarksToAppend);
            }
        }

        // Check the fault has been provided and is available for direct reporting
        // moved this down here so the isOtherCheck can be done
        if (!taskDetails.fault) {
            if (request.validationErrors.indexOf(errors.MISSING_VEHICLE) === -1) {
                request.validationErrors.push(errors.MISSING_FAULT_DATA);
            }
        } else {
            if (taskDetails.fault && this._quickCodeService.isKeyAssist(taskDetails.fault.quickCode())) {
                request.validationErrors.push(errors.UNSUPPORTED_FAULT_KEY_ASSIST);
            }

            if (taskDetails.fault && this._quickCodeService.isFuelAssist(taskDetails.fault.quickCode())) {
                taskDetails.fault = await this._quickCodeService.fault('S00', 'PERS');
                request.validationErrors.push(errors.UNSUPPORTED_FAULT_FUEL_ASSIST);
            }

            if (taskDetails.fault && this._quickCodeService.isRecovery(taskDetails.fault.quickCode())) {
                request.validationErrors.push(errors.UNSUPPORTED_FAULT_NEEDS_RECOVERY);
            }

            if (taskDetails.fault && this._quickCodeService.isKeysLockedInside(taskDetails.fault.quickCode())) {
                taskDetails.fault = null;
                request.validationErrors.push(errors.MISSING_FAULT_DATA);
            }

            if (taskDetails.fault && this._quickCodeService.isStuckInFault(taskDetails.fault.quickCode())) {
                taskDetails.fault = null;
                request.validationErrors.push(errors.UNSUPPORTED_STUCK_IN_FAULT);
            }
            if (taskDetails.fault && this._quickCodeService.isGlassTask(taskDetails.fault.quickCode())) {
                taskDetails.fault = null;
                request.validationErrors.push(errors.UNSUPPORTED_GLASS_TASK);
            }
            //RBAUAA-2304. We only wanto to add MISSING_FAULT_DATA if there's already other errors.
            if (request.validationErrors.length) {
                if (taskDetails.fault && this._quickCodeService.isOther(taskDetails.fault.quickCode())) {
                    taskDetails.fault = null;
                    request.validationErrors.push(errors.MISSING_FAULT_DATA);
                }
            }
        }

        //we don't care about dangerous location if homestart flag has been set to true.
        //we also don't cae about dangerous location checks if locaion is a fully qualified address
        //we also don't care about dangerous location checks if locaion is a fully qualified address
        if (!request.dangerousLocationOverrideReason() && !(request.atHomeAddress() || request.address())) {
            if (locationDetails && (locationDetails.motorway || locationDetails.dangerousLocation)) {
                let dangerousLocError = Object.assign({}, errors.DANGEROUS_LOCATION);
                dangerousLocError.msg += `: ${locationDetails.formatted_address};`;

                request.validationErrors.push(dangerousLocError);

                locationDetails = null;
                taskDetails.location = null;
            }
        }

        // Check MOT and Tax Status
        if (request.vehicleReg() && roadWorthy.errors && roadWorthy.errors.length) {
            const isTaxi = vehicleDetails.experianDetails().doorPlan() === 'TAXI';
            const isOnlySORN = roadWorthy.errors[0].id === errors.SORN_STATUS.id;

            //we'll add errors when the vehicle is not a taxi
            if (!isTaxi) {
                request.validationErrors.push(roadWorthy.errors[0]);
            }

            //we'll also add errors if it is a taxi and is SORN. RBAUAA-1501
            if (isTaxi && isOnlySORN) {
                request.validationErrors.push(roadWorthy.errors[0]);
            }
        }
        return { taskDetails, cshResponse };
    }

    getLatLng(locationObj) {
        return Object.keys(locationObj)
            .map((key) => {
                return locationObj[key];
            })
            .join(',');
    }

    calculateDistanceInMiles(request, locationObj, homeLocationObj) {
        try {
            if (request.atHomeAddress()) {
                return Q.resolve(0);
            }
            if (locationObj && homeLocationObj) {
                let originLatLng = this.getLatLng(locationObj.location.coordinates().toJSON());
                let destinationLatLng = this.getLatLng(homeLocationObj.toJSON());
                let distanceCalculatorUrl = URLS.DISTANCE_CALCULATOR + '?origins=' + originLatLng + '&destinations=' + destinationLatLng;
                return aaEndpoints.restfulRequest(queryBuilder._getQueryBuilder(distanceCalculatorUrl, null, request.authToken)).then((resp) => {
                    //check that the response contains all details we need to calculate the result
                    if (resp && resp.data && resp.data.rows && resp.data.rows.length && resp.data.rows[0].elements && resp.data.rows[0].elements.length && !!resp.data.rows[0].elements[0]) {
                        //calculate distance in miles and return it
                        return Q.resolve(+(resp.data.rows[0].elements[0].distance.value * METERS_TO_MILES).toFixed(2));
                    } else {
                        return Q.resolve(0);
                    }
                });
            } else {
                return Q.resolve(0);
            }
        } catch (ex) {
            logger.warn('assistance.service:: calculateDistanceInMiles:: could not calculate distance', { locationObj, homeLocationObj, exception: ex.message });
            return Q.resolve(0);
        }
    }

    calculateDistanceAsCrowFlies(request, locationObj, homeLocationObj) {
        try {
            if (request.atHomeAddress()) {
                return Q.resolve(0);
            }
            if (locationObj && homeLocationObj) {
                let originLatLng = this.getLatLng(locationObj.location.coordinates().toJSON());
                let destinationLatLng = this.getLatLng(homeLocationObj.toJSON());
                let distanceCalculatorUrl = URLS.DISTANCE_CALCULATOR_AS_CROW_FLIES + '?origins=' + originLatLng + '&destinations=' + destinationLatLng;
                return aaEndpoints.restfulRequest(queryBuilder._getQueryBuilder(distanceCalculatorUrl, null, request.authToken)).then((resp) => {
                    //check that the response contains all details we need to calculate the result
                    if (resp && resp.data && resp.data.distance) {
                        //calculate distance in miles and return it
                        return Q.resolve(+resp.data.distance.toFixed(2));
                    } else {
                        return Q.resolve(0);
                    }
                });
            } else {
                return Q.resolve(0);
            }
        } catch (ex) {
            logger.warn('assistance.service:: calculateDistanceAsCrowFlies:: could not calculate distance', { locationObj, homeLocationObj, exception: ex.message });
            return Q.resolve(0);
        }
    }

    addTaskContractValidation(taskDetails, coolOffResponse) {
        taskDetails.contractValidation = {
            coolingOff: {
                product: coolOffResponse.productName,
                passed: coolOffResponse.isProductBeyondCoolingOff
            }
        };
    }
    validatePolicyCoolOffPeriod(product, entitlement) {
        let currentDate = Date.now();
        let response = {
            productName: product.name(),
            isProductBeyondCoolingOff: false
        };
        let { claimFromDateTime } = product;
        response.isProductBeyondCoolingOff = claimFromDateTime && currentDate > claimFromDateTime();
        if (!response.isProductBeyondCoolingOff && product.name().toUpperCase() === CONTRACT_VALIDATION.ROADSIDE && entitlement.vehicle() && entitlement.vehicle().registration()) {
            /* check if VBM */
            response.productName = this.isMTA(product) ? CONTRACT_VALIDATION.VEHICLE : CONTRACT_VALIDATION.ROADSIDE;
        }
        return response;
    }

    isMTA(product) {
        let daysInATerm = this.getTermDays(product.startDate(), product.endDate());
        if (
            this.dateDifference(product.startDate(), product.endDate()) < daysInATerm /* TIA - product term < 1 year => MTA */ ||
            this.dateDifference(product.startDate(), product.claimFromDateTime()) > 1
        ) {
            /* GALAXY(CATHIE/RIO) - Differance between startDate and claimfromDate > 1 day => MTA */
            return true;
        }
        return false;
    }

    dateDifference(startDate, endDate) {
        return Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
    }

    getTermDays(startDate, endDate) {
        /* The term year will contrain 366 days if :
         * startDate.year is a leap year and the month is January or February (the term then includes February of the leap year)
         *  OR
         * endDate.year is a leap year and the month is after February (the term then included February of the leap year)
         *
         * NOTE: using month of the endDate because startDate may change if there is any MTA
         */
        if ((this.isLeapYear(startDate.getFullYear()) && endDate.getMonth() < 3) || (this.isLeapYear(endDate.getFullYear()) && endDate.getMonth() > 2)) {
            return 365; // days in a leapYear - 1 : date differance doesn't include the last day
        }
        return 364; // days in a year - 1 : date differance doesn't include the last day
    }

    isLeapYear(year) {
        return year % 400 ? (year % 100 ? (year % 4 ? false : true) : false) : true;
    }

    async finaliseTask(request, taskDetails, cshResponse) {
        const status = request.validationErrors.length ? 'INIT' : 'UNAC';

        const processResponse = await this._taskService.createTask(
            taskDetails,
            cshResponse,
            request.appointment(),
            request.validationErrors,
            request.authToken,
            request.createReason,
            status,
            request.channel(),
            request
        );
        const errors = request.validationErrors;

        return { processResponse, errors };
    }

    isVehicleValidAgainstEntitlement(vehicleReg, entitlement) {
        if (entitlement.policy().membershipType() == null) {
            return true;
        }

        if (!entitlement.vehicle() || entitlement.policy().membershipType().toLowerCase() !== 'vehicle') {
            return true;
        }

        if (entitlement.vehicle().registration() === vehicleReg) {
            return true;
        }

        return false;
    }

    /**
     * Complete task request
     * @param {Object} request
     * @returns {Promise}
     */
    complete(cr, task, authToken) {
        return new Promise((resolve, reject) => {
            this._taskService
                .complete(cr, task, authToken)
                .then((resp) => {
                    resolve(resp);
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    /**
     * Update task request
     * @param {Object} request
     * @returns {Promise}
     */
    update(task, req, authToken) {
        return new Promise((resolve, reject) => {
            this._taskService
                .update(task, authToken)
                .then(async (resp) => {
                    if (req.body.callInfo && req.body.callInfo.length) {
                        for (let callInfo of req.body.callInfo) {
                            await this._auditService.sendCallinfo(task, callInfo);
                        }
                    }

                    resolve(resp);
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }
};
