import { BackendApplication } from '@aa/identifiers';
import { BackendEnvironment } from '@aa/utils';
import { Microservice } from '@aa/microservice';
import { Request, Response } from 'express';
import { ServerResponseCode } from '@aa/http-client';
import { getResponse } from '@aa/server-utils';
import { ClosedHireDto, CountsDays, ClosedHireAvailableStats, OrderChargesFactory, ClosedHiresFilter } from '@aa/closed-hires';
import { ClosedHiresStore } from '@aa/data-store';
import { RequestParserFactory, SummaryResponseFactory, Exprt2Csv } from './factory';

export class App extends Microservice {
    public name = 'ClosedHiresApi';
    public application = BackendApplication.CLOSED_HIRES_API;
    private dabaseName = 'cache';
    private closedHiresStore: ClosedHiresStore;

    constructor(environment: BackendEnvironment) {
        super({ environment, appName: 'closed-hires-api' });

        this.closedHiresStore = new ClosedHiresStore({
            databaseName: this.dabaseName,
            logger: this.logger,
            dataStore: this.dataStore
        });

        this.server.get('/counts', this.closedHiresCounts, { protect: false });
        this.server.get('/summary', this.summary, { protect: false });
        this.server.get('/v2/counts/:period', this.closedHiresCounts, { protect: false });
        this.server.get('/v2/summary/:period', this.summaryV2, { protect: false });
        this.server.get('/availableStats', this.availableStats, { protect: false });

        this.server.get('/export2csv/summary/:period', this.export2csv, { protect: false });
    }

    protected closedHiresCounts = async (req: Request, res: Response) => {
        const query = RequestParserFactory.query(req);
        if (!query.valid || !query?.reportedPeriod) {
            this.logger.log({ message: 'Closed Hires Summary', data: query });
            getResponse(res, ServerResponseCode.BAD_REQUEST, { status: 'error', message: `Invalid request ${JSON.stringify(query)}` });
            return;
        }
        this.logger.log({
            message: 'Closed Hires Counts',
            data: { ...query }
        });
        res.setHeader('Content-Type', 'application/json');
        const data: CountsDays | null = await this.closedHiresStore.retailerClosedHiresCounts(query.retailerId, query.reportedPeriod);
        getResponse(res, ServerResponseCode.OK, { status: 'ok', data });
    };

    protected summary = async (req: Request, res: Response) => {
        const query = RequestParserFactory.query(req);
        this.logger.log({ message: 'Closed Hires Summary', data: query });
        if (!query.valid) {
            getResponse(res, ServerResponseCode.BAD_REQUEST, { status: 'error', message: `Invalid request ${JSON.stringify(query)}` });
            return;
        }

        const data: ClosedHireDto | null = await this.closedHiresStore.retailerClosedHires(query);

        if (data && data.closedHires.length > 0) {
            data.closedHires = OrderChargesFactory.byDuration(data.closedHires);
        }

        res.setHeader('Content-Type', 'application/json');

        getResponse(res, ServerResponseCode.OK, { status: 'ok', data });
    };

    protected summaryV2 = async (req: Request, res: Response) => {
        const query = RequestParserFactory.query(req);
        if (!query.valid) {
            getResponse(res, ServerResponseCode.BAD_REQUEST, { status: 'error', message: `Invalid request ${JSON.stringify(query)}` });
            return;
        }

        const filter = RequestParserFactory.filters(req);
        this.logger.log({ message: 'summaryV2 parameters', data: { query, filter } });
        const summary: ClosedHireDto | null = await this.closedHiresStore.retailerClosedHires(query);
        if (summary === null || summary.closedHires.length === 0) {
            getResponse(res, ServerResponseCode.OK, { status: 'ok', data: [] });
            return;
        }
        const summaryResponse = SummaryResponseFactory.mapToResponse(summary);
        const filteredSet = ClosedHiresFilter.filter(summaryResponse.closedHires, filter);
        summaryResponse.closedHires = OrderChargesFactory.byDuration(filteredSet);

        res.setHeader('Content-Type', 'application/json');
        getResponse(res, ServerResponseCode.OK, { status: 'ok', data: summaryResponse });
    };

    protected availableStats = async (req: Request, res: Response) => {
        this.logger.log({ message: 'Closed Hires Available Stats' });
        const retailerId = req.headers['retailer-id'] ? parseInt(req.headers['retailer-id'] as string) : 0;
        res.setHeader('Content-Type', 'application/json');
        if (retailerId === 0) {
            getResponse(res, ServerResponseCode.BAD_REQUEST, { status: 'error', message: 'Invalid retailer id' });
            return;
        }
        const data: ClosedHireAvailableStats[] | null = await this.closedHiresStore.availableStats(retailerId);
        getResponse(res, ServerResponseCode.OK, { status: 'ok', data });
    };

    protected export2csv = async (req: Request, res: Response) => {
        const csvHeader = 'Total days(A+B), Center days(B), AA Days(A), Customer Name, Customer VRN ,Fleet Type, Hire VRN , Checkout Date, Checkin Date';

        const query = RequestParserFactory.query(req);

        if (!query.valid) {
            getResponse(res, ServerResponseCode.BAD_REQUEST, { status: 'error', message: `Invalid request ${JSON.stringify(query)}` });
            return;
        }

        const filter = RequestParserFactory.filters(req);
        this.logger.log({ message: 'Closed Hires Summary', data: { query, filter } });
        const summary: ClosedHireDto | null = await this.closedHiresStore.retailerClosedHires(query);
        const csvData = Exprt2Csv.map(summary, filter);

        const csvString = [csvHeader].concat(csvData).join('\n');
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=closed-hires-${query.reportedPeriod}.csv`);
        res.send(csvString);
    };
}
