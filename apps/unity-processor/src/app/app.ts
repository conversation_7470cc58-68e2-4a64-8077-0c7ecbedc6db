import { AuditClient } from '@aa/audit-client';
import { isBreakdownCover } from '@aa/data-models/entities/breakdown-task';
import { BackendApplication } from '@aa/identifiers';
import { Address, Namespace, Entitlement, OutdoorEvents, OutdoorGarageBooking, OutdoorTaskComplete, GarageBookingStatusType } from '@aa/data-models/common';
import { BreakdownTask } from '@aa/data-models/entities/breakdown-task';
import { DataStoreProviderType, GarageBookingStore, NewOrderRequest, UnityStore } from '@aa/data-store';
import { Exception } from '@aa/exception';
import { Microservice } from '@aa/microservice';
import { EventHubCheckpoint, EventHubReceiver, EventHubReceiverConfig, QueueEventHandler, ServiceBusReceiver, ServiceBusReceiverConfig } from '@aa/queue';
import { BackendEnvironment, Utils } from '@aa/utils';

const appName = 'unity-processor';

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.UNITY_PROCESSOR;
    protected auxStreamReceiver: EventHubReceiver<OutdoorEvents.CREATE_GARAGE_BOOKING, OutdoorGarageBooking, void>;
    protected taskCompletionReceiver: ServiceBusReceiver<OutdoorEvents.COMPLETE_BREAKDOWN_TASK, OutdoorTaskComplete, void>;
    protected auditClient: AuditClient;
    protected unityStore: UnityStore;
    protected garageBookingStore: GarageBookingStore;
    private garageBookingDatabaseName = 'task';

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.REDIS, DataStoreProviderType.MONGODB, DataStoreProviderType.PRESTIGE, DataStoreProviderType.ORACLE]
        });

        this.auditClient = new AuditClient({
            application: BackendApplication.UNITY_PROCESSOR,
            connector: this.connector,
            operatorId: -1
        });

        this.unityStore = new UnityStore({
            logger: this.logger,
            dataStore: this.dataStore
        });

        this.garageBookingStore = new GarageBookingStore({
            databaseName: this.garageBookingDatabaseName,
            logger: this.logger,
            dataStore: this.dataStore
        });

        const checkpoint = new EventHubCheckpoint({
            accountUrl: environment.queue.storageAccountUrl || '',
            accountName: environment.queue.storageAccountName || '',
            accountKey: environment.queue.storageAccountKey || '',
            containerName: 'unity-processor',
            logger: this.logger
        });

        const ehBaseConfig: Omit<EventHubReceiverConfig, 'eventHubName' | 'consumerGroup' | 'checkpoint'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            maxBatchSize: 1, // we want to process even 1
            maxDelayPerBatch: 2 // 2s
        };

        this.auxStreamReceiver = new EventHubReceiver({
            ...ehBaseConfig,
            checkpoint,
            eventHubName: 'aux-stream',
            consumerGroup: 'unity-processor'
        });

        this.auxStreamReceiver.on(OutdoorEvents.CREATE_GARAGE_BOOKING, this.onBooking);

        const sbqBaseConfig: Omit<ServiceBusReceiverConfig, 'queueName'> = {
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            connectionString: environment.queue.SBQConnectionString,
            hasDeadletter: true
        };

        this.taskCompletionReceiver = new ServiceBusReceiver({
            ...sbqBaseConfig,
            queueName: 'unity-queue'
        });

        this.taskCompletionReceiver.on(OutdoorEvents.COMPLETE_BREAKDOWN_TASK, this.onCompleteBreakdownTask);
    }

    /**
     * Convert garage booking to the Unity req order
     * @param {BreakdownTask} task
     * @param {Entitlement} entitlement
     * @param {OutdoorGarageBooking} booking
     * @param {Partial<NewOrderRequest>} details
     * @param {number} accountId
     * @returns {Promise<NewOrderRequest>}
     * @protected
     */
    protected async getNewOrderFromBooking(
        task: BreakdownTask,
        entitlement: Entitlement,
        booking: OutdoorGarageBooking,
        accountId: number,
        details?: Partial<NewOrderRequest>
    ): Promise<NewOrderRequest> {
        // get customer address if not provided during booking
        const address: Address = Object.assign(entitlement.contact.address, booking.customer.address);

        const taskPhoneDef = entitlement.contact?.phones[0];
        let mobileNumber: string;
        if (typeof taskPhoneDef === 'string') {
            mobileNumber = taskPhoneDef;
        } else {
            mobileNumber = `${taskPhoneDef?.extension || ''}${taskPhoneDef?.phoneNumber || ''}`;
        }

        // Check if Breakdown Cover
        const isBRC = isBreakdownCover(task);
        // get staff number for operator id
        const staffNumber = Utils.backfill(booking.staffNumber.toString(), 6, '0');

        const defaults: Omit<NewOrderRequest, 'items' | 'voucher'> = {
            garageId: booking.job.garageId,
            accountId,
            externalId: booking.taskId.toString(),
            date: new Date(),
            customer: {
                title: entitlement.contact.title || '',
                firstName: entitlement.contact.firstName || '',
                lastName: entitlement.contact.surname || '',
                mobileNumber: booking.customer.mobileNumber || mobileNumber || '',
                postCode: booking.customer.postCode || '',
                email: booking.customer.email || ''
            },
            vehicleDetails: {
                makeName: task.vehicle?.makeId?.toString() || '',
                model: task.vehicle?.model?.name || '',
                registration: task.vehicle?.registration || '',
                variant: task.vehicle?.typeId?.toString() || ''
            },
            comments: `AA smart breakdown | notes: ${booking.job.notes || 'n/a'}`,
            vehicleCollectionDetails: {
                address: {
                    line1: address.addressLines[0] || '',
                    line2: address.addressLines[1] || '',
                    line3: address.addressLines[2] || '',
                    line4: address.addressLines[3] || '',
                    town: '',
                    county: '',
                    postcode: address.postcode || ''
                },
                comments: `AA smart breakdown | collection: to be agreed on arrival`
            },
            isBRC,
            staffNumber
        };

        // if is BRC, that's all data we need
        if (isBRC) {
            // return defaults and apply custom details
            return { ...defaults, ...details, isBRC };
        }

        // if not BRC, we need to add extra data used by the smartcare team after req reaches UNITY

        // Voucher data required to receive signup emails - RBAUAA-3445
        const voucher = {
            code: 'TOWIN',
            discountDetails: {
                REPAIR: {
                    fixedAmount: 0.0,
                    percentageAmount: 10.0
                }
            }
        };
        // required for SmartCare payments to work (INC001822844)
        const items = [
            {
                id: 99994,
                priceDetails: {
                    netPrice: 29.17,
                    totalPrice: 35.0,
                    vat: 5.83,
                    vatable: 35.0,
                    discountApplied: 0,
                    totalPriceBeforeDiscount: 35.0
                },
                fullDescription: 'Investigate and report',
                comments: '',
                legallyRequired: null,
                status: null
            }
        ];

        // return defaults, apply custom details and smartcare specific data
        return { ...defaults, ...details, items, voucher };
    }

    protected onBooking: QueueEventHandler<OutdoorEvents.CREATE_GARAGE_BOOKING, OutdoorGarageBooking, void> = async (context) => {
        try {
            const {
                entry: { data }
            } = context;
            this.logger.info({ sourceName: this.name, message: 'Booking event received', data: { data } });

            const trace = this.auditClient.getTrace(Namespace.UNITY, data.taskId.toString());
            await this.auditClient.reportAction(trace, {
                message: 'Received garage booking event',
                data
            });

            // Check status of the booking only if data has aahBookingRequestId
            if (data.aah2BookingRequestId) {
                const bookingStatus = await this.garageBookingStore.getStatusByAah2BookingRequestId(data.aah2BookingRequestId);
                // If booking status is either 'success' or 'failed' then we should not process the booking
                // In other case, booking status must be 'pending' then we can process the booking
                if (bookingStatus && (bookingStatus.status === GarageBookingStatusType.SUCCESS || bookingStatus.status === GarageBookingStatusType.FAILED)) {
                    this.logger.info({
                        sourceName: this.name,
                        message: `Booking status is ${bookingStatus.status}, hence no need to call Unity`,
                        data: { bookingStatus }
                    });
                    return;
                }
            }

            const task = await this.connector.task.find.byId(data.taskId);
            if (!task) {
                throw new Exception({
                    message: 'Unable to find task for the booking',
                    data: { context }
                });
            }

            // to get customer details we need to fetch CR and use it find entitlement
            const customerRequests = await this.connector.customerRequest.find.byId(data.taskId);
            const customerRequest = customerRequests && customerRequests.length ? customerRequests[0] : undefined;
            if (!customerRequest) {
                throw new Exception({
                    message: 'Unable to find Customer request for the booking',
                    data: { context }
                });
            }
            const entitlements = await this.connector.entitlement.find.byCr(customerRequest, false);
            // lets pick the most recent entitlement
            const entitlement = entitlements && entitlements.length ? entitlements[0] : undefined;
            if (!entitlement) {
                throw new Exception({
                    message: 'Unable to find entitlement for the booking',
                    data: { context }
                });
            }

            const order = await this.getNewOrderFromBooking(task, entitlement, data, this.unityStore.accountId, {});
            const booking = await this.unityStore.submitNewOrder(data.taskId, order);

            // Update booking status to success only
            //  1. if data has aahBookingRequestId (i.e. booking request came via V2 endpoint)
            //  2. and only if unity has returned bookingId as well (as per code review dated 01-APR-2025)
            // Otherwise status should be pending and keep it that way only
            if (data.aah2BookingRequestId && booking.bookingId) {
                try {
                    await this.garageBookingStore.upsertBookingStatus(data.aah2BookingRequestId, data.taskId, GarageBookingStatusType.SUCCESS, booking.bookingId);
                } catch (innerErr) {
                    this.logger.error({
                        sourceName: this.name,
                        message: 'Error while updating garage booking status to success',
                        data: { innerErr }
                    });
                }
            }

            await this.auditClient.reportAction(trace, {
                message: 'Garage booked',
                booking
            });
        } catch (error) {
            // Update booking status to failed only if context has aahBookingRequestId
            if (context && context.entry && context.entry.data && context.entry.data.aah2BookingRequestId) {
                try {
                    const aah2BookingRequestId = context.entry.data.aah2BookingRequestId;
                    const taskId = context.entry.data.taskId;
                    await this.garageBookingStore.upsertBookingStatus(aah2BookingRequestId, taskId, GarageBookingStatusType.FAILED, undefined, `unity-processor -> onBooking -> ${error.message}`);
                } catch (innerErr) {
                    this.logger.error({
                        sourceName: this.name,
                        message: 'Error while updating garage booking status to failed',
                        data: { innerErr }
                    });
                }
            }

            throw new Exception({
                message: 'Error while processing garage booking event from EH',
                data: { context },
                error
            });
        }
    };

    protected onCompleteBreakdownTask: QueueEventHandler<OutdoorEvents.COMPLETE_BREAKDOWN_TASK, OutdoorTaskComplete, void> = async (context) => {
        try {
            const {
                entry: { data }
            } = context;
            this.logger.info({
                sourceName: this.name,
                message: 'On complete event received',
                data: { data }
            });

            const trace = this.auditClient.getTrace(Namespace.UNITY, data.taskId.toString());
            await this.auditClient.reportAction(trace, {
                message: 'Received task completion event',
                data
            });
        } catch (error) {
            throw new Exception({
                message: 'Error while sending event to SBQ',
                data: { context },
                error
            });
        }
    };
}
