import { AuditClient } from '@aa/audit-client';
import { AuditEvent, PaginationQuery } from '@aa/data-models/common';
import { AuditStore } from '@aa/data-store';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubSender } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';

export class App extends Microservice {
    public name = 'Audit';
    public application = BackendApplication.AUDIT;
    protected auditStreamSender: EventHubSender<string, AuditEvent>;
    protected store: AuditStore;

    constructor(environment: BackendEnvironment) {
        super({ environment, appName: 'audit' });
        this.store = new AuditStore({
            logger: this.logger,
            dataStore: this.dataStore
        });

        // create universal queue sender
        this.auditStreamSender = new EventHubSender({
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            eventHubName: 'audit-stream',
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint
        });

        this.server.post('/read', this.auditRead);
        this.server.post('/write', this.auditWrite);
    }

    protected auditWrite = async (req: Request, res: Response) => {
        try {
            const { body } = req as { body: AuditEvent };
            this.logger.log({ message: 'Audit event', data: { body } });
            const namespace = body.namespace.toLowerCase();
            await this.auditStreamSender.send(`create/audit/${namespace}`, body, false);
            return getResponse(res, ServerResponseCode.ACCEPTED);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    protected auditRead = async (req: Request, res: Response) => {
        try {
            const { trace, limit, skip, latestFirst } = req.body as {
                trace: string;
                latestFirst?: boolean;
            } & PaginationQuery;
            this.logger.log(`Audit event read for trace ${trace}`);
            const { namespace } = AuditClient.decodeTrace(trace);

            const result = await this.store.getAuditEvents({
                namespace,
                trace,
                limit,
                skip,
                latestFirst
            });

            if (result && result.results.length) {
                return getResponse(res, ServerResponseCode.OK, result);
            } else {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
}
