import { CustomerGroupCode, EcallTaskData } from '@aa/data-models/common';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubSender } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';

enum EcallTaskEventTypes {
    CREATE_ECALL_TASK = 'create/task/ecall'
}

export class App extends Microservice {
    public name = 'Ecall task';
    public application = BackendApplication.ECALL_TASK;
    protected auxStreamSender: EventHubSender<EcallTaskEventTypes, EcallTaskData & { customerGroup: CustomerGroupCode }>;

    constructor(environment: BackendEnvironment) {
        super({ environment, appName: 'ecall-task' });

        // create universal queue sender
        this.auxStreamSender = new EventHubSender({
            logger: this.logger,
            context: this.context,
            dataStore: this.dataStore,
            system: this.system,
            eventHubName: 'aux-stream',
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint
        });

        this.server.post('/create', this.create);
    }

    protected create = async (req: Request, res: Response) => {
        try {
            const { body } = req;
            this.logger.log('Ecall Task');
            this.logger.log(body);
            const queueId = await this.auxStreamSender.send(EcallTaskEventTypes.CREATE_ECALL_TASK, body, true);
            return getResponse(res, ServerResponseCode.ACCEPTED, { queueId });
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
}
