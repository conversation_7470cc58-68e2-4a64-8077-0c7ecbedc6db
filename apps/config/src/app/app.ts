import { AuditOperation, PaginationQuery, PaginationQueryResult } from '@aa/data-models/common';
import { DataStoreProviderType, MongodbDataProvider } from '@aa/data-store';
import { EventSource, EventSourceMeta, SanitizedEntity } from '@aa/event-source';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment, PartialBy } from '@aa/utils';
import { Request, Response } from 'express';

// TODO: move away from the EventSource, switch to the standard audit

type ConfigValue = string | number | boolean | Array<string | number | boolean> | undefined;

interface ConfigEntryBase {
    created: Date;
    updated: Date;
    namespace: string;
    name: string;
    type: ConfigType;
}

enum ConfigType {
    SECRET = 'SECRET',
    VALUE = 'VALUE'
}

interface ConfigValueEntry<T extends ConfigValue = ConfigValue> {
    type: ConfigType.VALUE;
    value: T;
}

interface ConfigSecretEntry {
    type: ConfigType.SECRET;
    value: string;
    expiresIn?: Date;
    notBefore?: Date;
}

type ConfigEntry<T extends ConfigValue = ConfigValue> = ConfigEntryBase & (ConfigValueEntry<T> | ConfigSecretEntry);

function isValidValueSetPayload(val: unknown): val is ConfigEntryBase & ConfigValueEntry {
    return (
        !!val &&
        typeof val === 'object' &&
        'namespace' in val &&
        typeof val.namespace === 'string' &&
        'name' in val &&
        typeof val.name === 'string' &&
        'type' in val &&
        val.type === ConfigType.VALUE &&
        'value' in val &&
        (['string', 'number', 'boolean'].includes(typeof val.value) || Array.isArray(val.value))
    );
}

function isValidSecretSetPayload(val: unknown): val is ConfigEntryBase &
    Omit<ConfigSecretEntry, 'expiresIn' | 'notBefore'> & {
        expiresIn?: string;
        notBefore?: string;
    } {
    return (
        !!val &&
        typeof val === 'object' &&
        'namespace' in val &&
        typeof val.namespace === 'string' &&
        'name' in val &&
        typeof val.name === 'string' &&
        'type' in val &&
        val.type === ConfigType.SECRET &&
        'value' in val &&
        typeof val.value === 'string' &&
        'expiresIn' in val &&
        typeof val.expiresIn === 'string' &&
        'notBefore' in val &&
        typeof val.notBefore === 'string'
    );
}

function isValidNamespace(val: unknown): val is string {
    return typeof val === 'string';
}

function isValidPaginationQuery(val: unknown): val is PaginationQuery {
    return !!val && typeof val === 'object' && (('limit' in val && typeof val.limit === 'number') || !('limit' in val)) && (('skip' in val && typeof val.skip === 'number') || !('skip' in val));
}

function isValidName(val: unknown): val is string {
    return typeof val === 'string';
}

function isConfigSecretEntry(val: ConfigEntry | unknown): val is ConfigSecretEntry {
    return !!val && typeof val === 'object' && 'type' in val && val.type === ConfigType.SECRET && 'value' in val && typeof val.value === 'string';
}

export class App extends Microservice {
    public name = 'Config';
    public application = BackendApplication.CONFIG;
    protected provider: MongodbDataProvider;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName: 'config',
            dataStoreProviders: [DataStoreProviderType.REDIS, DataStoreProviderType.MONGODB, DataStoreProviderType.ORACLE]
        });

        this.provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);

        /**
         * TODO: Release related API - do we need it?
         */
        // this.server.get('/release', this.getCurrentRelease);
        // this.server.get('/release/:release', this.getRelease);
        // this.server.post('/release/:release', this.setRelease);
        // this.server.get('/config/:release/:namespace', this.getConfigs);
        // this.server.post('/config/:release/:namespace', this.getConfigs);
        // this.server.get('/config/:release/:namespace/:name', this.getConfig);

        /**
         * Query config by name
         */
        this.server.post('/config', this.getConfigs);
        this.server.get('/config/:namespace', this.getConfigs);
        this.server.post('/config/:namespace', this.getConfigs);
        this.server.get('/config/:namespace/:name', this.getConfig);
        this.server.post('/config/:namespace/:name', this.getConfig);
        // this.server.post('/config/:namespace/:name/history', this.getConfigHistory);
        this.server.post('/config/:namespace/:name', this.setConfig);
    }

    public async init(): Promise<void> {
        await super.init();
        this.setupCron();
    }

    protected setupCron() {
        // TODO:
        //  - on the start check if any remote queue active to rotate secrets
        //  - if none, set job to rotate expired or close to expiration secrets
    }

    /**
     * Retrieve secret for config entry
     * @param {ConfigSecretEntry} entry
     * @return {Promise<string>}
     * @protected
     */
    protected async getSecretValue(entry: ConfigSecretEntry): Promise<string> {
        // TODO: return from Azure KV in the future
        return entry.value;
    }

    protected getValues(entries: PartialBy<Pick<ConfigEntry, 'namespace' | 'name' | 'value'>, 'value'>[]): { namespace: string; name: string; value: ConfigValue }[] {
        // TODO: return from Azure KV in the future
        return entries.map(({ namespace, name, value }) => {
            return { namespace, name, value };
        });
    }

    protected getConfigs = async (
        req: Request<
            { namespace?: string },
            unknown,
            | ({
                  onlyValues?: boolean;
              } & PaginationQuery)
            | undefined
        >,
        res: Response
    ): Promise<void> => {
        try {
            const { namespace } = req.params;
            // be default no limit on query and only values
            const { limit = -1, skip = 0, onlyValues = true } = req.body || {};

            if (!isValidNamespace(namespace) || !isValidPaginationQuery(req.body)) {
                return getResponse(res, ServerResponseCode.BAD_REQUEST);
            }

            const collection = await this.provider.collection<ConfigEntry>('system-config', 'environments');

            const store = new EventSource({
                logger: this.logger,
                collection,
                aggregationKeys: ['name', 'namespace']
            });

            // if namespace provided, return only keys with this namespace
            let filter = undefined;
            if (namespace) {
                filter = { namespace };
            }

            // get all possible entry keys from the store
            const availableEntryKeys = await store.keys({
                limit,
                skip,
                filter
            });

            if (!availableEntryKeys.results.length) {
                return getResponse(res, ServerResponseCode.NOT_FOUND, []);
            }

            // use retrieved keys to retrieve snapshot of all values
            const requests = availableEntryKeys.results.map((query) => {
                return store.get(query);
            });
            const results = await Promise.all(requests);
            const finalResults = results.filter((entry) => typeof entry !== 'undefined') as unknown as SanitizedEntity<ConfigEntry, 'name' | 'namespace'>[];

            // if only values and default val for pagination - return only results without pagination wrapper
            if (onlyValues && limit === -1 && skip === 0) {
                const values = this.getValues(finalResults);
                return getResponse(res, ServerResponseCode.OK, values);
            }

            // if we are retrieving entries
            // if we are retrieving only values but paginated
            const paginatedResults: PaginationQueryResult = {
                results: [],
                more: availableEntryKeys.more
            };
            if (onlyValues) {
                paginatedResults.results = this.getValues(finalResults);
            } else {
                paginatedResults.results = results;
            }

            return getResponse(res, ServerResponseCode.OK, paginatedResults);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while retrieving CUV data'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Get config
     * @param {e.Request<{namespace?: string, name?: string}, unknown, {onlyValue?: boolean}>} req
     * @param {e.Response} res
     * @return {Promise<void>}
     */
    protected getConfig = async (req: Request<{ namespace?: string; name?: string }, unknown, { onlyValue?: boolean }>, res: Response): Promise<void> => {
        try {
            const { namespace, name } = req.params;
            const { onlyValue = true } = req.body || {};

            if (!isValidNamespace(namespace) || !isValidName(name)) {
                return getResponse(res, ServerResponseCode.BAD_REQUEST);
            }

            const collection = await this.provider.collection<ConfigEntry>('system-config', 'environments');

            const store = new EventSource({
                logger: this.logger,
                collection,
                aggregationKeys: ['name', 'namespace']
            });

            const entry = await collection.findOne({ name, namespace });

            if (!entry) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }

            // if we are asked to retrieve only value
            if (onlyValue) {
                // if secret, value is not readable straight away
                let value = entry.value;
                if (isConfigSecretEntry(entry)) {
                    value = await this.getSecretValue(entry);
                }

                return getResponse(res, ServerResponseCode.OK, { value });
            }

            // return whole entry
            return getResponse(res, ServerResponseCode.OK, entry);
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while retrieving CUV data'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };

    /**
     * Create or update config entry
     * @param {
     *      e.Request<{namespace?: string, name?: string}, unknown,
     *      Omit<ConfigEntry, 'name' | 'namespace' | 'created' | 'updated'>>
     * } req
     * @param {e.Response} res
     * @return {Promise<void>}
     */
    protected setConfig = async (req: Request<{ namespace?: string; name?: string }, unknown, Omit<ConfigEntry, 'name' | 'namespace' | 'created' | 'updated'>>, res: Response): Promise<void> => {
        try {
            const { namespace, name } = req.params;
            const payload = req.body;

            if (!isValidNamespace(namespace) || !isValidName(name)) {
                return getResponse(res, ServerResponseCode.BAD_REQUEST);
            }
            if (!isValidSecretSetPayload(payload) && !isValidValueSetPayload(payload)) {
                return getResponse(res, ServerResponseCode.BAD_REQUEST);
            }

            const { value, type } = payload;

            const collection = await this.provider.collection<ConfigEntry>('system-config', 'environments');

            const store = new EventSource({
                logger: this.logger,
                collection,
                aggregationKeys: ['name', 'namespace']
            });

            let upsertEntry: SanitizedEntity<ConfigEntry, 'name' | 'namespace'>;
            if (isValidSecretSetPayload(payload)) {
                const { expiresIn, notBefore } = payload;
                upsertEntry = {
                    name,
                    namespace: namespace,
                    updated: new Date(),
                    ...({
                        type,
                        value,
                        expiresIn: expiresIn && new Date(expiresIn),
                        notBefore: notBefore && new Date(notBefore)
                    } as ConfigSecretEntry)
                };
            } else {
                upsertEntry = {
                    name,
                    namespace: namespace,
                    updated: new Date(),
                    ...({ type, value } as ConfigValueEntry)
                };
            }
            const meta: EventSourceMeta = {
                action: AuditOperation.UPDATE,
                operatorId: this.context.store.operatorId
            };

            const baseEntry = await store.get({ name, namespace });
            // if first ever entry
            if (!baseEntry) {
                meta.action = AuditOperation.CREATE;
                upsertEntry.created = new Date();
            }

            await store.set(upsertEntry, meta);

            return getResponse(res, ServerResponseCode.OK, {});
        } catch (error) {
            const exception = new Exception({
                error,
                message: 'Failure while retrieving CUV data'
            });
            this.logger.error(exception);
            return getResponse(res, ServerResponseCode.SERVER_ERROR);
        }
    };
}
