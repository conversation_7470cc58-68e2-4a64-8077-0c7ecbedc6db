'use strict';
const { Address } = require('@aa/data-models/common');
var formatting = require('@aa/utilities/aa-utilities').formatting;

function createAddress(sourceAddress) {
    var address = new Address();
    var addressLines = [];
    for (var i = 0; i < sourceAddress.lines.length; i++) {
        addressLines.push(formatting.toTitleCase(sourceAddress.lines[i]));
    }

    address.addressLines(addressLines);
    address.postcode(sourceAddress.postcode);

    return address;
}

module.exports = {
    createAddress: createAddress
};
