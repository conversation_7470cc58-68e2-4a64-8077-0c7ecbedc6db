import { init, connect, release } from '@aa/oracle-utilities';

import * as oracledb from 'oracledb';
const appName = 'sap-processor';

export class OracleDatabaseConnection {
    public static async init() {
        return await init({
            connectStrings: process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : ['sun54:1521/VACSH'],
            user: process.env.cshUser || 'Vanda',
            password: process.env.cshPassword || 'Va.77.av',
            appName
        });
    }

    public static async connect() {
        return await connect();
    }

    public static async disconnect() {
        return await release();
    }

    public static jsonify(metadata: any, data: any) {
        const retVal: any = {};

        metadata.forEach((item: any, i: any) => {
            retVal[item.name] = data[i];
        });

        return retVal;
    }
}
