import { ErrorResponse } from '@aa/connector';
import { PaginationQuery } from '@aa/data-models/common';
import { DataStore, DataStoreProviderType, PoiStore } from '@aa/data-store';
import { Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';

const appName = 'poi-facade';

export class App extends Microservice {
    public name = 'Poi facade';
    public application = BackendApplication.POI_FACADE;
    protected legacyPOIDatastore: DataStore;
    protected poiMongodbDatabase: string;
    protected store: PoiStore;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.REDIS, DataStoreProviderType.MONGODB, DataStoreProviderType.ORACLE]
        });
        const {
            mongodb: { poiMongodbDatabase }
        } = environment;

        this.poiMongodbDatabase = poiMongodbDatabase;

        // For POIs we are using old mongodb
        this.legacyPOIDatastore = new DataStore({
            logger: this.logger,
            providers: [
                {
                    type: DataStoreProviderType.MONGODB,
                    config: {
                        logger: this.logger,
                        system: this.system,
                        connectionString: environment.mongodb.mongodbUrl,
                        appName
                    }
                }
            ]
        });

        this.store = new PoiStore({
            logger: this.logger,
            dataStore: this.legacyPOIDatastore,
            databaseName: this.poiMongodbDatabase
        });

        this.server.post('/categories', this.categories, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.get('/category/:categoryId/parent', this.categoryParent, {
            allowedRoles: ['outdoor'],
            protect: false
        });
        this.server.post('/pois/:categoryId', this.pois, {
            allowedRoles: ['outdoor'],
            protect: false
        });
    }

    protected categories = async (req: Request<unknown, unknown, PaginationQuery>, res: Response) => {
        const { limit = -1, skip = 0 } = req.body;

        req.body;
        try {
            // this endpoint is used by Outdoor only, so we assume some filters
            const response = await this.store.categories({
                limit,
                skip,
                isAAHelp: true,
                isEva: true
            });
            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    protected categoryParent = async (req: Request<{ categoryId: string }>, res: Response) => {
        try {
            const categoryId = Number.parseInt(req.params.categoryId);
            const response = await this.store.categoryParent({ categoryId });
            if (!response) {
                return getResponse(res, ServerResponseCode.NOT_FOUND);
            }
            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    protected pois = async (req: Request<{ categoryId: string }, unknown, { fromDate?: string } & PaginationQuery>, res: Response) => {
        // TODO: implement fromDate to fetch only pois that were updated since fromDate
        const categoryId = Number.parseInt(req.params.categoryId);
        const { limit = -1, skip = 0 } = req.body;

        try {
            // this endpoint is used by Outdoor only, so we assume some filters
            const response = await this.store.getPois({
                categoryId,
                limit,
                skip,
                isAAHelp: true,
                isEva: true
            });
            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: ErrorResponse = {
                status: 'ERROR',
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };
}
