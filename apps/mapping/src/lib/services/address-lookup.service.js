'use strict';

// Vendor Libraries
const logger = require('../helper/logger.helper');
// AA Libraries
const aaEndpoints = require('@aa/endpoints');
const { Address } = require('@aa/data-models/common');
// Local Imports
const queryBuilder = require('../repository/query-builder');

module.exports = class AddressLookupService {
    constructor(mappingService, locationService) {
        this._aaEndpoints = aaEndpoints;
        this._logger = logger;
        this._mappingService = mappingService;
        this._locationService = locationService;
    }

    _getCorrectLocation(originalPostCode, Locations) {
        //Some locations will be completely insane and will have a wrong post code. Thanks google.
        //let's find the one that has the same post code as the original postCode
        const locationWithSamePostCode = Locations.find((location) => {
            const addressComponentWithPostCode = location.address_components.find((ac) => ac.types.includes('postal_code'));
            const locationPostCode = addressComponentWithPostCode.short_name;
            return locationPostCode.replace(/\s+/g, '') === originalPostCode.replace(/\s+/g, '');
        });

        return locationWithSamePostCode;
    }

    async _addGeoLocationToAddress(item) {
        const address = new Address(item);
        try {
            this._logger.info('Geocoding address', address.addressAsString());
            const locations = await this._mappingService.geocode({
                address: address.addressAsString()
            });
            const location = this._getCorrectLocation(item.postcode, locations) || locations[0];
            return {
                ...item,
                location
            };
        } catch (ex) {
            this._logger.error('Could not geocode address', ex, address.addressAsString());
            return null;
        }
    }

    async _getLatLongForAll({ addresses }, authToken) {
        const promises = addresses.map(async (item) => {
            const addressWithGeolocation = this._addGeoLocationToAddress(item);
            return addressWithGeolocation;
        });
        let allLocations = await Promise.all(promises);
        allLocations = allLocations.filter(Boolean);

        if (!allLocations.length) {
            return [];
        }

        //we now need to get the dangerous location status of the first result and set it to all addresses.
        const locationForDangerousLocationCheck = allLocations[0].location;
        const { dangerousLocation } = await this._locationService.dangerousLocationCheck({ location: locationForDangerousLocationCheck }, authToken);
        allLocations = allLocations.map((el) => ({ ...el, dangerousLocation }));
        return allLocations;
    }

    async lookup(firstLineOfAddress, postCode, authToken) {
        let address = firstLineOfAddress !== '' ? `${firstLineOfAddress}/${postCode}` : postCode;
        try {
            let resp = await this._aaEndpoints.restfulRequest(queryBuilder._getQueryBuilder('/api/address-lookup-service/addresses/' + address, {}, authToken));
            let data = await this._getLatLongForAll({ addresses: resp.data }); //we add geolocationData for each address as stated on RBAUAA-818
            return data;
        } catch (ex) {
            this._logger.error('Could not address lookup', JSON.stringify({ firstLineOfAddress, postCode }), ex.stack || ex.msg || ex.message || ex);
            return Promise.reject(ex);
        }
    }
};
