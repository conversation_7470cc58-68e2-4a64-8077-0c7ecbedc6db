'use strict';

var _ = require('lodash'),
    Q = require('q'),
    CONST = require('../const'),
    geoSearchFactory = require('../factory/geosearch-queries.factory'),
    poiNearestJunction = require('../factory/poi-nearest-junction.factory'),
    oracledb = require('oracledb'),
    logger = require('winston');
const { migration } = require('@aa/backend-migration');

function PoiRepository(collection) {
    this.poiCollection = collection;
    this.poiCollectionBackup = collection.s.db.collection('pois-backup');
}
function _processDataSet(dataSet) {
    return dataSet.map((poi) => {
        let data = _.cloneDeep(poi);
        data.retailer = {
            categoryId: poi.properties.categoryId, // add category id so ui can group results ..
            id: poi.properties.SUP_RESOURCE_ID || poi.properties.supResourceId || poi.properties.UID || poi.properties.uid,
            name: poi.properties.name,
            address: poi.properties.street + ',' + poi.properties.area + ',' + poi.properties.postcode,
            telephone: poi.properties.telephone,
            distanceFromSrc: Math.round(poi.distance * 0.00062137119223733),
            driveTimeFromSrc: Math.round(poi.duration / 60),
            openingTimes: poi.properties.comments,
            location: {
                longitude: poi.geometry.coordinates[0],
                latitude: poi.geometry.coordinates[1]
            }
        };
        return data;
    });
}

/**
 * search within box
 * @param  {Object} postData [description]
 * @param  {int[]} postData.catIds list of ids to search for , optional
 * @param  {number[2]} postData.swCorner  south west corner (lng,lat)
 * @param  {number[2]} postData.neCorner  north est corner (lng, lat)
 * @return {defer.promise}           [description]
 */
PoiRepository.prototype.searchWithInBox = function (postData) {
    const query = geoSearchFactory.boxSeach(postData);
    return this.poiCollection.find(query).toArray();
};

/**
 * Search for POIs within a radius
 * @param {{
 *          catIds: number[], // list of ids to search for , optional
 *          centre: [long: number, lat: number],  // centre of search (lng,lat)
 *          radius: number //  radius  of search in miles
 *        }} query
 * @return {Promise<unknown[]>}
 */
PoiRepository.prototype.searchWithinRadius = function (query) {
    const proximityQuery = geoSearchFactory.radiusSearch(query);
    return this.poiCollection.find(proximityQuery).toArray();
};

/**
 * Search for POIs within a radius, increase the radius by radiusIncrement up to maxRadius
 * until we find minResults count
 * @template {{
 *          catIds: number[], // list of ids to search for , optional
 *          centre: [long: number, lat: number],  // centre of search (lng,lat)
 *          radius: number //  radius  of search in miles
 *        }} QUERY
 * @param {QUERY} query
 * @param {number} minResults Minimum count of results required
 * @param {number} maxRadius Maximum radius of search
 * @default 100 miles
 * @param {number} radiusIncrement Increment of radius search until we find minResults
 * @default 5 miles
 * @return {Promise<unknown[]>}
 */
PoiRepository.prototype.searchWithinRadiusRecursive = function (query, minResults, maxRadius = 100, radiusIncrement = 5) {
    if (!minResults) {
        throw new Error(`PoiRepository :: searchWithinRadiusRecursive :: Please provide minResults`);
    }

    /**
     * Recursive search
     * @param {unknown[]} prevResults
     * @param {number} prevRadius
     * @return {Promise<unknown[]>}
     */
    let i = 0;

    const search = (prevResults, prevRadius) => {
        console.log(`searchWithinRadiusRecursive iteration ${i}`, {
            prevResults,
            prevRadius
        });
        // if reached desired result count
        if (prevResults.length >= minResults) {
            console.log(`searchWithinRadiusRecursive reached desired results ${prevResults.length}`);
            return Promise.resolve(prevResults);
        }

        // if reached max radius
        if (prevRadius >= maxRadius) {
            console.log(`searchWithinRadiusRecursive reached max radius ${prevRadius}`);
            return Promise.resolve(prevResults);
        }

        // else increase radius and search again
        const newRadius = prevRadius + radiusIncrement;
        const alteredQuery = { ...query, radius: newRadius };
        i++;

        const proximityQuery = geoSearchFactory.radiusSearch(alteredQuery);
        return this.poiCollection
            .find(proximityQuery)
            .toArray()
            .then((results) => {
                return search(results, newRadius);
            });
    };

    // initialise recursive search
    return search([], query.radius - radiusIncrement);
};

/**
 * search all POI by Id
 * @param  {Object} postData [description]
 * @param  {int[]} postData.pois list of ids to search for , optional
 * @return {default.promise}
 */
PoiRepository.prototype.searchAllPOIById = function (postData) {
    const query = geoSearchFactory.searchByPoiId(postData);
    return this.poiCollection.find(query).toArray();
};

/**
 * recursive function to find
 * @param  {[type]} repo     [description]
 * @param  {[type]} postData promise       [description]
 * @return {[type]}          [description]
 */
var _findNearestJunction = function (repo, postData, promise) {
    const query = geoSearchFactory.radiusSearch(postData);
    repo.poiCollection
        .find(query)
        .toArray()
        .then((dataSet) => {
            if (dataSet.length < 25) {
                postData.radius++;
                _findNearestJunction(repo, postData, promise);
            } else {
                promise.resolve(dataSet);
            }
        })
        .catch((err) => {
            promise.reject(err);
        });
};

PoiRepository.prototype.remove = function remove(query) {
    return this.poiCollection.remove(query);
};

PoiRepository.prototype.findNearestJunction = function (postData) {
    var deferred = new Q.defer(),
        promise = deferred.promise,
        radius = 1,
        catIds = [CONST.poiCategoryIds.junction];

    _findNearestJunction(
        this,
        {
            center: postData.center,
            radius: radius,
            catIds: catIds
        },
        deferred
    );

    return promise;
};

PoiRepository.prototype.write = function write(data) {
    return this.poiCollection.insert(data, { w: 0 });
};

PoiRepository.prototype.searchMarkerPost = function searchMarkerPost(data) {
    const query = geoSearchFactory.markerPostSearch(data);
    return this.poiCollection
        .find(query)
        .toArray()
        .then((dataSet) => {
            return poiNearestJunction.addJunctions(dataSet);
        });
};

PoiRepository.prototype.searchSosBox = function searchSosBox(data) {
    const query = geoSearchFactory.sosBoxSearch(data);
    return this.poiCollection
        .find(query)
        .toArray()
        .then((dataSet) => {
            return poiNearestJunction.addJunctions(dataSet);
        });
};

PoiRepository.prototype.searchJunction = function searchJunction(data) {
    const query = geoSearchFactory.junctionPoiSearch(data);
    return this.poiCollection.find(query).toArray();
};

PoiRepository.prototype.searchRetailer = function searchRetailer(postData) {
    let query = {
        $or: [{ 'properties.supResourceId': postData >> 0 }, { 'properties.SUP_RESOURCE_ID': postData >> 0 }]
    };

    return this.poiCollection
        .find(query)
        .toArray()
        .then((dataSet) => {
            return _processDataSet(dataSet);
        });
};

PoiRepository.prototype.dealerSearch = function dealerSearch(data) {
    const page = _.isNumber(data.page) ? data.page : 0,
        pageSize = _.isNumber(data.pageSize) ? data.pageSize : 10;

    const query = geoSearchFactory.dealerSearch(data);
    return this.poiCollection
        .find(query)
        .skip(pageSize * page)
        .limit(pageSize)
        .toArray()
        .then((dataSet) => {
            return _processDataSet(dataSet);
        });
};

PoiRepository.prototype.searchPoisByCategoryId = function searchPoisByCategoryId(postData) {
    let query = {
        'properties.categoryId': postData
    };

    return this.poiCollection
        .find(query, { projection: { _id: 0 } })
        .toArray()
        .then((dataSet) => {
            return _processDataSet(dataSet);
        });
};

function poiEntry(supplier, categoryId) {
    const lines = supplier.address.split(',');
    const postcode = lines[lines.length - 1].trim().split(' ');
    return {
        type: 'Feature',
        geometry: {
            type: 'Point',
            coordinates: [supplier.longitude, supplier.latitude]
        },
        properties: {
            categoryId: parseInt(categoryId),
            name: supplier.name,
            street: lines[0].trim(),
            area: lines[1].trim(),
            postcode: `${postcode[0].trim()} ${postcode[postcode.length - 1].trim()}`,
            countryCode: 'UK',
            route: 'NULL',
            isIntersection: false,
            joiningStreet: null,
            junction: null,
            telephone: supplier.telephone,
            heightRestriction: 0,
            widthRestriction: 0,
            dangerousLocation: '0    ',
            comments: supplier.comments,
            locationPrompt: null,
            updateLabel: null,
            dataSource: null,
            pointXReferenceNumber: null,
            pointXClassificationCode: null,
            actionId: 0,
            lastUpdated: new Date(),
            isAAHelp: true,
            isEva: true,
            isActive: true,
            epyxId: null,
            automyzeId: supplier.id,
            ntId: null,
            extNumber: supplier.telephone,
            county: null,
            id: supplier.id,
            uid: supplier.id,
            gbCode: null,
            SUP_RESOURCE_ID: supplier.id,
            supResourceId: supplier.id
        }
    };
}

PoiRepository.prototype.upsertPois = async function upsertPois(categoryId, supNetworkCode, supResourceId) {
    // Trying to safe guard POIS from being overwritten by other categories
    const eligibleCategoryIds = [3531, 3538, 3555, 3528, 3513, 3563];
    if (!eligibleCategoryIds.includes(categoryId)) {
        throw new Error('Category and supplier combination not supported');
    }
    const oracleClient = migration.dataStore.getProvider('ORACLE');
    const _storeProc = 'BEGIN MSDSDBA.MB_GET_RETAILER(:id, :cursor); END;';

    const bindvars = {
        id: {
            dir: oracledb.BIND_IN,
            type: oracledb.NUMBER,
            val: supResourceId === null || supResourceId === undefined ? 0 : parseInt(supResourceId)
        },
        cursor: {
            dir: oracledb.BIND_OUT,
            type: oracledb.CURSOR
        }
    };

    try {
        const records = await oracleClient.execute(_storeProc, bindvars);

        // Format records
        const formattedRecords = records.map((supplier) => {
            let position = {
                lat: null,
                lon: null
            };
            if (supplier.latitude && supplier.longitude) {
                position = {
                    lat: supplier.latitude,
                    lon: supplier.longitude
                };
            } else {
                // Note: This part can be removed in next release, keeping it here for backward compatibility while testing in prelive
                const gridref = new OsGridRef(supplier.eastings, supplier.northings);
                position = OsGridRef.osGridToLatLon(gridref, LatLonEllipsoidal.datum.OSGB36);
            }

            let lines = [];
            lines.push(supplier.supplierAddrLine1);
            if (supplier.supplierAddrLine2) {
                lines.push(supplier.supplierAddrLine2);
            }
            if (supplier.supplierAddrLine3) {
                lines.push(supplier.supplierAddrLine3);
            }
            if (supplier.supplierAddrLine4) {
                lines.push(supplier.supplierAddrLine4);
            }
            lines.push(`${supplier.supOutwdPostCode} ${supplier.supInwdPostCode}`);

            return {
                id: supplier.supResourceId,
                name: supplier.supplierName,
                address: lines.join(', '),
                region: supplier.description,
                regionCode: supplier.paramCode.trim(),
                telephone: supplier.telephone,
                email: supplier.supEmail || '-',
                franchiseEmail: supplier.franchiseEmail || '-',
                latitude: position.lat,
                longitude: position.lon,
                accountNo: supplier.supplierAccountNo.trim(),
                supNetworkId: supplier.supNetworkId,
                supNetworkCode: supplier.supNetworkCode,
                supNetworkName: supplier.supNetworkName,
                comments: supplier.comments
            };
        });

        let mongoResult;
        let updatedDocuments;

        // If supNetworkCode is present, perform bulk insert
        if (supNetworkCode != '' && supResourceId === 0 && formattedRecords.length > 0) {
            // Filter records based on supNetworkCode
            const filteredRecords = formattedRecords.filter((supplier) => supplier.supNetworkCode === supNetworkCode);

            if (filteredRecords.length > 0) {
                // Map POI process set
                const processedRecords = filteredRecords.map((supplier) => poiEntry(supplier, categoryId));

                // Backup existing records
                const existingRecords = await this.poiCollection.find({ 'properties.categoryId': categoryId }, { projection: { _id: 0 } }).toArray();
                console.log(`Existing records count :: ${existingRecords.length}`);
                if (existingRecords.length > 0) {
                    await this.poiCollectionBackup.insertMany(existingRecords, { ordered: false });
                    await this.poiCollection.deleteMany({ 'properties.categoryId': categoryId });
                }
                // Insert new records
                const insertOps = processedRecords.map((record) => {
                    return {
                        insertOne: {
                            document: record
                        }
                    };
                });

                mongoResult = await this.poiCollection.bulkWrite(insertOps);
                return mongoResult;
            } else {
                logger.error('No records found in CSH for the given supNetworkCode');
                throw new Error('No records found in CSH for the given supNetworkCode');
            }
        } else if (supResourceId > 0 && formattedRecords.length > 0) {
            // Filter records based on supResourceId
            //  const filteredRecords = formattedRecords.filter((supplier) => supplier.id === supResourceId);

            // Process records using poiEntry function
            const processedRecords = formattedRecords.map((supplier) => poiEntry(supplier, categoryId));

            // Check if records contain categoryId and supResourceId for updates
            const updateOps = processedRecords
                .filter((record) => record.properties.categoryId && record.properties.supResourceId)
                .map((record) => {
                    return {
                        updateOne: {
                            filter: { 'properties.supResourceId': record.properties.supResourceId, 'properties.categoryId': record.properties.categoryId },
                            update: { $set: record },
                            upsert: true
                        }
                    };
                });

            // Perform updates
            mongoResult = await this.poiCollection.bulkWrite(updateOps);

            // Retrieve the updated documents
            updatedDocuments = await this.poiCollection
                .find({
                    'properties.supResourceId': supResourceId,
                    'properties.categoryId': categoryId
                })
                .toArray();

            return { mongoResult, updatedDocuments };
        } else {
            logger.error('No records found in CSH for the given supResourceId or supNetworkCode');
            throw new Error('No records found in CSH for the given supResourceId or supNetworkCode');
        }
    } catch (err) {
        throw err;
    }
};
module.exports = PoiRepository;
