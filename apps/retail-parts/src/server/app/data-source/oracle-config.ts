export interface OracleBaseConfig {
    //
    connectString: string;
    // DB credentials
    user: string;
    password: string;
    // maximum size of the pool
    poolMax?: number;
    // start with no connections
    poolMin?: number;
    // check aliveness of connection if idle in the pool for X seconds
    pingInterval?: number;
    // terminate connections that are idle in the pool for X seconds
    timeout?: number;
    // don't allow more than X unsatisfied getConnection() calls in the pool queue
    queueMax?: number;
    // terminate getConnection() calls queued for longer than X milliseconds
    queueTimeout?: number;
    // number of statements that are cached in the statement cache of each connection
    stmtCacheSize?: number;
    // record pool usage statistics that can be output with pool._logStats()
    stats?: boolean;
    // how many times we should loop via all pools to reconnect before we throw error
    retryRoundMax?: number;
    // connect increament defaults to 1
    poolIncrement?: number;
    appName?: string;
}
export interface OraclePoolConfig extends OracleBaseConfig {
    // pool alias to simply this will simplify interation by simply calling oracledb.getConnection(<poolAlias>)
    poolAlias: string;
}
