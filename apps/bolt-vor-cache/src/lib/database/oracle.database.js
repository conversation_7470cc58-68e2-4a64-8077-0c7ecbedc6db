const aaOracleUtility = require('@aa/oracle-utilities'),
    oracledb = require('oracledb');

oracledb.fetchAsString = [oracledb.CLOB];
const appName = 'bolt-vor-cache';

module.exports = {
    init: () => {
        return aaOracleUtility.init({
            connectStrings: process.env.cshConnectStrings ? process.env.cshConnectStrings.split(',') : ['sun54:1521/VACSH'],
            user: process.env.cshUser || 'Vanda',
            password: process.env.cshPassword || 'Va.77.av',
            appName
        });
    },
    connect: () => {
        return aaOracleUtility.connect();
    },
    disconnect: (db) => {
        aaOracleUtility.release(db);
    },
    jsonify: (metadata, data) => {
        return metadata.reduce((ac, item, i) => {
            ac[item.name] = data[i];
            return ac;
        }, {});
    }
};
