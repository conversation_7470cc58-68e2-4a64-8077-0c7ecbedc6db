import { IVRCanCancelResponse, IVRCancelResponse, IVRErrorResponse, IVRStatus } from '@aa/connector';
import { CreateReasonCode, OutcomeCode, TaskStatus } from '@aa/data-models/common';
import { BreakdownTask } from '@aa/data-models/entities/breakdown-task';
import { EventCode, Exception } from '@aa/exception';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment, Utils } from '@aa/utils';
import { Request, Response } from 'express';

export class App extends Microservice {
    /**
     * Standardises the phone number
     *
     * The number passed will always be in international format e.g. +44xxxxxxx.
     * For UK numbers the query to the task service will strip out +44 and replace it with 0xxxxxx
     * For non UK number the system will query task service with the number verbatim .
     *
     * @param {string} phoneNo
     * @returns {string}
     * @protected
     */
    protected static normalisePhoneNo(phoneNo: string): string {
        return phoneNo.replace('+44', '0');
    }

    /**
     * Validate phone number
     * @param {string} phoneNo
     * @return {boolean}
     * @protected
     */
    protected static isPhoneNoValid(phoneNo?: string): boolean {
        // Check if illegal characters provided
        return !!phoneNo && !!phoneNo.match(/^[+]?[0-9]+$/);
    }

    /**
     * Check if garage
     * @param {BreakdownTask} task
     * @return {boolean}
     * @protected
     */
    protected static isGarageTask(task: BreakdownTask): boolean {
        const { schedule, status } = task;

        // resource need to have name and status needs to be indicating garage task
        return !!(status && schedule?.resource.name && [TaskStatus.GARR, TaskStatus.GDET, TaskStatus.GHED].includes(status));
    }

    /**
     * Assess if task is cancellable by e.g. operator
     * @param {BreakdownTask} task
     * @returns {boolean}
     * @protected
     */
    protected static isCancellable(task: BreakdownTask): boolean {
        const activeStatuses = [TaskStatus.INIT, TaskStatus.PLAN, TaskStatus.PACK, TaskStatus.GDET, TaskStatus.CHCK, TaskStatus.HOLD, TaskStatus.HEAD, TaskStatus.GHED];

        return activeStatuses.includes(task.status as TaskStatus);
    }

    /**
     * Assess if task is self-cancellable
     * @param {BreakdownTask} task
     * @returns {boolean}
     * @protected
     */
    protected static isSelfCancellable(task: BreakdownTask): boolean {
        // task can be cancelled only for specific statuses
        // including HEAD & GHED (business req.)
        const allowedStatuses = [TaskStatus.PLAN, TaskStatus.PACK, TaskStatus.GDET, TaskStatus.CHCK, TaskStatus.HOLD, TaskStatus.HEAD, TaskStatus.GHED];
        if (!allowedStatuses.includes(task.status as TaskStatus)) {
            return false;
        }

        // allow cancel only for specific create reasons
        const allowedCreateReasons = [CreateReasonCode.INITIAL_TASK, CreateReasonCode.SELF_SERVICE_APP, CreateReasonCode.SELF_SERVICE_CAR, CreateReasonCode.RECOVERY];

        return allowedCreateReasons.includes(task.createReason?.id as CreateReasonCode);
    }

    public name = 'IVR';
    public application = BackendApplication.IVR;
    constructor(environment: BackendEnvironment) {
        super({ environment, appName: 'ivr' });

        this.server.get('/has-active-case', this.hasActiveCase);
        this.server.get('/cancel-task', this.cancelTask);
    }

    protected async getCancellableDigest(phoneNo: string): Promise<CancellableDigest> {
        try {
            const taskSearchResult = await this.connector.task.find.byPhoneNo(phoneNo);
            if (!taskSearchResult || (taskSearchResult && !taskSearchResult.length)) {
                // if no tasks at all
                return {
                    cancellable: false,
                    selfCancellable: false,
                    multipleActiveTasks: false
                };
            }
            const tasks: BreakdownTask[] = [];
            // for each get valid task
            for (const { taskId } of taskSearchResult) {
                const task = await this.connector.task.find.byId(taskId);
                if (task) {
                    tasks.push(task);
                }
            }

            const cancellableTasks = tasks.filter(App.isCancellable);
            const selfCancellableTask = cancellableTasks.find(App.isSelfCancellable);

            return {
                multipleActiveTasks: cancellableTasks.length > 1,
                cancellable: !!cancellableTasks.length,
                selfCancellable: !!selfCancellableTask,
                selfCancellableTask
            };
        } catch (error) {
            throw new Exception({
                error,
                code: EventCode.MOD_EXEC_FAIL,
                message: 'Failed while retrieving tasks'
            });
        }
    }

    /**
     * Get ETA
     * @param {CancellableDigest} digest
     * @returns {Promise<ETADigest>}
     * @protected
     */
    protected async getETADigest({ selfCancellableTask }: CancellableDigest): Promise<ETADigest> {
        if (!selfCancellableTask || !selfCancellableTask?.id) {
            return { eta: undefined, playable: false };
        }

        const { id } = selfCancellableTask;
        const { date: eta } = await this.connector.task.getStatusMessage(id, this.environment.systemOperatorId);

        if (!eta) {
            return { eta: undefined, playable: false };
        }

        // if deployed to a garage agent
        if (App.isGarageTask(selfCancellableTask)) {
            // allow to play ETA for garage jobs only if in specific window 6min to max 4h
            const earliest = Utils.generateFutureDate('6m');
            const latest = Utils.generateFutureDate('4h');
            if (eta < earliest || eta > latest) {
                return { eta, playable: false };
            }
        }

        return { eta, playable: true };
    }

    protected hasActiveCase = async (req: Request, res: Response) => {
        try {
            let phoneNo = req.query['phoneNo'] as string;

            if (!App.isPhoneNoValid(phoneNo)) {
                const exception = new Exception({
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Invalid req params'
                });
                const response: IVRErrorResponse = {
                    success: false,
                    status: IVRStatus.ERROR,
                    error: {
                        code: exception.report.code,
                        msg: exception.message
                    }
                };
                return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
            }

            phoneNo = App.normalisePhoneNo(phoneNo);

            // by default no active task found
            const response: IVRCanCancelResponse = {
                success: true,
                status: IVRStatus.NO_ACTIVE_TASK
            };

            // search for active task
            const digest = await this.getCancellableDigest(phoneNo);
            const { cancellable, selfCancellable, selfCancellableTask, multipleActiveTasks } = digest;

            if (multipleActiveTasks) {
                // if multiple active tasks we can't make decision automatically
                response.status = IVRStatus.REDIRECT_TO_AGENT;
            } else if (cancellable) {
                if (selfCancellable && selfCancellableTask) {
                    // if self-cancellable
                    response.status = IVRStatus.CAN_CANCEL;

                    const { eta, playable } = await this.getETADigest(digest);
                    response.eta = eta;
                    response.etaPlayback = playable;

                    response.vehicleReg = selfCancellableTask.vehicle?.registration;
                } else {
                    // if not self-cancellable or no task - lets direct IVR to direct call to agent
                    response.status = IVRStatus.REDIRECT_TO_AGENT;
                }
            }

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: IVRErrorResponse = {
                success: false,
                status: IVRStatus.ERROR,
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };

    protected cancelTask = async (req: Request, res: Response) => {
        try {
            let phoneNo = req.query['phoneNo'] as string;
            if (!App.isPhoneNoValid(phoneNo)) {
                const exception = new Exception({
                    code: EventCode.MOD_EXEC_FAIL,
                    message: 'Invalid req params'
                });
                const response: IVRErrorResponse = {
                    success: false,
                    status: IVRStatus.ERROR,
                    error: {
                        code: exception.report.code,
                        msg: exception.message
                    }
                };
                return getResponse(res, ServerResponseCode.BAD_REQUEST, response);
                return;
            }

            phoneNo = App.normalisePhoneNo(phoneNo);

            // by default no active task found
            const response: IVRCancelResponse = {
                status: IVRStatus.NO_ACTIVE_TASK,
                success: true
            };

            // search for active task
            const { cancellable, selfCancellable, selfCancellableTask, multipleActiveTasks } = await this.getCancellableDigest(phoneNo);

            if (multipleActiveTasks) {
                // if multiple active tasks we can't make decision automatically which one to cancel
                response.status = IVRStatus.REDIRECT_TO_AGENT;
            } else if (cancellable) {
                if (selfCancellable && selfCancellableTask) {
                    // if self-cancellable
                    selfCancellableTask.fault = selfCancellableTask.fault ? selfCancellableTask.fault : {};
                    selfCancellableTask.fault.outcome = {
                        completionCode: OutcomeCode.MEMBER_CANCELLED
                    };
                    selfCancellableTask.operatorId = this.environment.systemOperatorId;
                    response.status = IVRStatus.TASK_CANCELLED;
                    response.vehicleReg = selfCancellableTask.vehicle?.registration;

                    try {
                        await this.connector.task.cancel(selfCancellableTask);
                    } catch (error) {
                        this.logger.warn(
                            new Exception({
                                error,
                                message: 'Failure while cancelling task',
                                data: { selfCancellableTask, phoneNo }
                            })
                        );
                        // if error while cancelling - lets direct IVR to direct call to agent
                        response.status = IVRStatus.REDIRECT_TO_AGENT;
                    }
                } else {
                    // if not self-cancellable or no task - lets direct IVR to direct call to agent
                    response.status = IVRStatus.REDIRECT_TO_AGENT;
                }
            }

            return getResponse(res, ServerResponseCode.OK, response);
        } catch (error) {
            const exception = new Exception({ error, message: 'Req failure' });
            this.logger.error(exception);

            const response: IVRErrorResponse = {
                success: false,
                status: IVRStatus.ERROR,
                error: {
                    code: exception.report.code,
                    msg: exception.message
                }
            };

            return getResponse(res, ServerResponseCode.SERVER_ERROR, response);
        }
    };
}

interface CancellableDigest {
    multipleActiveTasks: boolean;
    cancellable: boolean;
    selfCancellable: boolean;
    selfCancellableTask?: BreakdownTask;
}

export interface ETADigest {
    eta?: Date;
    // should we play eta to the customer?
    playable: boolean;
}
