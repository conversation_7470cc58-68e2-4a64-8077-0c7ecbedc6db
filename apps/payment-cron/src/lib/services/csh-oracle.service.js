'use strict';

const Q = require('q');
const aaOracle = require('@aa/oracle-utilities');
const oracledb = require('oracledb');

const user = process.env.cshUser,
    password = process.env.cshPassword,
    connectStrings = process.env.cshConnectStrings.split(','),
    appName = 'payment-cron';

oracledb.fetchAsString = [oracledb.CLOB];
oracledb.poolIncrement = 1;

module.exports = {
    init: () => {
        return aaOracle.init({
            user,
            password,
            connectStrings,
            appName
        });
    },
    connect: () => {
        return aaOracle.connect();
    },
    release: (dbConn) => {
        return aaOracle.release(dbConn);
    },
    cursorRead: (dbConn, sql, bindvars) => {
        const defer = Q.defer();

        dbConn.execute(
            sql,
            bindvars,
            {
                outFormat: oracledb.OBJECT
            },
            function (err, result) {
                let resultStream = null,
                    results = [];

                if (err) {
                    aaOracle.release(dbConn);
                    defer.reject(err.message);
                    return;
                }
                resultStream = result.outBinds.cursor.toQueryStream();

                resultStream.on('error', function (error) {
                    aaOracle.release(dbConn);
                    defer.reject(error);
                });

                resultStream.on('data', function (data) {
                    results.push(data);
                });

                resultStream.on('end', () => {
                    resultStream.destroy();
                });

                resultStream.on('close', function () {
                    aaOracle.release(dbConn);
                    defer.resolve(results);
                });
            }
        );
        return defer.promise;
    },
    execute: (sql, bindvars) => {
        bindvars = bindvars ? bindvars : {};
        return aaOracle.connect().then((connection) => {
            const dbConn = connection;
            return connection.execute(sql, bindvars, { outFormat: oracledb.OBJECT }).then((data) => {
                if (!data) {
                    return [];
                }
                aaOracle.release(dbConn);
                return data.rows;
            });
        });
    },
    stats: () => {
        return aaOracle.stats();
    }
};
