import { applicationBasePaths } from '@aa/connector';
import { Notification, NotificationEventType } from '@aa/data-models/events/notification-event';
import { DataStoreProviderType } from '@aa/data-store';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubCheckpoint, EventHubReceiver, EventHubReceiverConfig } from '@aa/queue';
import { environment } from '@aa/system';
import { BackendEnvironment } from '@aa/utils';

class Backend extends Microservice {
    public name: string;
    public application: BackendApplication;
    public notificationStreamReceiver: EventHubReceiver<NotificationEventType, Notification, void>;

    constructor(appName: string, environment: BackendEnvironment, application: BackendApplication, dataStoreProviders = [DataStoreProviderType.MONGODB]) {
        super({
            environment,
            appName,
            dataStoreProviders
        });
        this.name = appName;
        this.application = application;

        //event hub receiver
        const checkpoint = new EventHubCheckpoint({
            accountUrl: environment.queue.storageAccountUrl || '',
            accountName: environment.queue.storageAccountName || '',
            accountKey: environment.queue.storageAccountKey || '',
            containerName: `notification-api-${environment.system.serverName}-${environment.system.systemUser}`,
            logger: this.logger
        });

        const ehBaseConfig: Omit<EventHubReceiverConfig, 'eventHubName' | 'consumerGroup' | 'checkpoint'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore,
            maxBatchSize: 1, // we want to process even 1
            maxDelayPerBatch: 2 // 2s
        };
        environment.system;
        this.notificationStreamReceiver = new EventHubReceiver({
            ...ehBaseConfig,
            checkpoint,
            eventHubName: 'notification-stream',
            // Lets make sure that multiple deployments can have unique consumer
            consumerGroup: `notification-api-${environment.system.serverName}-${environment.system.systemUser}`
        });
    }
}

environment.server.port = parseInt(process.env.notificationApiServicePort || process.env.PORT || '') || 7833;
environment.server.basePath = process.env.notificationApiServiceEndPoint || applicationBasePaths.NOTIFICATION_API;

export const backend = new Backend('notification-api', environment, BackendApplication.NOTIFICATION_API, [DataStoreProviderType.MONGODB, DataStoreProviderType.REDIS]);
