import { AuditClient } from '@aa/audit-client';
import { toTaskPreview } from '@aa/converters';
import { CommsRoute, EventType, Namespace, PrimeStateTransition, PrimeStateTransitionSchema, TaskStatus } from '@aa/data-models/common';
import { Task } from '@aa/data-models/entities/task';
import { isNewerTask, TaskPreview } from '@aa/data-models/entities/task-preview';
import { DataStoreProviderType } from '@aa/data-store';
import { Diff, DiffSnapshot } from '@aa/diff';
import { ServerResponseCode } from '@aa/http-client';
import { BackendApplication } from '@aa/identifiers';
import { Microservice } from '@aa/microservice';
import { EventHubSender, EventHubSenderConfig } from '@aa/queue';
import { getResponse } from '@aa/server-utils';
import { BackendEnvironment } from '@aa/utils';
import { Request, Response } from 'express';
import { WithId } from 'mongodb';

const appName = 'euops-event-ingest-api';

// Note: stateTransition required here, thats different to the consumer euops-task-stream that can accept this
// with state transition missing
type TaskChange = DiffSnapshot<TaskPreview, TaskPreview> & { stateTransition: PrimeStateTransition };

export class App extends Microservice {
    public name = appName;
    public application = BackendApplication.EUOPS_EVENT_INGEST_API;
    protected taskStreamSender: EventHubSender<EventType.TASK_CHANGE, TaskChange>;
    protected auditClient: AuditClient;

    constructor(environment: BackendEnvironment) {
        super({
            environment,
            appName,
            dataStoreProviders: [DataStoreProviderType.MONGODB, DataStoreProviderType.REDIS]
        });

        this.auditClient = new AuditClient({
            application: BackendApplication.EUROHELP_API,
            connector: this.connector,
            operatorId: -1
        });

        const evhSenderBase: Omit<EventHubSenderConfig, 'eventHubName'> = {
            securityKeyName: environment.queue.eventHubSecurityKeyName,
            securityKey: environment.queue.eventHubSecurityKey,
            namespace: environment.queue.eventHubNamespace,
            endpoint: environment.queue.eventHubEndpoint,
            logger: this.logger,
            context: this.context,
            system: this.system,
            dataStore: this.dataStore
        };

        this.taskStreamSender = new EventHubSender({
            ...evhSenderBase,
            eventHubName: 'task-stream'
        });

        //our main api that will be called by prime
        this.server.post('/event-ingest', this.eventIngest, { protect: false, schema: PrimeStateTransitionSchema });
    }

    protected eventIngest = async (req: Request<unknown, unknown, PrimeStateTransition>, res: Response<number>): Promise<void> => {
        try {
            const { id } = req.body;
            const taskId = Number.parseInt(id);
            this.logger.info({
                sourceName: this.name,
                message: 'Payload from prime-event ingest api',
                data: { body: req.body }
            });
            const provider = this.dataStore.getProvider(DataStoreProviderType.MONGODB);
            const routesCollection = await provider.collection<CommsRoute>('system-config', 'euopsCommsRoutes');
            const taskPreviewCollection = await provider.collection<TaskPreview>('entities', 'taskPreview');

            const trace = this.auditClient.getTrace(Namespace.EUOPS, taskId.toString());

            // retrieve task details from payload
            const task: Task = await this.connector.task.find.byId(taskId);

            if (!task) {
                this.logger.info({
                    sourceName: this.name,
                    message: `No task found for id ${taskId}`,
                    data: { body: req.body }
                });
                return getResponse(res, ServerResponseCode.BAD_REQUEST);
            }

            this.logger.info({
                sourceName: this.name,
                message: `Task found for id ${taskId}`,
                data: { body: req.body, task }
            });

            const custGroupCode = task.entitlement?.customerGroup.code;

            if (!custGroupCode) {
                this.logger.info({
                    sourceName: this.name,
                    message: `Cust group not found for task id ${taskId}`,
                    data: { body: req.body }
                });
                return getResponse(res, ServerResponseCode.BAD_REQUEST);
            }

            // get routing rule for customer group
            const commsRoute = await routesCollection.findOne({
                custGroup: custGroupCode
            });

            if (!commsRoute) {
                this.logger.info({
                    sourceName: this.name,
                    message: `Routing rule not found for ${custGroupCode}`,
                    data: { body: req.body }
                });
                return getResponse(res, ServerResponseCode.BAD_REQUEST);
            }

            this.logger.info({
                sourceName: this.name,
                message: `Routing rule found for ${custGroupCode}`,
                data: { body: req.body, commsRoute }
            });

            // get current task preview
            const taskPreview = await taskPreviewCollection.findOne({
                id: taskId
            });

            // We need to protect against race conditions. We will accept the change only if:
            // - no preview created before or
            // - task is completed or
            // - preview is newer than preview
            if (taskPreview && task.status !== TaskStatus.COMP && isNewerTask(taskPreview, task)) {
                this.logger.info({
                    sourceName: this.name,
                    message: `Processed task ${taskId} older than stored task preview, bailing out`,
                    data: { body: req.body, commsRoute, taskPreview, task }
                });
                // bailout
                return getResponse(res, ServerResponseCode.NO_CHANGE);
            }
            const { _id, ...previousTaskPreview } = taskPreview || ({} as WithId<TaskPreview>);

            const updatedTaskPreview = toTaskPreview(task);
            const diff = new Diff({
                current: updatedTaskPreview,
                previous: previousTaskPreview
            });
            const diffSnapshot = diff.diff();

            // if no change
            if (!diffSnapshot.isChange) {
                this.logger.info({
                    sourceName: this.name,
                    message: `No change in task data for task id ${taskId}, bailing out`,
                    data: { body: req.body, commsRoute, diffSnapshot }
                });
                // bailout
                return getResponse(res, ServerResponseCode.NO_CHANGE);
            }

            // if no change to status
            if (!diffSnapshot.change.status) {
                this.logger.info({
                    sourceName: this.name,
                    message: `No status change for task with id ${taskId}, bailing out`,
                    data: { body: req.body, commsRoute, diffSnapshot }
                });
                // bailout
                return getResponse(res, ServerResponseCode.NO_CHANGE);
            }

            // update or insert task preview
            if (taskPreview) {
                await taskPreviewCollection.updateOne(
                    {
                        id: taskId,
                        sequence: { $lt: updatedTaskPreview.sequence }
                    },
                    { $set: updatedTaskPreview }
                );
                this.logger.info({
                    sourceName: this.name,
                    message: `Task preview updated`,
                    data: { body: req.body, updatedTaskPreview, diffSnapshot }
                });
            } else {
                await taskPreviewCollection.insertOne(updatedTaskPreview);
                this.logger.info({
                    sourceName: this.name,
                    message: `Task preview created`,
                    data: { body: req.body, updatedTaskPreview, diffSnapshot }
                });
            }

            //to be picked up by euops-task-stream
            await this.taskStreamSender.send(
                EventType.TASK_CHANGE,
                {
                    ...diffSnapshot,
                    stateTransition: req.body
                },
                false
            );

            this.logger.info({
                sourceName: this.name,
                message: `Successfully sent task diff to task stream`,
                data: { body: req.body, diffSnapshot, taskPreview }
            });

            // we have a status change, we can proceed with propagation of event
            return getResponse(res, ServerResponseCode.OK);
        } catch (error) {
            this.logger.error({
                sourceName: this.name,
                message: 'Failed to process event',
                data: { error, body: req.body }
            });
            return getResponse(res, ServerResponseCode.SERVER_ERROR, error);
        }
    };
}
