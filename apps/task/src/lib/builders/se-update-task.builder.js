const { Address } = require('@aa/data-models/common');
var SeUpdateTask = require('@aa/malstrom-models/lib/se-update-task.model'),
    Contact = require('@aa/malstrom-models/lib/contact.model');

function buildSeUpdateTask(bridgeTask) {
    var seUpdateTask = new SeUpdateTask(bridgeTask),
        address = new Address(),
        contact = new Contact(),
        addressLines = [];

    addressLines.push(bridgeTask.custAddLine1);
    addressLines.push(bridgeTask.custAddLine2);

    if (bridgeTask.custAddLine3) {
        addressLines.push(bridgeTask.custAddLine3);
    }

    if (bridgeTask.custAddLine4) {
        addressLines.push(bridgeTask.custAddLine4);
    }

    address.addressLines(addressLines);
    address.postcode(bridgeTask.outwdPostCode + ' ' + bridgeTask.inwdPostCode);
    seUpdateTask.address(address);
    //surname coming in as a full name from bridge
    seUpdateTask.memberName(bridgeTask.surname);
    seUpdateTask.customerRequestId(bridgeTask.customerRequestId);

    if (bridgeTask.dayTelNo) {
        contact.telephone(bridgeTask.dayTelNo);
        contact.extension(bridgeTask.dayTelExtnNo);
    }
    if (bridgeTask.eveningTelNo) {
        contact.eveningTelephone(bridgeTask.eveningTelNo);
        contact.eveningExtension(bridgeTask.eveningTelExtnNo);
    }
    seUpdateTask.contact(contact);

    seUpdateTask.packageId(bridgeTask.packageId);
    seUpdateTask.amount(bridgeTask.payAmount);
    seUpdateTask.id(bridgeTask.id);

    return seUpdateTask;
}

module.exports = {
    build: buildSeUpdateTask
};
