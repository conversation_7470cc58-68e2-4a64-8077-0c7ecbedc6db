const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    CommendationTask = require('@aa/malstrom-models/lib/commendation-task.model'),
    Schedule = require('@aa/malstrom-models/lib/task-schedule.model'),
    Contact = require('@aa/malstrom-models/lib/contact.model');

function buildCommendationTask(bridgeTask) {
    var commendationTask = new CommendationTask(bridgeTask),
        address = new Address(),
        contact = new Contact(),
        schedule = new Schedule(),
        addressLines = [];

    // very old commendation tasks don't have an address !
    if (_.has(bridgeTask, 'address')) {
        _.forEach(bridgeTask.address.addressLines, function forEachAddressLine(addressLine) {
            addressLines.push(addressLine);
        });
    }

    address.addressLines(addressLines);
    commendationTask.address(address);

    schedule.complete(bridgeTask.statusTime);
    schedule.create(bridgeTask.statusTime);
    commendationTask.schedule(schedule);

    if (bridgeTask.contact) {
        if (bridgeTask.contact.daytime) {
            contact.telephone(bridgeTask.contact.daytime.telephone);
            contact.extension(bridgeTask.contact.daytime.extension);
        }
        if (bridgeTask.contact.evening) {
            contact.eveningTelephone(bridgeTask.contact.evening.telephone);
            contact.eveningExtension(bridgeTask.contact.evening.extension);
        }
        commendationTask.contact(contact);
    }

    return commendationTask;
}

function buildBridgeTask(commendationTask) {
    var bridgeTask = {};
    bridgeTask.custRequestId = commendationTask.customerRequestId();
    bridgeTask.remark = '';
    bridgeTask.taskStatusId = 1;
    bridgeTask.taskStatusTime = new Date();
    bridgeTask.taskStartTime = new Date();
    bridgeTask.updateTime = new Date();
    bridgeTask.taskId = 0;
    bridgeTask.busLocnId = 0;
    bridgeTask.operatorId = commendationTask.operatorId();
    bridgeTask.callerName = commendationTask.callerName();
    bridgeTask.cardName = '';
    bridgeTask.address = {
        addressLines: [],
        postcode: ''
    };
    _.forEach(commendationTask.address().addressLines(), function forEachAddressLine(addressLine) {
        bridgeTask.address.addressLines.push(addressLine);
    });
    bridgeTask.contact = {
        daytime: {
            telephone: commendationTask.contact().telephone(),
            extension: commendationTask.contact().extension()
        },
        evening: {
            telephone: commendationTask.contact().eveningTelephone(),
            extension: commendationTask.contact().eveningExtension()
        }
    };
    bridgeTask.commendComment = commendationTask.commendComment();
    return bridgeTask;
}

module.exports = {
    build: buildCommendationTask,
    buildBridgeTask: buildBridgeTask
};
