const { Address } = require('@aa/data-models/common');
var _ = require('lodash'),
    FlpTask = require('@aa/malstrom-models/lib/flp-task.model'),
    Schedule = require('@aa/malstrom-models/lib/task-schedule.model'),
    Contact = require('@aa/malstrom-models/lib/contact.model'),
    formatting = require('@aa/utilities/aa-utilities').formatting;

function buildFlpTask(bridgeTask, flpType) {
    var flpTask = new FlpTask(bridgeTask),
        address = new Address(),
        schedule = new Schedule(),
        contact = new Contact(),
        addressLines = [];

    flpTask.reasonText(formatting.toTitleCase(bridgeTask.reason));
    flpTask.actionTakenText(formatting.toTitleCase(bridgeTask.actionTaken));
    flpTask.payment(bridgeTask.payment);
    flpTask.partValue(bridgeTask.partValue);
    flpTask.taskTypeId1(bridgeTask.taskTypeId1);
    flpTask.operatorId(bridgeTask.staffNo);
    flpTask.flpTypeText(flpType.flpTypeName());
    flpTask.furtherActionText(bridgeTask.furtherAction);

    if (bridgeTask.contact) {
        if (bridgeTask.contact.daytime) {
            contact.telephone(bridgeTask.contact.daytime.telephone);
            contact.extension(bridgeTask.contact.daytime.extension);
        }
        if (bridgeTask.contact.evening) {
            contact.eveningTelephone(bridgeTask.contact.evening.telephone);
            contact.eveningExtension(bridgeTask.contact.evening.extension);
        }
        flpTask.contact(contact);
    }

    schedule.complete(bridgeTask.statusTime);
    schedule.create(bridgeTask.statusTime);
    flpTask.schedule(schedule);

    if (bridgeTask.address) {
        _.forEach(bridgeTask.address.addressLines, function forEachAddressLine(addressLine) {
            addressLines.push(addressLine);
        });
    }
    address.addressLines(addressLines);
    flpTask.address(address);

    return flpTask;
}

module.exports = {
    build: buildFlpTask
};
