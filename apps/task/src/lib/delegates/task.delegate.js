'use strict';

const { CustomerGroup, VehicleModel } = require('@aa/data-models/common');
var refRepo = require('@aa/ref-data'),
    TaskFactory = require('../factories/task.factory'),
    CreateReasonKey = require('../mappers/create-reason-key-map'),
    MalstromTaskBuilder = require('../builders/malstrom-task.builder'),
    RemarkFactory = require('../factories/remark.factory'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model');

/**
 * create task
 * @param {number} id              [description]
 * @param {CustomerRequest} customerRequest [description]
 * @param {Entitlement} entitlement     [description]
 * @param {ServiceType} serviceType     [description]
 * @return {Task}
 */
function createTask(id, customerRequest, entitlement, serviceTypeCode) {
    var createReason,
        customerGroup,
        priorityReason = null;

    createReason = refRepo.createReasons().findSync(CreateReasonKey.map(serviceTypeCode));
    createReason.serviceType(serviceTypeCode);

    if (entitlement.riskCode !== 'WJ') {
        customerGroup = refRepo.customerGroups().findSync(entitlement.policy().customerGroup().code());
    } else {
        customerGroup = entitlement.policy().customerGroup();
    }

    return TaskFactory.create(id, customerRequest, entitlement, createReason, customerGroup, RemarkFactory.create(entitlement), priorityReason);
}

/**
 * setup task to complete as referral tranfer
 * @param  {[type]} task [description]
 * @return {[type]}      [description]
 */
function referralTranfer(task) {
    task.status('COMP');

    if (!task.location().isSet()) {
        task.location().area('foo');
        task.location().text('foo');
    }
    task.sequence(0);
    task.fault().outcome().componentCode('   ');
    task.fault().outcome().completionCode('48');
    task.fault().outcome().completionFaultCode('  ');

    // need to ensure that we have some

    return task;
}

function _getRecoveryFault(task, vehicleFaults) {
    if (!task || !task.recovery || !task.recovery.relay) {
        return null;
    }

    var fault = vehicleFaults.findSync(function fault(fault) {
        return (fault.categoryCode() === 'FRE' || fault.categoryCode() === 'FRM') && fault.id() === task.fault.id;
    });

    return fault[0];
}

function parsePrimeTask(primeTask, vehicleFaults, customerGroups) {
    var recoveryFault = _getRecoveryFault(primeTask, vehicleFaults),
        customerGroup = customerGroups.findSync(primeTask.entitlement.custGroup.code.trim()),
        model = new VehicleModel({
            id: primeTask.vehicle.model.id
        });

    return MalstromTaskBuilder.build(primeTask, customerGroup, model, recoveryFault);
}

/**
 * create will join task
 * @param  {[type]} customerGroup   [description]
 * @param  {[type]} serviceTypeCode [description]
 * @return {[type]}                 [description]
 */
function createWillJoin(customerGroupCode, serviceTypeCode) {
    var createReason, customerGroup;

    // make a copy of customer group
    customerGroup = new CustomerGroup(refRepo.customerGroups().findSync(customerGroupCode).toJSON());
    createReason = new CreateReason(refRepo.createReasons().findSync(CreateReasonKey.map(serviceTypeCode)).toJSON());
    createReason.serviceType(serviceTypeCode);

    return TaskFactory.createWillJoin(createReason, customerGroup);
}

module.exports = {
    create: createTask,
    referralTranfer: referralTranfer,
    parse: parsePrimeTask,
    createWillJoin: createWillJoin
};
