'use strict';

var _ = require('lodash'),
    Q = require('q'),
    logger = require('winston'),
    CustomerRequestRepo = require('../repository/customer-requests.repository'),
    CustomerServiceHistoryDelegate = require('../delegates/customer-service-history-response.delegate'),
    TaskRepo = require('../repository/tasks.repository'),
    TasksByCustomerRequestRepo = require('../repository/tasks-by-customer-request.repository'),
    CustomerRequestSearchModel = require('@aa/malstrom-models/lib/advanced-customer-request-query.model');

function CustomerRequestByTaskService(bridge) {
    //TODO - we need to pass in the repos rather than instantiating in here as this becomes untestable
    var svc = this,
        crRepo = new CustomerRequestRepo(bridge),
        taskRepo = new TaskRepo(bridge),
        tasksByCustomerRequestRepo = new TasksByCustomerRequestRepo(bridge);

    _.extend(svc, {
        searchCshByTaskId: function searchCshByTaskId(taskId) {
            var deferred = Q.defer(),
                _task,
                cshResp,
                customerRequestId,
                customerSearchQueries;

            taskRepo
                .find(taskId)
                .then(function onTaskFindSuccess(task) {
                    logger.info('CustomerRequestByTaskId.searchCSHByTaskId :: found task details ::', taskId);
                    _task = task;
                    customerRequestId = task.customerRequestId();
                    // we got the task and its customer request id , so its time for finding all tasks associated with the customer request and CSH.
                    customerSearchQueries = new CustomerRequestSearchModel();
                    customerSearchQueries.customerRequestId(customerRequestId);

                    return Q.all([
                        crRepo.find(customerSearchQueries), // get cr details
                        tasksByCustomerRequestRepo.find(customerRequestId) //get all tasks for this cr
                    ]);
                })
                .then(
                    _.spread(function (crList, taskList) {
                        var cr = crList[0];
                        //assume 1 cr since we are searching by cr id
                        cshResp = CustomerServiceHistoryDelegate.doCshMerge(crList);
                        cr.addTasks(taskList);
                        if (!_task.isCompleted()) {
                            cshResp.taskId(_task.id()); // if active ..
                        }
                        deferred.resolve(cshResp);
                    })
                )
                .catch(function taskRepoFindError(err) {
                    logger.error('CustomerRequestByTaskId.searchCSHByTaskId :: task id ' + taskId + '::', err);

                    if (_task) {
                        // we have retrieved a task ..
                        deferred.resolve(CustomerServiceHistoryDelegate.createFromTask(_task, bridge.assistanceTypes(), bridge.taskStatus()));
                    } else {
                        deferred.reject(err); //
                    }
                });

            return deferred.promise;
        }
    });
}

module.exports = CustomerRequestByTaskService;
