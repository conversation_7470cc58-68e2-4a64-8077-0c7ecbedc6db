'use strict';

const { Vehicle } = require('@aa/data-models/common');
const _ = require('lodash'),
    Q = require('q'),
    logger = require('winston'),
    RTFReaderService = require('@aa/ref-data'),
    SplitTaskRepo = require('../repository/split-task.repository'),
    MobilityHelperService = require('./mobility-helper.service'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model'),
    SplitTaskResponseFactory = require('../factories/split-task-response.factory'),
    FaultCreateReasonDelegate = require('../delegates/fault-create-reason.delegate'),
    MiscFieldsFactory = require('../factories/misc-fields.factory'),
    TaskIndicatorsRepo = require('../repository/task-indicators.repository'),
    TaskIndicators = require('@aa/malstrom-models/lib/task-indicators.model'),
    RefId = require('@aa/malstrom-models/lib/ref-id.model');

module.exports = function SplitTaskService(bridge) {
    const svc = this,
        _repo = new SplitTaskRepo(bridge),
        _mobilityHelperService = new MobilityHelperService(bridge),
        taskIndicatorsRepo = new TaskIndicatorsRepo(bridge),
        _getAuthId = (createReason) => {
            var promise = createReason.isRelayPlus() ? _repo.getKeyId('relayPlus') : null;

            return Q.when(promise).then(function cshKeySuccess(response) {
                return response ? response.cshKey : null;
            });
        },
        _getIndicatorsByCreateReason = (createReason) => {
            return taskIndicatorsRepo
                .find(createReason.id())
                .then((indicators) => {
                    logger.info(`TaskService.getIndicatorsByCreateReason (id: ${createReason.id()}) :: indicators: ${JSON.stringify(indicators)}`);
                    return new TaskIndicators(indicators);
                })
                .catch((err) => {
                    logger.error('SplitTaskService.getIndicatorsByCreateReason :: ', err);
                    throw err;
                });
        },
        _getJobType = (createReason) => {
            switch (createReason.id()) {
                case CreateReason.RELAY_PLUS_HIRE_CAR:
                case CreateReason.RELAY_PLUS_HOTEL_PUB_TRANSPORT:
                    return 'N';
                case CreateReason.RELAY_PLUS_TAXI:
                    return 'T';
                case CreateReason.RELAY_PLUS_EXTENSION:
                    return 'X';
                default:
                    return 'Z'; //use a dummy value if create_reason not set (should never happen)
            }
        },
        _buildAuthCode = (task, authId) => {
            var opsCode = 'X',
                coverCode = task.miscFields().relayPlus().coverCode() || 'XX',
                paddedAuthId = _.padStart(authId, 7, '0'),
                jobType = _getJobType(task.createReason());

            return opsCode.concat(coverCode, paddedAuthId, jobType);
        },
        _cloneTask = (parentTask, createReason) => {
            return Q.all([
                _repo.find({
                    task: parentTask,
                    createReason: createReason
                }),
                _getIndicatorsByCreateReason(createReason)
            ]).then(
                _.spread(function (clonedTask, indicators) {
                    // make sure the cloned task has the same operator id as the source task ...
                    clonedTask.operatorId(parentTask.operatorId());
                    clonedTask.indicators().authorised(indicators.authorised());
                    let result = SplitTaskResponseFactory.clone(createReason, clonedTask, FaultCreateReasonDelegate.map(createReason, bridge, parentTask.fault()), parentTask);
                    return result;
                })
            );
        },
        _sendTask = (srcTask) => {
            return Q.all([_repo.parentTask(srcTask), _getAuthId(srcTask.createReason())])
                .then(
                    _.spread(function (parentTask, authId) {
                        // to ensure parentTask sequence no has un up to date value .. don't like that
                        srcTask.parentTask().sequence(parentTask.sequence);

                        if (authId) {
                            srcTask.miscFields().relayPlus().authId(authId);
                            srcTask.miscFields().relayPlus().authCode(_buildAuthCode(srcTask, authId));
                        }
                        return _repo.write(srcTask);
                    })
                )
                .then(function onWriteSuccess(splitResp) {
                    // set split to false so that subsequent saves don't overwrite capabilities
                    splitResp.task().split(false);
                    // make sure the cloned task has the same operator id as the source task ...
                    splitResp.task().operatorId(srcTask.operatorId());
                    // load vehicle details back into the new task that was returned from prime ..
                    // so we avoid another search for vehicle make & model
                    splitResp.task().vehicle(new Vehicle(srcTask.vehicle().toJSON()));
                    return Q.allSettled([splitResp, RTFReaderService.forceRTFToHtml(splitResp.response().primeMsg())]);
                })
                .then(
                    _.spread(function qAllSettledResponse(splitResp, rtfResponse) {
                        if (rtfResponse.state === 'fulfilled' && rtfResponse.value) {
                            splitResp.value.response().primeMsg(rtfResponse.value);
                        }
                        return splitResp.value;
                    })
                );
        };

    _.extend(svc, {
        /**
         * clone task
         * @param  {Task} parentTask   that we will clone
         * @param  {CreateReason} createReason
         * @return {Promise}      on success resolves to Task or MobilityTask if create reason is HIRE_TASK
         */
        cloneTask: function cloneTask(parentTask, createReason) {
            let clonePromise = null;
            // this is because some createReasons don't follow the normal clone-split processe
            // but keeps the UI consistent
            switch (createReason.id()) {
                case CreateReason.HIRE_CAR:
                    clonePromise = _mobilityHelperService.clone(parentTask, createReason);
                    break;
                default:
                    clonePromise = _cloneTask(parentTask, createReason);
                    break;
            }

            return clonePromise;
        },
        /**
         * send a split task
         * @param  {Task} task to send into prime
         * @return {Promise}         on success return TaskWriteResponse
         */
        sendSplitTask: function sendSplitTask(task) {
            let splitPromise = null;
            // this is because some createReasons don't follow the normal clone-split processe
            // but keeps UI consistent
            switch (task.createReason().id()) {
                case CreateReason.HIRE_CAR:
                    // do a normal write
                    splitPromise = _mobilityHelperService.write(task);
                    break;
                case CreateReason.HOTEL:
                case CreateReason.TRANSPORT:
                    const key = task.transport && task.transport().isTransportSet() ? 'transport' : 'hotel';
                    const relayPlusDetails = MiscFieldsFactory.createSbRelayPlus(task, key);
                    task.miscFields().relayPlus(relayPlusDetails);
                    splitPromise = _sendTask(task);
                    break;
                case CreateReason.GARAGE_REPAIR:
                    //task.fault().repairMinutes(42);
                    splitPromise = _sendTask(task);
                    break;
                default:
                    splitPromise = _sendTask(task);
                    break;
            }

            return splitPromise;
        }
    });
};
