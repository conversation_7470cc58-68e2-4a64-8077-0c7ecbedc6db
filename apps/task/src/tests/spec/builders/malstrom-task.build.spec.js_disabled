/*global describe,it, expect,beforeEach, spyOn, jasmine*/
'use strict';

const { CustomerGroup, VehicleModel } = require('@aa/data-models/common');

var _ = require('lodash'),
    MalstromTask = require('../../../lib/builders/malstrom-task.builder'),
    Location = require('@aa/malstrom-models/lib/location.model'),
    Fault = require('@aa/malstrom-models/lib/fault.model'),
    RefCode = require('@aa/malstrom-models/lib/ref-code.model'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model'),
    FormattingUtil = require('@aa/utilities/aa-utilities').formatting;
const HotelTask = require('@aa/malstrom-models/lib/hotel-task.model');
const TransportTask = require('@aa/malstrom-models/lib/transport-task.model');

describe('test Malstrom-Tasks factory', function () {
    var mockData,
        mockTaskWithMotorwayOptions,
        customerGroup,
        mockInitialFault,
        mockTask,
        mockRecoveryFault,
        vehicleModel,
        refDataRepo;

    beforeEach(function () {
        mockData = require('../mock-data/prime-data.mock');
        mockTaskWithMotorwayOptions = require('../mock-data/prime-task-with-motorway-info.mock');
    });

    beforeEach(function () {
        mockTask = mockData.task.task;

        customerGroup = new CustomerGroup({
            code: 'PERS',
            name: 'PERSONAL',
        });
        vehicleModel = new VehicleModel(mockData.vehicleModelMock);

        mockRecoveryFault = new Fault();

        refDataRepo = require('@aa/ref-data');
    });

    beforeEach(function () {
        spyOn(refDataRepo, 'completions').mockReturnValue({
            findSync: jest.spyOn().andCallFake(function () {
                return new RefCode({
                    code: 'ONE',
                    name: 'ONE NAME',
                });
            }),
        });

        spyOn(refDataRepo, 'createReasons').mockReturnValue({
            findSync: jest.spyOn().andCallFake(function () {
                return [
                    new CreateReason({
                        id: 0,
                        name: 'Breakdown',
                        serviceType: 'RSS',
                    }),
                ];
            }),
        });
    });

    beforeEach(function () {
        mockInitialFault = new Fault({
            id: 116,
            repairProbability: 1,
            repairMinutes: 40,
            name: 'DRIVE SHAFT',
            code: {
                code: 'DS',
                name: 'DRIVE SHAFT',
            },
            categoryCode: 'SUS',
            amFaultChk: false,
            motorwayChk: '',
            mwayRecovVehFaultId: 813,
            userPrompt: '',
            rtaChk: false,
        });

        const mockFaultAlt = new Fault({
            id: 115,
            repairProbability: 7,
            repairMinutes: 35,
            name: 'ALTERNATOR',

            code: {
                code: 'ALT',
                name: 'ALTERNATOR',
            },
            categoryCode: 'ELE',

            amFaultChk: false,
            motorwayChk: '',
            mwayRecovVehFaultId: 0,
            userPrompt: '',
            rtaChk: false,
        });

        spyOn(refDataRepo, 'vehicleFaults').mockReturnValue({
            findSyncAndClone: jest.spyOn().andCallFake(function (id) {
                return id && id == 115 ? mockFaultAlt : mockInitialFault;
            }),
        });
    });

    it('with all parameters', function () {
        var model;
        expect(mockTask.recovery.isTowing).toBe(false);

        model = MalstromTask.build(
            mockTask,
            customerGroup,
            vehicleModel,
            mockRecoveryFault
        );

        expect(model).not.toBeNull();

        expect(model.id()).toBe(mockTask.id);
        expect(model.customerRequestId()).toBe(mockTask.customerRequestId);

        expect(model.entitlement().variableData().length).toBeTruthy();
        expect(model.entitlement().textTopics().length).toBeTruthy();

        expect(model.recovery().adults()).toBe(mockTask.adults);
        expect(model.recovery().children()).toBe(mockTask.children);
        expect(model.recovery().dogs()).toBe(mockTask.dogs);

        expect(model.indicators().homeStart()).toBe(
            mockTask.indicators.homeStart
        );
        expect(model.indicators().hasCallInfo()).toBe(
            mockTask.indicators.hasCallInfo
        );
        expect(model.indicators().motorway()).toBe(
            mockTask.indicators.motorway
        );
        expect(model.indicators().needsAIProcess()).toBe(
            mockTask.indicators.needsAIProcess
        );
        expect(model.indicators().noFreeService()).toBe(
            mockTask.indicators.noFreeService
        );
        expect(model.indicators().dissatisfied()).toBe(
            mockTask.indicators.dissatisfied
        );
        expect(model.indicators().emailTracking()).toBe(
            mockTask.indicators.emailTracking
        );
        expect(model.indicators().relayPlusAbuse()).toBe(
            mockTask.indicators.relayPlusAbuse
        );
        expect(model.indicators().relayPlusSource()).toBe(
            mockTask.indicators.relayPlusSource
        );
        expect(model.indicators().safetyAdviceGiven()).toBe(
            mockTask.indicators.safetyAdviceGiven
        );
        expect(model.indicators().selfService()).toBe(
            mockTask.indicators.selfService
        );
        expect(model.indicators().smsTracking()).toBe(
            mockTask.indicators.smsTracking
        );
        expect(model.indicators().suppressIM()).toBe(
            mockTask.indicators.suppressIM
        );
        expect(model.indicators().useSlotWindow()).toBe(
            mockTask.indicators.useSlotWindow
        );
        expect(model.indicators().incidentManaged()).toBe(
            mockTask.indicators.incidentManaged
        );

        expect(model.miscFields().relayPlus()).not.toBeNull();
        expect(model.miscFields().relayPlus().occupants()).toBe(1);
        expect(model.miscFields().relayPlus().licenceOK()).toBeTruthy();
        expect(model.miscFields().relayPlus().crdCard()).toBeTruthy();
        expect(model.miscFields().relayPlus().deliveryAmount()).toBe('x0.00');
        expect(model.miscFields().seat()).not.toBeNull();
        expect(model.miscFields().vauxhall()).not.toBeNull();
        expect(model.miscFields().originatingPatrol()).not.toBeNull();
        expect(model.miscFields().pdqNo()).toBe(mockTask.miscFields.pdqNo);
        expect(model.miscFields().sif()).toBe(mockTask.miscFields.SIF);

        expect(model.miscFields().vbm()).toBe(mockTask.miscFields.vbm);

        expect(model.miscFields().garageRefNo()).toBe(
            mockTask.miscFields.garageRefNo
        );
        expect(model.miscFields().holdingResource()).toBe(
            mockTask.miscFields.holdingResource
        );
        expect(model.miscFields().cardType()).toBe(
            mockTask.miscFields.cardType
        );
        expect(model.miscFields().membershipType()).toBe(
            mockTask.miscFields.membershipType
        );
        expect(model.miscFields().entitlements()).toBe(
            mockTask.miscFields.entitlements
        );
        expect(model.miscFields().inceptionDate()).toBe(
            mockTask.miscFields.inceptionDate
        );

        expect(model.jobNoToday()).toBe(mockTask.jobNoToday);
        expect(model.recovery().unaccompaniedAfterLoading()).toBe(
            mockTask.recovery.unaccompaniedAfterLoading
        );

        // todo : this needs to become RefCode
        //expect(model.status()).toBe(mockTask.status);

        expect(model.supJobTypeCode()).toBe(mockTask.supJobTypeCode);
        expect(model.membImpressed()).toBe(mockTask.membImpressed);

        expect(model.location()).not.toBeNull();
        expect(model.location().area()).toBe(mockTask.location.area);
        expect(model.location().text()).toBe(mockTask.location.text);
        // todo rename coordinates to coordinate
        expect(model.location().coordinates()).not.toBeNull();
        expect(model.location().coordinates().latitude()).toBe(
            mockTask.location.coordinates.latitude
        );
        expect(model.location().coordinates().longitude()).toBe(
            mockTask.location.coordinates.longitude
        );

        expect(model.recovery().destination()).not.toBeNull();
        expect(model.recovery().destination().area()).toBe(
            mockTask.recovery.destination.area
        );
        expect(model.recovery().destination().text()).toBe(
            mockTask.recovery.destination.text
        );
        // todo rename coorinates to coordinate ..
        expect(model.recovery().destination().coordinates()).not.toBeNull();
        expect(model.recovery().destination().coordinates().latitude()).toBe(
            mockTask.recovery.destination.coordinates.latitude
        );
        expect(model.recovery().destination().coordinates().longitude()).toBe(
            mockTask.recovery.destination.coordinates.longitude
        );
        expect(model.recovery().isTowing()).toBe(true);

        expect(model.appointment()).not.toBeNull();
        expect(model.appointment().earliest().getTime()).toBe(
            new Date(mockTask.appointment.earliest).getTime()
        );
        expect(model.appointment().latest().getTime()).toBe(
            new Date(mockTask.appointment.latest).getTime()
        );

        expect(model.contact()).not.toBeNull();
        expect(model.contact().name()).toBe(
            FormattingUtil.toTitleCase(mockTask.contact.name)
        );
        expect(model.contact().telephone()).toBe(mockTask.contact.telephone);

        expect(model.createReason()).not.toBeNull();
        expect(model.createReason().id()).toBe(mockTask.createReason.id);
        // todo need to map create read id to name
        expect(model.createReason().name()).toBe(mockTask.createReason.name);

        expect(model.entitlement()).not.toBeNull();
        //todo no customer group ...
        //expect(model.entitlement().custGroup()).not.toBeNull();
        expect(model.entitlement().customerGroup().code()).toBe(
            mockTask.entitlement.custGroup.code
        );
        expect(model.entitlement().benefits().length).toBe(
            mockTask.entitlement.benefits.length
        );
        expect(model.entitlement().memberDetails().length).toBe(
            mockTask.entitlement.memberDetails.length
        );

        expect(model.schedule()).not.toBeNull();
        expect(model.schedule().arrive().getTime()).toBe(
            new Date(mockTask.schedule.arrive).getTime()
        );
        expect(model.schedule().arrive().getTime()).toBe(
            new Date(mockTask.schedule.arrive).getTime()
        );

        expect(model.schedule().resource()).not.toBeNull();
        expect(model.schedule().resource().id()).toBe(
            mockTask.schedule.resource.id
        );
        // todo ... why schedule has a telephone no?
        expect(model.schedule().resource().telephone()).toBe(
            mockTask.schedule.resource.telephone
        );

        expect(model.vehicle()).not.toBeNull();
        expect(model.vehicle().registration()).toBe(
            mockTask.vehicle.registration
        );
        expect(model.vehicle().colour()).toBe(mockTask.vehicle.colour);
        expect(model.vehicle().modelId()).toBe(mockTask.vehicle.model.id);

        expect(model.vehicle().modelId()).toBe(mockData.vehicleModelMock.id);
        expect(model.vehicle().typeId()).toBe(mockData.vehicleModelMock.typeId);

        expect(model.vehicle().experianDetails().dateFirstRegistered()).toBe(
            '20020202'
        );

        expect(model.vehicle().experianDetails().ukDateFirstRegistered()).toBe(
            '20130202'
        );

        expect(model.vehicle().experianDetails().transmission()).toBe(
            mockTask.vehicle.experianDetails.transmission
        );
        expect(model.vehicle().experianDetails().fuel()).toBe(
            mockTask.vehicle.experianDetails.fuel
        );
        expect(model.vehicle().experianDetails().yrOfManufacture()).toBe(
            mockTask.vehicle.experianDetails.yrOfManufacture
        );
        expect(model.vehicle().experianDetails().vin()).toBe(
            mockTask.vehicle.experianDetails.vin
        );
        expect(model.vehicle().experianDetails().engineSize()).toBe(
            mockTask.vehicle.experianDetails.engineSize
        );

        expect(model.vehicle().experianDetails().kerbWeight()).toBe(
            mockTask.vehicle.experianDetails.kerbWeight
        );

        expect(model.entitlement().memberDetails().length).toBe(
            mockTask.entitlement.memberDetails.length
        );
        expect(model.entitlement().benefits().length).toBe(
            mockTask.entitlement.benefits.length
        );

        expect(model.fault()).not.toBeNull();
        expect(model.fault().id()).toBe(mockTask.fault.id);
        expect(model.fault().initialFault().id()).toBe(
            mockTask.fault.initialId
        );
        expect(model.fault().capabilities().length).toBe(
            mockTask.fault.capabilities.length
        );
        expect(model.fault().name()).toBe(mockTask.fault.name);
        expect(model.fault().repairMinutes()).toBe(
            mockTask.fault.repairMinutes
        );
        expect(model.fault().repairProbability()).toBe(
            mockTask.fault.repairProbability
        );

        expect(model.fault().outcome().completionFault().name()).toBe(
            mockTask.completionFault
        );
        expect(model.fault().outcome().component().name()).toBe(
            mockTask.component
        );

        expect(model.priorities()).toEqual(mockTask.priorities);

        expect(model.sequence()).toBe(mockTask.sequence);

        expect(model.fault().diagnosticsQAList().length).toBe(2); // because each Q & A pair is mapped to a single diagnosticsQAList entry
        expect(model.recovery().fault().diagnosticsQAList().length).toBe(1); //
        expect(model.fault().code().code()).toEqual('ALT');
        expect(model.fault().code().name()).toEqual('ALTERNATOR');
    });

    it('with no vehicleModel', function () {
        var model;

        model = MalstromTask.build(mockTask, customerGroup, null, null);

        expect(model).not.toBeNull();

        expect(model.id()).toBe(mockTask.id);
        expect(model.customerRequestId()).toBe(mockTask.customerRequestId);

        expect(model.vehicle().makeId()).toBeFalsy();
        expect(model.vehicle().typeId()).toBeFalsy();
        expect(model.vehicle().modelId()).toBeFalsy();
    });

    it('with vehicleModel but no experianDetails', function () {
        var tmpMockTask = _.cloneDeep(mockTask),
            model;
        // mock task with vehicle that does not ahve any experiance data
        tmpMockTask.vehicle.experianDetails = null;

        model = MalstromTask.build(tmpMockTask, customerGroup, null, null);

        expect(model).not.toBeNull();

        expect(model.id()).toBe(mockTask.id);
        expect(model.customerRequestId()).toBe(mockTask.customerRequestId);

        expect(model.vehicle().modelId()).toBeFalsy();
        expect(model.vehicle().modelId()).toBeFalsy();
        expect(model.vehicle().typeId()).toBeFalsy();
    });

    it('with creatReason not having a name', function () {
        var tmpMockTask = _.cloneDeep(mockTask),
            model;
        // mock task with vehicle that does not ahve any experiance data
        tmpMockTask.createReason.name = null;

        model = MalstromTask.build(
            tmpMockTask,
            customerGroup,
            vehicleModel,
            null
        );

        expect(model).not.toBeNull();

        expect(model.id()).toBe(mockTask.id);
        expect(model.customerRequestId()).toBe(mockTask.customerRequestId);

        expect(model.createReason().name()).toBe('Breakdown');
        expect(model.createReason().serviceType()).toBe('RSS');
    });

    it('with no outcome', function () {
        var tmpMockTask = _.cloneDeep(mockTask),
            model;
        // mock task with vehicle that does not ahve any experiance data
        tmpMockTask.outcome = null;

        model = MalstromTask.build(
            tmpMockTask,
            customerGroup,
            vehicleModel,
            mockRecoveryFault
        );

        expect(model).not.toBeNull();

        expect(model.id()).toBe(mockTask.id);
        expect(model.customerRequestId()).toBe(mockTask.customerRequestId);

        expect(model.fault().outcome().completionFaultCode()).toBeNull();
        expect(model.fault().outcome().componentCode()).toBeNull();
        expect(model.fault().outcome().completionCode()).toBeNull();
    });

    it('should populate initial fault name by retrieving the initial fault', function () {
        var tmpMockTask = _.cloneDeep(mockTask),
            model;

        model = MalstromTask.build(
            tmpMockTask,
            customerGroup,
            vehicleModel,
            mockRecoveryFault
        );
        expect(
            refDataRepo.vehicleFaults().findSyncAndClone
        ).toHaveBeenCalledWith(tmpMockTask.fault.initialId);
        expect(model.fault().initialFault().id()).toBe(mockInitialFault.id());
        expect(model.fault().initialFault().name()).toBe(
            mockInitialFault.name()
        );
    });

    it('should populate fault code refData by retrieving the fault from ref-data', function () {
        var tmpMockTask = _.cloneDeep(mockTask),
            model;

        model = MalstromTask.build(
            tmpMockTask,
            customerGroup,
            vehicleModel,
            mockRecoveryFault
        );
        expect(
            refDataRepo.vehicleFaults().findSyncAndClone
        ).toHaveBeenCalledWith(tmpMockTask.fault.id);
        expect(model.fault().code().code()).toBe('ALT');
        expect(model.fault().code().name()).toBe('ALTERNATOR');
    });

    // TODO: disabled due inconsistent behaviour locally and in Jenkins
    // it('should build task extracting motorway options from location text', function () {
    // 	var mockTask = mockTaskWithMotorwayOptions.task,
    // 		model;
    //
    // 	mockTask.location.text = 'M4 JUNC 1 >>JUNC 1-->OXFORD>>WB>>ESLIP>>H/S<<';
    //
    // 	model = MalstromTask.build(mockTask, customerGroup, null, null);
    //
    // 	expect(model.location().lastJunctionPassed()).toBe('JUNC 1');
    // 	expect(model.location().travellingTo()).toBe('OXFORD');
    // 	expect(model.location().dot()).toBe(Location.DIRECTION_WEST);
    // 	expect(model.location().slipRoad()).toBe(Location.SLIP_ROAD_ENTRY);
    // 	expect(model.location().isHardShoulder()).toBe(true);
    // 	expect(model.location().text()).toBe('M4 JUNC 1');
    // });

    it('should set recovery adults count to null if bridge gives 99', () => {
        let tmpMockTask = _.cloneDeep(mockTask),
            model;

        tmpMockTask.recovery.adults = 99;
        model = MalstromTask.build(
            tmpMockTask,
            customerGroup,
            vehicleModel,
            mockRecoveryFault
        );
        expect(model.recovery().adults()).toEqual(null);
    });

    it('should build HOTEL task from bridgeResponse', () => {
        var tmpMockTask = _.cloneDeep(mockTask),
            model;
        // mock task with vehicle that does not ahve any experiance data
        tmpMockTask.createReason.id = CreateReason.HOTEL;
        tmpMockTask.hotel = {
            hotelName: 'Test Hotel',
        };

        model = MalstromTask.build(tmpMockTask, customerGroup, null, null);

        expect(model).not.toBeNull();

        expect(model.id()).toBe(mockTask.id);
        expect(model.customerRequestId()).toBe(mockTask.customerRequestId);

        expect(model instanceof HotelTask).toBe(true);
        expect(model.hotel().hotelName()).toEqual('Test Hotel');
    });

    it('should build TRANSPORT task from bridgeResponse', () => {
        var tmpMockTask = _.cloneDeep(mockTask),
            model;
        // mock task with vehicle that does not ahve any experiance data
        tmpMockTask.createReason.id = CreateReason.TRANSPORT;
        tmpMockTask.transport = {
            reservationId: 'AA123',
        };

        model = MalstromTask.build(tmpMockTask, customerGroup, null, null);

        expect(model).not.toBeNull();

        expect(model.id()).toBe(mockTask.id);
        expect(model.customerRequestId()).toBe(mockTask.customerRequestId);

        expect(model instanceof TransportTask).toBe(true);
        expect(model.transport().reservationId()).toEqual('AA123');
    });
});
