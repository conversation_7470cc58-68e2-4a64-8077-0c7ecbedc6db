/*global describe,it, expect,beforeEach, spyOn, jasmine*/
'use strict';

const { CustomerGroup, VehicleModel } = require('@aa/data-models/common');
var MalstromTaskBuilder = require('../../../lib/builders/malstrom-task.builder'),
    Location = require('@aa/malstrom-models/lib/location.model'),
    Task = require('@aa/malstrom-models/lib/task.model'),
    Fault = require('@aa/malstrom-models/lib/fault.model'),
    RefCode = require('@aa/malstrom-models/lib/ref-code.model'),
    PrimeTaskBuilder = require('../../../lib/builders/prime-task.builder'),
    BuzbyTelfix = require('@aa/malstrom-models/lib/buzby-telfix.model');

describe('test prime task factory', function () {
    var mockData,
        customerGroup,
        vehicleModel,
        model,
        mockTask,
        mockFault,
        refDataRepo,
        motorwayTask;

    beforeEach(function () {
        mockData = require('../mock-data/prime-data.mock');
    });

    beforeEach(function () {
        motorwayTask = new Task();
        motorwayTask.indicators().motorway(true);
        motorwayTask.location().text('M4 JUNC 1');
        motorwayTask.location().lastJunctionPassed('JUNC 1');
        motorwayTask.location().travellingTo('OXFORD');
        motorwayTask.location().dot(Location.DIRECTION_WEST);
    });

    beforeEach(function () {
        mockTask = mockData.task.task;
        customerGroup = new CustomerGroup({
            code: 'PERS',
            name: 'PERSONAL',
        });

        vehicleModel = new VehicleModel(mockData.vehicleModelMock);
        refDataRepo = require('@aa/ref-data');
    });

    beforeEach(function () {
        spyOn(refDataRepo, 'completions').mockReturnValue({
            findSync: jest.spyOn().andCallFake(function () {
                return new RefCode({
                    code: 'ONE',
                    name: 'ONE NAME',
                });
            }),
        });
    });

    beforeEach(function () {
        mockFault = new Fault();
        spyOn(refDataRepo, 'vehicleFaults').mockReturnValue({
            findSyncAndClone: jest.spyOn().andCallFake(function () {
                return mockFault;
            }),
        });
    });

    beforeEach(function () {
        model = MalstromTaskBuilder.build(
            mockData.task.task,
            customerGroup,
            vehicleModel,
            null
        );
    });

    it('test writeBuilder', function () {
        var bridgeTask;

        expect(model).not.toBeNull();

        bridgeTask = PrimeTaskBuilder.build(model);

        expect(bridgeTask).not.toBeNull();
        expect(bridgeTask.id).toBe(model.id());
        expect(bridgeTask.createReason.id).toBe(mockTask.createReason.id);
        expect(bridgeTask.customerRequestId).toBe(mockTask.customerRequestId);

        /*		expect(bridgeTask.specialInstructions).toContain('601Xr1C');
				expect(bridgeTask.specialInstructions).toContain('626Xr1C');
		*/
        expect(bridgeTask.indicators.dissatisfied).toBe(
            mockTask.indicators.dissatisfied
        );
        expect(bridgeTask.indicators.needsAIProcess).toBe(
            mockTask.indicators.needsAIProcess
        );
        expect(bridgeTask.indicators.noFreeService).toBe(
            mockTask.indicators.noFreeService
        );
        expect(bridgeTask.indicators.changeOfNote).toBe(
            mockTask.indicators.changeOfNote
        );
        expect(bridgeTask.indicators.dissatisfied).toBe(
            mockTask.indicators.dissatisfied
        );
        expect(bridgeTask.indicators.emailTracking).toBe(
            mockTask.indicators.emailTracking
        );
        expect(bridgeTask.indicators.hasCallInfo).toBe(
            mockTask.indicators.hasCallInfo
        );
        expect(bridgeTask.indicators.homeStart).toBe(
            mockTask.indicators.homeStart
        );
        expect(bridgeTask.indicators.membImpressed).toBe(
            mockTask.indicators.membImpressed
        );
        expect(bridgeTask.indicators.motorway).toBe(
            mockTask.indicators.motorway
        );
        expect(bridgeTask.indicators.relayPlusAbuse).toBe(
            mockTask.indicators.relayPlusAbuse
        );
        expect(bridgeTask.indicators.relayPlusSource).toBe(
            mockTask.indicators.relayPlusSource
        );
        expect(bridgeTask.indicators.selfService).toBe(
            mockTask.indicators.selfService
        );
        expect(bridgeTask.indicators.smsTracking).toBe(
            mockTask.indicators.smsTracking
        );
        expect(bridgeTask.indicators.suppressIM).toBe(
            mockTask.indicators.suppressIM
        );
        expect(bridgeTask.indicators.useSlotWindow).toBe(
            mockTask.indicators.useSlotWindow
        );
        expect(bridgeTask.indicators.incidentManaged).toBe(
            mockTask.indicators.incidentManaged
        );

        expect(bridgeTask.recovery.dogs).toBe(mockTask.recovery.dogs);

        expect(bridgeTask.recovery.adults).toBe(mockTask.recovery.adults);

        expect(bridgeTask.children).toBe(mockTask.recovery.children);

        expect(bridgeTask.jobNoToday).toBe(mockTask.jobNoToday);

        expect(bridgeTask.packageName).toBe(mockTask.packageName);
        expect(bridgeTask.partsStatus).toBe(mockTask.partsStatus);
        expect(bridgeTask.primaryLiveryPreferred).toBe(
            mockTask.primaryLiveryPreferred
        );
        expect(bridgeTask.priorities).toEqual(mockTask.priorities);

        expect(bridgeTask.safetyAdviceGiven).toBe(mockTask.safetyAdviceGiven);

        expect(bridgeTask.status).toBe(mockTask.status);
        expect(bridgeTask.supJobTypeCode).toBe(mockTask.supJobTypeCode);
        expect(bridgeTask.recovery.unaccompaniedAfterLoading).toBe(
            mockTask.recovery.unaccompaniedAfterLoading
        );

        expect(bridgeTask.appointment.earliest).toBe(
            mockTask.appointment.earliest
        );
        expect(bridgeTask.appointment.latest).toBe(mockTask.appointment.latest);
        expect(bridgeTask.contact.name).toBe(mockTask.contact.name);
        expect(bridgeTask.contact.telephone).toBe(mockTask.contact.telephone);

        expect(bridgeTask.completionFault).toBe(mockTask.completionFault);
        expect(bridgeTask.component).toBe(mockTask.component);

        // test location
        expect(bridgeTask.location.coordinates.latitude).toBe(
            mockTask.location.coordinates.latitude
        );
        expect(bridgeTask.location.coordinates.longitude).toBe(
            mockTask.location.coordinates.longitude
        );
        expect(bridgeTask.location.area).toBe(mockTask.location.area);
        expect(bridgeTask.location.text).toBe(mockTask.location.text);
        expect(bridgeTask.location.remarks).toBe(mockTask.location.remarks);
        // test destination aka recovery location
        expect(bridgeTask.recovery.destination.coordinates.latitude).toBe(
            mockTask.recovery.destination.coordinates.latitude
        );
        expect(bridgeTask.recovery.destination.coordinates.longitude).toBe(
            mockTask.recovery.destination.coordinates.longitude
        );
        expect(bridgeTask.recovery.destination.area).toBe(
            mockTask.recovery.destination.area
        );
        expect(bridgeTask.recovery.destination.text).toBe(
            mockTask.recovery.destination.text
        );
        expect(bridgeTask.recovery.destination.remarks).toBe(
            mockTask.recovery.destination.remarks
        );

        // test vehicle
        expect(bridgeTask.vehicle.colour).toBe(mockTask.vehicle.colour);
        expect(bridgeTask.vehicle.model.id).toBe(mockTask.vehicle.model.id);
        expect(bridgeTask.vehicle.registration).toBe(
            mockTask.vehicle.registration
        );

        expect(bridgeTask.vehicle.experianDetails.dateFirstRegistered).toBe(
            mockTask.vehicle.experianDetails.dateFirstRegistered
        );
        expect(bridgeTask.vehicle.experianDetails.engineSize).toBe(
            mockTask.vehicle.experianDetails.engineSize
        );
        expect(bridgeTask.vehicle.experianDetails.fuel).toBe(
            mockTask.vehicle.experianDetails.fuel
        );
        expect(bridgeTask.vehicle.experianDetails.transmission).toBe(
            mockTask.vehicle.experianDetails.transmission
        );
        expect(bridgeTask.vehicle.experianDetails.vin).toBe(
            mockTask.vehicle.experianDetails.vin
        );
        expect(bridgeTask.vehicle.experianDetails.kerbWeight).toBe(
            mockTask.vehicle.experianDetails.kerbWeight
        );

        // test outcome ...
        expect(bridgeTask.completionFault).toBe(mockTask.completionFault);
        expect(bridgeTask.componentCode).toBe(mockTask.componentCode);

        expect(bridgeTask.outcome.completionCode).toBe(
            mockTask.outcome.completionCode
        );
        expect(bridgeTask.outcome.completionFaultCode).toBe(
            mockTask.outcome.completionFaultCode
        );
        expect(bridgeTask.outcome.componentCode).toBe(
            mockTask.outcome.componentCode
        );

        expect(bridgeTask.fault.id).toBe(mockTask.fault.id);
        expect(bridgeTask.fault.capabilities.length).toBe(
            mockTask.fault.capabilities.length
        );
        expect(bridgeTask.fault.name).toBe(mockTask.fault.name);
        expect(bridgeTask.fault.repairMinutes).toBe(
            mockTask.fault.repairMinutes
        );
        expect(bridgeTask.fault.repairProbability).toBe(
            mockTask.fault.repairProbability
        );
        expect(bridgeTask.fault.categoryCode).toBe(mockTask.fault.categoryCode);

        expect(bridgeTask.sequence).toBe(mockTask.sequence);

        expect(bridgeTask.fault.diagnosticsQA.length).toBeTruthy(); // because each Q & A pair is mapped to a single diagnosticsQAList entry

        //Test Telfix details for Buzby Calls
        expect(bridgeTask.telFix).toBeUndefined();
    });

    it('should add telFix details in the bridgeTask if buzbyCall details recieved', function () {
        var bridgeTask;
        model.buzbyTelfix(
            new BuzbyTelfix({
                buzbyIntent: 'Test INTENT YELLOW LIGHT',
                perripheralCallKey: '12345',
                routerCallKey: '67890',
                outerCallKeyDay: 'TEST',
            })
        );

        bridgeTask = PrimeTaskBuilder.build(model);

        expect(bridgeTask.buzbyTelfix).toBeUndefined();
        expect(bridgeTask.telFix).not.toBeUndefined();
        expect(bridgeTask.telFix.perripheralCallKey).toBe(
            model.buzbyTelfix().perripheralCallKey()
        );
    });

    it('should not add telFix details in the bridgeTask if buzbyTelfix key is not present in the srcTask', function () {
        var bridgeTask;
        delete model.buzbyTelfix;
        bridgeTask = PrimeTaskBuilder.build(model);

        expect(bridgeTask.telFix).toBeUndefined();
    });

    // TODO: disabled due inconsistent behaviour locally and in Jenkins
    // it('should create prime task with motorway info appended to location text', function () {
    // 	var actual;
    // 	motorwayTask.location().slipRoad(Location.SLIP_ROAD_ENTRY);
    // 	motorwayTask.location().isHardShoulder(true);
    //
    // 	actual = PrimeTaskBuilder.build(motorwayTask);
    //
    // 	expect(actual.location.text).toBe('M4 JUNC 1 >>JUNC 1-->OXFORD>>WB>>ESLIP>>H/S<<');
    // });

    it('should create prime task having active lane for Lane', function () {
        var actual;
        motorwayTask.location().lane(Location.LANE_TYPE_HARD_SHOULDER);
        motorwayTask.location().isActiveLane(true);

        actual = PrimeTaskBuilder.build(motorwayTask);

        expect(actual.location.text).toBe(
            'M4 JUNC 1 >>JUNC 1-->OXFORD>>WB>>H/S>>ACTIVE<<'
        );
    });

    // TODO: disabled due inconsistent behaviour locally and in Jenkins
    // it('should create prime task having active lane for slipRoad', function () {
    // 	var actual;
    // 	motorwayTask.location().lane(Location.SLIP_ROAD_ENTRY);
    // 	motorwayTask.location().isActiveLane(true);
    //
    // 	actual = PrimeTaskBuilder.build(motorwayTask);
    //
    // 	expect(actual.location.text).toBe('M4 JUNC 1 >>JUNC 1-->OXFORD>>WB>>ESLIP>>ACTIVE<<');
    // });
});
