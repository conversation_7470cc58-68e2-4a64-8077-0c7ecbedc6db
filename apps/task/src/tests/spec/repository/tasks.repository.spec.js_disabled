/*global describe,it, expect,beforeEach, spyOn, jasmine*/
'use strict';

const { Benefit } = require('@aa/data-models/aux/entitlement');
const { CustomerGroup, VehicleModel } = require('@aa/data-models/common');
var Q = require('q'),
    Task = require('@aa/malstrom-models/lib/task.model'),
    Fault = require('@aa/malstrom-models/lib/fault.model'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model'),
    TasksRepo = require('../../../lib/repository/rss-task.repository'),
    MalstromTask = require('../../../lib/builders/malstrom-task.builder'),
    PrimeTask = require('../../../lib/builders/prime-task.builder');

describe('test Tasks repository', function () {
    var tasks, refDataRepo, mockData, mockBridge;

    beforeEach(function () {
        refDataRepo = require('@aa/ref-data');
        mockData = require('../mock-data/prime-data.mock');
    });

    beforeEach(function () {
        spyOn(refDataRepo, 'benefits').mockReturnValue({
            findSync: jest.spyOn().andCallFake(function () {
                return new Benefit();
            }),
        });

        spyOn(refDataRepo, 'vehicleModels').mockReturnValue({
            find: jest.spyOn().andCallFake(function () {
                var deferred = Q.defer();

                deferred.resolve([new VehicleModel(mockData.vehicleModelMock)]);
                return deferred.promise;
            }),
        });

        spyOn(refDataRepo, 'customerGroups').mockReturnValue({
            findSync: jest.spyOn().andCallFake(function () {
                return new CustomerGroup({
                    code: 'PERS',
                    name: 'PERSONAL',
                });
            }),
        });

        spyOn(refDataRepo, 'createReasons').mockReturnValue({
            findSync: jest.spyOn().andCallFake(function () {
                return new CreateReason({
                    id: 0,
                });
            }),
        });

        spyOn(refDataRepo, 'vehicleFaults').mockReturnValue({
            findSyncAndClone: jest.spyOn().andCallFake(function () {
                return new Fault({
                    id: 0,
                });
            }),
        });

        mockBridge = {
            get: jest.spyOn().andCallFake(function (data, onSuccess) {
                onSuccess(mockData.task);
            }),
        };

        tasks = new TasksRepo(mockBridge);

        spyOn(MalstromTask, 'build').mockReturnValue(new Task());

        spyOn(PrimeTask, 'build').mockReturnValue({});
    });

    it('test writeBuilder', function () {
        var primeQuery = tasks.writeBuilder(new Task());
        expect(primeQuery).not.toBeNull();
        expect(primeQuery.task).not.toBeNull();
    });

    it('test processFindResults', function (done) {
        tasks.processFindResponse(mockData.task).then(function (model) {
            expect(model).not.toBeNull();
            done();
        });
    });
});
