const { CustomerGroup, SeSystem } = require('@aa/data-models/common');
var RefCode = require('@aa/malstrom-models/lib/ref-code.model'),
    ServiceType = require('@aa/malstrom-models/lib/service-type.model'),
    CreateReason = require('@aa/malstrom-models/lib/create-reason.model'),
    AssistanceType = require('@aa/malstrom-models/lib/assistance-type.model'),
    PriorityReason = require('@aa/malstrom-models/lib/priority-reason.model');

module.exports = {
    createReasons: {
        INITIAL_TASK: new CreateReason({
            id: 0,
            name: 'INITIAL_TASK'
        })
    },
    customerGroups: {
        PERS: new CustomerGroup({
            id: 9,
            code: 'PERS',
            name: 'PERSONA<PERSON>',
            seSystemId: 13,
            warrantyPrompt: false,
            allowForceRecy: false,
            captureMileage: true,
            displayInd: 1,
            hirerNetworkId: 161,
            supNetworkId: 1,
            secondLevelSrchAllowed: false
        }),
        LAND: new CustomerGroup({
            id: 413,
            code: 'LAND',
            name: 'LAND ROVER ASSISTANCE',
            seSystemId: 20,
            warrantyPrompt: false,
            allowForceRecy: true,
            captureMileage: true,
            displayInd: 1,
            hirerNetworkId: 161,
            supNetworkId: 1,
            secondLevelSrchAllowed: false
        }),
        FTE: new CustomerGroup({
            id: 1201,
            code: 'FTE',
            name: 'NORTHGATE',
            seSystemId: 17,
            warrantyPrompt: true,
            allowForceRecy: true,
            captureMileage: true,
            displayInd: true,
            hirerNetworkId: 170,
            supNetworkId: 1,
            secondLevelSrchAllowed: true,
            firstLevelValidationLabel: 'MEMBER NUMBER'
        }),
        HALI: new CustomerGroup({
            id: 1100,
            code: 'HALI',
            name: 'HALIFAX',
            seSystemId: 13,
            resSystemId: 13,
            warrantyPrompt: false,
            allowForceRecy: false,
            captureMileage: true,
            displayInd: true,
            hirerNetworkId: 161,
            supNetworkId: 1,
            secondLevelSrchAllowed: false,
            firstLevelValidationLabel: null,
            recoveryCheckRequired: false
        }),
        QML: new CustomerGroup({
            id: 160,
            code: 'QML',
            name: 'AA BUSINESS SERVICES PFU',
            seSystemId: 17,
            resSystemId: 17,
            warrantyPrompt: true,
            allowForceRecy: true,
            captureMileage: true,
            displayInd: true,
            hirerNetworkId: 170,
            supNetworkId: 1,
            secondLevelSrchAllowed: true,
            firstLevelValidationLabel: 'MEMBER NUMBER',
            recoveryCheckRequired: false
        }),
        FSC: new CustomerGroup({
            id: 157,
            code: 'FSC',
            name: 'FLEET INSURED',
            seSystemId: 16,
            resSystemId: 16,
            warrantyPrompt: false,
            allowForceRecy: true,
            captureMileage: true,
            displayInd: true,
            hirerNetworkId: 170,
            supNetworkId: 1,
            secondLevelSrchAllowed: true,
            firstLevelValidationLabel: null,
            recoveryCheckRequired: false
        }),
        VWW: new CustomerGroup({
            id: 1358,
            code: 'VWW',
            name: 'VW WARRANTY',
            seSystemId: 20,
            resSystemId: 20,
            warrantyPrompt: false,
            allowForceRecy: true,
            captureMileage: true,
            displayInd: true,
            driverDetsReqInd: false,
            hirerNetworkId: 309,
            supNetworkId: 1,
            secondLevelSrchAllowed: false,
            firstLevelValidationLabel: null,
            recoveryCheckRequired: false,
            msgHandling: false,
            B2BGroupId: 0
        })
    },
    seSystems: {
        13: new SeSystem({
            id: 13,
            code: 'TIA',
            name: 'TIA'
        }),
        20: new SeSystem({
            id: 20,
            code: 'BCM',
            name: 'BCAS_MANU'
        }),
        21: new SeSystem({
            id: 21,
            code: 'BCMX',
            name: 'BCAS_MANU_EXT'
        }),
        17: new SeSystem({
            id: 17,
            code: '',
            name: ''
        })
    },
    serviceTypes: {
        2: new ServiceType({
            id: 2,
            code: 'RSS',
            name: 'BREAKDOWN'
        })
    },
    priorityReasons: {
        32: new PriorityReason({
            id: 32,
            name: 'VIP'
        })
    },
    assistanceTypes: {
        9: new AssistanceType({
            id: 9,
            customerGroup: {
                id: 9,
                code: 'PERS',
                name: 'PERSONAL',
                seSystemId: 13,
                warrantyPrompt: false,
                allowForceRecy: false,
                captureMileage: false,
                displayInd: 0,
                hirerNetworkId: -1,
                supNetworkId: -1,
                secondLevelSrchAllowed: false
            },
            serviceType: {
                code: 'RSS',
                name: 'BREAKDOWN',
                id: 2
            }
        }),
        544: new AssistanceType({
            id: 544,
            customerGroup: {
                id: 413,
                seSystemId: 20,
                code: 'LAND',
                name: 'LAND ROVER ASSISTANCE',
                warrantyPrompt: false,
                allowForceRecy: false,
                captureMileage: false,
                displayInd: 0,
                hirerNetworkId: -1,
                supNetworkId: -1,
                secondLevelSrchAllowed: false
            },
            serviceType: {
                id: 2,
                code: 'BKDN',
                name: 'BREAKDOWN'
            }
        }),
        260: new AssistanceType({
            id: 260,
            customerGroup: {
                id: 160,
                code: 'QML',
                name: 'AA BUSINESS SERVICES PFU',
                seSystemId: 17,
                resSystemId: 17,
                warrantyPrompt: true,
                allowForceRecy: true,
                captureMileage: true,
                displayInd: true,
                hirerNetworkId: 170,
                supNetworkId: 1,
                secondLevelSrchAllowed: true,
                firstLevelValidationLabel: 'MEMBER NUMBER',
                recoveryCheckRequired: false
            },
            serviceType: {
                id: 2,
                code: 'RSS',
                name: 'ROAD ASSISTANCE'
            }
        }),
        543: new AssistanceType({
            id: 543,
            customerGroup: {
                id: 414,
                code: 'JAG',
                name: 'JAGUAR ASSISTANCE',
                seSystemId: 20,
                resSystemId: 20,
                warrantyPrompt: false,
                allowForceRecy: true,
                captureMileage: true,
                displayInd: true,
                hirerNetworkId: 161,
                supNetworkId: 1,
                secondLevelSrchAllowed: false,
                firstLevelValidationLabel: null,
                recoveryCheckRequired: false
            },
            serviceType: {
                id: 2,
                code: 'RSS',
                name: 'ROAD ASSISTANCE'
            }
        }),
        640: new AssistanceType({
            id: 640,
            customerGroup: {
                id: 1100,
                code: 'HALI',
                name: 'HALIFAX',
                seSystemId: 13,
                resSystemId: 13,
                warrantyPrompt: false,
                allowForceRecy: false,
                captureMileage: true,
                displayInd: true,
                hirerNetworkId: 161,
                supNetworkId: 1,
                secondLevelSrchAllowed: false,
                firstLevelValidationLabel: null,
                recoveryCheckRequired: false
            },
            serviceType: {
                id: 2,
                code: 'RSS',
                name: 'ROAD ASSISTANCE'
            }
        }),
        227: new AssistanceType({
            id: 227,
            customerGroup: {
                id: 157,
                code: 'FSC',
                name: 'FLEET INSURED',
                seSystemId: 16,
                resSystemId: 16,
                warrantyPrompt: false,
                allowForceRecy: true,
                captureMileage: true,
                displayInd: true,
                hirerNetworkId: 170,
                supNetworkId: 1,
                secondLevelSrchAllowed: true,
                firstLevelValidationLabel: null,
                recoveryCheckRequired: false
            },
            serviceType: {
                id: 2,
                code: 'RSS',
                name: 'ROAD ASSISTANCE'
            }
        }),
        695: new AssistanceType({
            id: 695,
            customerGroup: {
                id: 1201,
                code: 'FTE',
                name: 'NORTHGATE',
                seSystemId: 17,
                warrantyPrompt: true,
                allowForceRecy: true,
                captureMileage: true,
                displayInd: true,
                hirerNetworkId: 170,
                supNetworkId: 1,
                secondLevelSrchAllowed: true,
                firstLevelValidationLabel: 'MEMBER NUMBER'
            },
            serviceType: {
                id: 2,
                code: 'RSS',
                name: 'ROAD ASSISTANCE'
            }
        })
    },
    taskStatus: {
        PLAN: new RefCode({
            code: 'PLAN',
            name: 'PLAN'
        }),
        UNAC: new RefCode({
            code: 'UNAC',
            name: 'UNACKNOWLEDGED'
        })
    }
};
