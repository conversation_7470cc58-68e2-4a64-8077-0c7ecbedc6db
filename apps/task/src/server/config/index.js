'use strict';

var path = require('path');

// Export the config object based on the NODE_ENV
// ==============================================
module.exports = {
    env: process.env.NODE_ENV || 'development',

    // Root path of server
    root: path.normalize(__dirname + '/../..'),

    // set service parameters
    apiEndPoint: process.env.aahPodEndpoint || process.env.taskServiceApiEndPoint || '/api/task-service',
    port: process.env.aahPodHttpPort || process.env.taskServicePort || 7822,
    ip: '0.0.0.0',
    serviceEntitlementEndPoint: process.env.serviceEntitlementEndPoint || 'http://localhost:7106/api/service-entitlement'
};
