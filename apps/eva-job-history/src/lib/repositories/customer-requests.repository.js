'use strict';

let _ = require('lodash'),
    AdvancedCustomerRequestQeury = require('@aa/malstrom-models/lib/advanced-customer-request-query.model'),
    CustomerRequestHistoryResponse = require('@aa/mobility-models-common/lib/customer-request-history-response.model'),
    logger = require('winston'),
    restfulRequest = require('@aa/endpoints').restfulRequest,
    queryLimitDuration = parseInt(process.env.TASKS_LIMIT_DURATION, 10) || 180 * 24 * 60 * 60 * 1000, // in milliseconds
    last6Months = 6 * 30 * 24 * 3600 * 1000,
    twoMinute = 2 * 60 * 1000; // in milliseconds
function CustomerRequestsRepository(config) {
    let repo = this,
        _config = config;

    logger.info('CustomerRequestsRepository init', _config);
    _.extend(repo, {
        find: function (taskId, token) {
            return restfulRequest({
                method: 'POST',
                rejectUnauthorized: false,
                baseUrl: _config.endPointUrl,
                uri: '/customerRequests/search',
                body: JSON.stringify({
                    search: '',
                    param: {
                        taskId: taskId
                    }
                }),
                timeout: _config.timeout,
                headers: {
                    'content-type': 'application/json',
                    'security-token': token
                }
            }).then(function (resp) {
                return new CustomerRequestHistoryResponse(resp.data);
            });
        },
        serviceHistory: function (seLocatorId, endSearch, token) {
            let query = new AdvancedCustomerRequestQeury();

            query.seLocatorId(seLocatorId);

            // limit to last 5 years
            query.startTime(new Date(Date.now() - queryLimitDuration));
            query.endTime(new Date(endSearch.getTime() - twoMinute)); // this will exclude current active CR

            return restfulRequest({
                method: 'POST',
                rejectUnauthorized: false,
                baseUrl: _config.endPointUrl,
                uri: '/customerRequests/search',
                body: JSON.stringify({
                    param: query.toJSON()
                }),
                timeout: _config.timeout,
                headers: {
                    'content-type': 'application/json',
                    'security-token': token
                }
            }).then(function (resp) {
                return new CustomerRequestHistoryResponse(resp.data);
            });
        },
        serviceHistoryForLastSixMonths: function (reg, endSearch, token) {
            return restfulRequest({
                method: 'POST',
                rejectUnauthorized: false,
                baseUrl: _config.endPointUrl,
                uri: '/customerRequest/searchByRegistration',
                body: JSON.stringify({
                    searchParams: { vehicleReg: reg, startTime: new Date(Date.now() - last5Years), endTime: new Date(endSearch.getTime() - twoMinute) }
                }),
                timeout: _config.timeout,
                headers: {
                    'content-type': 'application/json',
                    'security-token': token
                }
            }).then(function (resp) {
                return new CustomerRequestHistoryResponse(resp.data);
            });
        }
    });
}

module.exports = CustomerRequestsRepository;
