'use strict';

let _ = require('lodash'),
    logger = require('winston'),
    Q = require('q');

function PatrolDetailsRepository(cshDb) {
    let repo = this,
        _monthsLimit = parseInt(process.env.PATROL_DETAILS_DURATION, 10) || 60,
        _select = 'SELECT bt.task_id,' + 'ps.person_forename, ' + 'ps.person_surname, ' + 'rt.resource_tel_no, ' + 'rt.tel_type_id ',
        _seSearch =
            _select +
            ' FROM CUSTOMER_REQUEST cr ' +
            ' JOIN task t ON t.cust_request_id = cr.cust_request_id ' +
            ' JOIN MSDSDBA.breakdown_task bt ON bt.task_id  =t.task_id ' +
            ' JOIN PATROL p ON p.patrol_resource_id=bt.res_doing_task_id ' +
            ' JOIN person ps ON ps.person_id = p.person_id ' +
            ' LEFT OUTER JOIN resource_tel rt ON rt.resource_id = bt.res_doing_task_id ' +
            ` WHERE cr.se_locator_id = :id and cr.creation_time > add_months( trunc(sysdate), -${_monthsLimit})`,
        _crSearch =
            _select +
            ' FROM Task t  ' +
            ' JOIN CUSTOMER_REQUEST cr ON t.cust_request_id = cr.cust_request_id ' +
            ' JOIN MSDSDBA.breakdown_task bt ON bt.task_id  =t.task_id ' +
            ' JOIN PATROL p ON p.patrol_resource_id=bt.res_doing_task_id ' +
            ' JOIN person ps ON ps.person_id = p.person_id ' +
            ' LEFT OUTER JOIN resource_tel rt ON rt.resource_id = bt.res_doing_task_id ' +
            ` WHERE t.cust_request_id = :id and cr.creation_time > add_months( trunc(sysdate), -${_monthsLimit})`,
        _cshDb = cshDb,
        _search = function _search(sqlText, id) {
            let patrolDetails = [],
                defered = Q.defer();

            _cshDb.connect().then(function (dbCon) {
                dbCon.execute(sqlText, [id], function (err, results) {
                    if (err) {
                        _cshDb.release(dbCon).then(() => {
                            defered.reject([]);
                        });
                    } else {
                        _.forEach(results.rows, function (row) {
                            patrolDetails.push({
                                taskId: row[0],
                                forename: row[1],
                                surname: row[2],
                                telephoneNo: row[3],
                                telephoneType: row[4]
                            });
                        });
                        _cshDb.release(dbCon).then(() => {
                            logger.info(`Patrol repository :: patrol details length : ${patrolDetails.length}`);
                            defered.resolve(patrolDetails);
                        });
                    }
                });
            });

            return defered.promise;
        };

    _.extend(repo, {
        seLocator: function seLocator(seLocatorId) {
            logger.info(`patrol-details.seLocator :: seLocatorId ${seLocatorId}`);
            return _search(_seSearch, seLocatorId);
        },
        custRequest: function custRequest(crId) {
            logger.info(`patrol-details.seLocator :: custReqId ${crId}`);
            return _search(_crSearch, crId);
        }
    });
}

module.exports = PatrolDetailsRepository;
