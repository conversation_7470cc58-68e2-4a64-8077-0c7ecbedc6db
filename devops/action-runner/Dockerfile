FROM ghcr.io/actions/actions-runner:2.325.0 as base
USER root

# Install git
RUN apt update -y && \
    apt install -y --no-install-recommends git build-essential curl openssh-client

ENV PATH="$HOME/opt/git/bin:$PATH"

# Install Oracle Client Libraries tools
RUN apt-get update && \
    apt-get install -y libpq-dev zlib1g-dev build-essential shared-mime-info libaio1 libaio-dev unzip wget --no-install-recommends

ENV PATH="$HOME/opt/oracle/instantclient_21_4:$PATH"

USER runner

# Install Oracle Client Libraries
RUN wget https://download.oracle.com/otn_software/linux/instantclient/214000/instantclient-sdk-linux.x64-********.0dbru.zip && \
    wget https://download.oracle.com/otn_software/linux/instantclient/214000/instantclient-sqlplus-linux.x64-********.0dbru.zip && \
    wget https://download.oracle.com/otn_software/linux/instantclient/214000/instantclient-basic-linux.x64-********.0dbru.zip && \
    mkdir -p $HOME/opt/oracle && \
    cp instantclient-* $HOME/opt/oracle/ && \
    cd $HOME/opt/oracle/ && \
    unzip instantclient-basic-linux.x64-********.0dbru.zip && \
    unzip instantclient-sdk-linux.x64-********.0dbru.zip && \
    unzip instantclient-sqlplus-linux.x64-********.0dbru.zip && \
    rm -rf instantclient-basic-linux.x64-********.0dbru.zip instantclient-sdk-linux.x64-********.0dbru.zip instantclient-sqlplus-linux.x64-********.0dbru.zip

RUN echo "LD_LIBRARY_PATH=$HOME/opt/oracle/instantclient_21_4" >> .env
RUN echo "OCI_HOME=$HOME/opt/oracle/instantclient_21_4" >> .env
RUN echo "OCI_LIB_DIR=$HOME/opt/oracle/instantclient_21_4" >> .env
RUN echo "ORACLE_HOME=$HOME/opt/oracle/instantclient_21_4" >> .env

USER root
# Install Azure CLI
RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash

USER runner

WORKDIR /home/<USER>

# expose PATH to runners as per
# https://github.com/actions/actions-runner-controller/issues/2273
RUN echo PATH=$PATH >> .env

# Create cache for nodejs as per
# https://docs.github.com/en/enterprise-server@3.2/admin/github-actions/managing-access-to-actions-from-githubcom/setting-up-the-tool-cache-on-self-hosted-runners-without-internet-access
RUN mkdir -p _tools/node

# Persist the work dir path
RUN echo "AGENT_TOOLSDIRECTORY=/home/<USER>/_tools" >> .env
RUN echo "RUNNER_TOOL_CACHE=/home/<USER>/_tools" >> .env
RUN echo "LOCAL_CACHE=/home/<USER>/cache" >> .env

WORKDIR /home/<USER>/_tools/node

# Download Node 14.21.3 to cache it
RUN mkdir -p 14.21.3/x64
RUN curl -f -L -o node-14.21.3.tar.gz https://github.com/actions/node-versions/releases/download/14.21.3-4202774076/node-14.21.3-linux-x64.tar.gz \
    && tar xzf ./node-14.21.3.tar.gz -C ./14.21.3/x64 \
    && rm node-14.21.3.tar.gz
RUN touch 14.21.3/x64.complete

# Download Node 20.11.1 to cache it
RUN mkdir -p 20.11.1/x64
RUN curl -f -L -o node-20.11.1.tar.gz https://github.com/actions/node-versions/releases/download/20.11.1-7910924545/node-20.11.1-linux-x64.tar.gz \
    && tar xzf ./node-20.11.1.tar.gz -C ./20.11.1/x64 \
    && rm node-20.11.1.tar.gz
RUN touch 20.11.1/x64.complete

WORKDIR /home/<USER>
