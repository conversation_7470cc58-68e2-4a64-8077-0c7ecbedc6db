{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "isolatedModules": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "noImplicitReturns": true, "allowJs": true, "strictBindCallApply": true, "esModuleInterop": true, "target": "es2015", "module": "esnext", "lib": ["es2021", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@aa/action-client": ["libs/action-client/src/index.ts"], "@aa/admin-helpers": ["libs/admin-helpers/src/index.ts"], "@aa/audit-client": ["libs/audit-client/src/index.ts"], "@aa/auth-client": ["libs/auth-client/src/index.ts"], "@aa/auth-middleware": ["libs/auth-middleware/src/index.ts"], "@aa/azure-email-client": ["libs/azure-email-client/src/index.ts"], "@aa/azure-graph-client": ["libs/azure-graph-client/src/index.ts"], "@aa/azure-queue": ["libs/azure-queue/src/index.ts"], "@aa/azure-storage": ["libs/azure-storage/src/index.ts"], "@aa/backend-auth-client": ["libs/backend-auth-client/src/index.ts"], "@aa/backend-migration": ["libs/backend-migration/src/index.ts"], "@aa/backup-client": ["libs/backup-client/src/index.ts"], "@aa/blob-container-client": ["libs/blob-container-client/src/index.ts"], "@aa/blob-storage": ["libs/blob-storage/src/index.ts"], "@aa/bolt-formly-templates": ["libs/bolt-formly-templates/src/index.js"], "@aa/bolt-icons": ["libs/bolt-icons/src/index.ts"], "@aa/bridge": ["libs/bridge/src/index.ts"], "@aa/cache": ["libs/cache/src/index.ts"], "@aa/certificates": ["libs/certificates/src/index.ts"], "@aa/closed-hires": ["libs/closed-hires/src/index.ts"], "@aa/config-client": ["libs/config-client/src/index.ts"], "@aa/configs/bolt-config": ["libs/configs/bolt-config/src/index.ts"], "@aa/configs/common-config": ["libs/configs/common-config/src/index.ts"], "@aa/configs/config-def": ["libs/configs/config-def/src/index.ts"], "@aa/configs/euops-config": ["libs/configs/euops-config/src/index.ts"], "@aa/configs/public-config": ["libs/configs/public-config/src/index.ts"], "@aa/connector": ["libs/connector/src/index.ts"], "@aa/context": ["libs/context/src/index.ts"], "@aa/converters": ["libs/converters/src/index.ts"], "@aa/data-models/aux/case": ["libs/data-models/aux/case/src/index.ts"], "@aa/data-models/aux/customer-request": ["libs/data-models/aux/customer-request/src/index.ts"], "@aa/data-models/aux/entitlement": ["libs/data-models/aux/entitlement/src/index.ts"], "@aa/data-models/aux/file": ["libs/data-models/aux/file/src/index.ts"], "@aa/data-models/common": ["libs/data-models/common/src/index.ts"], "@aa/data-models/entities/ancillary-task": ["libs/data-models/entities/ancillary-task/src/index.ts"], "@aa/data-models/entities/breakdown-task": ["libs/data-models/entities/breakdown-task/src/index.ts"], "@aa/data-models/entities/car-hire-task": ["libs/data-models/entities/car-hire-task/src/index.ts"], "@aa/data-models/entities/content-template": ["libs/data-models/entities/content-template/src/index.ts"], "@aa/data-models/entities/financial-task": ["libs/data-models/entities/financial-task/src/index.ts"], "@aa/data-models/entities/flp-task": ["libs/data-models/entities/flp-task/src/index.ts"], "@aa/data-models/entities/garage-repair-task": ["libs/data-models/entities/garage-repair-task/src/index.ts"], "@aa/data-models/entities/hotel-task": ["libs/data-models/entities/hotel-task/src/index.ts"], "@aa/data-models/entities/recovery-task": ["libs/data-models/entities/recovery-task/src/index.ts"], "@aa/data-models/entities/storage-task": ["libs/data-models/entities/storage-task/src/index.ts"], "@aa/data-models/entities/task": ["libs/data-models/entities/task/src/index.ts"], "@aa/data-models/entities/task-preview": ["libs/data-models/entities/task-preview/src/index.ts"], "@aa/data-models/entities/transport-task": ["libs/data-models/entities/transport-task/src/index.ts"], "@aa/data-models/events/notification-event": ["libs/data-models/events/notification-event/src/index.ts"], "@aa/data-models/events/stream-event": ["libs/data-models/events/stream-event/src/index.ts"], "@aa/data-models/system/source": ["libs/data-models/system/source/src/index.ts"], "@aa/data-query": ["libs/data-query/src/index.ts"], "@aa/data-store": ["libs/data-store/src/index.ts"], "@aa/data-store-utils": ["libs/data-store-utils/src/index.ts"], "@aa/data-stores/job-history": ["libs/data-stores/job-history/src/index.ts"], "@aa/diff": ["libs/diff/src/index.ts"], "@aa/document-client": ["libs/document-client/src/index.ts"], "@aa/email-sender": ["libs/email-sender/src/index.ts"], "@aa/email-subscription-manager": ["libs/email-subscription-manager/src/index.ts"], "@aa/email-template-client": ["libs/email-template-client/src/index.ts"], "@aa/endpoints": ["libs/endpoints/src/index.ts"], "@aa/entitlement-benefit-client": ["libs/entitlement-benefit-client/src/index.ts"], "@aa/entitlement-client": ["libs/entitlement-client/src/index.ts"], "@aa/eurohelp-client": ["libs/eurohelp-client/src/index.ts"], "@aa/event-emitter": ["libs/event-emitter/src/index.ts"], "@aa/event-source": ["libs/event-source/src/index.ts"], "@aa/events/cloud-event": ["libs/events/cloud-event/src/index.ts"], "@aa/events/worker-events": ["libs/events/worker-events/src/index.ts"], "@aa/exception": ["libs/exception/src/index.ts"], "@aa/frontend-auth-client": ["libs/frontend-auth-client/src/index.ts"], "@aa/frontend-client": ["libs/frontend-client/src/index.ts"], "@aa/frontend-migration": ["libs/frontend-migration/src/index.ts"], "@aa/http-client": ["libs/http-client/src/index.ts"], "@aa/http-stream-client": ["libs/http-stream-client/src/index.ts"], "@aa/identifiers": ["libs/identifiers/src/index.ts"], "@aa/instrumentation": ["libs/instrumentation/src/index.ts"], "@aa/interval": ["libs/interval/src/index.ts"], "@aa/legacy-client": ["libs/legacy-client/src/index.ts"], "@aa/logger": ["libs/logger/src/index.ts"], "@aa/malstrom-models/*": ["libs/malstrom-models/src/*"], "@aa/message-client": ["libs/message-client/src/index.ts"], "@aa/microservice": ["libs/microservice/src/index.ts"], "@aa/mobility-models-common/*": ["libs/mobility-models-common/src/*"], "@aa/mobility-models/*": ["libs/mobility-models/src/*"], "@aa/mongo-models": ["libs/mongo-models/src/index.ts"], "@aa/mongodb": ["libs/data-store/src/lib/providers/mongodb-data-provider"], "@aa/note-client": ["libs/note-client/src/index.ts"], "@aa/notification-client": ["libs/notification-client/src/index.ts"], "@aa/open-api": ["libs/open-api/src/index.ts"], "@aa/oracle-utilities": ["libs/oracle-utilities/src/index.ts"], "@aa/overlays/*": ["libs/overlays/src/*"], "@aa/pdf-renderer": ["libs/pdf-renderer/src/index.ts"], "@aa/prime-client": ["libs/prime-client/src/index.ts"], "@aa/provider": ["libs/provider/src/index.ts"], "@aa/queue": ["libs/queue/src/index.ts"], "@aa/rabo-key-interceptor": ["libs/rabo-key-interceptor/src/index.ts"], "@aa/redis-connection": ["libs/redis-connection/src/index.ts"], "@aa/redis-list-stream": ["libs/redis-list-stream/src/index.ts"], "@aa/redis-lock": ["libs/redis-lock/src/index.ts"], "@aa/ref-data": ["libs/ref-data/src/index.ts"], "@aa/remote-cache": ["libs/remote-cache/src/index.ts"], "@aa/remote-cron": ["libs/remote-cron/src/index.ts"], "@aa/remote-election": ["libs/remote-election/src/index.ts"], "@aa/remote-queue": ["libs/remote-queue/src/index.ts"], "@aa/schema": ["libs/schema/src/index.ts"], "@aa/security-checker": ["libs/security-checker/src/index.ts"], "@aa/security-utils": ["libs/security-utils/src/index.ts"], "@aa/server": ["libs/server/src/index.ts"], "@aa/server-stream": ["libs/server-stream/src/index.ts"], "@aa/server-utils": ["libs/server-utils/src/index.ts"], "@aa/supplier-client": ["libs/supplier-client/src/index.ts"], "@aa/system": ["libs/system/src/index.ts"], "@aa/task-client": ["libs/task-client/src/index.ts"], "@aa/team-client": ["libs/team-client/src/index.ts"], "@aa/template-client": ["libs/template-client/src/index.ts"], "@aa/test-utils": ["libs/test-utils/src/index.ts"], "@aa/tracking-link-tools": ["libs/tracking-link-tools/src/index.ts"], "@aa/transaction-history-client": ["libs/transaction-history-client/src/index.ts"], "@aa/trip-clients": ["libs/trip-client/src/index.ts"], "@aa/uac-client": ["libs/uac-client/src/index.ts"], "@aa/ui/*": ["libs/ui/src/*"], "@aa/user-client": ["libs/user-client/src/index.ts"], "@aa/utilities/*": ["libs/utilities/src/*"], "@aa/utils": ["libs/utils/src/index.ts"], "@aa/vehicle-details-client": ["libs/vehicle-details-client/src/index.ts"], "@aa/workers/comms-worker": ["libs/workers/comms-worker/src/lib/comms-worker.ts"], "@aa/workers/comms-worker-client": ["libs/workers/comms-worker-client/src/index.ts"], "@aa/workers/notification-worker": ["libs/workers/notification-worker/src/lib/notification-worker.ts"], "@aa/workers/notification-worker-client": ["libs/workers/notification-worker-client/src/index.ts"], "aa-backend-migration": ["libs/backend-migration/src/index.ts"], "aa-ref-data": ["libs/ref-data/src/index.ts"], "aa-security-checker": ["libs/security-checker/src/index.ts"], "billing-client": ["libs/billing-client/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}