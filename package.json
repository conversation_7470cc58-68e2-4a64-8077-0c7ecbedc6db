{"name": "aa", "version": "0.0.0", "license": "MIT", "scripts": {"nx": "nx", "start": "nx serve", "build": "nx build", "debug": "nx debug", "test": "nx test --coverage=true", "lint": "nx workspace-lint && nx lint", "e2e": "nx e2e", "build:all": "nx run-many -t build --parallel 3 --prod", "test:all": "nx run-many -t test --parallel 3", "lint:all": "nx run-many -t lint --parallel 3", "e2e:all": "nx run-many -t e2e --parallel 3", "blt": "npm run lint:all && npm run build:all && npm run test:all && npm run e2e:all", "build:affected": "nx affected -t build --parallel 3 --prod", "test:affected": "nx affected -t test --parallel 3", "lint:affected": "nx affected -t lint --parallel 3", "e2e:affected": "nx affected -t lint --parallel 3", "blta": "npm run lint:affected && npm run build:affected && npm run test:affected  && npm run e2e:affected", "graph": "nx graph", "prepare": "husky install", "sync": "node -r dotenv/config ./tools/legacySync/sync.js", "proxy": "nodemon --watch proxy.json --watch proxy.json5 --watch proxy-rules.json tools/proxy/index.js", "scaffold": "node ./tools/scaffold/index.mjs", "preinstall": "node libs/bridge/src/scripts/hide.gyp.js", "postinstall": "node libs/bridge/src/scripts/unhide.gyp.js"}, "private": true, "dependencies": {"@aacom/aahelp-js-dependencies": "^18.5.6", "@aacom/cti-service": "18.0.34", "@anatine/zod-mock": "3.13.4", "@anatine/zod-openapi": "2.2.6", "@asteasolutions/zod-to-openapi": "7.1.1", "@azure/core-amqp": "4.2.0", "@azure/core-lro": "2.6.0", "@azure/core-rest-pipeline": "1.14.0", "@azure/event-hubs": "5.6.0", "@azure/eventgrid": "4.6.0", "@azure/eventhubs-checkpointstore-blob": "1.0.1", "@azure/logger": "1.0.4", "@azure/msal-browser": "2.14.2", "@azure/msal-node": "1.14.6", "@azure/service-bus": "7.8.1", "@azure/storage-blob": "12.12.0", "@faker-js/faker": "8.4.1", "@googlemaps/markerclusterer": "2.5.3", "@headlessui/react": "2.2.0", "@hookform/resolvers": "3.9.0", "@microsoft/applicationinsights-react-js": "17.3.5", "@microsoft/applicationinsights-web": "3.3.5", "@microsoft/fetch-event-source": "2.0.1", "@microsoft/microsoft-graph-client": "3.0.7", "@microsoft/microsoft-graph-types": "2.40.0", "@nivo/bar": "0.87.0", "@radix-ui/react-accessible-icon": "1.1.0", "@radix-ui/react-accordion": "1.2.0", "@radix-ui/react-alert-dialog": "1.1.1", "@radix-ui/react-aspect-ratio": "1.1.0", "@radix-ui/react-avatar": "1.1.0", "@radix-ui/react-checkbox": "1.1.1", "@radix-ui/react-collapsible": "1.1.0", "@radix-ui/react-context-menu": "2.2.1", "@radix-ui/react-dialog": "1.1.1", "@radix-ui/react-dropdown-menu": "2.1.1", "@radix-ui/react-form": "0.1.0", "@radix-ui/react-hover-card": "1.1.1", "@radix-ui/react-icons": "1.3.0", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-menubar": "1.1.1", "@radix-ui/react-navigation-menu": "1.2.0", "@radix-ui/react-popover": "1.1.1", "@radix-ui/react-portal": "1.1.1", "@radix-ui/react-progress": "1.1.0", "@radix-ui/react-radio-group": "1.2.0", "@radix-ui/react-scroll-area": "1.1.0", "@radix-ui/react-select": "2.1.1", "@radix-ui/react-separator": "1.1.0", "@radix-ui/react-slider": "1.2.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-switch": "1.1.0", "@radix-ui/react-tabs": "1.1.0", "@radix-ui/react-toast": "1.2.1", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-tooltip": "1.1.2", "@redis/client": "1.5.8", "@swc/helpers": "0.5.15", "@tanstack/react-table": "8.19.2", "@types/google.maps": "3.58.1", "@vis.gl/react-google-maps": "1.4.0", "aes-cross": "1.1.2", "ajv": "8.8.2", "angular": "1.6.1", "angular-animate": "1.4.7", "angular-apprating-api": "0.1.12", "angular-bootstrap-lightbox": "0.12.0", "angular-environment": "1.0.8", "angular-formly": "7.5.2", "angular-formly-templates-bootstrap": "6.5.1", "angular-hotkeys": "1.7.0", "angular-loading-bar": "0.8.0", "angular-messages": "1.4.6", "angular-mocks": "1.8.3", "angular-right-click": "0.1.1", "angular-sanitize": "1.6.1", "angular-socket-io": "0.7.0", "angular-ui-bootstrap": "0.14.x", "angular-ui-mask": "1.8.7", "angular-ui-router": "0.3.2", "api-check": "7.5.5", "applicationinsights": "2.2.1", "applicationinsights-native-metrics": "0.0.7", "assert": "1.5.1", "async": "2.6.4", "axios": "1.4.0", "azure-sb": "0.11.2", "azure-storage": "2.10.7", "babel-polyfill": "6.26.0", "better-queue": "3.8.12", "better-queue-sql": "1.0.6", "bindings": "1.5.0", "bl": "6.0.18", "body-parser": "1.20.2", "bootstrap": "3.4.1", "bower": "1.8.14", "browserify": "17.0.1", "buffer": "6.0.3", "bunyan": "1.8.15", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "cmdk": "1.0.0", "compression": "1.7.4", "copy-webpack-plugin": "12.0.2", "cors": "2.8.5", "countdown": "2.6.0", "crypto-browserify": "3.12.1", "crypto-js": "3.3.0", "csv-parser": "3.0.0", "date-fns": "3.6.0", "date-fns-tz": "3.2.0", "date-format": "1.2.0", "dateformat": "1.0.12", "deep-object-diff": "1.1.9", "dotenv": "8.6.0", "embla-carousel-react": "8.1.3", "enumify": "1.0.4", "escape-string-regexp": "4.0.0", "express": "4.18.2", "express-basic-auth": "1.2.1", "express-ntlm": "2.7.0", "express-queue": "0.0.12", "express-winston": "4.2.0", "express-xml-bodyparser": "0.3.0", "final-fs": "*", "flag-icons": "6.15.0", "geodesy": "1.1.3", "geolib": "2.0.24", "gmaps": "0.4.25", "gmaps.core": "0.5.1", "gmaps.markers": "0.5.0", "googleapis": "20.2.0", "grunt-contrib-uglify": "4.0.1", "handlebars": "4.7.8", "handlebars-helpers": "0.10.0", "haversine": "1.1.1", "helmet": "4.6.0", "html-pdf": "2.2.0", "https": "1.0.0", "input-otp": "1.2.4", "isomorphic-dompurify": "2.16.0", "isomorphic-fetch": "3.0.0", "jest-junit": "16.0.0", "jotai": "2.8.3", "json5": "2.2.3", "jsonschema": "1.4.1", "jsonwebtoken": "9.0.0", "jwks-rsa": "2.0.3", "ldapjs": "2.3.0", "lodash": "4.17.21", "lucide-react": "0.394.0", "mingo": "6.4.15", "mock-fs": "5.2.0", "mock-require": "3.0.3", "moment": "2.30.1", "moment-range": "4.0.2", "mongodb": "5.7.0", "mongoose": "5.9.10", "morgan": "1.10.0", "multer": "1.4.4", "nanoid": "2.1.11", "nconf": "0.10.0", "ng-pattern-restrict": "0.2.3", "ng-scrollbar": "0.0.8", "nocache": "4.0.0", "node-mailer": "0.1.1", "node-mocks-http": "1.14.1", "node-polyfill-webpack-plugin": "4.0.0", "node-uuid": "1.4.8", "nodemailer": "4.7.0", "npm": "6.14.18", "oracledb": "6.8.0", "os-browserify": "0.3.0", "path-browserify": "1.0.1", "pdf-lib": "1.17.1", "pm2": "2.10.4", "postcss-scss": "4.0.9", "process": "0.11.10", "puppeteer": "18.2.1", "q": "1.5.1", "querystring-es3": "0.2.1", "queue": "4.5.1", "react": "18.2.0", "react-datepicker": "7.5.0", "react-day-picker": "8.10.1", "react-dom": "18.2.0", "react-error-boundary": "4.0.13", "react-helmet": "6.1.0", "react-hook-form": "7.53.0", "react-icomoon": "2.5.7", "react-resizable-panels": "2.0.19", "react-router-dom": "6.23.1", "react-select": "5.9.0", "react-signature-canvas": "1.0.6", "react-svg": "16.2.0", "react-window": "1.8.11", "recharts": "2.13.3", "redis": "4.6.13", "reflect-metadata": "0.1.14", "request": "2.88.2", "rimraf": "3.0.0", "rsmq": "0.12.4", "rxjs": "6.5.5", "simple-oauth2": "5.0.0", "simple-react-validator": "1.6.2", "soap": "0.29.0", "soap-decorators": "2.0.1", "socket.io": "1.7.4", "socket.io-client": "1.7.4", "sonner": "1.4.41", "spdy": "4.0.2", "sqlite": "4.2.1", "sqlite3": "5.1.7", "stream-browserify": "3.0.0", "stream-http": "3.2.0", "string-strip-html": "4.5.1", "swagger-jsdoc": "6.2.8", "swagger-ui-express": "4.6.3", "swr": "2.2.5", "tailwind-merge": "2.3.0", "tailwindcss-animate": "1.0.7", "tedious": "8.3.1", "tslib": "2.5.0", "uk-bank-holidays": "0.1.0", "util": "0.12.5", "uuid": "8.3.2", "uuid-by-string": "3.0.7", "uuidv4": "6.2.13", "vaul": "0.9.1", "winston": "2.4.7", "xlsx": "0.18.5", "xml2js": "0.4.23", "xsd-decorators": "1.0.2", "yup": "0.29.3", "zod": "3.23.8", "zod-validation-error": "3.3.0"}, "devDependencies": {"@anatine/esbuild-decorators": "0.2.19", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-transform-private-methods": "7.24.1", "@babel/plugin-transform-private-property-in-object": "7.24.5", "@babel/preset-react": "7.24.1", "@heroicons/react": "2.2.0", "@nrwl/cli": "15.9.3", "@nrwl/node": "16.3.2", "@nx/cypress": "16.3.2", "@nx/esbuild": "16.3.2", "@nx/eslint-plugin": "16.3.2", "@nx/jest": "16.3.2", "@nx/js": "16.3.2", "@nx/linter": "16.3.2", "@nx/node": "16.3.2", "@nx/react": "16.3.2", "@nx/web": "16.3.2", "@nx/webpack": "16.3.2", "@nx/workspace": "16.3.2", "@pmmmwh/react-refresh-webpack-plugin": "0.5.13", "@svgr/webpack": "8.1.0", "@swc/cli": "~0.1.62", "@swc/core": "~1.3.51", "@testing-library/react": "14.0.0", "@types/bunyan": "1.8.8", "@types/compression": "1.7.2", "@types/cors": "2.8.10", "@types/dom-parser": "0.1.4", "@types/express": "4.17.17", "@types/geodesy": "1.2.2", "@types/html-pdf": "2.2.1", "@types/isomorphic-fetch": "0.0.36", "@types/jest": "29.5.2", "@types/jsonwebtoken": "9.0.1", "@types/ldapjs": "2.2.5", "@types/lodash": "4.17.1", "@types/mocha": "5.2.7", "@types/mock-fs": "4.13.4", "@types/morgan": "1.9.9", "@types/multer": "1.4.12", "@types/nconf": "0.10.6", "@types/node": "18.19.32", "@types/nodemailer": "4.6.8", "@types/oracledb": "5.2.0", "@types/q": "1.5.8", "@types/react": "18.0.28", "@types/react-dom": "18.0.11", "@types/react-helmet": "6.1.11", "@types/react-signature-canvas": "1.0.6", "@types/react-table": "7.7.20", "@types/simple-oauth2": "5.0.7", "@types/socket.io-client": "1.4.36", "@types/spdy": "3.4.9", "@types/swagger-ui-express": "4.1.6", "@types/uuid": "8.3.0", "@types/winston": "2.4.4", "@typescript-eslint/eslint-plugin": "5.59.9", "@typescript-eslint/parser": "5.59.9", "axios-mock-adapter": "2.1.0", "babel-jest": "29.7.0", "cypress": "12.17.4", "esbuild": "0.17.19", "eslint": "8.15.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-cypress": "2.15.2", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "http-proxy-middleware": "2.0.3", "husky": "8.0.3", "jest": "29.5.0", "jest-environment-jsdom": "29.5.0", "jest-environment-node": "29.5.0", "jshint-stylish": "2.2.1", "nock": "13.5.4", "nodemon": "2.0.15", "nx": "16.3.2", "path": "0.12.7", "postcss": "8.4.21", "prettier": "2.8.7", "react-refresh": "0.10.0", "react-table": "7.8.0", "supertest": "0.15.0", "tailwindcss": "3.4.4", "ts-jest": "29.1.2", "ts-node": "10.9.1", "typescript": "5.6.3", "url": "0.11.4", "url-loader": "4.1.1", "verdaccio": "5.33.0", "worker-loader": "3.0.8"}, "engines": {"node": "14"}}